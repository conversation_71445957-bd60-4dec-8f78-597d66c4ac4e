/////////////// 所有常量（全大写单词间用下划线隔开）///////////////

import { ArmyState, BookCommentType, BuffType, CEffect, CType, LobbyModeType, MailStateType, MarchLineType, StudyType } from "./Enums"

// 点击间隔
const CLICK_SPACE = 10
// 一格的大小
const TILE_SIZE = 80
// 地图边界额外宽度
const MAP_EXTRA_SIZE = 2

// 地图显示偏移
const MAP_SHOW_OFFSET = cc.v2(TILE_SIZE * 8, TILE_SIZE * 8)

//
const TILE_SIZE_HALF = cc.v2(TILE_SIZE * 0.5, TILE_SIZE * 0.5)

// 设施拖拽时候的高
const BUILD_DRAG_OFFSETY = TILE_SIZE_HALF.y - 24

// 区域最高y坐标
const AREA_MAX_ZINDEX = 21 * TILE_SIZE
// 层级最大值
const MAX_ZINDEX = 10000

// 长按时间
const LONG_PRESS_TIME = 0.4

// 延迟关闭pnl时间
const DELAY_CLOSE_PNL_TIME = 0.4

// 主城id
const CITY_MAIN_NID = 1001
// 要塞id
const CITY_FORT_NID = 2102
// 4个遗迹
const ANCIENT_WALL_ID = 3000 //城墙
const CITY_CHANGAN_ID = 3001 //长安
const CITY_JINLING_ID = 3002 //金陵
const CITY_YANJING_ID = 3003 //燕京
const CITY_LUOYANG_ID = 3004 //洛阳
// 农场
const BUILD_FARM_ID = 2201
// 伐木场
const BUILD_TIMBER_ID = 2202
// 采石场
const BUILD_QUARRY_ID = 2203
// 城墙建筑id
const BUILD_WALL_NID = 2000
// 主城id
const BUILD_MAIN_NID = 2001
// 仓库建筑id
const BUILD_GRANARY_NID = 2002
// 粮仓建筑id
const BUILD_WAREHOUSE_NID = 2003
// 兵营建筑id
const BUILD_BARRACKS_NID = 2004
// 大使馆建筑id
const BUILD_EMBASSY_NID = 2005
// 市场建筑id
const BUILD_BAZAAR_NID = 2006
// 铁匠铺所建筑id
const BUILD_SMITHY_NID = 2008
// 校场建筑id
const BUILD_DRILLGROUND_NID = 2011
// 工厂建筑id
const BUILD_PLANT_NID = 2010
// 联盟市场建筑id
const BUILD_ALLI_BAZAAR_NID = 2014
// 英雄殿建筑id
const BUILD_HEROHALL_NID = 2015
// 医馆建筑id
const BUILD_HOSPITAL_NID = 2016
// 旗子id
const BUILD_FLAG_NID = 2101
// 要塞建筑id
const BUILD_FORT_NID = 2102
// 箭塔建筑id
const BUILD_TOWER_NID = 2103

// 强弩兵ID
const PAWN_CROSSBOW_ID = 3305
// 斧骑兵ID
const AX_CAVALRY_ID = 3406

// 初始资源产量
const INIT_RES_OUTPUT = 120
// 初始容量
const INIT_RES_CAP = 1000
// 初始资源
const INIT_RES_COUNT = 700
// 默认修建队列
const DEFAULT_BT_QUEUE_COUNT = 2

// 立即完成修建需要的金币数
const IN_DONE_BT_GOLD = 30
// 立即完成打造需要的金币数
const IN_DONE_FORGE_GOLD = 30
// 修改昵称需要的金币数
const MODIFY_NICKNAME_GOLD = 500

// 军队最大士兵个数
const ARMY_PAWN_MAX_COUNT = 9
// 大使馆多少级可以创建联盟
const CREATE_ALLI_MAX_LV = 3

// 
const DEFAULT_CITY_SIZE = cc.v2(1, 1) //默认的城市地块大小
const DEFAULT_AREA_SIZE = cc.v2(11, 11) //默认的区域大小
const DEFAULT_BUILD_SIZE = cc.v2(1, 1) //默认的建筑面积大小
const BOSS_BUILD_SIZE = cc.v2(3, 3) //boss
const DEFAULT_MAX_ARMY_COUNT = 5 //默认区域的最大容纳军队数量
const DEFAULT_MAX_ADD_PAWN_TIMES = 20 //默认最大补兵次数

// 加速行军倍数
const UP_MARCH_SPEED_MUL = 3

// 城边加速倍数
const MAIN_CITY_MARCH_SPEED = {
    1: 3.5,
    2: 3,
    3: 2.5,
    4: 2,
    5: 1.5,
}

// 运送时间 格/小时
const TRANSIT_TIME = 300

// 一场战斗最多持续时间 秒
const BATTLE_MAX_TIME = 3600 * 3

// 地块底配置
const LAND_DI_CONF = [
    { list: [0, 0, 0, 0], no: '01' },
    { list: [0, 1, 1, 0], no: '02' },
    { list: [0, 1, 1, 1], no: '03' },
    { list: [0, 0, 1, 1], no: '04' },
    { list: [1, 1, 1, 0], no: '05' },
    { list: [1, 1, 1, 1], no: '06' },
    { list: [1, 0, 1, 1], no: '07' },
    { list: [1, 1, 0, 0], no: '08' },
    { list: [1, 1, 0, 1], no: '09' },
    { list: [1, 0, 0, 1], no: '10' },
    { list: [0, 0, 1, 0], no: '11' },
    { list: [1, 0, 1, 0], no: '12' },
    { list: [1, 0, 0, 0], no: '13' },
    { list: [0, 1, 0, 0], no: '14' },
    { list: [0, 1, 0, 1], no: '15' },
    { list: [0, 0, 0, 1], no: '16' },
]

// 地块底配置，方向：左上右下
const DECORATION_MUD_CONF = {
    '0011': '101',
    '1011': '102',
    '1001': '103',
    '0111': '104',
    '1101': '107',
    '0110': '108',
    '1110': '109',
    '1100': '110',
    '0010': '111',
    '1010': '112',
    '1000': '113',
    '0001': '114',
    '0101': '115',
    '0100': '116',
    '0000': '117',
    '1111': ['105', '106'],
}

// 边框线配置
const BORDER_LINE_CONF = [
    { size: cc.size(80, 4), pos: cc.v2(0, 38) }, //上
    { size: cc.size(4, 80), pos: cc.v2(38, 0) }, //右
    { size: cc.size(80, 4), pos: cc.v2(0, -38) }, //下
    { size: cc.size(4, 80), pos: cc.v2(-38, 0) }, //左
    { size: cc.size(4, 4), pos: cc.v2(-38, 38) }, //左上
    { size: cc.size(4, 4), pos: cc.v2(38, 38) }, //右上
    { size: cc.size(4, 4), pos: cc.v2(38, -38) }, //右下
    { size: cc.size(4, 4), pos: cc.v2(-38, -38) }, //左下
]

// 河流边框线配置
const RIVER_LINE_CONF = {
    0: { size: cc.size(80, 4), pos: cc.v2(0, 30) }, //上
    111: { size: cc.size(4, 68), pos: cc.v2(38, -6) }, //右 下短上短
    112: { size: cc.size(4, 80), pos: cc.v2(38, 0) }, //右 下短上长
    121: { size: cc.size(4, 80), pos: cc.v2(38, -12) }, //右 下长上短
    122: { size: cc.size(4, 92), pos: cc.v2(38, -6) }, //右 下长上长
    311: { size: cc.size(4, 68), pos: cc.v2(-38, -6) }, //左 下短上短
    312: { size: cc.size(4, 80), pos: cc.v2(-38, 0) }, //左 下短上长
    321: { size: cc.size(4, 80), pos: cc.v2(-38, -12) }, //左 下长上短
    322: { size: cc.size(4, 92), pos: cc.v2(-38, -6) }, //左 下长上长
}

// 选择地块信息框大小
const SELECT_CELL_INFO_BOX = cc.rect(320, 320, 272, 320)

// 士兵气泡高度
const PAWN_BUBBLE_OFFSETY = 100

// 地块资源配置列表字段
const CELL_RES_FIELDS = ['cereal', 'timber', 'stone']

// 资源字段反向映射
const RES_FIELDS_CTYPE = {
    'cereal': CType.CEREAL,
    'timber': CType.TIMBER,
    'stone': CType.STONE,
}

// 通用类型对应图标url
const CTYPE_ICON_URL = {
    [CType.TITLE]: 'icon/title_empty',
    [CType.WIN_POINT]: 'icon/win_point',
    [CType.HERO_OPT]: 'icon/hero_opt',
    [CType.UP_RECRUIT]: 'icon/up_recruit',
}

// 通用类型对应图标url
const CTYPE_ICON = {
    [CType.CEREAL]: 'cereal',
    [CType.TIMBER]: 'timber',
    [CType.STONE]: 'stone',
    [CType.GOLD]: 'gold',
    [CType.INGOT]: 'ingot',
    [CType.WAR_TOKEN]: 'war_token',
    [CType.EXP_BOOK]: 'exp_book',
    [CType.CEREAL_C]: 'cereal_c',
    [CType.IRON]: 'iron',
    [CType.UP_SCROLL]: 'up_scroll',
    [CType.FIXATOR]: 'fixator',
    [CType.BASE_RES]: 'base_res',
    [CType.BASE_RES_2]: 'base_res_2',
    [CType.STAMINA]: 'stamina',
    [CType.RANK_COIN]: 'rank_coin',
}

// 通用类型对应的名字
const CTYPE_NAME = {
    [CType.CEREAL]: 'ui.cereal',
    [CType.TIMBER]: 'ui.timber',
    [CType.STONE]: 'ui.stone',
    [CType.GOLD]: 'ui.gold',
    [CType.INGOT]: 'ui.ingot',
    [CType.WAR_TOKEN]: 'ui.war_token',
    [CType.EXP_BOOK]: 'ui.exp_book',
    [CType.CEREAL_C]: 'ui.cereal_c',
    [CType.IRON]: 'ui.iron',
    [CType.UP_SCROLL]: 'ui.up_scroll',
    [CType.FIXATOR]: 'ui.fixator',
    [CType.BASE_RES]: 'ui.base_res',
}

// 建筑效果配置
const BUILD_EFFECT_TYPE_CONF = {
    [CEffect.BT_QUEUE]: { vtype: 'number' },
    [CEffect.BUILD_CD]: { vtype: 'number', suffix: '%' }, //减少建造时间 2
    [CEffect.GRANARY_CAP]: { vtype: 'number' }, //粮仓容量 3
    [CEffect.WAREHOUSE_CAP]: { vtype: 'number' }, //仓库容量 4
    [CEffect.XL_CD]: { vtype: 'number', suffix: '%' }, //减少步兵训练速度 5
    [CEffect.ALLIANCE_PERS]: { vtype: 'number' }, //联盟人数 6
    [CEffect.MERCHANT_COUNT]: { vtype: 'number' }, //商人数量 7
    [CEffect.DRILL_QUEUE]: { vtype: 'number' }, //招募队列 8
    [CEffect.WALL_HP]: { vtype: 'number' }, //城墙血量 9
    [CEffect.FORGE_CD]: { vtype: 'number', suffix: '%' }, //打造装备速度 10
    [CEffect.ARMY_COUNT]: { vtype: 'number' }, //军队数量 11
    [CEffect.RES_OUTPUT]: { vtype: 'number' }, //资源产量 12
    [CEffect.MARCH_CD]: { vtype: 'number', suffix: '%' }, //减少行军时间 13
    [CEffect.UPLVING_CD]: { vtype: 'number', suffix: '%' }, //训练士兵时间 14
    [CEffect.GW_CAP]: { vtype: 'number' }, //增加粮仓和仓库容量 15
    [CEffect.XL_2LV]: { vtype: 'number', suffix: '%' }, //有一定几率训练出2级士兵 16
    [CEffect.TRANSIT_CD]: { vtype: 'number', suffix: '%' }, //每个商人的运输量增加1000 减少商人运送时间 17
    [CEffect.MAIN_MARCH_MUL]: { vtype: 'number', suffix: '%' }, //主城要塞行军时间加速 18
    [CEffect.CITY_BUILD_CD]: { vtype: 'number', suffix: '%' }, //地面建筑修建时间减少 19 x
    [CEffect.TREASURE_AWARD]: { vtype: 'number', suffix: '%' }, //宝箱奖励增加 20
    [CEffect.FREE_RECAST]: { vtype: 'number', suffix: '%' }, //有一定几率免费重铸装备 21
    [CEffect.RARE_RES_OUTPUT]: { vtype: 'number' }, //书铁每天增加 22
    [CEffect.MORE_RARE_RES]: { vtype: 'number' }, //书铁超过100 每天增加 23
    [CEffect.LV_UP_QUEUE]: { vtype: 'number' }, //训练队列 24
    [CEffect.TOWER_LV]: { vtype: 'number' }, //哨塔等级 25
    [CEffect.FARM_OUTPUT]: { vtype: 'number', suffix: '%' }, //农场产量 26
    [CEffect.QUARRY_OUTPUT]: { vtype: 'number', suffix: '%' }, //采石场产量 27
    [CEffect.MILL_OUTPUT]: { vtype: 'number', suffix: '%' }, //伐木场产量 28
    [CEffect.CURE_QUEUE]: { vtype: 'number', suffix: '%' }, //治疗队列 29
    [CEffect.MARKET_SERVICE_CHARGE]: { vtype: 'number', suffix: '%' }, //置换手续费 30
    [CEffect.CITY_COUNT_LIMIT]: { vtype: 'number' }, //地面建筑上限 31
    [CEffect.TOWER_HP]: { vtype: 'number', suffix: '%' }, //哨塔，要塞，城墙的耐久提高 35
    [CEffect.OTHER_RES_ODDS]: { vtype: 'number', suffix: '%' }, //地块的其他资源掉落概率提高 36
    [CEffect.CURE_CD]: { vtype: 'number', suffix: '%' }, // 治疗伤兵速度 37
}

// 行军军队名字颜色
const MARCH_ARMY_NAME_COLOR = {
    [MarchLineType.SELF_ARMY]: '#59A733',
    [MarchLineType.OTHER_ARMY]: '#C34B3F',
    [MarchLineType.ALLI_ARMY]: '#4F8FBA',
}

// 行军军队时间颜色
const MARCH_ARMY_TIME_COLOR = {
    [MarchLineType.SELF_ARMY]: '#FFFFFF',
    [MarchLineType.OTHER_ARMY]: '#FF9162',
    [MarchLineType.ALLI_ARMY]: '#7FD6FF',
}

// 军队状态颜色
const ARMY_STATE_COLOR = {
    [ArmyState.NONE]: '#936E5A',
    [ArmyState.MARCH]: '#936E5A',
    [ArmyState.FIGHT]: '#C34B3F',
    [ArmyState.DRILL]: '#59A733',
    [ArmyState.LVING]: '#59A733',
    [ArmyState.CURING]: '#59A733',
    [ArmyState.TONDEN]: '#59A733',
}

// 邮件状态颜色
const MAIL_STATE_COLOR = {
    [MailStateType.NONE]: '#C34B3F',
    [MailStateType.NOT_CLAIM]: '#C34B3F',
    [MailStateType.READ]: '#A18876',
}

const COLOR_NORMAL = {
    DONE: '#21DC2D'
}

// 军队记录说明的配置
const ARMY_RECORD_DESC_CONF = {
    0: ['index'], //在{0}发生战斗
    1: ['index', 'target'], //从{0}移动到{1}
    2: ['index', 'target'], //从{0}移动到{1}，被撤回
    3: ['index', 'target'], //在{0}被遣返回{1}
    4: ['index'], //在{0}被强行解散
    5: ['index', 'target'], //从{0}移动到{1}，被遣返
    6: ['index'], //在{0}被强制撤离
    7: ['index', 'target'], //从{0}移动到{1}，被强制撤离
}

// 回放倍数
const PLAYBACK_MULS = [
    { val: 4, text: '0.25x' },
    { val: 2, text: '0.5x' },
    { val: 1, text: '1x' },
    { val: 0.5, text: '2x' },
    { val: 0.25, text: '4x' },
]

// 固定到菜单配置
const FIXATION_MENU_CONFIG = {
    2001: 'build/BuildMainInfo', //主城
    2004: 'build/BuildBarracks', //兵营
    2005: 'build/BuildEmbassy', //联盟
    2006: 'build/BuildBazaar', //自由市场
    2008: 'build/BuildSmithy', //铁匠铺
    2010: 'build/BuildFactory', //工厂
    2011: 'build/BuildDrillground', //校场
    2012: 'build/BuildTower', //里亭属
    2013: 'build/BuildTower', //边塞营
    2014: 'build/BuildBazaar', //联盟市场
    2015: 'build/BuildHerohall', //英雄殿
    2016: 'build/BuildHospital', // 医馆
}
const FIXATION_MENU_MAX_COUNT = 3 //固定到菜单最多个数

// 免费头像列表
const FREE_HEAD_ICONS = [
    'head_icon_free_001',
    'head_icon_free_002',
    'head_icon_free_003',
    'head_icon_free_004',
    'head_icon_free_005',
    'head_icon_free_006',
    'head_icon_free_007',
    'head_icon_free_008',
]

// 商城购买添加产量需要的金币
const ADD_OUTPUT_GOLD = 50
const ADD_OUTPUT_RATIO = 20 //商城购买添加产量比例
const ADD_OUTPUT_TIME = 1 * 86400 * 1000 //商城购买添加产量持续时间

// 内政政策槽位配置
const POLICY_SLOT_CONF = [3, 5, 10, 15, 20]

// 装备槽位配置
const EQUIP_SLOT_CONF = [1, 3, 5, 7, 10, 12, 14, 16, 18, 20]
const EQUIP_SLOT_EXCLUSIVE_LV = { 10: true, 18: true }
// 装备融炼解锁等级
const EQUIP_SMELT_NEED_LV = [14, 20]

// 士兵槽位配置
const PAWN_SLOT_CONF = [1, 2, 4, 7]

// 研究每重置一次需要的金币
const RESET_STUDY_SLOT_GOLD = 50

// 多长时间可以退出联盟
const CAN_EXIT_ALLI_TIME = 3600000 * 12

// 创建联盟费用
const CREATE_ALLI_COST = '1,0,3000|2,0,2000|3,0,2000'
const CREATE_ALLI_COND = 100

// 单个玩家给其他玩家改变人气间隔
const ONE_USER_POPULARITY_CHANGE_INTERVAL = 86400000 * 30

// 外显buff 同时显示 层级
const BUFF_NODE_ZINDEX = {
    [BuffType.SHIELD]: 1,
    [BuffType.PROTECTION_SHIELD]: 1,
    [BuffType.RODELERO_SHIELD]: 1,
    [BuffType.RODELERO_SHIELD_001]: 1,
    [BuffType.RODELERO_SHIELD_102]: 1,
    [BuffType.ABNEGATION_SHIELD]: 1,
    [BuffType.PARRY]: 2,
    [BuffType.PARRY_001]: 2,
    [BuffType.PARRY_102]: 2,
    [BuffType.WITHSTAND]: 2,
    [BuffType.JUMPSLASH_DAMAGE]: 2,
    [BuffType.BEHEADED_GENERAL]: 2,
    [BuffType.ANTICIPATION_DEFENSE]: 2,
    [BuffType.ANTICIPATION_ATTACK]: 2,
    [BuffType.DIZZINESS]: 3,
    [BuffType.PARALYSIS]: 3,
    [BuffType.PARALYSIS_UP]: 3,
    [BuffType.WIRE_CHAIN]: 3,
    [BuffType.SILENCE]: 4,
    [BuffType.CHAOS]: 4,
    [BuffType.POISONED_WINE]: 4,
    [BuffType.LIAN_PO_ATTACK]: 5,
    [BuffType.LIAN_PO_DEFEND]: 5,
}

// 外显buff 同时显示
const NEED_SHOW_BUFF = {
    [BuffType.SHIELD]: true,
    [BuffType.PROTECTION_SHIELD]: true,
    [BuffType.RODELERO_SHIELD]: true,
    [BuffType.RODELERO_SHIELD_001]: true,
    [BuffType.RODELERO_SHIELD_102]: true,
    [BuffType.ABNEGATION_SHIELD]: true,
    [BuffType.PARRY]: true,
    [BuffType.PARRY_001]: true,
    [BuffType.PARRY_102]: true,
    [BuffType.WITHSTAND]: true,
    [BuffType.JUMPSLASH_DAMAGE]: true,
    [BuffType.BEHEADED_GENERAL]: true,
    [BuffType.ANTICIPATION_DEFENSE]: true,
    [BuffType.ANTICIPATION_ATTACK]: true,
    [BuffType.DIZZINESS]: true,
    [BuffType.PARALYSIS]: true,
    [BuffType.PARALYSIS_UP]: true,
    [BuffType.WIRE_CHAIN]: true,
    [BuffType.SILENCE]: true,
    [BuffType.CHAOS]: true,
    [BuffType.POISONED_WINE]: true,
    [BuffType.LIAN_PO_ATTACK]: true,
    [BuffType.LIAN_PO_DEFEND]: true,
}

// 外显buff 互斥显示
const NEED_MUTUAL_BUFF = {
    [BuffType.ARMOR_PENETRATION]: true,
    [BuffType.INSPIRE]: true,
    [BuffType.WORTHY_MONARCH]: true,
    [BuffType.DESTROY_WEAPONS]: true,
    [BuffType.POISONING_MAX_HP]: true,
    [BuffType.POISONING_CUR_HP]: true,
    [BuffType.INFECTION_PLAGUE]: true,
    [BuffType.BLEED]: true,
    [BuffType.DAMAGE_INCREASE]: true,
    [BuffType.DAMAGE_REDUCE]: true,
    [BuffType.GOD_WAR]: true,
    [BuffType.FEAR]: true,
    [BuffType.TIMIDITY]: true,
    [BuffType.TIGER_MANIA]: true,
    [BuffType.IRREMOVABILITY]: true,
    [BuffType.OVERLORD]: true,
    [BuffType.IGNITION]: true,
    [BuffType.RAGE]: true,
}

// 外显buff 类型转换
const BUFF_SHOW_TYPE_TRAN = {
    [BuffType.POISONING_CUR_HP]: BuffType.POISONING_MAX_HP,
    [BuffType.INFECTION_PLAGUE]: BuffType.POISONING_MAX_HP,
    [BuffType.DAMAGE_REDUCE]: BuffType.DESTROY_WEAPONS,
    [BuffType.WORTHY_MONARCH]: BuffType.INSPIRE,
    [BuffType.GOD_WAR]: BuffType.INSPIRE,
    [BuffType.TIGER_MANIA]: BuffType.INSPIRE,
    [BuffType.IRREMOVABILITY]: BuffType.TIMIDITY,
    [BuffType.OVERLORD]: BuffType.TIMIDITY,
}

// 护盾buff
const SHIELD_BUFF = {
    [BuffType.SHIELD]: true,
    [BuffType.PROTECTION_SHIELD]: true,
    [BuffType.LOW_HP_SHIELD]: true,
    [BuffType.ATTACK_SHIELD]: true,
    [BuffType.SUCKBLOOD_SHIELD]: true,
    [BuffType.RODELERO_SHIELD]: true,
    [BuffType.RODELERO_SHIELD_001]: true,
    [BuffType.RODELERO_SHIELD_102]: true,
    [BuffType.BATTLE_BEGIN_SHIELD]: true,
    [BuffType.KUROU_SHIELD]: true,
    [BuffType.SUCK_SHIELD]: true,
    [BuffType.ABNEGATION_SHIELD]: true,
    [BuffType.LONGITUDINAL_CLEFT_SHIELD]: true,
    [BuffType.CRIMSONGOLD_SHIELD]: true,
    [BuffType.BLACK_IRON_STAFF_SHIELD]: true,
}

// 战斗特效类型
// 10000002.攻击 10000003.闪避 10000004.减伤 10000005.护盾
const BATTLE_EFFECT_TYPE = {
    ATTACK: [10000002],
    DAMAGE_REDUCTION: [10000004],
    SHIELD: [10000005],
    VALOR: [10000002, 10000003],
    WISDOM_COURAGE: [10000002, 10000004],
    KUROU: [10000002, 10000005],
    SAND_CLOCK: [10000006],
    TONDEN: [114001],
}

// 聊天弹幕颜色
const CHAT_BARRAGE_COLOR = {
    0: '#FFFFFF', //世界
    1: '#5BB8FF', //联盟
    2: '#FF81F7', //私聊
}

// 和大自然交换资源手续费
const REPLACEMENT_SERVICE_CHARGE = 60
// 和大自然交换最少资源
const REPLACEMENT_MIN_RES_COUNT = 100
// 每日置换次数
const REPLACEMENT_TODAY_COUNT_MAP = {
    0: 3,
    1: 3,
    2: 3,
}
// 各个资源占用运送的容量
const RES_TRANSIT_CAP = {
    [CType.CEREAL]: 1,
    [CType.TIMBER]: 1,
    [CType.STONE]: 1,
    [CType.EXP_BOOK]: 100,
    [CType.IRON]: 100,
    [CType.UP_SCROLL]: 500,
    [CType.FIXATOR]: 500,
}

// 加速招募倍数
const UP_RECRUIT_PAWN_MUL = 0.125

// 多语言名字
const LANGUAGE_TEXT_LIST = [
    { lang: 'en', text: 'ENGLISH' },
    { lang: 'cn', text: '简体中文' },
    { lang: 'hk', text: '繁體(港澳)' },
    { lang: 'tw', text: '繁體(臺灣)' },
    { lang: 'jp', text: '日本語' },
    { lang: 'kr', text: '한국어' },
    { lang: 'idl', text: 'Bahasa Indonesia' }, //印尼
    { lang: 'th', text: 'ภาษาไทย' }, //泰语
    { lang: 'vi', text: 'Tiếng Việt' }, //越南
]

// 注销账号等待时间
const LOGOUT_MAX_DAY = 86400000 * 7

// 联盟职位说明
const ALLI_JOB_DESC = {
    0: [1, 2, 3, 4, 5, 7, 8], //盟主
    1: [3, 4, 5, 7], //副盟主
    2: [6, 8], //军师
    10: [0], //成员
}

// 职位数量
const ALLI_JOB_COUNT = {
    0: 1, //盟主
    1: 1, //副盟主
    2: 2, //军师
    10: 40,
}

// 开服多久内不可攻占
const NOT_OCCUPY_BY_SERVER_RUNTIME = 86400000 * 3
const NOT_OCCUPY_BY_MAX_LAND_COUNT = 100

// 不同类型的区最多可玩几个区
const CONCURRENT_GAME_LIMIT = 1

// 领地积分配置
const LAND_SCORE_CONF = {
    1: [[50, 2], [100, 1]],
    2: [[40, 4], [80, 2]],
    3: [[30, 6], [60, 3]],
    4: [[20, 8], [40, 4]],
    5: [[10, 10], [20, 5]],
}

// 多久才可以删除私聊
const REMOVE_PCHAT_TIME = 3600000 * 12

// 攻占玩家领地要求最低距离
const OCCUPY_PLAYER_CELL_MIN_DIS = 5

// 多少地可以无限制私聊
const NOLIMIT_PCHAT_MAX_LAND = 150

// 聊天 显示时间的最大间隔
const SHOW_TIME_MAX_INTERVAL = 60000 * 1

// 可申请添加好友最小地块数
const FRIENDS_MIN_LAND_COUNT = 100

// 战斗预测费用
const BATTLE_FORECAST_COST = 30

// 战斗预测免费次数
const BATTLE_FORECAST_FREE_COUNT = 5

// 一键打开宝箱要求
const OPEN_ALL_TREASURE_MIN_LAND_COUNT = 100

// 最小可修改的行军速度
const CAN_MIN_MARCH_SPEED = 30

// 一个季节持续的时间
const SEASON_DURATION_TIME = 3 * 86400000

// 遗迹加速资源倍数
const ANCIENT_SUP_COST_MUL = 40
// 遗迹加速时间
const ANCIENT_SUP_TIME = 6 * 60000

// 聊天相关
const CHAT_MAX_COUNT: number = 50
const CHAT_SEND_INTERVAL: number = 6000 //发送聊天的预期间隔 (毫秒)
const CHAT_TOLERATE_MAX_COUNT: number = 3 //最多容忍多少次在间隔内发送
const CHAT_REST_MAX_TIME: number = 30000 //如果太频繁就休息一下
const CHAT_BANNED_REST_MAX_TIME: number = 60000 * 10 //禁言休息时间

// 发送喇叭费用
const SEND_TRUMPET_COST = 50
const SEND_TRUMPET_ACC_COST = 25

// 多长时间可以取消报名
const SERVER_APPLY_CANCEL_CD = 1 * 60 * 1000
// 下次报名的等待时间
const NEXT_APPLY_CD = 6 * 1000

// 点将一次的费用
const POINTSETS_ONE_COST = 10
// 点击5次 金币费用
const POINTSETS_ONE_GOLD_COST = 598

// 残卷合成画像 需要数量
const PORTRAYAL_COMP_NEED_COUNT = 3
// 还原画像费用
const RESTORE_PORTRAYAL_WAR_TOKEN_COST = 50
const RESTORE_PORTRAYAL_GOLD_COST = 598

// 购买自选英雄费用 元宝
const BUY_OPT_HERO_COST = 999

// 英雄自选礼包
const HERO_OPT_GIFT = {
    // 陈到, 徐盛, 张辽
    1: [310101, 320101, 340101],
    // 陈到, 李嗣业, 徐盛, 黄盖, 王异, 张辽, 徐晃
    2: [310101, 310401, 320101, 320401, 330301, 340101, 340601],
    // 3: 全自选
    // 陈到, 张郃, 李嗣业, 文鸯, 徐盛, 曹仁, 张飞, 黄盖, 刘宠, 王异, 曹休, 张辽, 许诸, 夏侯渊, 徐晃
    4: [310101, 310201, 310401, 310601, 320101, 320201, 320301, 320401, 330202, 330301, 330501, 340101, 340401, 340501, 340601],
}

// 英雄复活时间
const HERO_REVIVES_TIME = 3600000 * 5

// 英雄槽位等级开启条件
const HERO_SLOT_LV_COND = [1, 10, 20]

// 养由基召唤时的对应等级
const SUMMON_LV = {
    1: 1,
    2: 2,
    3: 4,
    4: 6,
    5: 8,
    6: 10,
}

// 默认的宠物id
const DEFAULT_PET_ID = 4101
// 矛
const SPEAR_PAWN_ID = 3701
// 火
const FIRE_PAWN_ID = 3702

// 资源
const RES_MAP = {
    [CType.CEREAL]: true,
    [CType.TIMBER]: true,
    [CType.STONE]: true,
    [CType.BASE_RES]: true,
    [CType.EXP_BOOK]: true,
    [CType.IRON]: true,
    [CType.UP_SCROLL]: true,
    [CType.FIXATOR]: true,
}

// 最多可标记多少个
const MAX_MAP_MARK_COUNT = 10

// 申请联盟个数限制
const ALLI_APPLY_MAX_COUNT = 3

// 大厅模式对应的底部
const LOBBY_MODE_BUTTOM_NAME = {
    [LobbyModeType.FREE]: 'team',
    [LobbyModeType.NEWBIE]: 'team',
    [LobbyModeType.RANKED]: 'team',
    [LobbyModeType.SNAIL_ISLE]: 'twomiles',
}

// 斜度
const SKEW_ANGLE = 45
// 斜着的外宽高
const SKEW_SIZE = cc.size(16, 8)
// 斜着的内宽高 32 16
const SKEW_SIZE_HALF = cc.size(SKEW_SIZE.width * 0.5, SKEW_SIZE.height * 0.5)


// 段位商城的兵符配置
const RANK_SHOP_WAR_TOKEN_CONFIG = [
    { warToken: 10, coin: 10 },
    { warToken: 100, coin: 100 },
    { warToken: 1000, coin: 1000 },
    { warToken: 10000, coin: 10000 },
]

// 战斗的血条颜色
const BATTLE_HPBAR_COLOR = {
    m: { bar: '#8BE273', bg: '#162D20' },
    f: { bar: '#6DB5E2', bg: '#121D3A' },
    0: { bar: '#EE2A4A', bg: '#3B1316' },
    1: { bar: '#FF64B8', bg: '#41142C' },
    2: { bar: '#AD64FF', bg: '#281240' },
    3: { bar: '#FF9648', bg: '#4A2B14' },
}

// 战斗的火焰颜色
const BATTLE_FIRE_COLOR = {
    m: '#53B977',
    f: '#40A4E9',
    0: '#B90900',
    1: '#FF76F7',
    2: '#AD64FF',
    3: '#FFA836',
}

// 战令价格配置
const RECHARGE_BATTLE_PASS = 'jwm_up_book' // $8.99

// 战令经验购买配置
const RECHARGE_BATTLE_PASS_EXP = [50, 100] // 50元宝购买100经验

// 有奖问卷调查id
const PRIZE_QUESTION_ID = 99900001

// 有奖问卷调查期限
const PRIZE_QUESTION_TIME = ['2024-12-26-06-00', '2024-12-30-06-00']

// 打开允许通知弹窗的公共CD
const NOTICE_PERMISSION_CD = 24 * 60 * 60 * 1000 // 24小时

// 每日屯田次数
const TODAY_TONDEN_MAX_COUNT = 10
// 屯田资源获取比例
const TONDEN_GET_RES_RATIO = 1
// 屯田奖励点消耗倍数
const TONDEN_STAMINA_MUL = 3

// 医馆伤兵上限
const HOSPITAL_PAWN_LIMIT = 200

// 各等级士兵战败后回馆概率
const GO_HOSPITAL_CHANCE = {
    1: '0%',
    2: '100%',
    3: '100%',
    4: '100%',
    5: '100%',
    6: '100%',
}

// 画像的天选几率
const PORTRAYAL_CHOSENONE_ODDS = 0.005

// 研究类型转评论类型
const STUDY_TO_BOOKTYPE = {
    [StudyType.POLICY]: BookCommentType.POLICY,
    [StudyType.PAWN]: BookCommentType.PAWN,
    [StudyType.EQUIP]: BookCommentType.EQUIP,
    [StudyType.EXCLUSIVE]: BookCommentType.EQUIP,
}

// 盟主投票最大次数
const ALLI_LEADER_VOTE_MAX_COUNT = 4

// 招募动态资源每级系数
const PAWN_COST_LV_LIST = [1, 2, 4, 6, 8, 10]

// 工厂解锁配置
const FACTORY_SLOT_CONF = [5]

// 摄像机背景颜色
const CAMERA_BG_COLOR = ['#ACC961', '#88CA6E', '#E4B765', '#A7E2E3']

//
const MAP_MASK_ITEM_COLOR = ['#2B8A85', '#1755AC', '#832E4F', '#8378C2']

// 区域内的地面颜色
const AREA_DI_COLOR_CONF = [
    // 春
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头
        10: { bg: '#AFC864', battle: ['#CED974', '#BCD16A'], build: '#EFE28C' }, //主城
    },
    // 夏
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' }, //主城
    },
    // 秋
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' }, //主城
    },
    // 冬
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' }, //主城
    },
]

// 难度底板颜色
const DIFFICULTY_BG_COLOR = {
    1: '#81A514',
    2: '#6683AB',
    3: '#9C58BF',
    4: '#CE59A0',
    5: '#C34B3F',
}

export {
    CLICK_SPACE,
    MAP_SHOW_OFFSET,
    TILE_SIZE,
    TILE_SIZE_HALF,
    MAP_EXTRA_SIZE,
    BUILD_DRAG_OFFSETY,
    AREA_MAX_ZINDEX,
    MAX_ZINDEX,
    LONG_PRESS_TIME,
    DELAY_CLOSE_PNL_TIME,
    CITY_MAIN_NID,
    BUILD_FLAG_NID,
    CITY_FORT_NID,
    ANCIENT_WALL_ID,
    CITY_CHANGAN_ID,
    CITY_JINLING_ID,
    CITY_YANJING_ID,
    CITY_LUOYANG_ID,
    BUILD_FARM_ID,
    BUILD_TIMBER_ID,
    BUILD_QUARRY_ID,
    BUILD_WALL_NID,
    BUILD_MAIN_NID,
    BUILD_GRANARY_NID,
    BUILD_WAREHOUSE_NID,
    BUILD_BARRACKS_NID,
    BUILD_EMBASSY_NID,
    BUILD_BAZAAR_NID,
    BUILD_SMITHY_NID,
    BUILD_DRILLGROUND_NID,
    BUILD_PLANT_NID,
    BUILD_ALLI_BAZAAR_NID,
    BUILD_HEROHALL_NID,
    BUILD_HOSPITAL_NID,
    BUILD_FORT_NID,
    BUILD_TOWER_NID,
    PAWN_CROSSBOW_ID,
    AX_CAVALRY_ID,
    INIT_RES_CAP,
    INIT_RES_COUNT,
    INIT_RES_OUTPUT,
    DEFAULT_BT_QUEUE_COUNT,
    IN_DONE_BT_GOLD,
    IN_DONE_FORGE_GOLD,
    MODIFY_NICKNAME_GOLD,
    ARMY_PAWN_MAX_COUNT,
    CREATE_ALLI_MAX_LV,
    DEFAULT_CITY_SIZE,
    DEFAULT_AREA_SIZE,
    DEFAULT_BUILD_SIZE,
    BOSS_BUILD_SIZE,
    DEFAULT_MAX_ARMY_COUNT,
    DEFAULT_MAX_ADD_PAWN_TIMES,
    UP_MARCH_SPEED_MUL,
    MAIN_CITY_MARCH_SPEED,
    TRANSIT_TIME,
    BATTLE_MAX_TIME,
    LAND_DI_CONF,
    DECORATION_MUD_CONF,
    BORDER_LINE_CONF,
    RIVER_LINE_CONF,
    SELECT_CELL_INFO_BOX,
    CELL_RES_FIELDS,
    PAWN_BUBBLE_OFFSETY,
    RES_FIELDS_CTYPE,
    CTYPE_ICON_URL,
    CTYPE_ICON,
    CTYPE_NAME,
    BUILD_EFFECT_TYPE_CONF,
    MARCH_ARMY_NAME_COLOR,
    MARCH_ARMY_TIME_COLOR,
    ARMY_STATE_COLOR,
    MAIL_STATE_COLOR,
    ARMY_RECORD_DESC_CONF,
    PLAYBACK_MULS,
    FIXATION_MENU_CONFIG,
    FIXATION_MENU_MAX_COUNT,
    FREE_HEAD_ICONS,
    ADD_OUTPUT_GOLD,
    ADD_OUTPUT_RATIO,
    ADD_OUTPUT_TIME,
    POLICY_SLOT_CONF,
    EQUIP_SLOT_CONF,
    EQUIP_SLOT_EXCLUSIVE_LV,
    EQUIP_SMELT_NEED_LV,
    PAWN_SLOT_CONF,
    RESET_STUDY_SLOT_GOLD,
    CAN_EXIT_ALLI_TIME,
    CREATE_ALLI_COST,
    CREATE_ALLI_COND,
    ONE_USER_POPULARITY_CHANGE_INTERVAL,
    BUFF_SHOW_TYPE_TRAN,
    SHIELD_BUFF,
    BATTLE_EFFECT_TYPE,
    BUFF_NODE_ZINDEX,
    NEED_SHOW_BUFF,
    NEED_MUTUAL_BUFF,
    CHAT_BARRAGE_COLOR,
    REPLACEMENT_SERVICE_CHARGE,
    REPLACEMENT_MIN_RES_COUNT,
    REPLACEMENT_TODAY_COUNT_MAP,
    RES_TRANSIT_CAP,
    UP_RECRUIT_PAWN_MUL,
    LANGUAGE_TEXT_LIST,
    LOGOUT_MAX_DAY,
    ALLI_JOB_DESC,
    ALLI_JOB_COUNT,
    NOT_OCCUPY_BY_SERVER_RUNTIME,
    NOT_OCCUPY_BY_MAX_LAND_COUNT,
    CONCURRENT_GAME_LIMIT,
    LAND_SCORE_CONF,
    REMOVE_PCHAT_TIME,
    OCCUPY_PLAYER_CELL_MIN_DIS,
    NOLIMIT_PCHAT_MAX_LAND,
    SHOW_TIME_MAX_INTERVAL,
    FRIENDS_MIN_LAND_COUNT,
    BATTLE_FORECAST_COST,
    BATTLE_FORECAST_FREE_COUNT,
    OPEN_ALL_TREASURE_MIN_LAND_COUNT,
    CAN_MIN_MARCH_SPEED,
    SEASON_DURATION_TIME,
    ANCIENT_SUP_COST_MUL,
    ANCIENT_SUP_TIME,
    COLOR_NORMAL,
    CHAT_MAX_COUNT,
    CHAT_SEND_INTERVAL,
    CHAT_TOLERATE_MAX_COUNT,
    CHAT_REST_MAX_TIME,
    CHAT_BANNED_REST_MAX_TIME,
    SEND_TRUMPET_COST,
    SEND_TRUMPET_ACC_COST,
    SERVER_APPLY_CANCEL_CD,
    NEXT_APPLY_CD,
    POINTSETS_ONE_COST,
    POINTSETS_ONE_GOLD_COST,
    PORTRAYAL_COMP_NEED_COUNT,
    RESTORE_PORTRAYAL_WAR_TOKEN_COST,
    RESTORE_PORTRAYAL_GOLD_COST,
    BUY_OPT_HERO_COST,
    HERO_OPT_GIFT,
    HERO_REVIVES_TIME,
    HERO_SLOT_LV_COND,
    SUMMON_LV,
    DEFAULT_PET_ID,
    SPEAR_PAWN_ID,
    FIRE_PAWN_ID,
    RES_MAP,
    MAX_MAP_MARK_COUNT,
    ALLI_APPLY_MAX_COUNT,
    LOBBY_MODE_BUTTOM_NAME,
    SKEW_ANGLE,
    SKEW_SIZE,
    SKEW_SIZE_HALF,
    RANK_SHOP_WAR_TOKEN_CONFIG,
    BATTLE_HPBAR_COLOR,
    BATTLE_FIRE_COLOR,
    RECHARGE_BATTLE_PASS,
    RECHARGE_BATTLE_PASS_EXP,
    PRIZE_QUESTION_ID,
    PRIZE_QUESTION_TIME,
    NOTICE_PERMISSION_CD,
    TODAY_TONDEN_MAX_COUNT,
    TONDEN_GET_RES_RATIO,
    TONDEN_STAMINA_MUL,
    HOSPITAL_PAWN_LIMIT,
    GO_HOSPITAL_CHANCE,
    PORTRAYAL_CHOSENONE_ODDS,
    STUDY_TO_BOOKTYPE,
    ALLI_LEADER_VOTE_MAX_COUNT,
    PAWN_COST_LV_LIST,
    FACTORY_SLOT_CONF,
    CAMERA_BG_COLOR,
    MAP_MASK_ITEM_COLOR,
    AREA_DI_COLOR_CONF,
    DIFFICULTY_BG_COLOR,
}