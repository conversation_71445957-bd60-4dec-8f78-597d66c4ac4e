import { CTYPE_ICON, TILE_SIZE_HALF } from "../constant/Constant"
import { CType } from "../constant/Enums"
import OutlineShaderCtrl from "../shader/OutlineShaderCtrl"
import { gameHpr } from "./GameHelper"

/**
 * 游戏中的资源相关帮助方法
 */
class ResHelper {

    private lands: { [key: string]: cc.SpriteFrame } = {}
    private landMap: { [key: number]: { [key: string]: cc.SpriteFrame } } = {}
    private seawavePrefabs: { [key: string]: cc.Prefab } = {}
    private cityPrefabs: { [key: string]: cc.Prefab } = {}
    private mapFlagNumbers: { [key: string]: cc.SpriteFrame } = {}

    private spriteDefaultMaterial: cc.Material = null
    private sprite2dGrayMaterial: cc.Material = null

    public async init(progessCallback: (percent: number) => void) {
        this.lands = {}
        assetsMgr.debug = false
        let sfs = await assetsMgr.loadTempRseDir('land', cc.SpriteFrame, '_land_res_', (done, total) => progessCallback(done / total))
        sfs.forEach(m => this.lands[m.name] = m)
        let pfbs = await assetsMgr.loadTempRseDir('seawave', cc.Prefab, '_seawave_prefab_', (done, total) => progessCallback(done / total))
        pfbs.forEach(m => this.seawavePrefabs[m.name] = m)
        pfbs = await assetsMgr.loadTempRseDir('city', cc.Prefab, '_city_prefab_', (done, total) => progessCallback(done / total))
        pfbs.forEach(m => this.cityPrefabs[m.name] = m)
        // 地图标记数字
        sfs = await assetsMgr.loadTempRseDir('map_flag_num', cc.SpriteFrame, '_land_res_', (done, total) => progessCallback(done / total))
        sfs.forEach(m => this.mapFlagNumbers[m.name] = m)
        assetsMgr.debug = true
        // 材质
        this.spriteDefaultMaterial = cc.Material.getBuiltinMaterial('2d-sprite')
        this.sprite2dGrayMaterial = cc.Material.getBuiltinMaterial('2d-gray-sprite')
    }

    public async initNovice(key: string) {
        const pfb = await assetsMgr.loadTempRes('other/CITY_10010', cc.Prefab, key)
        this.cityPrefabs[pfb.name] = pfb
    }

    public cleanNovice() {
        delete this.cityPrefabs['CITY_10010']
    }

    public getPawnName(id: number) { return 'pawnText.name_' + id }

    public checkLandSkin(type: number) {
        return !!this.landMap[type]
    }

    // 初始化地块皮肤
    public async initLandSkin(type: number, progessCallback?: (percent: number) => void) {
        let obj = this.landMap[type]
        if (obj) {
            return
        }
        const sfs = await assetsMgr.loadTempRseDir('land_' + type, cc.SpriteFrame, '_land_res_' + type, (done, total) => progessCallback && progessCallback(done / total))
        obj = this.landMap[type] = {}
        sfs.forEach(m => obj[m.name] = m)
    }

    public cleanLandSkin() {
        const type = gameHpr.world.getSeason().type
        for (let k in this.landMap) {
            if (Number(k) !== type) {
                delete this.landMap[k]
                assetsMgr.releaseTempResByTag('_land_res_' + k)
            }
        }
    }

    // 根据下标获取节点
    public getNodeByIndex(node: cc.Node, i: number, position: cc.Vec2) {
        const it = node.children[i] || cc.instantiate2(node.children[0], node)
        it.active = true
        it.Data = null
        it.setPosition(position)
        return it
    }
    public getNodeByIndex2(node: cc.Node, i: number, position: cc.Vec2) {
        const it = node.children[i] || cc.instantiate2(node.children[0], node)
        it.active = true
        it.Data = null
        it.setPosition(position.x, position.y - TILE_SIZE_HALF.y)
        return it
    }

    // 隐藏多于的
    public hideNodeByIndex(node: cc.Node, idx: number) {
        for (let i = idx, l = node.childrenCount; i < l; i++) {
            const it = node.children[i]
            it.active = false
            it.Data = null
        }
    }

    // 清理子节点只剩1个
    public cleanNodeChildren(node: cc.Node) {
        for (let i = node.childrenCount - 1; i >= 1; i--) {
            node.children[i].destroy()
        }
        node.children[0]?.setActive(false)
    }

    // 获取sprite材质
    public get2dSpriteMaterial(unlock: boolean) {
        return unlock ? this.spriteDefaultMaterial : this.sprite2dGrayMaterial
    }

    // 纯色灰材质
    public getSpriteColorGrayMaterial(unlock: boolean) {
        return unlock ? this.spriteDefaultMaterial : assetsMgr.getMaterial('SpriteColorGrey')
    }

    // 地面icon
    public getLandIcon(icon: string) {
        if (icon.startsWith('land_')) {
            return this.getLandItemIcon(icon, gameHpr.world.getSeasonType())
        }
        return this.lands[icon]
    }

    // 地面资源icon
    public getLandItemIcon(icon: string, type: number) {
        return this.landMap[type]?.[icon] || this.lands[icon] || null
    }

    // 获取海浪预制体
    public getSeawavePrefab(id: number) {
        return this.seawavePrefabs['SEAWAVE_' + id]
    }

    // 获取城市预制体
    public getCityPrefab(id: number) {
        return this.cityPrefabs['CITY_' + id]
    }

    // 获取资源icon
    public getResIcon(type: CType) {
        return assetsMgr.getImage(CTYPE_ICON[type])
    }

    // 获取地图标记数字
    public getMapFlagNumber(flag: number) {
        const key = flag <= 0 ? 'x' : (flag - 1)
        return this.mapFlagNumbers['map_flag_' + key]
    }

    // 加载icon
    public async loadIcon(url: string, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {
        if (!url || !icon || !icon.isValid) {
            return null
        }
        const spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite)
        if (!spr) {
            return null
        } else if (setEmpty) {
            spr.spriteFrame = null
        }
        spr['__load_icon_url'] = url
        const sf = await assetsMgr.loadTempRes(url, cc.SpriteFrame, key)
        if (spr.isValid && spr['__load_icon_url'] === url) {
            spr.spriteFrame = sf || null
        }
        return spr
    }

    // 加载设施icon
    public async loadBuildIcon(url: string, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('build/' + url, icon, key)
    }

    // 加载城市icon
    public async loadCityIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('city/city_' + id, icon, key)
    }

    // 加载士兵头像icon
    public async loadPawnHeadIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        if (id === 3405104) { //牛仔隐藏款 特殊处理
            return this.loadIcon(`role/${id}/role_${id}_00`, icon, key)
        }
        return this.loadIcon(`role/${id}/role_${id}_01`, icon, key)
    }

    // 加载士兵小头像icon
    public async loadPawnHeadMiniIcon(id: number, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {
        return this.loadIcon(`role/${id}/role_${id}`, icon, key, setEmpty)
    }

    // 加载技能图标
    public async loadSkillIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('skill/skill_' + id, icon, key)
    }

    // 加载装备icon
    public async loadEquipIcon(id: number, icon: cc.Sprite | cc.Node, key: string, smeltLv?: number) {
        const spr = await this.loadIcon('equip/equip_' + id, icon, key)
        if (!spr || !spr.isValid) {
        } else if (smeltLv) {
            // const size = spr.node.getContentSize()
            const outline = spr.getComponent(OutlineShaderCtrl) || spr.addComponent(OutlineShaderCtrl)
            outline.setTarget(spr)
            outline.setOutlineSize(2)
            outline.setColor(ut.colorFromHEX(smeltLv === 1 ? '#58F1FF' : '#E488FF'))
            outline.setVisible(true)
            // spr.node.adaptScale(size, cc.size(size.width + 8, size.height + 8))
        } else {
            spr.getComponent(OutlineShaderCtrl)?.setVisible(false)
            // spr.node.scale = 1
        }
    }

    // 加载政策icon
    public async loadPolicyIcon(id: number, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {
        return this.loadIcon('policy/policy_' + id, icon, key, setEmpty)
    }

    // 加载buff icon
    public async loadBuffIcon(id: number, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {
        return this.loadIcon('buff_icon/buff_' + id, icon, key, setEmpty)
    }

    // 加载联盟图标
    public async loadAlliIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('alli_icon/alli_icon_' + id, icon, key)
    }

    // 加载评分图标
    public async loadRankScoreIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('rank_icon/rank_icon_' + (id >= 0 ? id : 'none'), icon, key)
    }

    // 加载礼物图标
    public async loadGiftIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('gift/gift_' + id, icon, key)
    }

    // 加载表情
    public async loadEmojiIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('emoji/emoji_' + id, icon, key)
    }

    // 加载画像
    public async loadPortrayalImage(id: number, icon: cc.Sprite | cc.Node, key: string) {
        if (assetsMgr.getJsonData('portrayalBase', id)?.has_anim) {
            return this.loadIcon(`portrayal_anim/${id}/portrayal_${id}_01`, icon, key)
        }
        return this.loadIcon('portrayal/portrayal_' + id, icon, key)
    }

    // 加载残卷遮挡
    public async loadPortrayalDebrisMask(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('portrayal/pd_mask_' + id, icon, key)
    }

    // 加载英雄技能图标
    public async loadHeroSkillIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('hero_skill_icon/hero_skill_' + id, icon, key)
    }

    // 加载英雄预制体
    public async loadHeroMarchPrefab(id: number, key: string) {
        const pfbName = 'ROLE_' + id
        const pfb = await assetsMgr.loadTempRes('march/' + pfbName, cc.Prefab, key)
        if (pfb?.name !== pfbName) {
            return null
        }
        return pfb
    }

    // 加载植物种子图标
    public async loadBotanySeedIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {
        return this.loadIcon('botany_seed/botany_seed_' + id, icon, key)
    }

    // 加载表情节点
    public async loadEmojiNode(id: number, root: cc.Node, scale: number, key: string, setEmpty: boolean = true) {
        if (!root || !id) {
            return
        } else if (setEmpty) {
            root.removeAllChildren()
        }
        const node = await nodePoolMgr.get('emoji/EMOJI_' + id, key)
        if (node && root.isValid) {
            node.parent = root
            node.active = true
            node.setPosition(0, 0)
            node.scaleX = scale || 1
        }
    }

    // 加载玩家头像
    public async loadPlayerHead(icon: cc.Sprite | cc.Node, url: string, key: string, setEmpty?: boolean) {
        if (!icon?.isValid) {
            return
        }
        const spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite)
        if (!spr) {
            return
        } else if (setEmpty) {
            spr.spriteFrame = null
            spr.node.removeAllChildren()
        }
        url = spr['_player_head_icon_'] = url || 'head_icon_free_001'
        let val: any = null
        if (url.startsWith('head_icon_anim_')) {
            val = await assetsMgr.loadTempRes('headicon/' + url, cc.Prefab, key)
        } else if (url.startsWith('head_icon_')) {
            val = await assetsMgr.loadTempRes('headicon/' + url, cc.SpriteFrame, key)
        } else {
            val = await assetsMgr.loadRemote(url, '.jpg', key || '_player_head_')
        }
        if (spr.isValid && spr['_player_head_icon_'] === url) {
            spr.node.removeAllChildren()
            if (!val) {
                spr.spriteFrame = null
            } else if (val instanceof cc.Prefab) {
                spr.spriteFrame = null
                const node = cc.instantiate2(val, spr.node)
                node.setContentSize(spr.node.width * spr.node.scaleX, spr.node.height * spr.node.scaleY)
            } else if (val instanceof cc.SpriteFrame) {
                spr.spriteFrame = val
            }
        }
    }
}

export const resHelper = new ResHelper()
if (cc.sys.isBrowser) {
    window['resHelper'] = resHelper
}