import NetworkModel from "../common/NetworkModel"
import BuildObj from "./BuildObj"
import AreaObj from "./AreaObj"
import { BUILD_NID, NotifyType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { gameHpr } from "../../common/helper/GameHelper"
import { ecode } from "../../common/constant/ECode"
import { netHelper } from "../../common/helper/NetHelper"
import PawnObj from "./PawnObj"
import PawnDrillInfoObj from "../main/PawnDrillInfoObj"
import PawnLevelingInfoObj from "../main/PawnLevelingInfoObj"
import PlayerModel from "../main/PlayerModel"
import { eventReportHelper } from "../../common/helper/EventReportHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import PawnCureInfoObj from "../main/PawnCureInfoObj"

/**
 * 战场管理中心
 */
@mc.addmodel('areaCenter')
export default class AreaCenterModel extends mc.BaseModel {

    private readonly AREA_MAX_LIFE_TIME: number = 60 * 1000 * 1 //区域最大存在时间

    private areas: Map<number, AreaObj> = new Map()
    private watchPlayers: Map<number, { uid: string, time: number }[]> = new Map() //区域观战玩家

    private net: NetworkModel = null
    private player: PlayerModel = null

    private lookArea: AreaObj = null //当前查看的区域

    public onCreate() {
        this.net = this.getModel('net')
        this.player = this.getModel('player')
    }

    // 初始化监听
    public initNetEvent() {
        this.net.on('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this)
        this.net.on('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this)
    }

    public clean() {
        this.net.off('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this)
        this.net.off('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this)
        this.cleanAreas()
        this.watchPlayers.clear()
    }

    public getArea(index: number) {
        if (this.lookArea?.index === index) {
            return this.lookArea
        }
        return this.areas.get(gameHpr.amendAreaIndex(index))
    }

    public addArea(area: AreaObj) {
        this.areas.set(area.index, area)
        return area
    }

    public removeArea(index: number) {
        this.getArea(index)?.clean()
        this.delArea(index)
    }

    private delArea(index: number) {
        this.areas.delete(index)
        netHelper.sendLeaveArea({ index })
    }

    public cleanAreas() {
        this.areas.forEach(m => m.clean())
        this.areas.clear()
        if (this.lookArea?.index >= 0) {
            this.lookArea = null
        }
    }

    // 获取当前场景的区域信息
    public getLookArea() {
        if (!this.lookArea) {
            this.lookArea = this.getArea(gameHpr.world.getLookCell()?.index ?? -1)
        }
        return this.lookArea
    }
    public setLookArea(area: AreaObj) {
        this.lookArea = area
    }

    // 获取自己的主城市
    public getMeMainArea() {
        return this.areas.get(this.player.getMainCityIndex())
    }

    // 获取军队
    public getArmy(index: number, uid: string) {
        return this.getArea(index)?.getArmyByUid(uid)
    }

    // 请求战场信息
    private async reqAreaInfo(index: number, reinit?: boolean) {
        const { err, data } = await netHelper.reqGetAreaInfo({ index })
        const areaInfo = data?.data
        if (!areaInfo) {
            return null
        } else if (areaInfo.battle) {
            this.checkRemoveBattleArea()
        }
        const area = new AreaObj().init(areaInfo)
        this.areas.set(index, area)
        // 是否有战斗
        if (areaInfo.battle) {
            area.battleBegin(areaInfo.battle, !reinit)
        }
        // 如果有不一样 表示没更新到 这里兼容同步一下地块信息
        if (area.owner && gameHpr.world.getMapCellByIndex(index)?.owner !== area.owner) {
            gameHpr.world.syncServerCellInfo(index)
        }
        return area
    }

    // 获取战场信息没有就请求
    public async reqAreaByIndex(index: number, reinit?: boolean) {
        if (index === -1) {
            return null
        }
        const cell = gameHpr.world.getMapCellByIndex(index)
        const owner = cell.owner || ''
        index = cell?.city?.index ?? index //修正一下index 有可能点到其他点
        let area = this.areas.get(index)
        if (!area || area.owner !== owner) {
            area = await this.reqAreaInfo(index, reinit)
        }
        return area
    }

    // 升级建筑
    public async upBuildToServer(item: BuildObj) {
        if (!gameHpr.checkCTypes(item.upCost)) {
            return ecode.RES_NOT_ENOUGH
        } else if (this.player.getBtQueues().has('id', item.id)) {
            return ecode.YET_IN_BTQUEUE
        }
        const { err, data } = await netHelper.reqUpAreaBuild({ index: item.aIndex, uid: item.uid })
        if (err) {
            return err
        }
        this.player.updateOutputByFlags(data.output)
        this.player.updateBtQueue(data.queues)
        // 买量数据上报
        if (item.id === BUILD_NID.MAIN) {
            if (item.lv === 4) {
                eventReportHelper.reportGlobalEventOne('cap_lv_5')
            } else if (item.lv === 9) {
                eventReportHelper.reportGlobalEventOne('cap_lv_10')
            } else if (item.lv === 14) {
                eventReportHelper.reportGlobalEventOne('cap_lv_15')
            } else if (item.lv === 19) {
                eventReportHelper.reportGlobalEventOne('cap_lv_20')
            }
        } else if (item.id === BUILD_NID.SMITHY) {
            if (item.lv === 9) {
                eventReportHelper.reportGlobalEventOne('smithy_lv_10')
            } else if (item.lv === 14) {
                eventReportHelper.reportGlobalEventOne('smithy_lv_15')
            } else if (item.lv === 19) {
                eventReportHelper.reportGlobalEventOne('smithy_lv_20')
            }
        } else if (item.id === BUILD_NID.CAMP) {
            if (item.lv === 0) {
                eventReportHelper.reportGlobalEventOne('frontier_garrison')
            } else if (item.lv === 4) {
                eventReportHelper.reportGlobalEventOne('frontier_lv_5')
            }
        }
        return ''
    }

    // 修建建筑
    public async unlockBuildToServer(index: number, id: number) {
        const { err, data } = await netHelper.reqAddAreaBuild({ index, id })
        if (!err) {
            this.player.updateOutputByFlags(data.output)
            this.player.updateBtQueue(data.queues)
            this.getArea(index)?.addBuild(data.build)
        }
        return err
    }

    // 训练士兵
    public async drillPawnToServer(index: number, buildUid: string, id: number, armyUid: string, armyName: string) {
        const { err, data } = await netHelper.reqDrillPawn({ index, buildUid, id, armyUid, armyName })
        if (!err) {
            this.getArea(index)?.updateArmyDrillPawns(data.army)
            this.player.updateOutputByFlags(data.output)
            this.player.updatePawnDrillQueue(data.queues)
            this.player.setFreeRecruitPawnCount(data.freeRecruitPawnCount || 0)
            this.player.setUpRecruitPawnCount(data.upRecruitPawnCount || 0)
        }
        return { err, army: data?.army }
    }

    // 治疗士兵
    public async curePawnToServer(index: number, armyUid: string, armyName: string, pawnUid: string) {
        const { err, data } = await netHelper.reqCurePawn({ index, armyUid, armyName, pawnUid })
        if (!err) {
            this.getArea(index)?.updateArmyCurePawns(data.army)
            this.player.updateOutputByFlags(data.output)
            this.player.updatePawnCuringQueue(data.queues)
            this.player.setFreeCurePawnCount(data.freeCurePawnCount || 0)
        }
        return { err, list: data?.queues, army: data?.army }
    }

    // 士兵练级
    public async pawnLvingToServer(index: number, auid: string, puid: string) {
        const { err, data } = await netHelper.reqPawnLving({ index, auid, puid })
        if (!err) {
            this.player.updateRewardItemsByFlags(data.cost)
            this.player.updatePawnLevelingQueue(data.queues)
            this.player.setFreeLevingPawnCount(data.freeLevingPawnCount || 0)
        }
        return { err, armyUid: data?.army?.uid }
    }

    // 创建一个士兵 根据招募信息
    public createPawnByDrillInfo(data: PawnDrillInfoObj) {
        const conf = this.player.getConfigPawnInfo(data.id)
        const pawn = new PawnObj().init(data.id, conf.equip, 1, conf.skinId).initAnger()
        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed
        pawn.lv = data.lv
        pawn.armyUid = data.auid
        pawn.armyName = this.getArea(data.index)?.getArmyByUid(data.auid)?.name || ''
        return pawn
    }

    // 创建一个士兵 根据治疗信息
    public createPawnByCureInfo(data: PawnCureInfoObj) {
        const conf = this.player.getConfigPawnInfo(data.id)
        const pawn = new PawnObj().init(data.id, conf.equip, 1, conf.skinId).initAnger()
        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed
        pawn.lv = data.lv
        pawn.armyUid = data.auid
        pawn.armyName = this.getArea(data.index)?.getArmyByUid(data.auid)?.name || ''
        return pawn
    }

    // 创建一个士兵 根据练级信息
    public createPawnByLvingInfo(pawn: PawnObj, data: PawnLevelingInfoObj) {
        const newPawn = new PawnObj().init(pawn.id, pawn.equip, data.lv, pawn.skinId).initAnger()
        newPawn.aIndex = pawn.aIndex
        newPawn.owner = pawn.owner
        newPawn.uid = pawn.uid
        newPawn.armyUid = pawn.armyUid
        newPawn.armyName = pawn.armyName
        newPawn.attackSpeed = pawn.attackSpeed
        newPawn.treasures = pawn.treasures
        newPawn.portrayal = pawn.portrayal?.clone()
        if (newPawn.portrayal) {
            newPawn.updateAttr()
        }
        return newPawn
    }

    // 检测删除多的战斗区域
    private checkRemoveBattleArea() {
        const loolAreaIndex = gameHpr.world.getLookCell()?.index
        let leaveTime = 0, count = 0, area: AreaObj = null
        this.areas.forEach(m => {
            if (!m.active && m.index >= 0 && m.isBattleing() && loolAreaIndex !== m.index && m.leaveTime > 0) {
                count += 1
                if (leaveTime < m.leaveTime) {
                    leaveTime = m.leaveTime
                    area = m
                }
            }
        })
        if (count >= 5 && area) {
            area.clean()
            this.delArea(area.index)
            // cc.log('checkRemoveBattleArea index=' + area.index)
        }
    }

    // 获取观战玩家列表
    public getAreaWatchPlayersByIndex(index: number) {
        return this.watchPlayers.get(index) || []
    }

    public update(dt: number) {
        const isNoviceMode = gameHpr.isNoviceMode
        const loolAreaIndex = gameHpr.world.getLookCell()?.index
        const now = Date.now(), life = this.AREA_MAX_LIFE_TIME
        this.areas.forEach(m => {
            if (!isNoviceMode && !m.active && m.index >= 0 && loolAreaIndex !== m.index && m.leaveTime > 0 && now - m.leaveTime >= life) {
                m.clean()
                this.delArea(m.index)
                // cc.log('delArea index=' + m.index)
            } else {
                m.update(dt)
            }
        })
    }
    // ----------------------------------------- net listener function --------------------------------------------

    // 更新战场信息
    private OnUpdateAreaInfo(res: any) {
        const type = res.type, index = res.index, data = res['data_' + type]
        cc.log('OnUpdateAreaInfo', NotifyType[type], index, data)
        const area = this.getArea(index)
        if (type === NotifyType.AREA_PLAYER_CHANGE) { //通知战场人员变动
            if (data?.length) {
                this.watchPlayers.set(index, data)
            } else {
                this.watchPlayers.delete(index)
            }
            this.emit(EventType.UPDATE_AREA_WATCH_PLAYER, index)
        } else if (type === NotifyType.AREA_CHAT) { //通知战场聊天
            this.emit(EventType.ADD_AREA_CHAT, index, data)
        } else if (!area) {
        } else if (type === NotifyType.BUILD_UP) { //建筑升级
            area.buildUp(data.uid, data.lv)
        } else if (type === NotifyType.MOVE_BUILD) { //移动建筑
            const build = area.getBuildByUid(data.uid)
            if (build) {
                build.point.set(data.point)
                this.emit(EventType.UPDATE_BUILD_POINT, build)
            }
        } else if (type === NotifyType.ADD_BUILD) { //添加建筑
            area.addBuild(data)
        } else if (type === NotifyType.REMOVE_BUILD) { //删除建筑
            area.removeBuild(data)
        } else if (type === NotifyType.ADD_ARMY) { //添加军队
            area.addArmy(data)
        } else if (type === NotifyType.REMOVE_ARMY) { //删除军队
            area.removeArmy(data)
        } else if (type === NotifyType.UPDATE_ARMY) { //更新军队
            area.updateArmy(data)
        } else if (type === NotifyType.UPDATE_ALL_PAWN_HP) { //更新所有士兵血量
            area.updateAllArmy(data)
        } else if (type === NotifyType.MOVE_PAWN) { //移动士兵
            const army = area.getArmyByUid(data.armyUid)
            if (army) {
                army.setPawnsPoint(data.pawns || [])
                if (!army.isOwner()) { //不是我自己就通知一下
                    eventCenter.emit(EventType.UPDATE_ARMY, army)
                }
            }
        } else if (type === NotifyType.CHANGE_PAWN_ATTR) { //改变士兵属性
            data?.forEach(m => {
                const pawn = area.getPawnByPrecise(m.armyUid, m.uid)
                if (pawn) {
                    pawn.setAttackSpeed(m.attackSpeed)
                    pawn.changeSkin(m.skinId)
                    pawn.changeEquip(m.equip)
                }
            })
            this.emit(EventType.UPDATE_AREA_ARMY_LIST, index)
        } else if (type === NotifyType.CHANGE_PAWN_PORTRAYAL) { //改变士兵化身
            const pawn = area.getPawnByPrecise(data.armyUid, data.uid)
            if (pawn) {
                pawn.setPortrayal(data.portrayal)
            }
        } else if (type === NotifyType.UPDATE_PAWN_TREASURE) { //刷新士兵宝箱
            const pawn = area.getPawnByPrecise(data.armyUid, data.uid)
            if (pawn) {
                pawn.updateTreasures(data.treasures)
                this.emit(EventType.UPDATE_PAWN_TREASURE, pawn)
                area.updateTreasureReddot()
            }
        } else if (type === NotifyType.UPDATE_ARMY_TREASURES) { //刷新军队宝箱
            const army = area.getArmyByUid(data.armyUid)
            if (army) {
                data.pawnTreasures.forEach(m => {
                    const pawn = army.pawns.find(p => p.uid === m.uid)
                    if (pawn) {
                        pawn.updateTreasures(m.treasures)
                    }
                })
                this.emit(EventType.UPDATE_ARMY_TREASURES, army)
                area.updateTreasureReddot()
            }
        } else if (type === NotifyType.AREA_BATTLE_BEGIN) { //发生战斗
            area.initBattleData(data)
            area.battleBegin(data.battle)
        } else if (type === NotifyType.AREA_BATTLE_END) { //战斗结束
            area.battleEndByServer(data)
        } else if (type === NotifyType.CELL_TONDEN_END) { //屯田结束
            if (area.active && area.getArmyByUid(data.auid)?.isOwner()) {
                viewHelper.showPnl('area/TondenEnd', data.treasures)
            }
        }
    }

    // 战斗帧数据
    private OnFSPCheckFrame(res: any) {
        const data = res?.data || {}
        const fsp = this.getArea(data.index)?.getFspModel() || this.lookArea?.getFspModel()
        fsp?.onFSPCheckFrame(data)
    }
}