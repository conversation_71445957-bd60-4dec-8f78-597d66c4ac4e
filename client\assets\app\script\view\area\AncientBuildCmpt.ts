import { TILE_SIZE } from "../../common/constant/Constant";
import { gameHpr } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BuildObj from "../../model/area/BuildObj";
import AncientObj from "../../model/main/AncientObj";
import ClickTouchCmpt from "../cmpt/ClickTouchCmpt";
import { ANCIENT_BTANIM_ROLE_POSITION } from "./AncientBTAnimRoleConf";
import BaseBuildCmpt from "./BaseBuildCmpt";

const { ccclass, property } = cc._decorator;

// 遗迹建筑
@ccclass
export default class AncientBuildCmpt extends BaseBuildCmpt {

    private lvNode: cc.Node = null //等级节点
    private upLvAnimNode: cc.Node = null //升级动画
    private touchCmpt: ClickTouchCmpt = null

    private ancientInfo: AncientObj = null

    public init(data: BuildObj, origin: cc.Vec2, originY: number, owner: string) {
        super.init(data, origin, originY, owner)
        this.initAncientInfo(data)
        this.touchCmpt = this.body.addComponent(ClickTouchCmpt).on(this.onClick, this)
        this.syncPoint()
        this.syncZindex()
        // 初始化等级
        this.initLv()
        this.updateLv(this.ancientInfo.lv)
        // 显示是否在升级中
        this.updateUpLvAnim()
        return this
    }

    // 重新同步
    public resync(data: BuildObj) {
        this.data = data
        this.initAncientInfo(data)
        this.syncPoint()
        this.syncZindex()
        this.setCanClick(true)
        this.updateLv(this.ancientInfo.lv)
        this.updateUpLvAnim()
        return this
    }

    // 初始化遗迹信息
    private initAncientInfo(data: BuildObj) {
        if (data.aIndex < 0) {
            this.ancientInfo = new AncientObj().init(gameHpr.world.getMapCellByIndex(data.aIndex))
            this.ancientInfo.updateInfo({ lv: data.lv })
        } else {
            this.ancientInfo = gameHpr.world.getAncientInfo(data.aIndex)
        }
    }

    private async initLv() {
        const pfb = await assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, 'area')
        if (!this.node || !this.node.isValid || !pfb) {
            return
        }
        this.lvNode = cc.instantiate2(pfb, this.node)
        this.lvNode.zIndex = 1
        this.lvNode.setPosition(Math.floor(this.data.size.x * 0.5) * TILE_SIZE, 36)
        this.lvNode.Child('val', cc.Label).string = '' + this.ancientInfo.lv
        this.lvNode.active = true
    }

    // 加载升级动画
    public async loadUpLvAnim() {
        const pfb = await assetsMgr.loadTempRes('build/BUILD_ANCIENT_BT', cc.Prefab, 'area')
        if (!this.node || !this.node.isValid || !pfb) {
            return
        }
        this.upLvAnimNode = cc.instantiate2(pfb, this.node)
        this.upLvAnimNode.zIndex = 1
        this.upLvAnimNode.setPosition(0, 0)
        this.upLvAnimNode.active = false
        this.updateUpLvAnim()
    }

    public clean() {
        this.unscheduleAllCallbacks()
        this.node.stopAllActions()
        this.touchCmpt?.clean()
        this.node.destroy()
        this.data = null
    }

    private onClick() {
        if (!this.data) {
            return
        }
        audioMgr.playSFX('click')
        if (this.ancientInfo && this.data.aIndex >= 0 && gameHpr.checkIsOneAlliance(this.ancientInfo.owner)) {
            viewHelper.showPnl(this.data.getUIUrl(), this.data)
        } else {
            viewHelper.showPnl('build/BuildAncientBase', this.data)
        }
    }

    // 设置是否可以点击
    public setCanClick(val: boolean) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val
        }
    }

    // 刷新等级
    public updateLv(lv: number) {
        if (this.lvNode) {
            this.lvNode.Child('val', cc.Label).string = '' + lv
        }
        if (this.data.isMaxLv()) {
            this.Child('body/val', cc.MultiFrame).setFrame(5)
        } else if (this.ancientInfo?.owner) {
            this.Child('body/val', cc.MultiFrame).setFrame(Math.floor(lv / 5) + 1)
        } else {
            this.Child('body/val', cc.MultiFrame).setFrame(0)
        }
    }

    // 刷新升级动画
    public updateUpLvAnim() {
        if (!this.ancientInfo.owner || this.data.isMaxLv() || this.ancientInfo.state !== 1 || !!this.ancientInfo.pauseState) {
            this.upLvAnimNode?.setActive(false)
        } else if (this.upLvAnimNode) {
            this.upLvAnimNode.active = true
            this.upLvAnimNode.Child('time', cc.LabelTimer).run(this.ancientInfo.getSurplusTime() * 0.001)
            const roles: any[] = ANCIENT_BTANIM_ROLE_POSITION[this.data.id]?.[Math.floor(this.ancientInfo.lv / 5)] || []
            this.upLvAnimNode.Child('root').Items(roles, (it, data) => {
                it.setPosition(data.x, data.y)
                it.scaleX = data.scaleX
                it.Component(cc.Animation).play('ancient_bt', ut.random(1, 5) * 0.1)
            })
        } else {
            this.loadUpLvAnim()
        }
    }
}