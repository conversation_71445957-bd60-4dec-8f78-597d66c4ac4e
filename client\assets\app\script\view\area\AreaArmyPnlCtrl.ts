import { ecode } from "../../common/constant/ECode";
import { PreferenceKey } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import AreaObj from "../../model/area/AreaObj";
import ArmyObj from "../../model/area/ArmyObj";
import PawnObj from "../../model/area/PawnObj";
import PawnDrillInfoObj from "../../model/main/PawnDrillInfoObj";
import TextButtonCmpt from "../cmpt/TextButtonCmpt";
import PlayerModel from "../../model/main/PlayerModel";
import EquipInfo from "../../model/main/EquipInfo";

const { ccclass } = cc._decorator;

@ccclass
export default class AreaArmyPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private tabLbl_: cc.Label = null // path://root/info/bg/tab_l
    private showPawnEquipTge_: cc.Toggle = null // path://root/info/show_pawn_equip_te_t
    private listSv_: cc.ScrollView = null // path://root/list_sv
    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce
    //@end

    private readonly PKEY_TAB: string = 'AREA_ARMY_TAB'

    private tab: number = 0
    private area: AreaObj = null
    private player: PlayerModel = null

    private preShowPawnEquip: boolean = false

    private hpBarList: { bar: cc.Sprite, pawn: PawnObj }[] = []

    public listenEventMaps() {
        return [
            { [EventType.UPDATE_PAWN_TREASURE]: this.onUpdatePawnTreasure, enter: true },
            { [EventType.UPDATE_ARMY_TREASURES]: this.onUpdateArmyTreasures, enter: true },
            { [EventType.UPDATE_ARMY_NAME]: this.onUpdateArmyName, enter: true },
            { [EventType.UPDATE_AREA_ARMY_LIST]: this.onUpdateAreaArmyList, enter: true },
            { [EventType.REMOVE_ARMY]: this.onUpdateAreaArmyList, enter: true },
            { [EventType.UPDATE_PAWN_DRILL_QUEUE]: this.onUpdateAreaArmyList, enter: true },
            { [EventType.UPDATE_PAWN_LVING_QUEUE]: this.onUpdateAreaArmyList, enter: true },
            { [EventType.UPDATE_PAWN_CURING_QUEUE]: this.onUpdateAreaArmyList, enter: true },
        ]
    }

    public async onCreate() {
        this.player = this.getModel('player')
    }

    public onEnter(data: any) {
        const area = this.area = gameHpr.areaCenter.getLookArea()
        if (!area) {
            return this.hide()
        }
        this.preShowPawnEquip = this.showPawnEquipTge_.isChecked = gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED) ?? false
        const [defCount, atkCount] = this.updateArmyCount()
        let tabStr: string = gameHpr.user.getTempPreferenceMap(this.PKEY_TAB) || ''
        let [index, type] = ut.stringToNumbers(tabStr, '_')
        if (index !== area.index) {
            this.listSv_.node.Data = -1
            if (gameHpr.checkIsOneAlliance(area.owner)) {
                type = defCount > 0 || atkCount === 0 ? 0 : 1
            } else {
                type = atkCount > 0 || defCount === 0 ? 1 : 0
            }
        }
        this.tabsTc_.Tabs(type ?? 0)
    }

    public onRemove() {
        this.hpBarList = []
        this.area = null
        if (this.preShowPawnEquip !== this.showPawnEquipTge_.isChecked) {
            gameHpr.user.setLocalPreferenceData(PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED, this.showPawnEquipTge_.isChecked)
        }
    }

    public onClean() {
        assetsMgr.releaseTempResByTag(this.key)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/list_sv/view/content/item/treasure_be
    onClickTreasure(event: cc.Event.EventTouch, _: string) {
        const data: ArmyObj = event.target.parent.parent.Data
        if (data.owner !== gameHpr.getUid()) {
            return viewHelper.showAlert(ecode.NOT_OPEN_OTHER_TREASURE)
        }
        const treasures = data?.getAllPawnTreasures()
        if (treasures && treasures.length > 0) {
            viewHelper.showPnl('common/TreasureList', treasures)
        }
    }

    // path://root/list_sv/view/content/item/name/edit/edit_name_be
    onClickEditName(event: cc.Event.EventTouch, _: string) {
        const data: ArmyObj = event.target.parent.parent.parent.parent.Data
        if (!data || !data.isOwner()) {
        } else if (this.area.isBattleing()) {
            viewHelper.showAlert(ecode.BATTLEING)
        } else {
            viewHelper.showPnl('area/EditArmyName', data)
        }
    }

    // path://root/list_sv/view/content/item/pawns/pawn_be
    onClickPawn(event: cc.Event.EventTouch, _: string) {
        audioMgr.playSFX('click')
        const data = event.target.Data
        if (!data) {
        } else if (data.drillInfo) {
            viewHelper.showPnl('area/PawnInfo', gameHpr.areaCenter.createPawnByDrillInfo(data.drillInfo), data.drillInfo, 'area_army')
        } else if (data.curingInfo) {
            viewHelper.showPnl('area/PawnInfo', gameHpr.areaCenter.createPawnByCureInfo(data.curingInfo), data.curingInfo, 'area_army')
        } else if (data.lvingInfo) {
            viewHelper.showPnl('area/PawnInfo', gameHpr.areaCenter.createPawnByLvingInfo(data.pawn, data.lvingInfo), data.lvingInfo, 'area_army')
        } else if (data.pawn) {
            viewHelper.showPnl('area/PawnInfo', data.pawn, null, 'area_army')
        }
    }

    // path://root/info/show_pawn_equip_te_t
    onClickShowPawnEquip(event: cc.Toggle, data: string) {
        audioMgr.playSFX('click')
        this.updateArmyList()
        viewHelper.showNoLongerTip('area_army_show_equip', { content: 'ui.area_army_show_equip_tip' })
    }

    // path://root/list_sv/view/content/item/march_speed_be
    onClickMarchSpeed(event: cc.Event.EventTouch, _: string) {
        const it = event.target.parent.parent
        const data: ArmyObj = it?.Data
        if (!data) {
            return
        } else if (this.area.isBattleing()) {
            return viewHelper.showAlert(ecode.BATTLEING)
        }
        viewHelper.showPnl('main/ModifyMarchSpeed', data, (speed: number) => {
            if (speed) {
                data.marchSpeed = speed
                if (this.isValid) {
                    this.updateArmyMarchSpeed(it.Child('top'), data)
                }
            }
        })
    }

    // path://root/list_sv/view/content/item/force_revoke_be
    onClickForceRevoke(event: cc.Event.EventTouch, _: string) {
        const data: ArmyObj = event.target.parent.parent.Data
        if (data.owner === gameHpr.getUid() || data.aIndex !== gameHpr.player.getMainCityIndex() || data.isBattleing()) {
            return
        }
        viewHelper.showMessageBox('ui.force_revoke_tip', {
            params: [gameHpr.getPlayerName(data.owner), data.name],
            ok: () => {
                gameHpr.world.forceRevoke(data.uid).then(err => {
                    if (err) {
                        return viewHelper.showAlert(err)
                    } else if (this.isValid) {
                        // this.hide()
                    }
                })
            },
            cancel: () => { }
        })
    }

    // path://root/tabs_tc_tce
    onClickTabs(event: cc.Toggle, data: string) {
        !data && audioMgr.playSFX('click')
        const tab = this.tab = Number(event.node.name)
        gameHpr.user.setTempPreferenceData(this.PKEY_TAB, this.area.index + '_' + tab)
        this.updateArmyList()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // 刷新士兵宝箱
    private onUpdatePawnTreasure(pawn: PawnObj) {
        const it = this.listSv_.content.children.find(m => m.Data?.uid === pawn.armyUid)
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data)
        }
    }

    // 刷新军队宝箱
    private onUpdateArmyTreasures(army: ArmyObj) {
        const it = this.listSv_.content.children.find(m => m.Data?.uid === army.uid)
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data)
        }
    }

    // 刷新军队名字
    private onUpdateArmyName(uid: string, name: string) {
        const it = this.listSv_.content.children.find(m => m.Data?.uid === uid)
        if (it) {
            it.Child('top').Child('name/val', cc.Label).string = name
        }
    }

    private onUpdateAreaArmyList(index: number) {
        if (this.area.index === index) {
            this.updateArmyCount()
            this.updateArmyList()
        }
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    // 刷新军队数量
    private updateArmyCount() {
        const owner = this.area.owner, maxArmyCount = this.area.maxArmyCount
        let defCount = 0, atkCount = 0
        // 计算各个军队数量
        this.area.armys.forEach(m => {
            if (m.getPawnActCount() === 0) {
                return
            } else if (gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defCount += 1
            } else {
                atkCount += 1
            }
        })
        // 从这个区域开始行军的军队数量
        const index = this.area.index
        gameHpr.world.getMarchs().filter(m => m.armyIndex === index).forEach(m => {
            if (m.autoRevoke) {
            } else if (gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defCount += 1
            } else {
                atkCount += 1
            }
        })
        this.tabsTc_.Child('0/Background/lay/count', cc.Label).string = `(${defCount}/${maxArmyCount})`
        this.tabsTc_.Child('0/checkmark/lay/count', cc.Label).string = `(${defCount}/${maxArmyCount})`
        this.tabsTc_.Child('1/Background/lay/count', cc.Label).string = `(${atkCount}/${maxArmyCount})`
        this.tabsTc_.Child('1/checkmark/lay/count', cc.Label).string = `(${atkCount}/${maxArmyCount})`
        return [defCount, atkCount]
    }

    // 刷新军队列表
    private updateArmyList() {
        this.tabLbl_.setLocaleKey('ui.area_army_' + this.tab)
        const marchs = {}
        gameHpr.world.getMarchs().forEach(m => marchs[m.armyUid] = true)
        const owner = this.area.owner, uid = gameHpr.getUid()
        const arr = this.area.armys.filter(m => {
            if (m.pawns.length === 0 && m.owner !== uid) {
                return false
            }
            return !this.tab === gameHpr.checkIsOneAlliance(m.owner, owner)
        })
        const pawnDrillMap = {}, lvingPawnLvMap = {}, curingPawnLvMap = {}
        this.player.getAllPawnDrillList().forEach(m => {
            let p = pawnDrillMap[m.auid]
            if (!p) {
                p = pawnDrillMap[m.auid] = []
            }
            p.push(m)
        })
        this.player.getPawnLevelingQueues().forEach(m => lvingPawnLvMap[m.puid] = m)
        this.player.getCuringPawnsQueue().forEach(m => curingPawnLvMap[m.uid] = m)
        this.listSv_.Child('empty').setActive(arr.length === 0)
        const isNoviceMode = gameHpr.isNoviceMode, showEquip = this.showPawnEquipTge_.isChecked
        const mainCityIndex = gameHpr.player.getMainCityIndex(), isAncient = this.area.isAncient()
        this.hpBarList = []
        if (this.listSv_.node.Data !== this.tab) {
            this.listSv_.node.Data = this.tab
            this.listSv_.stopAutoScroll()
            this.listSv_.content.y = 0
        }
        this.listSv_.Items(arr, (it, data) => {
            it.Data = data
            const top = it.Child('top'), bottom = it.Child('bottom')
            let pawns: any[] = data.pawns, isHasLving = false, isOwner = data.isOwner(), isOneAlliance = gameHpr.checkIsOneAlliance(data.owner, uid)
            it.Color(isOwner ? '#E9DDC7' : isOneAlliance ? '#DAEBDD' : '#F6D6CD')
            const armyName = data.owner ? data.name : isAncient ? assetsMgr.lang('ui.ancient_army_name') : assetsMgr.lang(pawns[0] ? 'ui.pawn_type_' + (pawns[0].type || 6) : 'ui.neutral_pawn')
            top.Child('name/val', cc.Label).Color(isOwner ? '#564C49' : isOneAlliance ? '#4A85D5' : '#D54A4A').string = armyName
            top.Child('name/edit').active = isOwner && !isNoviceMode
            const other = top.Child('name/other'), alli = top.Child('alli')
            if (other.active = !isOwner && !!data.owner) {
                const plr = gameHpr.getPlayerInfo(data.owner)
                if (plr) {
                    resHelper.loadPlayerHead(other.Child('head'), plr.headIcon, this.key)
                    other.Child('name', cc.Label).string = ut.nameFormator(plr.nickname, 7)
                    // 联盟
                    if (alli.active = !!plr.allianceUid && !isOneAlliance) {
                        resHelper.loadAlliIcon(plr.allianceIcon, alli.Child('icon'), this.key)
                        alli.Child('name', cc.Label).string = plr.allianceName
                    }
                } else {
                    other.active = alli.active = false
                }
            } else {
                alli.active = false
            }
            const drills: PawnDrillInfoObj[] = pawnDrillMap[data.uid]?.slice() || []
            const list = isOwner ? pawns.concat(data.drillPawns).concat(data.curingPawns) : pawns
            it.Child('pawns').Items(list, (node, pawn: any) => {
                const icon = node.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime
                if (isId) {
                    node.Data = { id: pawn, drillInfo: drills.remove('id', pawn) }
                } else if (isCuring) {
                    node.Data = { id: pawn, curingInfo: curingPawnLvMap[pawn.uid] }
                } else {
                    node.Data = { pawn, id: pawn.id, lvingInfo: lvingPawnLvMap[pawn.uid] }
                }
                const isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring
                const lv = isLving ? lvingPawnLvMap[pawn.uid]?.lv : (isId ? 1 : pawn.lv)
                icon.opacity = (isId || isLving || isCuring) ? 120 : 255
                resHelper.loadPawnHeadMiniIcon(isId ? pawn : (pawn.portrayal?.id || pawn.id), icon, this.key, false)
                node.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv
                if (node.Child('hp').active = (!isId && !isCuring)) {
                    const spr = node.Child('hp/bar', cc.Sprite)
                    spr.fillRange = pawn.getHpRatio()
                    this.hpBarList.push({ bar: spr, pawn })
                }
                const showNode = node.Child('show')
                if (showNode.active = showEquip) {
                    // 出手速度
                    showNode.Child('speed', cc.Label).string = (isId || isCuring) ? '' : '' + pawn.attackSpeed
                    // 装备
                    if (showNode.Child('equip').active = !isId && !isCuring && !!pawn?.isCanWearEquip()) {
                        const spr = showNode.Child('equip/val', cc.Sprite), equip: EquipInfo = pawn.equip
                        if (equip?.id) {
                            resHelper.loadEquipIcon(equip.id, spr, this.key, equip.getSmeltCount())
                        } else {
                            spr.spriteFrame = null
                        }
                    }
                }
                if (isLving) {
                    isHasLving = true
                }
            })
            bottom.Child('force_revoke_be').active = data.aIndex === mainCityIndex && !isOwner && !data.isBattleing()
            viewHelper.updateArmyState(bottom, data, marchs[data.uid], isHasLving, data.isOwner())
            this.updateArmyMarchSpeed(top, data)
            this.updateArmyTreasure(bottom, data)
        })
    }

    // 行军速度
    private updateArmyMarchSpeed(it: cc.Node, data: ArmyObj) {
        const node = it.Child('march_speed_be'), isOneAlliance = gameHpr.checkIsOneAlliance(data.owner)
        if (node.active = isOneAlliance && !gameHpr.isNoviceMode && data.pawns.length > 0 && data.defaultMarchSpeed > 0) {
            const marchSpeedLbl = node.Component(cc.Label), line = node.Child('line'), isOwner = data.isOwner()
            node.Color(/* isOwner &&  */data.marchSpeed === data.defaultMarchSpeed ? '#936E5A' : '#B6A591').setLocaleKey('ui.march_speed_desc', data.marchSpeed)
            node.Component(cc.Button).interactable = isOwner
            if (line.active = isOwner) {
                marchSpeedLbl._forceUpdateRenderData()
                line.width = marchSpeedLbl.node.width
            }
        }
    }

    // 刷新宝箱信息
    private updateArmyTreasure(it: cc.Node, data: ArmyObj) {
        const node = it.Child('treasure_be'), treasureCount = data.getAllPawnTreasureCount()
        if (node.active = treasureCount > 0 && data.isOwner()) {
            node.Child('treasure', TextButtonCmpt).setKey('ui.get_treasure_count', treasureCount)
        }
    }

    update(dt: number) {
        this.hpBarList.forEach(m => {
            if (m.pawn) {
                m.bar.fillRange = m.pawn.getHpRatio()
            }
        })
    }
}
