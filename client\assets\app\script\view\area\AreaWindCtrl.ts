import EventType from "../../common/event/EventType";
import { mapHelper } from "../../common/helper/MapHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BuildObj from "../../model/area/BuildObj";
import AreaCenterModel from "../../model/area/AreaCenterModel";
import AreaObj from "../../model/area/AreaObj";
import BuildCmpt from "./BuildCmpt";
import { animHelper } from "../../common/helper/AnimHelper";
import PawnCmpt from "./PawnCmpt";
import PawnObj from "../../model/area/PawnObj";
import ArmyObj from "../../model/area/ArmyObj";
import { cameraCtrl } from "../../common/camera/CameraCtrl";
import MapTouchCmpt from "../cmpt/MapTouchCmpt";
import ClickTouchCmpt from "../cmpt/ClickTouchCmpt";
import NetEvent from "../../common/event/NetEvent";
import HPBarCmpt from "./HPBarCmpt";
import { AREA_DI_COLOR_CONF, AREA_MAX_ZINDEX, BUILD_SMITHY_NID, CITY_MAIN_NID, TILE_SIZE } from "../../common/constant/Constant";
import BaseBuildCmpt from "./BaseBuildCmpt";
import { PawnState, PreferenceKey } from "../../common/constant/Enums";
import { ecode } from "../../common/constant/ECode";
import SearchCircle from "../../common/astar/SearchCircle";
import { gameHpr } from "../../common/helper/GameHelper";
import SelectCellCmpt from "../cmpt/SelectCellCmpt";
import { netHelper } from "../../common/helper/NetHelper";
import { guideHelper } from "../../common/helper/GuideHelper";
import AncientObj from "../../model/main/AncientObj";
import { AreaLandColorInfo } from "../../common/constant/DataType";

const { ccclass } = cc._decorator;

@ccclass
export default class AreaWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    private mapNode_: cc.Node = null // path://root/map_n
    private gridNode_: cc.Node = null // path://root/map_n/grid_n
    private skillDiNode_: cc.Node = null // path://root/map_n/skill_di_n
    private buildNode_: cc.Node = null // path://root/map_n/build_n
    private selectPawnNode_: cc.Node = null // path://root/select_pawn_n
    private roleNode_: cc.Node = null // path://root/role_n
    private topLayerNode_: cc.Node = null // path://root/top_layer_n
    private editJiantouNode_: cc.Node = null // path://root/edit_jiantou_n
    private weakGuideNode_: cc.Node = null // path://root/weak_guide_n
    //@end

    private readonly PAWN_SIZE: cc.Vec2 = cc.v2(1, 1)

    private diNode: cc.Node = null
    private maskNode: cc.Node = null
    private touchCmpt: MapTouchCmpt = null

    private model: AreaObj = null
    private areaCenter: AreaCenterModel = null
    private centre: cc.Vec2 = cc.v2()
    private preCameraZoomRatio: number = 0
    private areaSize: cc.Vec2 = cc.v2() //战场大小
    private buildSize: cc.Vec2 = cc.v2() //建筑区域
    private areaActSize: cc.Vec2 = cc.v2() //战场的实际大小
    private borderSize: cc.Vec2 = cc.v2() //地图边框宽度
    private buildOrigin: cc.Vec2 = cc.v2() //建筑起点

    private walls: cc.Node[] = [] //城墙列表
    private flames: cc.Node[] = [] //火焰列表
    private alliFlags: cc.Node[] = [] //联盟旗帜
    private areaOutDecorate: cc.Node[] = [] //装饰
    private builds: BaseBuildCmpt[] = [] //建筑列表
    private buildMap: { [key: string]: number } = {}
    private pawns: PawnCmpt[] = [] //士兵列表
    private pawnMap: any = {}
    private wallLvNode: cc.Node = null
    private hpBar: HPBarCmpt = null //血条

    private currEditBuild: BuildCmpt = null //当前编辑的建筑
    private currEditPawn: PawnCmpt = null //当前编辑的士兵
    private searchCircle: SearchCircle = null
    private isPawnMoveing: boolean = false //当前是否士兵移动中
    private editPawns: { [key: string]: PawnCmpt } = {} //编辑过的士兵列表
    private tempSeasonType: number = 0

    private _temp_vec2_0: cc.Vec2 = cc.v2()
    private _temp_vec2_1: cc.Vec2 = cc.v2()
    private _temp_vec2_2: cc.Vec2 = cc.v2()
    private _temp_vec2_3: cc.Vec2 = cc.v2()

    public listenEventMaps() {
        return [
            { [NetEvent.NET_RECONNECT]: this.onNetReconnect, enter: true },
            { [EventType.REENTER_AREA_WIND]: this.onReenterAreaWind, enter: true },
            { [EventType.SHOW_BUILD_JIANTOU]: this.onShowBuildJiantou, enter: true },
            { [EventType.LONG_PRESS_BUILD]: this.onLongPressBuild, enter: true },
            { [EventType.MOVE_BUILD]: this.onMoveBuild, enter: true },
            { [EventType.CLICK_EDIT_BUILD_MENU]: this.onClickEditBuildMenu, enter: true },
            { [EventType.EDIT_PAWN_POS]: this.onEditPawnPos, enter: true },
            { [EventType.CLICK_EDIT_PAWN_MENU]: this.onClickEditPawnMenu, enter: true },
            { [EventType.ADD_BUILD]: this.onAddBuild, enter: true },
            { [EventType.REMOVE_BUILD]: this.onRemoveBuild, enter: true },
            { [EventType.UPDATE_BUILD_LV]: this.onUpdateBuildLv, enter: true },
            { [EventType.UPDATE_BUILD_POINT]: this.onUpdateBuildPoint, enter: true },
            { [EventType.UPDATE_BUILDS]: this.onUpdateBuilds, enter: true },
            { [EventType.UPDATE_AREA_HP]: this.onUpdateAreaHp, enter: true },
            { [EventType.UPDATE_ANCIENT_INFO]: this.onUpdateAncientInfo, enter: true },
            { [EventType.ADD_ARMY]: this.onAddArmy, enter: true },
            { [EventType.ADD_PAWN]: this.onAddPawn, enter: true },
            { [EventType.REMOVE_ARMY]: this.onRemoveArmy, enter: true },
            { [EventType.UPDATE_ARMY]: this.onUpdateArmy, enter: true },
            { [EventType.UPDATE_ALL_ARMY]: this.onUpdateAllArmy, enter: true },
            { [EventType.REMOVE_PAWN]: this.onRemovePawn, enter: true },
            { [EventType.AREA_BATTLE_BEGIN]: this.onAreaBattleBegin, enter: true },
            { [EventType.AREA_BATTLE_END]: this.onAreaBattleEnd, enter: true },
            { [EventType.AREA_MAIN_HIT]: this.onAreaMainHit, enter: true },
            { [EventType.PLAY_FLUTTER_HP]: this.onPlayFlutterHp, enter: true },
            { [EventType.PLAY_FLUTTER_ANGER]: this.onPlayFlutterAnger, enter: true },
            { [EventType.PLAY_BULLET_FLY]: this.onPlayBulletFly, enter: true },
            { [EventType.PLAY_BATTLE_EFFECT]: this.onPlayBattleEffect, enter: true },
            { [EventType.PLAY_BATTLE_SFX]: this.onPlayBattleSfx, enter: true },
            { [EventType.PLAY_BATTLE_SCENE_SHAKE]: this.onPlayBattleSceneShake, enter: true },
            { [EventType.FOCUS_PAWN]: this.onFocusPawn, enter: true },
            { [EventType.UPDATE_BT_QUEUE]: this.onUpdateBtQueue, enter: true },
            { [EventType.UPDATE_PAWN_DRILL_QUEUE]: this.onUpdatePawnDrillQueue, enter: true },
            { [EventType.UPDATE_PAWN_LVING_QUEUE]: this.onUpdatePawnDrillQueue, enter: true },
            { [EventType.UPDATE_PAWN_CURING_QUEUE]: this.onUpdatePawnDrillQueue, enter: true },
            { [EventType.CHANGE_SHOW_PAWN_LV]: this.onChangeShowPawnLv, enter: true },
            { [EventType.CHANGE_SHOW_PAWN_EQUIP]: this.onChangeShowPawnEquip, enter: true },
            { [EventType.CHANGE_PAWN_EQUIP]: this.onChangePawnEquip, enter: true },
            { [EventType.CHANGE_PAWN_SKIN]: this.onChangePawnSkin, enter: true },
            { [EventType.CHANGE_PAWN_PORTRAYAL]: this.onChangePawnPortrayal, enter: true },
            { [EventType.FORGE_EQUIP_BEGIN]: this.onForgeEquipBegin, enter: true },
            { [EventType.FORGE_EQUIP_COMPLETE]: this.onForgeEquipComplete, enter: true },
            { [EventType.WEAK_GUIDE_SHOW_NODE_CHOOSE]: this.onWeakGuideShowNodeChoose, enter: true },
        ]
    }

    public async onCreate() {
        this.setParam({ isClean: false })
        this.areaCenter = this.getModel('areaCenter')
        this.diNode = this.mapNode_.FindChild('di')
        this.maskNode = this.mapNode_.FindChild('mask')
        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt)
        this.selectPawnNode_.active = false
        this.editJiantouNode_.active = false
        this.gridNode_.active = false
        // 加载UI
        await viewHelper.preloadPnl('area/AreaUI')
    }

    public async onReady() {
        this.model = await this.areaCenter.reqAreaByIndex(gameHpr.world.getLookCell()?.index ?? -1)
        if (!this.model) {
            return
        }
        this.areaCenter.setLookArea(this.model)
        // 区域大小
        this.areaSize.set(this.model.areaSize)
        this.buildSize.set(this.model.buildSize)
        // 获取地图边框的宽度 至少都有2格
        this.model.getBorderSize(this.borderSize)
        // 重新计算地图的真实大小
        this.borderSize.mul(2, this.areaActSize).addSelf(this.areaSize)
        // 计算建筑的起点
        this.model.buildOrigin.add(this.borderSize, this.buildOrigin)
        // 分帧创建地块
        const range = gameHpr.world.getMaxTileRange(), count = (range.x * 2 + 1) * (range.y * 2 + 1)
        this.diNode.Items(count)
        // 初始化城墙
        await this.initWall()
    }

    public onEnter(reenter: boolean) {
        if (!this.model) {
            viewHelper.gotoWind(gameHpr.world.getSceneKey())
            if (gameHpr.net.isConnected()) {
                viewHelper.showMessageBox(ecode.UNKNOWN)
            }
            return
        }
        this.buildNode_.Data = true
        this.topLayerNode_.Data = true
        this.model.setActive(true)
        this.tempSeasonType = gameHpr.world.getSeasonType()
        // 刷新宝箱红点
        this.model.updateTreasureReddot()
        // 设置中心位置
        this.areaActSize.mul(0.5, this.centre).subSelf(cc.v2(0.5, 0.5))
        // 初始化相机位置
        const zr = gameHpr.user.getLocalPreferenceData(PreferenceKey.AREA_ZOOM_RATIO)
        cameraCtrl.init(mapHelper.getPixelByPoint(this.centre), this.areaActSize, cc.Vec2.ZERO, zr)
        // 绘制士兵
        this.initPawns()
        // 绘制建筑
        this.initBuilds()
        // 刷新地图
        this.updateMap(this.centre.floor())
        // UI
        if (reenter) {
            this.emit(EventType.UPDATE_AREA_BATTLE_TIME_UI, this.model.index)
        } else {
            viewHelper.showPnl('area/AreaUI', this.model)
        }
        //
        this.touchCmpt.init(this.onClickMap.bind(this))
        //
        gameHpr.playAreaBgm(this.model.isBattleing())
    }

    public onLeave() {
        gameHpr.user.setLocalPreferenceData(PreferenceKey.AREA_ZOOM_RATIO, cameraCtrl.zoomRatio)
        viewHelper.hidePnl('area/AreaUI')
        this.touchCmpt.clean()
        gameHpr.world.setLookCell(null)
        this.clean()
        this.cleanPawns()
        resHelper.cleanNodeChildren(this.diNode)
        resHelper.cleanNodeChildren(this.maskNode)
        this.buildNode_.removeAllChildren()
        this.buildNode_.Data = false
        this.topLayerNode_.removeAllChildren()
        this.topLayerNode_.Data = false
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key)
        animHelper.clean()
        assetsMgr.releaseTempResByTag(this.key)
        audioMgr.releaseByMod('build')
        audioMgr.releaseByMod('pawn')
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onNetReconnect() {
        this.reinit()
    }

    private onReenterAreaWind() {
        return this.reenter()
    }

    // 显示编辑家具的箭头
    private onShowBuildJiantou(item: BuildCmpt, index: number) {
        this.editJiantouNode_.active = !!item
        if (item) {
            this.editJiantouNode_.setPosition(item.getBodyOffsetTopPosition(68))
            this.editJiantouNode_.Component(cc.MultiFrame).setFrame(index)
        }
    }

    // 长按选中一个家具
    private onLongPressBuild(item: BuildCmpt) {
        // 设置可以点击选择了
        this.builds.forEach(m => item.uid !== m.uid && m.setCanClickSelect(true))
        this.pawns.forEach(m => m.setCanClick(false))
        // 显示编辑UI
        this.openEditBuild(item)
    }

    // 移动建筑
    private onMoveBuild(item: BuildCmpt, pos: cc.Vec2) {
        const point = item.getActPointByPixel(pos)
        this.model.amendBuildPoint(point, item.data.size) //修正一下
        item.setOffsetPositionByPoint(point)
        item.updateEditState(this.model.getBuildGroundPointMap(), point)
    }

    // 点击编辑建筑菜单
    private onClickEditBuildMenu(type: string) {
        if (!this.currEditBuild || !this.currEditBuild.data /* || this.model.isBattleing() */) {
            return
        }
        const item = this.currEditBuild
        if (type === 'cancel') { //取消
            item.cancel()
        } else if (type === 'ok') { //确定
            if (item.editState) {
                return viewHelper.showAlert(item.editState)
            }
            item.confirm(item.getActPointByPixel(item.getTempPosition()))
            // audioMgr.playSFX('area/sound06')
        }
        viewHelper.hidePnl('area/EditBuild') //隐藏编辑UI
        this.builds.forEach(m => m.setCanClickSelect(false)) //关闭点击选择
        this.pawns.forEach(m => m.setCanClick(true))
        item.syncZindex() //同步zindex
        this.closeEditBuild()
    }

    // 编辑士兵
    private onEditPawnPos(index: number, uid: string) {
        if (this.model.index !== index || this.model.isBattleing()) {
            return
        }
        const pawn = this.pawns.find(m => m.uid === uid)
        if (pawn) {
            this.builds.forEach(m => m.setCanClick(false))
            this.pawns.forEach(m => m.setCanClick(false))
            this.maskNode.children.forEach(m => m.active = !!m.Data)
            // 行动中
            pawn.data.actioning = true
            // 显示pnl
            viewHelper.showPnl('area/EditPawn', pawn)
            this.currEditPawn = pawn
            this.selectPawnNode_.Component(SelectCellCmpt).open(this.currEditPawn.getTempPosition(), this.PAWN_SIZE)
        }
    }

    // 点击编辑士兵的菜单
    private onClickEditPawnMenu(type: string) {
        if (!this.currEditPawn || !this.currEditPawn.data || this.model.isBattleing()) {
            viewHelper.hidePnl('area/EditPawn')
            this.builds.forEach(m => m.setCanClick(true))
            this.pawns.forEach(m => m.setCanClick(true))
            this.maskNode.children.forEach(m => m.active = false)
            this.closeEditPawn()
        } else if (type === 'gather') { //集合
            this.gatherPawn()
        } else if (type === 'ok') {
            this.editPawnEnd()
        }
    }

    // 添加建筑
    private onAddBuild(data: BuildObj) {
        if (data.aIndex === this.model?.index) {
            const isOwner = this.model.isOwner()
            this.createBuild(data).then(item => {
                if (item && this.isActive() && isOwner) {
                    const body = item.getBody()
                    // 摄像机移动到这个位置来
                    cameraCtrl.setTargetOnce(body)
                    // 扫光
                    // animHelper.playFlashLight([body])
                }
            })
        }
    }

    // 删除建筑
    private onRemoveBuild(data: BuildObj) {
        if (data.aIndex === this.model?.index) {
            const build = this.builds.remove('uid', data.uid)
            if (build) {
                this.cleanBuild(build)
                assetsMgr.releaseTempRes(data.getPrefabUrl(), this.key)
            }
        }
    }

    // 刷新建筑等级
    private onUpdateBuildLv(data: BuildObj) {
        if (data.aIndex !== this.model?.index) {
        } else if (data.uid === this.model.wall.uid) {
            this.updateWallLv(data.lv)
        } else {
            this.builds.find(m => m.uid === data.uid)?.updateLv(data.lv)
        }
    }

    // 刷新建筑位置
    private onUpdateBuildPoint(data: BuildObj) {
        if (data.aIndex === this.model?.index) {
            this.builds.find(m => m.uid === data.uid)?.syncPoint()
        }
    }

    // 刷新血量
    private onUpdateAreaHp(index: number) {
        if (index === this.model?.index) {
            this.hpBar?.init(this.model)
        }
    }

    // 刷新遗迹信息
    private onUpdateAncientInfo(data: AncientObj) {
        if (data.index !== this.model?.index) {
            return
        }
        const build = this.builds.find(m => m.data.isAncient())
        if (build) {
            build.updateLv(data.lv)
            build.updateUpLvAnim()
        }
    }

    // 刷新城市
    private onUpdateBuilds(index: number) {
        if (index === this.model?.index) {
            this.initBuilds()
        }
    }

    // 添加军队
    private onAddArmy(data: ArmyObj) {
        if (data.aIndex === this.model?.index) {
            data.pawns.forEach(m => this.createPawn(m))
        }
    }

    // 删除军队
    private onRemoveArmy(data: ArmyObj) {
        if (data.aIndex === this.model?.index) {
            data.pawns.forEach(m => {
                const i = this.pawns.findIndex(p => p.uid === m.uid && p.data?.armyUid === data.uid)
                if (i !== -1) {
                    this.cleanPawn(this.pawns.splice(i, 1)[0], true)
                }
            })
        }
    }

    // 更新军队信息
    private onUpdateArmy(data: ArmyObj) {
        if (data.aIndex !== this.model?.index) {
            return
        }
        // 先删除没有的
        for (let i = this.pawns.length - 1; i >= 0; i--) {
            const m = this.pawns[i]
            if (m.data?.armyUid !== data.uid) {
                continue
            } else if (!data.pawns.has('uid', m.uid)) {
                this.pawns.splice(i, 1)
                this.cleanPawn(m, true)
            }
        }
        data.pawns.forEach(m => this.createPawn(m))
    }

    // 更新所有军队
    private onUpdateAllArmy(index: number) {
        if (this.model.index === index) {
            this.initPawns()
        }
    }

    // 添加士兵
    private onAddPawn(index: number, data: PawnObj) {
        if (this.model.index === index) {
            this.createPawn(data)
        }
    }

    // 删除士兵
    private onRemovePawn(index: number, uid: string) {
        if (this.model.index === index) {
            this.cleanPawn(this.pawns.remove('uid', uid), true)
        }
    }

    // 战斗开始
    private onAreaBattleBegin(index: number) {
        if (this.model.index !== index) {
            return
        }
        // 关闭当前正在编辑的建筑
        this.checkConfirmEditBuild()
        this.closeEditBuild()
        // 关闭当前正在编辑的士兵
        this.currEditPawn?.cancel()
        this.closeEditPawn()
        // 初始化血量
        this.hpBar?.init(this.model)
        // 初始化士兵
        this.initPawns()
        // 战斗时间
        this.emit(EventType.UPDATE_AREA_BATTLE_TIME_UI, this.model.index)
        //
        gameHpr.playAreaBgm(true)
    }

    // 战斗结束
    private onAreaBattleEnd(index: number) {
        if (this.model?.index !== index) {
            return
        } else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
            return this.reenter()
        }
        this.hpBar?.init(this.model)
        this.initBuilds(true)
        this.initPawns()
        // 战斗时间
        this.emit(EventType.UPDATE_AREA_BATTLE_TIME_UI, this.model.index)
        //
        gameHpr.playAreaBgm(false)
    }

    // 受到伤害
    private onAreaMainHit(data: any) {
        if (data.index === this.model?.index) {
            if (this.model.isBattleing()) {
                this.hpBar?.play()
            }
            animHelper.playFlutterHp({ type: 'isDamage', value: data.value }, this.topLayerNode_, this.getPixelByPoint(data.point), this.key)
        }
    }

    // 播放飘血
    private onPlayFlutterHp(data: any) {
        if (this.model?.index === data.index) {
            const pos = data.point ? this.getPixelByPoint(data.point).clone() : this.pawns.find(m => m.uid === data.uid)?.getPosition()
            if (pos) {
                animHelper.readyPlayFlutterHp(data, pos, this.topLayerNode_, this.key)
            }
        }
    }

    // 播放增加怒气
    private onPlayFlutterAnger(data: any) {
        if (this.model?.index === data.index) {
            const pawn = this.pawns.find(m => m.uid === data.uid)
            if (pawn) {
                animHelper.playFlutterAnger(data.value, this.topLayerNode_, pawn.getPosition(), this.key)
            }
        }
    }

    // 播放子弹飞行
    private onPlayBulletFly(data: any) {
        if (this.model?.index === data.index) {
            data.startPos = this.getPixelByPoint(data.startPoint).clone()
            data.targetPos = this.getPixelByPoint(data.targetPoint).clone()
            animHelper.playBulletFly(data, this.topLayerNode_, this.key)
        }
    }

    // 播放战斗特效
    private onPlayBattleEffect(data: any) {
        if (this.model?.index === data.index) {
            data.pos = this.getPixelByPoint(data.point).clone()
            let root = this.skillDiNode_
            if (data.root === 'top') {
                root = this.topLayerNode_
            } else if (data.root === 'role') {
                root = this.roleNode_
                data.zIndex = (AREA_MAX_ZINDEX - (data.pos.y - this.borderSize.y * TILE_SIZE)) * 10 + 3
            }
            animHelper.playBattleEffect(data, root, this.key)
        }
    }

    // 播放音效
    private onPlayBattleSfx(index: number, url: string, data: any) {
        if (this.model?.index === index) {
            audioMgr.playSFX(url, data)
        }
    }

    // 播放屏幕抖动
    private onPlayBattleSceneShake(index: number, time: number) {
        if (this.model?.index === index) {
            cameraCtrl.shake(time)
        }
    }

    // 聚焦士兵
    private onFocusPawn(data: any) {
        if (this.model?.index !== data?.index) {
            return
        }/*  else if (cameraCtrl.getNoDragTime() < 5000) {
            return //多久没拖动才可以聚焦
        } */
        const pos = this.getPixelByPoint(data.point)
        if (cameraCtrl.isInScreenRangeByWorld(pos)) {
            cameraCtrl.moveTo(0.5, pos, true)
        }
    }

    // 刷新修建队列
    private onUpdateBtQueue() {
        this.builds.forEach(m => m.updateUpLvAnim())
    }

    // 刷新训练队列
    private onUpdatePawnDrillQueue(index: number) {
        if (this.model.index === index) {
            this.builds.forEach(m => m.updateDrillPawn())
        }
    }

    // 切换显示士兵等级
    private onChangeShowPawnLv(val: number) {
        this.pawns.forEach(m => m.showPawnLv(val))
    }

    // 切换显示士兵装备
    private onChangeShowPawnEquip(val: boolean) {
        this.pawns.forEach(m => m.showPawnEquip(val))
    }

    // 切换士兵装备
    private onChangePawnEquip(data: PawnObj) {
        this.pawns.find(m => m.uid === data.uid)?.updateShowPawnEquip()
    }

    // 切换士兵皮肤
    private onChangePawnSkin(data: PawnObj) {
        const i = this.pawns.findIndex(m => m.uid === data.uid)
        if (i !== -1) {
            const pawn = this.pawns[i]
            if (pawn.curSkinId !== data.skinId) {
                this.pawns.splice(i, 1)
                this.cleanPawn(pawn)
                this.createPawn(data)
            }
        }
        this.builds.forEach(m => m.updateDrillPawn())
    }

    // 化身英雄
    private onChangePawnPortrayal(data: PawnObj) {
        if (!data.portrayal) {
            return
        }
        const i = this.pawns.findIndex(m => m.uid === data.uid)
        if (i !== -1) {
            const pawn = this.pawns[i]
            if (pawn.curPortrayalId !== data.portrayal.id) {
                pawn.playAvatarHeroAnim(data.portrayal.id).then(() => {
                    if (this.isActive()) {
                        this.pawns.splice(i, 1)
                        this.cleanPawn(pawn)
                        this.createPawn(data)
                    }
                })
            }
        }
    }

    // 打造装备开始
    private onForgeEquipBegin() {
        this.builds.find(m => m.id === BUILD_SMITHY_NID)?.updateForgeEquip()
    }

    // 打造装备完成
    private onForgeEquipComplete() {
        this.builds.find(m => m.id === BUILD_SMITHY_NID)?.updateForgeEquip()
    }

    // 若引导
    private onWeakGuideShowNodeChoose(data: any) {
        if (data.scene === 'area') {
            guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key)
        }
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    private isActive() { return this.isValid && !!this.model?.active }

    private getPixelByPoint(point: cc.Vec2) {
        return point && mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_3))
    }

    private getPointByPixel(pixel: cc.Vec2) {
        return pixel && mapHelper.getPointByPixel(pixel).subSelf(this.borderSize)
    }

    private getBuildPixelByPoint(point: cc.Vec2) {
        return mapHelper.getPixelByPoint(point.add(this.buildOrigin, this._temp_vec2_1), this._temp_vec2_1)
    }

    private clean() {
        this.areaCenter.setLookArea(null)
        this.model?.setActive(false)
        this.model = null
        gameHpr.cleanPawnAstarMap()
        this.searchCircle = null
        this.cleanWalls()
        this.cleanFlames()
        this.cleanAlliFlags()
        this.cleanAreaOutDecorate()
        this.cheanBuilds()
        // this.cleanPawns()
        this.closeEditBuild()
        this.closeEditPawn()
    }

    // 重连之后的初始化
    private async reinit() {
        gameHpr.cleanPawnAstarMap()
        // 关闭当前正在编辑的建筑
        this.checkConfirmEditBuild()
        this.closeEditBuild()
        // 关闭当前正在编辑的士兵
        this.currEditPawn?.cancel()
        this.closeEditPawn()
        // 重新获取
        const world = gameHpr.world
        this.model = await this.areaCenter.reqAreaByIndex(world.getLookCell()?.index ?? -1, true)
        if (!this.model) {
            return viewHelper.gotoWind(world.getSceneKey())
        } else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
            return this.reenter()
        }
        this.model.setActive(true)
        // 刷新地图
        this.updateMap(this.centre.floor())
        // 城墙等级
        this.model.wall && this.updateWallLv(this.model.wall.lv)
        // 血条
        this.hpBar?.init(this.model)
        // 绘制建筑
        this.initBuilds()
        // 绘制士兵
        this.initPawns()
        // 战斗时间
        this.emit(EventType.REINIT_AREA_UI, this.model)
    }

    // 重新绘制
    private async reenter() {
        this.emit(mc.Event.READY_BEGIN_WIND)
        this.clean()
        await this.onReady()
        this.emit(mc.Event.READY_END_WIND)
        this.onEnter(true)
        this.emit(EventType.UPDATE_REENTER_AREA)
    }

    // 初始化城墙
    private async initWall() {
        this.cleanWalls()
        if (!this.model.isBoss()) {
            // 加载血条
            let pfb = await assetsMgr.loadTempRes('wall/WALL_HP_BAR', cc.Prefab, this.key)
            if (!pfb || !this.isValid) {
                return
            }
            let node = cc.instantiate2(pfb, this.roleNode_)
            this.hpBar = node.addComponent(HPBarCmpt).init(this.model)
            // 绘网格
            const size = this.model.buildSize
            viewHelper.drawGrid(this.gridNode_.Component(cc.Graphics), cc.v2(size.x - 2, size.y - 2), cc.v2(this.buildOrigin.x + 1, this.buildOrigin.y + 1))
            this.gridNode_.active = false
            if (this.model.wall) {
                // 初始化墙 遗迹不绘制城墙
                if (!this.model.isAncient()) {
                    await this.createWall()
                }
                // 加载一个墙的等级
                if (this.model.wall.lv > 0) {
                    pfb = await assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, this.key)
                    if (pfb && this.isValid) {
                        node = this.wallLvNode = cc.instantiate2(pfb, this.roleNode_)
                        this.updateWallLv(this.model.wall.lv)
                    }
                }
            }
            this.updateWallHpPosition()
        }
        // 创建区域外的装饰
        await this.createAreaOutDecorate()
        // 创建联盟旗帜
        await this.createAlliFlags(gameHpr.getPlayerAlliIcon(this.model.owner))
    }

    private async createWall() {
        while (this.walls.length > 0) {
            this.walls.pop().destroy()
        }
        this.walls.length = 0
        const skinId = 0
        await Promise.all(this.model.walls.map(async (m) => {
            let url = `wall/${skinId}/WALL_${skinId}_${m.type}_${m.dir}`
            if (m.index) {
                url += '_' + m.index
            }
            const pfb = await assetsMgr.loadTempRes(url, cc.Prefab, this.key)
            if (pfb && this.isValid) {
                const node = cc.instantiate2(pfb, this.buildNode_)
                node.Data = this.model.wall
                node.setPosition(mapHelper.getPixelByPoint(m.point.add(this.buildOrigin, this._temp_vec2_1)))
                node.addComponent(ClickTouchCmpt).on(this.onClickWall, this)
                node.zIndex = AREA_MAX_ZINDEX - node.y
                this.walls.push(node)
            }
        }))
    }

    private cleanWalls() {
        this.wallLvNode?.destroy()
        this.wallLvNode = null
        this.hpBar?.clean()
        this.hpBar = null
        while (this.walls.length > 0) {
            this.walls.pop().destroy()
        }
        this.walls.length = 0
    }

    // 初始化火焰
    private async createFlames() {
        const flames = this.model.flames
        if (this.flames.length === flames.length) {
            return flames.forEach((point, i) => {
                const node = this.flames[i], pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))
                node.setPosition(pos.x - 2, pos.y + 26)
                node.zIndex = AREA_MAX_ZINDEX - node.y
            })
        }
        const pfb = await assetsMgr.loadTempRes('build/FLAME', cc.Prefab, this.key)
        if (pfb && this.isValid) {
            flames.forEach(point => {
                const node = cc.instantiate2(pfb, this.roleNode_), pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))
                node.setPosition(pos.x - 2, pos.y + 26)
                node.zIndex = AREA_MAX_ZINDEX - node.y
                this.flames.push(node)
            })
        }
    }

    private cleanFlames() {
        while (this.flames.length > 0) {
            this.flames.pop().destroy()
        }
        this.flames.length = 0
    }

    // 创建联盟旗帜
    private async createAlliFlags(icon: number) {
        if (!icon) {
            return this.cleanAlliFlags()
        }
        const points = this.model.alliFlags
        if (this.alliFlags.length === points.length && this.alliFlags[0].Data === icon) {
            return points.forEach((point, i) => {
                const node = this.alliFlags[i], pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))
                node.setPosition(pos)
                node.zIndex = AREA_MAX_ZINDEX - node.y
            })
        }
        this.cleanAlliFlags()
        const pfb = await assetsMgr.loadTempRes('alli_flag/ALLI_FLAG_' + icon, cc.Prefab, this.key)
        if (pfb && this.isValid) {
            points.forEach(point => {
                const node = cc.instantiate2(pfb, this.roleNode_), pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))
                node.Child('body').y = -28
                node.setPosition(pos)
                node.zIndex = AREA_MAX_ZINDEX - node.y
                this.alliFlags.push(node)
            })
        }
    }

    private cleanAlliFlags() {
        while (this.alliFlags.length > 0) {
            this.alliFlags.pop().destroy()
        }
        this.alliFlags.length = 0
    }

    // 创建区域外的装饰
    private async createAreaOutDecorate() {
        this.cleanAreaOutDecorate()
        const seasonType = /* gameHpr.world.getSeasonType() */0
        const cell = gameHpr.world.getMapCellByIndex(this.model.index), drawType = cell.getLandDrawType()
        const url = `area_decorate/${seasonType}/AREA_DECORATE_${drawType}`
        const pfb = await assetsMgr.loadTempRes(url, cc.Prefab, this.key)
        if (pfb && this.isValid) {
            const node = cc.instantiate2(pfb, this.mapNode_.Child('bg'))
            node.setPosition(this.borderSize.x * TILE_SIZE, this.borderSize.y * TILE_SIZE)
            this.areaOutDecorate.push(node)
        }
    }

    private cleanAreaOutDecorate() {
        while (this.areaOutDecorate.length > 0) {
            this.areaOutDecorate.pop().destroy()
        }
        this.areaOutDecorate.length = 0
    }

    // 初始化建筑
    private async initBuilds(playOccupyEffect?: boolean) {
        this.buildMap = {}
        // 先删除没有的
        const builds = this.model.builds
        builds.forEach(m => this.buildMap[m.uid] = 1)
        for (let i = this.builds.length - 1; i >= 0; i--) {
            if (!this.buildMap[this.builds[i].uid]) {
                this.cleanBuild(this.builds.splice(i, 1)[0])
            }
        }
        await Promise.all(builds.map(m => this.createBuild(m)))
    }

    private async createBuild(data: BuildObj) {
        if (!this.isActive()) {
            return null
        }
        let build = this.builds.find(m => m.uid === data.uid)
        if (build) {
            this.buildMap[data.uid] = 2
            return build.resync(data, this.model.owner)
        }
        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)
        if (!pfb || !this.isActive()) {
            return null
        } else if (this.buildMap[data.uid] === 2) {
            return null //防止重复创建或创建没有的
        } else if (data.aIndex !== this.model?.index) {
            return null
        }
        this.buildMap[data.uid] = 2
        build = cc.instantiate2(pfb, this.roleNode_).getComponent(BaseBuildCmpt).init(data, this.buildOrigin, this.borderSize.y * TILE_SIZE, this.model.owner)
        this.builds.push(build)
        return build
    }

    private cheanBuilds() {
        while (this.builds.length > 0) {
            this.builds.pop().clean()
        }
        this.buildMap = {}
    }

    private cleanBuild(data: BaseBuildCmpt) {
        if (data) {
            data.clean()
            delete this.buildMap[data.uid]
        }
    }

    // 刷新墙的等级
    private updateWallLv(lv: number) {
        if (this.wallLvNode) {
            this.wallLvNode.Child('val', cc.Label).string = '' + lv
        }
    }

    // 刷新血条位置
    private updateWallHpPosition() {
        if (this.hpBar) {
            let node = this.hpBar.node, pos = cc.v2()
            if (this.model.cityId === CITY_MAIN_NID) {
                pos = mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin))
                node.setPosition(pos.x, pos.y + 25)
            } else if (this.model.isAncient()) {
                pos = mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin))
                node.setPosition(pos.x, pos.y + 25)
            } else {
                pos = mapHelper.getPixelByPoint(this.buildOrigin)
                node.setPosition(pos.x, pos.y + 47)
            }
            node.zIndex = (AREA_MAX_ZINDEX - (pos.y - this.borderSize.y * TILE_SIZE)) * 10 + 1
        }
        if (this.wallLvNode) {
            const pos = mapHelper.getPixelByPoint(this.buildOrigin)
            this.wallLvNode.setPosition(pos.x, pos.y + 16)
            this.wallLvNode.zIndex = this.hpBar.node.zIndex + 1
        }
    }

    // 打开编辑建筑
    private openEditBuild(item: BuildCmpt) {
        // 显示pnl
        viewHelper.showPnl('area/EditBuild')
        this.gridNode_.active = true
        // 如果之前有就放下
        this.checkConfirmEditBuild()
        this.currEditBuild = item
        // 刷新一下地面点
        this.model.updateBuildGroundPoints(item.data)
        // 刷新编辑状态
        item.updateEditState(this.model.getBuildGroundPointMap(), item.point)
    }

    // 如果有建筑在编辑状态 就放下
    private checkConfirmEditBuild() {
        if (!this.isEditBuildState()) {
        } else if (this.currEditBuild.editState) {
            this.currEditBuild.cancel()
        } else {
            this.currEditBuild.confirm(this.currEditBuild.getActPointByPixel(this.currEditBuild.getTempPosition()))
        }
    }

    // 关闭编辑建筑
    private closeEditBuild() {
        this.currEditBuild = null
        this.editJiantouNode_.active = false
        this.gridNode_.active = false
    }

    // 关闭编辑士兵
    private closeEditPawn() {
        for (let key in this.editPawns) {
            const pawn = this.editPawns[key], state = pawn.data?.getState()
            if (state && state < PawnState.STAND) {
                pawn.data.changeState(PawnState.NONE)
            }
        }
        this.editPawns = {}
        this.currEditPawn = null
        this.selectPawnNode_.Component(SelectCellCmpt).close()
        this.isPawnMoveing = false
    }

    private cleanPawns() {
        while (this.pawns.length > 0) {
            this.pawns.pop().clean()
        }
        this.pawnMap = {}
    }

    // 初始化士兵
    private async initPawns() {
        // 先删除没有的
        const pawns = this.model.getAllPawns(), uidMap = {}
        pawns.forEach(m => uidMap[m.getAbsUid()] = true)
        for (let i = this.pawns.length - 1; i >= 0; i--) {
            const m = this.pawns[i]
            if (!uidMap[m.getAbsUid()] || !m.data || m.data.isDie()) {
                this.cleanPawn(this.pawns.splice(i, 1)[0])
            }
        }
        return Promise.all(pawns.map(m => this.createPawn(m)))
    }

    private async createPawn(data: PawnObj) {
        if (!this.isActive() || !data) {
            return null
        }
        let pawn = this.pawns.find(m => m.uid === data.uid)
        if (!pawn) {
        } else if (pawn.curSkinId !== data.skinId || pawn.curPortrayalId !== data.getPortrayalId()) {
            this.pawns.remove('uid', data.uid)
            this.cleanPawn(pawn)
        } else if (data.isDie()) {
            this.pawns.remove('uid', data.uid)
            this.cleanPawn(pawn)
            return null
        } else {
            return pawn.resync(data)
        }
        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)
        if (!pfb || !this.isActive()) {
            return null
        } else if (this.pawnMap[data.uid]) {
            return null //防止多次创建
        } else if (data.aIndex !== this.model?.index) {
            return null
        }
        // 重新获取以防数据不统一
        const uid = data.uid
        data = this.model.getPawn(uid) || this.model.getBattleTempPawn(uid)
        if (!data || data.isDie()) {
            return null
        }
        pawn = cc.instantiate2(pfb, this.roleNode_).getComponent(PawnCmpt).init(data, this.borderSize, this.key)
        this.pawns.push(pawn)
        this.pawnMap[data.uid] = pawn
        return pawn
    }

    private cleanPawn(pawn: PawnCmpt, release?: boolean) {
        if (pawn) {
            delete this.pawnMap[pawn.uid]
            pawn.clean(release)
        }
    }

    // 是否编辑建筑中
    private isEditBuildState() {
        return !!this.currEditBuild?.data
    }

    // 是否编辑小兵中
    private isEditPawnState() {
        return !!this.currEditPawn?.data
    }

    // 绘制地图
    private updateMap(centre: cc.Vec2) {
        const seasonType = /* gameHpr.world.getSeasonType() */0
        const cell = gameHpr.world.getMapCellByIndex(this.model.index), seasonColorConf = AREA_DI_COLOR_CONF[seasonType]
        const colorConf: AreaLandColorInfo = seasonColorConf[cell.getLandDrawType()] || seasonColorConf[0]
        const areaSize = this.model.areaSize, oYindex = (areaSize.x + 1) % 2
        // 设置整个背景颜色
        cameraCtrl.setBgColor(colorConf.bg)
        //
        // this.preCameraZoomRatio = cameraCtrl.zoomRatio
        // this.centre.set(centre)
        const buildOrigin = this.model.buildOrigin, buildSize = this.model.buildSize
        const points = mapHelper.getRangePointsByPoint(centre, gameHpr.world.getMaxTileRange())
        const isBoss = this.model.isBoss()
        let mi = 0
        this.diNode.Items(points, (it, point, i) => {
            const x = point.x - this.borderSize.x, y = point.y - this.borderSize.y
            const bx = x - buildOrigin.x, by = y - buildOrigin.y
            const index = it.Data = mapHelper.pointToIndexByNumer(x, y, areaSize)
            const id = x + '_' + y
            it.setPosition(mapHelper.getPixelByPoint(point, this._temp_vec2_0))
            if (mapHelper.isBorder(x, y, areaSize)) { //边界外
                it.Component(cc.Sprite).spriteFrame = null
            } else if (mapHelper.isBorder(bx, by, buildSize)) { //战斗区域
                const idx = y % 2 === 0 ? index : index + oYindex
                it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('comm_area_01')
                it.Color(colorConf.battle[Number(idx % 2 !== 0)])
                if (this.model.banPlacePawnPointMap[id]) {
                    const mn = resHelper.getNodeByIndex(this.maskNode, mi++, this._temp_vec2_0)
                    mn.Data = true
                    mn.active = this.isEditPawnState()
                }
            } else if (!isBoss) { //建筑区域
                it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('comm_area_01')
                if (bx === 0 || bx === buildSize.x - 1 || by === 0 || by === buildSize.y - 1) {
                    it.Color(colorConf.build)
                } else if (cell.isMainCity() || cell.isAncient()) {
                    it.Color('#D6DBAA')
                }
            } else if (bx === 1 && by === 1) { //boss位置
                it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('land_area_boss_' + cell.landType)
            } else {
                it.Component(cc.Sprite).spriteFrame = null
            }
        })
        // 隐藏多余的
        resHelper.hideNodeByIndex(this.maskNode, mi)
    }

    private getLandIcon(icon: string) {
        return resHelper.getLandItemIcon(icon, this.tempSeasonType)
    }

    // 点击地图
    private onClickMap(worldLocation: cc.Vec2) {
        if (!this.model || !worldLocation) {
            return
        }
        const point = this.getPointByPixel(worldLocation)
        if (!point) {
            return
        } else if (mapHelper.isBorder(point.x, point.y, this.model.areaSize)) {
            return
        }
        // 是否点击的建筑区域
        const bpoint = point.sub(this.model.buildOrigin, this._temp_vec2_1)
        if (mapHelper.isBorder(bpoint.x, bpoint.y, this.model.buildSize)) {
            this.onClickMapArea(point)
        } else {
            this.onClickBuildArea(bpoint)
        }
    }

    // 点击地图区域
    private onClickMapArea(point: cc.Vec2) {
        if (!this.isEditPawnState() || this.isEditBuildState() || this.isPawnMoveing) {
            return
        }
        const uid = this.currEditPawn.uid
        if (!this.model.banPlacePawnPointMap[point.ID()] && !this.pawns.some(m => m.uid !== uid && m.getActPoint().equals(point))) {
            this.selectPawnNode_.Component(SelectCellCmpt).open(this.getPixelByPoint(point), this.PAWN_SIZE)
            this.movePawn(point)
        }
    }

    // 点击建筑区域
    private onClickBuildArea(point: cc.Vec2) {
        if (!this.isEditBuildState() || this.isEditPawnState()) {
            return
        } else if (!this.model.isBuildBorder(point)) {
            this.currEditBuild.setOffsetPositionByPoint(point)
            this.currEditBuild.updateEditState(this.model.getBuildGroundPointMap(), point)
        }
    }

    // 点击墙
    private onClickWall() {
        if (!this.model.wall || this.isEditBuildState() || this.isEditPawnState()) {
            return
        }
        audioMgr.playSFX('click')
        if (this.model.isAncient()) {
            const build = this.model.getBuildById(this.model.cityId)
            if (!build) {
            } else if (gameHpr.checkIsOneAlliance(this.model.owner)) {
                viewHelper.showPnl(build.getUIUrl(), build)
            } else {
                viewHelper.showPnl('build/BuildAncientBase', build)
            }
        } else if (this.model.isOwner()) {
            viewHelper.showPnl(this.model.wall.getUIUrl(), this.model.wall)
        } else {
            viewHelper.showPnl('build/BuildCity', this.model.wall)
        }
    }

    private getSearchCircle() {
        if (!this.searchCircle) {
            const model = this.model
            this.searchCircle = new SearchCircle().init((x: number, y: number) => model.checkIsBattleArea(x, y) && !model.banPlacePawnPointMap[x + '_' + y])
        }
        return this.searchCircle
    }

    // 移动士兵
    private async movePawn(point: cc.Vec2) {
        if (this.currEditPawn) {
            this.isPawnMoveing = true
            this.emit(EventType.EDIT_PAWN_MOVEING, true)
            await this.movePawnOne(this.currEditPawn, point)
            if (this.isActive()) {
                this.isPawnMoveing = false
                this.emit(EventType.EDIT_PAWN_MOVEING, false)
            }
        }
    }

    // 移动单个士兵
    private async movePawnOne(pawn: PawnCmpt, point: cc.Vec2) {
        if (!pawn?.data || pawn.data?.getState() === PawnState.EDIT_MOVE) {
            return
        }
        const data = pawn.data
        const sp = pawn.getActPoint()
        const area = this.model, as = gameHpr.getPawnASatr(data.uid).init((x: number, y: number) => area.checkIsBattleArea(x, y) && !area.banPlacePawnPointMap[x + '_' + y])
        const points = await as.search(sp, point)
        if (!this.isActive() || points.length === 0) {
            return
        } else if (!this.editPawns[pawn.uid]) {
            this.editPawns[pawn.uid] = pawn
        }
        const time = mapHelper.getMoveNeedTime(points, 400)
        data.changeState(PawnState.EDIT_MOVE, { paths: points, needMoveTime: time })
        await ut.wait(time * 0.001)
        if (data.getState() < PawnState.STAND) {
            data.changeState(PawnState.NONE)
        }
    }

    // 集合士兵
    private async gatherPawn() {
        if (!this.currEditPawn?.data) {
            return
        }
        const pawn = this.currEditPawn
        const uid = this.currEditPawn.uid
        const armyUid = this.currEditPawn.data.armyUid
        const point = pawn.getActPoint()
        // 获取一个军队的
        const pawns: PawnCmpt[] = [], otherPawns = {}
        this.pawns.forEach(m => {
            if (m.uid === uid) {
            } else if (m.data?.armyUid === armyUid) {
                pawns.push(m)
            } else {
                otherPawns[m.point.ID()] = true
            }
        })
        const count = pawns.length
        if (count === 0) {
            return
        }
        // 获取位置
        const points = this.getSearchCircle().search(point, count, otherPawns)
        // 删除已经在这个位置的士兵
        points.delete(m => {
            const i = pawns.findIndex(p => p.getActPoint().equals(m))
            if (i !== -1) {
                pawns.splice(i, 1)
                return true
            }
            return false
        })
        if (points.length === 0) {
            return
        }
        this.isPawnMoveing = true
        this.emit(EventType.EDIT_PAWN_MOVEING, true)
        await Promise.all(points.map((m, i) => this.movePawnOne(pawns[i], m)))
        if (this.isActive()) {
            this.isPawnMoveing = false
            this.emit(EventType.EDIT_PAWN_MOVEING, false)
        }
    }

    // 保存编辑士兵的信息
    private async editPawnEnd() {
        if (!this.currEditPawn?.data || this.isPawnMoveing) {
            return
        }
        const info = this.currEditPawn.data
        const pawns = []
        const moveInfos = []
        for (let key in this.editPawns) {
            const pawn = this.editPawns[key]
            const point = pawn.getActPoint()
            moveInfos.push({
                uid: pawn.uid,
                point: point.toJson()
            })
            if (!pawn.point?.equals(point)) {
                pawns.push({ uid: pawn.uid, point: point.toJson() })
            }
        }
        if (pawns.length > 0) {
            const { err, data } = await netHelper.reqMoveAreaPawns({ index: info.aIndex, armyUid: info.armyUid, pawns: moveInfos })
            if (err) {
                return viewHelper.showAlert(err)
            } else if (!this.isActive()) {
                return
            }
        }
        viewHelper.hidePnl('area/EditPawn')
        for (let key in this.editPawns) {
            this.editPawns[key].confirm()
        }
        this.currEditPawn.data.actioning = false
        this.builds.forEach(m => m.setCanClick(true))
        this.pawns.forEach(m => m.setCanClick(true))
        this.maskNode.children.forEach(m => m.active = false)
        this.closeEditPawn()
    }

    update(dt: number) {
        if (!this.model?.active) {
            return
        }
        // 检测是否需要填充地图
        // this.checkUpdateMap()
    }

    private checkUpdateMap() {
        const point = mapHelper.getPointByPixel(cameraCtrl.getCentrePosition(), this._temp_vec2_2)
        if (!this.centre.equals(point) || this.preCameraZoomRatio !== cameraCtrl.zoomRatio) {
            this.updateMap(point)
        }
    }
}
