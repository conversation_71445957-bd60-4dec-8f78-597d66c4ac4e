import { AREA_MAX_ZINDEX, BUILD_DRILLGROUND_NID, TILE_SIZE } from "../../common/constant/Constant";
import { mapHelper } from "../../common/helper/MapHelper";
import BuildObj from "../../model/area/BuildObj";

const { ccclass, property } = cc._decorator;

// 建筑
@ccclass
export default class BaseBuildCmpt extends cc.Component {

    public data: BuildObj = null
    public owner: string = ''

    protected body: cc.Node = null
    protected origin: cc.Vec2 = cc.v2() //起点
    protected originY: number = 0 //实际地图的七点 像素
    protected tempBodyPosition: cc.Vec2 = cc.v2()
    public tempIndex = 0

    private _temp_vec2_1: cc.Vec2 = cc.v2()
    private _temp_vec2_2: cc.Vec2 = cc.v2()
    private _temp_position: cc.Vec2 = cc.v2()

    public init(data: BuildObj, origin: cc.Vec2, originY: number, owner: string) {
        this.data = data
        this.origin.set(origin)
        this.originY = originY
        this.owner = owner
        this.body = this.FindChild('body')
        this.body.getPosition(this.tempBodyPosition)
        return this
    }

    public clean() {
    }

    public get id() { return this.data?.id }
    public get uid() { return this.data?.uid }
    public get point() { return this.data.point }
    public getBody() { return this.body }
    public getTempPosition() { return this.getPosition(this._temp_position) }

    public getBodyOffsetTopPosition(y: number = 0) {
        const pos = this.getTempPosition()
        pos.y += y + this.getBuildY()
        return pos
    }

    public getBuildY() {
        return (this.data.size.y - 1) * TILE_SIZE
    }

    // 同步位置
    public syncPoint() {
        if (this.data) {
            this.node.setPosition(this.getActPixelByPoint(this.data.point))
        }
    }

    // 同步zindex
    public syncZindex() {
        if (this.data) {
            const y = this.node.y - this.originY
            let index = (AREA_MAX_ZINDEX - y) * 10
            if (this.data.id === BUILD_DRILLGROUND_NID) {
                index += 1
            }
            this.tempIndex = this.node.zIndex = index
        }
    }

    // 根据像素点获取网格点
    public getActPointByPixel(pixel: cc.Vec2) {
        return mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin)
    }

    // 根据网格点获取像素点
    public getActPixelByPoint(point: cc.Vec2) {
        return mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1)
    }

    // 重新同步
    public resync(data: any, owner: string) { return this }
    // 刷新等级
    public updateLv(lv: number) { }
    // 设置是否可以点击
    public setCanClick(val: boolean) { }
    // 设置可以点击选择
    public setCanClickSelect(val: boolean) { }
    // 刷新升级动画
    public updateUpLvAnim() { }
    // 刷新训练士兵
    public updateDrillPawn() { }
    // 刷新打造装备
    public updateForgeEquip() { }
}