import { AREA_MAX_ZINDEX, BUFF_NODE_ZINDEX, BUFF_SHOW_TYPE_TRAN, BUILD_SMITHY_NID, FIRE_PAWN_ID, NEED_MUTUAL_BUFF, NEED_SHOW_BUFF, PAWN_CROSSBOW_ID, SHIELD_BUFF, SPEAR_PAWN_ID, TILE_SIZE } from "../../common/constant/Constant";
import { BuffType, HeroType, PawnSkillType, PawnState, PawnType, PreferenceKey } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { mapHelper } from "../../common/helper/MapHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { wxHelper } from "../../common/helper/WxHelper";
import OutlineShaderCtrl from "../../common/shader/OutlineShaderCtrl";
import PawnObj from "../../model/area/PawnObj";
import PawnSkillObj from "../../model/area/PawnSkillObj";
import ClickTouchCmpt from "../cmpt/ClickTouchCmpt";
import FrameAnimationCmpt from "../cmpt/FrameAnimationCmpt";
import BuffIconCmpt from "./BuffIconCmpt";
import HPBarCmpt from "./HPBarCmpt";
import { PAWN_SOUND_CONF_TRANSITION } from "./PawnAnimConf";
import PawnAnimationCmpt from "./PawnAnimationCmpt";

const { ccclass, property } = cc._decorator;

// 士兵
@ccclass
export default class PawnCmpt extends cc.Component {

    private key: string = ''

    public data: PawnObj = null
    public body: cc.Node = null
    public curSkinId: number = 0
    public curPortrayalId: number = 0
    private animNode: cc.Node = null
    private animCmpt: PawnAnimationCmpt = null
    private touchCmpt: ClickTouchCmpt = null
    private hpBar: HPBarCmpt = null

    private animNodeInitY: number = 0
    private origin: cc.Vec2 = cc.v2() //起点
    private originY: number = 0
    private tempBodyPosition: cc.Vec2 = cc.v2()

    private prePoint: cc.Vec2 = cc.v2()
    private prePositionY: number = -1
    private preAnger: number = -1
    private curShieldValue: number = -1 //当前护盾值
    private preShieldValue: number = -1
    private preActioning: boolean = false
    private preStateUid: string = ''
    private preAnimName: string = ''
    private currAnimName: string = ''
    private isDie: boolean = false //是否在视图层死亡
    private isShowStandShield: boolean = false //是否显示立盾
    private isShowBuffMap: any = {} //外显buff
    private mutualBuffType: BuffType = 0 //当前显示的互斥buff

    private isLoadBuffMap: any = {}
    private buffNodes: cc.Node[] = [] //buff节点列表
    private buffIconCmpt: BuffIconCmpt = null //buff图标容器
    private isDiaup: boolean = false

    private _temp_vec2_1: cc.Vec2 = cc.v2()
    private _temp_vec2_2: cc.Vec2 = cc.v2()
    private _temp_vec2_3: cc.Vec2 = cc.v2()
    private _temp_position: cc.Vec2 = cc.v2()

    public tempIndex = 0

    public init(data: PawnObj, origin: cc.Vec2, key: string) {
        this.reset(data.uid)
        this.data = data
        this.key = key
        this.curSkinId = data.skinId
        this.curPortrayalId = data.getPortrayalId()
        this.origin.set(origin)
        this.originY = origin.y * TILE_SIZE
        this.body = this.FindChild('body')
        this.body.getPosition(this.tempBodyPosition)
        this.updatePosition(true)
        this.updateZIndex()
        this.updateAnger()
        this.updateShieldValue()
        this.preStateUid = ''
        this.animNode = this.FindChild('body/anim')
        this.animCmpt = this.node.addComponent(PawnAnimationCmpt).init(this.animNode.getComponent(cc.Sprite), data.getViewId(), key)
        if (this.animNodeInitY === 0) {
            this.animNodeInitY = this.animNode.y
        }
        this.animNode.scale = 1
        this.animNode.y = this.animNodeInitY
        // 设置体型
        this.updateAnimScale(1 + data.getBuffValue(BuffType.FEED_INTENSIFY) * 0.02)
        // 非战斗单位不用加点击
        const isNoncombat = data.isNoncombat()
        if (!isNoncombat) {
            this.touchCmpt = this.FindChild('touch').addComponent(ClickTouchCmpt).on(this.onClick, this)/* .setPlayAction(true).setTarget(this.body) */
        }
        if (data.isBuilding() || isNoncombat) {
            // 秦良玉的矛 特殊处理下
            this.body.scaleX = data.id === SPEAR_PAWN_ID ? data.enterDir : 1
            if (data.id !== FIRE_PAWN_ID || data.enterDir === 2) {
                this.playAnimation('create', () => {
                    if (this.isValid) {
                        this.loadHPBar()
                        this.playAnimation('idle')
                    }
                })
                return this
            }
        } else if (data.isHasBuff(BuffType.STAND_SHIELD)) {
            this.loadHPBar()
            this.playAnimation('stand_shield') //如果有立盾
            return this
        }
        this.loadHPBar()
        this.playAnimation('idle')
        return this
    }

    private reset(uid: string) {
        this.stopSFXByKey('move_sound', uid)
        this.putAllBuff()
        this.isShowStandShield = false
        this.isShowBuffMap = {}
        this.mutualBuffType = 0
        this.isLoadBuffMap = {}
    }

    private putAllBuff() {
        for (let k in NEED_SHOW_BUFF) {
            this.putBuff(Number(k))
        }
        this.putBuff(this.mutualBuffType)
    }

    // 重新同步一下信息
    public resync(data: PawnObj, jump: boolean = false) {
        this.reset(data.uid)
        // this.animCmpt?.stop()
        this.animCmpt?.resetMove()
        const point = this.getActPoint()
        this.data = data
        if (!jump && point && !data.isBattleing() && data.aIndex >= 0 && !data.point.equals(point)) {
            this.prePoint.set(data.point)
            this.updatePositionForMove(point, data.point.clone())
        } else {
            this.prePoint.set2(-1, -1)
            this.updatePosition()
            this.updateZIndex()
        }
        this.hpBar?.init(data)
        this.hpBar?.updateAnger(data.getAngerRatio())
        this.showPawnLv(gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_LV) ?? 1)
        this.showPawnEquip(this.isCanShowPawnEquip())
        this.setCanClick(true)
        this.preStateUid = ''
        this.animCmpt?.initModel()
        // 秦良玉没矛状态
        if (data.getPortrayalSkill()?.id === HeroType.QIN_LIANGYU && data.getState() === PawnState.STAND) {
            if (data.isHasBuff(BuffType.BARB)) {
                this.playAnimation('idle_barb')
            } else {
                this.playAnimation('idle')
            }
        }
        return this
    }

    public clean(release?: boolean) {
        if (!this.isValid || !this.node) {
            return cc.error('clean error?')
        }
        this.unscheduleAllCallbacks()
        this.stopSFXByKey('move_sound', this.uid)
        this.node.stopAllActions()
        this.touchCmpt?.clean()
        this.animCmpt?.clean()
        this.isShowStandShield = false
        this.isShowBuffMap = {}
        this.mutualBuffType = 0
        this.isLoadBuffMap = {}
        nodePoolMgr.put(this.hpBar?.node)
        this.hpBar = null
        this.buffNodes.forEach(m => nodePoolMgr.put(m))
        this.buffNodes.length = 0
        this.buffIconCmpt?.clean()
        nodePoolMgr.put(this.buffIconCmpt?.node)
        this.buffIconCmpt = null
        this.node.destroy()
        release && assetsMgr.releaseTempRes(this.data?.getPrefabUrl(), this.key)
        this.data = null
    }

    public get id() { return this.data?.id }
    public get uid() { return this.data?.uid }
    public get cuid() { return this.data?.cuid }
    public get point() { return this.data.point }
    public getBody() { return this.body }
    public getTempPosition() { return this.getPosition(this._temp_position) }
    public getAbsUid() { return this.uid + (this.curPortrayalId || this.curSkinId || this.id) }

    public getActPoint() {
        return this.getActPointByPixel(this.getTempPosition())
    }

    // 根据像素点获取网格点
    public getActPointByPixel(pixel: cc.Vec2) {
        return mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin)
    }

    // 根据网格点获取像素点
    public getActPixelByPoint(point: cc.Vec2) {
        return mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1)
    }

    // 播放化身
    public async playAvatarHeroAnim(id: number) {
        const it = await nodePoolMgr.get('pawn/AVATAR_HERO', this.key)
        if (!this.isValid || !this.data) {
            return nodePoolMgr.put(it)
        }
        it.parent = this.node
        it.zIndex = 20
        it.active = true
        const anim = it.Child('val', FrameAnimationCmpt)
        await anim.init('avatar_' + id, this.key)
        if (!this.isValid || !this.data) {
            return nodePoolMgr.put(it)
        }
        anim.play('avatar')
        this.playSFX('sound_074')
        ut.wait(2.42).then(() => nodePoolMgr.put(it))
        await ut.wait(1.98)
    }

    private playAnimation(name: string, cb?: Function, startTime?: number, intervalMul?: number) {
        if (name === 'move' && this.data.getState() === PawnState.MOVE) {
            this.playSFXByKey('move_sound', '', { loop: true, tag: this.uid })
        } else {
            this.fadeOutSFXByKey('move_sound', this.uid)
        }
        if (this.isDiaup && name !== 'idle') {
            this.isDiaup = false
        }
        if (name !== 'die' && name !== 'stand_shield' && this.data?.isHasBuff(BuffType.STAND_SHIELD)) {
            this.currAnimName = name
            if (name !== 'hit') {
            } else if (this.data.portrayal?.skill?.id === HeroType.CAO_REN) { //曹仁需要播放受击动画
                this.animCmpt?.play('stand_hit', () => this.isValid && this.playAnimation('stand_shield'), startTime)
            } else {
                ut.wait(0.5, this).then(() => {
                    if (this.isValid && this.data?.state?.type === PawnState.HIT) {
                        this.data.changeState(PawnState.STAND)
                        this.currAnimName = this.data?.isHasBuff(BuffType.STAND_SHIELD) ? 'stand_shield' : 'idle'
                    }
                })
            }
            return //如果是立盾状态就不播放其他动画了
        } else if (this.animCmpt?.playAnimName === 'create' && name !== 'die') {
            return cb && cb() //如果还在播放创建 其他动画就不要播放了 死亡还是可以的
        }
        if (this.isPullStringState(name)) {
            name = name + '_pull' //检测是否有拉弦状态
        } else if (this.isBarbState(name)) {
            name = name + '_barb' //检测秦良玉手上是否有矛
        }
        if (this.data.id === FIRE_PAWN_ID && name === 'idle') {
            name = 'fire_' + this.data.lv //火
        }
        this.currAnimName = name
        this.animCmpt?.play(name, cb, startTime)
    }

    // 播放音效
    private playSFXByKey(key: string, suffix: string = '', opts?: any) {
        if (this.data.isHero()) {
            const prev = this.data.portrayal.json[key]
            if (prev) {
                return this.playSFX(prev + suffix, opts)
            }
        } else if (this.data.skinId > 0) {
            const json = assetsMgr.getJsonData('pawnSkin', this.data.skinId)
            const prev = json?.[key]
            if (prev) {
                return this.playSFX(prev + suffix, opts)
            }
        }
        if (this.data.baseJson) {
            const url = this.data.baseJson[key]
            if (url) {
                this.playSFX(url + suffix, opts)
            }
        }
    }

    // 播放音效
    private playSFX(url: string, opts?: any) {
        if (!url) {
            return
        }
        url = PAWN_SOUND_CONF_TRANSITION[url] ?? url
        audioMgr.playSFX('pawn/' + url, opts)
    }

    private stopSFX(url: string, tag?: string) {
        if (!url) {
            return
        }
        audioMgr.stopSFX('pawn/' + url, tag)
    }

    private stopSFXByKey(key: string, tag?: string) {
        if (!this.data) {
            return
        }
        let url = null
        if (this.data.isHero()) {
            url = this.data.portrayal.json[key]
        } else if (this.data.baseJson) {
            url = this.data.baseJson[key]
        }
        this.stopSFX(url, tag)
    }

    private fadeOutSFXByKey(key: string, tag?: string) {
        let url = null
        if (this.data.isHero()) {
            url = 'pawn/' + this.data.portrayal.json[key]
        } else if (this.data.baseJson) {
            url = 'pawn/' + this.data.baseJson[key]
        }
        if (url) {
            audioMgr.fadeOutSFX(0.15, url, tag)
        }
    }

    // 是否拉弦状态
    private isPullStringState(name: string) {
        if (this.data?.id !== PAWN_CROSSBOW_ID) {
            return false
        } else if (name !== 'idle' && name !== 'move' && name !== 'hit' && name !== 'diaup' && name !== 'die') {
            return false
        }
        return !!this.data.getSkillByType(PawnSkillType.PULL_STRING) && this.data.getCurAnger() !== 0
    }

    // 是否无矛状态
    private isBarbState(name: string) {
        if (this.data?.getPortrayalSkill()?.id !== HeroType.QIN_LIANGYU || !this.data?.isHasBuff(BuffType.BARB)) {
            return false
        }
        return name === 'idle' || name === 'hit' || name === 'diaup'
    }

    // 设置方向
    private setDir(dir: number) {
        if (this.data.isBuilding()) {
            this.body.scaleX = 1 //建筑不需要翻转
        } else if (dir !== 0) {
            this.body.scaleX = ut.normalizeNumber(dir)
        }
    }

    // 加载血条
    private async loadHPBar() {
        if (this.data.id === FIRE_PAWN_ID) {
            return this.body.Child('bar')?.Color(gameHpr.getBattleFireBarColor(this.data))
        } else if (this.data.maxHp === 0 || this.data.isNoncombat()) {
            return
        }
        const it = await nodePoolMgr.get(this.data.getHPBarPrefabUrl(), this.key)
        if (!this.isValid || !this.data) {
            return nodePoolMgr.put(it)
        }
        it.parent = this.node
        it.zIndex = 10
        it.active = true
        if (this.data.isBoss()) {
            it.setPosition(0, 113)
        } else {
            it.setPosition(0, this.data.isHero() ? 56 : 46)
        }
        this.hpBar = it.addComponent(HPBarCmpt).init(this.data)
        this.hpBar?.updateAnger(this.data.getAngerRatio())
        this.hpBar?.updateShieldValue(this.data.getShieldValue(), this.data.curHp, this.data.getMaxHp())
        this.showPawnLv(gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_LV) ?? 1)
        this.showPawnEquip(this.isCanShowPawnEquip())
    }

    public showPawnLv(val: number) {
        if (this.hpBar) {
            this.hpBar.Child('root').opacity = val ? 255 : 0
            const armyNameLbl = this.hpBar.Child('army_name', cc.Label)
            if (armyNameLbl) {
                armyNameLbl.string = (val === 1 && this.data.armyName) ? ut.nameFormator(this.data.armyName, 4) : ''
            }
            this.hpBar.Child('lv', cc.Label).string = (val === 1 && this.data.lv && !this.data.isMachine() && !this.data.isBuilding()) ? this.data.lv + '' : ''
        }
    }

    // 是否可以显示装备信息 这里如果还没有铁匠铺就不会显示
    private isCanShowPawnEquip() {
        const player = gameHpr.player
        return !!gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_EQUIP) && (player.isCapture() || player.getMainBuilds().some(m => m.id === BUILD_SMITHY_NID && m.lv >= 1))
    }

    // 是否可以显示没有装备的提示
    private isCanShowNotEquipTip() {
        return !this.data.equip.id && this.data.isCanWearEquip() && this.data.isOwner() && gameHpr.player.getEquips().length > 0
    }

    // 显示装备信息
    public showPawnEquip(val: boolean) {
        if (!this.hpBar || !this.hpBar.Child('equip')) {
        } else if (this.hpBar.Child('equip').active = val && this.data.isCanWearEquip()) {
            this.updateShowPawnEquipInfo()
            this.hpBar.Child('not_equip')?.setActive(false)
        } else {
            this.hpBar.Child('not_equip')?.setActive(this.isCanShowNotEquipTip())
        }
    }
    public updateShowPawnEquip() {
        if (!this.hpBar || !this.hpBar.Child('equip')) {
            return
        } else if (this.hpBar.Child('equip').active) {
            this.updateShowPawnEquipInfo()
            this.hpBar.Child('not_equip')?.setActive(false)
        } else {
            this.hpBar.Child('not_equip')?.setActive(this.isCanShowNotEquipTip())
        }
        this.hpBar.initInfo()
    }

    private updateShowPawnEquipInfo() {
        this.hpBar.Child('equip/add').active = !this.data.equip?.id && this.data.isOwner()
        const spr = this.hpBar.Child('equip/val', cc.Sprite)
        if (this.data.equip?.id) {
            resHelper.loadEquipIcon(this.data.equip.id, spr, this.key, this.data.equip.getSmeltCount())
        } else {
            spr.spriteFrame = null
        }
    }

    private showOutline(v: boolean) {
        let outline = this.getComponent(OutlineShaderCtrl)
        if (!outline) {
            outline = this.addComponent(OutlineShaderCtrl)
            outline.setTarget(outline.FindChild('body/anim').Component(cc.Sprite))
            outline.setOutlineSize(4)
            outline.setColor(cc.Color.WHITE)
        }
        outline.setVisible(v)
    }

    // 被点击了
    private onClick() {
        if (this.data) {
            audioMgr.playSFX('click')
            viewHelper.showPnl('area/PawnInfo', this.data)
            // this.showOutline(true)
        }
    }

    // 设置是否可以点击
    public setCanClick(val: boolean) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val
        }
    }

    // 移动位置
    public movePoint(point: cc.Vec2) {
        this.node.setPosition(this.getActPixelByPoint(point))
    }

    // 取消编辑
    public cancel() {
        this.node.setPosition(this.getActPixelByPoint(this.data.point))
    }

    // 确认编辑
    public confirm() {
        const point = this.getActPoint()
        this.point.set(point)
        this.prePoint.set(point)
    }

    update(dt: number) {
        if (!this.data) {
            return
        }
        this.updateZIndex()
        this.updateAnger()
        this.updateBuff()
        this.updateShieldValue()
        this.updateState()
        this.updateCheckDie()
    }

    // 同步zindex
    private updateZIndex() {
        if (this.prePositionY !== this.node.y || (this.preActioning !== !!this.data?.actioning || this.preAnimName !== this.currAnimName)) {
            this.prePositionY = this.node.y
            this.preActioning = !!this.data?.actioning
            this.preAnimName = this.currAnimName
            const y = this.prePositionY - this.originY
            let add = 0
            if (this.data.getPawnType() === PawnType.NONCOMBAT) {
                add = 3 //非战斗单位在最上层
            } else if (this.preActioning) {
                const state = this.data.getState()
                add = state === PawnState.ATTACK || state >= PawnState.SKILL ? 2 : 1
            } else if (this.preAnimName === 'die') {
                return
            } else {
                add = !this.preAnimName || this.preAnimName === 'create' || this.preAnimName === 'stand_shield' || this.preAnimName === 'idle' || this.preAnimName === 'idle_pull' ? 0 : 1
            }
            const index = (AREA_MAX_ZINDEX - y) * 10 + add
            if (index !== this.node.zIndex) {
                this.tempIndex = this.node.zIndex = index
            }
            // cc.log('updateZIndex', this.data.id, this.data.uid, this.preActioning, this.preAnimName, add)
        }
    }

    // 同步位置
    private updatePosition(init?: boolean) {
        if (init || !this.prePoint.equals(this.data.point)) {
            this.prePoint.set(this.data.point)
            this.node.setPosition(this.getActPixelByPoint(this.data.point))
        }
    }

    private async updatePositionForMove(sp: cc.Vec2, ep: cc.Vec2) {
        const data = this.data
        let points = [sp, ep]
        const area = gameHpr.areaCenter.getArea(data.index)
        if (area) {
            points = await gameHpr.getPawnASatr(data.uid).init((x: number, y: number) => area.checkIsBattleArea(x, y)).search(sp, ep)
        }
        const time = mapHelper.getMoveNeedTime(points, 400)
        data.changeState(PawnState.EDIT_MOVE, { paths: points, needMoveTime: time })
        await ut.wait(time * 0.001)
        if (data.getState() < PawnState.STAND) {
            data.changeState(PawnState.NONE)
        }
    }

    // 刷新怒气
    private updateAnger() {
        if (this.preAnger !== this.data.curAnger) {
            this.preAnger = this.data.curAnger
            this.hpBar?.updateAnger(this.data.getAngerRatio())
        }
    }

    // 刷新白盾
    private updateShieldValue() {
        if (this.preShieldValue !== this.curShieldValue) {
            this.preShieldValue = this.curShieldValue
            this.hpBar?.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp())
        }
    }

    // 刷新buff效果
    private updateBuff() {
        this.curShieldValue = 0
        let showBuffTypeMap = {}, mutualBuff = 0, standShield = false, feedIntensifyValue = 0
        this.data.buffs.forEach(m => {
            if (SHIELD_BUFF[m.type]) { //记录护盾值
                this.curShieldValue += m.value
            }
            if (NEED_SHOW_BUFF[m.type]) {
                showBuffTypeMap[m.type] = true
            } else if (NEED_MUTUAL_BUFF[m.type]) {
                mutualBuff = m.type //互斥buff
            } else if (m.type === BuffType.STAND_SHIELD) { //立盾
                standShield = true
            } else if (m.type === BuffType.FEED_INTENSIFY) { //投喂强化
                feedIntensifyValue = m.value
            }
        })
        // 刷新外显buff
        for (let k in NEED_SHOW_BUFF) {
            const type = Number(k)
            this.updateShowBuff(type, showBuffTypeMap[type])
        }
        // 刷新互斥buff
        this.updateMutualBuff(mutualBuff)
        // 刷新立盾
        this.updateStandShield(standShield)
        // 体型
        this.updateAnimScale(1 + feedIntensifyValue * 0.02)
    }

    // 刷新立盾
    private updateStandShield(val: boolean) {
        if (!this.isShowStandShield && val) {
            this.isShowStandShield = true
            this.playAnimation('stand_shield')
        } else if (this.isShowStandShield && !val) {
            this.isShowStandShield = false
            if (this.currAnimName !== 'shield_end') {
                this.playAnimation('idle')
            }
        }
    }

    // 刷新外显buff
    private updateShowBuff(type: BuffType, val: boolean) {
        if (this.isLoadBuffMap[type]) {
        } else if (!this.isShowBuffMap[type] && val) {
            this.showBuff(type)
            this.isShowBuffMap[type] = true
        } else if (this.isShowBuffMap[type] && !val) {
            this.isShowBuffMap[type] = false
            this.putBuff(type)
        }
    }

    // 刷新互斥buff效果 就是只会显示一个效果
    private updateMutualBuff(type: BuffType) {
        if (this.mutualBuffType === type) {
            return
        } else if (this.mutualBuffType) {
            this.putBuff(this.mutualBuffType)
            this.mutualBuffType = 0
        }
        if (type && !this.isLoadBuffMap[type]) {
            this.mutualBuffType = type
            this.showBuff(type)
        }
    }

    // 显示一个buff
    private async showBuff(type: BuffType) {
        const showType: BuffType = BUFF_SHOW_TYPE_TRAN[type] || type
        const name = 'BUFF_' + showType
        let it = this.buffNodes.find(m => m.name === name)
        if (it) {
            this.isLoadBuffMap[showType] = false
        } else if (!this.isLoadBuffMap) {
            return wxHelper.errorAndFilter('showBuff', '!this.isLoadBuffMap')
        } else if (this.isLoadBuffMap[showType]) {
            return
        } else {
            this.isLoadBuffMap[showType] = true
            it = await nodePoolMgr.get('buff/' + name, this.key)
            if (!this.isValid || !this.data) {
                return nodePoolMgr.put(it)
            }
            this.isLoadBuffMap[showType] = false
            this.buffNodes.push(it)
        }
        it.opacity = 255
        it.parent = this.node
        it.zIndex = BUFF_NODE_ZINDEX[showType] || 10
        const cmpt = it.Component(PawnAnimationCmpt).init(it.Child('body/anim', cc.Sprite), showType, this.key)
        if (showType === BuffType.SHIELD
            || showType === BuffType.PROTECTION_SHIELD
            || showType === BuffType.RODELERO_SHIELD
            || showType === BuffType.RODELERO_SHIELD_001
            || showType === BuffType.RODELERO_SHIELD_102
            || showType === BuffType.ABNEGATION_SHIELD
            || showType === BuffType.POISONED_WINE
        ) {
            cmpt.play('trigger', () => cmpt.isValid && cmpt.play('stand'))
        } else {
            cmpt.play('stand')
        }
    }

    private putBuff(type: BuffType) {
        const showType = BUFF_SHOW_TYPE_TRAN[type] || type
        const name = 'BUFF_' + showType
        const node = this.buffNodes.remove('name', name)
        if (!node) {
        } else if (type === BuffType.SHIELD
            || showType === BuffType.PROTECTION_SHIELD
            || type === BuffType.RODELERO_SHIELD
            || type === BuffType.RODELERO_SHIELD_001
            || type === BuffType.RODELERO_SHIELD_102
            || type === BuffType.ABNEGATION_SHIELD
        ) {
            node.Component(PawnAnimationCmpt).play('die', () => nodePoolMgr.put(node))
        } else {
            nodePoolMgr.put(node)
        }
    }

    // 刷新体型
    private updateAnimScale(val: number) {
        if (this.animNode.scale !== val) {
            this.animNode.scale = val
            this.animNode.y = this.animNodeInitY + (this.animNodeInitY + 36) * (val - 1)
        }
    }

    // 检测是否死亡
    private updateCheckDie() {
        if (!this.isDie && this.data?.isDie()) {
            this.isDie = true
            const name = this.animCmpt?.playAnimName
            if (name !== 'hit' && name !== 'die' && name !== 'hit_pull' && name !== 'die_pull') {
                this.playAnimation('die', () => eventCenter.emit(EventType.REMOVE_PAWN, this.data.aIndex, this.data.uid))
            }
        }
    }

    // 同步状态信息
    private updateState() {
        if (gameHpr.playback.isSimulating) {
            return
        } else if (!this.data?.state || this.preStateUid === this.data.state.uid || this.isDie) {
            return
        }
        this.preStateUid = this.data.state.uid
        this.node.stopAllActions()
        this.unscheduleAllCallbacks()
        const state = this.data.state.type, data = this.data.state.data
        // cc.log('updateState', this.uid, this.point.ID(), PawnState[state])
        // this.data.actioning = this.data.actioning || (state !== PawnState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动
        if (state === PawnState.STAND) { //待机
            this.doStand()
        } else if (state === PawnState.MOVE || state === PawnState.EDIT_MOVE) { //移动
            this.doMove(data)
        } else if (state === PawnState.ATTACK) { //攻击
            this.doAttack(data)
        } else if (state === PawnState.HIT) { //受击
            this.doHit(data)
        } else if (state === PawnState.DIAUP) { //击飞
            this.doDiaup(data)
        } else if (state === PawnState.HEAL) { //回血
            this.doHeal(data)
        } else if (state === PawnState.DEDUCT_HP) { //掉血
            this.doDeductHp(data)
        } else if (state === PawnState.ADD_ANGER) { //加怒气
            this.doAddAnger(data)
        } else if (state === PawnState.FEAR) { //恐惧
            this.doFear(data)
        } else if (state === PawnState.DIE) { //直接死亡
            this.doDie(data)
        } else if (state >= PawnState.SKILL && state <= PawnState.SKILL_MAX) { //技能
            this.doSkill(data)
        } else {
            this.playAnimation('idle')
        }
        // 通知聚焦
        // if (state === PawnState.MOVE || state === PawnState.ATTACK || (state >= PawnState.SKILL && state <= PawnState.SKILL_MAX)) {
        //     eventCenter.emit(EventType.FOCUS_PAWN, { index: this.data.aIndex, uid: this.data.uid, point: this.data.point })
        // }
    }

    // 待机
    private doStand() {
        const animName = this.animCmpt?.playAnimName
        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle
            this.playAnimation('idle')
        }
        this.hpBar?.initInfo()
        this.updatePosition()
    }

    // 移动
    private doMove(data: any) {
        this.updatePosition()
        const paths: cc.Vec2[] = (data.paths || []).map(m => this.getActPixelByPoint(cc.v2(m)).clone())
        const currMoveTime = data.currMoveTime ?? 0
        const needMoveTime = data.needMoveTime ?? 0
        // 计算各个距离信息
        let sumDis = 0, arr = []
        for (let i = 1, l = paths.length; i < l; i++) {
            const curr = paths[i], prep = paths[i - 1], speed = curr.sub(prep, this._temp_vec2_3)
            const dis = speed.mag()
            sumDis += dis
            arr.push({ dis: dis, progress: sumDis, speed: speed.normalize(), prep: prep, pos: curr })
        }
        let ratio = currMoveTime / needMoveTime
        let startPos: cc.Vec2 = null, list = []
        for (let i = 0, l = arr.length; i < l; i++) {
            const m = arr[i], pr = m.progress / sumDis
            if (ratio > pr) {
                continue
            } else if (!startPos) { //找出起点
                const dr = m.dis / sumDis
                const r = Math.max(ratio - (pr - dr), 0)
                const d = sumDis * r //超出的一段距离
                startPos = m.speed.mul(d).addSelf(m.prep)
                m.dis -= d //减去已经走过的路
            }
            list.push({ time: m.dis / sumDis * needMoveTime, endPos: m.pos })
        }
        if (!startPos) {
            return
        }
        // 开始移动
        this.playAnimation('move')
        this.node.setPosition(startPos)
        this.animCmpt.resetMove()
        this.runMove(list)
    }
    private runMove(list: any[]) {
        if (!this.isValid) {
        } else if (list.length > 0) {
            const d = list.shift(), pos = d.endPos
            this.setDir(pos.x - this.node.x)
            this.animCmpt.moveNodeOne(0, d.time, this.getPosition(), pos, () => this.runMove(list))
        } else if (this.data.getState() === PawnState.MOVE || this.data.getState() === PawnState.EDIT_MOVE) {
            this.playAnimation('idle')
        }
    }

    // 攻击
    private doAttack(data: any) {
        const currAttackTime = data.currAttackTime ?? 0
        const targetPoint = data.targetPoint || this.point
        const suffix = data.instabilityAttackIndex || ''
        this.updatePosition()
        this.setDir(targetPoint.x - this.point.x)
        this.playSFXByKey('attack_sound', suffix)
        this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime)
    }

    // 技能
    private doSkill(data: any) {
        const currAttackTime = data.currAttackTime ?? 0
        const targetPoint = data.targetPoint || this.point
        const skill: PawnSkillObj = data.skill, heroSkill = this.data.getPortrayalSkill()
        // 位移
        if (!this.prePoint.equals(this.data.point) && (skill?.type === PawnSkillType.SKILL_208
            || skill?.type === PawnSkillType.SKILL_212
            || skill?.type === PawnSkillType.SKILL_213
            || skill?.type === PawnSkillType.SKILL_219
            || skill?.type === PawnSkillType.SKILL_306
            || heroSkill?.id === HeroType.QIN_QIONG
        )) {
            this.prePoint.set(this.data.point)
            const pos = this.getActPixelByPoint(this.data.point)
            this.setDir(pos.x - this.node.x)
            let params = skill.params
            if (!heroSkill) {
            } else if (heroSkill.id === HeroType.ZHANG_FEI
                || heroSkill.id === HeroType.XU_CHU
                || heroSkill.id === HeroType.PEI_XINGYAN
                || heroSkill?.id === HeroType.QIN_QIONG
                || heroSkill?.id === HeroType.GAO_SHUN
                || heroSkill?.id === HeroType.HUO_QUBING
                || heroSkill?.id === HeroType.DIAN_WEI
            ) {
                params = heroSkill.params //张飞 高顺 许褚 裴行俨 秦琼
            } else if (this.data.skinId === 3404103) {
                params = '0.36,0.6' //重骑冬季皮肤
            }
            let [delay, time] = ut.stringToNumbers(params, ',')
            this.animCmpt.resetMove().setMoveDelay((delay ?? 0) * 1000).moveNodeOne(0, (time ?? 0.1) * 1000, this.getPosition(), pos, () => this.setDir(targetPoint.x - this.point.x))
        } else {
            this.updatePosition()
            this.setDir(targetPoint.x - this.point.x)
        }
        if (data.sound !== undefined) {
            this.playSFX(data.sound)
        } else {
            this.playSFXByKey('skill_sound')
        }
        const isStandShield = skill?.type === PawnSkillType.SKILL_205 //立盾
        const isSpearthrowing = heroSkill?.id === HeroType.QIN_LIANGYU && !data.skillName
        this.playAnimation(data.skillName || 'skill', () => {
            if (!this.isValid) {
            } else if (isStandShield) {
                this.playAnimation('stand_shield')
            } else if (isSpearthrowing) {
                this.playAnimation('idle_barb')
            } else {
                this.playAnimation('idle')
            }
        }, currAttackTime)
        // 播放粉碎大地 地面效果
        if (skill?.type === PawnSkillType.SKILL_215) {
            let type: number = skill.type, delay = 0.9
            // 黄盖特殊处理下
            if (this.data.portrayal?.skill?.id === HeroType.HUANG_GAI) {
                type = 215001
                delay = 0.6
            }
            eventCenter.emit(EventType.PLAY_BATTLE_EFFECT, {
                type, delay,
                index: this.data.aIndex,
                point: this.data.point,
            })
        }
        // 秦良玉 收矛
        if (data.skillName === 'recycle_spear') {
            eventCenter.emit(EventType.PLAY_BULLET_FLY, {
                bulletId: 5022,
                currTime: currAttackTime,
                needTime: 1000,
                index: this.data.aIndex,
                startPoint: targetPoint,
                targetPoint: this.data.point,
            })
        }
    }

    // 受击
    private doHit(data: any) {
        const index = this.data.aIndex
        let damage = data.damage ?? 0
        const trueDamage = data.trueDamage ?? 0
        const isCrit = !!data.isCrit //暴击
        const heal = data.heal ?? 0 //回复
        const isDodge = damage === -1 //闪避
        const isParry = damage === -2 //格挡
        const isTurntheblade = damage === -3 //招架
        const isWithstand = damage === -4 //抵挡
        damage = isDodge || isParry || isTurntheblade || isWithstand ? 0 : damage
        const attackPoint = data.attackPoint || this.point
        const isDie = this.isDie = !!data.isDie
        const time = data.time ?? 0 //经过的时间
        const sound = data.sound //受击音效
        const uid = this.uid
        const isDiaup = this.isDiaup
        this.isDiaup = false
        this.setDir(attackPoint.x - this.point.x)
        this.hpBar?.play()
        if (damage + trueDamage === 0) {
            return this.playAnimation('idle')
        } else if (isDie) {
            if (this.data.getPawnType() !== PawnType.NONCOMBAT) {
                this.node.zIndex = 0
            }
            this.putAllBuff()
        } else if (isDiaup) { //如果没有死亡且上一个动作是击飞
            return this.playAnimation('idle')
        }
        let animName = 'hit'
        if (isDie) {
            animName = 'die'
            this.playSFXByKey('die_sound')
        } else if (sound) {
            this.playSFX(sound)
        }
        this.playAnimation(animName, () => {
            if (isDie) {
                eventCenter.emit(EventType.REMOVE_PAWN, index, uid)
            } else if (this.isValid) {
                this.playAnimation('idle')
            }
        })
    }

    // 直接死亡
    private doDie(data: any) {
        const index = this.data.aIndex
        const uid = this.uid
        if (!this.data.isNoncombat()) {
            this.node.zIndex = 0
        }
        this.putAllBuff()
        this.playSFX(this.data.baseJson?.die_sound)
        this.playAnimation('die', () => eventCenter.emit(EventType.REMOVE_PAWN, index, uid))
    }

    // 击飞
    private doDiaup(data: any) {
        const time = data.time ?? 0 //经过的时间
        const attackPoint = data.attackPoint || this.prePoint
        this.setDir(attackPoint.x - this.point.x)
        if (time > 0) {
            this.prePoint.set(this.data.point)
            this.playSFX('sound_037_1')
            this.playAnimation('diaup')
            this.isDiaup = true
            const pos = this.getActPixelByPoint(this.data.point)
            this.animCmpt.resetMove().setMoveParabolaHeight(data.parabolaHeight ?? 20).moveNodeOne(0, time, this.getPosition(), pos, () => {
                if (this.isValid && !this.isDie) {
                    this.playAnimation('idle')
                }
            })
        } else {
            this.playAnimation('idle')
            this.updatePosition()
        }
    }

    // 恐惧
    private doFear(data: any) {
        const time = data.time ?? 0 //经过的时间
        if (time > 0 && !this.prePoint.equals(this.data.point)) {
            this.setDir(this.point.x - this.prePoint.x)
            this.prePoint.set(this.data.point)
            const pos = this.getActPixelByPoint(this.data.point)
            this.playAnimation('move', null, 0, 0.5)
            this.animCmpt.resetMove().moveNodeOne(0, time, this.getPosition(), pos, () => {
                if (this.isValid && !this.isDie) {
                    this.playAnimation('idle')
                }
            })
        } else {
            this.playAnimation('idle')
            this.updatePosition()
        }
    }

    // 回血
    private doHeal(data: any) {
        const index = this.data.aIndex
        const val = data.val ?? 0
        const time = data.time ?? 0
        const uid = this.uid
        this.hpBar?.play()
        if (this.preShieldValue > 0) { //这里主动刷新一下护盾
            this.hpBar?.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp())
        }
    }

    // 掉血
    private doDeductHp(data: any) {
        const index = this.data.aIndex
        const damage = data.damage ?? 0
        const trueDamage = data.trueDamage ?? 0
        const time = data.time ?? 0
        const isDie = this.isDie = !!data.isDie
        const uid = this.uid
        if (isDie) {
            this.hpBar?.setActive(false)
            this.node.zIndex = 0
            this.putAllBuff()
            this.playAnimation('die', () => eventCenter.emit(EventType.REMOVE_PAWN, index, uid))
        } else {
            this.hpBar?.play()
        }
    }

    // 添加怒气
    private doAddAnger(data: any) {
        this.updateAnger()
        // this.doStand()
    }
}