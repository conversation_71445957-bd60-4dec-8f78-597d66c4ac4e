import { ARMY_PAWN_MAX_COUNT, HOSPITAL_PAWN_LIMIT } from "../../common/constant/Constant";
import { ArmyItem } from "../../common/constant/DataType";
import { ecode } from "../../common/constant/ECode";
import { ArmyState, CEffect, CType, PreferenceKey } from "../../common/constant/Enums";
import { IPawnDrillInfo } from "../../common/constant/Interface";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { netHelper } from "../../common/helper/NetHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import AreaCenterModel from "../../model/area/AreaCenterModel";
import ArmyObj from "../../model/area/ArmyObj";
import BuildObj from "../../model/area/BuildObj";
import PawnObj from "../../model/area/PawnObj";
import CTypeObj from "../../model/common/CTypeObj";
import UserModel from "../../model/common/UserModel";
import PawnCureInfoObj from "../../model/main/PawnCureInfoObj";
import PlayerModel from "../../model/main/PlayerModel";
import TextButtonCmpt from "../cmpt/TextButtonCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class BuildHospitalPnlCtrl extends mc.BasePnlCtrl {

	//@autocode property begin
	private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce
	private pagesNode_: cc.Node = null // path://root/pages_n
	private sortSelectNode_: cc.Node = null // path://root/pages_n/1/info/pawn/sort/sort_select_be_n
	private queueSv_: cc.ScrollView = null // path://root/pages_n/1/cure/content/queue_sv
	private upTimeNode_: cc.Node = null // path://root/pages_n/1/cure/x/up_time_be_n
	//@end

	private PKEY_TAB: string = 'HOSPITAL_TAB'
	private PKEY_SELECT_ARMY: string = 'HOSPITAL_SELECT_ARMY'
	private PKEY_SELECT_PAWN: string = 'HOSPITAL_SELECT_PAWN'

	private user: UserModel = null
	private player: PlayerModel = null
	private areaCenter: AreaCenterModel = null
	private data: BuildObj = null

	private cureProgressTween: { [key: number]: cc.Tween } = {}

	private currSelectSort: number = 0 // 当前选择的排序方式
	private preSelectIndex: number = -1 // 治疗的目标在伤兵中的下标
	private tempCreateArmy: ArmyObj = null
	private selectArmy: ArmyItem = null
	private tempArmySortWeightMap: { [key: string]: number } = {}

	public listenEventMaps() {
		return [
			{ [EventType.UPDATE_BUILD_LV]: this.onUpdateBuildLv, enter: true },
			{ [EventType.UPDATE_PAWN_INJURY_QUEUE]: this.onUpdatePawnInjuryQueue, enter: true },
			{ [EventType.UPDATE_PAWN_CURING_QUEUE]: this.onUpdatePawnCuringQueue, enter: true },
			{ [EventType.AREA_BATTLE_BEGIN]: this.onAreaBattleBegin, enter: true },
			{ [EventType.AREA_BATTLE_END]: this.onAreaBattleEnd, enter: true },
			{ [EventType.CHANGE_PAWN_SKIN]: this.onChangePawnSkin, enter: true },
			{ [EventType.CHANGE_PAWN_EQUIP]: this.onChangePawnEquip, enter: true },
			{ [EventType.ADD_ARMY]: this.onUpdateArmy, enter: true },
			{ [EventType.REMOVE_ARMY]: this.onUpdateArmy, enter: true },
			{ [EventType.UPDATE_ARMY]: this.onUpdateArmy, enter: true },
			{ [EventType.UPDATE_ALL_ARMY]: this.onUpdateArmy, enter: true },
			{ [EventType.UPDATE_ARMY_DIST_INFO]: this.onUpdateArmy, enter: true },
			{ [EventType.UPDATE_BATTLE_ARMY_BY_UI]: this.onUpdateArmy, enter: true },
		]
	}

	public async onCreate() {
		this.user = this.getModel('user')
		this.player = this.getModel('player')
		this.areaCenter = this.getModel('areaCenter')
	}

	public onEnter(data: BuildObj, tab?: number) {
		this.data = data
		this.tempCreateArmy = this.user.getTempPreferenceMap(PreferenceKey.TEMP_CREATE_ARMY)
		const cond = this.pagesNode_.Child('1/info/cond')
		cond.Child('need/title/layout/val').setLocaleKey('ui.drill_cost', 'ui.button_cure')
		this.pagesNode_.Child('1/cure/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_cure')
		this.tabsTc_.Tabs(tab ?? (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0))
		// 排序选择
		this.selectSortItem(this.sortSelectNode_, gameHpr.user.getLocalPreferenceData(PreferenceKey.INJURY_QUEUE_SORT) ?? 5, true)
	}

	public onRemove() {
		this.selectArmy = null
		this.tempArmySortWeightMap = {}
		viewHelper.closePopupBoxList(this.sortSelectNode_)
		this.showCreateArmyFingerTip(false)
		if (this.currSelectSort !== gameHpr.user.getLocalPreferenceData(PreferenceKey.INJURY_QUEUE_SORT)) {
			gameHpr.user.setLocalPreferenceData(PreferenceKey.INJURY_QUEUE_SORT, this.currSelectSort)
		}
		this.user.setTempPreferenceData(PreferenceKey.TEMP_CREATE_ARMY, this.tempCreateArmy)
	}

	public onClean() {
		assetsMgr.releaseTempResByTag(this.key)
	}

	// ----------------------------------------- button listener function -------------------------------------------
	//@autocode button listener

	// path://root/tabs_tc_tce
	onClickTabs(event: cc.Toggle, data: string) {
		!data && audioMgr.playSFX('click')
		const type = Number(event.node.name)
		this.user.setTempPreferenceData(this.PKEY_TAB, type)
		const node = this.pagesNode_.Swih(type)[0]
		if (type === 0) {
			// viewHelper.updateBuildBaseUI(node, this.data, this.key)
			viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key)
			viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key)
		} else if (type === 1) {
			// 显示当前的军队列表
			this.selectArmy = null
			this.tempArmySortWeightMap = {}
			this.updateArmyList(true, node)
			// 显示可治疗的士兵
			this.updateInjuryList(true, node)
			// 费用
			this.updateCureCost(node)
			// 治疗列表
			this.updateCureQueue(node)
		}
	}

	// path://root/pages_n/0/bottom/buttons/up_be
	onClickUp(event: cc.Event.EventTouch, data: string) {
		gameHpr.clickBuildUp(this.data, this)
	}

	// path://root/pages_n/1/info/pawn/list/view/content/pawn_be
	onClickPawn(event: cc.Event.EventTouch, _data: string) {
		audioMgr.playSFX('click')
		const it = event.target, data = it.Data, uid = data?.data.uid
		if (!data) {
			return
		} else if (uid === this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)) {
			uid && viewHelper.showPnl('area/PawnInfo', data.pawn)
			return
		}
		this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, uid)
		this.preSelectIndex = this.player.getInjuryPawns().findIndex(m => m.uid === uid)
		this.updatePawnSelect(false)
		this.updateCureCost()
	}

	// path://root/pages_n/1/army/list/view/content/army_be
	onClickArmy(event: cc.Event.EventTouch, _data: string) {
		audioMgr.playSFX('click')
		const it = event.target, data: ArmyItem = it.Data
		if (!data) {
			if (this.tempCreateArmy) {
				return viewHelper.showAlert('toast.yet_has_empty_army')
			}
			return this.showCreateArmyUI()
		} else if (!data.army) {
			return viewHelper.showAlert('toast.army_not_in_maincity')
		} else if (data.uid !== this.selectArmy?.uid) {
			this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid)
			this.updateArmySelect(data)
		}
	}

	// path://root/pages_n/1/info/cond/need/buttons/state/cure_be
	onClickCure(event: cc.Event.EventTouch, data: string) {
		const pawnUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)
		if (!pawnUid) {
			return
		}
		const area = this.areaCenter.getArea(this.data.aIndex)
		if (!area) {
			return
		} else if (!this.selectArmy) {
			if (area.armys.length === 0 || area.armys.every(m => m.pawns.length >= ARMY_PAWN_MAX_COUNT)) { //一个军队也没有
				viewHelper.showAlert('toast.please_create_army', {
					cb: () => {
						if (this.isValid && !this.player.isArmyCountFull() && !gameHpr.isNoLongerTip('no_army')) {
							this.showCreateArmyFingerTip(true)
						}
					}
				})
			} else {
				viewHelper.showAlert('toast.please_select_army')
			}
			return
		}
		let selectArmyUid = this.selectArmy.uid, tempArmyUid = '', armyName = ''
		const army = area.getArmyByUid(selectArmyUid) || this.getTempCreateArmy(selectArmyUid)
		if (!army) {
			return viewHelper.showAlert(ecode.ARMY_NOT_EXIST)
		} else if (army.uid.startsWith('temp_')) {
			armyName = army.name
		} else {
			tempArmyUid = army.uid
		}
		this.areaCenter.curePawnToServer(this.data.aIndex, tempArmyUid, armyName, pawnUid).then(res => {
			if (!this.isValid) {
			} else if (res.err === ecode.TEXT_LEN_LIMIT) {
				return viewHelper.showAlert('toast.text_len_limit_name')
			} else if (res.err === ecode.TEXT_HAS_SENSITIVE) {
				return viewHelper.showAlert('toast.has_sensitive_word_name')
			} /* else if (res.err === ecode.ANTI_CHEAT) {
				viewHelper.showPnl('main/AntiCheat')
			} */ else if (res.err) {
				viewHelper.showAlert(res.err)
			} else {
				audioMgr.playSFX('build/sound_ui_00' + (this.data.id === 2010 ? '7' : '6'))
				const army = res.army
				if (this.tempCreateArmy?.uid === selectArmyUid) {
					this.tempCreateArmy = null
					if (this.selectArmy) this.selectArmy.uid = army.uid
					if (this.tempArmySortWeightMap[selectArmyUid]) {
						this.tempArmySortWeightMap[army.uid] = this.tempArmySortWeightMap[selectArmyUid]
						delete this.tempArmySortWeightMap[selectArmyUid]
					}
					this.player.getBaseArmys().push({
						index: this.data.aIndex,
						uid: army.uid,
						name: army.name, //军队名字
						state: ArmyState.CURING,
						pawns: [], //士兵列表
					})
				}
				const node = this.pagesNode_.Child(1)
				this.updateCureQueue(node)
				this.updateInjuryList(false, node)
				// this.updateArmyList(false, node)
				// this.updateCureCost(node)
				this.updateCureCost(node)
			}
		})
	}

	// path://root/pages_n/1/cure/content/0/cure_pawn_be
	onClickCurePawn(event: cc.Event.EventTouch, _data: string) {
		audioMgr.playSFX('click')
		const data: PawnCureInfoObj = event.target.parent.Data
		if (data) {
			viewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByCureInfo(data), data)
		}
	}

	// path://root/pages_n/1/info/cond/need/buttons/delete_be
	onClickDelete(event: cc.Event.EventTouch, _data: string) {
		const uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN) || ''
		if (uid) {
			if (this.player.getCuringPawnsQueue().some(m => m.uid === uid)) { // 治疗中无法删除
				this.updateCureButton(uid)
				return viewHelper.showAlert('toast.delete_curing_pawn_tip')
			}
			const data = this.player.getInjuryPawns().find(m => m.uid === uid)
			if (data) {
				viewHelper.showMessageBox('ui.giveup_cure_tip', {
					params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv])],
					ok: () => this.isValid && this.giveUpCure(uid),
					cancel: () => { },
				})
			}
		}
	}

	// path://root/pages_n/1/info/pawn/sort/sort_select_be_n
	onClickSortSelect(event: cc.Event.EventTouch, data: string) {
		audioMgr.playSFX('click')
		viewHelper.changePopupBoxList(event.target, true)
	}

	// path://root/pages_n/1/info/pawn/sort/sort_select_be_n/select_mask_be
	onClickSelectMask(event: cc.Event.EventTouch, data: string) {
		viewHelper.changePopupBoxList(event.target.parent, false)
	}

	// path://root/pages_n/1/info/pawn/sort/sort_select_be_n/mask/root/sort_items_nbe
	onClickSortItems(event: cc.Event.EventTouch, data: string) {
		const node = this.sortSelectNode_
		viewHelper.changePopupBoxList(node, false)
		const type = Number(event.target.name)
		if (type !== this.currSelectSort) {
			this.selectSortItem(node, type)
		}
	}

	// path://root/pages_n/1/cure/x/up_time_be_n
	onClickUpTime(event: cc.Event.EventTouch, data: string) {
		viewHelper.showPnl('build/SpeedUpCure', this.data)
	}

	// path://root/pages_n/1/info/cond/need/title/layout/view_cured_be
	onClickViewCured(event: cc.Event.EventTouch, data: string) {
		viewHelper.showPnl('common/Desc', { text: 'ui.cured_count_desc' })
	}

	// path://root/pages_n/1/cure/content/0/cancel_cure_be
	onClickCancelCure(event: cc.Event.EventTouch, _data: string) {
		const data = event.target.parent.Data
		if (!data) {
			return
		} else if (data.surplusTime > 0) {
			return viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {
				ok: () => this.isValid && this.cancelCure(data),
				cancel: () => { },
			})
		}
		this.cancelCure(data)
	}

	// path://root/pages_n/0/info/chance_be
	onClickChance(event: cc.Event.EventTouch, data: string) {
		viewHelper.showPnl('build/HospitalChanceDesc')
	}
	//@end
	// ----------------------------------------- event listener function --------------------------------------------

	private onUpdateBuildLv(data: BuildObj) {
		if (this.data.uid === data.uid) {
			const node = this.pagesNode_.Child(0)
			node.Child('lv').setLocaleKey('ui.lv', data.lv)
			viewHelper.updateBuildAttrInfo(node, data)
		}
	}

	private onUpdatePawnInjuryQueue() {
		this.updateInjuryList(false)
	}

	// 刷新治疗列表
	private onUpdatePawnCuringQueue() {
		this.updateCureQueue()
		// this.updateArmyList(false)
		// this.updateCureCost()
	}

	// 战斗开始
	private onAreaBattleBegin(index: number) {
		if (this.data.aIndex === index) {
			this.updateCureQueue()
		}
	}

	// 战斗结束
	private onAreaBattleEnd(index: number) {
		if (this.data.aIndex === index) {
			this.updateCureQueue()
		}
	}

	// 切换士兵皮肤
	private onChangePawnSkin(data: PawnObj) {
		this.updateInjuryList(false)
		this.updateCureQueue()
	}

	// 切换士兵装备
	private onChangePawnEquip() {
		this.updateInjuryList(false)
	}

	// 重新刷新军队列表
	private onUpdateArmy() {
		this.updateArmyList(false)
	}

	// ----------------------------------------- custom function ----------------------------------------------------
	private getTempCreateArmy(uid: string) {
		if (this.tempCreateArmy?.uid === uid) {
			return this.tempCreateArmy
		}
		return null
	}

	// 选择排序
	private selectSortItem(node: cc.Node, type: number, init?: boolean) {
		node.Data = this.currSelectSort = type
		node.Child('val', cc.Label).setLocaleKey('ui.portrayal_list_sort_' + type)
		node.Child('mask/root/sort_items_nbe').children.forEach(m => {
			const select = Number(m.name) === type
			// m.Child('val').Color(select ? '#E6DCC8' : '#B6A591')
			m.Child('select').active = select
		})
		if (!init) {
			this.updateInjuryList(false)
		}
	}

	// 放弃治疗
	private giveUpCure(uid: string) {
		netHelper.reqGiveUpInjuryPawn({ uid }).then(res => {
			if (res.err) {
				return viewHelper.showAlert(res.err)
			} else {
				const data = res.data
				this.player.updateInjuryPawns(data.injuryPawns)
				this.updateInjuryList(false)
				const selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)
				this.updateCureButton(selectUid)
			}
		})
	}

	private addArmyToList(data: any, army: ArmyObj, pawns?: any[]) {
		const item: ArmyItem = {
			name: data.name,
			uid: data.uid,
			pawns,
			army
		}
		if (!this.tempArmySortWeightMap[data.uid]) {
			let weight = item.army ? 2 : 1
			weight = weight * 10 + (9 - (item.army?.getActPawnCount() || 0))
			weight = weight * 10 + (9 - (item.army?.pawns.length || 0))
			this.tempArmySortWeightMap[data.uid] = weight
		}
		return item
	}

	// 刷新军队列表
	private updateArmyList(isLocation: boolean, node?: cc.Node) {
		node = node || this.pagesNode_.Child(1)
		// 当前区域的军队
		const areaArmyMap: { [key: string]: ArmyObj } = {}
		this.areaCenter.getArea(this.data.aIndex)?.armys.forEach(m => {
			if (m.isCanDrillPawn()) {
				areaArmyMap[m.uid] = m
			}
		})
		const armys: ArmyItem[] = [null]
		// 先装自己所有的军队 再装临时创建的军队
		this.player.getBaseArmys().forEach(m => armys.push(this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)))
		if (this.tempCreateArmy) {
			armys.push(this.addArmyToList(this.tempCreateArmy, this.tempCreateArmy))
		}
		// 排个序
		armys.sort((a, b) => this.tempArmySortWeightMap[b?.uid] - this.tempArmySortWeightMap[a?.uid])
		const countNode = node.Child('army/title/count_bg')
		countNode.Child('cur', cc.Label).string = armys.length + ''
		countNode.Child('max', cc.Label).string = '/' + this.player.getArmyMaxCount()
		const uid = this.selectArmy?.uid ?? this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY)
		let curArmy = uid ? armys.find(m => !!m?.army && m?.uid === uid) : null, index = -1
		const sv = node.Child('army/list', cc.ScrollView)
		sv.stopAutoScroll()
		// armys.push(null)
		sv.Items(armys, (it, data, i) => {
			it.Data = data
			const army = data?.army
			const root = it.Child('root')
			root.Child('add').active = !data
			root.Child('count').active = !!data
			root.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : ''
			const state = root.Child('state')
			if (army) {
				root.Child('count/val', cc.Label).string = (data.pawns?.length || 0) + ''
				const addLbl = root.Child('count/add', cc.Label), dpc = army.drillPawns.length + army.curingPawns.length
				if (addLbl.node.active = dpc > 0) {
					addLbl.string = '+' + dpc
				}
				const isFull = state.active = army.getActPawnCount() >= ARMY_PAWN_MAX_COUNT
				root.opacity = isFull ? 150 : 255
				if (isFull) {
					state.setLocaleKey('ui.yet_full')
				}
				// 显示选择
				if (!curArmy && isFull) {
				} else if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {
					curArmy = data
					index = i
					this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, army.uid)
				}
			} else if (data) {
				root.opacity = 150
				root.Child('count/val', cc.Label).string = (data.pawns?.length || 0) + ''
				root.Child('count/add').active = false
				state.active = true
				state.setLocaleKey('ui.go_out')
			} else {
				root.opacity = 255
				state.active = false
			}
		})
		// 将选中的移动到中间
		if (isLocation) {
			sv.SelectItemToCentre(index)
		}
		// 刷新选中
		this.updateArmySelect(curArmy, node)
		/* // 将选中的移动到中间
		if (index !== -1) {
			const lay = sv.content.Component(cc.Layout)
			lay.updateLayout()
			const width = sv.content.children[0].width
			const tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft //当前位置
			const pw = sv.content.parent.width
			const cx = pw * 0.5 //中间位置
			sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0)
		} else {
			sv.scrollToLeft()
		} */
	}

	private updateArmySelect(item: ArmyItem, node?: cc.Node) {
		node = node || this.pagesNode_.Child(1)
		this.selectArmy = item
		const uid = item?.uid || ''
		node.Child('army/list', cc.ScrollView).content.children.forEach(m => {
			const select = m.Child('select').active = m.Data?.uid === uid
			m.Component(cc.Button).interactable = !select
		})
		const army = item?.army, pawns = []
		if (army) {
			pawns.pushArr(army.pawns)
			pawns.pushArr(army.curingPawns)
			pawns.pushArr(army.drillPawns)
		} else if (item?.pawns) {
			pawns.pushArr(item.pawns)
		}
		// 刷新士兵列表
		node.Child('info/army_pawns').children.forEach((it, i) => {
			const data = pawns[i], isId = typeof (data) === 'number', isCuring = !!data && !!army?.curingPawns.some(m => m.uid === data.uid)
			it.Swih('none', !!data)
			if (data) {
				const icon = it.Child('icon')
				icon.opacity = (isId || isCuring) ? 120 : 255
				resHelper.loadPawnHeadMiniIcon(isId ? data : (data.portrayal?.id || data.id), icon, this.key)
				it.Child('lv', cc.Label).string = isId || data.lv <= 1 ? '' : data.lv + ''
			}
		})
		// 刷新按钮
		const buttons = node.Child('info/cond/need/buttons'), button = buttons.Child('state').Swih('cure_be')[0]
		button.Data = army ? (army.getActPawnCount() >= ARMY_PAWN_MAX_COUNT ? 1 : 0) : 2
		button.opacity = !!item && button.Data ? 120 : 255
		if (pawns.length < ARMY_PAWN_MAX_COUNT) {
			const uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)
			this.updateCureButton(uid, buttons.parent)
		}
	}

	// 刷新士兵列表
	private updateInjuryList(isLocation: boolean, node?: cc.Node) {
		node = node || this.pagesNode_.Child(1)
		const pawns = this.player.getInjuryPawns(), pawnCount = pawns.length
		const mapObj = {}
		this.player.getCuringPawnsQueue().forEach(m => mapObj[m.uid] = true)
		pawns.sort((a, b) => {
			const aState = mapObj[a.uid] ? 1 : 0, bState = mapObj[b.uid] ? 1 : 0
			if (aState !== bState) {
				return aState - bState
			}
			switch (this.currSelectSort) {
				case 0: // 兵种升序 > 等级降序 > 时间降序
					if (a.id !== b.id) return a.id - b.id
					if (a.lv !== b.lv) return b.lv - a.lv
					return b.deadTime - a.deadTime
				case 4: // 时间降序 > 兵种升序 > 等级降序 
					if (a.deadTime !== b.deadTime) return b.deadTime - a.deadTime
					if (a.id !== b.id) return a.id - b.id
					return b.lv - a.lv
				case 5: // 等级降序  > 兵种升序 > 时间降序
					if (a.lv !== b.lv) return b.lv - a.lv
					if (a.id !== b.id) return a.id - b.id
					return b.deadTime - a.deadTime
			}
			return b.lv - a.lv
		})
		let selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN), nextUid = selectUid
		if (!pawns.some(m => m.uid === selectUid) || mapObj[selectUid]) { // 士兵治疗完成 或 士兵治疗中，需要自动切换治疗目标
			nextUid = 0
		}
		const curingQueue = this.player.getCuringPawnsQueue()
		const sv = node.Child('info/pawn/list', cc.ScrollView)
		sv.Child('empty').active = !pawnCount
		sv.Items(pawns, (it, data, i) => {
			const conf = this.player.getConfigPawnInfo(data.id),
				pawn = new PawnObj().init(data.id, conf.equip, data.lv, conf.skinId)
			it.Data = { data: data, pawn: pawn }
			it.Child('icon').opacity = curingQueue.some(m => m.uid === data.uid) ? 120 : 255
			it.Child('lv/val', cc.Label).string = data.lv + ''
			const iconNode = it.Child('icon')
			resHelper.loadPawnHeadIcon(conf.skinId || data.id, iconNode, this.key)
			if (i >= this.preSelectIndex && !nextUid && !mapObj[data.uid]) {
				nextUid = this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, data.uid)
			}
		})
		// 没有找到下一个，就找上一个
		if (!nextUid) {
			for (let i = pawns.length - 1; i >= 0; i--) {
				const pawn = pawns[i]
				if (!nextUid && i <= this.preSelectIndex && !mapObj[pawn.uid]) {
					nextUid = this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, pawn.uid)
				}
			}
		}
		// 将选中的移动到中间
		if (this.preSelectIndex !== -1) {
			const lay = sv.content.Component(cc.Layout)
			lay.updateLayout()
			const width = sv.content.children[0].width
			const tx = (width + lay.spacingX) * this.preSelectIndex + width * 0.5 + lay.paddingLeft //当前位置
			const pw = sv.content.parent.width
			const cx = pw * 0.5 //中间位置
			sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0)
		} else {
			sv.scrollToLeft()
		}
		this.updatePawnSelect(isLocation, node)
		if (pawnCount <= 0) {
			this.updateCureCost()
		}
		// 刷新数量
		this.pagesNode_.Child('1/info/pawn/title/val').setLocaleKey('ui.select_wounded', pawnCount, HOSPITAL_PAWN_LIMIT)
	}

	private updatePawnSelect(isLocation: boolean, node?: cc.Node) {
		node = node || this.pagesNode_.Child(1)
		const selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)
		let selectIndex = -1
		const sv = node.Child('info/pawn/list', cc.ScrollView)
		sv.content.children.forEach((m, i) => {
			/* const uid = m.Data?.data?.uid
			m.Child('bg/select').active = m.Child('select').active = uid === selectUid */
			const select = m.Child('bg/select').active = m.Child('select').active = m.Data?.data.uid === selectUid
			m.Component(cc.Button).interactable = !select || !!m.Data?.pawn
			if (select) {
				selectIndex = i
			}
		})
		if (isLocation) {
			sv.SelectItemToCentre(selectIndex)
		}
	}

	// 刷新治疗费用
	private updateCureCost(node?: cc.Node) {
		node = node || this.pagesNode_.Child(1)
		const uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)
		const data = node.Child('info/pawn/list', cc.ScrollView).Find(m => m.Data?.data?.uid === uid)?.Data?.data
		const need = node.Child('info/cond/need'), empty = node.Child('info/cond/empty')
		empty.active = !data
		if (need.active = !!data) {
			// 计算当前等级总耗时、总耗资
			// (招募三资 + 训练三资) * (1.0 + 治疗次数 * 0.2)
			// (招募时间 + 训练时间) * (0.5 + 阵亡次数 * 0.5) * (1 - 建筑等级  * 0.02)
			const baseCfg = assetsMgr.getJsonData('pawnBase', data.id)
			let cost = [], cureTime = baseCfg.drill_time
			cost.pushArr(gameHpr.stringToCTypes(baseCfg.drill_cost))
			for (let i = 1; i < data.lv; i++) {
				const cfg = assetsMgr.getJsonData('pawnAttr', data.id * 1000 + i)
				cost.pushArr(gameHpr.stringToCTypes(cfg.lv_cost))
				cureTime += cfg.lv_time
			}
			// 粮耗
			const crealCost = new CTypeObj().init(CType.CEREAL_C, 0, baseCfg.cereal_cost || 0)
			cost.push(crealCost)
			// // 检测是否有治疗士兵费用增加
			// cost = gameHpr.world.getSeason().changeBaseResCost(CEffect.CURE_COST, cost)
			const finalCost: CTypeObj[] = []
			gameHpr.mergeTypeObjsCount(finalCost, ...cost)
			// 剔除经验书，后续用来加速
			for (let i = finalCost.length - 1; i >= 0; i--) {
				if (finalCost[i].type === CType.EXP_BOOK) {
					finalCost.splice(i, 1)
				}
			}
			finalCost.forEach(m => m.type !== CType.CEREAL_C && (m.count = Math.floor(m.count * (1 + data.cureCount * 0.2))))
			cureTime = cureTime * (0.5 + data.cureCount * 0.5)
			// 治疗消耗信息
			const cd = this.getCureTimeCD(), policyFreeCount = this.player.getFreeCurePawnSurplusCount()
			viewHelper.updateFreeCostView(need, finalCost, cureTime, cd, false, policyFreeCount)
			need.Child('buttons/state/cure_be/val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_cure' : 'ui.button_cure')
			const curedCount = need.Child('title/layout/view_cured_be'), has = !!data.cureCount
			curedCount.active = has
			has && curedCount.Component(TextButtonCmpt).setKey('ui.button_cured_count', [data.cureCount])
			// need.Child('cost').Items(finalCost || [], (it, cost) => viewHelper.updateCostViewOne(it, cost, true))
			// if (need.Child('time')?.setActive(!!cureTime)) {
			// 	const up = need.Child('time/up', cc.Label)
			// 	if (up?.setActive(!!cd)) {
			// 		up.string = `(-${Math.floor(cd * 100)}%)`
			// 		cureTime = Math.max(3, Math.floor(cureTime * (1 - cd)))
			// 	}
			// 	need.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(cureTime, 'h:mm:ss')
			// }
			// 刷新按钮
			this.updateCureButton(data.uid, need)
		}
	}

	private updateCureButton(uid: string, node?: cc.Node) {
		node = node || this.pagesNode_.Child('1/info/cond/need')
		const info = gameHpr.player.getCuringPawnsQueue().find(m => m.uid === uid)
		const buttons = node.Child('buttons'), button = buttons.Child('state/cure_be')
		button.opacity = button.Data ? 120 : 255
		buttons.Child('delete_be').opacity = !!info ? 120 : 255
		if (info) { // 在治疗队列中
			buttons.Child('state').Swih('curing')[0].Child('val').setLocaleKey(info.surplusTime > 0 ? 'ui.army_state_6' : 'ui.queueing')
		} else {
			buttons.Child('state').Swih('cure_be')
		}
	}

	// 获取治疗减CDBuff
	private getCureTimeCD() {
		let cd = gameHpr.getPlayerPolicyEffect(CEffect.CURE_CD)
		if (this.data.effect?.type === CEffect.CURE_CD) {
			cd += this.data.effect.value
		}
		return cd * 0.01
	}

	// 刷新治疗列表
	private updateCureQueue(node?: cc.Node) {
		node = node || this.pagesNode_.Child(1)
		const list = this.player.getCuringPawnsQueue()
		this.upTimeNode_.active = list.length > 0
		list.sort((a, b) => b.surplusTime - a.surplusTime)
		const pawnConf = this.player.getConfigPawnMap()
		let time = 0
		// 是否有政策的加成
		const queueCount = 6 + gameHpr.getPlayerPolicyEffect(CEffect.CURE_QUEUE)
		node.Child('cure/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')'
		for (let i = 0; i < queueCount; i++) {
			let it = null, data = list[i]
			if (i === 0) {
				it = node.Child('cure/content/' + i)
			} else {
				const childrenCount = this.queueSv_.content.childrenCount
				if (childrenCount <= 1) {
					this.queueSv_.Items(queueCount - 1, () => { })
				}
				it = this.queueSv_.content.children[i - 1]
			}
			it.Data = data
			const skinId = data ? (pawnConf[data.id]?.skinId || data.id) : 0
			const has = it.Child('icon').active = it.Child('cure_pawn_be').active = !!data
			it.Child('cancel_cure_be')?.setActive(has)
			it.Child('icon/progress')?.setActive(has)
			it.Child('lv/val', cc.Label).string = data ? data.lv + '' : ''
			resHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key)
			if (i !== 0) {
				time += data?.needTime || 0
			} else if (data) {
				const progress = it.Child('icon/progress', cc.Sprite)
				resHelper.loadPawnHeadIcon(skinId, progress, this.key)
				const stime = data.getSurplusTime()
				time += stime
				this.cureProgressTween[i]?.stop()
				this.cureProgressTween[i] = null
				progress.fillRange = stime / data.needTime
				const st = stime * 0.001
				it.Child('time', cc.LabelTimer).run(st)
				this.cureProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start()
			} else {
				it.Child('time', cc.LabelTimer).string = ''
			}
		}
		node.Child('cure/desc').active = time > 0
		if (time > 0) {
			node.Child('cure/desc/title').setLocaleKey('ui.drill_all_desc', 'ui.button_cure')
			node.Child('cure/desc/time/val', cc.LabelTimer).run(time * 0.001)
		}
	}

	private showCreateArmyUI() {
		if (gameHpr.player.isArmyCountFull()) {
			return viewHelper.showAlert(ecode.PLAYER_FULL_ARMY)
		}
		this.showCreateArmyFingerTip(false)
		return viewHelper.showPnl('common/CreateArmy', (name: string) => {
			if (this.isValid) {
				this.tempCreateArmy = new ArmyObj().init(this.data.aIndex, gameHpr.getUid(), name)
				this.tempArmySortWeightMap = {}
				if (!this.selectArmy) {
					this.selectArmy = {} as any
				}
				this.selectArmy.uid = this.tempCreateArmy.uid
				this.updateArmyList(true, this.pagesNode_.Child(1))
			}
		})
	}

	// 显示创建军队提示手指
	private showCreateArmyFingerTip(val: boolean) {
		const node = this.pagesNode_.Child(1)
		const sv = node.Child('army/list', cc.ScrollView), finger = sv.Child('finger')
		if (finger.active = val) {
			const count = sv.content.childrenCount
			sv.stopAutoScroll()
			if (count >= 4) {
				sv.scrollToRight()
			}
			const it = sv.content.children[count - 1]
			const pos = ut.convertToNodeAR(it, sv.node)
			finger.setPosition(pos.x, pos.y - 12)
		}
	}

	// 取消治疗
	private cancelCure(info: IPawnDrillInfo) {
		if (!this.data) {
			return
		}
		const index = info.index
		const uid = info.uid
		const json = info.json
		netHelper.reqCancelCurePawn({ index, uid }).then(res => {
			if (res.err) {
				return viewHelper.showAlert(res.err)
			} else {
				const data = res.data
				gameHpr.player.updateOutputByFlags(data.output)
				gameHpr.areaCenter.getArea(index)?.updateArmyCurePawns(data.army)
				gameHpr.player.updatePawnCuringQueue(data.queues)
				gameHpr.delMessageByTag(uid)
				this.emit(EventType.UPDATE_PAWN_INJURY_QUEUE)
				if (data.needCost?.length) {
					viewHelper.showPnl('common/CancelDrill', {
						text: 'ui.cancel_cure_tip',
						id: json.id,
						cost: data.needCost,
					})
				}
			}
		})
	}
}
