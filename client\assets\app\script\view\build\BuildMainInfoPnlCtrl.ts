import { POLICY_SLOT_CONF } from "../../common/constant/Constant";
import { ecode } from "../../common/constant/ECode";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BuildObj from "../../model/area/BuildObj";
import UserModel from "../../model/common/UserModel";
import PlayerModel from "../../model/main/PlayerModel";
import PolicyObj from "../../model/main/PolicyObj";
import BuildUnlockTipCmpt from "../cmpt/BuildUnlockTipCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class BuildMainInfoPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce
    private pagesNode_: cc.Node = null // path://root/pages_n
    private landNode_: cc.Node = null // path://root/pages_n/0/info/land_n
    private unlockTipNode_: cc.Node = null // path://root/pages_n/0/bottom/title/unlock_tip_n
    private policySv_: cc.ScrollView = null // path://root/pages_n/1/info/policy_sv
    //@end

    private readonly PKEY_TAB: string = 'MAIN_INFO_TAB'
    private tab: number = 0

    private user: UserModel = null
    private player: PlayerModel = null
    private data: BuildObj = null
    private unlockTipCmpt: BuildUnlockTipCmpt = null

    public listenEventMaps() {
        return [
            { [EventType.UPDATE_BUILD_LV]: this.onUpdateBuildLv, enter: true },
            { [EventType.UPDATE_POLICY_SLOTS]: this.onUpdatePolicySlots, enter: true },
        ]
    }

    public async onCreate() {
        this.user = this.getModel('user')
        this.player = this.getModel('player')
        this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt)
    }

    public onEnter(data: BuildObj, tab?: number) {
        this.data = data
        this.tabsTc_.Tabs(tab ?? (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0))
    }

    public onRemove() {
    }

    public onClean() {
        assetsMgr.releaseTempResByTag(this.key)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/tabs_tc_tce
    onClickTabs(event: cc.Toggle, data: string) {
        !data && audioMgr.playSFX('click')
        const type = this.tab = Number(event.node.name)
        const node = this.pagesNode_.Swih(type)[0]
        this.user.setTempPreferenceData(this.PKEY_TAB, type)
        if (type === 0) {
            this.landNode_.Child('val').setLocaleKey('ui.cur_land_count', gameHpr.getPlayerOweCellCount(gameHpr.getUid()))
            this.unlockTipCmpt.updateInfo(this.data, POLICY_SLOT_CONF, this.key)
            viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key)
            viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key)
        } else if (type === 1) {
            this.showPolicyInfo(node)
        }
    }

    // path://root/pages_n/0/bottom/buttons/up_be
    onClickUp(event: cc.Event.EventTouch, data: string) {
        gameHpr.clickBuildUp(this.data, this)
    }

    // path://root/pages_n/1/policy/slot_nbe
    onClickSlot(event: cc.Event.EventTouch, _: string) {
        audioMgr.playSFX('click')
        const data: PolicyObj = event.target.Data, lv = Number(event.target.name)
        const isUnlock = this.data.lv >= lv
        if (!isUnlock) {
            return viewHelper.showAlert('ui.lv_unlock_new', { params: [assetsMgr.lang('ui.short_lv', lv), 'ui.ceri_type_name_1'] })
        } else if (!!data?.isYetStudy()) {
            return viewHelper.showPnl('common/PolicyInfoBox', data.id, 'book')
        } else if (!data || data.selectIds.length === 0) {
            return viewHelper.showAlert(ecode.NEED_STUDY_PER_SLOT)
        }
        viewHelper.showPnl('build/StudySelect', data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onUpdateBuildLv(data: BuildObj) {
        if (this.data.uid === data.uid) {
            const node = this.pagesNode_.Child(0)
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv)
            viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key)
            this.unlockTipCmpt.updateInfo(this.data, POLICY_SLOT_CONF, this.key, data.lv)
        }
    }

    // 刷新政策
    private onUpdatePolicySlots() {
        if (this.tab === 1) {
            this.showPolicyInfo(this.pagesNode_.Child(1))
        }
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    // 内政
    private showPolicyInfo(node: cc.Node) {
        const slot = node.Child('policy/slot_nbe'), buildLv = this.data.lv
        const policys = this.player.getPolicySlots()
        slot.children.forEach(it => {
            const lv = Number(it.name)
            const data = it.Data = policys[lv]
            const isUnlock = buildLv >= lv, isSelect = !!data?.isYetStudy()
            if (!isUnlock) { //还未解锁
                it.Swih('lock')
                // state.Color('#C34A32').setLocaleKey('ui.need_lv_unlock', lv)
            } else if (!isSelect) { //还未选择
                const canSelect = !!data?.selectIds.length
                it.Swih('add')[0].Child('dot').active = canSelect
                // state.Color(canSelect ? '#49983C' : '#756963').setLocaleKey(canSelect ? 'ui.can_study' : 'ui.button_wait_study')
            }
            // 选择信息
            if (isSelect) {
                resHelper.loadPolicyIcon(data.id, it.Swih('icon')[0], this.key)
            }
            // it.Component(cc.Button).interactable = isUnlock && !isSelect
        })
        this.updatePolicyEffectInfo()
    }

    // 内政效果
    private updatePolicyEffectInfo() {
        const initItemHeight = 92
        const datas = gameHpr.getPlayerPolicysBaseInfo()
        this.policySv_.stopAutoScroll()
        this.policySv_.Items(datas, (it, data, i) => {
            it.Child('name/val').setLocaleKey('policyText.name_' + data.id)
            const value = data.values[Math.min(data.up - 1, data.values.length - 1)]
            const params = `<color=#4AB32E>${value}</c>`
            const descRt = it.Child('desc', cc.RichText)
            descRt.setLocaleKey('policyText.desc_' + data.id, params)
            it.Child('policys').Items(data.styles, (item, style) => {
                resHelper.loadPolicyIcon(data.id, item.Child('icon'), this.key)
                const styleSpr = item.Child('style', cc.Sprite)
                if (style >= 10) { // 季节政策
                    resHelper.loadIcon('icon/season_' + (style % 10), styleSpr, this.key)
                } else if (style === 2) { // 联盟政策
                    resHelper.loadAlliIcon(gameHpr.alliance.getIcon(), styleSpr, this.key)
                } else {
                    styleSpr.Component(cc.Sprite).spriteFrame = null
                }
            })
            const h = Math.max(initItemHeight, (initItemHeight - descRt.lineHeight) + descRt.node.height)
            if (it.height !== h) {
                it.height = h
                it.children.forEach(m => m.Component(cc.Widget)?.updateAlignment())
            }
        })
    }
}
