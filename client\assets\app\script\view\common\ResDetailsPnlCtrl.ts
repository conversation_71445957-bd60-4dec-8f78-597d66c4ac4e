import { ADD_OUTPUT_GOLD, ADD_OUTPUT_RATIO, CELL_RES_FIELDS, CTYPE_NAME, INIT_RES_OUTPUT, RES_FIELDS_CTYPE } from "../../common/constant/Constant";
import { CEffect, CType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import MapCellObj from "../../model/main/MapCellObj";

const { ccclass } = cc._decorator;

@ccclass
export default class ResDetailsPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private landsSv_: cc.ScrollView = null // path://root/pages/lands_sv
    private sumNode_: cc.Node = null // path://root/pages/sum_n
    private buyAddOpNode_: cc.Node = null // path://root/pages/sum_n/5/buy_add_op_be_n
    private curLbl_: cc.Label = null // path://root/pages/cur_l
    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce
    //@end

    private type: number = 0
    private resMap: { [key: number]: { arr: { cell: MapCellObj, val: number, count: number }[], sum: number } } = {}

    public listenEventMaps() {
        return [
            { [EventType.UPDATE_ADD_OUTPUT_TIME]: this.onUpdateAddOutputTime, enter: true },
        ]
    }

    public async onCreate() {
        this.buyAddOpNode_.Child('val', cc.Label).string = ADD_OUTPUT_GOLD + ''
    }

    public onEnter(type: CType) {
        this.initResDist()
        this.tabsTc_.Tabs(type)
    }

    public onRemove() {
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/tabs_tc_tce
    onClickTabs(event: cc.Toggle, data: string) {
        !data && audioMgr.playSFX('click')
        this.showResDetails(Number(event.node.name))
    }

    // path://root/pages/sum_n/5/buy_add_op_be_n
    onClickBuyAddOp(event: cc.Event.EventTouch, data: string) {
        if (gameHpr.world.isGameOver()) {
            return viewHelper.showAlert('toast.gold_increase_output')
        }
        if (gameHpr.isNoviceMode) {
            return
        }
        const type = this.type
        const hasAdd = !!gameHpr.player.getAddOutputSurplusTime()[type]
        viewHelper.showMessageBox(hasAdd ? 'ui.add_output_desc_1' : 'ui.add_output_desc_0', {
            params: [ADD_OUTPUT_GOLD, ADD_OUTPUT_RATIO, CTYPE_NAME[type]],
            ok: () => gameHpr.player.buyAddOutputTime(type).then(err => {
                if (err) {
                    return viewHelper.showAlert(err)
                } else if (this.isValid) {
                    this.showResDetails(type)
                }
            }),
            cancel: () => { },
        })
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onUpdateAddOutputTime() {
        this.showResDetails(this.type)
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    private initResDist() {
        this.resMap = { [CType.CEREAL]: { arr: [], sum: 0 }, [CType.TIMBER]: { arr: [], sum: 0 }, [CType.STONE]: { arr: [], sum: 0 } }
        gameHpr.getPlayerInfo(gameHpr.getUid())?.cells?.forEach(cell => {
            if (cell.isHasRes()) {
                const json = cell.getResJson() || {}
                CELL_RES_FIELDS.filter(m => !!json[m]).forEach(m => {
                    const info = this.resMap[RES_FIELDS_CTYPE[m]], val = json[m]
                    const it = info.arr.find(x => (x.cell.landId === cell.landId || x.cell.cityId === cell.cityId) && x.val === val)
                    if (it) {
                        it.count += 1
                    } else {
                        info.arr.push({ cell, val, count: 1 })
                    }
                    info.sum += val
                })
            }
        })
        for (let key in this.resMap) {
            this.resMap[key].arr.sort((a, b) => b.val - a.val)
        }
    }

    private showResDetails(type: CType) {
        this.type = type
        const player = gameHpr.player
        const info = this.resMap[type], len = info.arr.length
        this.landsSv_.Child('empty').active = len === 0
        this.landsSv_.Items(info.arr, (it, data, i) => {
            it.Child('name').setLocaleKey(data.cell.getName())
            it.Child('val', cc.Label).string = data.val + ''
            it.Child('count', cc.Label).string = data.count + ''
            it.Child('line').active = i !== 4
        })
        // 初始资源
        let output = player.isCapture() ? 0 : INIT_RES_OUTPUT, addNum = 0
        this.sumNode_.Child('3/val', cc.Label).string = output + ''
        // 土地资源
        output += info.sum
        this.sumNode_.Child('1/val', cc.Label).string = info.sum + ''
        // 内政加成 固定
        const policyAddRes = gameHpr.getPlayerPolicyEffect(CEffect.RES_OUTPUT)
        if (this.sumNode_.Child('2').active = policyAddRes > 0) {
            output += policyAddRes
            this.sumNode_.Child('2/val', cc.Label).string = policyAddRes + ''
        }
        // 商城购买 百分比
        const time = player.getAddOutputSurplusTime()[type] || 0
        addNum = time > 0 ? Math.floor(output * ADD_OUTPUT_RATIO * 0.01) : 0
        output += addNum
        this.sumNode_.Child('5/desc/val').setLocaleKey('ui.add_output_desc', '+' + ADD_OUTPUT_RATIO)
        this.sumNode_.Child('5/val', cc.Label).string = addNum + ''
        // 购买加成
        if (this.sumNode_.Child('5').active = !gameHpr.isNoviceMode) {
            this.updateAddOutputStateTime(this.sumNode_.Child('5/desc/state'), time)
        }
        // 粮耗
        const cost = type === CType.CEREAL ? player.getCerealConsume() : 0
        if (this.sumNode_.Child('7').active = cost > 0) {
            output -= cost
            this.sumNode_.Child('7/val', cc.Label).string = '-' + cost
        }
        // 总
        this.sumNode_.Child('6/val', cc.Label).string = output + ''
        const cap = type === CType.CEREAL ? player.getGranaryCap() : player.getWarehouseCap()
        this.curLbl_.setLocaleKey('ui.cur_res_cap', gameHpr.getCountByCType(type) + '/' + cap)
    }

    private updateAddOutputStateTime(node: cc.Node, time: number) {
        const hasAdd = time > 0, color = hasAdd ? '#59A733' : '#D7634D'
        this.buyAddOpNode_.Child('desc').setLocaleKey(hasAdd ? 'ui.button_lengthen' : 'ui.button_enable')
        node.children.forEach(m => m.Color(color))
        node.Child('val').setLocaleKey(hasAdd ? 'ui.takeeffecting' : 'ui.not_takeeffect')
        node.Child('s').active = hasAdd
        const lbl = node.Child('time', cc.LabelTimer)
        if (lbl.setActive(hasAdd)) {
            lbl.run(time * 0.001)
        }
    }
}
