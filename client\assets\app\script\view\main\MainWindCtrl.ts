import { cameraCtrl } from "../../common/camera/CameraCtrl";
import { CAMERA_BG_COLOR, CELL_RES_FIELDS, CITY_FORT_NID, CITY_MAIN_NID, CTYPE_ICON, MAP_EXTRA_SIZE, MAP_MASK_ITEM_COLOR, MAP_SHOW_OFFSET, NOT_OCCUPY_BY_MAX_LAND_COUNT, SELECT_CELL_INFO_BOX, TILE_SIZE } from "../../common/constant/Constant";
import { ArmyShortInfo, PlayerInfo } from "../../common/constant/DataType";
import { ecode } from "../../common/constant/ECode";
import { DecorationType, LandType, PreferenceKey } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import NetEvent from "../../common/event/NetEvent";
import { animHelper } from "../../common/helper/AnimHelper";
import { gameHpr } from "../../common/helper/GameHelper";
import { guideHelper } from "../../common/helper/GuideHelper";
import { mapHelper } from "../../common/helper/MapHelper";
import { popupPnlHelper } from "../../common/helper/PopupPnlHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BuildObj from "../../model/area/BuildObj";
import CTypeObj from "../../model/common/CTypeObj";
import UserModel from "../../model/common/UserModel";
import AncientObj from "../../model/main/AncientObj";
import BaseMarchObj from "../../model/main/BaseMarchObj";
import BTCityObj from "../../model/main/BTCityObj";
import MapCellObj from "../../model/main/MapCellObj";
import PlayerModel from "../../model/main/PlayerModel";
import TondenObj from "../../model/main/TondenObj";
import WorldModel from "../../model/main/WorldModel";
import MapTouchCmpt from "../cmpt/MapTouchCmpt";
import SelectCellCmpt from "../cmpt/SelectCellCmpt";
import CellInfoCmpt from "./CellInfoCmpt";
import MapAnimNodePool from "./MapAnimNodePool";
import MarchCmpt from "./MarchCmpt";
import SceneEffectCmpt from "./SceneEffectCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class MainWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    private mapNode_: cc.Node = null // path://root/map_n
    private cellEffectNode_: cc.Node = null // path://root/map_n/cell_effect_n
    private selectCellNode_: cc.Node = null // path://root/select_cell_n
    private textNode_: cc.Node = null // path://root/text_n
    private ancientTextNode_: cc.Node = null // path://root/ancient_text_n
    private marchLineNode_: cc.Node = null // path://root/march/march_line_n
    private marchRoleNode_: cc.Node = null // path://root/march/march_role_n
    private cellEmojiNode_: cc.Node = null // path://root/cell_emoji_n
    private sceneEffectNode_: cc.Node = null // path://root/scene_effect_n
    private topLayerNode_: cc.Node = null // path://root/top_layer_n
    private weakGuideNode_: cc.Node = null // path://root/weak_guide_n
    //@end

    private readonly INIT_KEY = '_init_main_'

    private diNode: cc.Node = null//装饰
    private mountainNode: cc.Node = null//山脉
    private protectLineNode: cc.Node = null //保护线
    private lineNode: cc.Node = null
    private seawaveNode: cc.Node = null //海浪
    private landNode: cc.Node = null
    private cityNode: cc.Node = null //城市层
    private maskNode: cc.Node = null //遮罩层
    private btinfoNode: cc.Node = null //修建信息层
    private outputNode: cc.Node = null //产出层
    private iconNode: cc.Node = null //小图标层
    private tondenNode: cc.Node = null //屯田中图标层
    private battleNode: cc.Node = null //战斗中图标层
    private mapFlagNode: cc.Node = null //地图标记
    private cellInfoCmpt: CellInfoCmpt = null
    private touchCmpt: MapTouchCmpt = null
    private sceneEffect: SceneEffectCmpt = null
    private cellEmojiItemMap: { [type: number]: cc.Node } = {}
    private seasonType: number = 0

    private model: WorldModel = null
    private user: UserModel = null
    private player: PlayerModel = null
    private centre: cc.Vec2 = cc.v2() //当前的中心位置
    private preCameraZoomRatio: number = 0
    private preCameraPosition: cc.Vec2 = cc.v2()
    private marchs: MarchCmpt[] = [] //当前所有行军
    private tempShowCellMap: { [key: number]: MapCellObj } = {} //当前屏幕显示的地块信息
    private reqSelectArmysing: boolean = false //当前是否请求军队列表中
    private cellEmojiMap: any = {} //当前的领地表情map
    private cityAnimNodePool: MapAnimNodePool = null //城市节点管理
    private seawaveAnimNodePool: MapAnimNodePool = null //海浪节点管理

    private _temp_vec2_1: cc.Vec2 = cc.v2()
    private _temp_vec2_2: cc.Vec2 = cc.v2()
    private _temp_vec2_3: cc.Vec2 = cc.v2()
    private _temp_vec2_4: cc.Vec2 = cc.v2()
    private _temp_vec2_5: cc.Vec2 = cc.v2()

    public listenEventMaps() {
        return [
            { [NetEvent.NET_RECONNECT]: this.onNetReconnect, enter: true },
            { [EventType.UPDATE_CELL_INFO]: this.onUpdateCellInfo, enter: true },
            { [EventType.ADD_MARCH]: this.onAddMarch, enter: true },
            { [EventType.REMOVE_MARCH]: this.onRemoveMarch, enter: true },
            { [EventType.UPDATE_ALL_MARCH]: this.onUpdateAllMarch, enter: true },
            { [EventType.HIDE_WORLD_TEXT]: this.onHideWorldText, enter: true },
            { [EventType.CLOSE_SELECT_CELL]: this.onCloseSelectCell, enter: true },
            { [EventType.UPDATE_BATTLE_DIST_INFO]: this.onUpdateBattleDistInfo, enter: true },
            { [EventType.UPDATE_AVOIDWAR_DIST_INFO]: this.onUpdateAvoidWarDistInfo, enter: true },
            { [EventType.UPDATE_BT_CITY]: this.onUpdateBtCity, enter: true },
            { [EventType.UPDATE_TONDEN]: this.onUpdateTonden, enter: true },
            { [EventType.UPDATE_ARMY_DIST_INFO]: this.onUpdateArmyDistInfo, enter: true },
            { [EventType.MAP_MOVE_TO]: this.onMapMoveTo, enter: true },
            { [EventType.UPDATE_PLAYER_NICKNAME]: this.onUpdatePlayerNickname, enter: true },
            { [EventType.UPDATE_PLAYER_HEAD_ICON]: this.onUpdatePlayerHeadIcon, enter: true },
            { [EventType.UPDATE_ALLI_MAP_FLAG]: this.onUpdateAlliMapFlag, enter: true },
            { [EventType.UPDATE_MARCH_OPACITY]: this.onUpdateMarchOpacity, enter: true },
            { [EventType.PLAY_NEW_CELL_EFFECT]: this.onPlayNewCellEffect, enter: true },
            { [EventType.PLAY_CELL_TONDEN_EFFECT]: this.onPlayCellTondenEffect, enter: true },
            { [EventType.PLAY_CELL_EMOJI]: this.onPlayCellEmoji, enter: true },
            { [EventType.UPDATE_CITY_OUTPUT]: this.onUpdateCityOutput, enter: true },
            { [EventType.CHANGE_SEASON_COMPLETE]: this.onChangeSeasonComplete, enter: true },
            { [EventType.UPDATE_ANCIENT_INFO]: this.onUpdateAncientInfo, enter: true },
            { [EventType.UPDATE_CITY_SKIN]: this.onUpdateCitySkin, enter: true },
            { [EventType.WEAK_GUIDE_SHOW_NODE_CHOOSE]: this.onWeakGuideShowNodeChoose, enter: true },
        ]
    }

    public async onCreate() {
        this.setParam({ isClean: false })
        this.diNode = this.mapNode_.FindChild('di')
        this.mountainNode = this.mapNode_.FindChild('mountain')
        this.protectLineNode = this.mapNode_.FindChild('protect_line')
        this.lineNode = this.mapNode_.FindChild('line')
        this.seawaveNode = this.mapNode_.FindChild('seawave')
        this.landNode = this.mapNode_.FindChild('land')
        this.cityNode = this.mapNode_.FindChild('city')
        this.maskNode = this.mapNode_.FindChild('mask')
        this.btinfoNode = this.mapNode_.FindChild('btinfo')
        this.outputNode = this.mapNode_.FindChild('output')
        this.iconNode = this.mapNode_.FindChild('icon')
        this.tondenNode = this.mapNode_.FindChild('tonden')
        this.battleNode = this.mapNode_.FindChild('battle')
        this.mapFlagNode = this.mapNode_.FindChild('map_flag')
        for (let i = 2; i <= 3; i++) {
            const item = this.cellEmojiItemMap[i] = this.cellEmojiNode_.FindChild('item_' + i)
            item.parent = null
        }
        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt)
        this.cellInfoCmpt = this.FindChild('root/cell_info', CellInfoCmpt)
        this.cityAnimNodePool = new MapAnimNodePool().init(this.cityNode, resHelper.getCityPrefab.bind(resHelper))
        this.model = this.getModel('world')
        this.user = this.getModel('user')
        this.player = this.getModel('player')
        // this.seawaveAnimNodePool = new MapAnimNodePool().init(this.seawaveNode, resHelper.getSeawavePrefab.bind(resHelper))
        // this.seawaveAnimNodePool.setAnimInfo('land_104', 1.76)
        this.selectCellNode_.active = false
        this.cellInfoCmpt.close()
        this.sceneEffect = this.sceneEffectNode_.getComponent(SceneEffectCmpt)
        this.updateSeasonSeceneEffect()
        this.maskNode.children[0].color = cc.Color.WHITE.fromHEX(MAP_MASK_ITEM_COLOR[this.model.getSeasonType()])
    }

    public async onReady() {

    }

    public onEnter(data: any) {
        this.model.initCameraInfo()
        this.cellEffectNode_.Data = true
        this.cellEmojiNode_.Data = true
        this.topLayerNode_.Data = true
        this.checkSeason()
        this.initMarch() //初始化行军
        this.playNewCellEffect()
        this.playCellTondenEffect()
        this.playCellEmoji()
        this.touchCmpt.init(this.onClickMap.bind(this))
        gameHpr.playMainBgm()
        cameraCtrl.setBgColor(CAMERA_BG_COLOR[this.model.getSeasonType()])
    }

    public onLeave() {
        this.model.saveCameraInfo()
        this.touchCmpt.clean()
        this.selectCellNode_.Component(SelectCellCmpt).close()
        this.cellInfoCmpt.close()
        this.reqSelectArmysing = false
        this.cleanMarch()
        this.cellEffectNode_.removeAllChildren()
        this.cellEffectNode_.Data = false
        this.cellEmojiNode_.removeAllChildren()
        this.cellEmojiNode_.Data = false
        this.topLayerNode_.removeAllChildren()
        this.topLayerNode_.Data = false
        // resHelper.cleanNodeChildren(this.diNode) 这里暂时不清理 因为进入其他场景太慢了
        this.cellEmojiMap = {}
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key)
        assetsMgr.releaseTempResByTag(this.key)
        cameraCtrl.setBgColor('#D1F1F3')
    }

    public onClean() {
        for (let k in this.cellEmojiItemMap) {
            this.cellEmojiItemMap[k].destroy()
        }
        this.cellEmojiItemMap = {}
        this.cellEmojiMap = {}
        this.cellInfoCmpt.clean()
        this.sceneEffect?.clean()
        assetsMgr.releaseTempResByTag(this.INIT_KEY)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/cell_info/buttons/enter_be
    onClickEnter(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (cell) {
            this.model.setLookCell(cell)
            viewHelper.gotoWind('area')
            this.hideSelectCell(false)
        }
    }

    // path://root/cell_info/buttons/occupy_be
    onClickOccupy(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (!cell) {
            return
        } else if (!this.model.checkCanOccupyCell(cell)) {
            return viewHelper.showAlert(ecode.ONLY_ATTACK_ADJOIN_CELL)
        } else if (cell.checkAttackByProtect()) { //是否有保护
            return viewHelper.showAlert(ecode.CELL_PROTECT)
        } else if (cell.isAvoidWar()) { //是否金盾
            return viewHelper.showAlert(ecode.AVOID_WAR_NOT_ATTACK)
        }
        this.occupyCell(cell.actIndex)
    }

    // path://root/cell_info/buttons/tonden_be
    onClickTonden(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (!cell || !cell.isOwn()) {
            return
        } else if (gameHpr.isBattleingByIndex(cell.index)) {
            return viewHelper.showAlert('toast.battleing_not_tonden')
        } else if (cell.isBTCitying()) {
            return viewHelper.showAlert('toast.bting_not_tonden')
        }
        this.cellTonden(cell.actIndex)
    }

    // path://root/cell_info/buttons/cancel_tonden_be
    onClickCancelTonden(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (!cell || !cell.isOwn() || !cell.isTondening()) {
            return
        }
        viewHelper.showMessageBox('ui.cancel_tonden_tip', {
            ok: () => this.isActive && this.cancelTonden(cell.actIndex),
            cancel: () => { },
        })
    }

    // path://root/cell_info/buttons/move_be
    onClickMove(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (!cell || !cell.isOneAlliance()) {
            return
        }
        this.moveToCell(cell.actIndex)
    }

    // path://root/cell_info/buttons/build_be
    onClickBuild(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (!cell || !cell.isOwn()) {
            return
        } else if (cell.isTondening()) {
            return viewHelper.showAlert('toast.tondening_not_bt')
        }
        viewHelper.showPnl('main/CityList', cell)
    }

    // path://root/cell_info/buttons/dismantle_be
    onClickDismantle(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (!cell || !cell.isOwn()) {
            return
        }
        viewHelper.showPnl('main/DismantleCityTip', cell)
    }

    // path://root/cell_info/buttons/player_info_be
    onClickPlayerInfo(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        const info = this.model.getPlayerInfo(cell?.owner)
        if (info) {
            viewHelper.showPnl('common/PlayerInfo', info, 'cellinfo')
        }
    }

    // path://root/cell_info/title/share_pos_be
    onClickSharePos(event: cc.Event.EventTouch, data: string) {
        audioMgr.playSFX('click')
        const cell = this.cellInfoCmpt.getCell()
        if (cell) {
            const point = cell.actPoint.Join(',')
            viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_point_to_chat_tip', params: [point] }, (type: number, select: { type: PreferenceKey, channel: string }) => {
                if (this.isValid) {
                    viewHelper.showPnl('common/Chat', { tab: type, text: `[${point}]` }).then(() => this.isValid && this.hideSelectCell(false))
                }
            })
        }
    }

    // path://root/cell_info/buttons/flag_be
    onClickFlag(event: cc.Event.EventTouch, data: string) {
        if (!gameHpr.alliance.isMeMilitary()) {
            return
        }
        const cell = this.cellInfoCmpt.getCell()
        if (cell) {
            viewHelper.showPnl('main/SelectFlagIcon', cell.actIndex)
        }
    }

    // path://root/cell_info/info/score/score_desc_be
    onClickScoreDesc(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (cell) {
            viewHelper.showPnl('main/LandScoreDesc', cell.landLv)
        }
    }

    // path://root/cell_info/info/stamina/stamina_desc_be
    onClickStaminaDesc(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (cell) {
            viewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType)
        }
    }

    // path://root/cell_info/title/cell_emoji_be
    onClickCellEmoji(event: cc.Event.EventTouch, data: string) {
        audioMgr.playSFX('click')
        const cell = this.cellInfoCmpt.getCell()
        if (cell) {
            viewHelper.showPnl('main/CellSelectEmoji', cell.isOwn(), (id: number) => {
                if (id) {
                    gameHpr.ground.sendCellEmoji(id, cell.actIndex)
                }
                if (this.isValid) {
                    this.hideSelectCell(false)
                }
            })
        }
    }

    // path://root/map_n/output/item/city_output_be
    onClickCityOutput(event: cc.Event.EventTouch, data: string) {
        const cell: MapCellObj = event.target.parent.Data
        if (!cell || !cell.isOwn()) {
            return
        }
        const rewards = gameHpr.getPlayerInfo(gameHpr.getUid())?.cityOutputMap[cell.index] || []
        this.model.claimCityOutput(cell.index).then(err => {
            if (err) {
                return viewHelper.showAlert(err)
            }
            // gameHpr.addGainMassage(rewards)
            rewards.forEach((m, i) => animHelper.playFlutterCellOutput(i * 0.4, m, this.topLayerNode_, cell.actPosition, this.key))
        })
    }

    // path://root/cell_info/buttons/ancient_info_be
    onClickAncientInfo(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (cell) {
            const ancient = this.model.getAncientInfo(cell.index)
            if (ancient) {
                const build = new BuildObj().init(cell.index, ut.UID(), cc.v2(0, 0), ancient.id, ancient.lv)
                viewHelper.showPnl('build/BuildAncientBase', build)
            }
        }
    }

    // path://root/cell_info/buttons/city_skin_be
    onClickCitySkin(event: cc.Event.EventTouch, data: string) {
        const cell = this.cellInfoCmpt.getCell()
        if (cell && cell.cityId > 0) {
            viewHelper.showPnl('main/SelectCitySkin', cell, (ok: boolean) => {
                if (this.isValid && ok) {
                    this.hideSelectCell(false)
                }
            })
        }
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onNetReconnect() {
        if (!gameHpr.guide.isOneGuideWorking()) { //这里如果在新手引导 就不要关闭了
            this.selectCellNode_.Component(SelectCellCmpt).close()
            this.cellInfoCmpt.close()
            this.model.initCameraInfo()
        }
        this.updateMap(this.centre) //刷新地图显示
        this.initMarch() //初始化行军
    }

    // 更新地块信息
    private onUpdateCellInfo() {
        this.updateMap(this.centre)
    }

    // 添加行军
    private onAddMarch(data: BaseMarchObj) {
        if (!data.isCanShowMarch()) {
            return this.onRemoveMarch(data)
        }
        let march = this.marchs.find(m => m.uid === data.uid)
        if (march) {
            march.init(data, this.marchRoleNode_, this.key)
            this.checkMarchLineOffset(data)
        } else {
            this.marchLineNode_.AddItem(it => {
                march = this.marchs.add(it.Component(MarchCmpt).init(data, this.marchRoleNode_, this.key))
                this.checkMarchLineOffset(data)
            })
        }
    }

    // 删除行军
    private onRemoveMarch(data: BaseMarchObj) {
        const march = this.marchs.remove('uid', data.uid)
        if (march) {
            march.clean()
            this.checkMarchLineOffset(data)
        }
    }

    // 刷新所有行军
    private onUpdateAllMarch() {
        this.initMarch()
    }

    // 隐藏文本
    private onHideWorldText(val: boolean) {
        val = !val
        this.textNode_.active = val
        this.ancientTextNode_.active = val
        this.btinfoNode.children.forEach(m => m.Data && (m.Child('time').active = val))
        this.tondenNode.children.forEach(m => m.Data && (m.Child('time').active = val))
    }

    // 关闭选择地块
    private onCloseSelectCell(play: boolean) {
        this.hideSelectCell(!!play)
    }

    // 刷新战斗状态
    private onUpdateBattleDistInfo() {
        const cells: MapCellObj[] = [], distMap = this.model.getBattleDistMap()
        for (let index in distMap) {
            const cell = this.tempShowCellMap[index]
            cell && cells.push(cell)
        }
        this.battleNode.Items(cells, (it, data) => it.setPosition(data.actPosition))
    }

    // 刷新免战状态
    private onUpdateAvoidWarDistInfo() {
        this.updateIconNode()
    }

    // 刷新修建信息
    private onUpdateBtCity(index: number) {
        this.updateMap(this.centre)
        if (this.cellInfoCmpt.getCell()?.actIndex === index) {
            this.cellInfoCmpt.updateInfo()
        }
    }

    // 刷新屯田信息
    private onUpdateTonden(index: number) {
        this.updateMap(this.centre)
        if (this.cellInfoCmpt.getCell()?.actIndex === index) {
            this.cellInfoCmpt.updateInfo()
        }
    }

    // 刷新地图上面的军队分布情况  这里主动绘制一次
    private onUpdateArmyDistInfo() {
        this.updateIconNode()
        this.cellInfoCmpt.updateArmyInfo()
    }

    // 移动地图
    private onMapMoveTo(point: cc.Vec2, showCellInfo: boolean) {
        if (this.centre.equals(point)) {
            return showCellInfo && this.showSelectCell(this.model.getMapCellByPoint(point.clone().floor()))
        } else if (!this.tempShowCellMap[mapHelper.pointToIndex(point)]) { //如果没有在当前绘制区域就移动到目标点
            const start = this.centre.sub(point, this._temp_vec2_4).normalizeSelf().mulSelf(2).addSelf(point)
            cameraCtrl.init(mapHelper.getPixelByPoint(start), mapHelper.MAP_SIZE, MAP_SHOW_OFFSET, cameraCtrl.zoomRatio)
            this.updateMap(start.floor())
            this.checkInCameraMarchLine()
        }
        // 移动
        cameraCtrl.moveTo(0.25, mapHelper.getPixelByPoint(point).subSelf(cameraCtrl.getWinSizeHalf())).then(() => {
            if (this.isActive && showCellInfo) {
                this.showSelectCell(this.model.getMapCellByPoint(point.clone().floor()))
            }
        })
    }

    // 刷新玩家昵称
    private onUpdatePlayerNickname(data: PlayerInfo) {
        const it = this.textNode_.children.find(m => m.active && m.Data?.owner === data.uid)
        if (it) {
            this.updatePlayerNickname(it, data)
        }
    }

    // 刷新玩家头像
    private onUpdatePlayerHeadIcon(data: any) {
        const it = this.textNode_.children.find(m => m.active && m.Data?.owner === data.uid)
        if (it) {
            resHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, this.INIT_KEY)
        }
    }

    // 刷新联盟地图标记
    private onUpdateAlliMapFlag() {
        const cells: { cell: MapCellObj, flag: number }[] = [], mapFalg = gameHpr.alliance.getMapFlag()
        for (let index in mapFalg) {
            const cell = this.tempShowCellMap[index]
            cell && cells.push({ cell, flag: mapFalg[index] })
        }
        this.mapFlagNode.Items(cells, (it, data) => {
            it.setPosition(data.cell.actPosition)
            it.Child('root/val', cc.Sprite).spriteFrame = resHelper.getMapFlagNumber(data.flag)
        })
    }

    // 刷新行军线透明度
    private onUpdateMarchOpacity() {
        this.marchs.forEach(m => m.updateOpacity())
    }

    // 播放新的地块效果
    private onPlayNewCellEffect() {
        this.playNewCellEffect()
    }

    // 播放屯田结束效果
    private onPlayCellTondenEffect() {
        this.playCellTondenEffect()
    }

    // 播放地图表情
    private onPlayCellEmoji() {
        this.playCellEmoji()
    }

    // 刷新城市产出
    private onUpdateCityOutput() {
        const cells: { cell: MapCellObj, output: CTypeObj }[] = []
        for (let index in this.tempShowCellMap) {
            const cell = this.tempShowCellMap[index]
            const output = cell.getOutputType()
            if (output) {
                cells.push({ cell, output: output })
            }
        }
        this.outputNode.Items(cells, (it, data) => {
            it.setPosition(data.cell.actPosition)
            it.Child('city_output_be').active = data.cell.isOwn()
            it.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(CTYPE_ICON[data.output.type])
            it.Child('root/val', cc.Label).string = data.output.count > 1 ? data.output.count + '' : ''
        })
    }

    // 改变季节完成
    private onChangeSeasonComplete() {
        this.playChangeSeason(this.model.getSeason().type)
    }

    // 刷新遗迹
    private onUpdateAncientInfo(data: AncientObj) {
        const index = data.index
        const it = this.ancientTextNode_.children.find(m => m.active && m.Data?.index === index)
        if (it) {
            this.updateAncientTextInfo(it, data)
        }
    }

    // 刷新城市皮肤
    private onUpdateCitySkin(index: number) {
        this.updateMap(this.centre)
    }

    // 若引导
    private onWeakGuideShowNodeChoose(data: any) {
        if (data.scene === 'main') {
            guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key)
        }
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    private get isActive() { return this.isValid && this.isEnter() }

    // 点击地图
    private onClickMap(worldLocation: cc.Vec2) {
        const cell = this.model.getMapCellByPoint(mapHelper.getPointByPixel(worldLocation))
        if (cell && !this.selectCellNode_.Data) {
            audioMgr.playSFX('click')
            this.showSelectCell(cell)
            cameraCtrl.redressPositionByRange(cell.actPosition, SELECT_CELL_INFO_BOX)
        } else {
            this.hideSelectCell()
        }
    }

    // 刷新场景特效
    private updateSeasonSeceneEffect() {
        this.sceneEffect.clean()
        const sceneEffectUrl = this.model.getSeason().getCurrSceneEffectUrl()
        if (sceneEffectUrl) { //加载场景特效
            this.sceneEffect.init(sceneEffectUrl, this.INIT_KEY)
        }
    }

    // 绘制地图
    private updateMap(centre: cc.Vec2) {
        this.model.initDecorationUseLand()
        this.preCameraZoomRatio = cameraCtrl.zoomRatio
        this.centre.set(centre)
        this.model.setCentre(centre)
        // 绘制地面
        const armyDistMap = this.player.getArmyDistMap(), battleDist = this.model.getBattleDistMap()
        const mapFlag = gameHpr.alliance.getMapFlag()
        const btCityMap = this.model.getBTCityQueueMap()
        const tondenMap = this.model.getTondenQueueMap()
        let di = 0, linei = 0, li = 0, mi = 0, ii = 0, ti = 0, bi = 0, mfi = 0, oi = 0, mti = 0, pli = 0
        const texts: MapCellObj[] = [], tondens: any[] = [], btCitys: any[] = [], ancientTexts: AncientObj[] = []
        // this.seawaveAnimNodePool?.reset()
        this.cityAnimNodePool.reset()
        this.tempShowCellMap = {}
        const points = mapHelper.getRangePointsByPoint(centre, this.model.getMaxTileRange())
        const seasonType = this.seasonType
        let tempDecorationLoadMap = {}
        for (let i = 0; i < points.length; i++) {
            const point = points[i], cell = this.model.getMapCellByPoint(point)
            const position = cell?.position || mapHelper.getPixelByPoint(point)
            if (cell) {
                const btInfo = btCityMap[cell.index], tondenInfo = tondenMap[cell.index]
                this.tempShowCellMap[cell.index] = cell
                if (cell.cityId > 0) {
                    const animName = cell.cityId === CITY_FORT_NID ? 'city_2102_' + cell.getOwnType() : undefined
                    const city = this.cityAnimNodePool.showNode(cell.getCityViewId(), cell.actPosition, true, animName)
                    if (cell.cityId === CITY_MAIN_NID) {
                        // 这里先获取后面用来显示文本
                        texts.push(cell)
                        // 是否有保护模式 绘制保护线
                        const state = gameHpr.checkPlayerProtectModeState(cell.owner)
                        if (state > 0) {
                            resHelper.getNodeByIndex(this.protectLineNode, pli++, cell.actPosition).opacity = state === 1 ? 255 : 100
                        }
                    } else if (cell.isAncient()) {
                        const info = this.model.getAncientInfo(cell.index)
                        if (info) {
                            ancientTexts.push(info) //遗迹
                            city.Child('val', cc.MultiFrame).setFrame(info.lv === 20)
                        }
                    }
                } else if (cell.cityId < 0) {
                } else if (tondenInfo) { //绘制屯田地
                    tondens.push({ tondenInfo, cell })
                    resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon('land_tonden', seasonType)
                } else if (cell.icon && (!btInfo || btInfo.id === 0)) {
                    resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon(cell.icon, seasonType)
                }
                // 绘制修建信息
                if (btInfo) {
                    btCitys.push({ btInfo, cell })
                    // 只绘制修建 不绘制拆除
                    if (cell.cityId === 0 && btInfo.id > 0) {
                        this.cityAnimNodePool.showNode(btInfo.id, cell.actPosition, false)
                    }
                }
                // 绘制地图军队分布图标
                if (!!armyDistMap[cell.index]) {
                    // 下面是否主城
                    const y = (!cell.isMainCity() && this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))?.isMainCity()) ? -6 : -22
                    const pos = this._temp_vec2_3.set2(-22, y).addSelf(cell.position) //显示到左下角
                    resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('army_min_icon')
                }
                // 绘制免战图标
                if (cell.isCanShowAvoidWar()) {
                    // 下面是否主城
                    const y = (!cell.isMainCity() && this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))?.isMainCity()) ? -6 : -22
                    const pos = this._temp_vec2_3.set2(22, y).addSelf(cell.getRightPosition(this._temp_vec2_5)) //显示到右下角
                    resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('avoidwar_icon_1')
                }
                // 绘制战斗图标
                if (!!battleDist[cell.index]) {
                    resHelper.getNodeByIndex(this.battleNode, bi++, cell.actPosition)
                }
                const flag = mapFlag[cell.index]
                if (flag) { //地图标记
                    resHelper.getNodeByIndex(this.mapFlagNode, mfi++, cell.actPosition).Child('root/val', cc.Sprite).spriteFrame = resHelper.getMapFlagNumber(flag)
                }
                // 绘制产出
                const output = cell.getOutputType()
                if (output) {
                    const oNode = resHelper.getNodeByIndex(this.outputNode, oi++, cell.actPosition)
                    oNode.Data = cell
                    oNode.Child('city_output_be').active = cell.isOwn()
                    oNode.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(CTYPE_ICON[output.type])
                    oNode.Child('root/val', cc.Label).string = output.count > 1 ? output.count + '' : ''
                }
                // 记录边框线
                const borderLines = cell.owner ? cell.borderLines : []
                if (borderLines.length > 0) {
                    const lineItemNode = resHelper.getNodeByIndex(this.lineNode, linei++, position)
                    viewHelper.updateCellBorderLines(lineItemNode, borderLines, cell.getBorderLineColor())
                }
                // 绘制遮罩
                if (!cell.owner) {
                    const maskItemNode = resHelper.getNodeByIndex(this.maskNode, mi++, position)
                    maskItemNode.opacity = cell.getProtectOwner() ? 20 : 38
                }
                //绘制地图装饰
                let decorationCell = cell
                if (!decorationCell.decorationJson && this.model.getDecorationIndex(cell.index)) {
                    decorationCell = this.model.getMapCells()[this.model.getDecorationIndex(cell.index)]
                }
                if (decorationCell.decorationJson && !tempDecorationLoadMap[decorationCell.index]) {
                    tempDecorationLoadMap[decorationCell.index] = true
                    let itemNode: cc.Node = null
                    let iconName = this.model.getDecorationIcon(decorationCell)
                    let frame = resHelper.getLandItemIcon(iconName, seasonType)
                    switch (decorationCell.decorationJson.type) {
                        case DecorationType.MOUNTAIN:
                            itemNode = resHelper.getNodeByIndex(this.mountainNode, mti++, decorationCell.position)
                            if (!decorationCell.mountainAnchor) {
                                //偶数倍数时锚点偏移
                                let size = frame.getOriginalSize()
                                let anchorX = 0.5, anchorY = 0.5
                                if (size.width / TILE_SIZE % 2 == 0) {
                                    anchorX = (size.width / 2 - TILE_SIZE / 2) / size.width
                                }
                                if (size.height / TILE_SIZE % 2 == 0) {
                                    anchorY = (size.height / 2 - TILE_SIZE / 2) / size.height
                                }
                                decorationCell.mountainAnchor = cc.v2(anchorX, anchorY)
                            }
                            itemNode.setAnchorPoint(decorationCell.mountainAnchor)
                            break;
                        default:
                            itemNode = resHelper.getNodeByIndex(this.diNode, di++, decorationCell.position)
                            break;
                    }
                    itemNode.Component(cc.Sprite).spriteFrame = frame
                }

            } else {
                let landId = this.model.getRoundId(point.x, point.y)
                if (landId) {
                    let itemInfo = assetsMgr.getJsonData('land', landId)
                    resHelper.getNodeByIndex(this.diNode, di++, position).Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon(itemInfo.icon, seasonType)
                    resHelper.getNodeByIndex(this.maskNode, mi++, position)
                }
            }
        }
        // 隐藏多余的
        resHelper.hideNodeByIndex(this.diNode, di)
        resHelper.hideNodeByIndex(this.protectLineNode, pli)
        resHelper.hideNodeByIndex(this.lineNode, linei)
        resHelper.hideNodeByIndex(this.landNode, li)
        resHelper.hideNodeByIndex(this.maskNode, mi)
        resHelper.hideNodeByIndex(this.iconNode, ii)
        resHelper.hideNodeByIndex(this.battleNode, bi)
        resHelper.hideNodeByIndex(this.mapFlagNode, mfi)
        resHelper.hideNodeByIndex(this.outputNode, oi)
        resHelper.hideNodeByIndex(this.mountainNode, mti)
        // this.seawaveAnimNodePool?.hideOtherNode()
        this.cityAnimNodePool.hideOtherNode()
        // 当前正在显示的
        const showIndex = this.cellInfoCmpt.getCell()?.actIndex ?? -1
        const zIndexMaxY = mapHelper.MAP_SIZE.y
        const isCanShowText = !this.touchCmpt.isDraging()
        // 绘制文本层
        this.textNode_.Items(texts, (it, data) => {
            const pos = data.actPosition, index = data.actIndex
            const d = it.Data
            const info = gameHpr.getPlayerInfo(data.owner)
            it.setPosition(pos.x, pos.y + 76)
            if (!d || d.owner !== data.owner || d.nickname !== info?.nickname || d.title !== info?.title || d.headIcon !== info?.headIcon) {
                resHelper.loadPlayerHead(it.Child('head', cc.Sprite), info?.headIcon, this.INIT_KEY)
                this.updatePlayerNickname(it, info)
            }
            it.active = showIndex !== index
            it.Data = { index, owner: data.owner, nickname: info?.nickname, title: info?.title, headIcon: info?.headIcon }
        })
        this.textNode_.active = isCanShowText
        // 绘制遗迹文本层
        this.ancientTextNode_.Items(ancientTexts, (it, data) => {
            const pos = data.cell.actPosition, index = data.index
            it.setPosition(pos.x, pos.y + 72)
            this.updateAncientTextInfo(it, data)
            it.active = showIndex !== index
            it.Data = { index }
        })
        this.ancientTextNode_.active = isCanShowText
        // 绘制修建城市的信息
        this.btinfoNode.Items(btCitys, (it, data) => {
            const info: BTCityObj = data.btInfo, index = info.index
            it.setPosition(data.cell.actPosition)
            const timeNode = it.Child('time')
            if (it.Data?.index !== index) {
                const surplusTime = info.getSurplusTime()
                timeNode.Color(info.id ? '#21DC2D' : '#FF9162')
                timeNode.Component(cc.LabelTimer).run(surplusTime * 0.001)
                // 动画
                const anim = it.Child('anim', cc.Animation)
                const elapsedTime = Math.max(0, info.needTime - surplusTime) * 0.001
                const tween = cc.tween(it)
                tween.stop()
                if (elapsedTime < 0.62) {
                    anim.play('cting_begin', elapsedTime)
                    tween.delay(0.62 - elapsedTime).call(() => this.isValid && anim.play('cting_loop')).start()
                } else {
                    anim.play('cting_loop')
                }
            }
            timeNode.active = isCanShowText && showIndex !== index
            it.Data = { index }
            it.zIndex = zIndexMaxY - data.cell.actPoint.y
        })
        // 绘制屯田信息
        this.tondenNode.Items(tondens, (it, data) => {
            const info: TondenObj = data.tondenInfo, index = info.index
            it.setPosition(data.cell.actPosition)
            const timeNode = it.Child('time')
            if (it.Data?.index !== index) {
                timeNode.Component(cc.LabelTimer).run(info.getSurplusTime() * 0.001)
            }
            timeNode.active = isCanShowText
            it.Data = { index }
            it.zIndex = zIndexMaxY - data.cell.actPoint.y
        })
    }

    // 0.上 1.右 2.下 3.左 4.左上 5.右上 6.右下 7.左下
    private getSeaLandIcon(point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {
        if (point.x < minx) {
            return point.y < miny ? 7 : (point.y < maxy ? 3 : 4)
        } else if (point.x < maxx) {
            return point.y < miny ? 2 : 0
        }
        return point.y < miny ? 6 : (point.y < maxy ? 1 : 5)
    }

    private setSeaLand(it: cc.Node, type: string, point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {
        const dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy)
        it.Component(cc.Sprite).spriteFrame = this.getLandIcon(`${type}_${Math.min(dir, 4)}`)
    }

    // 海浪
    private setSeawaveLand(position: cc.Vec2, point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {
        const dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy), no = Math.floor(dir / 4) + 1, angle = dir % 4
        const it = this.seawaveAnimNodePool.showNode(no, position, true)
        it.angle = angle * -90
    }

    private getLandIcon(icon: string) {
        return resHelper.getLandIcon(icon)
    }

    private updatePlayerNickname(it: cc.Node, data: PlayerInfo) {
        const nameLbl = it.Child('name/val', cc.Label)
        nameLbl.string = ut.nameFormator(data?.nickname ?? '???', 7)
        const titleLbl = it.Child('name/title', cc.Label)
        if (titleLbl.setActive(!!data?.title)) {
            const json = assetsMgr.getJsonData('title', data.title)
            titleLbl.Color(json?.color || '#333333').setLocaleKey('titleText.' + json?.id)
            nameLbl.node.y = -10
        } else {
            nameLbl.node.y = 0
        }
    }

    private updateAncientTextInfo(it: cc.Node, data: AncientObj) {
        it.Child('name').setLocaleKey('ui.ancient_name_text', data.name, assetsMgr.lang('ui.short_lv', data.lv || 1))
        if (it.Child('time').active = data.state === 1 && !data.pauseState) {
            it.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001)
        }
    }

    // 刷新显示文本节点
    private updateHideTextByIndex(index: number = -1) {
        this.textNode_.children.forEach(m => m.active = !!m.Data && m.Data.index !== index)
        this.ancientTextNode_.children.forEach(m => m.active = !!m.Data && m.Data.index !== index)
        this.btinfoNode.children.forEach(m => m.Data && (m.Child('time').active = m.Data.index !== index))
    }

    // 刷新图标层
    private updateIconNode() {
        const offset1: cc.Vec2 = cc.v2(-22, -22)
        const offset2: cc.Vec2 = cc.v2(22, -22)
        const cells: any[] = [], armyDistMap = this.player.getArmyDistMap()
        for (let key in this.tempShowCellMap) {
            const cell: MapCellObj = this.tempShowCellMap[key]
            if (cell.isCanShowAvoidWar()) {
                cells.push({ position: cell.getRightPosition(), offset: offset2, icon: 'avoidwar_icon_1' })
            }
            if (armyDistMap[key]) {
                cells.push({ position: cell.position, offset: offset1, icon: 'army_min_icon' })
            }
        }
        this.iconNode.Items(cells, (it, data) => {
            it.setPosition(this._temp_vec2_3.set(data.offset).addSelf(data.position))
            it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon(data.icon)
        })
    }

    // 显示选择地块
    private showSelectCell(cell: MapCellObj) {
        if (!cell || cell.landType == LandType.SEA || cell.landType == LandType.BEACH) {
            return
        } else if (cell.actIndex !== cell.index) {
            cell = this.model.getMapCellByIndex(cell.actIndex)
        }
        const pos = this.selectCellNode_.Data = cell.actPosition
        this.selectCellNode_.Component(SelectCellCmpt).open(pos, cell.getSize())
        this.cellInfoCmpt.open(pos, cell)
        // 隐藏文本节点
        this.updateHideTextByIndex(cell.actIndex)
    }

    // 隐藏
    private hideSelectCell(play: boolean = true) {
        if (this.selectCellNode_.Data) {
            this.selectCellNode_.Component(SelectCellCmpt).close()
            this.cellInfoCmpt.close(play)
            this.updateHideTextByIndex()
        }
    }

    // 初始化行军
    private initMarch() {
        this.cleanMarch()
        const list = this.model.getAllMarchs().filter(m => m.isCanShowMarch())
        this.marchLineNode_.Items(list, (it, data) => {
            const march = this.marchs.add(it.Component(MarchCmpt).init(data, this.marchRoleNode_, this.key))
            march.isCheckLineOffset = false
        })
        this.marchs.forEach(m => !m.isCheckLineOffset && this.checkMarchLineOffset(m.getData()))
    }

    private cleanMarch() {
        while (this.marchs.length > 0) {
            this.marchs.pop().clean()
        }
        this.marchs = []
        // resHelper.cleanNodeChildren(this.marchLineNode_) //这个注释了 不知道什么原因会出现行军线被消耗的情况
        this.marchRoleNode_.removeAllChildren()
    }

    // 检测行军线偏移
    private checkMarchLineOffset(data: BaseMarchObj) {
        const others: MarchCmpt[] = []
        this.marchs.forEach(m => {
            const d = m.getData()
            if (data.checkOtherMarchLine(d)) {
                m.angleOffset = data.startIndex === d.startIndex ? 0 : -180
                m.isCheckLineOffset = true
                others.push(m)
            }
        })
        const len = others.length
        others.forEach((m, i) => m.updateLineOffset(i, len))
    }

    // 攻击地块
    private async occupyCell(index: number) {
        if (this.reqSelectArmysing) {
            return
        }
        this.reqSelectArmysing = true
        const { err, list, canGotoCount } = await this.player.getSelectArmys(index, 2, 0)
        this.reqSelectArmysing = false
        if (!this.isActive) {
            return
        } else if (err === ecode.NOT_IN_OCCUPY_TIME) {
            this.hideSelectCell(false)
            return viewHelper.showMessageBox('ui.not_in_occupy_time_tip')
        } else if (err === ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击
            this.hideSelectCell(false)
            return viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip')
        } else if (err) {
            return viewHelper.showAlert(err)
        } else if (list.length === 0) {
            return viewHelper.showAlert('toast.not_idle_army')
        }
        viewHelper.showPnl('main/SelectArmy', 'occupy', index, list, canGotoCount, (armys: ArmyShortInfo[], isSameSpeed: boolean, autoBackType: number) => {
            if (this.isActive) {
                this.hideSelectCell(false)
                this.model.occupyCell(armys, index, autoBackType, isSameSpeed).then(err => err && viewHelper.showAlert(err))
            }
        })
    }

    // 移动过去屯田
    private async cellTonden(index: number) {
        if (this.reqSelectArmysing) {
            return
        }
        this.reqSelectArmysing = true
        const { err, list, canGotoCount } = await this.player.getSelectArmys(index, 3, 0)
        this.reqSelectArmysing = false
        if (!this.isActive) {
            return
        } else if (err === ecode.BATTLEING) {
            return viewHelper.showAlert('toast.battleing_not_tonden')
        } else if (err) {
            return viewHelper.showAlert(err)
        } else if (list.length === 0) {
            return viewHelper.showAlert('toast.not_idle_army')
        }
        viewHelper.showPnl('main/SelectTondenArmy', index, list, canGotoCount, (army: ArmyShortInfo) => {
            if (this.isActive) {
                this.hideSelectCell(false)
                this.model.cellTonden(army, index).then(err => err && viewHelper.showAlert(err))
            }
        })
    }

    // 取消屯田
    private async cancelTonden(index: number) {
        const err = await this.model.cancelTonden(index)
        if (err) {
            return viewHelper.showAlert(err)
        } else if (this.isActive) {
            this.hideSelectCell(false)
        }
    }

    // 移动到地块
    private async moveToCell(index: number) {
        if (this.reqSelectArmysing) {
            return
        }
        this.reqSelectArmysing = true
        const { err, list, canGotoCount } = await this.player.getSelectArmys(index, 1)
        this.reqSelectArmysing = false
        if (!this.isActive) {
            return
        } else if (err) {
            return viewHelper.showAlert(err)
        } else if (list.length === 0) {
            return viewHelper.showAlert('toast.not_idle_army')
        }
        viewHelper.showPnl('main/SelectArmy', 'move', index, list, canGotoCount, (armys: ArmyShortInfo[], isSameSpeed: boolean) => {
            if (this.isActive) {
                this.hideSelectCell(false)
                this.model.moveCellArmy(armys, index, isSameSpeed).then(err => err && viewHelper.showAlert(err))
            }
        })
    }

    // 播放新的地块效果
    private playNewCellEffect() {
        this.model.getNotPlayNewCells().forEach(index => {
            const cell = this.tempShowCellMap[index]
            if (cell) {
                const json = cell.getResJson() || {}, keys = CELL_RES_FIELDS.filter(m => !!json[m])
                const pos = cell.actPosition, isMore = keys.length > 1
                keys.forEach((key, i) => animHelper.playFlutterCellRes(key, json[key], 0.3 + i * 0.2, isMore, this.topLayerNode_, pos, this.key))
                animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key)
                // 隐藏行军线
                this.marchs.forEach(march => march.isHasIndex(index) && march.hide(1.2))
            }
        })
    }

    // 播放屯田结束的地块效果
    private playCellTondenEffect() {
        this.model.getNotPlayCellTondens().forEach(data => {
            const cell = this.tempShowCellMap[data.index]
            if (cell) {
                const pos = cell.actPosition
                const obj = {}
                data.treasureIds?.forEach(id => {
                    let idObj = obj[id]
                    if (!idObj) {
                        const json = assetsMgr.getJsonData('treasure', id)
                        idObj = obj[id] = { count: 0, icon: 'treasure_' + (json?.lv || 1) + '_0' }
                    }
                    idObj.count += 1
                })
                for (let key in obj) {
                    const data = obj[key]
                    animHelper.playFlutterTreasure(data.icon, data.count, this.topLayerNode_, pos, this.key)
                }
                animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key)
            }
        })
    }

    // 播放地图表情
    private playCellEmoji() {
        const now = Date.now()
        gameHpr.ground.getCellEmojis().forEach(m => {
            const cell = this.tempShowCellMap[m.index], item = this.cellEmojiItemMap[Math.floor(m.emoji / 1000)]
            if (cell && item) {
                let node: cc.Node = this.cellEmojiMap[m.index]
                if (node?.isValid) {
                    node.Child('root').children.forEach(it => nodePoolMgr.put(it))
                    node.destroy()
                }
                const startTime = Math.max(0, (now - m.getTime) * 0.001)
                node = this.cellEmojiMap[m.index] = cc.instantiate2(item, this.cellEmojiNode_)
                node.Data = m.index
                node.setPosition(cell.actPosition)
                node.zIndex = cell.point.y
                animHelper.playCellEmoji(node, m.emoji, m.uid, startTime, this.key).then(n => {
                    if (this.isValid && n?.isValid) {
                        delete this.cellEmojiMap[n.Data]
                        n.Child('root').children.forEach(it => nodePoolMgr.put(it))
                        n.destroy()
                    }
                })
            }
        })
    }

    private testPlayNewCell(x: number, y: number, keys: string[], delay: number) {
        // for (let i = 0; i < 5; i++) {
        //     const position = mapHelper.getPixelByPoint(cc.v2(x + i, y)).clone()
        //     animHelper.playFlutterCellRes('stone', 30, this.topLayerNode_, position, this.key)
        //     animHelper.playNewCellEffect(this.cellEffectNode_, position, this.key)
        // }
        const pos = mapHelper.getPixelByPoint(cc.v2(x, y)).clone(), isMore = keys.length > 1
        keys.forEach((key, i) => animHelper.playFlutterCellRes(key, 1, 0.3 + i * delay, isMore, this.topLayerNode_, pos, this.key))
        animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key)
    }

    // 检测季节
    private checkSeason() {
        let oldType = this.user.getLocalPreferenceDataBySid(PreferenceKey.LAST_PLAY_SEASON_TYPE) ?? -1
        this.seasonType = cc.misc.clampf(oldType, 0, 3)
        if (resHelper.checkLandSkin(this.seasonType)) {
            this.updateMap(this.model.getCentre()) //刷新地图显示
        } else {
            resHelper.initLandSkin(this.seasonType).then(() => this.isValid && this.updateMap(this.model.getCentre()))
        }
        // 弹界面
        const curType = this.model.getSeason().type
        if (oldType !== curType) {
            popupPnlHelper.add({ key: 'main/SeasonSwitch' })
        }
    }

    // 播放切换季节
    private async playChangeSeason(type: number) {
        this.seasonType = type
        this.user.setLocalPreferenceDataBySid(PreferenceKey.LAST_PLAY_SEASON_TYPE, type)
        this.hideSelectCell(false)
        await resHelper.initLandSkin(type)
        this.updateSeasonSeceneEffect()
        this.updateMap(this.centre) //刷新地图显示
        resHelper.cleanLandSkin()
    }

    update(dt: number) {
        //
        this.seawaveAnimNodePool?.update(dt)
        // 检测是否需要填充地图
        this.checkUpdateMap()
        // 检测是否在相机范围
        this.checkInCameraRange()
    }

    private checkUpdateMap() {
        const point = mapHelper.getPointByPixel(cameraCtrl.getCentrePosition(), this._temp_vec2_1)
        let size = Math.max(Math.abs(point.x - this.centre.x), Math.abs(point.y - this.centre.y))
        if (size >= MAP_EXTRA_SIZE / 2 || this.preCameraZoomRatio !== cameraCtrl.zoomRatio) {
            this.updateMap(point)
            this.checkInCameraMarchLine()
        }
    }

    // 检测只会在在相机范围内的行军线
    private checkInCameraMarchLine() {
        const uidMap = {}
        this.marchs.forEach(m => {
            m.checkUpdateInCamera()
            uidMap[m.uid] = true
        })
        // 兼容检测是否有多余的行军角色
        for (let i = this.marchRoleNode_.childrenCount - 1; i >= 0; i--) {
            const node = this.marchRoleNode_.children[i]
            if (!uidMap[node.Data?.uid]) {
                node.destroy()
            }
        }
    }

    private checkInCameraRange() {
        const position = cameraCtrl.getPosition()
        if (this.preCameraPosition.equals(position)) {
            return
        }
        this.preCameraPosition.set(position)
        // 选择地块框
        if (this.cellInfoCmpt?.checkNotInScreenRange()) {
            this.hideSelectCell(false)
        }
    }
}
