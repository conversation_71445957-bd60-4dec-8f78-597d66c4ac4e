import { MailInfo } from "../../common/constant/DataType";
import { CType, MailStateType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import CTypeObj from "../../model/common/CTypeObj";
import LabelAutoAnyCmpt from "../cmpt/LabelAutoAnyCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class MailInfoPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private titleNode_: cc.Node = null // path://root/title_n
    private senderNode_: cc.Node = null // path://root/sender_n
    private timeLbl_: cc.Label = null // path://root/time_l
    private contentSv_: cc.ScrollView = null // path://root/content_sv
    private listSv_: cc.ScrollView = null // path://root/list_sv
    private buttonsNode_: cc.Node = null // path://root/buttons_n
    private autoRemoveNode_: cc.Node = null // path://root/auto_remove_n
    //@end

    private data: MailInfo = null
    private readonly ITEM_ADAPT_SIZE: cc.Size = cc.size(64, 64)

    public listenEventMaps() {
        return [
            { [EventType.TRANSLATE_TEXT_COMPLETE]: this.onTranslateTextComplete, enter: true },
        ]
    }

    public async onCreate() {
    }

    public onEnter(data: MailInfo) {
        this.data = data
        this.checkRead()
        this.updateContent(data)
        this.buttonsNode_.Child('reply_be').active = !!data.sender && data.sender !== '-1' //不能回复系统邮件
    }

    public onRemove() {
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/buttons_n/remove_be
    onClickRemove(event: cc.Event.EventTouch, _: string) {
        if (!this.data) {
            return
        } else if (this.data.items?.length > 0 && this.data.state !== MailStateType.READ) {
            viewHelper.showMessageBox('ui.has_not_claim_mail_item', {
                ok: () => this.removeMail(),
                cancel: () => { },
            })
        } else {
            this.removeMail()
        }
    }

    // path://root/buttons_n/claim_be
    onClickClaim(event: cc.Event.EventTouch, _: string) {
        if (!this.data || !this.data.items || this.data.items.length === 0) {
            return
        }
        const data = this.data
        const arr: CTypeObj[] = [], indexs: number[] = []
        data.items.forEach((m, i) => {
            if (!data.oneClaims?.has(i)) {
                arr.push(m)
                indexs.push(i)
            }
        })
        if (arr.length === 0) {
            return
        }
        const items = gameHpr.checkRewardFull(arr)
        if (items.length > 0) {
            return viewHelper.showPnl('common/ResFullTip', items, (ok: boolean) => { ok && this.claimReward(data) })
        } else if (arr.length === 1) {
            return this.claimRewardOne(data, indexs[0])
        } else if (arr.some(m => m.type === CType.HERO_OPT)) {
            return viewHelper.showMessageBox('ui.claim_hero_opt_tip', {
                ok: () => this.claimReward(data),
                cancel: () => { },
            })
        }
        this.claimReward(data)
    }

    // path://root/buttons_n/reply_be
    onClickReply(event: cc.Event.EventTouch, data: string) {
        this.hide()
        viewHelper.showPnl('menu/WriteMail', this.data)
    }

    // path://root/list_sv/view/content/item_be
    onClickItem(event: cc.Event.EventTouch, _: string) {
        if (!this.data || !this.data.items || this.data.items.length === 0) {
            return
        }
        const data = this.data, index = event.target.Data
        const item = data.items[index]
        if (!item) {
            return
        }
        const items = gameHpr.checkRewardFull([item])
        if (items.length > 0) {
            return viewHelper.showPnl('common/ResFullTip', items, (ok: boolean) => { ok && this.claimRewardOne(data, index) })
        }
        this.claimRewardOne(data, index)
    }

    // path://root/content_sv/view/content/translate_be
    onClickTranslate(event: cc.Event.EventTouch, data: string) {
        gameHpr.translateText(this.data, 'mail')
        this.updateContentText(this.data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // 翻译完成
    private onTranslateTextComplete(type: string, data: MailInfo) {
        if (type !== 'mail' || data.uid !== this.data.uid) {
            return
        }
        this.updateContentText(data)
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    // 检测是否已读
    private checkRead() {
        const data = this.data
        if (data.state === MailStateType.NONE) {
            data.state = (data.items && data.items.length > 0) ? MailStateType.NOT_CLAIM : MailStateType.READ
            gameHpr.net.send('mail/HD_ReadMail', { uid: data.uid }) //标记已读
            this.emit(EventType.UPDATE_MAIL_STATE, data)
        }
    }

    private updateContent(data: MailInfo) {
        this.titleNode_.Child('val', cc.Label).string = data.title
        const isSys = data.sender === '-1'
        this.senderNode_.Child('val', cc.Label).Color(isSys ? '#BE772B' : '#756963').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8)
        this.timeLbl_.string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime)
        // 内容
        this.updateContentText(data)
        // 道具
        const items = data.items || []
        const claimBtn = this.buttonsNode_.Child('claim_be', cc.Button)
        const hasItem = claimBtn.setActive(items.length > 0)
        if (this.listSv_.setActive(hasItem)) {
            this.updateClaimButton(claimBtn, data.state)
            this.updateItems()
        }
        // 刷新内容高度
        this.contentSv_.node.height = hasItem ? 280 : 420
        this.contentSv_.scrollToTop()
        //
        if (this.autoRemoveNode_.active = !!data.autoDelSurplusTime) {
            const autoDelTime = Math.max(0, data.autoDelSurplusTime - (Date.now() - data.getTime))
            this.autoRemoveNode_.setLocaleKey('ui.auto_remove_mail_desc', gameHpr.millisecondToStringForDay(autoDelTime))
        }
    }

    private updateContentText(data: MailInfo) {
        // 内容
        const content = this.contentSv_.content, contentLbl = content.Child('val', cc.Label)
        contentLbl.string = data.content
        // 翻译
        content.Child('translate_be').active = !data.translate && !data.contentId && gameHpr.isGLobal()
        const lineNode = content.Child('line'), translateLoading = content.Child('loading'), translateLbl = content.Child('translate', cc.Label)
        lineNode.active = !!data.translate
        translateLoading.active = !!data.translate?.req
        if (translateLbl.setActive(!!data.translate?.text)) {
            translateLbl.string = data.translate.text
            translateLbl.Component(LabelAutoAnyCmpt).check()
            lineNode.width = translateLbl.node.width * 0.5
        } else if (lineNode.active) {
            contentLbl._forceUpdateRenderData()
            lineNode.width = contentLbl.node.width * 0.5
        }
    }

    private updateClaimButton(button: cc.Button, state: MailStateType) {
        button.interactable = state !== MailStateType.READ
        button.Child('val').setLocaleKey(state === MailStateType.READ ? 'ui.yet_take' : 'ui.button_one_take')
    }

    private updateItems() {
        const data = this.data, claims = data.oneClaims || [], isClaim = data.state === MailStateType.READ
        const len = data.items.length
        this.listSv_.Items(data.items, (it, item, i) => {
            it.Data = i
            viewHelper.updateItemByCTypeOne(it, item, this.key, this.ITEM_ADAPT_SIZE)
            const claim = it.Child('claim').active = isClaim || claims.has(i)
            it.Child('icon').opacity = claim ? 120 : 255
            it.Component(cc.Button).interactable = !claim
        })
        if (len <= 4) {
            this.listSv_.node.width = Math.max(80, len * 80 + (len - 1) * 40) + 28 + 4
            this.listSv_.node.x = 2
        } else {
            this.listSv_.node.width = 528
            this.listSv_.node.x = 0
        }
        this.listSv_.node.children.forEach(m => m.Component(cc.Widget)?.updateAlignment())
    }

    // 领取单个物品
    private claimRewardOne(data: MailInfo, index: number) {
        const item = data.items?.[index]
        if (!item) {
            return
        } else if (item.type === CType.HERO_OPT) {
            viewHelper.showHeroOptSelect(item.id).then(id => (this.isValid && id > 0) && this.claimRewardOneDo(data, index, id))
        } else {
            this.claimRewardOneDo(data, index, 0)
        }
    }
    private claimRewardOneDo(data: MailInfo, index: number, heroId: number) {
        gameHpr.net.request('mail/HD_ClaimMailItemOne', { uid: data.uid, index, heroId }, true).then(res => {
            if (res.err) {
                return viewHelper.showAlert(res.err)
            }
            viewHelper.showAlert('toast.take_succeed')
            gameHpr.addGainMassage(data.items[index])
            gameHpr.player.updateRewardItemsByFlags(res.data.rewards)
            if (data.oneClaims) {
                data.oneClaims.push(index)
            } else {
                data.oneClaims = [index]
            }
            if (data.oneClaims.length === data.items.length) {
                data.state = MailStateType.READ
                this.emit(EventType.UPDATE_MAIL_STATE, data)
            }
            if (this.isValid) {
                this.updateClaimButton(this.buttonsNode_.Child('claim_be', cc.Button), data.state)
                this.updateItems()
            }
        })
    }

    // 领取奖励
    private claimReward(data: MailInfo) {
        gameHpr.net.request('mail/HD_ClaimMailItem', { uid: data.uid }, true).then(res => {
            if (res.err) {
                return viewHelper.showAlert(res.err)
            }
            viewHelper.showAlert('toast.take_succeed')
            gameHpr.addGainMassage(data.items)
            gameHpr.player.updateRewardItemsByFlags(res.data.rewards)
            data.state = MailStateType.READ
            this.emit(EventType.UPDATE_MAIL_STATE, data)
            if (this.isValid) {
                this.updateClaimButton(this.buttonsNode_.Child('claim_be', cc.Button), data.state)
                this.updateItems()
            }
        })
    }

    // 删除邮件
    private removeMail() {
        gameHpr.user.removeMail(this.data.uid).then(err => {
            if (err) {
                return viewHelper.showAlert(err)
            } else if (this.isValid) {
                this.hide()
            }
        })
    }
}
