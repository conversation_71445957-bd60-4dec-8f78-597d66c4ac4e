import { MAIL_STATE_COLOR } from "../../common/constant/Constant";
import { MailInfo } from "../../common/constant/DataType";
import { MailStateType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { reddotHelper } from "../../common/helper/ReddotHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import UserModel from "../../model/common/UserModel";

const { ccclass } = cc._decorator;

@ccclass
export default class MailListPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private ttileLbl_: cc.Label = null // path://root/title/ttile_l
    private listSv_: cc.ScrollView = null // path://root/list_sv
    private loadingNode_: cc.Node = null // path://root/loading_n
    private delReadBtn_: cc.Button = null // path://root/buttons/del_read_be_b
    private writeNode_: cc.Node = null // path://root/buttons/write_be_n
    //@end

    private model: UserModel = null

    public listenEventMaps() {
        return [
            { [EventType.REMOVE_MAIL]: this.onRemoveMail, enter: true },
            { [EventType.UPDATE_MAIL_STATE]: this.onUpdateMailState, enter: true },
        ]
    }

    public async onCreate() {
        this.model = this.getModel('user')
        this.ttileLbl_.setLocaleKey('ui.title_mail_list', 0)
    }

    public onEnter() {
        this.listSv_.content.Swih('')
        this.loadingNode_.active = true
        this.delReadBtn_.interactable = false
        this.listSv_.Child('empty').active = false
        this.model.getMails().then(list => {
            if (!this.isValid || !this.isEnter()) {
                return
            }
            reddotHelper.set('new_mail', false)
            this.loadingNode_.active = false
            this.updateList(list)
        })
        this.writeNode_.active = gameHpr.alliance.isCanSendMail()
    }

    public onRemove() {
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/list_sv/view/content/item_be
    onClickItem(event: cc.Event.EventTouch, _: string) {
        audioMgr.playSFX('click')
        const data: MailInfo = event.target.Data
        if (data) {
            viewHelper.showPnl('menu/MailInfo', data)
        }
    }

    // path://root/buttons/write_be_n
    onClickWrite(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('menu/WriteMail')
    }

    // path://root/buttons/del_read_be_b
    onClickDelRead(event: cc.Event.EventTouch, data: string) {
        this.model.delAllReadMail().then(err => {
            if (err) {
                return viewHelper.showAlert(err)
            } else if (this.isValid) {
                this.updateList(this.model.getTempMails())
            }
        })
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // 删除邮件
    private onRemoveMail(uid: string) {
        this.updateList(this.model.getTempMails())
    }

    // 刷新邮件状态
    private onUpdateMailState(data: MailInfo) {
        const it = this.listSv_.content.children.find(m => m.Data?.uid === data.uid)
        if (it) {
            this.updateState(it, data.state)
        }
        if (data.state === MailStateType.READ) {
            this.delReadBtn_.interactable = true
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private updateList(mails: MailInfo[]) {
        const list = mails.sort((a, b) => a.state === b.state ? b.createTime - a.createTime : a.state - b.state), len = list.length
        this.delReadBtn_.interactable = mails.some(m => m.state === MailStateType.READ)
        this.ttileLbl_.setLocaleKey('ui.title_mail_list', len)
        this.listSv_.Child('empty').active = len === 0
        this.listSv_.stopAutoScroll()
        this.listSv_.content.y = 0
        this.listSv_.List(len, (it, i) => {
            const data = it.Data = list[i]
            this.updateState(it, data.state)
            const isReaded = data.state === MailStateType.READ // 已读状态全部换色 
            it.Child('title', cc.Label).Color(isReaded ? '#A18876' : '#3F332F').string = data.title
            it.Child('sender/name').Color(isReaded ? '#A18876' : '#625450')
            const isSys = data.sender === '-1'
            it.Child('sender/val', cc.Label).Color(isReaded ? '#A18876' : isSys ? '#BE772B' : '#936E5A').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8)
            it.Child('time', cc.Label).Color(isReaded ? '#A18876' : '#625450').string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime)
        })
    }

    private updateState(it: cc.Node, state: number) {
        it.Child('state').Color(MAIL_STATE_COLOR[state]).setLocaleKey('ui.mail_state_' + state)
    }
}
