"use strict";
cc._RF.push(module, '03f52oPlWdKPbzt6ww8C/z3', 'MailInfoPnlCtrl');
// app/script/view/menu/MailInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var LabelAutoAnyCmpt_1 = require("../cmpt/LabelAutoAnyCmpt");
var ccclass = cc._decorator.ccclass;
var MailInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(MailInfoPnlCtrl, _super);
    function MailInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleNode_ = null; // path://root/title_n
        _this.senderNode_ = null; // path://root/sender_n
        _this.timeLbl_ = null; // path://root/time_l
        _this.contentSv_ = null; // path://root/content_sv
        _this.listSv_ = null; // path://root/list_sv
        _this.buttonsNode_ = null; // path://root/buttons_n
        _this.autoRemoveNode_ = null; // path://root/auto_remove_n
        //@end
        _this.data = null;
        _this.ITEM_ADAPT_SIZE = cc.size(64, 64);
        return _this;
    }
    MailInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.TRANSLATE_TEXT_COMPLETE] = this.onTranslateTextComplete, _a.enter = true, _a),
        ];
    };
    MailInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MailInfoPnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        this.checkRead();
        this.updateContent(data);
        this.buttonsNode_.Child('reply_be').active = !!data.sender && data.sender !== '-1'; //不能回复系统邮件
    };
    MailInfoPnlCtrl.prototype.onRemove = function () {
    };
    MailInfoPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/remove_be
    MailInfoPnlCtrl.prototype.onClickRemove = function (event, _) {
        var _this = this;
        var _a;
        if (!this.data) {
            return;
        }
        else if (((_a = this.data.items) === null || _a === void 0 ? void 0 : _a.length) > 0 && this.data.state !== Enums_1.MailStateType.READ) {
            ViewHelper_1.viewHelper.showMessageBox('ui.has_not_claim_mail_item', {
                ok: function () { return _this.removeMail(); },
                cancel: function () { },
            });
        }
        else {
            this.removeMail();
        }
    };
    // path://root/buttons_n/claim_be
    MailInfoPnlCtrl.prototype.onClickClaim = function (event, _) {
        var _this = this;
        if (!this.data || !this.data.items || this.data.items.length === 0) {
            return;
        }
        var data = this.data;
        var arr = [], indexs = [];
        data.items.forEach(function (m, i) {
            var _a;
            if (!((_a = data.oneClaims) === null || _a === void 0 ? void 0 : _a.has(i))) {
                arr.push(m);
                indexs.push(i);
            }
        });
        if (arr.length === 0) {
            return;
        }
        var items = GameHelper_1.gameHpr.checkRewardFull(arr);
        if (items.length > 0) {
            return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.claimReward(data); });
        }
        else if (arr.length === 1) {
            return this.claimRewardOne(data, indexs[0]);
        }
        else if (arr.some(function (m) { return m.type === Enums_1.CType.HERO_OPT; })) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.claim_hero_opt_tip', {
                ok: function () { return _this.claimReward(data); },
                cancel: function () { },
            });
        }
        this.claimReward(data);
    };
    // path://root/buttons_n/reply_be
    MailInfoPnlCtrl.prototype.onClickReply = function (event, data) {
        this.hide();
        ViewHelper_1.viewHelper.showPnl('menu/WriteMail', this.data);
    };
    // path://root/list_sv/view/content/item_be
    MailInfoPnlCtrl.prototype.onClickItem = function (event, _) {
        var _this = this;
        if (!this.data || !this.data.items || this.data.items.length === 0) {
            return;
        }
        var data = this.data, index = event.target.Data;
        var item = data.items[index];
        if (!item) {
            return;
        }
        var items = GameHelper_1.gameHpr.checkRewardFull([item]);
        if (items.length > 0) {
            return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.claimRewardOne(data, index); });
        }
        this.claimRewardOne(data, index);
    };
    // path://root/content_sv/view/content/translate_be
    MailInfoPnlCtrl.prototype.onClickTranslate = function (event, data) {
        GameHelper_1.gameHpr.translateText(this.data, 'mail');
        this.updateContentText(this.data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 翻译完成
    MailInfoPnlCtrl.prototype.onTranslateTextComplete = function (type, data) {
        if (type !== 'mail' || data.uid !== this.data.uid) {
            return;
        }
        this.updateContentText(data);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 检测是否已读
    MailInfoPnlCtrl.prototype.checkRead = function () {
        var data = this.data;
        if (data.state === Enums_1.MailStateType.NONE) {
            data.state = (data.items && data.items.length > 0) ? Enums_1.MailStateType.NOT_CLAIM : Enums_1.MailStateType.READ;
            GameHelper_1.gameHpr.net.send('mail/HD_ReadMail', { uid: data.uid }); //标记已读
            this.emit(EventType_1.default.UPDATE_MAIL_STATE, data);
        }
    };
    MailInfoPnlCtrl.prototype.updateContent = function (data) {
        this.titleNode_.Child('val', cc.Label).string = data.title;
        var isSys = data.sender === '-1';
        this.senderNode_.Child('val', cc.Label).Color(isSys ? '#BE772B' : '#756963').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8);
        this.timeLbl_.string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime);
        // 内容
        this.updateContentText(data);
        // 道具
        var items = data.items || [];
        var claimBtn = this.buttonsNode_.Child('claim_be', cc.Button);
        var hasItem = claimBtn.setActive(items.length > 0);
        if (this.listSv_.setActive(hasItem)) {
            this.updateClaimButton(claimBtn, data.state);
            this.updateItems();
        }
        // 刷新内容高度
        this.contentSv_.node.height = hasItem ? 280 : 420;
        this.contentSv_.scrollToTop();
        //
        if (this.autoRemoveNode_.active = !!data.autoDelSurplusTime) {
            var autoDelTime = Math.max(0, data.autoDelSurplusTime - (Date.now() - data.getTime));
            this.autoRemoveNode_.setLocaleKey('ui.auto_remove_mail_desc', GameHelper_1.gameHpr.millisecondToStringForDay(autoDelTime));
        }
    };
    MailInfoPnlCtrl.prototype.updateContentText = function (data) {
        var _a, _b;
        // 内容
        var content = this.contentSv_.content, contentLbl = content.Child('val', cc.Label);
        contentLbl.string = data.content;
        // 翻译
        content.Child('translate_be').active = !data.translate && !data.contentId && GameHelper_1.gameHpr.isGLobal();
        var lineNode = content.Child('line'), translateLoading = content.Child('loading'), translateLbl = content.Child('translate', cc.Label);
        lineNode.active = !!data.translate;
        translateLoading.active = !!((_a = data.translate) === null || _a === void 0 ? void 0 : _a.req);
        if (translateLbl.setActive(!!((_b = data.translate) === null || _b === void 0 ? void 0 : _b.text))) {
            translateLbl.string = data.translate.text;
            translateLbl.Component(LabelAutoAnyCmpt_1.default).check();
            lineNode.width = translateLbl.node.width * 0.5;
        }
        else if (lineNode.active) {
            contentLbl._forceUpdateRenderData();
            lineNode.width = contentLbl.node.width * 0.5;
        }
    };
    MailInfoPnlCtrl.prototype.updateClaimButton = function (button, state) {
        button.interactable = state !== Enums_1.MailStateType.READ;
        button.Child('val').setLocaleKey(state === Enums_1.MailStateType.READ ? 'ui.yet_take' : 'ui.button_one_take');
    };
    MailInfoPnlCtrl.prototype.updateItems = function () {
        var _this = this;
        var data = this.data, claims = data.oneClaims || [], isClaim = data.state === Enums_1.MailStateType.READ;
        var len = data.items.length;
        this.listSv_.Items(data.items, function (it, item, i) {
            it.Data = i;
            ViewHelper_1.viewHelper.updateItemByCTypeOne(it, item, _this.key, _this.ITEM_ADAPT_SIZE);
            var claim = it.Child('claim').active = isClaim || claims.has(i);
            it.Child('icon').opacity = claim ? 120 : 255;
            it.Component(cc.Button).interactable = !claim;
        });
        if (len <= 4) {
            this.listSv_.node.width = Math.max(80, len * 80 + (len - 1) * 40) + 28 + 4;
            this.listSv_.node.x = 2;
        }
        else {
            this.listSv_.node.width = 528;
            this.listSv_.node.x = 0;
        }
        this.listSv_.node.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
    };
    // 领取单个物品
    MailInfoPnlCtrl.prototype.claimRewardOne = function (data, index) {
        var _this = this;
        var _a;
        var item = (_a = data.items) === null || _a === void 0 ? void 0 : _a[index];
        if (!item) {
            return;
        }
        else if (item.type === Enums_1.CType.HERO_OPT) {
            ViewHelper_1.viewHelper.showHeroOptSelect(item.id).then(function (id) { return (_this.isValid && id > 0) && _this.claimRewardOneDo(data, index, id); });
        }
        else {
            this.claimRewardOneDo(data, index, 0);
        }
    };
    MailInfoPnlCtrl.prototype.claimRewardOneDo = function (data, index, heroId) {
        var _this = this;
        GameHelper_1.gameHpr.net.request('mail/HD_ClaimMailItemOne', { uid: data.uid, index: index, heroId: heroId }, true).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            ViewHelper_1.viewHelper.showAlert('toast.take_succeed');
            GameHelper_1.gameHpr.addGainMassage(data.items[index]);
            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(res.data.rewards);
            if (data.oneClaims) {
                data.oneClaims.push(index);
            }
            else {
                data.oneClaims = [index];
            }
            if (data.oneClaims.length === data.items.length) {
                data.state = Enums_1.MailStateType.READ;
                _this.emit(EventType_1.default.UPDATE_MAIL_STATE, data);
            }
            if (_this.isValid) {
                _this.updateClaimButton(_this.buttonsNode_.Child('claim_be', cc.Button), data.state);
                _this.updateItems();
            }
        });
    };
    // 领取奖励
    MailInfoPnlCtrl.prototype.claimReward = function (data) {
        var _this = this;
        GameHelper_1.gameHpr.net.request('mail/HD_ClaimMailItem', { uid: data.uid }, true).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            ViewHelper_1.viewHelper.showAlert('toast.take_succeed');
            GameHelper_1.gameHpr.addGainMassage(data.items);
            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(res.data.rewards);
            data.state = Enums_1.MailStateType.READ;
            _this.emit(EventType_1.default.UPDATE_MAIL_STATE, data);
            if (_this.isValid) {
                _this.updateClaimButton(_this.buttonsNode_.Child('claim_be', cc.Button), data.state);
                _this.updateItems();
            }
        });
    };
    // 删除邮件
    MailInfoPnlCtrl.prototype.removeMail = function () {
        var _this = this;
        GameHelper_1.gameHpr.user.removeMail(this.data.uid).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.hide();
            }
        });
    };
    MailInfoPnlCtrl = __decorate([
        ccclass
    ], MailInfoPnlCtrl);
    return MailInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MailInfoPnlCtrl;

cc._RF.pop();