{"version": 3, "sources": ["assets\\app\\script\\view\\menu\\MailInfoPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,qDAAmE;AACnE,0DAAqD;AACrD,6DAAyD;AACzD,6DAA4D;AAE5D,6DAAwD;AAEhD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA6C,mCAAc;IAA3D;QAAA,qEA0QC;QAxQG,0BAA0B;QAClB,gBAAU,GAAY,IAAI,CAAA,CAAC,sBAAsB;QACjD,iBAAW,GAAY,IAAI,CAAA,CAAC,uBAAuB;QACnD,cAAQ,GAAa,IAAI,CAAA,CAAC,qBAAqB;QAC/C,gBAAU,GAAkB,IAAI,CAAA,CAAC,yBAAyB;QAC1D,aAAO,GAAkB,IAAI,CAAA,CAAC,sBAAsB;QACpD,kBAAY,GAAY,IAAI,CAAA,CAAC,wBAAwB;QACrD,qBAAe,GAAY,IAAI,CAAA,CAAC,4BAA4B;QACpE,MAAM;QAEE,UAAI,GAAa,IAAI,CAAA;QACZ,qBAAe,GAAY,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;;IA6P/D,CAAC;IA3PU,yCAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,uBAAuB,EAAE,QAAK,GAAE,IAAI;SACnF,CAAA;IACL,CAAC;IAEY,kCAAQ,GAArB;;;;;;KACC;IAEM,iCAAO,GAAd,UAAe,IAAc;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QACxB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAA,CAAC,UAAU;IACjG,CAAC;IAEM,kCAAQ,GAAf;IACA,CAAC;IAEM,iCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,kCAAkC;IAClC,uCAAa,GAAb,UAAc,KAA0B,EAAE,CAAS;QAAnD,iBAWC;;QAVG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAM;SACT;aAAM,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,KAAK,0CAAE,MAAM,IAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,qBAAa,CAAC,IAAI,EAAE;YAC9E,uBAAU,CAAC,cAAc,CAAC,4BAA4B,EAAE;gBACpD,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,UAAU,EAAE,EAAjB,CAAiB;gBAC3B,MAAM,EAAE,cAAQ,CAAC;aACpB,CAAC,CAAA;SACL;aAAM;YACH,IAAI,CAAC,UAAU,EAAE,CAAA;SACpB;IACL,CAAC;IAED,iCAAiC;IACjC,sCAAY,GAAZ,UAAa,KAA0B,EAAE,CAAS;QAAlD,iBA2BC;QA1BG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAChE,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAM,GAAG,GAAe,EAAE,EAAE,MAAM,GAAa,EAAE,CAAA;QACjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;;YACpB,IAAI,QAAC,IAAI,CAAC,SAAS,0CAAE,GAAG,CAAC,CAAC,EAAC,EAAE;gBACzB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACX,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACjB;QACL,CAAC,CAAC,CAAA;QACF,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YAClB,OAAM;SACT;QACD,IAAM,KAAK,GAAG,oBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;QAC1C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAClB,OAAO,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK,EAAE,UAAC,EAAW,IAAO,EAAE,IAAI,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,CAAC,CAAC,CAAC,CAAA;SAC3G;aAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;SAC9C;aAAM,IAAI,GAAG,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAzB,CAAyB,CAAC,EAAE;YACjD,OAAO,uBAAU,CAAC,cAAc,CAAC,uBAAuB,EAAE;gBACtD,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAtB,CAAsB;gBAChC,MAAM,EAAE,cAAQ,CAAC;aACpB,CAAC,CAAA;SACL;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,iCAAiC;IACjC,sCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACjD,IAAI,CAAC,IAAI,EAAE,CAAA;QACX,uBAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IACnD,CAAC;IAED,2CAA2C;IAC3C,qCAAW,GAAX,UAAY,KAA0B,EAAE,CAAS;QAAjD,iBAcC;QAbG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAChE,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACjD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;QACD,IAAM,KAAK,GAAG,oBAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QAC7C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAClB,OAAO,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK,EAAE,UAAC,EAAW,IAAO,EAAE,IAAI,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA,CAAC,CAAC,CAAC,CAAA;SACrH;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACpC,CAAC;IAED,mDAAmD;IACnD,0CAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACrD,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,OAAO;IACC,iDAAuB,GAA/B,UAAgC,IAAY,EAAE,IAAc;QACxD,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/C,OAAM;SACT;QACD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IACD,iHAAiH;IAEjH,SAAS;IACD,mCAAS,GAAjB;QACI,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,IAAI,CAAC,KAAK,KAAK,qBAAa,CAAC,IAAI,EAAE;YACnC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAa,CAAC,SAAS,CAAC,CAAC,CAAC,qBAAa,CAAC,IAAI,CAAA;YACjG,oBAAO,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA,CAAC,MAAM;YAC9D,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;SAC/C;IACL,CAAC;IAEO,uCAAa,GAArB,UAAsB,IAAc;QAChC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QAC1D,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,CAAA;QAClC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAA;QAC/J,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;QAC5E,KAAK;QACL,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QAC5B,KAAK;QACL,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAA;QAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,CAAA;QAC/D,IAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACpD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACjC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAC5C,IAAI,CAAC,WAAW,EAAE,CAAA;SACrB;QACD,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACjD,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAA;QAC7B,EAAE;QACF,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACzD,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;YACtF,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,0BAA0B,EAAE,oBAAO,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC,CAAA;SAChH;IACL,CAAC;IAEO,2CAAiB,GAAzB,UAA0B,IAAc;;QACpC,KAAK;QACL,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QACpF,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QAChC,KAAK;QACL,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,oBAAO,CAAC,QAAQ,EAAE,CAAA;QAC/F,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QACxI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;QAClC,gBAAgB,CAAC,MAAM,GAAG,CAAC,QAAC,IAAI,CAAC,SAAS,0CAAE,GAAG,CAAA,CAAA;QAC/C,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC,QAAC,IAAI,CAAC,SAAS,0CAAE,IAAI,CAAA,CAAC,EAAE;YAChD,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;YACzC,YAAY,CAAC,SAAS,CAAC,0BAAgB,CAAC,CAAC,KAAK,EAAE,CAAA;YAChD,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;SACjD;aAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;YACxB,UAAU,CAAC,sBAAsB,EAAE,CAAA;YACnC,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;SAC/C;IACL,CAAC;IAEO,2CAAiB,GAAzB,UAA0B,MAAiB,EAAE,KAAoB;QAC7D,MAAM,CAAC,YAAY,GAAG,KAAK,KAAK,qBAAa,CAAC,IAAI,CAAA;QAClD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,KAAK,KAAK,qBAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAA;IACzG,CAAC;IAEO,qCAAW,GAAnB;QAAA,iBAkBC;QAjBG,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,KAAK,qBAAa,CAAC,IAAI,CAAA;QAClG,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;YACvC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAA;YACX,uBAAU,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;YACzE,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YACjE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAC5C,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,KAAK,CAAA;QACjD,CAAC,CAAC,CAAA;QACF,IAAI,GAAG,IAAI,CAAC,EAAE;YACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YAC1E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;SAC1B;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;YAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;SAC1B;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,yBAAI,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,0CAAE,eAAe,KAAE,CAAC,CAAA;IACtF,CAAC;IAED,SAAS;IACD,wCAAc,GAAtB,UAAuB,IAAc,EAAE,KAAa;QAApD,iBASC;;QARG,IAAM,IAAI,SAAG,IAAI,CAAC,KAAK,0CAAG,KAAK,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE;YACrC,uBAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,KAAI,CAAC,OAAO,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,KAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,EAAlE,CAAkE,CAAC,CAAA;SACvH;aAAM;YACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;SACxC;IACL,CAAC;IACO,0CAAgB,GAAxB,UAAyB,IAAc,EAAE,KAAa,EAAE,MAAc;QAAtE,iBAsBC;QArBG,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC5F,IAAI,GAAG,CAAC,GAAG,EAAE;gBACT,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACvC;YACD,uBAAU,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA;YAC1C,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;YACzC,oBAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACzD,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aAC7B;iBAAM;gBACH,IAAI,CAAC,SAAS,GAAG,CAAC,KAAK,CAAC,CAAA;aAC3B;YACD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBAC7C,IAAI,CAAC,KAAK,GAAG,qBAAa,CAAC,IAAI,CAAA;gBAC/B,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;aAC/C;YACD,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,iBAAiB,CAAC,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;gBAClF,KAAI,CAAC,WAAW,EAAE,CAAA;aACrB;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACC,qCAAW,GAAnB,UAAoB,IAAc;QAAlC,iBAeC;QAdG,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC1E,IAAI,GAAG,CAAC,GAAG,EAAE;gBACT,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACvC;YACD,uBAAU,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA;YAC1C,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClC,oBAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACzD,IAAI,CAAC,KAAK,GAAG,qBAAa,CAAC,IAAI,CAAA;YAC/B,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;YAC5C,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,iBAAiB,CAAC,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;gBAClF,KAAI,CAAC,WAAW,EAAE,CAAA;aACrB;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACC,oCAAU,GAAlB;QAAA,iBAQC;QAPG,oBAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC3C,IAAI,GAAG,EAAE;gBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aACnC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACrB,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAzQgB,eAAe;QADnC,OAAO;OACa,eAAe,CA0QnC;IAAD,sBAAC;CA1QD,AA0QC,CA1Q4C,EAAE,CAAC,WAAW,GA0Q1D;kBA1QoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { MailInfo } from \"../../common/constant/DataType\";\nimport { CType, MailStateType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport CTypeObj from \"../../model/common/CTypeObj\";\nimport LabelAutoAnyCmpt from \"../cmpt/LabelAutoAnyCmpt\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class MailInfoPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private titleNode_: cc.Node = null // path://root/title_n\n    private senderNode_: cc.Node = null // path://root/sender_n\n    private timeLbl_: cc.Label = null // path://root/time_l\n    private contentSv_: cc.ScrollView = null // path://root/content_sv\n    private listSv_: cc.ScrollView = null // path://root/list_sv\n    private buttonsNode_: cc.Node = null // path://root/buttons_n\n    private autoRemoveNode_: cc.Node = null // path://root/auto_remove_n\n    //@end\n\n    private data: MailInfo = null\n    private readonly ITEM_ADAPT_SIZE: cc.Size = cc.size(64, 64)\n\n    public listenEventMaps() {\n        return [\n            { [EventType.TRANSLATE_TEXT_COMPLETE]: this.onTranslateTextComplete, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(data: MailInfo) {\n        this.data = data\n        this.checkRead()\n        this.updateContent(data)\n        this.buttonsNode_.Child('reply_be').active = !!data.sender && data.sender !== '-1' //不能回复系统邮件\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/buttons_n/remove_be\n    onClickRemove(event: cc.Event.EventTouch, _: string) {\n        if (!this.data) {\n            return\n        } else if (this.data.items?.length > 0 && this.data.state !== MailStateType.READ) {\n            viewHelper.showMessageBox('ui.has_not_claim_mail_item', {\n                ok: () => this.removeMail(),\n                cancel: () => { },\n            })\n        } else {\n            this.removeMail()\n        }\n    }\n\n    // path://root/buttons_n/claim_be\n    onClickClaim(event: cc.Event.EventTouch, _: string) {\n        if (!this.data || !this.data.items || this.data.items.length === 0) {\n            return\n        }\n        const data = this.data\n        const arr: CTypeObj[] = [], indexs: number[] = []\n        data.items.forEach((m, i) => {\n            if (!data.oneClaims?.has(i)) {\n                arr.push(m)\n                indexs.push(i)\n            }\n        })\n        if (arr.length === 0) {\n            return\n        }\n        const items = gameHpr.checkRewardFull(arr)\n        if (items.length > 0) {\n            return viewHelper.showPnl('common/ResFullTip', items, (ok: boolean) => { ok && this.claimReward(data) })\n        } else if (arr.length === 1) {\n            return this.claimRewardOne(data, indexs[0])\n        } else if (arr.some(m => m.type === CType.HERO_OPT)) {\n            return viewHelper.showMessageBox('ui.claim_hero_opt_tip', {\n                ok: () => this.claimReward(data),\n                cancel: () => { },\n            })\n        }\n        this.claimReward(data)\n    }\n\n    // path://root/buttons_n/reply_be\n    onClickReply(event: cc.Event.EventTouch, data: string) {\n        this.hide()\n        viewHelper.showPnl('menu/WriteMail', this.data)\n    }\n\n    // path://root/list_sv/view/content/item_be\n    onClickItem(event: cc.Event.EventTouch, _: string) {\n        if (!this.data || !this.data.items || this.data.items.length === 0) {\n            return\n        }\n        const data = this.data, index = event.target.Data\n        const item = data.items[index]\n        if (!item) {\n            return\n        }\n        const items = gameHpr.checkRewardFull([item])\n        if (items.length > 0) {\n            return viewHelper.showPnl('common/ResFullTip', items, (ok: boolean) => { ok && this.claimRewardOne(data, index) })\n        }\n        this.claimRewardOne(data, index)\n    }\n\n    // path://root/content_sv/view/content/translate_be\n    onClickTranslate(event: cc.Event.EventTouch, data: string) {\n        gameHpr.translateText(this.data, 'mail')\n        this.updateContentText(this.data)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // 翻译完成\n    private onTranslateTextComplete(type: string, data: MailInfo) {\n        if (type !== 'mail' || data.uid !== this.data.uid) {\n            return\n        }\n        this.updateContentText(data)\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    // 检测是否已读\n    private checkRead() {\n        const data = this.data\n        if (data.state === MailStateType.NONE) {\n            data.state = (data.items && data.items.length > 0) ? MailStateType.NOT_CLAIM : MailStateType.READ\n            gameHpr.net.send('mail/HD_ReadMail', { uid: data.uid }) //标记已读\n            this.emit(EventType.UPDATE_MAIL_STATE, data)\n        }\n    }\n\n    private updateContent(data: MailInfo) {\n        this.titleNode_.Child('val', cc.Label).string = data.title\n        const isSys = data.sender === '-1'\n        this.senderNode_.Child('val', cc.Label).Color(isSys ? '#BE772B' : '#756963').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8)\n        this.timeLbl_.string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime)\n        // 内容\n        this.updateContentText(data)\n        // 道具\n        const items = data.items || []\n        const claimBtn = this.buttonsNode_.Child('claim_be', cc.Button)\n        const hasItem = claimBtn.setActive(items.length > 0)\n        if (this.listSv_.setActive(hasItem)) {\n            this.updateClaimButton(claimBtn, data.state)\n            this.updateItems()\n        }\n        // 刷新内容高度\n        this.contentSv_.node.height = hasItem ? 280 : 420\n        this.contentSv_.scrollToTop()\n        //\n        if (this.autoRemoveNode_.active = !!data.autoDelSurplusTime) {\n            const autoDelTime = Math.max(0, data.autoDelSurplusTime - (Date.now() - data.getTime))\n            this.autoRemoveNode_.setLocaleKey('ui.auto_remove_mail_desc', gameHpr.millisecondToStringForDay(autoDelTime))\n        }\n    }\n\n    private updateContentText(data: MailInfo) {\n        // 内容\n        const content = this.contentSv_.content, contentLbl = content.Child('val', cc.Label)\n        contentLbl.string = data.content\n        // 翻译\n        content.Child('translate_be').active = !data.translate && !data.contentId && gameHpr.isGLobal()\n        const lineNode = content.Child('line'), translateLoading = content.Child('loading'), translateLbl = content.Child('translate', cc.Label)\n        lineNode.active = !!data.translate\n        translateLoading.active = !!data.translate?.req\n        if (translateLbl.setActive(!!data.translate?.text)) {\n            translateLbl.string = data.translate.text\n            translateLbl.Component(LabelAutoAnyCmpt).check()\n            lineNode.width = translateLbl.node.width * 0.5\n        } else if (lineNode.active) {\n            contentLbl._forceUpdateRenderData()\n            lineNode.width = contentLbl.node.width * 0.5\n        }\n    }\n\n    private updateClaimButton(button: cc.Button, state: MailStateType) {\n        button.interactable = state !== MailStateType.READ\n        button.Child('val').setLocaleKey(state === MailStateType.READ ? 'ui.yet_take' : 'ui.button_one_take')\n    }\n\n    private updateItems() {\n        const data = this.data, claims = data.oneClaims || [], isClaim = data.state === MailStateType.READ\n        const len = data.items.length\n        this.listSv_.Items(data.items, (it, item, i) => {\n            it.Data = i\n            viewHelper.updateItemByCTypeOne(it, item, this.key, this.ITEM_ADAPT_SIZE)\n            const claim = it.Child('claim').active = isClaim || claims.has(i)\n            it.Child('icon').opacity = claim ? 120 : 255\n            it.Component(cc.Button).interactable = !claim\n        })\n        if (len <= 4) {\n            this.listSv_.node.width = Math.max(80, len * 80 + (len - 1) * 40) + 28 + 4\n            this.listSv_.node.x = 2\n        } else {\n            this.listSv_.node.width = 528\n            this.listSv_.node.x = 0\n        }\n        this.listSv_.node.children.forEach(m => m.Component(cc.Widget)?.updateAlignment())\n    }\n\n    // 领取单个物品\n    private claimRewardOne(data: MailInfo, index: number) {\n        const item = data.items?.[index]\n        if (!item) {\n            return\n        } else if (item.type === CType.HERO_OPT) {\n            viewHelper.showHeroOptSelect(item.id).then(id => (this.isValid && id > 0) && this.claimRewardOneDo(data, index, id))\n        } else {\n            this.claimRewardOneDo(data, index, 0)\n        }\n    }\n    private claimRewardOneDo(data: MailInfo, index: number, heroId: number) {\n        gameHpr.net.request('mail/HD_ClaimMailItemOne', { uid: data.uid, index, heroId }, true).then(res => {\n            if (res.err) {\n                return viewHelper.showAlert(res.err)\n            }\n            viewHelper.showAlert('toast.take_succeed')\n            gameHpr.addGainMassage(data.items[index])\n            gameHpr.player.updateRewardItemsByFlags(res.data.rewards)\n            if (data.oneClaims) {\n                data.oneClaims.push(index)\n            } else {\n                data.oneClaims = [index]\n            }\n            if (data.oneClaims.length === data.items.length) {\n                data.state = MailStateType.READ\n                this.emit(EventType.UPDATE_MAIL_STATE, data)\n            }\n            if (this.isValid) {\n                this.updateClaimButton(this.buttonsNode_.Child('claim_be', cc.Button), data.state)\n                this.updateItems()\n            }\n        })\n    }\n\n    // 领取奖励\n    private claimReward(data: MailInfo) {\n        gameHpr.net.request('mail/HD_ClaimMailItem', { uid: data.uid }, true).then(res => {\n            if (res.err) {\n                return viewHelper.showAlert(res.err)\n            }\n            viewHelper.showAlert('toast.take_succeed')\n            gameHpr.addGainMassage(data.items)\n            gameHpr.player.updateRewardItemsByFlags(res.data.rewards)\n            data.state = MailStateType.READ\n            this.emit(EventType.UPDATE_MAIL_STATE, data)\n            if (this.isValid) {\n                this.updateClaimButton(this.buttonsNode_.Child('claim_be', cc.Button), data.state)\n                this.updateItems()\n            }\n        })\n    }\n\n    // 删除邮件\n    private removeMail() {\n        gameHpr.user.removeMail(this.data.uid).then(err => {\n            if (err) {\n                return viewHelper.showAlert(err)\n            } else if (this.isValid) {\n                this.hide()\n            }\n        })\n    }\n}\n"]}