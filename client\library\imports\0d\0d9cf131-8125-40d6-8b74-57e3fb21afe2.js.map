{"version": 3, "sources": ["assets\\app\\script\\view\\common\\ResDetailsPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAmJ;AACnJ,qDAA6D;AAC7D,0DAAqD;AACrD,6DAAyD;AACzD,6DAA4D;AAGpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA+C,qCAAc;IAA7D;QAAA,qEAuJC;QArJG,0BAA0B;QAClB,cAAQ,GAAkB,IAAI,CAAA,CAAC,6BAA6B;QAC5D,cAAQ,GAAY,IAAI,CAAA,CAAC,0BAA0B;QACnD,mBAAa,GAAY,IAAI,CAAA,CAAC,4CAA4C;QAC1E,aAAO,GAAa,IAAI,CAAA,CAAC,0BAA0B;QACnD,aAAO,GAAuB,IAAI,CAAA,CAAC,0BAA0B;QACrE,MAAM;QAEE,UAAI,GAAW,CAAC,CAAA;QAChB,YAAM,GAAgG,EAAE,CAAA;;IA4IpH,CAAC;IA1IU,2CAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,mBAAS,CAAC,sBAAsB,IAAG,IAAI,CAAC,qBAAqB,EAAE,QAAK,GAAE,IAAI;SAChF,CAAA;IACL,CAAC;IAEY,oCAAQ,GAArB;;;gBACI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,0BAAe,GAAG,EAAE,CAAA;;;;KAC1E;IAEM,mCAAO,GAAd,UAAe,IAAW;QACtB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC3B,CAAC;IAEM,oCAAQ,GAAf;IACA,CAAC;IAEM,mCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,0BAA0B;IAC1B,uCAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACtC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IAChD,CAAC;IAED,4CAA4C;IAC5C,2CAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;QAAxD,iBAoBC;QAnBG,IAAI,oBAAO,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE;YAC5B,OAAO,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;SAC5D;QACD,IAAI,oBAAO,CAAC,YAAY,EAAE;YACtB,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAM,MAAM,GAAG,CAAC,CAAC,oBAAO,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,CAAA;QAC/D,uBAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,sBAAsB,EAAE;YAChF,MAAM,EAAE,CAAC,0BAAe,EAAE,2BAAgB,EAAE,qBAAU,CAAC,IAAI,CAAC,CAAC;YAC7D,EAAE,EAAE,cAAM,OAAA,oBAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;gBACpD,IAAI,GAAG,EAAE;oBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;iBACnC;qBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;oBACrB,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;iBAC5B;YACL,CAAC,CAAC,EANQ,CAMR;YACF,MAAM,EAAE,cAAQ,CAAC;SACpB,CAAC,CAAA;IACN,CAAC;IACD,MAAM;IACN,iHAAiH;IAEzG,iDAAqB,GAA7B;QACI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAClC,CAAC;IACD,iHAAiH;IAEzG,uCAAW,GAAnB;;QAAA,iBAoBC;;QAnBG,IAAI,CAAC,MAAM,aAAK,GAAC,aAAK,CAAC,MAAM,IAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,GAAC,aAAK,CAAC,MAAM,IAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,GAAC,aAAK,CAAC,KAAK,IAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,KAAE,CAAA;QAC9H,YAAA,oBAAO,CAAC,aAAa,CAAC,oBAAO,CAAC,MAAM,EAAE,CAAC,0CAAE,KAAK,0CAAE,OAAO,CAAC,UAAA,IAAI;YACxD,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAM,MAAI,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAA;gBACpC,0BAAe,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC,EAAT,CAAS,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;oBAC5C,IAAM,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,2BAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAI,CAAC,CAAC,CAAC,CAAA;oBAC5D,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAjF,CAAiF,CAAC,CAAA;oBAChH,IAAI,EAAE,EAAE;wBACJ,EAAE,CAAC,KAAK,IAAI,CAAC,CAAA;qBAChB;yBAAM;wBACH,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,GAAG,KAAA,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAA;qBACzC;oBACD,IAAI,CAAC,GAAG,IAAI,GAAG,CAAA;gBACnB,CAAC,CAAC,CAAA;aACL;QACL,CAAC,EAAC;QACF,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAb,CAAa,CAAC,CAAA;SACrD;IACL,CAAC;IAEO,0CAAc,GAAtB,UAAuB,IAAW;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAM,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAA;QAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAA;QACrD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAA;QAC/C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;YACtC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;YAClD,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;YAChD,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;YACpD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAA;QACrC,CAAC,CAAC,CAAA;QACF,OAAO;QACP,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0BAAe,EAAE,MAAM,GAAG,CAAC,CAAA;QACjE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,EAAE,CAAA;QAC3D,OAAO;QACP,MAAM,IAAI,IAAI,CAAC,GAAG,CAAA;QAClB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;QAC7D,UAAU;QACV,IAAM,YAAY,GAAG,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,UAAU,CAAC,CAAA;QACtE,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,YAAY,GAAG,CAAC,EAAE;YACpD,MAAM,IAAI,YAAY,CAAA;YACtB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,YAAY,GAAG,EAAE,CAAA;SACpE;QACD,WAAW;QACX,IAAM,IAAI,GAAG,MAAM,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxD,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,2BAAgB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACpE,MAAM,IAAI,MAAM,CAAA;QAChB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,oBAAoB,EAAE,GAAG,GAAG,2BAAgB,CAAC,CAAA;QAC5F,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,EAAE,CAAA;QAC3D,OAAO;QACP,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,oBAAO,CAAC,YAAY,EAAE;YACzD,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC,CAAA;SAC3E;QACD,KAAK;QACL,IAAM,IAAI,GAAG,IAAI,KAAK,aAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;QAClE,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,EAAE;YAC5C,MAAM,IAAI,IAAI,CAAA;YACd,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAA;SAC7D;QACD,IAAI;QACJ,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,EAAE,CAAA;QAC3D,IAAM,GAAG,GAAG,IAAI,KAAK,aAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,EAAE,CAAA;QACrF,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAA;IAC1F,CAAC;IAEO,oDAAwB,GAAhC,UAAiC,IAAa,EAAE,IAAY;QACxD,IAAM,MAAM,GAAG,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAA;QAC/D,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAA;QACjG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAd,CAAc,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAA;QACjF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,MAAM,CAAA;QAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACvB,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;SACxB;IACL,CAAC;IAtJgB,iBAAiB;QADrC,OAAO;OACa,iBAAiB,CAuJrC;IAAD,wBAAC;CAvJD,AAuJC,CAvJ8C,EAAE,CAAC,WAAW,GAuJ5D;kBAvJoB,iBAAiB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ADD_OUTPUT_GOLD, ADD_OUTPUT_RATIO, CELL_RES_FIELDS, CTYPE_NAME, INIT_RES_OUTPUT, RES_FIELDS_CTYPE } from \"../../common/constant/Constant\";\nimport { CEffect, CType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport MapCellObj from \"../../model/main/MapCellObj\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class ResDetailsPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private landsSv_: cc.ScrollView = null // path://root/pages/lands_sv\n    private sumNode_: cc.Node = null // path://root/pages/sum_n\n    private buyAddOpNode_: cc.Node = null // path://root/pages/sum_n/5/buy_add_op_be_n\n    private curLbl_: cc.Label = null // path://root/pages/cur_l\n    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce\n    //@end\n\n    private type: number = 0\n    private resMap: { [key: number]: { arr: { cell: MapCellObj, val: number, count: number }[], sum: number } } = {}\n\n    public listenEventMaps() {\n        return [\n            { [EventType.UPDATE_ADD_OUTPUT_TIME]: this.onUpdateAddOutputTime, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.buyAddOpNode_.Child('val', cc.Label).string = ADD_OUTPUT_GOLD + ''\n    }\n\n    public onEnter(type: CType) {\n        this.initResDist()\n        this.tabsTc_.Tabs(type)\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/tabs_tc_tce\n    onClickTabs(event: cc.Toggle, data: string) {\n        !data && audioMgr.playSFX('click')\n        this.showResDetails(Number(event.node.name))\n    }\n\n    // path://root/pages/sum_n/5/buy_add_op_be_n\n    onClickBuyAddOp(event: cc.Event.EventTouch, data: string) {\n        if (gameHpr.world.isGameOver()) {\n            return viewHelper.showAlert('toast.gold_increase_output')\n        }\n        if (gameHpr.isNoviceMode) {\n            return\n        }\n        const type = this.type\n        const hasAdd = !!gameHpr.player.getAddOutputSurplusTime()[type]\n        viewHelper.showMessageBox(hasAdd ? 'ui.add_output_desc_1' : 'ui.add_output_desc_0', {\n            params: [ADD_OUTPUT_GOLD, ADD_OUTPUT_RATIO, CTYPE_NAME[type]],\n            ok: () => gameHpr.player.buyAddOutputTime(type).then(err => {\n                if (err) {\n                    return viewHelper.showAlert(err)\n                } else if (this.isValid) {\n                    this.showResDetails(type)\n                }\n            }),\n            cancel: () => { },\n        })\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    private onUpdateAddOutputTime() {\n        this.showResDetails(this.type)\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private initResDist() {\n        this.resMap = { [CType.CEREAL]: { arr: [], sum: 0 }, [CType.TIMBER]: { arr: [], sum: 0 }, [CType.STONE]: { arr: [], sum: 0 } }\n        gameHpr.getPlayerInfo(gameHpr.getUid())?.cells?.forEach(cell => {\n            if (cell.isHasRes()) {\n                const json = cell.getResJson() || {}\n                CELL_RES_FIELDS.filter(m => !!json[m]).forEach(m => {\n                    const info = this.resMap[RES_FIELDS_CTYPE[m]], val = json[m]\n                    const it = info.arr.find(x => (x.cell.landId === cell.landId || x.cell.cityId === cell.cityId) && x.val === val)\n                    if (it) {\n                        it.count += 1\n                    } else {\n                        info.arr.push({ cell, val, count: 1 })\n                    }\n                    info.sum += val\n                })\n            }\n        })\n        for (let key in this.resMap) {\n            this.resMap[key].arr.sort((a, b) => b.val - a.val)\n        }\n    }\n\n    private showResDetails(type: CType) {\n        this.type = type\n        const player = gameHpr.player\n        const info = this.resMap[type], len = info.arr.length\n        this.landsSv_.Child('empty').active = len === 0\n        this.landsSv_.Items(info.arr, (it, data, i) => {\n            it.Child('name').setLocaleKey(data.cell.getName())\n            it.Child('val', cc.Label).string = data.val + ''\n            it.Child('count', cc.Label).string = data.count + ''\n            it.Child('line').active = i !== 4\n        })\n        // 初始资源\n        let output = player.isCapture() ? 0 : INIT_RES_OUTPUT, addNum = 0\n        this.sumNode_.Child('3/val', cc.Label).string = output + ''\n        // 土地资源\n        output += info.sum\n        this.sumNode_.Child('1/val', cc.Label).string = info.sum + ''\n        // 内政加成 固定\n        const policyAddRes = gameHpr.getPlayerPolicyEffect(CEffect.RES_OUTPUT)\n        if (this.sumNode_.Child('2').active = policyAddRes > 0) {\n            output += policyAddRes\n            this.sumNode_.Child('2/val', cc.Label).string = policyAddRes + ''\n        }\n        // 商城购买 百分比\n        const time = player.getAddOutputSurplusTime()[type] || 0\n        addNum = time > 0 ? Math.floor(output * ADD_OUTPUT_RATIO * 0.01) : 0\n        output += addNum\n        this.sumNode_.Child('5/desc/val').setLocaleKey('ui.add_output_desc', '+' + ADD_OUTPUT_RATIO)\n        this.sumNode_.Child('5/val', cc.Label).string = addNum + ''\n        // 购买加成\n        if (this.sumNode_.Child('5').active = !gameHpr.isNoviceMode) {\n            this.updateAddOutputStateTime(this.sumNode_.Child('5/desc/state'), time)\n        }\n        // 粮耗\n        const cost = type === CType.CEREAL ? player.getCerealConsume() : 0\n        if (this.sumNode_.Child('7').active = cost > 0) {\n            output -= cost\n            this.sumNode_.Child('7/val', cc.Label).string = '-' + cost\n        }\n        // 总\n        this.sumNode_.Child('6/val', cc.Label).string = output + ''\n        const cap = type === CType.CEREAL ? player.getGranaryCap() : player.getWarehouseCap()\n        this.curLbl_.setLocaleKey('ui.cur_res_cap', gameHpr.getCountByCType(type) + '/' + cap)\n    }\n\n    private updateAddOutputStateTime(node: cc.Node, time: number) {\n        const hasAdd = time > 0, color = hasAdd ? '#59A733' : '#D7634D'\n        this.buyAddOpNode_.Child('desc').setLocaleKey(hasAdd ? 'ui.button_lengthen' : 'ui.button_enable')\n        node.children.forEach(m => m.Color(color))\n        node.Child('val').setLocaleKey(hasAdd ? 'ui.takeeffecting' : 'ui.not_takeeffect')\n        node.Child('s').active = hasAdd\n        const lbl = node.Child('time', cc.LabelTimer)\n        if (lbl.setActive(hasAdd)) {\n            lbl.run(time * 0.001)\n        }\n    }\n}\n"]}