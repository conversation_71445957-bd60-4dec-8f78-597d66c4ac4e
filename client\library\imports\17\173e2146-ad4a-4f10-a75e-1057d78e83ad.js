"use strict";
cc._RF.push(module, '173e2FGrUpPEKdeEFfXjoOt', 'BuildHospitalPnlCtrl');
// app/script/view/build/BuildHospitalPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ArmyObj_1 = require("../../model/area/ArmyObj");
var PawnObj_1 = require("../../model/area/PawnObj");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var BuildHospitalPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildHospitalPnlCtrl, _super);
    function BuildHospitalPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.sortSelectNode_ = null; // path://root/pages_n/1/info/pawn/sort/sort_select_be_n
        _this.queueSv_ = null; // path://root/pages_n/1/cure/content/queue_sv
        _this.upTimeNode_ = null; // path://root/pages_n/1/cure/x/up_time_be_n
        //@end
        _this.PKEY_TAB = 'HOSPITAL_TAB';
        _this.PKEY_SELECT_ARMY = 'HOSPITAL_SELECT_ARMY';
        _this.PKEY_SELECT_PAWN = 'HOSPITAL_SELECT_PAWN';
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.data = null;
        _this.cureProgressTween = {};
        _this.currSelectSort = 0; // 当前选择的排序方式
        _this.preSelectIndex = -1; // 治疗的目标在伤兵中的下标
        _this.tempCreateArmy = null;
        _this.selectArmy = null;
        _this.tempArmySortWeightMap = {};
        return _this;
    }
    BuildHospitalPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PAWN_INJURY_QUEUE] = this.onUpdatePawnInjuryQueue, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdatePawnCuringQueue, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.CHANGE_PAWN_EQUIP] = this.onChangePawnEquip, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.ADD_ARMY] = this.onUpdateArmy, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.REMOVE_ARMY] = this.onUpdateArmy, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateArmy, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmy, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_BATTLE_ARMY_BY_UI] = this.onUpdateArmy, _o.enter = true, _o),
        ];
    };
    BuildHospitalPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.areaCenter = this.getModel('areaCenter');
                return [2 /*return*/];
            });
        });
    };
    BuildHospitalPnlCtrl.prototype.onEnter = function (data, tab) {
        var _a;
        this.data = data;
        this.tempCreateArmy = this.user.getTempPreferenceMap(Enums_1.PreferenceKey.TEMP_CREATE_ARMY);
        var cond = this.pagesNode_.Child('1/info/cond');
        cond.Child('need/title/layout/val').setLocaleKey('ui.drill_cost', 'ui.button_cure');
        this.pagesNode_.Child('1/cure/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_cure');
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
        // 排序选择
        this.selectSortItem(this.sortSelectNode_, (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT)) !== null && _a !== void 0 ? _a : 5, true);
    };
    BuildHospitalPnlCtrl.prototype.onRemove = function () {
        this.selectArmy = null;
        this.tempArmySortWeightMap = {};
        ViewHelper_1.viewHelper.closePopupBoxList(this.sortSelectNode_);
        this.showCreateArmyFingerTip(false);
        if (this.currSelectSort !== GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT)) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT, this.currSelectSort);
        }
        this.user.setTempPreferenceData(Enums_1.PreferenceKey.TEMP_CREATE_ARMY, this.tempCreateArmy);
    };
    BuildHospitalPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildHospitalPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            // viewHelper.updateBuildBaseUI(node, this.data, this.key)
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            // 显示当前的军队列表
            this.selectArmy = null;
            this.tempArmySortWeightMap = {};
            this.updateArmyList(true, node);
            // 显示可治疗的士兵
            this.updateInjuryList(true, node);
            // 费用
            this.updateCureCost(node);
            // 治疗列表
            this.updateCureQueue(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildHospitalPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/info/pawn/list/view/content/pawn_be
    BuildHospitalPnlCtrl.prototype.onClickPawn = function (event, _data) {
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data, uid = data === null || data === void 0 ? void 0 : data.data.uid;
        if (!data) {
            return;
        }
        else if (uid === this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)) {
            uid && ViewHelper_1.viewHelper.showPnl('area/PawnInfo', data.pawn);
            return;
        }
        this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, uid);
        this.preSelectIndex = this.player.getInjuryPawns().findIndex(function (m) { return m.uid === uid; });
        this.updatePawnSelect(false);
        this.updateCureCost();
    };
    // path://root/pages_n/1/army/list/view/content/army_be
    BuildHospitalPnlCtrl.prototype.onClickArmy = function (event, _data) {
        var _a;
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data;
        if (!data) {
            if (this.tempCreateArmy) {
                return ViewHelper_1.viewHelper.showAlert('toast.yet_has_empty_army');
            }
            return this.showCreateArmyUI();
        }
        else if (!data.army) {
            return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
        }
        else if (data.uid !== ((_a = this.selectArmy) === null || _a === void 0 ? void 0 : _a.uid)) {
            this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid);
            this.updateArmySelect(data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/state/cure_be
    BuildHospitalPnlCtrl.prototype.onClickCure = function (event, data) {
        var _this = this;
        var pawnUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        if (!pawnUid) {
            return;
        }
        var area = this.areaCenter.getArea(this.data.aIndex);
        if (!area) {
            return;
        }
        else if (!this.selectArmy) {
            if (area.armys.length === 0 || area.armys.every(function (m) { return m.pawns.length >= Constant_1.ARMY_PAWN_MAX_COUNT; })) { //一个军队也没有
                ViewHelper_1.viewHelper.showAlert('toast.please_create_army', {
                    cb: function () {
                        if (_this.isValid && !_this.player.isArmyCountFull() && !GameHelper_1.gameHpr.isNoLongerTip('no_army')) {
                            _this.showCreateArmyFingerTip(true);
                        }
                    }
                });
            }
            else {
                ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
            }
            return;
        }
        var selectArmyUid = this.selectArmy.uid, tempArmyUid = '', armyName = '';
        var army = area.getArmyByUid(selectArmyUid) || this.getTempCreateArmy(selectArmyUid);
        if (!army) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_NOT_EXIST);
        }
        else if (army.uid.startsWith('temp_')) {
            armyName = army.name;
        }
        else {
            tempArmyUid = army.uid;
        }
        this.areaCenter.curePawnToServer(this.data.aIndex, tempArmyUid, armyName, pawnUid).then(function (res) {
            var _a;
            if (!_this.isValid) {
            }
            else if (res.err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
            }
            else if (res.err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name');
            } /* else if (res.err === ecode.ANTI_CHEAT) {
                viewHelper.showPnl('main/AntiCheat')
            } */
            else if (res.err) {
                ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                audioMgr.playSFX('build/sound_ui_00' + (_this.data.id === 2010 ? '7' : '6'));
                var army_1 = res.army;
                if (((_a = _this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === selectArmyUid) {
                    _this.tempCreateArmy = null;
                    if (_this.selectArmy)
                        _this.selectArmy.uid = army_1.uid;
                    if (_this.tempArmySortWeightMap[selectArmyUid]) {
                        _this.tempArmySortWeightMap[army_1.uid] = _this.tempArmySortWeightMap[selectArmyUid];
                        delete _this.tempArmySortWeightMap[selectArmyUid];
                    }
                    _this.player.getBaseArmys().push({
                        index: _this.data.aIndex,
                        uid: army_1.uid,
                        name: army_1.name,
                        state: Enums_1.ArmyState.CURING,
                        pawns: [],
                    });
                }
                var node = _this.pagesNode_.Child(1);
                _this.updateCureQueue(node);
                _this.updateInjuryList(false, node);
                // this.updateArmyList(false, node)
                // this.updateCureCost(node)
                _this.updateCureCost(node);
            }
        });
    };
    // path://root/pages_n/1/cure/content/0/cure_pawn_be
    BuildHospitalPnlCtrl.prototype.onClickCurePawn = function (event, _data) {
        audioMgr.playSFX('click');
        var data = event.target.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByCureInfo(data), data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/delete_be
    BuildHospitalPnlCtrl.prototype.onClickDelete = function (event, _data) {
        var _this = this;
        var uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN) || '';
        if (uid) {
            if (this.player.getCuringPawnsQueue().some(function (m) { return m.uid === uid; })) { // 治疗中无法删除
                this.updateCureButton(uid);
                return ViewHelper_1.viewHelper.showAlert('toast.delete_curing_pawn_tip');
            }
            var data = this.player.getInjuryPawns().find(function (m) { return m.uid === uid; });
            if (data) {
                ViewHelper_1.viewHelper.showMessageBox('ui.giveup_cure_tip', {
                    params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv])],
                    ok: function () { return _this.isValid && _this.giveUpCure(uid); },
                    cancel: function () { },
                });
            }
        }
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n
    BuildHospitalPnlCtrl.prototype.onClickSortSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n/select_mask_be
    BuildHospitalPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n/mask/root/sort_items_nbe
    BuildHospitalPnlCtrl.prototype.onClickSortItems = function (event, data) {
        var node = this.sortSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var type = Number(event.target.name);
        if (type !== this.currSelectSort) {
            this.selectSortItem(node, type);
        }
    };
    // path://root/pages_n/1/cure/x/up_time_be_n
    BuildHospitalPnlCtrl.prototype.onClickUpTime = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/SpeedUpCure', this.data);
    };
    // path://root/pages_n/1/info/cond/need/title/layout/view_cured_be
    BuildHospitalPnlCtrl.prototype.onClickViewCured = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/Desc', { text: 'ui.cured_count_desc' });
    };
    // path://root/pages_n/1/cure/content/0/cancel_cure_be
    BuildHospitalPnlCtrl.prototype.onClickCancelCure = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!data) {
            return;
        }
        else if (data.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelCure(data); },
                cancel: function () { },
            });
        }
        this.cancelCure(data);
    };
    // path://root/pages_n/0/info/chance_be
    BuildHospitalPnlCtrl.prototype.onClickChance = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/HospitalChanceDesc');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildHospitalPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('lv').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper.updateBuildAttrInfo(node, data);
        }
    };
    BuildHospitalPnlCtrl.prototype.onUpdatePawnInjuryQueue = function () {
        this.updateInjuryList(false);
    };
    // 刷新治疗列表
    BuildHospitalPnlCtrl.prototype.onUpdatePawnCuringQueue = function () {
        this.updateCureQueue();
        // this.updateArmyList(false)
        // this.updateCureCost()
    };
    // 战斗开始
    BuildHospitalPnlCtrl.prototype.onAreaBattleBegin = function (index) {
        if (this.data.aIndex === index) {
            this.updateCureQueue();
        }
    };
    // 战斗结束
    BuildHospitalPnlCtrl.prototype.onAreaBattleEnd = function (index) {
        if (this.data.aIndex === index) {
            this.updateCureQueue();
        }
    };
    // 切换士兵皮肤
    BuildHospitalPnlCtrl.prototype.onChangePawnSkin = function (data) {
        this.updateInjuryList(false);
        this.updateCureQueue();
    };
    // 切换士兵装备
    BuildHospitalPnlCtrl.prototype.onChangePawnEquip = function () {
        this.updateInjuryList(false);
    };
    // 重新刷新军队列表
    BuildHospitalPnlCtrl.prototype.onUpdateArmy = function () {
        this.updateArmyList(false);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildHospitalPnlCtrl.prototype.getTempCreateArmy = function (uid) {
        var _a;
        if (((_a = this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === uid) {
            return this.tempCreateArmy;
        }
        return null;
    };
    // 选择排序
    BuildHospitalPnlCtrl.prototype.selectSortItem = function (node, type, init) {
        node.Data = this.currSelectSort = type;
        node.Child('val', cc.Label).setLocaleKey('ui.portrayal_list_sort_' + type);
        node.Child('mask/root/sort_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            // m.Child('val').Color(select ? '#E6DCC8' : '#B6A591')
            m.Child('select').active = select;
        });
        if (!init) {
            this.updateInjuryList(false);
        }
    };
    // 放弃治疗
    BuildHospitalPnlCtrl.prototype.giveUpCure = function (uid) {
        var _this = this;
        NetHelper_1.netHelper.reqGiveUpInjuryPawn({ uid: uid }).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                _this.player.updateInjuryPawns(data.injuryPawns);
                _this.updateInjuryList(false);
                var selectUid = _this.user.getTempPreferenceMap(_this.PKEY_SELECT_PAWN);
                _this.updateCureButton(selectUid);
            }
        });
    };
    BuildHospitalPnlCtrl.prototype.addArmyToList = function (data, army, pawns) {
        var _a, _b;
        var item = {
            name: data.name,
            uid: data.uid,
            pawns: pawns,
            army: army
        };
        if (!this.tempArmySortWeightMap[data.uid]) {
            var weight = item.army ? 2 : 1;
            weight = weight * 10 + (9 - (((_a = item.army) === null || _a === void 0 ? void 0 : _a.getActPawnCount()) || 0));
            weight = weight * 10 + (9 - (((_b = item.army) === null || _b === void 0 ? void 0 : _b.pawns.length) || 0));
            this.tempArmySortWeightMap[data.uid] = weight;
        }
        return item;
    };
    // 刷新军队列表
    BuildHospitalPnlCtrl.prototype.updateArmyList = function (isLocation, node) {
        var _this = this;
        var _a, _b, _c;
        node = node || this.pagesNode_.Child(1);
        // 当前区域的军队
        var areaArmyMap = {};
        (_a = this.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) {
            if (m.isCanDrillPawn()) {
                areaArmyMap[m.uid] = m;
            }
        });
        var armys = [null];
        // 先装自己所有的军队 再装临时创建的军队
        this.player.getBaseArmys().forEach(function (m) { return armys.push(_this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)); });
        if (this.tempCreateArmy) {
            armys.push(this.addArmyToList(this.tempCreateArmy, this.tempCreateArmy));
        }
        // 排个序
        armys.sort(function (a, b) { return _this.tempArmySortWeightMap[b === null || b === void 0 ? void 0 : b.uid] - _this.tempArmySortWeightMap[a === null || a === void 0 ? void 0 : a.uid]; });
        var countNode = node.Child('army/title/count_bg');
        countNode.Child('cur', cc.Label).string = armys.length + '';
        countNode.Child('max', cc.Label).string = '/' + this.player.getArmyMaxCount();
        var uid = (_c = (_b = this.selectArmy) === null || _b === void 0 ? void 0 : _b.uid) !== null && _c !== void 0 ? _c : this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY);
        var curArmy = uid ? armys.find(function (m) { return !!(m === null || m === void 0 ? void 0 : m.army) && (m === null || m === void 0 ? void 0 : m.uid) === uid; }) : null, index = -1;
        var sv = node.Child('army/list', cc.ScrollView);
        sv.stopAutoScroll();
        // armys.push(null)
        sv.Items(armys, function (it, data, i) {
            var _a, _b;
            it.Data = data;
            var army = data === null || data === void 0 ? void 0 : data.army;
            var root = it.Child('root');
            root.Child('add').active = !data;
            root.Child('count').active = !!data;
            root.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : '';
            var state = root.Child('state');
            if (army) {
                root.Child('count/val', cc.Label).string = (((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || 0) + '';
                var addLbl = root.Child('count/add', cc.Label), dpc = army.drillPawns.length + army.curingPawns.length;
                if (addLbl.node.active = dpc > 0) {
                    addLbl.string = '+' + dpc;
                }
                var isFull = state.active = army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT;
                root.opacity = isFull ? 150 : 255;
                if (isFull) {
                    state.setLocaleKey('ui.yet_full');
                }
                // 显示选择
                if (!curArmy && isFull) {
                }
                else if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, army.uid);
                }
            }
            else if (data) {
                root.opacity = 150;
                root.Child('count/val', cc.Label).string = (((_b = data.pawns) === null || _b === void 0 ? void 0 : _b.length) || 0) + '';
                root.Child('count/add').active = false;
                state.active = true;
                state.setLocaleKey('ui.go_out');
            }
            else {
                root.opacity = 255;
                state.active = false;
            }
        });
        // 将选中的移动到中间
        if (isLocation) {
            sv.SelectItemToCentre(index);
        }
        // 刷新选中
        this.updateArmySelect(curArmy, node);
        /* // 将选中的移动到中间
        if (index !== -1) {
            const lay = sv.content.Component(cc.Layout)
            lay.updateLayout()
            const width = sv.content.children[0].width
            const tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft //当前位置
            const pw = sv.content.parent.width
            const cx = pw * 0.5 //中间位置
            sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0)
        } else {
            sv.scrollToLeft()
        } */
    };
    BuildHospitalPnlCtrl.prototype.updateArmySelect = function (item, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        this.selectArmy = item;
        var uid = (item === null || item === void 0 ? void 0 : item.uid) || '';
        node.Child('army/list', cc.ScrollView).content.children.forEach(function (m) {
            var _a;
            var select = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select;
        });
        var army = item === null || item === void 0 ? void 0 : item.army, pawns = [];
        if (army) {
            pawns.pushArr(army.pawns);
            pawns.pushArr(army.curingPawns);
            pawns.pushArr(army.drillPawns);
        }
        else if (item === null || item === void 0 ? void 0 : item.pawns) {
            pawns.pushArr(item.pawns);
        }
        // 刷新士兵列表
        node.Child('info/army_pawns').children.forEach(function (it, i) {
            var _a;
            var data = pawns[i], isId = typeof (data) === 'number', isCuring = !!data && !!(army === null || army === void 0 ? void 0 : army.curingPawns.some(function (m) { return m.uid === data.uid; }));
            it.Swih('none', !!data);
            if (data) {
                var icon = it.Child('icon');
                icon.opacity = (isId || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? data : (((_a = data.portrayal) === null || _a === void 0 ? void 0 : _a.id) || data.id), icon, _this.key);
                it.Child('lv', cc.Label).string = isId || data.lv <= 1 ? '' : data.lv + '';
            }
        });
        // 刷新按钮
        var buttons = node.Child('info/cond/need/buttons'), button = buttons.Child('state').Swih('cure_be')[0];
        button.Data = army ? (army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT ? 1 : 0) : 2;
        button.opacity = !!item && button.Data ? 120 : 255;
        if (pawns.length < Constant_1.ARMY_PAWN_MAX_COUNT) {
            var uid_1 = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
            this.updateCureButton(uid_1, buttons.parent);
        }
    };
    // 刷新士兵列表
    BuildHospitalPnlCtrl.prototype.updateInjuryList = function (isLocation, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var pawns = this.player.getInjuryPawns(), pawnCount = pawns.length;
        var mapObj = {};
        this.player.getCuringPawnsQueue().forEach(function (m) { return mapObj[m.uid] = true; });
        pawns.sort(function (a, b) {
            var aState = mapObj[a.uid] ? 1 : 0, bState = mapObj[b.uid] ? 1 : 0;
            if (aState !== bState) {
                return aState - bState;
            }
            switch (_this.currSelectSort) {
                case 0: // 兵种升序 > 等级降序 > 时间降序
                    if (a.id !== b.id)
                        return a.id - b.id;
                    if (a.lv !== b.lv)
                        return b.lv - a.lv;
                    return b.deadTime - a.deadTime;
                case 4: // 时间降序 > 兵种升序 > 等级降序 
                    if (a.deadTime !== b.deadTime)
                        return b.deadTime - a.deadTime;
                    if (a.id !== b.id)
                        return a.id - b.id;
                    return b.lv - a.lv;
                case 5: // 等级降序  > 兵种升序 > 时间降序
                    if (a.lv !== b.lv)
                        return b.lv - a.lv;
                    if (a.id !== b.id)
                        return a.id - b.id;
                    return b.deadTime - a.deadTime;
            }
            return b.lv - a.lv;
        });
        var selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN), nextUid = selectUid;
        if (!pawns.some(function (m) { return m.uid === selectUid; }) || mapObj[selectUid]) { // 士兵治疗完成 或 士兵治疗中，需要自动切换治疗目标
            nextUid = 0;
        }
        var curingQueue = this.player.getCuringPawnsQueue();
        var sv = node.Child('info/pawn/list', cc.ScrollView);
        sv.Child('empty').active = !pawnCount;
        sv.Items(pawns, function (it, data, i) {
            var conf = _this.player.getConfigPawnInfo(data.id), pawn = new PawnObj_1.default().init(data.id, conf.equip, data.lv, conf.skinId);
            it.Data = { data: data, pawn: pawn };
            it.Child('icon').opacity = curingQueue.some(function (m) { return m.uid === data.uid; }) ? 120 : 255;
            it.Child('lv/val', cc.Label).string = data.lv + '';
            var iconNode = it.Child('icon');
            ResHelper_1.resHelper.loadPawnHeadIcon(conf.skinId || data.id, iconNode, _this.key);
            if (i >= _this.preSelectIndex && !nextUid && !mapObj[data.uid]) {
                nextUid = _this.user.setTempPreferenceData(_this.PKEY_SELECT_PAWN, data.uid);
            }
        });
        // 没有找到下一个，就找上一个
        if (!nextUid) {
            for (var i = pawns.length - 1; i >= 0; i--) {
                var pawn = pawns[i];
                if (!nextUid && i <= this.preSelectIndex && !mapObj[pawn.uid]) {
                    nextUid = this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, pawn.uid);
                }
            }
        }
        // 将选中的移动到中间
        if (this.preSelectIndex !== -1) {
            var lay = sv.content.Component(cc.Layout);
            lay.updateLayout();
            var width = sv.content.children[0].width;
            var tx = (width + lay.spacingX) * this.preSelectIndex + width * 0.5 + lay.paddingLeft; //当前位置
            var pw = sv.content.parent.width;
            var cx = pw * 0.5; //中间位置
            sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0);
        }
        else {
            sv.scrollToLeft();
        }
        this.updatePawnSelect(isLocation, node);
        if (pawnCount <= 0) {
            this.updateCureCost();
        }
        // 刷新数量
        this.pagesNode_.Child('1/info/pawn/title/val').setLocaleKey('ui.select_wounded', pawnCount, Constant_1.HOSPITAL_PAWN_LIMIT);
    };
    BuildHospitalPnlCtrl.prototype.updatePawnSelect = function (isLocation, node) {
        node = node || this.pagesNode_.Child(1);
        var selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        var selectIndex = -1;
        var sv = node.Child('info/pawn/list', cc.ScrollView);
        sv.content.children.forEach(function (m, i) {
            var _a, _b;
            /* const uid = m.Data?.data?.uid
            m.Child('bg/select').active = m.Child('select').active = uid === selectUid */
            var select = m.Child('bg/select').active = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.data.uid) === selectUid;
            m.Component(cc.Button).interactable = !select || !!((_b = m.Data) === null || _b === void 0 ? void 0 : _b.pawn);
            if (select) {
                selectIndex = i;
            }
        });
        if (isLocation) {
            sv.SelectItemToCentre(selectIndex);
        }
    };
    // 刷新治疗费用
    BuildHospitalPnlCtrl.prototype.updateCureCost = function (node) {
        var _a, _b;
        node = node || this.pagesNode_.Child(1);
        var uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        var data = (_b = (_a = node.Child('info/pawn/list', cc.ScrollView).Find(function (m) { var _a, _b; return ((_b = (_a = m.Data) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.uid) === uid; })) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.data;
        var need = node.Child('info/cond/need'), empty = node.Child('info/cond/empty');
        empty.active = !data;
        if (need.active = !!data) {
            // 计算当前等级总耗时、总耗资
            // (招募三资 + 训练三资) * (1.0 + 治疗次数 * 0.2)
            // (招募时间 + 训练时间) * (0.5 + 阵亡次数 * 0.5) * (1 - 建筑等级  * 0.02)
            var baseCfg = assetsMgr.getJsonData('pawnBase', data.id);
            var cost = [], cureTime = baseCfg.drill_time;
            cost.pushArr(GameHelper_1.gameHpr.stringToCTypes(baseCfg.drill_cost));
            for (var i = 1; i < data.lv; i++) {
                var cfg = assetsMgr.getJsonData('pawnAttr', data.id * 1000 + i);
                cost.pushArr(GameHelper_1.gameHpr.stringToCTypes(cfg.lv_cost));
                cureTime += cfg.lv_time;
            }
            // 粮耗
            var crealCost = new CTypeObj_1.default().init(Enums_1.CType.CEREAL_C, 0, baseCfg.cereal_cost || 0);
            cost.push(crealCost);
            // // 检测是否有治疗士兵费用增加
            // cost = gameHpr.world.getSeason().changeBaseResCost(CEffect.CURE_COST, cost)
            var finalCost = [];
            GameHelper_1.gameHpr.mergeTypeObjsCount.apply(GameHelper_1.gameHpr, __spread([finalCost], cost));
            // 剔除经验书，后续用来加速
            for (var i = finalCost.length - 1; i >= 0; i--) {
                if (finalCost[i].type === Enums_1.CType.EXP_BOOK) {
                    finalCost.splice(i, 1);
                }
            }
            finalCost.forEach(function (m) { return m.type !== Enums_1.CType.CEREAL_C && (m.count = Math.floor(m.count * (1 + data.cureCount * 0.2))); });
            cureTime = cureTime * (0.5 + data.cureCount * 0.5);
            // 治疗消耗信息
            var cd = this.getCureTimeCD(), policyFreeCount = this.player.getFreeCurePawnSurplusCount();
            ViewHelper_1.viewHelper.updateFreeCostView(need, finalCost, cureTime, cd, false, policyFreeCount);
            need.Child('buttons/state/cure_be/val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_cure' : 'ui.button_cure');
            var curedCount = need.Child('title/layout/view_cured_be'), has = !!data.cureCount;
            curedCount.active = has;
            has && curedCount.Component(TextButtonCmpt_1.default).setKey('ui.button_cured_count', [data.cureCount]);
            // need.Child('cost').Items(finalCost || [], (it, cost) => viewHelper.updateCostViewOne(it, cost, true))
            // if (need.Child('time')?.setActive(!!cureTime)) {
            // 	const up = need.Child('time/up', cc.Label)
            // 	if (up?.setActive(!!cd)) {
            // 		up.string = `(-${Math.floor(cd * 100)}%)`
            // 		cureTime = Math.max(3, Math.floor(cureTime * (1 - cd)))
            // 	}
            // 	need.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(cureTime, 'h:mm:ss')
            // }
            // 刷新按钮
            this.updateCureButton(data.uid, need);
        }
    };
    BuildHospitalPnlCtrl.prototype.updateCureButton = function (uid, node) {
        node = node || this.pagesNode_.Child('1/info/cond/need');
        var info = GameHelper_1.gameHpr.player.getCuringPawnsQueue().find(function (m) { return m.uid === uid; });
        var buttons = node.Child('buttons'), button = buttons.Child('state/cure_be');
        button.opacity = button.Data ? 120 : 255;
        buttons.Child('delete_be').opacity = !!info ? 120 : 255;
        if (info) { // 在治疗队列中
            buttons.Child('state').Swih('curing')[0].Child('val').setLocaleKey(info.surplusTime > 0 ? 'ui.army_state_6' : 'ui.queueing');
        }
        else {
            buttons.Child('state').Swih('cure_be');
        }
    };
    // 获取治疗减CDBuff
    BuildHospitalPnlCtrl.prototype.getCureTimeCD = function () {
        var _a;
        var cd = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_CD);
        if (((_a = this.data.effect) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.CEffect.CURE_CD) {
            cd += this.data.effect.value;
        }
        return cd * 0.01;
    };
    // 刷新治疗列表
    BuildHospitalPnlCtrl.prototype.updateCureQueue = function (node) {
        var _a, _b, _c, _d;
        node = node || this.pagesNode_.Child(1);
        var list = this.player.getCuringPawnsQueue();
        this.upTimeNode_.active = list.length > 0;
        list.sort(function (a, b) { return b.surplusTime - a.surplusTime; });
        var pawnConf = this.player.getConfigPawnMap();
        var time = 0;
        // 是否有政策的加成
        var queueCount = 6 + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_QUEUE);
        node.Child('cure/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')';
        for (var i = 0; i < queueCount; i++) {
            var it = null, data = list[i];
            if (i === 0) {
                it = node.Child('cure/content/' + i);
            }
            else {
                var childrenCount = this.queueSv_.content.childrenCount;
                if (childrenCount <= 1) {
                    this.queueSv_.Items(queueCount - 1, function () { });
                }
                it = this.queueSv_.content.children[i - 1];
            }
            it.Data = data;
            var skinId = data ? (((_a = pawnConf[data.id]) === null || _a === void 0 ? void 0 : _a.skinId) || data.id) : 0;
            var has = it.Child('icon').active = it.Child('cure_pawn_be').active = !!data;
            (_b = it.Child('cancel_cure_be')) === null || _b === void 0 ? void 0 : _b.setActive(has);
            (_c = it.Child('icon/progress')) === null || _c === void 0 ? void 0 : _c.setActive(has);
            it.Child('lv/val', cc.Label).string = data ? data.lv + '' : '';
            ResHelper_1.resHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key);
            if (i !== 0) {
                time += (data === null || data === void 0 ? void 0 : data.needTime) || 0;
            }
            else if (data) {
                var progress = it.Child('icon/progress', cc.Sprite);
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, progress, this.key);
                var stime = data.getSurplusTime();
                time += stime;
                (_d = this.cureProgressTween[i]) === null || _d === void 0 ? void 0 : _d.stop();
                this.cureProgressTween[i] = null;
                progress.fillRange = stime / data.needTime;
                var st = stime * 0.001;
                it.Child('time', cc.LabelTimer).run(st);
                this.cureProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start();
            }
            else {
                it.Child('time', cc.LabelTimer).string = '';
            }
        }
        node.Child('cure/desc').active = time > 0;
        if (time > 0) {
            node.Child('cure/desc/title').setLocaleKey('ui.drill_all_desc', 'ui.button_cure');
            node.Child('cure/desc/time/val', cc.LabelTimer).run(time * 0.001);
        }
    };
    BuildHospitalPnlCtrl.prototype.showCreateArmyUI = function () {
        var _this = this;
        if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        this.showCreateArmyFingerTip(false);
        return ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.tempCreateArmy = new ArmyObj_1.default().init(_this.data.aIndex, GameHelper_1.gameHpr.getUid(), name);
                _this.tempArmySortWeightMap = {};
                if (!_this.selectArmy) {
                    _this.selectArmy = {};
                }
                _this.selectArmy.uid = _this.tempCreateArmy.uid;
                _this.updateArmyList(true, _this.pagesNode_.Child(1));
            }
        });
    };
    // 显示创建军队提示手指
    BuildHospitalPnlCtrl.prototype.showCreateArmyFingerTip = function (val) {
        var node = this.pagesNode_.Child(1);
        var sv = node.Child('army/list', cc.ScrollView), finger = sv.Child('finger');
        if (finger.active = val) {
            var count = sv.content.childrenCount;
            sv.stopAutoScroll();
            if (count >= 4) {
                sv.scrollToRight();
            }
            var it = sv.content.children[count - 1];
            var pos = ut.convertToNodeAR(it, sv.node);
            finger.setPosition(pos.x, pos.y - 12);
        }
    };
    // 取消治疗
    BuildHospitalPnlCtrl.prototype.cancelCure = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        NetHelper_1.netHelper.reqCancelCurePawn({ index: index, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateOutputByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnCuringQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                _this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: 'ui.cancel_cure_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
        });
    };
    BuildHospitalPnlCtrl = __decorate([
        ccclass
    ], BuildHospitalPnlCtrl);
    return BuildHospitalPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildHospitalPnlCtrl;

cc._RF.pop();