{"version": 3, "sources": ["assets\\app\\script\\view\\build\\BuildHospitalPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA0F;AAE1F,qDAAoD;AACpD,qDAAuF;AAEvF,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,2DAA0D;AAC1D,6DAA4D;AAE5D,oDAA+C;AAE/C,oDAA+C;AAC/C,wDAAmD;AAInD,yDAAoD;AAE5C,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAkD,wCAAc;IAAhE;QAAA,qEAgyBC;QA9xBA,0BAA0B;QAClB,aAAO,GAAuB,IAAI,CAAA,CAAC,0BAA0B;QAC7D,gBAAU,GAAY,IAAI,CAAA,CAAC,sBAAsB;QACjD,qBAAe,GAAY,IAAI,CAAA,CAAC,wDAAwD;QACxF,cAAQ,GAAkB,IAAI,CAAA,CAAC,8CAA8C;QAC7E,iBAAW,GAAY,IAAI,CAAA,CAAC,4CAA4C;QAChF,MAAM;QAEE,cAAQ,GAAW,cAAc,CAAA;QACjC,sBAAgB,GAAW,sBAAsB,CAAA;QACjD,sBAAgB,GAAW,sBAAsB,CAAA;QAEjD,UAAI,GAAc,IAAI,CAAA;QACtB,YAAM,GAAgB,IAAI,CAAA;QAC1B,gBAAU,GAAoB,IAAI,CAAA;QAClC,UAAI,GAAa,IAAI,CAAA;QAErB,uBAAiB,GAAgC,EAAE,CAAA;QAEnD,oBAAc,GAAW,CAAC,CAAA,CAAC,YAAY;QACvC,oBAAc,GAAW,CAAC,CAAC,CAAA,CAAC,eAAe;QAC3C,oBAAc,GAAY,IAAI,CAAA;QAC9B,gBAAU,GAAa,IAAI,CAAA;QAC3B,2BAAqB,GAA8B,EAAE,CAAA;;IAuwB9D,CAAC;IArwBO,8CAAe,GAAtB;;QACC,OAAO;sBACJ,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,wBAAwB,IAAG,IAAI,CAAC,uBAAuB,EAAE,QAAK,GAAE,IAAI;sBAC/E,GAAC,mBAAS,CAAC,wBAAwB,IAAG,IAAI,CAAC,uBAAuB,EAAE,QAAK,GAAE,IAAI;sBAC/E,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,QAAK,GAAE,IAAI;sBAClE,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,QAAK,GAAE,IAAI;sBAClE,GAAC,mBAAS,CAAC,QAAQ,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACpD,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBAC3D,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACjE,GAAC,mBAAS,CAAC,wBAAwB,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;SACtE,CAAA;IACF,CAAC;IAEY,uCAAQ,GAArB;;;gBACC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;;;;KAC7C;IAEM,sCAAO,GAAd,UAAe,IAAc,EAAE,GAAY;;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,qBAAa,CAAC,gBAAgB,CAAC,CAAA;QACpF,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;QACjD,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,YAAY,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAA;QACnF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,YAAY,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAA;QAC7F,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC9E,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,QAAE,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,iBAAiB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;IAC3H,CAAC;IAEM,uCAAQ,GAAf;QACC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAA;QAC/B,uBAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QAClD,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,iBAAiB,CAAC,EAAE;YACjG,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;SACzF;QACD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,qBAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;IACrF,CAAC;IAEM,sCAAO,GAAd;QACC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACxC,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,0BAA0B;IAC1B,0CAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACzC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1C,IAAI,IAAI,KAAK,CAAC,EAAE;YACf,0DAA0D;YAC1D,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC5E,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACnI;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACtB,YAAY;YACZ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YACtB,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAA;YAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YAC/B,WAAW;YACX,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACjC,KAAK;YACL,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YACzB,OAAO;YACP,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;SAC1B;IACF,CAAC;IAED,6CAA6C;IAC7C,wCAAS,GAAT,UAAU,KAA0B,EAAE,IAAY;QACjD,oBAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACtC,CAAC;IAED,4DAA4D;IAC5D,0CAAW,GAAX,UAAY,KAA0B,EAAE,KAAa;QACpD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC,GAAG,CAAA;QAC7D,IAAI,CAAC,IAAI,EAAE;YACV,OAAM;SACN;aAAM,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;YACzE,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YACrD,OAAM;SACN;QACD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAA;QAC3D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;QAChF,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAA;IACtB,CAAC;IAED,uDAAuD;IACvD,0CAAW,GAAX,UAAY,KAA0B,EAAE,KAAa;;QACpD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,GAAa,EAAE,CAAC,IAAI,CAAA;QACjD,IAAI,CAAC,IAAI,EAAE;YACV,IAAI,IAAI,CAAC,cAAc,EAAE;gBACxB,OAAO,uBAAU,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;aACvD;YACD,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;SAC9B;aAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACtB,OAAO,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;SACzD;aAAM,IAAI,IAAI,CAAC,GAAG,YAAK,IAAI,CAAC,UAAU,0CAAE,GAAG,CAAA,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAChE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;SAC3B;IACF,CAAC;IAED,6DAA6D;IAC7D,0CAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAApD,iBAmEC;QAlEA,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACrE,IAAI,CAAC,OAAO,EAAE;YACb,OAAM;SACN;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACtD,IAAI,CAAC,IAAI,EAAE;YACV,OAAM;SACN;aAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,8BAAmB,EAArC,CAAqC,CAAC,EAAE,EAAE,SAAS;gBACvG,uBAAU,CAAC,SAAS,CAAC,0BAA0B,EAAE;oBAChD,EAAE,EAAE;wBACH,IAAI,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAO,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;4BACxF,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA;yBAClC;oBACF,CAAC;iBACD,CAAC,CAAA;aACF;iBAAM;gBACN,uBAAU,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;aAChD;YACD,OAAM;SACN;QACD,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,WAAW,GAAG,EAAE,EAAE,QAAQ,GAAG,EAAE,CAAA;QACxE,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAA;QACtF,IAAI,CAAC,IAAI,EAAE;YACV,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,cAAc,CAAC,CAAA;SACjD;aAAM,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACxC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAA;SACpB;aAAM;YACN,WAAW,GAAG,IAAI,CAAC,GAAG,CAAA;SACtB;QACD,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;;YAC1F,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE;aAClB;iBAAM,IAAI,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,cAAc,EAAE;gBAC5C,OAAO,uBAAU,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;aACxD;iBAAM,IAAI,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,kBAAkB,EAAE;gBAChD,OAAO,uBAAU,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAA;aAC5D,CAAC;;gBAEE;iBAAM,IAAI,GAAG,CAAC,GAAG,EAAE;gBACtB,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aAC7B;iBAAM;gBACN,QAAQ,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,KAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC3E,IAAM,MAAI,GAAG,GAAG,CAAC,IAAI,CAAA;gBACrB,IAAI,OAAA,KAAI,CAAC,cAAc,0CAAE,GAAG,MAAK,aAAa,EAAE;oBAC/C,KAAI,CAAC,cAAc,GAAG,IAAI,CAAA;oBAC1B,IAAI,KAAI,CAAC,UAAU;wBAAE,KAAI,CAAC,UAAU,CAAC,GAAG,GAAG,MAAI,CAAC,GAAG,CAAA;oBACnD,IAAI,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE;wBAC9C,KAAI,CAAC,qBAAqB,CAAC,MAAI,CAAC,GAAG,CAAC,GAAG,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAA;wBAChF,OAAO,KAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAA;qBAChD;oBACD,KAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC;wBAC/B,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,MAAM;wBACvB,GAAG,EAAE,MAAI,CAAC,GAAG;wBACb,IAAI,EAAE,MAAI,CAAC,IAAI;wBACf,KAAK,EAAE,iBAAS,CAAC,MAAM;wBACvB,KAAK,EAAE,EAAE;qBACT,CAAC,CAAA;iBACF;gBACD,IAAM,IAAI,GAAG,KAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBACrC,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC1B,KAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;gBAClC,mCAAmC;gBACnC,4BAA4B;gBAC5B,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;aACzB;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,oDAAoD;IACpD,8CAAe,GAAf,UAAgB,KAA0B,EAAE,KAAa;QACxD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAoB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACtD,IAAI,IAAI,EAAE;YACT,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;SACrF;IACF,CAAC;IAED,yDAAyD;IACzD,4CAAa,GAAb,UAAc,KAA0B,EAAE,KAAa;QAAvD,iBAgBC;QAfA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAA;QACvE,IAAI,GAAG,EAAE;YACR,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,EAAE,EAAE,UAAU;gBAC3E,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;gBAC1B,OAAO,uBAAU,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAA;aAC3D;YACD,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;YAClE,IAAI,IAAI,EAAE;gBACT,uBAAU,CAAC,cAAc,CAAC,oBAAoB,EAAE;oBAC/C,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC9E,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAApC,CAAoC;oBAC9C,MAAM,EAAE,cAAQ,CAAC;iBACjB,CAAC,CAAA;aACF;SACD;IACF,CAAC;IAED,wDAAwD;IACxD,gDAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACzD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,uBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAClD,CAAC;IAED,uEAAuE;IACvE,gDAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACzD,uBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC1D,CAAC;IAED,iFAAiF;IACjF,+CAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACxD,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAA;QACjC,uBAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAC1C,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,IAAI,KAAK,IAAI,CAAC,cAAc,EAAE;YACjC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SAC/B;IACF,CAAC;IAED,4CAA4C;IAC5C,4CAAa,GAAb,UAAc,KAA0B,EAAE,IAAY;QACrD,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IACnD,CAAC;IAED,kEAAkE;IAClE,+CAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACxD,uBAAU,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAA;IACnE,CAAC;IAED,sDAAsD;IACtD,gDAAiB,GAAjB,UAAkB,KAA0B,EAAE,KAAa;QAA3D,iBAWC;QAVA,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACrC,IAAI,CAAC,IAAI,EAAE;YACV,OAAM;SACN;aAAM,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;YAChC,OAAO,uBAAU,CAAC,cAAc,CAAC,iCAAiC,EAAE;gBACnE,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAArC,CAAqC;gBAC/C,MAAM,EAAE,cAAQ,CAAC;aACjB,CAAC,CAAA;SACF;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC;IAED,uCAAuC;IACvC,4CAAa,GAAb,UAAc,KAA0B,EAAE,IAAY;QACrD,uBAAU,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAA;IAC/C,CAAC;IACD,MAAM;IACN,iHAAiH;IAEzG,8CAAe,GAAvB,UAAwB,IAAc;QACrC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE;YAC/B,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;YAC/C,uBAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SAC1C;IACF,CAAC;IAEO,sDAAuB,GAA/B;QACC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;IAC7B,CAAC;IAED,SAAS;IACD,sDAAuB,GAA/B;QACC,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,6BAA6B;QAC7B,wBAAwB;IACzB,CAAC;IAED,OAAO;IACC,gDAAiB,GAAzB,UAA0B,KAAa;QACtC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YAC/B,IAAI,CAAC,eAAe,EAAE,CAAA;SACtB;IACF,CAAC;IAED,OAAO;IACC,8CAAe,GAAvB,UAAwB,KAAa;QACpC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YAC/B,IAAI,CAAC,eAAe,EAAE,CAAA;SACtB;IACF,CAAC;IAED,SAAS;IACD,+CAAgB,GAAxB,UAAyB,IAAa;QACrC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAA;IACvB,CAAC;IAED,SAAS;IACD,gDAAiB,GAAzB;QACC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;IAC7B,CAAC;IAED,WAAW;IACH,2CAAY,GAApB;QACC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAED,iHAAiH;IACzG,gDAAiB,GAAzB,UAA0B,GAAW;;QACpC,IAAI,OAAA,IAAI,CAAC,cAAc,0CAAE,GAAG,MAAK,GAAG,EAAE;YACrC,OAAO,IAAI,CAAC,cAAc,CAAA;SAC1B;QACD,OAAO,IAAI,CAAA;IACZ,CAAC;IAED,OAAO;IACC,6CAAc,GAAtB,UAAuB,IAAa,EAAE,IAAY,EAAE,IAAc;QACjE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QACtC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAA;QAC1E,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;YACxD,IAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAA;YACtC,uDAAuD;YACvD,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,MAAM,CAAA;QAClC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,EAAE;YACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;SAC5B;IACF,CAAC;IAED,OAAO;IACC,yCAAU,GAAlB,UAAmB,GAAW;QAA9B,iBAYC;QAXA,qBAAS,CAAC,mBAAmB,CAAC,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC9C,IAAI,GAAG,CAAC,GAAG,EAAE;gBACZ,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACpC;iBAAM;gBACN,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;gBACrB,KAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC/C,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;gBAC5B,IAAM,SAAS,GAAG,KAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAI,CAAC,gBAAgB,CAAC,CAAA;gBACvE,KAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;aAChC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAEO,4CAAa,GAArB,UAAsB,IAAS,EAAE,IAAa,EAAE,KAAa;;QAC5D,IAAM,IAAI,GAAa;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,OAAA;YACL,IAAI,MAAA;SACJ,CAAA;QACD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC1C,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC9B,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,OAAA,IAAI,CAAC,IAAI,0CAAE,eAAe,OAAM,CAAC,CAAC,CAAC,CAAA;YAChE,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,OAAA,IAAI,CAAC,IAAI,0CAAE,KAAK,CAAC,MAAM,KAAI,CAAC,CAAC,CAAC,CAAA;YAC3D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAA;SAC7C;QACD,OAAO,IAAI,CAAA;IACZ,CAAC;IAED,SAAS;IACD,6CAAc,GAAtB,UAAuB,UAAmB,EAAE,IAAc;QAA1D,iBAgFC;;QA/EA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,UAAU;QACV,IAAM,WAAW,GAA+B,EAAE,CAAA;QAClD,MAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,0CAAE,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YACzD,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;gBACvB,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACtB;QACF,CAAC,EAAC;QACF,IAAM,KAAK,GAAe,CAAC,IAAI,CAAC,CAAA;QAChC,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAK,CAAC,IAAI,CAAC,KAAI,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAA9D,CAA8D,CAAC,CAAA;QACvG,IAAI,IAAI,CAAC,cAAc,EAAE;YACxB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAA;SACxE;QACD,MAAM;QACN,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,KAAI,CAAC,qBAAqB,CAAC,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,GAAG,CAAC,GAAG,KAAI,CAAC,qBAAqB,CAAC,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,GAAG,CAAC,EAAvE,CAAuE,CAAC,CAAA;QAC7F,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAA;QACnD,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,EAAE,CAAA;QAC3D,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAA;QAC7E,IAAM,GAAG,eAAG,IAAI,CAAC,UAAU,0CAAE,GAAG,mCAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACzF,IAAI,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,EAAC,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,CAAA,IAAI,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,GAAG,MAAK,GAAG,EAA3B,CAA2B,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAA;QACnF,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QACjD,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,mBAAmB;QACnB,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;;YAC3B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAA;YACvB,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAA;YAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAA;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YAC/E,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YACjC,IAAI,IAAI,EAAE;gBACT,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,OAAA,IAAI,CAAC,KAAK,0CAAE,MAAM,KAAI,CAAC,CAAC,GAAG,EAAE,CAAA;gBACzE,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;gBACxG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE;oBACjC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAA;iBACzB;gBACD,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,IAAI,8BAAmB,CAAA;gBAC3E,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;gBACjC,IAAI,MAAM,EAAE;oBACX,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,CAAA;iBACjC;gBACD,OAAO;gBACP,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE;iBACvB;qBAAM,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE;oBAClE,OAAO,GAAG,IAAI,CAAA;oBACd,KAAK,GAAG,CAAC,CAAA;oBACT,KAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;iBAChE;aACD;iBAAM,IAAI,IAAI,EAAE;gBAChB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;gBAClB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,OAAA,IAAI,CAAC,KAAK,0CAAE,MAAM,KAAI,CAAC,CAAC,GAAG,EAAE,CAAA;gBACzE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;gBACtC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAA;gBACnB,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;aAC/B;iBAAM;gBACN,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;gBAClB,KAAK,CAAC,MAAM,GAAG,KAAK,CAAA;aACpB;QACF,CAAC,CAAC,CAAA;QACF,YAAY;QACZ,IAAI,UAAU,EAAE;YACf,EAAE,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;SAC5B;QACD,OAAO;QACP,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QACpC;;;;;;;;;;;YAWI;IACL,CAAC;IAEO,+CAAgB,GAAxB,UAAyB,IAAc,EAAE,IAAc;QAAvD,iBAmCC;QAlCA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAM,GAAG,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,KAAI,EAAE,CAAA;QAC3B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;;YAChE,IAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,OAAA,CAAC,CAAC,IAAI,0CAAE,GAAG,MAAK,GAAG,CAAA;YAC7D,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,MAAM,CAAA;QAC9C,CAAC,CAAC,CAAA;QACF,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,EAAE,KAAK,GAAG,EAAE,CAAA;QACnC,IAAI,IAAI,EAAE;YACT,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACzB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC/B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;SAC9B;aAAM,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,EAAE;YACvB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SACzB;QACD,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,EAAE,EAAE,CAAC;;YACpD,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,EAAC,CAAA;YAChI,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;YACvB,IAAI,IAAI,EAAE;gBACT,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAC7B,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;gBAC7C,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAA,IAAI,CAAC,SAAS,0CAAE,EAAE,KAAI,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBAC7F,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;aAC1E;QACF,CAAC,CAAC,CAAA;QACF,OAAO;QACP,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;QACxG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,8BAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAChF,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QAClD,IAAI,KAAK,CAAC,MAAM,GAAG,8BAAmB,EAAE;YACvC,IAAM,KAAG,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YACjE,IAAI,CAAC,gBAAgB,CAAC,KAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;SAC1C;IACF,CAAC;IAED,SAAS;IACD,+CAAgB,GAAxB,UAAyB,UAAmB,EAAE,IAAc;QAA5D,iBAwEC;QAvEA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAA;QACpE,IAAM,MAAM,GAAG,EAAE,CAAA;QACjB,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAApB,CAAoB,CAAC,CAAA;QACpE,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YACf,IAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACpE,IAAI,MAAM,KAAK,MAAM,EAAE;gBACtB,OAAO,MAAM,GAAG,MAAM,CAAA;aACtB;YACD,QAAQ,KAAI,CAAC,cAAc,EAAE;gBAC5B,KAAK,CAAC,EAAE,qBAAqB;oBAC5B,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;wBAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;oBACrC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;wBAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;oBACrC,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;gBAC/B,KAAK,CAAC,EAAE,sBAAsB;oBAC7B,IAAI,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;wBAAE,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;oBAC7D,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;wBAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;oBACrC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;gBACnB,KAAK,CAAC,EAAE,sBAAsB;oBAC7B,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;wBAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;oBACrC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;wBAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;oBACrC,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;aAC/B;YACD,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;QACnB,CAAC,CAAC,CAAA;QACF,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC1F,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,SAAS,EAAnB,CAAmB,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,4BAA4B;YAC7F,OAAO,GAAG,CAAC,CAAA;SACX;QACD,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;QACrD,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QACtD,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,SAAS,CAAA;QACrC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;YAC3B,IAAM,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,EAClD,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACrE,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;YACpC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAChF,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;YAClD,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjC,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;YACtE,IAAI,CAAC,IAAI,KAAI,CAAC,cAAc,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC9D,OAAO,GAAG,KAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;aAC1E;QACF,CAAC,CAAC,CAAA;QACF,gBAAgB;QAChB,IAAI,CAAC,OAAO,EAAE;YACb,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;gBACrB,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBAC9D,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;iBAC1E;aACD;SACD;QACD,YAAY;QACZ,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,CAAC,EAAE;YAC/B,IAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;YAC3C,GAAG,CAAC,YAAY,EAAE,CAAA;YAClB,IAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;YAC1C,IAAM,EAAE,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,WAAW,CAAA,CAAC,MAAM;YAC9F,IAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAA;YAClC,IAAM,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA,CAAC,MAAM;YAC1B,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;SAC7E;aAAM;YACN,EAAE,CAAC,YAAY,EAAE,CAAA;SACjB;QACD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;QACvC,IAAI,SAAS,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,cAAc,EAAE,CAAA;SACrB;QACD,OAAO;QACP,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,YAAY,CAAC,mBAAmB,EAAE,SAAS,EAAE,8BAAmB,CAAC,CAAA;IACjH,CAAC;IAEO,+CAAgB,GAAxB,UAAyB,UAAmB,EAAE,IAAc;QAC3D,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACvE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAA;QACpB,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QACtD,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;;YAChC;yFAC6E;YAC7E,IAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,OAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,CAAC,GAAG,MAAK,SAAS,CAAA;YACtG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,QAAC,CAAC,CAAC,IAAI,0CAAE,IAAI,CAAA,CAAA;YAC/D,IAAI,MAAM,EAAE;gBACX,WAAW,GAAG,CAAC,CAAA;aACf;QACF,CAAC,CAAC,CAAA;QACF,IAAI,UAAU,EAAE;YACf,EAAE,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAA;SAClC;IACF,CAAC;IAED,SAAS;IACD,6CAAc,GAAtB,UAAuB,IAAc;;QACpC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACjE,IAAM,IAAI,eAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAA,CAAC,gBAAI,OAAA,aAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,0CAAE,GAAG,MAAK,GAAG,CAAA,EAAA,CAAC,0CAAE,IAAI,0CAAE,IAAI,CAAA;QACzG,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAChF,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAA;QACpB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE;YACzB,gBAAgB;YAChB,qCAAqC;YACrC,0DAA0D;YAC1D,IAAM,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;YAC1D,IAAI,IAAI,GAAG,EAAE,EAAE,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAA;YAC5C,IAAI,CAAC,OAAO,CAAC,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAA;YACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;gBACjC,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC,CAAA;gBACjE,IAAI,CAAC,OAAO,CAAC,oBAAO,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAA;gBACjD,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAA;aACvB;YACD,KAAK;YACL,IAAM,SAAS,GAAG,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,CAAA;YAClF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACpB,mBAAmB;YACnB,8EAA8E;YAC9E,IAAM,SAAS,GAAe,EAAE,CAAA;YAChC,oBAAO,CAAC,kBAAkB,OAA1B,oBAAO,YAAoB,SAAS,GAAK,IAAI,GAAC;YAC9C,eAAe;YACf,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE;oBACzC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;iBACtB;aACD;YACD,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,EAAzF,CAAyF,CAAC,CAAA;YACjH,QAAQ,GAAG,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAA;YAClD,SAAS;YACT,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE,CAAA;YAC5F,uBAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,eAAe,CAAC,CAAA;YACpF,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC,YAAY,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;YACpH,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;YACnF,UAAU,CAAC,MAAM,GAAG,GAAG,CAAA;YACvB,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;YAC7F,wGAAwG;YACxG,mDAAmD;YACnD,8CAA8C;YAC9C,8BAA8B;YAC9B,8CAA8C;YAC9C,4DAA4D;YAC5D,KAAK;YACL,oHAAoH;YACpH,IAAI;YACJ,OAAO;YACP,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;SACrC;IACF,CAAC;IAEO,+CAAgB,GAAxB,UAAyB,GAAW,EAAE,IAAc;QACnD,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;QACxD,IAAM,IAAI,GAAG,oBAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;QAC1E,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QAC9E,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACxC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACvD,IAAI,IAAI,EAAE,EAAE,SAAS;YACpB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;SAC5H;aAAM;YACN,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;SACtC;IACF,CAAC;IAED,cAAc;IACN,4CAAa,GAArB;;QACC,IAAI,EAAE,GAAG,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,OAAO,CAAC,CAAA;QACvD,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,eAAO,CAAC,OAAO,EAAE;YAC/C,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;SAC5B;QACD,OAAO,EAAE,GAAG,IAAI,CAAA;IACjB,CAAC;IAED,SAAS;IACD,8CAAe,GAAvB,UAAwB,IAAc;;QACrC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;QAC9C,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACzC,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAA7B,CAA6B,CAAC,CAAA;QAClD,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;QAC/C,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,WAAW;QACX,IAAM,UAAU,GAAG,CAAC,GAAG,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,UAAU,CAAC,CAAA;QACxE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,CAAA;QAC/F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,EAAE;gBACZ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC,CAAA;aACpC;iBAAM;gBACN,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAA;gBACzD,IAAI,aAAa,IAAI,CAAC,EAAE;oBACvB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,cAAQ,CAAC,CAAC,CAAA;iBAC9C;gBACD,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;aAC1C;YACD,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,0CAAE,MAAM,KAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAChE,IAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAA;YAC9E,MAAA,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,0CAAE,SAAS,CAAC,GAAG,EAAC;YAC1C,MAAA,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,0CAAE,SAAS,CAAC,GAAG,EAAC;YACzC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;YAC9D,qBAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC9D,IAAI,CAAC,KAAK,CAAC,EAAE;gBACZ,IAAI,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,KAAI,CAAC,CAAA;aAC3B;iBAAM,IAAI,IAAI,EAAE;gBAChB,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,MAAM,CAAC,CAAA;gBACrD,qBAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;gBACtD,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;gBACnC,IAAI,IAAI,KAAK,CAAA;gBACb,MAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,0CAAE,IAAI,GAAE;gBACjC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;gBAChC,QAAQ,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAA;gBAC1C,IAAM,EAAE,GAAG,KAAK,GAAG,KAAK,CAAA;gBACxB,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;gBACvC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA;aAC/E;iBAAM;gBACN,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,EAAE,CAAA;aAC3C;SACD;QACD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAA;QACzC,IAAI,IAAI,GAAG,CAAC,EAAE;YACb,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAA;YACjF,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;SACjE;IACF,CAAC;IAEO,+CAAgB,GAAxB;QAAA,iBAgBC;QAfA,IAAI,oBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE;YACrC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,gBAAgB,CAAC,CAAA;SACnD;QACD,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;QACnC,OAAO,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAC,IAAY;YAC3D,IAAI,KAAI,CAAC,OAAO,EAAE;gBACjB,KAAI,CAAC,cAAc,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,MAAM,EAAE,oBAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAA;gBAClF,KAAI,CAAC,qBAAqB,GAAG,EAAE,CAAA;gBAC/B,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE;oBACrB,KAAI,CAAC,UAAU,GAAG,EAAS,CAAA;iBAC3B;gBACD,KAAI,CAAC,UAAU,CAAC,GAAG,GAAG,KAAI,CAAC,cAAc,CAAC,GAAG,CAAA;gBAC7C,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;aACnD;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,aAAa;IACL,sDAAuB,GAA/B,UAAgC,GAAY;QAC3C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACrC,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC9E,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;YACxB,IAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,aAAa,CAAA;YACtC,EAAE,CAAC,cAAc,EAAE,CAAA;YACnB,IAAI,KAAK,IAAI,CAAC,EAAE;gBACf,EAAE,CAAC,aAAa,EAAE,CAAA;aAClB;YACD,IAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;YACzC,IAAM,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;YAC3C,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;SACrC;IACF,CAAC;IAED,OAAO;IACC,yCAAU,GAAlB,UAAmB,IAAoB;QAAvC,iBA0BC;QAzBA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACf,OAAM;SACN;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,qBAAS,CAAC,iBAAiB,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;;YACnD,IAAI,GAAG,CAAC,GAAG,EAAE;gBACZ,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACpC;iBAAM;gBACN,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;gBACrB,oBAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAC/C,MAAA,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAC;gBACjE,oBAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACjD,oBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;gBAC5B,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,wBAAwB,CAAC,CAAA;gBAC7C,UAAI,IAAI,CAAC,QAAQ,0CAAE,MAAM,EAAE;oBAC1B,uBAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE;wBACxC,IAAI,EAAE,oBAAoB;wBAC1B,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,IAAI,EAAE,IAAI,CAAC,QAAQ;qBACnB,CAAC,CAAA;iBACF;aACD;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IA/xBmB,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CAgyBxC;IAAD,2BAAC;CAhyBD,AAgyBC,CAhyBiD,EAAE,CAAC,WAAW,GAgyB/D;kBAhyBoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ARMY_PAWN_MAX_COUNT, HOSPITAL_PAWN_LIMIT } from \"../../common/constant/Constant\";\nimport { ArmyItem } from \"../../common/constant/DataType\";\nimport { ecode } from \"../../common/constant/ECode\";\nimport { ArmyState, CEffect, CType, PreferenceKey } from \"../../common/constant/Enums\";\nimport { IPawnDrillInfo } from \"../../common/constant/Interface\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { netHelper } from \"../../common/helper/NetHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport AreaCenterModel from \"../../model/area/AreaCenterModel\";\nimport ArmyObj from \"../../model/area/ArmyObj\";\nimport BuildObj from \"../../model/area/BuildObj\";\nimport PawnObj from \"../../model/area/PawnObj\";\nimport CTypeObj from \"../../model/common/CTypeObj\";\nimport UserModel from \"../../model/common/UserModel\";\nimport PawnCureInfoObj from \"../../model/main/PawnCureInfoObj\";\nimport PlayerModel from \"../../model/main/PlayerModel\";\nimport TextButtonCmpt from \"../cmpt/TextButtonCmpt\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class BuildHospitalPnlCtrl extends mc.BasePnlCtrl {\n\n\t//@autocode property begin\n\tprivate tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce\n\tprivate pagesNode_: cc.Node = null // path://root/pages_n\n\tprivate sortSelectNode_: cc.Node = null // path://root/pages_n/1/info/pawn/sort/sort_select_be_n\n\tprivate queueSv_: cc.ScrollView = null // path://root/pages_n/1/cure/content/queue_sv\n\tprivate upTimeNode_: cc.Node = null // path://root/pages_n/1/cure/x/up_time_be_n\n\t//@end\n\n\tprivate PKEY_TAB: string = 'HOSPITAL_TAB'\n\tprivate PKEY_SELECT_ARMY: string = 'HOSPITAL_SELECT_ARMY'\n\tprivate PKEY_SELECT_PAWN: string = 'HOSPITAL_SELECT_PAWN'\n\n\tprivate user: UserModel = null\n\tprivate player: PlayerModel = null\n\tprivate areaCenter: AreaCenterModel = null\n\tprivate data: BuildObj = null\n\n\tprivate cureProgressTween: { [key: number]: cc.Tween } = {}\n\n\tprivate currSelectSort: number = 0 // 当前选择的排序方式\n\tprivate preSelectIndex: number = -1 // 治疗的目标在伤兵中的下标\n\tprivate tempCreateArmy: ArmyObj = null\n\tprivate selectArmy: ArmyItem = null\n\tprivate tempArmySortWeightMap: { [key: string]: number } = {}\n\n\tpublic listenEventMaps() {\n\t\treturn [\n\t\t\t{ [EventType.UPDATE_BUILD_LV]: this.onUpdateBuildLv, enter: true },\n\t\t\t{ [EventType.UPDATE_PAWN_INJURY_QUEUE]: this.onUpdatePawnInjuryQueue, enter: true },\n\t\t\t{ [EventType.UPDATE_PAWN_CURING_QUEUE]: this.onUpdatePawnCuringQueue, enter: true },\n\t\t\t{ [EventType.AREA_BATTLE_BEGIN]: this.onAreaBattleBegin, enter: true },\n\t\t\t{ [EventType.AREA_BATTLE_END]: this.onAreaBattleEnd, enter: true },\n\t\t\t{ [EventType.CHANGE_PAWN_SKIN]: this.onChangePawnSkin, enter: true },\n\t\t\t{ [EventType.CHANGE_PAWN_EQUIP]: this.onChangePawnEquip, enter: true },\n\t\t\t{ [EventType.ADD_ARMY]: this.onUpdateArmy, enter: true },\n\t\t\t{ [EventType.REMOVE_ARMY]: this.onUpdateArmy, enter: true },\n\t\t\t{ [EventType.UPDATE_ARMY]: this.onUpdateArmy, enter: true },\n\t\t\t{ [EventType.UPDATE_ALL_ARMY]: this.onUpdateArmy, enter: true },\n\t\t\t{ [EventType.UPDATE_ARMY_DIST_INFO]: this.onUpdateArmy, enter: true },\n\t\t\t{ [EventType.UPDATE_BATTLE_ARMY_BY_UI]: this.onUpdateArmy, enter: true },\n\t\t]\n\t}\n\n\tpublic async onCreate() {\n\t\tthis.user = this.getModel('user')\n\t\tthis.player = this.getModel('player')\n\t\tthis.areaCenter = this.getModel('areaCenter')\n\t}\n\n\tpublic onEnter(data: BuildObj, tab?: number) {\n\t\tthis.data = data\n\t\tthis.tempCreateArmy = this.user.getTempPreferenceMap(PreferenceKey.TEMP_CREATE_ARMY)\n\t\tconst cond = this.pagesNode_.Child('1/info/cond')\n\t\tcond.Child('need/title/layout/val').setLocaleKey('ui.drill_cost', 'ui.button_cure')\n\t\tthis.pagesNode_.Child('1/cure/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_cure')\n\t\tthis.tabsTc_.Tabs(tab ?? (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0))\n\t\t// 排序选择\n\t\tthis.selectSortItem(this.sortSelectNode_, gameHpr.user.getLocalPreferenceData(PreferenceKey.INJURY_QUEUE_SORT) ?? 5, true)\n\t}\n\n\tpublic onRemove() {\n\t\tthis.selectArmy = null\n\t\tthis.tempArmySortWeightMap = {}\n\t\tviewHelper.closePopupBoxList(this.sortSelectNode_)\n\t\tthis.showCreateArmyFingerTip(false)\n\t\tif (this.currSelectSort !== gameHpr.user.getLocalPreferenceData(PreferenceKey.INJURY_QUEUE_SORT)) {\n\t\t\tgameHpr.user.setLocalPreferenceData(PreferenceKey.INJURY_QUEUE_SORT, this.currSelectSort)\n\t\t}\n\t\tthis.user.setTempPreferenceData(PreferenceKey.TEMP_CREATE_ARMY, this.tempCreateArmy)\n\t}\n\n\tpublic onClean() {\n\t\tassetsMgr.releaseTempResByTag(this.key)\n\t}\n\n\t// ----------------------------------------- button listener function -------------------------------------------\n\t//@autocode button listener\n\n\t// path://root/tabs_tc_tce\n\tonClickTabs(event: cc.Toggle, data: string) {\n\t\t!data && audioMgr.playSFX('click')\n\t\tconst type = Number(event.node.name)\n\t\tthis.user.setTempPreferenceData(this.PKEY_TAB, type)\n\t\tconst node = this.pagesNode_.Swih(type)[0]\n\t\tif (type === 0) {\n\t\t\t// viewHelper.updateBuildBaseUI(node, this.data, this.key)\n\t\t\tviewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key)\n\t\t\tviewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key)\n\t\t} else if (type === 1) {\n\t\t\t// 显示当前的军队列表\n\t\t\tthis.selectArmy = null\n\t\t\tthis.tempArmySortWeightMap = {}\n\t\t\tthis.updateArmyList(true, node)\n\t\t\t// 显示可治疗的士兵\n\t\t\tthis.updateInjuryList(true, node)\n\t\t\t// 费用\n\t\t\tthis.updateCureCost(node)\n\t\t\t// 治疗列表\n\t\t\tthis.updateCureQueue(node)\n\t\t}\n\t}\n\n\t// path://root/pages_n/0/bottom/buttons/up_be\n\tonClickUp(event: cc.Event.EventTouch, data: string) {\n\t\tgameHpr.clickBuildUp(this.data, this)\n\t}\n\n\t// path://root/pages_n/1/info/pawn/list/view/content/pawn_be\n\tonClickPawn(event: cc.Event.EventTouch, _data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tconst it = event.target, data = it.Data, uid = data?.data.uid\n\t\tif (!data) {\n\t\t\treturn\n\t\t} else if (uid === this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)) {\n\t\t\tuid && viewHelper.showPnl('area/PawnInfo', data.pawn)\n\t\t\treturn\n\t\t}\n\t\tthis.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, uid)\n\t\tthis.preSelectIndex = this.player.getInjuryPawns().findIndex(m => m.uid === uid)\n\t\tthis.updatePawnSelect(false)\n\t\tthis.updateCureCost()\n\t}\n\n\t// path://root/pages_n/1/army/list/view/content/army_be\n\tonClickArmy(event: cc.Event.EventTouch, _data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tconst it = event.target, data: ArmyItem = it.Data\n\t\tif (!data) {\n\t\t\tif (this.tempCreateArmy) {\n\t\t\t\treturn viewHelper.showAlert('toast.yet_has_empty_army')\n\t\t\t}\n\t\t\treturn this.showCreateArmyUI()\n\t\t} else if (!data.army) {\n\t\t\treturn viewHelper.showAlert('toast.army_not_in_maincity')\n\t\t} else if (data.uid !== this.selectArmy?.uid) {\n\t\t\tthis.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid)\n\t\t\tthis.updateArmySelect(data)\n\t\t}\n\t}\n\n\t// path://root/pages_n/1/info/cond/need/buttons/state/cure_be\n\tonClickCure(event: cc.Event.EventTouch, data: string) {\n\t\tconst pawnUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)\n\t\tif (!pawnUid) {\n\t\t\treturn\n\t\t}\n\t\tconst area = this.areaCenter.getArea(this.data.aIndex)\n\t\tif (!area) {\n\t\t\treturn\n\t\t} else if (!this.selectArmy) {\n\t\t\tif (area.armys.length === 0 || area.armys.every(m => m.pawns.length >= ARMY_PAWN_MAX_COUNT)) { //一个军队也没有\n\t\t\t\tviewHelper.showAlert('toast.please_create_army', {\n\t\t\t\t\tcb: () => {\n\t\t\t\t\t\tif (this.isValid && !this.player.isArmyCountFull() && !gameHpr.isNoLongerTip('no_army')) {\n\t\t\t\t\t\t\tthis.showCreateArmyFingerTip(true)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\tviewHelper.showAlert('toast.please_select_army')\n\t\t\t}\n\t\t\treturn\n\t\t}\n\t\tlet selectArmyUid = this.selectArmy.uid, tempArmyUid = '', armyName = ''\n\t\tconst army = area.getArmyByUid(selectArmyUid) || this.getTempCreateArmy(selectArmyUid)\n\t\tif (!army) {\n\t\t\treturn viewHelper.showAlert(ecode.ARMY_NOT_EXIST)\n\t\t} else if (army.uid.startsWith('temp_')) {\n\t\t\tarmyName = army.name\n\t\t} else {\n\t\t\ttempArmyUid = army.uid\n\t\t}\n\t\tthis.areaCenter.curePawnToServer(this.data.aIndex, tempArmyUid, armyName, pawnUid).then(res => {\n\t\t\tif (!this.isValid) {\n\t\t\t} else if (res.err === ecode.TEXT_LEN_LIMIT) {\n\t\t\t\treturn viewHelper.showAlert('toast.text_len_limit_name')\n\t\t\t} else if (res.err === ecode.TEXT_HAS_SENSITIVE) {\n\t\t\t\treturn viewHelper.showAlert('toast.has_sensitive_word_name')\n\t\t\t} /* else if (res.err === ecode.ANTI_CHEAT) {\n\t\t\t\tviewHelper.showPnl('main/AntiCheat')\n\t\t\t} */ else if (res.err) {\n\t\t\t\tviewHelper.showAlert(res.err)\n\t\t\t} else {\n\t\t\t\taudioMgr.playSFX('build/sound_ui_00' + (this.data.id === 2010 ? '7' : '6'))\n\t\t\t\tconst army = res.army\n\t\t\t\tif (this.tempCreateArmy?.uid === selectArmyUid) {\n\t\t\t\t\tthis.tempCreateArmy = null\n\t\t\t\t\tif (this.selectArmy) this.selectArmy.uid = army.uid\n\t\t\t\t\tif (this.tempArmySortWeightMap[selectArmyUid]) {\n\t\t\t\t\t\tthis.tempArmySortWeightMap[army.uid] = this.tempArmySortWeightMap[selectArmyUid]\n\t\t\t\t\t\tdelete this.tempArmySortWeightMap[selectArmyUid]\n\t\t\t\t\t}\n\t\t\t\t\tthis.player.getBaseArmys().push({\n\t\t\t\t\t\tindex: this.data.aIndex,\n\t\t\t\t\t\tuid: army.uid,\n\t\t\t\t\t\tname: army.name, //军队名字\n\t\t\t\t\t\tstate: ArmyState.CURING,\n\t\t\t\t\t\tpawns: [], //士兵列表\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tconst node = this.pagesNode_.Child(1)\n\t\t\t\tthis.updateCureQueue(node)\n\t\t\t\tthis.updateInjuryList(false, node)\n\t\t\t\t// this.updateArmyList(false, node)\n\t\t\t\t// this.updateCureCost(node)\n\t\t\t\tthis.updateCureCost(node)\n\t\t\t}\n\t\t})\n\t}\n\n\t// path://root/pages_n/1/cure/content/0/cure_pawn_be\n\tonClickCurePawn(event: cc.Event.EventTouch, _data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tconst data: PawnCureInfoObj = event.target.parent.Data\n\t\tif (data) {\n\t\t\tviewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByCureInfo(data), data)\n\t\t}\n\t}\n\n\t// path://root/pages_n/1/info/cond/need/buttons/delete_be\n\tonClickDelete(event: cc.Event.EventTouch, _data: string) {\n\t\tconst uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN) || ''\n\t\tif (uid) {\n\t\t\tif (this.player.getCuringPawnsQueue().some(m => m.uid === uid)) { // 治疗中无法删除\n\t\t\t\tthis.updateCureButton(uid)\n\t\t\t\treturn viewHelper.showAlert('toast.delete_curing_pawn_tip')\n\t\t\t}\n\t\t\tconst data = this.player.getInjuryPawns().find(m => m.uid === uid)\n\t\t\tif (data) {\n\t\t\t\tviewHelper.showMessageBox('ui.giveup_cure_tip', {\n\t\t\t\t\tparams: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv])],\n\t\t\t\t\tok: () => this.isValid && this.giveUpCure(uid),\n\t\t\t\t\tcancel: () => { },\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\t// path://root/pages_n/1/info/pawn/sort/sort_select_be_n\n\tonClickSortSelect(event: cc.Event.EventTouch, data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tviewHelper.changePopupBoxList(event.target, true)\n\t}\n\n\t// path://root/pages_n/1/info/pawn/sort/sort_select_be_n/select_mask_be\n\tonClickSelectMask(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.changePopupBoxList(event.target.parent, false)\n\t}\n\n\t// path://root/pages_n/1/info/pawn/sort/sort_select_be_n/mask/root/sort_items_nbe\n\tonClickSortItems(event: cc.Event.EventTouch, data: string) {\n\t\tconst node = this.sortSelectNode_\n\t\tviewHelper.changePopupBoxList(node, false)\n\t\tconst type = Number(event.target.name)\n\t\tif (type !== this.currSelectSort) {\n\t\t\tthis.selectSortItem(node, type)\n\t\t}\n\t}\n\n\t// path://root/pages_n/1/cure/x/up_time_be_n\n\tonClickUpTime(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.showPnl('build/SpeedUpCure', this.data)\n\t}\n\n\t// path://root/pages_n/1/info/cond/need/title/layout/view_cured_be\n\tonClickViewCured(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.showPnl('common/Desc', { text: 'ui.cured_count_desc' })\n\t}\n\n\t// path://root/pages_n/1/cure/content/0/cancel_cure_be\n\tonClickCancelCure(event: cc.Event.EventTouch, _data: string) {\n\t\tconst data = event.target.parent.Data\n\t\tif (!data) {\n\t\t\treturn\n\t\t} else if (data.surplusTime > 0) {\n\t\t\treturn viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {\n\t\t\t\tok: () => this.isValid && this.cancelCure(data),\n\t\t\t\tcancel: () => { },\n\t\t\t})\n\t\t}\n\t\tthis.cancelCure(data)\n\t}\n\n\t// path://root/pages_n/0/info/chance_be\n\tonClickChance(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.showPnl('build/HospitalChanceDesc')\n\t}\n\t//@end\n\t// ----------------------------------------- event listener function --------------------------------------------\n\n\tprivate onUpdateBuildLv(data: BuildObj) {\n\t\tif (this.data.uid === data.uid) {\n\t\t\tconst node = this.pagesNode_.Child(0)\n\t\t\tnode.Child('lv').setLocaleKey('ui.lv', data.lv)\n\t\t\tviewHelper.updateBuildAttrInfo(node, data)\n\t\t}\n\t}\n\n\tprivate onUpdatePawnInjuryQueue() {\n\t\tthis.updateInjuryList(false)\n\t}\n\n\t// 刷新治疗列表\n\tprivate onUpdatePawnCuringQueue() {\n\t\tthis.updateCureQueue()\n\t\t// this.updateArmyList(false)\n\t\t// this.updateCureCost()\n\t}\n\n\t// 战斗开始\n\tprivate onAreaBattleBegin(index: number) {\n\t\tif (this.data.aIndex === index) {\n\t\t\tthis.updateCureQueue()\n\t\t}\n\t}\n\n\t// 战斗结束\n\tprivate onAreaBattleEnd(index: number) {\n\t\tif (this.data.aIndex === index) {\n\t\t\tthis.updateCureQueue()\n\t\t}\n\t}\n\n\t// 切换士兵皮肤\n\tprivate onChangePawnSkin(data: PawnObj) {\n\t\tthis.updateInjuryList(false)\n\t\tthis.updateCureQueue()\n\t}\n\n\t// 切换士兵装备\n\tprivate onChangePawnEquip() {\n\t\tthis.updateInjuryList(false)\n\t}\n\n\t// 重新刷新军队列表\n\tprivate onUpdateArmy() {\n\t\tthis.updateArmyList(false)\n\t}\n\n\t// ----------------------------------------- custom function ----------------------------------------------------\n\tprivate getTempCreateArmy(uid: string) {\n\t\tif (this.tempCreateArmy?.uid === uid) {\n\t\t\treturn this.tempCreateArmy\n\t\t}\n\t\treturn null\n\t}\n\n\t// 选择排序\n\tprivate selectSortItem(node: cc.Node, type: number, init?: boolean) {\n\t\tnode.Data = this.currSelectSort = type\n\t\tnode.Child('val', cc.Label).setLocaleKey('ui.portrayal_list_sort_' + type)\n\t\tnode.Child('mask/root/sort_items_nbe').children.forEach(m => {\n\t\t\tconst select = Number(m.name) === type\n\t\t\t// m.Child('val').Color(select ? '#E6DCC8' : '#B6A591')\n\t\t\tm.Child('select').active = select\n\t\t})\n\t\tif (!init) {\n\t\t\tthis.updateInjuryList(false)\n\t\t}\n\t}\n\n\t// 放弃治疗\n\tprivate giveUpCure(uid: string) {\n\t\tnetHelper.reqGiveUpInjuryPawn({ uid }).then(res => {\n\t\t\tif (res.err) {\n\t\t\t\treturn viewHelper.showAlert(res.err)\n\t\t\t} else {\n\t\t\t\tconst data = res.data\n\t\t\t\tthis.player.updateInjuryPawns(data.injuryPawns)\n\t\t\t\tthis.updateInjuryList(false)\n\t\t\t\tconst selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)\n\t\t\t\tthis.updateCureButton(selectUid)\n\t\t\t}\n\t\t})\n\t}\n\n\tprivate addArmyToList(data: any, army: ArmyObj, pawns?: any[]) {\n\t\tconst item: ArmyItem = {\n\t\t\tname: data.name,\n\t\t\tuid: data.uid,\n\t\t\tpawns,\n\t\t\tarmy\n\t\t}\n\t\tif (!this.tempArmySortWeightMap[data.uid]) {\n\t\t\tlet weight = item.army ? 2 : 1\n\t\t\tweight = weight * 10 + (9 - (item.army?.getActPawnCount() || 0))\n\t\t\tweight = weight * 10 + (9 - (item.army?.pawns.length || 0))\n\t\t\tthis.tempArmySortWeightMap[data.uid] = weight\n\t\t}\n\t\treturn item\n\t}\n\n\t// 刷新军队列表\n\tprivate updateArmyList(isLocation: boolean, node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\t// 当前区域的军队\n\t\tconst areaArmyMap: { [key: string]: ArmyObj } = {}\n\t\tthis.areaCenter.getArea(this.data.aIndex)?.armys.forEach(m => {\n\t\t\tif (m.isCanDrillPawn()) {\n\t\t\t\tareaArmyMap[m.uid] = m\n\t\t\t}\n\t\t})\n\t\tconst armys: ArmyItem[] = [null]\n\t\t// 先装自己所有的军队 再装临时创建的军队\n\t\tthis.player.getBaseArmys().forEach(m => armys.push(this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)))\n\t\tif (this.tempCreateArmy) {\n\t\t\tarmys.push(this.addArmyToList(this.tempCreateArmy, this.tempCreateArmy))\n\t\t}\n\t\t// 排个序\n\t\tarmys.sort((a, b) => this.tempArmySortWeightMap[b?.uid] - this.tempArmySortWeightMap[a?.uid])\n\t\tconst countNode = node.Child('army/title/count_bg')\n\t\tcountNode.Child('cur', cc.Label).string = armys.length + ''\n\t\tcountNode.Child('max', cc.Label).string = '/' + this.player.getArmyMaxCount()\n\t\tconst uid = this.selectArmy?.uid ?? this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY)\n\t\tlet curArmy = uid ? armys.find(m => !!m?.army && m?.uid === uid) : null, index = -1\n\t\tconst sv = node.Child('army/list', cc.ScrollView)\n\t\tsv.stopAutoScroll()\n\t\t// armys.push(null)\n\t\tsv.Items(armys, (it, data, i) => {\n\t\t\tit.Data = data\n\t\t\tconst army = data?.army\n\t\t\tconst root = it.Child('root')\n\t\t\troot.Child('add').active = !data\n\t\t\troot.Child('count').active = !!data\n\t\t\troot.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : ''\n\t\t\tconst state = root.Child('state')\n\t\t\tif (army) {\n\t\t\t\troot.Child('count/val', cc.Label).string = (data.pawns?.length || 0) + ''\n\t\t\t\tconst addLbl = root.Child('count/add', cc.Label), dpc = army.drillPawns.length + army.curingPawns.length\n\t\t\t\tif (addLbl.node.active = dpc > 0) {\n\t\t\t\t\taddLbl.string = '+' + dpc\n\t\t\t\t}\n\t\t\t\tconst isFull = state.active = army.getActPawnCount() >= ARMY_PAWN_MAX_COUNT\n\t\t\t\troot.opacity = isFull ? 150 : 255\n\t\t\t\tif (isFull) {\n\t\t\t\t\tstate.setLocaleKey('ui.yet_full')\n\t\t\t\t}\n\t\t\t\t// 显示选择\n\t\t\t\tif (!curArmy && isFull) {\n\t\t\t\t} else if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {\n\t\t\t\t\tcurArmy = data\n\t\t\t\t\tindex = i\n\t\t\t\t\tthis.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, army.uid)\n\t\t\t\t}\n\t\t\t} else if (data) {\n\t\t\t\troot.opacity = 150\n\t\t\t\troot.Child('count/val', cc.Label).string = (data.pawns?.length || 0) + ''\n\t\t\t\troot.Child('count/add').active = false\n\t\t\t\tstate.active = true\n\t\t\t\tstate.setLocaleKey('ui.go_out')\n\t\t\t} else {\n\t\t\t\troot.opacity = 255\n\t\t\t\tstate.active = false\n\t\t\t}\n\t\t})\n\t\t// 将选中的移动到中间\n\t\tif (isLocation) {\n\t\t\tsv.SelectItemToCentre(index)\n\t\t}\n\t\t// 刷新选中\n\t\tthis.updateArmySelect(curArmy, node)\n\t\t/* // 将选中的移动到中间\n\t\tif (index !== -1) {\n\t\t\tconst lay = sv.content.Component(cc.Layout)\n\t\t\tlay.updateLayout()\n\t\t\tconst width = sv.content.children[0].width\n\t\t\tconst tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft //当前位置\n\t\t\tconst pw = sv.content.parent.width\n\t\t\tconst cx = pw * 0.5 //中间位置\n\t\t\tsv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0)\n\t\t} else {\n\t\t\tsv.scrollToLeft()\n\t\t} */\n\t}\n\n\tprivate updateArmySelect(item: ArmyItem, node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\tthis.selectArmy = item\n\t\tconst uid = item?.uid || ''\n\t\tnode.Child('army/list', cc.ScrollView).content.children.forEach(m => {\n\t\t\tconst select = m.Child('select').active = m.Data?.uid === uid\n\t\t\tm.Component(cc.Button).interactable = !select\n\t\t})\n\t\tconst army = item?.army, pawns = []\n\t\tif (army) {\n\t\t\tpawns.pushArr(army.pawns)\n\t\t\tpawns.pushArr(army.curingPawns)\n\t\t\tpawns.pushArr(army.drillPawns)\n\t\t} else if (item?.pawns) {\n\t\t\tpawns.pushArr(item.pawns)\n\t\t}\n\t\t// 刷新士兵列表\n\t\tnode.Child('info/army_pawns').children.forEach((it, i) => {\n\t\t\tconst data = pawns[i], isId = typeof (data) === 'number', isCuring = !!data && !!army?.curingPawns.some(m => m.uid === data.uid)\n\t\t\tit.Swih('none', !!data)\n\t\t\tif (data) {\n\t\t\t\tconst icon = it.Child('icon')\n\t\t\t\ticon.opacity = (isId || isCuring) ? 120 : 255\n\t\t\t\tresHelper.loadPawnHeadMiniIcon(isId ? data : (data.portrayal?.id || data.id), icon, this.key)\n\t\t\t\tit.Child('lv', cc.Label).string = isId || data.lv <= 1 ? '' : data.lv + ''\n\t\t\t}\n\t\t})\n\t\t// 刷新按钮\n\t\tconst buttons = node.Child('info/cond/need/buttons'), button = buttons.Child('state').Swih('cure_be')[0]\n\t\tbutton.Data = army ? (army.getActPawnCount() >= ARMY_PAWN_MAX_COUNT ? 1 : 0) : 2\n\t\tbutton.opacity = !!item && button.Data ? 120 : 255\n\t\tif (pawns.length < ARMY_PAWN_MAX_COUNT) {\n\t\t\tconst uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)\n\t\t\tthis.updateCureButton(uid, buttons.parent)\n\t\t}\n\t}\n\n\t// 刷新士兵列表\n\tprivate updateInjuryList(isLocation: boolean, node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\tconst pawns = this.player.getInjuryPawns(), pawnCount = pawns.length\n\t\tconst mapObj = {}\n\t\tthis.player.getCuringPawnsQueue().forEach(m => mapObj[m.uid] = true)\n\t\tpawns.sort((a, b) => {\n\t\t\tconst aState = mapObj[a.uid] ? 1 : 0, bState = mapObj[b.uid] ? 1 : 0\n\t\t\tif (aState !== bState) {\n\t\t\t\treturn aState - bState\n\t\t\t}\n\t\t\tswitch (this.currSelectSort) {\n\t\t\t\tcase 0: // 兵种升序 > 等级降序 > 时间降序\n\t\t\t\t\tif (a.id !== b.id) return a.id - b.id\n\t\t\t\t\tif (a.lv !== b.lv) return b.lv - a.lv\n\t\t\t\t\treturn b.deadTime - a.deadTime\n\t\t\t\tcase 4: // 时间降序 > 兵种升序 > 等级降序 \n\t\t\t\t\tif (a.deadTime !== b.deadTime) return b.deadTime - a.deadTime\n\t\t\t\t\tif (a.id !== b.id) return a.id - b.id\n\t\t\t\t\treturn b.lv - a.lv\n\t\t\t\tcase 5: // 等级降序  > 兵种升序 > 时间降序\n\t\t\t\t\tif (a.lv !== b.lv) return b.lv - a.lv\n\t\t\t\t\tif (a.id !== b.id) return a.id - b.id\n\t\t\t\t\treturn b.deadTime - a.deadTime\n\t\t\t}\n\t\t\treturn b.lv - a.lv\n\t\t})\n\t\tlet selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN), nextUid = selectUid\n\t\tif (!pawns.some(m => m.uid === selectUid) || mapObj[selectUid]) { // 士兵治疗完成 或 士兵治疗中，需要自动切换治疗目标\n\t\t\tnextUid = 0\n\t\t}\n\t\tconst curingQueue = this.player.getCuringPawnsQueue()\n\t\tconst sv = node.Child('info/pawn/list', cc.ScrollView)\n\t\tsv.Child('empty').active = !pawnCount\n\t\tsv.Items(pawns, (it, data, i) => {\n\t\t\tconst conf = this.player.getConfigPawnInfo(data.id),\n\t\t\t\tpawn = new PawnObj().init(data.id, conf.equip, data.lv, conf.skinId)\n\t\t\tit.Data = { data: data, pawn: pawn }\n\t\t\tit.Child('icon').opacity = curingQueue.some(m => m.uid === data.uid) ? 120 : 255\n\t\t\tit.Child('lv/val', cc.Label).string = data.lv + ''\n\t\t\tconst iconNode = it.Child('icon')\n\t\t\tresHelper.loadPawnHeadIcon(conf.skinId || data.id, iconNode, this.key)\n\t\t\tif (i >= this.preSelectIndex && !nextUid && !mapObj[data.uid]) {\n\t\t\t\tnextUid = this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, data.uid)\n\t\t\t}\n\t\t})\n\t\t// 没有找到下一个，就找上一个\n\t\tif (!nextUid) {\n\t\t\tfor (let i = pawns.length - 1; i >= 0; i--) {\n\t\t\t\tconst pawn = pawns[i]\n\t\t\t\tif (!nextUid && i <= this.preSelectIndex && !mapObj[pawn.uid]) {\n\t\t\t\t\tnextUid = this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, pawn.uid)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t// 将选中的移动到中间\n\t\tif (this.preSelectIndex !== -1) {\n\t\t\tconst lay = sv.content.Component(cc.Layout)\n\t\t\tlay.updateLayout()\n\t\t\tconst width = sv.content.children[0].width\n\t\t\tconst tx = (width + lay.spacingX) * this.preSelectIndex + width * 0.5 + lay.paddingLeft //当前位置\n\t\t\tconst pw = sv.content.parent.width\n\t\t\tconst cx = pw * 0.5 //中间位置\n\t\t\tsv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0)\n\t\t} else {\n\t\t\tsv.scrollToLeft()\n\t\t}\n\t\tthis.updatePawnSelect(isLocation, node)\n\t\tif (pawnCount <= 0) {\n\t\t\tthis.updateCureCost()\n\t\t}\n\t\t// 刷新数量\n\t\tthis.pagesNode_.Child('1/info/pawn/title/val').setLocaleKey('ui.select_wounded', pawnCount, HOSPITAL_PAWN_LIMIT)\n\t}\n\n\tprivate updatePawnSelect(isLocation: boolean, node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\tconst selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)\n\t\tlet selectIndex = -1\n\t\tconst sv = node.Child('info/pawn/list', cc.ScrollView)\n\t\tsv.content.children.forEach((m, i) => {\n\t\t\t/* const uid = m.Data?.data?.uid\n\t\t\tm.Child('bg/select').active = m.Child('select').active = uid === selectUid */\n\t\t\tconst select = m.Child('bg/select').active = m.Child('select').active = m.Data?.data.uid === selectUid\n\t\t\tm.Component(cc.Button).interactable = !select || !!m.Data?.pawn\n\t\t\tif (select) {\n\t\t\t\tselectIndex = i\n\t\t\t}\n\t\t})\n\t\tif (isLocation) {\n\t\t\tsv.SelectItemToCentre(selectIndex)\n\t\t}\n\t}\n\n\t// 刷新治疗费用\n\tprivate updateCureCost(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\tconst uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)\n\t\tconst data = node.Child('info/pawn/list', cc.ScrollView).Find(m => m.Data?.data?.uid === uid)?.Data?.data\n\t\tconst need = node.Child('info/cond/need'), empty = node.Child('info/cond/empty')\n\t\tempty.active = !data\n\t\tif (need.active = !!data) {\n\t\t\t// 计算当前等级总耗时、总耗资\n\t\t\t// (招募三资 + 训练三资) * (1.0 + 治疗次数 * 0.2)\n\t\t\t// (招募时间 + 训练时间) * (0.5 + 阵亡次数 * 0.5) * (1 - 建筑等级  * 0.02)\n\t\t\tconst baseCfg = assetsMgr.getJsonData('pawnBase', data.id)\n\t\t\tlet cost = [], cureTime = baseCfg.drill_time\n\t\t\tcost.pushArr(gameHpr.stringToCTypes(baseCfg.drill_cost))\n\t\t\tfor (let i = 1; i < data.lv; i++) {\n\t\t\t\tconst cfg = assetsMgr.getJsonData('pawnAttr', data.id * 1000 + i)\n\t\t\t\tcost.pushArr(gameHpr.stringToCTypes(cfg.lv_cost))\n\t\t\t\tcureTime += cfg.lv_time\n\t\t\t}\n\t\t\t// 粮耗\n\t\t\tconst crealCost = new CTypeObj().init(CType.CEREAL_C, 0, baseCfg.cereal_cost || 0)\n\t\t\tcost.push(crealCost)\n\t\t\t// // 检测是否有治疗士兵费用增加\n\t\t\t// cost = gameHpr.world.getSeason().changeBaseResCost(CEffect.CURE_COST, cost)\n\t\t\tconst finalCost: CTypeObj[] = []\n\t\t\tgameHpr.mergeTypeObjsCount(finalCost, ...cost)\n\t\t\t// 剔除经验书，后续用来加速\n\t\t\tfor (let i = finalCost.length - 1; i >= 0; i--) {\n\t\t\t\tif (finalCost[i].type === CType.EXP_BOOK) {\n\t\t\t\t\tfinalCost.splice(i, 1)\n\t\t\t\t}\n\t\t\t}\n\t\t\tfinalCost.forEach(m => m.type !== CType.CEREAL_C && (m.count = Math.floor(m.count * (1 + data.cureCount * 0.2))))\n\t\t\tcureTime = cureTime * (0.5 + data.cureCount * 0.5)\n\t\t\t// 治疗消耗信息\n\t\t\tconst cd = this.getCureTimeCD(), policyFreeCount = this.player.getFreeCurePawnSurplusCount()\n\t\t\tviewHelper.updateFreeCostView(need, finalCost, cureTime, cd, false, policyFreeCount)\n\t\t\tneed.Child('buttons/state/cure_be/val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_cure' : 'ui.button_cure')\n\t\t\tconst curedCount = need.Child('title/layout/view_cured_be'), has = !!data.cureCount\n\t\t\tcuredCount.active = has\n\t\t\thas && curedCount.Component(TextButtonCmpt).setKey('ui.button_cured_count', [data.cureCount])\n\t\t\t// need.Child('cost').Items(finalCost || [], (it, cost) => viewHelper.updateCostViewOne(it, cost, true))\n\t\t\t// if (need.Child('time')?.setActive(!!cureTime)) {\n\t\t\t// \tconst up = need.Child('time/up', cc.Label)\n\t\t\t// \tif (up?.setActive(!!cd)) {\n\t\t\t// \t\tup.string = `(-${Math.floor(cd * 100)}%)`\n\t\t\t// \t\tcureTime = Math.max(3, Math.floor(cureTime * (1 - cd)))\n\t\t\t// \t}\n\t\t\t// \tneed.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(cureTime, 'h:mm:ss')\n\t\t\t// }\n\t\t\t// 刷新按钮\n\t\t\tthis.updateCureButton(data.uid, need)\n\t\t}\n\t}\n\n\tprivate updateCureButton(uid: string, node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child('1/info/cond/need')\n\t\tconst info = gameHpr.player.getCuringPawnsQueue().find(m => m.uid === uid)\n\t\tconst buttons = node.Child('buttons'), button = buttons.Child('state/cure_be')\n\t\tbutton.opacity = button.Data ? 120 : 255\n\t\tbuttons.Child('delete_be').opacity = !!info ? 120 : 255\n\t\tif (info) { // 在治疗队列中\n\t\t\tbuttons.Child('state').Swih('curing')[0].Child('val').setLocaleKey(info.surplusTime > 0 ? 'ui.army_state_6' : 'ui.queueing')\n\t\t} else {\n\t\t\tbuttons.Child('state').Swih('cure_be')\n\t\t}\n\t}\n\n\t// 获取治疗减CDBuff\n\tprivate getCureTimeCD() {\n\t\tlet cd = gameHpr.getPlayerPolicyEffect(CEffect.CURE_CD)\n\t\tif (this.data.effect?.type === CEffect.CURE_CD) {\n\t\t\tcd += this.data.effect.value\n\t\t}\n\t\treturn cd * 0.01\n\t}\n\n\t// 刷新治疗列表\n\tprivate updateCureQueue(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\tconst list = this.player.getCuringPawnsQueue()\n\t\tthis.upTimeNode_.active = list.length > 0\n\t\tlist.sort((a, b) => b.surplusTime - a.surplusTime)\n\t\tconst pawnConf = this.player.getConfigPawnMap()\n\t\tlet time = 0\n\t\t// 是否有政策的加成\n\t\tconst queueCount = 6 + gameHpr.getPlayerPolicyEffect(CEffect.CURE_QUEUE)\n\t\tnode.Child('cure/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')'\n\t\tfor (let i = 0; i < queueCount; i++) {\n\t\t\tlet it = null, data = list[i]\n\t\t\tif (i === 0) {\n\t\t\t\tit = node.Child('cure/content/' + i)\n\t\t\t} else {\n\t\t\t\tconst childrenCount = this.queueSv_.content.childrenCount\n\t\t\t\tif (childrenCount <= 1) {\n\t\t\t\t\tthis.queueSv_.Items(queueCount - 1, () => { })\n\t\t\t\t}\n\t\t\t\tit = this.queueSv_.content.children[i - 1]\n\t\t\t}\n\t\t\tit.Data = data\n\t\t\tconst skinId = data ? (pawnConf[data.id]?.skinId || data.id) : 0\n\t\t\tconst has = it.Child('icon').active = it.Child('cure_pawn_be').active = !!data\n\t\t\tit.Child('cancel_cure_be')?.setActive(has)\n\t\t\tit.Child('icon/progress')?.setActive(has)\n\t\t\tit.Child('lv/val', cc.Label).string = data ? data.lv + '' : ''\n\t\t\tresHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key)\n\t\t\tif (i !== 0) {\n\t\t\t\ttime += data?.needTime || 0\n\t\t\t} else if (data) {\n\t\t\t\tconst progress = it.Child('icon/progress', cc.Sprite)\n\t\t\t\tresHelper.loadPawnHeadIcon(skinId, progress, this.key)\n\t\t\t\tconst stime = data.getSurplusTime()\n\t\t\t\ttime += stime\n\t\t\t\tthis.cureProgressTween[i]?.stop()\n\t\t\t\tthis.cureProgressTween[i] = null\n\t\t\t\tprogress.fillRange = stime / data.needTime\n\t\t\t\tconst st = stime * 0.001\n\t\t\t\tit.Child('time', cc.LabelTimer).run(st)\n\t\t\t\tthis.cureProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start()\n\t\t\t} else {\n\t\t\t\tit.Child('time', cc.LabelTimer).string = ''\n\t\t\t}\n\t\t}\n\t\tnode.Child('cure/desc').active = time > 0\n\t\tif (time > 0) {\n\t\t\tnode.Child('cure/desc/title').setLocaleKey('ui.drill_all_desc', 'ui.button_cure')\n\t\t\tnode.Child('cure/desc/time/val', cc.LabelTimer).run(time * 0.001)\n\t\t}\n\t}\n\n\tprivate showCreateArmyUI() {\n\t\tif (gameHpr.player.isArmyCountFull()) {\n\t\t\treturn viewHelper.showAlert(ecode.PLAYER_FULL_ARMY)\n\t\t}\n\t\tthis.showCreateArmyFingerTip(false)\n\t\treturn viewHelper.showPnl('common/CreateArmy', (name: string) => {\n\t\t\tif (this.isValid) {\n\t\t\t\tthis.tempCreateArmy = new ArmyObj().init(this.data.aIndex, gameHpr.getUid(), name)\n\t\t\t\tthis.tempArmySortWeightMap = {}\n\t\t\t\tif (!this.selectArmy) {\n\t\t\t\t\tthis.selectArmy = {} as any\n\t\t\t\t}\n\t\t\t\tthis.selectArmy.uid = this.tempCreateArmy.uid\n\t\t\t\tthis.updateArmyList(true, this.pagesNode_.Child(1))\n\t\t\t}\n\t\t})\n\t}\n\n\t// 显示创建军队提示手指\n\tprivate showCreateArmyFingerTip(val: boolean) {\n\t\tconst node = this.pagesNode_.Child(1)\n\t\tconst sv = node.Child('army/list', cc.ScrollView), finger = sv.Child('finger')\n\t\tif (finger.active = val) {\n\t\t\tconst count = sv.content.childrenCount\n\t\t\tsv.stopAutoScroll()\n\t\t\tif (count >= 4) {\n\t\t\t\tsv.scrollToRight()\n\t\t\t}\n\t\t\tconst it = sv.content.children[count - 1]\n\t\t\tconst pos = ut.convertToNodeAR(it, sv.node)\n\t\t\tfinger.setPosition(pos.x, pos.y - 12)\n\t\t}\n\t}\n\n\t// 取消治疗\n\tprivate cancelCure(info: IPawnDrillInfo) {\n\t\tif (!this.data) {\n\t\t\treturn\n\t\t}\n\t\tconst index = info.index\n\t\tconst uid = info.uid\n\t\tconst json = info.json\n\t\tnetHelper.reqCancelCurePawn({ index, uid }).then(res => {\n\t\t\tif (res.err) {\n\t\t\t\treturn viewHelper.showAlert(res.err)\n\t\t\t} else {\n\t\t\t\tconst data = res.data\n\t\t\t\tgameHpr.player.updateOutputByFlags(data.output)\n\t\t\t\tgameHpr.areaCenter.getArea(index)?.updateArmyCurePawns(data.army)\n\t\t\t\tgameHpr.player.updatePawnCuringQueue(data.queues)\n\t\t\t\tgameHpr.delMessageByTag(uid)\n\t\t\t\tthis.emit(EventType.UPDATE_PAWN_INJURY_QUEUE)\n\t\t\t\tif (data.needCost?.length) {\n\t\t\t\t\tviewHelper.showPnl('common/CancelDrill', {\n\t\t\t\t\t\ttext: 'ui.cancel_cure_tip',\n\t\t\t\t\t\tid: json.id,\n\t\t\t\t\t\tcost: data.needCost,\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t}\n}\n"]}