{"version": 3, "sources": ["assets\\app\\script\\model\\area\\AreaCenterModel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,qCAA+B;AAC/B,qDAAmE;AACnE,0DAAoD;AACpD,6DAAwD;AACxD,qDAAmD;AACnD,2DAAyD;AACzD,qCAA+B;AAI/B,2EAAyE;AACzE,6DAA2D;AAG3D;;GAEG;AAEH;IAA6C,mCAAY;IAAzD;QAAA,qEA8XC;QA5XoB,wBAAkB,GAAW,EAAE,GAAG,IAAI,GAAG,CAAC,CAAA,CAAC,UAAU;QAE9D,WAAK,GAAyB,IAAI,GAAG,EAAE,CAAA;QACvC,kBAAY,GAAiD,IAAI,GAAG,EAAE,CAAA,CAAC,QAAQ;QAE/E,SAAG,GAAiB,IAAI,CAAA;QACxB,YAAM,GAAgB,IAAI,CAAA;QAE1B,cAAQ,GAAY,IAAI,CAAA,CAAC,SAAS;;IAoX9C,CAAC;IAlXU,kCAAQ,GAAf;QACI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IACzC,CAAC;IAED,QAAQ;IACD,sCAAY,GAAnB;QACI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;QACjE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;IACnE,CAAC;IAEM,+BAAK,GAAZ;QACI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;QAClE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAChE,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;IAC7B,CAAC;IAEM,iCAAO,GAAd,UAAe,KAAa;;QACxB,IAAI,OAAA,IAAI,CAAC,QAAQ,0CAAE,KAAK,MAAK,KAAK,EAAE;YAChC,OAAO,IAAI,CAAC,QAAQ,CAAA;SACvB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,oBAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAA;IACxD,CAAC;IAEM,iCAAO,GAAd,UAAe,IAAa;QACxB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAChC,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,oCAAU,GAAjB,UAAkB,KAAa;;QAC3B,MAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,KAAK,GAAE;QAC5B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACvB,CAAC;IAEO,iCAAO,GAAf,UAAgB,KAAa;QACzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACxB,qBAAS,CAAC,aAAa,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,CAAA;IACtC,CAAC;IAEM,oCAAU,GAAjB;;QACI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,CAAA;QAClC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QAClB,IAAI,OAAA,IAAI,CAAC,QAAQ,0CAAE,KAAK,KAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;SACvB;IACL,CAAC;IAED,cAAc;IACP,qCAAW,GAAlB;;QACI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,aAAC,oBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,0CAAE,KAAK,mCAAI,CAAC,CAAC,CAAC,CAAA;SACzE;QACD,OAAO,IAAI,CAAC,QAAQ,CAAA;IACxB,CAAC;IACM,qCAAW,GAAlB,UAAmB,IAAa;QAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;IACxB,CAAC;IAED,WAAW;IACJ,uCAAa,GAApB;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAA;IACzD,CAAC;IAED,OAAO;IACA,iCAAO,GAAd,UAAe,KAAa,EAAE,GAAW;;QACrC,aAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,YAAY,CAAC,GAAG,EAAC;IACjD,CAAC;IAED,SAAS;IACK,qCAAW,GAAzB,UAA0B,KAAa,EAAE,MAAgB;;;;;;4BAC/B,qBAAM,qBAAS,CAAC,cAAc,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,EAAA;;wBAAzD,KAAgB,SAAyC,EAAvD,GAAG,SAAA,EAAE,IAAI,UAAA;wBACX,QAAQ,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAA;wBAC3B,IAAI,CAAC,QAAQ,EAAE;4BACX,sBAAO,IAAI,EAAA;yBACd;6BAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;4BACxB,IAAI,CAAC,qBAAqB,EAAE,CAAA;yBAC/B;wBACK,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;wBACzC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;wBAC3B,QAAQ;wBACR,IAAI,QAAQ,CAAC,MAAM,EAAE;4BACjB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAA;yBAC7C;wBACD,6BAA6B;wBAC7B,IAAI,IAAI,CAAC,KAAK,IAAI,OAAA,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,0CAAE,KAAK,MAAK,IAAI,CAAC,KAAK,EAAE;4BAC5E,oBAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;yBAC1C;wBACD,sBAAO,IAAI,EAAA;;;;KACd;IAED,cAAc;IACD,wCAAc,GAA3B,UAA4B,KAAa,EAAE,MAAgB;;;;;;;wBACvD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;4BACd,sBAAO,IAAI,EAAA;yBACd;wBACK,IAAI,GAAG,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;wBAC7C,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAA;wBAC9B,KAAK,eAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,KAAK,mCAAI,KAAK,CAAA,CAAC,oBAAoB;wBACnD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;6BAC5B,CAAA,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAA,EAA7B,wBAA6B;wBACtB,qBAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAA;;wBAA5C,IAAI,GAAG,SAAqC,CAAA;;4BAEhD,sBAAO,IAAI,EAAA;;;;KACd;IAED,OAAO;IACM,yCAAe,GAA5B,UAA6B,IAAc;;;;;;wBACvC,IAAI,CAAC,oBAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;4BACnC,sBAAO,aAAK,CAAC,cAAc,EAAA;yBAC9B;6BAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE;4BACrD,sBAAO,aAAK,CAAC,cAAc,EAAA;yBAC9B;wBACqB,qBAAM,qBAAS,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAA;;wBAArF,KAAgB,SAAqE,EAAnF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,sBAAO,GAAG,EAAA;yBACb;wBACD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBAC5C,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBACtC,SAAS;wBACT,IAAI,IAAI,CAAC,EAAE,KAAK,iBAAS,CAAC,IAAI,EAAE;4BAC5B,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;gCACf,qCAAiB,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAA;6BACrD;iCAAM,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;gCACtB,qCAAiB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAA;6BACtD;iCAAM,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;gCACvB,qCAAiB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAA;6BACtD;iCAAM,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;gCACvB,qCAAiB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAA;6BACtD;yBACJ;6BAAM,IAAI,IAAI,CAAC,EAAE,KAAK,iBAAS,CAAC,MAAM,EAAE;4BACrC,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;gCACf,qCAAiB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAA;6BACzD;iCAAM,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;gCACvB,qCAAiB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAA;6BACzD;iCAAM,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;gCACvB,qCAAiB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAA;6BACzD;yBACJ;6BAAM,IAAI,IAAI,CAAC,EAAE,KAAK,iBAAS,CAAC,IAAI,EAAE;4BACnC,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;gCACf,qCAAiB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAA;6BAC9D;iCAAM,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;gCACtB,qCAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAA;6BAC1D;yBACJ;wBACD,sBAAO,EAAE,EAAA;;;;KACZ;IAED,OAAO;IACM,6CAAmB,GAAhC,UAAiC,KAAa,EAAE,EAAU;;;;;;4BAChC,qBAAM,qBAAS,CAAC,eAAe,CAAC,EAAE,KAAK,OAAA,EAAE,EAAE,IAAA,EAAE,CAAC,EAAA;;wBAA9D,KAAgB,SAA8C,EAA5D,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BAC5C,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BACtC,MAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAC;yBAC5C;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,OAAO;IACM,2CAAiB,GAA9B,UAA+B,KAAa,EAAE,QAAgB,EAAE,EAAU,EAAE,OAAe,EAAE,QAAgB;;;;;;4BACnF,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,EAAE,IAAA,EAAE,OAAO,SAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,EAAA;;wBAAxF,KAAgB,SAAwE,EAAtF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,MAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAC;4BACpD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BAC5C,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BAC7C,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAC,CAAA;4BACnE,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAA;yBAClE;wBACD,sBAAO,EAAE,GAAG,KAAA,EAAE,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,EAAE,EAAA;;;;KACnC;IAED,OAAO;IACM,0CAAgB,GAA7B,UAA8B,KAAa,EAAE,OAAe,EAAE,QAAgB,EAAE,OAAe;;;;;;4BACrE,qBAAM,qBAAS,CAAC,WAAW,CAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,QAAQ,UAAA,EAAE,OAAO,SAAA,EAAE,CAAC,EAAA;;wBAAlF,KAAgB,SAAkE,EAAhF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,MAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAC;4BACnD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BAC5C,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BAC9C,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,CAAA;yBAChE;wBACD,sBAAO,EAAE,GAAG,KAAA,EAAE,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,EAAE,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,EAAE,EAAA;;;;KACvD;IAED,OAAO;IACM,2CAAiB,GAA9B,UAA+B,KAAa,EAAE,IAAY,EAAE,IAAY;;;;;;4BAC9C,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,CAAC,EAAA;;wBAAnE,KAAgB,SAAmD,EAAjE,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BAC/C,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BAChD,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,CAAA;yBACpE;wBACD,sBAAO,EAAE,GAAG,KAAA,EAAE,OAAO,QAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,GAAG,EAAE,EAAA;;;;KAC3C;IAED,gBAAgB;IACT,+CAAqB,GAA5B,UAA6B,IAAsB;;QAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACnD,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAA;QAChF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAA;QACvD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG,aAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,0CAAE,YAAY,CAAC,IAAI,CAAC,IAAI,2CAAG,IAAI,KAAI,EAAE,CAAA;QAC7E,OAAO,IAAI,CAAA;IACf,CAAC;IAED,gBAAgB;IACT,8CAAoB,GAA3B,UAA4B,IAAqB;;QAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACnD,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAA;QAChF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAA;QACvD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG,aAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,0CAAE,YAAY,CAAC,IAAI,CAAC,IAAI,2CAAG,IAAI,KAAI,EAAE,CAAA;QAC7E,OAAO,IAAI,CAAA;IACf,CAAC;IAED,gBAAgB;IACT,+CAAqB,GAA5B,UAA6B,IAAa,EAAE,IAAyB;;QACjE,IAAM,OAAO,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAA;QACzF,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC5B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QAC1B,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACtB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC9B,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QACtC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAClC,OAAO,CAAC,SAAS,SAAG,IAAI,CAAC,SAAS,0CAAE,KAAK,EAAE,CAAA;QAC3C,IAAI,OAAO,CAAC,SAAS,EAAE;YACnB,OAAO,CAAC,UAAU,EAAE,CAAA;SACvB;QACD,OAAO,OAAO,CAAA;IAClB,CAAC;IAED,aAAa;IACL,+CAAqB,GAA7B;;QACI,IAAM,aAAa,SAAG,oBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,0CAAE,KAAK,CAAA;QACxD,IAAI,SAAS,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,GAAY,IAAI,CAAA;QAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,EAAE;gBAC9F,KAAK,IAAI,CAAC,CAAA;gBACV,IAAI,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE;oBACzB,SAAS,GAAG,CAAC,CAAC,SAAS,CAAA;oBACvB,IAAI,GAAG,CAAC,CAAA;iBACX;aACJ;QACL,CAAC,CAAC,CAAA;QACF,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;YACpB,IAAI,CAAC,KAAK,EAAE,CAAA;YACZ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACxB,sDAAsD;SACzD;IACL,CAAC;IAED,WAAW;IACJ,oDAA0B,GAAjC,UAAkC,KAAa;QAC3C,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;IAC7C,CAAC;IAEM,gCAAM,GAAb,UAAc,EAAU;QAAxB,iBAaC;;QAZG,IAAM,YAAY,GAAG,oBAAO,CAAC,YAAY,CAAA;QACzC,IAAM,aAAa,SAAG,oBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,0CAAE,KAAK,CAAA;QACxD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAA;QACtD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,aAAa,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,IAAI,IAAI,EAAE;gBACzH,CAAC,CAAC,KAAK,EAAE,CAAA;gBACT,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;gBACrB,qCAAqC;aACxC;iBAAM;gBACH,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;aACf;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IACD,+GAA+G;IAE/G,SAAS;IACD,0CAAgB,GAAxB,UAAyB,GAAQ;;QAC7B,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,IAAI,GAAG,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA;QACpE,EAAE,CAAC,GAAG,CAAC,kBAAkB,EAAE,kBAAU,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QACzD,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAChC,IAAI,IAAI,KAAK,kBAAU,CAAC,kBAAkB,EAAE,EAAE,UAAU;YACpD,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,EAAE;gBACd,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;aACrC;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;aAClC;YACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;SACvD;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,SAAS,EAAE,EAAE,QAAQ;YAChD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;SAClD;aAAM,IAAI,CAAC,IAAI,EAAE;SACjB;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,QAAQ,EAAE,EAAE,MAAM;YAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;SAClC;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,UAAU,EAAE,EAAE,MAAM;YAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC1C,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC3B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAA;aACjD;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,SAAS,EAAE,EAAE,MAAM;YAC9C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;SACtB;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,YAAY,EAAE,EAAE,MAAM;YACjD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;SACzB;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,QAAQ,EAAE,EAAE,MAAM;YAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SACrB;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,WAAW,EAAE,EAAE,MAAM;YAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SACxB;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,WAAW,EAAE,EAAE,MAAM;YAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SACxB;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,kBAAkB,EAAE,EAAE,UAAU;YAC3D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;SAC3B;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,SAAS,EAAE,EAAE,MAAM;YAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC5C,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;gBACpC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,EAAE,YAAY;oBAC/B,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;iBAChD;aACJ;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,gBAAgB,EAAE,EAAE,QAAQ;YACvD,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,CAAC,UAAA,CAAC;gBACX,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;gBACpD,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;oBAClC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;oBACzB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;iBAC5B;YACL,CAAC,EAAC;YACF,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;SACpD;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,qBAAqB,EAAE,EAAE,QAAQ;YAC5D,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC1D,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aACpC;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,oBAAoB,EAAE,EAAE,QAAQ;YAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC1D,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBACpC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAA;gBAC/C,IAAI,CAAC,oBAAoB,EAAE,CAAA;aAC9B;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,qBAAqB,EAAE,EAAE,QAAQ;YAC5D,IAAM,MAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC5C,IAAI,MAAI,EAAE;gBACN,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,CAAC;oBACxB,IAAM,IAAI,GAAG,MAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAf,CAAe,CAAC,CAAA;oBAClD,IAAI,IAAI,EAAE;wBACN,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;qBACpC;gBACL,CAAC,CAAC,CAAA;gBACF,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,EAAE,MAAI,CAAC,CAAA;gBAChD,IAAI,CAAC,oBAAoB,EAAE,CAAA;aAC9B;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,iBAAiB,EAAE,EAAE,MAAM;YACtD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAChC;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,MAAM;YACpD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;SAC/B;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,MAAM;YACpD,IAAI,IAAI,CAAC,MAAM,WAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,0CAAE,OAAO,GAAE,EAAE;gBACxD,uBAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;aACvD;SACJ;IACL,CAAC;IAED,QAAQ;IACA,yCAAe,GAAvB,UAAwB,GAAQ;;QAC5B,IAAM,IAAI,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,KAAI,EAAE,CAAA;QAC5B,IAAM,GAAG,GAAG,OAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,0CAAE,WAAW,cAAM,IAAI,CAAC,QAAQ,0CAAE,WAAW,GAAE,CAAA;QACnF,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,eAAe,CAAC,IAAI,EAAC;IAC9B,CAAC;IA7XgB,eAAe;QADnC,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;OACL,eAAe,CA8XnC;IAAD,sBAAC;CA9XD,AA8XC,CA9X4C,EAAE,CAAC,SAAS,GA8XxD;kBA9XoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import NetworkModel from \"../common/NetworkModel\"\nimport BuildObj from \"./BuildObj\"\nimport AreaObj from \"./AreaObj\"\nimport { BUILD_NID, NotifyType } from \"../../common/constant/Enums\"\nimport EventType from \"../../common/event/EventType\"\nimport { gameHpr } from \"../../common/helper/GameHelper\"\nimport { ecode } from \"../../common/constant/ECode\"\nimport { netHelper } from \"../../common/helper/NetHelper\"\nimport PawnObj from \"./PawnObj\"\nimport PawnDrillInfoObj from \"../main/PawnDrillInfoObj\"\nimport PawnLevelingInfoObj from \"../main/PawnLevelingInfoObj\"\nimport PlayerModel from \"../main/PlayerModel\"\nimport { eventReportHelper } from \"../../common/helper/EventReportHelper\"\nimport { viewHelper } from \"../../common/helper/ViewHelper\"\nimport PawnCureInfoObj from \"../main/PawnCureInfoObj\"\n\n/**\n * 战场管理中心\n */\*************('areaCenter')\nexport default class AreaCenterModel extends mc.BaseModel {\n\n    private readonly AREA_MAX_LIFE_TIME: number = 60 * 1000 * 1 //区域最大存在时间\n\n    private areas: Map<number, AreaObj> = new Map()\n    private watchPlayers: Map<number, { uid: string, time: number }[]> = new Map() //区域观战玩家\n\n    private net: NetworkModel = null\n    private player: PlayerModel = null\n\n    private lookArea: AreaObj = null //当前查看的区域\n\n    public onCreate() {\n        this.net = this.getModel('net')\n        this.player = this.getModel('player')\n    }\n\n    // 初始化监听\n    public initNetEvent() {\n        this.net.on('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this)\n        this.net.on('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this)\n    }\n\n    public clean() {\n        this.net.off('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this)\n        this.net.off('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this)\n        this.cleanAreas()\n        this.watchPlayers.clear()\n    }\n\n    public getArea(index: number) {\n        if (this.lookArea?.index === index) {\n            return this.lookArea\n        }\n        return this.areas.get(gameHpr.amendAreaIndex(index))\n    }\n\n    public addArea(area: AreaObj) {\n        this.areas.set(area.index, area)\n        return area\n    }\n\n    public removeArea(index: number) {\n        this.getArea(index)?.clean()\n        this.delArea(index)\n    }\n\n    private delArea(index: number) {\n        this.areas.delete(index)\n        netHelper.sendLeaveArea({ index })\n    }\n\n    public cleanAreas() {\n        this.areas.forEach(m => m.clean())\n        this.areas.clear()\n        if (this.lookArea?.index >= 0) {\n            this.lookArea = null\n        }\n    }\n\n    // 获取当前场景的区域信息\n    public getLookArea() {\n        if (!this.lookArea) {\n            this.lookArea = this.getArea(gameHpr.world.getLookCell()?.index ?? -1)\n        }\n        return this.lookArea\n    }\n    public setLookArea(area: AreaObj) {\n        this.lookArea = area\n    }\n\n    // 获取自己的主城市\n    public getMeMainArea() {\n        return this.areas.get(this.player.getMainCityIndex())\n    }\n\n    // 获取军队\n    public getArmy(index: number, uid: string) {\n        return this.getArea(index)?.getArmyByUid(uid)\n    }\n\n    // 请求战场信息\n    private async reqAreaInfo(index: number, reinit?: boolean) {\n        const { err, data } = await netHelper.reqGetAreaInfo({ index })\n        const areaInfo = data?.data\n        if (!areaInfo) {\n            return null\n        } else if (areaInfo.battle) {\n            this.checkRemoveBattleArea()\n        }\n        const area = new AreaObj().init(areaInfo)\n        this.areas.set(index, area)\n        // 是否有战斗\n        if (areaInfo.battle) {\n            area.battleBegin(areaInfo.battle, !reinit)\n        }\n        // 如果有不一样 表示没更新到 这里兼容同步一下地块信息\n        if (area.owner && gameHpr.world.getMapCellByIndex(index)?.owner !== area.owner) {\n            gameHpr.world.syncServerCellInfo(index)\n        }\n        return area\n    }\n\n    // 获取战场信息没有就请求\n    public async reqAreaByIndex(index: number, reinit?: boolean) {\n        if (index === -1) {\n            return null\n        }\n        const cell = gameHpr.world.getMapCellByIndex(index)\n        const owner = cell.owner || ''\n        index = cell?.city?.index ?? index //修正一下index 有可能点到其他点\n        let area = this.areas.get(index)\n        if (!area || area.owner !== owner) {\n            area = await this.reqAreaInfo(index, reinit)\n        }\n        return area\n    }\n\n    // 升级建筑\n    public async upBuildToServer(item: BuildObj) {\n        if (!gameHpr.checkCTypes(item.upCost)) {\n            return ecode.RES_NOT_ENOUGH\n        } else if (this.player.getBtQueues().has('id', item.id)) {\n            return ecode.YET_IN_BTQUEUE\n        }\n        const { err, data } = await netHelper.reqUpAreaBuild({ index: item.aIndex, uid: item.uid })\n        if (err) {\n            return err\n        }\n        this.player.updateOutputByFlags(data.output)\n        this.player.updateBtQueue(data.queues)\n        // 买量数据上报\n        if (item.id === BUILD_NID.MAIN) {\n            if (item.lv === 4) {\n                eventReportHelper.reportGlobalEventOne('cap_lv_5')\n            } else if (item.lv === 9) {\n                eventReportHelper.reportGlobalEventOne('cap_lv_10')\n            } else if (item.lv === 14) {\n                eventReportHelper.reportGlobalEventOne('cap_lv_15')\n            } else if (item.lv === 19) {\n                eventReportHelper.reportGlobalEventOne('cap_lv_20')\n            }\n        } else if (item.id === BUILD_NID.SMITHY) {\n            if (item.lv === 9) {\n                eventReportHelper.reportGlobalEventOne('smithy_lv_10')\n            } else if (item.lv === 14) {\n                eventReportHelper.reportGlobalEventOne('smithy_lv_15')\n            } else if (item.lv === 19) {\n                eventReportHelper.reportGlobalEventOne('smithy_lv_20')\n            }\n        } else if (item.id === BUILD_NID.CAMP) {\n            if (item.lv === 0) {\n                eventReportHelper.reportGlobalEventOne('frontier_garrison')\n            } else if (item.lv === 4) {\n                eventReportHelper.reportGlobalEventOne('frontier_lv_5')\n            }\n        }\n        return ''\n    }\n\n    // 修建建筑\n    public async unlockBuildToServer(index: number, id: number) {\n        const { err, data } = await netHelper.reqAddAreaBuild({ index, id })\n        if (!err) {\n            this.player.updateOutputByFlags(data.output)\n            this.player.updateBtQueue(data.queues)\n            this.getArea(index)?.addBuild(data.build)\n        }\n        return err\n    }\n\n    // 训练士兵\n    public async drillPawnToServer(index: number, buildUid: string, id: number, armyUid: string, armyName: string) {\n        const { err, data } = await netHelper.reqDrillPawn({ index, buildUid, id, armyUid, armyName })\n        if (!err) {\n            this.getArea(index)?.updateArmyDrillPawns(data.army)\n            this.player.updateOutputByFlags(data.output)\n            this.player.updatePawnDrillQueue(data.queues)\n            this.player.setFreeRecruitPawnCount(data.freeRecruitPawnCount || 0)\n            this.player.setUpRecruitPawnCount(data.upRecruitPawnCount || 0)\n        }\n        return { err, army: data?.army }\n    }\n\n    // 治疗士兵\n    public async curePawnToServer(index: number, armyUid: string, armyName: string, pawnUid: string) {\n        const { err, data } = await netHelper.reqCurePawn({ index, armyUid, armyName, pawnUid })\n        if (!err) {\n            this.getArea(index)?.updateArmyCurePawns(data.army)\n            this.player.updateOutputByFlags(data.output)\n            this.player.updatePawnCuringQueue(data.queues)\n            this.player.setFreeCurePawnCount(data.freeCurePawnCount || 0)\n        }\n        return { err, list: data?.queues, army: data?.army }\n    }\n\n    // 士兵练级\n    public async pawnLvingToServer(index: number, auid: string, puid: string) {\n        const { err, data } = await netHelper.reqPawnLving({ index, auid, puid })\n        if (!err) {\n            this.player.updateRewardItemsByFlags(data.cost)\n            this.player.updatePawnLevelingQueue(data.queues)\n            this.player.setFreeLevingPawnCount(data.freeLevingPawnCount || 0)\n        }\n        return { err, armyUid: data?.army?.uid }\n    }\n\n    // 创建一个士兵 根据招募信息\n    public createPawnByDrillInfo(data: PawnDrillInfoObj) {\n        const conf = this.player.getConfigPawnInfo(data.id)\n        const pawn = new PawnObj().init(data.id, conf.equip, 1, conf.skinId).initAnger()\n        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed\n        pawn.lv = data.lv\n        pawn.armyUid = data.auid\n        pawn.armyName = this.getArea(data.index)?.getArmyByUid(data.auid)?.name || ''\n        return pawn\n    }\n\n    // 创建一个士兵 根据治疗信息\n    public createPawnByCureInfo(data: PawnCureInfoObj) {\n        const conf = this.player.getConfigPawnInfo(data.id)\n        const pawn = new PawnObj().init(data.id, conf.equip, 1, conf.skinId).initAnger()\n        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed\n        pawn.lv = data.lv\n        pawn.armyUid = data.auid\n        pawn.armyName = this.getArea(data.index)?.getArmyByUid(data.auid)?.name || ''\n        return pawn\n    }\n\n    // 创建一个士兵 根据练级信息\n    public createPawnByLvingInfo(pawn: PawnObj, data: PawnLevelingInfoObj) {\n        const newPawn = new PawnObj().init(pawn.id, pawn.equip, data.lv, pawn.skinId).initAnger()\n        newPawn.aIndex = pawn.aIndex\n        newPawn.owner = pawn.owner\n        newPawn.uid = pawn.uid\n        newPawn.armyUid = pawn.armyUid\n        newPawn.armyName = pawn.armyName\n        newPawn.attackSpeed = pawn.attackSpeed\n        newPawn.treasures = pawn.treasures\n        newPawn.portrayal = pawn.portrayal?.clone()\n        if (newPawn.portrayal) {\n            newPawn.updateAttr()\n        }\n        return newPawn\n    }\n\n    // 检测删除多的战斗区域\n    private checkRemoveBattleArea() {\n        const loolAreaIndex = gameHpr.world.getLookCell()?.index\n        let leaveTime = 0, count = 0, area: AreaObj = null\n        this.areas.forEach(m => {\n            if (!m.active && m.index >= 0 && m.isBattleing() && loolAreaIndex !== m.index && m.leaveTime > 0) {\n                count += 1\n                if (leaveTime < m.leaveTime) {\n                    leaveTime = m.leaveTime\n                    area = m\n                }\n            }\n        })\n        if (count >= 5 && area) {\n            area.clean()\n            this.delArea(area.index)\n            // cc.log('checkRemoveBattleArea index=' + area.index)\n        }\n    }\n\n    // 获取观战玩家列表\n    public getAreaWatchPlayersByIndex(index: number) {\n        return this.watchPlayers.get(index) || []\n    }\n\n    public update(dt: number) {\n        const isNoviceMode = gameHpr.isNoviceMode\n        const loolAreaIndex = gameHpr.world.getLookCell()?.index\n        const now = Date.now(), life = this.AREA_MAX_LIFE_TIME\n        this.areas.forEach(m => {\n            if (!isNoviceMode && !m.active && m.index >= 0 && loolAreaIndex !== m.index && m.leaveTime > 0 && now - m.leaveTime >= life) {\n                m.clean()\n                this.delArea(m.index)\n                // cc.log('delArea index=' + m.index)\n            } else {\n                m.update(dt)\n            }\n        })\n    }\n    // ----------------------------------------- net listener function --------------------------------------------\n\n    // 更新战场信息\n    private OnUpdateAreaInfo(res: any) {\n        const type = res.type, index = res.index, data = res['data_' + type]\n        cc.log('OnUpdateAreaInfo', NotifyType[type], index, data)\n        const area = this.getArea(index)\n        if (type === NotifyType.AREA_PLAYER_CHANGE) { //通知战场人员变动\n            if (data?.length) {\n                this.watchPlayers.set(index, data)\n            } else {\n                this.watchPlayers.delete(index)\n            }\n            this.emit(EventType.UPDATE_AREA_WATCH_PLAYER, index)\n        } else if (type === NotifyType.AREA_CHAT) { //通知战场聊天\n            this.emit(EventType.ADD_AREA_CHAT, index, data)\n        } else if (!area) {\n        } else if (type === NotifyType.BUILD_UP) { //建筑升级\n            area.buildUp(data.uid, data.lv)\n        } else if (type === NotifyType.MOVE_BUILD) { //移动建筑\n            const build = area.getBuildByUid(data.uid)\n            if (build) {\n                build.point.set(data.point)\n                this.emit(EventType.UPDATE_BUILD_POINT, build)\n            }\n        } else if (type === NotifyType.ADD_BUILD) { //添加建筑\n            area.addBuild(data)\n        } else if (type === NotifyType.REMOVE_BUILD) { //删除建筑\n            area.removeBuild(data)\n        } else if (type === NotifyType.ADD_ARMY) { //添加军队\n            area.addArmy(data)\n        } else if (type === NotifyType.REMOVE_ARMY) { //删除军队\n            area.removeArmy(data)\n        } else if (type === NotifyType.UPDATE_ARMY) { //更新军队\n            area.updateArmy(data)\n        } else if (type === NotifyType.UPDATE_ALL_PAWN_HP) { //更新所有士兵血量\n            area.updateAllArmy(data)\n        } else if (type === NotifyType.MOVE_PAWN) { //移动士兵\n            const army = area.getArmyByUid(data.armyUid)\n            if (army) {\n                army.setPawnsPoint(data.pawns || [])\n                if (!army.isOwner()) { //不是我自己就通知一下\n                    eventCenter.emit(EventType.UPDATE_ARMY, army)\n                }\n            }\n        } else if (type === NotifyType.CHANGE_PAWN_ATTR) { //改变士兵属性\n            data?.forEach(m => {\n                const pawn = area.getPawnByPrecise(m.armyUid, m.uid)\n                if (pawn) {\n                    pawn.setAttackSpeed(m.attackSpeed)\n                    pawn.changeSkin(m.skinId)\n                    pawn.changeEquip(m.equip)\n                }\n            })\n            this.emit(EventType.UPDATE_AREA_ARMY_LIST, index)\n        } else if (type === NotifyType.CHANGE_PAWN_PORTRAYAL) { //改变士兵化身\n            const pawn = area.getPawnByPrecise(data.armyUid, data.uid)\n            if (pawn) {\n                pawn.setPortrayal(data.portrayal)\n            }\n        } else if (type === NotifyType.UPDATE_PAWN_TREASURE) { //刷新士兵宝箱\n            const pawn = area.getPawnByPrecise(data.armyUid, data.uid)\n            if (pawn) {\n                pawn.updateTreasures(data.treasures)\n                this.emit(EventType.UPDATE_PAWN_TREASURE, pawn)\n                area.updateTreasureReddot()\n            }\n        } else if (type === NotifyType.UPDATE_ARMY_TREASURES) { //刷新军队宝箱\n            const army = area.getArmyByUid(data.armyUid)\n            if (army) {\n                data.pawnTreasures.forEach(m => {\n                    const pawn = army.pawns.find(p => p.uid === m.uid)\n                    if (pawn) {\n                        pawn.updateTreasures(m.treasures)\n                    }\n                })\n                this.emit(EventType.UPDATE_ARMY_TREASURES, army)\n                area.updateTreasureReddot()\n            }\n        } else if (type === NotifyType.AREA_BATTLE_BEGIN) { //发生战斗\n            area.initBattleData(data)\n            area.battleBegin(data.battle)\n        } else if (type === NotifyType.AREA_BATTLE_END) { //战斗结束\n            area.battleEndByServer(data)\n        } else if (type === NotifyType.CELL_TONDEN_END) { //屯田结束\n            if (area.active && area.getArmyByUid(data.auid)?.isOwner()) {\n                viewHelper.showPnl('area/TondenEnd', data.treasures)\n            }\n        }\n    }\n\n    // 战斗帧数据\n    private OnFSPCheckFrame(res: any) {\n        const data = res?.data || {}\n        const fsp = this.getArea(data.index)?.getFspModel() || this.lookArea?.getFspModel()\n        fsp?.onFSPCheckFrame(data)\n    }\n}"]}