{"version": 3, "sources": ["assets\\app\\script\\view\\main\\MainWindCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAA4D;AAC5D,2DAAiP;AAEjP,qDAAoD;AACpD,qDAAsF;AACtF,0DAAqD;AACrD,wDAAmD;AACnD,6DAA4D;AAC5D,6DAAyD;AACzD,+DAA8D;AAC9D,2DAA0D;AAC1D,qEAAoE;AACpE,2DAA0D;AAC1D,6DAA4D;AAC5D,sDAAiD;AAUjD,qDAAgD;AAChD,yDAAoD;AACpD,+CAA0C;AAC1C,qDAAgD;AAChD,yCAAoC;AACpC,qDAAgD;AAExC,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA0C,gCAAe;IAAzD;QAAA,qEA+pCC;QA7pCG,0BAA0B;QAClB,cAAQ,GAAY,IAAI,CAAA,CAAC,oBAAoB;QAC7C,qBAAe,GAAY,IAAI,CAAA,CAAC,kCAAkC;QAClE,qBAAe,GAAY,IAAI,CAAA,CAAC,4BAA4B;QAC5D,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,sBAAgB,GAAY,IAAI,CAAA,CAAC,6BAA6B;QAC9D,oBAAc,GAAY,IAAI,CAAA,CAAC,iCAAiC;QAChE,oBAAc,GAAY,IAAI,CAAA,CAAC,iCAAiC;QAChE,oBAAc,GAAY,IAAI,CAAA,CAAC,2BAA2B;QAC1D,sBAAgB,GAAY,IAAI,CAAA,CAAC,6BAA6B;QAC9D,mBAAa,GAAY,IAAI,CAAA,CAAC,0BAA0B;QACxD,oBAAc,GAAY,IAAI,CAAA,CAAC,2BAA2B;QAClE,MAAM;QAEW,cAAQ,GAAG,aAAa,CAAA;QAEjC,YAAM,GAAY,IAAI,CAAA,CAAA,IAAI;QAC1B,kBAAY,GAAY,IAAI,CAAA,CAAA,IAAI;QAChC,qBAAe,GAAY,IAAI,CAAA,CAAC,KAAK;QACrC,cAAQ,GAAY,IAAI,CAAA;QACxB,iBAAW,GAAY,IAAI,CAAA,CAAC,IAAI;QAChC,cAAQ,GAAY,IAAI,CAAA;QACxB,cAAQ,GAAY,IAAI,CAAA,CAAC,KAAK;QAC9B,cAAQ,GAAY,IAAI,CAAA,CAAC,KAAK;QAC9B,gBAAU,GAAY,IAAI,CAAA,CAAC,OAAO;QAClC,gBAAU,GAAY,IAAI,CAAA,CAAC,KAAK;QAChC,cAAQ,GAAY,IAAI,CAAA,CAAC,MAAM;QAC/B,gBAAU,GAAY,IAAI,CAAA,CAAC,QAAQ;QACnC,gBAAU,GAAY,IAAI,CAAA,CAAC,QAAQ;QACnC,iBAAW,GAAY,IAAI,CAAA,CAAC,MAAM;QAClC,kBAAY,GAAiB,IAAI,CAAA;QACjC,eAAS,GAAiB,IAAI,CAAA;QAC9B,iBAAW,GAAoB,IAAI,CAAA;QACnC,sBAAgB,GAAgC,EAAE,CAAA;QAClD,gBAAU,GAAW,CAAC,CAAA;QAEtB,WAAK,GAAe,IAAI,CAAA;QACxB,UAAI,GAAc,IAAI,CAAA;QACtB,YAAM,GAAgB,IAAI,CAAA;QAC1B,YAAM,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,SAAS;QACnC,wBAAkB,GAAW,CAAC,CAAA;QAC9B,uBAAiB,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QACpC,YAAM,GAAgB,EAAE,CAAA,CAAC,QAAQ;QACjC,qBAAe,GAAkC,EAAE,CAAA,CAAC,aAAa;QACjE,uBAAiB,GAAY,KAAK,CAAA,CAAC,aAAa;QAChD,kBAAY,GAAQ,EAAE,CAAA,CAAC,YAAY;QACnC,sBAAgB,GAAoB,IAAI,CAAA,CAAC,QAAQ;QACjD,yBAAmB,GAAoB,IAAI,CAAA,CAAC,QAAQ;QAEpD,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;;IAwmC3C,CAAC;IAtmCU,sCAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,kBAAQ,CAAC,aAAa,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC1D,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,SAAS,IAAG,IAAI,CAAC,UAAU,EAAE,QAAK,GAAE,IAAI;sBACnD,GAAC,mBAAS,CAAC,YAAY,IAAG,IAAI,CAAC,aAAa,EAAE,QAAK,GAAE,IAAI;sBACzD,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,QAAK,GAAE,IAAI;sBAClE,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC7E,GAAC,mBAAS,CAAC,yBAAyB,IAAG,IAAI,CAAC,wBAAwB,EAAE,QAAK,GAAE,IAAI;sBACjF,GAAC,mBAAS,CAAC,cAAc,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC5D,GAAC,mBAAS,CAAC,aAAa,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC3D,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACzE,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,WAAW,EAAE,QAAK,GAAE,IAAI;sBACtD,GAAC,mBAAS,CAAC,sBAAsB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC5E,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC7E,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;sBACvE,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACxE,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;sBACvE,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC7E,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,kBAAkB,IAAG,IAAI,CAAC,kBAAkB,EAAE,QAAK,GAAE,IAAI;sBACpE,GAAC,mBAAS,CAAC,sBAAsB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC5E,GAAC,mBAAS,CAAC,mBAAmB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;sBACtE,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,2BAA2B,IAAG,IAAI,CAAC,yBAAyB,EAAE,QAAK,GAAE,IAAI;SACzF,CAAA;IACL,CAAC;IAEY,+BAAQ,GAArB;;;;gBACI,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;gBACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;gBACvD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,CAAA;gBAC9D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;gBACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBACnD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBACnD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBACnD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;gBACtD,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBACnB,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAA;oBAClF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;iBACrB;gBACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,sBAAY,CAAC,CAAA;gBACnE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,sBAAY,CAAC,CAAA;gBAClE,IAAI,CAAC,gBAAgB,GAAG,IAAI,yBAAe,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAS,CAAC,CAAC,CAAA;gBAC1G,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBACrC,sHAAsH;gBACtH,yDAAyD;gBACzD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;gBACnC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;gBACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,yBAAe,CAAC,CAAA;gBACtE,IAAI,CAAC,wBAAwB,EAAE,CAAA;gBAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,8BAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAA;;;;KAC5G;IAEY,8BAAO,GAApB;;;;;;KAEC;IAEM,8BAAO,GAAd,UAAe,IAAS;QACpB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAA;QAC3B,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAA;QAChC,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAA;QAC9B,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,SAAS,EAAE,CAAA,CAAC,OAAO;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC3B,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/C,oBAAO,CAAC,WAAW,EAAE,CAAA;QACrB,uBAAU,CAAC,UAAU,CAAC,0BAAe,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAA;IACtE,CAAC;IAEM,8BAAO,GAAd;QACI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAA;QAC3B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QACtB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,KAAK,EAAE,CAAA;QACtD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QACzB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;QAC9B,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAA;QACxC,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,KAAK,CAAA;QACjC,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAA;QACvC,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,KAAK,CAAA;QAChC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAA;QACtC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,KAAK,CAAA;QAC/B,+DAA+D;QAC/D,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;QACtB,WAAW,CAAC,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvC,uBAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;IACpC,CAAC;IAEM,8BAAO,GAAd;;QACI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACjC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;SACrC;QACD,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAA;QAC1B,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;QACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QACzB,MAAA,IAAI,CAAC,WAAW,0CAAE,KAAK,GAAE;QACzB,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAChD,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,yCAAyC;IACzC,mCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACjD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YAC5B,uBAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAC3B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;SAC7B;IACL,CAAC;IAED,0CAA0C;IAC1C,oCAAa,GAAb,UAAc,KAA0B,EAAE,IAAY;QAClD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;aAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;YAC7C,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,uBAAuB,CAAC,CAAA;SAC7D;aAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAE,OAAO;YAC7C,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,YAAY,CAAC,CAAA;SAClD;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,MAAM;YAClC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,oBAAoB,CAAC,CAAA;SAC1D;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAClC,CAAC;IAED,0CAA0C;IAC1C,oCAAa,GAAb,UAAc,KAA0B,EAAE,IAAY;QAClD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;YACxB,OAAM;SACT;aAAM,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC/C,OAAO,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;SAC5D;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC3B,OAAO,uBAAU,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;SACxD;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAClC,CAAC;IAED,iDAAiD;IACjD,0CAAmB,GAAnB,UAAoB,KAA0B,EAAE,IAAY;QAA5D,iBASC;QARG,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YAC/C,OAAM;SACT;QACD,uBAAU,CAAC,cAAc,CAAC,sBAAsB,EAAE;YAC9C,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,QAAQ,IAAI,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAjD,CAAiD;YAC3D,MAAM,EAAE,cAAQ,CAAC;SACpB,CAAC,CAAA;IACN,CAAC;IAED,wCAAwC;IACxC,kCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;YAChC,OAAM;SACT;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAClC,CAAC;IAED,yCAAyC;IACzC,mCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACjD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;YACxB,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC3B,OAAO,uBAAU,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;SACxD;QACD,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED,6CAA6C;IAC7C,uCAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;YACxB,OAAM;SACT;QACD,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;IACrD,CAAC;IAED,+CAA+C;IAC/C,wCAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACtD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,CAAA;QAClD,IAAI,IAAI,EAAE;YACN,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE,UAAU,CAAC,CAAA;SAC5D;IACL,CAAC;IAED,2CAA2C;IAC3C,sCAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;QAAxD,iBAWC;QAVG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,IAAM,OAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACrC,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,GAAG,EAAE,2BAA2B,EAAE,MAAM,EAAE,CAAC,OAAK,CAAC,EAAE,EAAE,UAAC,IAAY,EAAE,MAAgD;gBAC9J,IAAI,KAAI,CAAC,OAAO,EAAE;oBACd,uBAAU,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAI,OAAK,MAAG,EAAE,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAA1C,CAA0C,CAAC,CAAA;iBAC9H;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,wCAAwC;IACxC,kCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,IAAI,CAAC,oBAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE;YAClC,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC3D;IACL,CAAC;IAED,iDAAiD;IACjD,uCAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,uBAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;SACxD;IACL,CAAC;IAED,qDAAqD;IACrD,yCAAkB,GAAlB,UAAmB,KAA0B,EAAE,IAAY;QACvD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC7E;IACL,CAAC;IAED,4CAA4C;IAC5C,uCAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QAAzD,iBAaC;QAZG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,uBAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,UAAC,EAAU;gBAChE,IAAI,EAAE,EAAE;oBACJ,oBAAO,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;iBAClD;gBACD,IAAI,KAAI,CAAC,OAAO,EAAE;oBACd,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;iBAC7B;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,+CAA+C;IAC/C,wCAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QAA1D,iBAaC;;QAZG,IAAM,IAAI,GAAe,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACjD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;YACxB,OAAM;SACT;QACD,IAAM,OAAO,GAAG,OAAA,oBAAO,CAAC,aAAa,CAAC,oBAAO,CAAC,MAAM,EAAE,CAAC,0CAAE,aAAa,CAAC,IAAI,CAAC,KAAK,MAAK,EAAE,CAAA;QACxF,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC3C,IAAI,GAAG,EAAE;gBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aACnC;YACD,kCAAkC;YAClC,OAAO,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,uBAAU,CAAC,qBAAqB,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,KAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,KAAI,CAAC,GAAG,CAAC,EAA5F,CAA4F,CAAC,CAAA;QAC3H,CAAC,CAAC,CAAA;IACN,CAAC;IAED,gDAAgD;IAChD,yCAAkB,GAAlB,UAAmB,KAA0B,EAAE,IAAY;QACvD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACrD,IAAI,OAAO,EAAE;gBACT,IAAM,KAAK,GAAG,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,CAAA;gBAC5F,uBAAU,CAAC,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;aACtD;SACJ;IACL,CAAC;IAED,6CAA6C;IAC7C,sCAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;QAAxD,iBASC;QARG,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,EAAE,UAAC,EAAW;gBACxD,IAAI,KAAI,CAAC,OAAO,IAAI,EAAE,EAAE;oBACpB,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;iBAC7B;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IACD,MAAM;IACN,iHAAiH;IAEzG,qCAAc,GAAtB;QACI,IAAI,CAAC,oBAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,EAAE,kBAAkB;YACxD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,KAAK,EAAE,CAAA;YACtD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;YACzB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAA;SAC9B;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,CAAC,QAAQ;QACpC,IAAI,CAAC,SAAS,EAAE,CAAA,CAAC,OAAO;IAC5B,CAAC;IAED,SAAS;IACD,uCAAgB,GAAxB;QACI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC/B,CAAC;IAED,OAAO;IACC,iCAAU,GAAlB,UAAmB,IAAkB;QAArC,iBAcC;QAbG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;SAClC;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;QACrD,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC/C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;SAClC;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,EAAE;gBAC1B,KAAK,GAAG,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,mBAAS,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC1F,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;YACnC,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,OAAO;IACC,oCAAa,GAArB,UAAsB,IAAkB;QACpC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,KAAK,EAAE,CAAA;YACb,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;SAClC;IACL,CAAC;IAED,SAAS;IACD,uCAAgB,GAAxB;QACI,IAAI,CAAC,SAAS,EAAE,CAAA;IACpB,CAAC;IAED,OAAO;IACC,sCAAe,GAAvB,UAAwB,GAAY;QAChC,GAAG,GAAG,CAAC,GAAG,CAAA;QACV,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,CAAA;QAC3B,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,GAAG,CAAA;QAClC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,EAAxC,CAAwC,CAAC,CAAA;QAC/E,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,EAAxC,CAAwC,CAAC,CAAA;IACnF,CAAC;IAED,SAAS;IACD,wCAAiB,GAAzB,UAA0B,IAAa;QACnC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,SAAS;IACD,6CAAsB,GAA9B;QACI,IAAM,KAAK,GAAiB,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAA;QACvE,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE;YACvB,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC3B;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,IAAK,OAAA,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,EAAhC,CAAgC,CAAC,CAAA;IAChF,CAAC;IAED,SAAS;IACD,+CAAwB,GAAhC;QACI,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,SAAS;IACD,qCAAc,GAAtB,UAAuB,KAAa;;QAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC3B,IAAI,OAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,0CAAE,QAAQ,MAAK,KAAK,EAAE;YACjD,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;SACjC;IACL,CAAC;IAED,SAAS;IACD,qCAAc,GAAtB,UAAuB,KAAa;;QAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC3B,IAAI,OAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,0CAAE,QAAQ,MAAK,KAAK,EAAE;YACjD,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;SACjC;IACL,CAAC;IAED,0BAA0B;IAClB,2CAAoB,GAA5B;QACI,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;IACtC,CAAC;IAED,OAAO;IACC,kCAAW,GAAnB,UAAoB,KAAc,EAAE,YAAqB;QAAzD,iBAeC;QAdG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,OAAO,YAAY,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;SAClG;aAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,oBAAoB;YACnF,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YACjG,uBAAU,CAAC,IAAI,CAAC,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,qBAAS,CAAC,QAAQ,EAAE,0BAAe,EAAE,uBAAU,CAAC,SAAS,CAAC,CAAA;YAC5G,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAA;SAChC;QACD,KAAK;QACL,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,uBAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAChG,IAAI,KAAI,CAAC,QAAQ,IAAI,YAAY,EAAE;gBAC/B,KAAI,CAAC,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;aAC3E;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACD,6CAAsB,GAA9B,UAA+B,IAAgB;QAC3C,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,MAAM,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,KAAK,MAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;QACpF,IAAI,EAAE,EAAE;YACJ,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;SACtC;IACL,CAAC;IAED,SAAS;IACD,6CAAsB,GAA9B,UAA+B,IAAS;QACpC,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,MAAM,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,KAAK,MAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;QACpF,IAAI,EAAE,EAAE;YACJ,qBAAS,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SACtF;IACL,CAAC;IAED,WAAW;IACH,0CAAmB,GAA3B;QACI,IAAM,KAAK,GAAyC,EAAE,EAAE,OAAO,GAAG,oBAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;QAC/F,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE;YACvB,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;SACrD;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YACnC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvF,CAAC,CAAC,CAAA;IACN,CAAC;IAED,WAAW;IACH,2CAAoB,GAA5B;QACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,EAAE,EAAjB,CAAiB,CAAC,CAAA;IAC/C,CAAC;IAED,WAAW;IACH,0CAAmB,GAA3B;QACI,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAC5B,CAAC;IAED,WAAW;IACH,6CAAsB,GAA9B;QACI,IAAI,CAAC,oBAAoB,EAAE,CAAA;IAC/B,CAAC;IAED,SAAS;IACD,sCAAe,GAAvB;QACI,IAAI,CAAC,aAAa,EAAE,CAAA;IACxB,CAAC;IAED,SAAS;IACD,yCAAkB,GAA1B;QACI,IAAM,KAAK,GAA6C,EAAE,CAAA;QAC1D,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE;YACpC,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACxC,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;YACnC,IAAI,MAAM,EAAE;gBACR,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;aACvC;SACJ;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YAClC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAA;YACrD,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,qBAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;YAC/F,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAC/F,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACD,6CAAsB,GAA9B;QACI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAA;IACtD,CAAC;IAED,OAAO;IACC,0CAAmB,GAA3B,UAA4B,IAAgB;QACxC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,MAAM,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,KAAK,MAAK,KAAK,CAAA,EAAA,CAAC,CAAA;QACxF,IAAI,EAAE,EAAE;YACJ,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;SACvC;IACL,CAAC;IAED,SAAS;IACD,uCAAgB,GAAxB,UAAyB,KAAa;QAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC/B,CAAC;IAED,MAAM;IACE,gDAAyB,GAAjC,UAAkC,IAAS;QACvC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACvB,yBAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACvE;IACL,CAAC;IAGD,sBAAY,kCAAQ;QAFpB,iHAAiH;aAEjH,cAAyB,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,CAAA,CAAC,CAAC;;;OAAA;IAEhE,OAAO;IACC,iCAAU,GAAlB,UAAmB,aAAsB;QACrC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,qBAAS,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAA;QACnF,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YACpC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YACzB,uBAAU,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,+BAAoB,CAAC,CAAA;SAC5E;aAAM;YACH,IAAI,CAAC,cAAc,EAAE,CAAA;SACxB;IACL,CAAC;IAED,SAAS;IACD,+CAAwB,GAAhC;QACI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QACxB,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,qBAAqB,EAAE,CAAA;QACrE,IAAI,cAAc,EAAE,EAAE,QAAQ;YAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SACvD;IACL,CAAC;IAED,OAAO;IACC,gCAAS,GAAjB,UAAkB,MAAe;QAAjC,iBA2NC;;QA1NG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAA;QAClC,IAAI,CAAC,kBAAkB,GAAG,uBAAU,CAAC,SAAS,CAAA;QAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC5B,OAAO;QACP,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAA;QAC5F,IAAM,OAAO,GAAG,oBAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;QAC7C,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAA;QAChD,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAA;QAChD,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAA;QAChG,IAAM,KAAK,GAAiB,EAAE,EAAE,OAAO,GAAU,EAAE,EAAE,OAAO,GAAU,EAAE,EAAE,YAAY,GAAiB,EAAE,CAAA;QACzG,oCAAoC;QACpC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAA;QAC7B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QACzB,IAAM,MAAM,GAAG,qBAAS,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAA;QACpF,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAClC,IAAI,qBAAqB,GAAG,EAAE,CAAA;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;YACnE,IAAM,QAAQ,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,KAAI,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACnE,IAAI,IAAI,EAAE;gBACN,IAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACxE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;gBACvC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACjB,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,KAAK,wBAAa,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;oBAC7F,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;oBACnG,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,EAAE;wBAC/B,gBAAgB;wBAChB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBAChB,gBAAgB;wBAChB,IAAM,KAAK,GAAG,oBAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;wBAC7D,IAAI,KAAK,GAAG,CAAC,EAAE;4BACX,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;yBAC5G;qBACJ;yBAAM,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;wBACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;wBAClD,IAAI,IAAI,EAAE;4BACN,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,IAAI;4BAC5B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;yBAC5D;qBACJ;iBACJ;qBAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;iBAC3B;qBAAM,IAAI,UAAU,EAAE,EAAE,OAAO;oBAC5B,OAAO,CAAC,IAAI,CAAC,EAAE,UAAU,YAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAA;oBAClC,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,eAAe,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;iBAClJ;qBAAM,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;oBAClD,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;iBAC9I;gBACD,SAAS;gBACT,IAAI,MAAM,EAAE;oBACR,OAAO,CAAC,IAAI,CAAC,EAAE,MAAM,QAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAA;oBAC9B,cAAc;oBACd,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE;wBACpC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;qBACrE;iBACJ;gBACD,aAAa;gBACb,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBAC3B,SAAS;oBACT,IAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,WAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,0CAAE,UAAU,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;oBAC/I,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA,CAAC,QAAQ;oBAC1E,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAA;iBAC/H;gBACD,SAAS;gBACT,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;oBAC1B,SAAS;oBACT,IAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,WAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,0CAAE,UAAU,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;oBAC/I,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAA,CAAC,QAAQ;oBACpG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;iBACjI;gBACD,SAAS;gBACT,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBAC1B,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;iBACpE;gBACD,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChC,IAAI,IAAI,EAAE,EAAE,MAAM;oBACd,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;iBAClJ;gBACD,OAAO;gBACP,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;gBACnC,IAAI,MAAM,EAAE;oBACR,IAAM,KAAK,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;oBAC/E,KAAK,CAAC,IAAI,GAAG,IAAI,CAAA;oBACjB,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;oBACnD,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,qBAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;oBAC7F,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;iBACvF;gBACD,QAAQ;gBACR,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;gBACtD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oBACxB,IAAM,YAAY,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAA;oBAC/E,uBAAU,CAAC,qBAAqB,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAA;iBACzF;gBACD,OAAO;gBACP,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;oBACb,IAAM,YAAY,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAA;oBAC5E,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;iBAC1D;gBACD,QAAQ;gBACR,IAAI,cAAc,GAAG,IAAI,CAAA;gBACzB,IAAI,CAAC,cAAc,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBAC7E,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;iBACvF;gBACD,IAAI,cAAc,CAAC,cAAc,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC/E,qBAAqB,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;oBAClD,IAAI,QAAQ,GAAY,IAAI,CAAA;oBAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAA;oBAC3D,IAAI,KAAK,GAAG,qBAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;oBAC3D,QAAQ,cAAc,CAAC,cAAc,CAAC,IAAI,EAAE;wBACxC,KAAK,sBAAc,CAAC,QAAQ;4BACxB,QAAQ,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAA;4BACtF,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;gCAChC,WAAW;gCACX,IAAI,IAAI,GAAG,KAAK,CAAC,eAAe,EAAE,CAAA;gCAClC,IAAI,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG,CAAA;gCAChC,IAAI,IAAI,CAAC,KAAK,GAAG,oBAAS,GAAG,CAAC,IAAI,CAAC,EAAE;oCACjC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,oBAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAA;iCAC1D;gCACD,IAAI,IAAI,CAAC,MAAM,GAAG,oBAAS,GAAG,CAAC,IAAI,CAAC,EAAE;oCAClC,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,oBAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;iCAC5D;gCACD,cAAc,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;6BAC1D;4BACD,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;4BACtD,MAAM;wBACV;4BACI,QAAQ,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAA;4BAC/E,MAAM;qBACb;oBACD,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,KAAK,CAAA;iBACpD;aAEJ;iBAAM;gBACH,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;gBACpD,IAAI,MAAM,EAAE;oBACR,IAAI,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;oBACpD,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;oBAC7I,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAA;iBAC1D;aACJ;SACJ;QACD,QAAQ;QACR,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;QAC1C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAA;QACpD,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QAC/C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC5C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC5C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC5C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;QAC9C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAA;QAChD,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;QAC9C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAA;QACjD,4CAA4C;QAC5C,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAA;QACrC,UAAU;QACV,IAAM,SAAS,eAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,0CAAE,QAAQ,mCAAI,CAAC,CAAC,CAAA;QAC7D,IAAM,UAAU,GAAG,qBAAS,CAAC,QAAQ,CAAC,CAAC,CAAA;QACvC,IAAM,aAAa,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAA;QACjD,QAAQ;QACR,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YACjC,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAA;YACnD,IAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAA;YACjB,IAAM,IAAI,GAAG,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC9C,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;YACjC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,QAAQ,MAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAA,IAAI,CAAC,CAAC,KAAK,MAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAA,IAAI,CAAC,CAAC,QAAQ,MAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAA,EAAE;gBAC3H,qBAAS,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE,KAAI,CAAC,QAAQ,CAAC,CAAA;gBACpF,KAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;aACtC;YACD,EAAE,CAAC,MAAM,GAAG,SAAS,KAAK,KAAK,CAAA;YAC/B,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,OAAA,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE,CAAA;QAClH,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,aAAa,CAAA;QACrC,UAAU;QACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,YAAY,EAAE,UAAC,EAAE,EAAE,IAAI;YAC/C,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;YACrD,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;YACjC,KAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YACpC,EAAE,CAAC,MAAM,GAAG,SAAS,KAAK,KAAK,CAAA;YAC/B,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,OAAA,EAAE,CAAA;QACvB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,aAAa,CAAA;QAC5C,YAAY;QACZ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,IAAI;;YACpC,IAAM,IAAI,GAAc,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;YACvD,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjC,IAAI,OAAA,EAAE,CAAC,IAAI,0CAAE,KAAK,MAAK,KAAK,EAAE;gBAC1B,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;gBACzC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;gBAC/C,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,WAAW,GAAG,KAAK,CAAC,CAAA;gBAC1D,KAAK;gBACL,IAAM,MAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,CAAA;gBAC3C,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,KAAK,CAAA;gBACpE,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;gBAC1B,KAAK,CAAC,IAAI,EAAE,CAAA;gBACZ,IAAI,WAAW,GAAG,IAAI,EAAE;oBACpB,MAAI,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;oBACrC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,MAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAvC,CAAuC,CAAC,CAAC,KAAK,EAAE,CAAA;iBAC9F;qBAAM;oBACH,MAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;iBAC1B;aACJ;YACD,QAAQ,CAAC,MAAM,GAAG,aAAa,IAAI,SAAS,KAAK,KAAK,CAAA;YACtD,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,OAAA,EAAE,CAAA;YACnB,EAAE,CAAC,MAAM,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;QACF,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,IAAI;;YACpC,IAAM,IAAI,GAAc,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;YAC3D,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjC,IAAI,OAAA,EAAE,CAAC,IAAI,0CAAE,KAAK,MAAK,KAAK,EAAE;gBAC1B,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC,CAAA;aACvE;YACD,QAAQ,CAAC,MAAM,GAAG,aAAa,CAAA;YAC/B,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,OAAA,EAAE,CAAA;YACnB,EAAE,CAAC,MAAM,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACN,CAAC;IAED,sCAAsC;IAC9B,qCAAc,GAAtB,UAAuB,KAAc,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QACzF,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;YAChB,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACvD;aAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;YACvB,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SAChC;QACD,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACxD,CAAC;IAEO,iCAAU,GAAlB,UAAmB,EAAW,EAAE,IAAY,EAAE,KAAc,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QAChH,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAC9D,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAI,IAAI,SAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAG,CAAC,CAAA;IACzF,CAAC;IAED,KAAK;IACG,qCAAc,GAAtB,UAAuB,QAAiB,EAAE,KAAc,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QAC5G,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;QAC7G,IAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChE,EAAE,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,EAAE,CAAA;IAC1B,CAAC;IAEO,kCAAW,GAAnB,UAAoB,IAAY;QAC5B,OAAO,qBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IACtC,CAAC;IAEO,2CAAoB,GAA5B,UAA6B,EAAW,EAAE,IAAgB;;QACtD,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QAC9C,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,OAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,mCAAI,KAAK,EAAE,CAAC,CAAC,CAAA;QAC5D,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QACjD,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAA,CAAC,EAAE;YACnC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YACvD,QAAQ,CAAC,KAAK,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,KAAI,SAAS,CAAC,CAAC,YAAY,CAAC,YAAY,IAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,EAAE,CAAA,CAAC,CAAA;YAC9E,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAA;SACvB;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;SACrB;IACL,CAAC;IAEO,4CAAqB,GAA7B,UAA8B,EAAW,EAAE,IAAgB;QACvD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;QAC7G,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAChE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC,CAAA;SACrE;IACL,CAAC;IAED,WAAW;IACH,4CAAqB,GAA7B,UAA8B,KAAkB;QAAlB,sBAAA,EAAA,SAAiB,CAAC;QAC5C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAA7C,CAA6C,CAAC,CAAA;QACnF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAA7C,CAA6C,CAAC,CAAA;QAC1F,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,EAA3D,CAA2D,CAAC,CAAA;IACtG,CAAC;IAED,QAAQ;IACA,qCAAc,GAAtB;QAAA,iBAiBC;QAhBG,IAAM,OAAO,GAAY,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;QACxC,IAAM,OAAO,GAAY,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;QACvC,IAAM,KAAK,GAAU,EAAE,EAAE,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAA;QACnE,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YAClC,IAAM,IAAI,GAAe,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;YAClD,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC1B,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAA;aAC9F;YACD,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;gBAClB,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAA;aAClF;SACJ;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YAChC,EAAE,CAAC,WAAW,CAAC,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;YACzE,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1E,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACD,qCAAc,GAAtB,UAAuB,IAAgB;QACnC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,gBAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,IAAI,gBAAQ,CAAC,KAAK,EAAE;YAC3E,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;YACrC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SACrD;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAA;QACxD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QACjC,SAAS;QACT,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC7C,CAAC;IAED,KAAK;IACG,qCAAc,GAAtB,UAAuB,IAAoB;QAApB,qBAAA,EAAA,WAAoB;QACvC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YAC3B,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,KAAK,EAAE,CAAA;YACtD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAA;SAC/B;IACL,CAAC;IAED,QAAQ;IACA,gCAAS,GAAjB;QAAA,iBAQC;QAPG,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,cAAc,EAAE,EAAlB,CAAkB,CAAC,CAAA;QACtE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;YACrC,IAAM,KAAK,GAAG,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,mBAAS,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC,CAAA;YAChG,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAA;QACnC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,iBAAiB,IAAI,KAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAA9D,CAA8D,CAAC,CAAA;IAC5F,CAAC;IAEO,iCAAU,GAAlB;QACI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAA;SAC5B;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;QAChB,+EAA+E;QAC/E,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAA;IAC3C,CAAC;IAED,UAAU;IACF,2CAAoB,GAA5B,UAA6B,IAAkB;QAC3C,IAAM,MAAM,GAAgB,EAAE,CAAA;QAC9B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACjB,IAAM,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAA;YACrB,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;gBAC7B,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;gBAC3D,CAAC,CAAC,iBAAiB,GAAG,IAAI,CAAA;gBAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACjB;QACL,CAAC,CAAC,CAAA;QACF,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAA;QACzB,MAAM,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAA;IACxD,CAAC;IAED,OAAO;IACO,iCAAU,GAAxB,UAAyB,KAAa;;;;;;;wBAClC,IAAI,IAAI,CAAC,iBAAiB,EAAE;4BACxB,sBAAM;yBACT;wBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;wBACO,qBAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAA;;wBAA3E,KAA8B,SAA6C,EAAzE,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,YAAY,kBAAA;wBAC/B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;wBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BAChB,sBAAM;yBACT;6BAAM,IAAI,GAAG,KAAK,aAAK,CAAC,kBAAkB,EAAE;4BACzC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;4BAC1B,sBAAO,uBAAU,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAA;yBAChE;6BAAM,IAAI,GAAG,KAAK,aAAK,CAAC,0BAA0B,EAAE,EAAE,aAAa;4BAChE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;4BAC1B,sBAAO,uBAAU,CAAC,cAAc,CAAC,mCAAmC,CAAC,EAAA;yBACxE;6BAAM,IAAI,GAAG,EAAE;4BACZ,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;4BAC1B,sBAAO,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAA;yBACrD;wBACD,uBAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,UAAC,KAAsB,EAAE,WAAoB,EAAE,YAAoB;4BAC1I,IAAI,KAAI,CAAC,QAAQ,EAAE;gCACf,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gCAC1B,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAhC,CAAgC,CAAC,CAAA;6BAC/G;wBACL,CAAC,CAAC,CAAA;;;;;KACL;IAED,SAAS;IACK,iCAAU,GAAxB,UAAyB,KAAa;;;;;;;wBAClC,IAAI,IAAI,CAAC,iBAAiB,EAAE;4BACxB,sBAAM;yBACT;wBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;wBACO,qBAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAA;;wBAA3E,KAA8B,SAA6C,EAAzE,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,YAAY,kBAAA;wBAC/B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;wBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BAChB,sBAAM;yBACT;6BAAM,IAAI,GAAG,KAAK,aAAK,CAAC,SAAS,EAAE;4BAChC,sBAAO,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,EAAA;yBAC5D;6BAAM,IAAI,GAAG,EAAE;4BACZ,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;4BAC1B,sBAAO,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAA;yBACrD;wBACD,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,UAAC,IAAmB;4BACvF,IAAI,KAAI,CAAC,QAAQ,EAAE;gCACf,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gCAC1B,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAhC,CAAgC,CAAC,CAAA;6BACnF;wBACL,CAAC,CAAC,CAAA;;;;;KACL;IAED,OAAO;IACO,mCAAY,GAA1B,UAA2B,KAAa;;;;;4BACxB,qBAAM,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAA;;wBAA1C,GAAG,GAAG,SAAoC;wBAChD,IAAI,GAAG,EAAE;4BACL,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;4BACtB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;yBAC7B;;;;;KACJ;IAED,QAAQ;IACM,iCAAU,GAAxB,UAAyB,KAAa;;;;;;;wBAClC,IAAI,IAAI,CAAC,iBAAiB,EAAE;4BACxB,sBAAM;yBACT;wBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;wBACO,qBAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,EAAA;;wBAAxE,KAA8B,SAA0C,EAAtE,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,YAAY,kBAAA;wBAC/B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;wBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BAChB,sBAAM;yBACT;6BAAM,IAAI,GAAG,EAAE;4BACZ,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;4BAC1B,sBAAO,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAA;yBACrD;wBACD,uBAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,UAAC,KAAsB,EAAE,WAAoB;4BAClH,IAAI,KAAI,CAAC,QAAQ,EAAE;gCACf,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gCAC1B,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAhC,CAAgC,CAAC,CAAA;6BACnG;wBACL,CAAC,CAAC,CAAA;;;;;KACL;IAED,WAAW;IACH,wCAAiB,GAAzB;QAAA,iBAYC;QAXG,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,UAAA,KAAK;YACzC,IAAM,IAAI,GAAG,KAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,IAAI,EAAE;gBACN,IAAM,MAAI,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,GAAG,0BAAe,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC,EAAT,CAAS,CAAC,CAAA;gBACnF,IAAM,KAAG,GAAG,IAAI,CAAC,WAAW,EAAE,QAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;gBACtD,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,uBAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE,MAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,QAAM,EAAE,KAAI,CAAC,aAAa,EAAE,KAAG,EAAE,KAAI,CAAC,GAAG,CAAC,EAAvG,CAAuG,CAAC,CAAA;gBACjI,uBAAU,CAAC,iBAAiB,CAAC,KAAI,CAAC,eAAe,EAAE,KAAG,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBACjE,QAAQ;gBACR,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAA1C,CAA0C,CAAC,CAAA;aAC3E;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,cAAc;IACN,2CAAoB,GAA5B;QAAA,iBAqBC;QApBG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,OAAO,CAAC,UAAA,IAAI;;YAC3C,IAAM,IAAI,GAAG,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7C,IAAI,IAAI,EAAE;gBACN,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAA;gBAC5B,IAAM,KAAG,GAAG,EAAE,CAAA;gBACd,MAAA,IAAI,CAAC,WAAW,0CAAE,OAAO,CAAC,UAAA,EAAE;oBACxB,IAAI,KAAK,GAAG,KAAG,CAAC,EAAE,CAAC,CAAA;oBACnB,IAAI,CAAC,KAAK,EAAE;wBACR,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;wBAClD,KAAK,GAAG,KAAG,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,EAAE,KAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAA;qBAC7E;oBACD,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA;gBACpB,CAAC,EAAC;gBACF,KAAK,IAAI,GAAG,IAAI,KAAG,EAAE;oBACjB,IAAM,MAAI,GAAG,KAAG,CAAC,GAAG,CAAC,CAAA;oBACrB,uBAAU,CAAC,mBAAmB,CAAC,MAAI,CAAC,IAAI,EAAE,MAAI,CAAC,KAAK,EAAE,KAAI,CAAC,aAAa,EAAE,GAAG,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;iBAC3F;gBACD,uBAAU,CAAC,iBAAiB,CAAC,KAAI,CAAC,eAAe,EAAE,GAAG,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aACpE;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACD,oCAAa,GAArB;QAAA,iBAwBC;QAvBG,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,oBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;YACpC,IAAM,IAAI,GAAG,KAAI,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAA;YACpG,IAAI,IAAI,IAAI,IAAI,EAAE;gBACd,IAAI,IAAI,GAAY,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC9C,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,EAAE;oBACf,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAA;oBAC9D,IAAI,CAAC,OAAO,EAAE,CAAA;iBACjB;gBACD,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,CAAA;gBACxD,IAAI,GAAG,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAI,CAAC,cAAc,CAAC,CAAA;gBAC9E,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAA;gBACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;gBAC1B,uBAAU,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,CAAC;oBACtE,IAAI,KAAI,CAAC,OAAO,KAAI,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,OAAO,CAAA,EAAE;wBAC5B,OAAO,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;wBAChC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAA;wBAC3D,CAAC,CAAC,OAAO,EAAE,CAAA;qBACd;gBACL,CAAC,CAAC,CAAA;aACL;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,sCAAe,GAAvB,UAAwB,CAAS,EAAE,CAAS,EAAE,IAAc,EAAE,KAAa;QAA3E,iBASC;QARG,gCAAgC;QAChC,0EAA0E;QAC1E,yFAAyF;QACzF,6EAA6E;QAC7E,IAAI;QACJ,IAAM,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACpF,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,uBAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,MAAM,EAAE,KAAI,CAAC,aAAa,EAAE,GAAG,EAAE,KAAI,CAAC,GAAG,CAAC,EAAjG,CAAiG,CAAC,CAAA;QAC3H,uBAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IACrE,CAAC;IAED,OAAO;IACC,kCAAW,GAAnB;QAAA,iBAaC;;QAZG,IAAI,OAAO,SAAG,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,qBAAqB,CAAC,mCAAI,CAAC,CAAC,CAAA;QAC9F,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;QAC/C,IAAI,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAA,CAAC,QAAQ;SAClD;aAAM;YACH,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,SAAS,CAAC,KAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAtD,CAAsD,CAAC,CAAA;SAC7G;QACD,MAAM;QACN,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,CAAA;QAC3C,IAAI,OAAO,KAAK,OAAO,EAAE;YACrB,+BAAc,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,mBAAmB,EAAE,CAAC,CAAA;SACnD;IACL,CAAC;IAED,SAAS;IACK,uCAAgB,GAA9B,UAA+B,IAAY;;;;;wBACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;wBACtB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;wBAChF,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;wBAC1B,qBAAM,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAA;;wBAAlC,SAAkC,CAAA;wBAClC,IAAI,CAAC,wBAAwB,EAAE,CAAA;wBAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,CAAC,QAAQ;wBACpC,qBAAS,CAAC,aAAa,EAAE,CAAA;;;;;KAC5B;IAED,6BAAM,GAAN,UAAO,EAAU;;QACb,EAAE;QACF,MAAA,IAAI,CAAC,mBAAmB,0CAAE,MAAM,CAAC,EAAE,EAAC;QACpC,aAAa;QACb,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,YAAY;QACZ,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC7B,CAAC;IAEO,qCAAc,GAAtB;QACI,IAAM,KAAK,GAAG,qBAAS,CAAC,eAAe,CAAC,uBAAU,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QAC1F,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACzF,IAAI,IAAI,IAAI,yBAAc,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,KAAK,uBAAU,CAAC,SAAS,EAAE;YAChF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACrB,IAAI,CAAC,sBAAsB,EAAE,CAAA;SAChC;IACL,CAAC;IAED,kBAAkB;IACV,6CAAsB,GAA9B;;QACI,IAAM,MAAM,GAAG,EAAE,CAAA;QACjB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACjB,CAAC,CAAC,mBAAmB,EAAE,CAAA;YACvB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;QACxB,CAAC,CAAC,CAAA;QACF,iBAAiB;QACjB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7D,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;YAC5C,IAAI,CAAC,MAAM,OAAC,IAAI,CAAC,IAAI,0CAAE,GAAG,CAAC,EAAE;gBACzB,IAAI,CAAC,OAAO,EAAE,CAAA;aACjB;SACJ;IACL,CAAC;IAEO,yCAAkB,GAA1B;;QACI,IAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAA;QACzC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACzC,OAAM;SACT;QACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACpC,QAAQ;QACR,UAAI,IAAI,CAAC,YAAY,0CAAE,qBAAqB,IAAI;YAC5C,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;SAC7B;IACL,CAAC;IA9pCgB,YAAY;QADhC,OAAO;OACa,YAAY,CA+pChC;IAAD,mBAAC;CA/pCD,AA+pCC,CA/pCyC,EAAE,CAAC,YAAY,GA+pCxD;kBA/pCoB,YAAY", "file": "", "sourceRoot": "/", "sourcesContent": ["import { cameraCtrl } from \"../../common/camera/CameraCtrl\";\nimport { CAMERA_BG_COLOR, CELL_RES_FIELDS, CITY_FORT_NID, CITY_MAIN_NID, CTYPE_ICON, MAP_EXTRA_SIZE, MAP_MASK_ITEM_COLOR, MAP_SHOW_OFFSET, NOT_OCCUPY_BY_MAX_LAND_COUNT, SELECT_CELL_INFO_BOX, TILE_SIZE } from \"../../common/constant/Constant\";\nimport { ArmyShortInfo, PlayerInfo } from \"../../common/constant/DataType\";\nimport { ecode } from \"../../common/constant/ECode\";\nimport { DecorationType, LandType, PreferenceKey } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport NetEvent from \"../../common/event/NetEvent\";\nimport { animHelper } from \"../../common/helper/AnimHelper\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { guideHelper } from \"../../common/helper/GuideHelper\";\nimport { mapHelper } from \"../../common/helper/MapHelper\";\nimport { popupPnlHelper } from \"../../common/helper/PopupPnlHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport BuildObj from \"../../model/area/BuildObj\";\nimport CTypeObj from \"../../model/common/CTypeObj\";\nimport UserModel from \"../../model/common/UserModel\";\nimport AncientObj from \"../../model/main/AncientObj\";\nimport BaseMarchObj from \"../../model/main/BaseMarchObj\";\nimport BTCityObj from \"../../model/main/BTCityObj\";\nimport MapCellObj from \"../../model/main/MapCellObj\";\nimport PlayerModel from \"../../model/main/PlayerModel\";\nimport TondenObj from \"../../model/main/TondenObj\";\nimport WorldModel from \"../../model/main/WorldModel\";\nimport MapTouchCmpt from \"../cmpt/MapTouchCmpt\";\nimport SelectCellCmpt from \"../cmpt/SelectCellCmpt\";\nimport CellInfoCmpt from \"./CellInfoCmpt\";\nimport MapAnimNodePool from \"./MapAnimNodePool\";\nimport MarchCmpt from \"./MarchCmpt\";\nimport SceneEffectCmpt from \"./SceneEffectCmpt\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class MainWindCtrl extends mc.BaseWindCtrl {\n\n    //@autocode property begin\n    private mapNode_: cc.Node = null // path://root/map_n\n    private cellEffectNode_: cc.Node = null // path://root/map_n/cell_effect_n\n    private selectCellNode_: cc.Node = null // path://root/select_cell_n\n    private textNode_: cc.Node = null // path://root/text_n\n    private ancientTextNode_: cc.Node = null // path://root/ancient_text_n\n    private marchLineNode_: cc.Node = null // path://root/march/march_line_n\n    private marchRoleNode_: cc.Node = null // path://root/march/march_role_n\n    private cellEmojiNode_: cc.Node = null // path://root/cell_emoji_n\n    private sceneEffectNode_: cc.Node = null // path://root/scene_effect_n\n    private topLayerNode_: cc.Node = null // path://root/top_layer_n\n    private weakGuideNode_: cc.Node = null // path://root/weak_guide_n\n    //@end\n\n    private readonly INIT_KEY = '_init_main_'\n\n    private diNode: cc.Node = null//装饰\n    private mountainNode: cc.Node = null//山脉\n    private protectLineNode: cc.Node = null //保护线\n    private lineNode: cc.Node = null\n    private seawaveNode: cc.Node = null //海浪\n    private landNode: cc.Node = null\n    private cityNode: cc.Node = null //城市层\n    private maskNode: cc.Node = null //遮罩层\n    private btinfoNode: cc.Node = null //修建信息层\n    private outputNode: cc.Node = null //产出层\n    private iconNode: cc.Node = null //小图标层\n    private tondenNode: cc.Node = null //屯田中图标层\n    private battleNode: cc.Node = null //战斗中图标层\n    private mapFlagNode: cc.Node = null //地图标记\n    private cellInfoCmpt: CellInfoCmpt = null\n    private touchCmpt: MapTouchCmpt = null\n    private sceneEffect: SceneEffectCmpt = null\n    private cellEmojiItemMap: { [type: number]: cc.Node } = {}\n    private seasonType: number = 0\n\n    private model: WorldModel = null\n    private user: UserModel = null\n    private player: PlayerModel = null\n    private centre: cc.Vec2 = cc.v2() //当前的中心位置\n    private preCameraZoomRatio: number = 0\n    private preCameraPosition: cc.Vec2 = cc.v2()\n    private marchs: MarchCmpt[] = [] //当前所有行军\n    private tempShowCellMap: { [key: number]: MapCellObj } = {} //当前屏幕显示的地块信息\n    private reqSelectArmysing: boolean = false //当前是否请求军队列表中\n    private cellEmojiMap: any = {} //当前的领地表情map\n    private cityAnimNodePool: MapAnimNodePool = null //城市节点管理\n    private seawaveAnimNodePool: MapAnimNodePool = null //海浪节点管理\n\n    private _temp_vec2_1: cc.Vec2 = cc.v2()\n    private _temp_vec2_2: cc.Vec2 = cc.v2()\n    private _temp_vec2_3: cc.Vec2 = cc.v2()\n    private _temp_vec2_4: cc.Vec2 = cc.v2()\n    private _temp_vec2_5: cc.Vec2 = cc.v2()\n\n    public listenEventMaps() {\n        return [\n            { [NetEvent.NET_RECONNECT]: this.onNetReconnect, enter: true },\n            { [EventType.UPDATE_CELL_INFO]: this.onUpdateCellInfo, enter: true },\n            { [EventType.ADD_MARCH]: this.onAddMarch, enter: true },\n            { [EventType.REMOVE_MARCH]: this.onRemoveMarch, enter: true },\n            { [EventType.UPDATE_ALL_MARCH]: this.onUpdateAllMarch, enter: true },\n            { [EventType.HIDE_WORLD_TEXT]: this.onHideWorldText, enter: true },\n            { [EventType.CLOSE_SELECT_CELL]: this.onCloseSelectCell, enter: true },\n            { [EventType.UPDATE_BATTLE_DIST_INFO]: this.onUpdateBattleDistInfo, enter: true },\n            { [EventType.UPDATE_AVOIDWAR_DIST_INFO]: this.onUpdateAvoidWarDistInfo, enter: true },\n            { [EventType.UPDATE_BT_CITY]: this.onUpdateBtCity, enter: true },\n            { [EventType.UPDATE_TONDEN]: this.onUpdateTonden, enter: true },\n            { [EventType.UPDATE_ARMY_DIST_INFO]: this.onUpdateArmyDistInfo, enter: true },\n            { [EventType.MAP_MOVE_TO]: this.onMapMoveTo, enter: true },\n            { [EventType.UPDATE_PLAYER_NICKNAME]: this.onUpdatePlayerNickname, enter: true },\n            { [EventType.UPDATE_PLAYER_HEAD_ICON]: this.onUpdatePlayerHeadIcon, enter: true },\n            { [EventType.UPDATE_ALLI_MAP_FLAG]: this.onUpdateAlliMapFlag, enter: true },\n            { [EventType.UPDATE_MARCH_OPACITY]: this.onUpdateMarchOpacity, enter: true },\n            { [EventType.PLAY_NEW_CELL_EFFECT]: this.onPlayNewCellEffect, enter: true },\n            { [EventType.PLAY_CELL_TONDEN_EFFECT]: this.onPlayCellTondenEffect, enter: true },\n            { [EventType.PLAY_CELL_EMOJI]: this.onPlayCellEmoji, enter: true },\n            { [EventType.UPDATE_CITY_OUTPUT]: this.onUpdateCityOutput, enter: true },\n            { [EventType.CHANGE_SEASON_COMPLETE]: this.onChangeSeasonComplete, enter: true },\n            { [EventType.UPDATE_ANCIENT_INFO]: this.onUpdateAncientInfo, enter: true },\n            { [EventType.UPDATE_CITY_SKIN]: this.onUpdateCitySkin, enter: true },\n            { [EventType.WEAK_GUIDE_SHOW_NODE_CHOOSE]: this.onWeakGuideShowNodeChoose, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.setParam({ isClean: false })\n        this.diNode = this.mapNode_.FindChild('di')\n        this.mountainNode = this.mapNode_.FindChild('mountain')\n        this.protectLineNode = this.mapNode_.FindChild('protect_line')\n        this.lineNode = this.mapNode_.FindChild('line')\n        this.seawaveNode = this.mapNode_.FindChild('seawave')\n        this.landNode = this.mapNode_.FindChild('land')\n        this.cityNode = this.mapNode_.FindChild('city')\n        this.maskNode = this.mapNode_.FindChild('mask')\n        this.btinfoNode = this.mapNode_.FindChild('btinfo')\n        this.outputNode = this.mapNode_.FindChild('output')\n        this.iconNode = this.mapNode_.FindChild('icon')\n        this.tondenNode = this.mapNode_.FindChild('tonden')\n        this.battleNode = this.mapNode_.FindChild('battle')\n        this.mapFlagNode = this.mapNode_.FindChild('map_flag')\n        for (let i = 2; i <= 3; i++) {\n            const item = this.cellEmojiItemMap[i] = this.cellEmojiNode_.FindChild('item_' + i)\n            item.parent = null\n        }\n        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt)\n        this.cellInfoCmpt = this.FindChild('root/cell_info', CellInfoCmpt)\n        this.cityAnimNodePool = new MapAnimNodePool().init(this.cityNode, resHelper.getCityPrefab.bind(resHelper))\n        this.model = this.getModel('world')\n        this.user = this.getModel('user')\n        this.player = this.getModel('player')\n        // this.seawaveAnimNodePool = new MapAnimNodePool().init(this.seawaveNode, resHelper.getSeawavePrefab.bind(resHelper))\n        // this.seawaveAnimNodePool.setAnimInfo('land_104', 1.76)\n        this.selectCellNode_.active = false\n        this.cellInfoCmpt.close()\n        this.sceneEffect = this.sceneEffectNode_.getComponent(SceneEffectCmpt)\n        this.updateSeasonSeceneEffect()\n        this.maskNode.children[0].color = cc.Color.WHITE.fromHEX(MAP_MASK_ITEM_COLOR[this.model.getSeasonType()])\n    }\n\n    public async onReady() {\n\n    }\n\n    public onEnter(data: any) {\n        this.model.initCameraInfo()\n        this.cellEffectNode_.Data = true\n        this.cellEmojiNode_.Data = true\n        this.topLayerNode_.Data = true\n        this.checkSeason()\n        this.initMarch() //初始化行军\n        this.playNewCellEffect()\n        this.playCellTondenEffect()\n        this.playCellEmoji()\n        this.touchCmpt.init(this.onClickMap.bind(this))\n        gameHpr.playMainBgm()\n        cameraCtrl.setBgColor(CAMERA_BG_COLOR[this.model.getSeasonType()])\n    }\n\n    public onLeave() {\n        this.model.saveCameraInfo()\n        this.touchCmpt.clean()\n        this.selectCellNode_.Component(SelectCellCmpt).close()\n        this.cellInfoCmpt.close()\n        this.reqSelectArmysing = false\n        this.cleanMarch()\n        this.cellEffectNode_.removeAllChildren()\n        this.cellEffectNode_.Data = false\n        this.cellEmojiNode_.removeAllChildren()\n        this.cellEmojiNode_.Data = false\n        this.topLayerNode_.removeAllChildren()\n        this.topLayerNode_.Data = false\n        // resHelper.cleanNodeChildren(this.diNode) 这里暂时不清理 因为进入其他场景太慢了\n        this.cellEmojiMap = {}\n        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key)\n        assetsMgr.releaseTempResByTag(this.key)\n        cameraCtrl.setBgColor('#D1F1F3')\n    }\n\n    public onClean() {\n        for (let k in this.cellEmojiItemMap) {\n            this.cellEmojiItemMap[k].destroy()\n        }\n        this.cellEmojiItemMap = {}\n        this.cellEmojiMap = {}\n        this.cellInfoCmpt.clean()\n        this.sceneEffect?.clean()\n        assetsMgr.releaseTempResByTag(this.INIT_KEY)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/cell_info/buttons/enter_be\n    onClickEnter(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell) {\n            this.model.setLookCell(cell)\n            viewHelper.gotoWind('area')\n            this.hideSelectCell(false)\n        }\n    }\n\n    // path://root/cell_info/buttons/occupy_be\n    onClickOccupy(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (!cell) {\n            return\n        } else if (!this.model.checkCanOccupyCell(cell)) {\n            return viewHelper.showAlert(ecode.ONLY_ATTACK_ADJOIN_CELL)\n        } else if (cell.checkAttackByProtect()) { //是否有保护\n            return viewHelper.showAlert(ecode.CELL_PROTECT)\n        } else if (cell.isAvoidWar()) { //是否金盾\n            return viewHelper.showAlert(ecode.AVOID_WAR_NOT_ATTACK)\n        }\n        this.occupyCell(cell.actIndex)\n    }\n\n    // path://root/cell_info/buttons/tonden_be\n    onClickTonden(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (!cell || !cell.isOwn()) {\n            return\n        } else if (gameHpr.isBattleingByIndex(cell.index)) {\n            return viewHelper.showAlert('toast.battleing_not_tonden')\n        } else if (cell.isBTCitying()) {\n            return viewHelper.showAlert('toast.bting_not_tonden')\n        }\n        this.cellTonden(cell.actIndex)\n    }\n\n    // path://root/cell_info/buttons/cancel_tonden_be\n    onClickCancelTonden(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (!cell || !cell.isOwn() || !cell.isTondening()) {\n            return\n        }\n        viewHelper.showMessageBox('ui.cancel_tonden_tip', {\n            ok: () => this.isActive && this.cancelTonden(cell.actIndex),\n            cancel: () => { },\n        })\n    }\n\n    // path://root/cell_info/buttons/move_be\n    onClickMove(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (!cell || !cell.isOneAlliance()) {\n            return\n        }\n        this.moveToCell(cell.actIndex)\n    }\n\n    // path://root/cell_info/buttons/build_be\n    onClickBuild(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (!cell || !cell.isOwn()) {\n            return\n        } else if (cell.isTondening()) {\n            return viewHelper.showAlert('toast.tondening_not_bt')\n        }\n        viewHelper.showPnl('main/CityList', cell)\n    }\n\n    // path://root/cell_info/buttons/dismantle_be\n    onClickDismantle(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (!cell || !cell.isOwn()) {\n            return\n        }\n        viewHelper.showPnl('main/DismantleCityTip', cell)\n    }\n\n    // path://root/cell_info/buttons/player_info_be\n    onClickPlayerInfo(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        const info = this.model.getPlayerInfo(cell?.owner)\n        if (info) {\n            viewHelper.showPnl('common/PlayerInfo', info, 'cellinfo')\n        }\n    }\n\n    // path://root/cell_info/title/share_pos_be\n    onClickSharePos(event: cc.Event.EventTouch, data: string) {\n        audioMgr.playSFX('click')\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell) {\n            const point = cell.actPoint.Join(',')\n            viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_point_to_chat_tip', params: [point] }, (type: number, select: { type: PreferenceKey, channel: string }) => {\n                if (this.isValid) {\n                    viewHelper.showPnl('common/Chat', { tab: type, text: `[${point}]` }).then(() => this.isValid && this.hideSelectCell(false))\n                }\n            })\n        }\n    }\n\n    // path://root/cell_info/buttons/flag_be\n    onClickFlag(event: cc.Event.EventTouch, data: string) {\n        if (!gameHpr.alliance.isMeMilitary()) {\n            return\n        }\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell) {\n            viewHelper.showPnl('main/SelectFlagIcon', cell.actIndex)\n        }\n    }\n\n    // path://root/cell_info/info/score/score_desc_be\n    onClickScoreDesc(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell) {\n            viewHelper.showPnl('main/LandScoreDesc', cell.landLv)\n        }\n    }\n\n    // path://root/cell_info/info/stamina/stamina_desc_be\n    onClickStaminaDesc(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell) {\n            viewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType)\n        }\n    }\n\n    // path://root/cell_info/title/cell_emoji_be\n    onClickCellEmoji(event: cc.Event.EventTouch, data: string) {\n        audioMgr.playSFX('click')\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell) {\n            viewHelper.showPnl('main/CellSelectEmoji', cell.isOwn(), (id: number) => {\n                if (id) {\n                    gameHpr.ground.sendCellEmoji(id, cell.actIndex)\n                }\n                if (this.isValid) {\n                    this.hideSelectCell(false)\n                }\n            })\n        }\n    }\n\n    // path://root/map_n/output/item/city_output_be\n    onClickCityOutput(event: cc.Event.EventTouch, data: string) {\n        const cell: MapCellObj = event.target.parent.Data\n        if (!cell || !cell.isOwn()) {\n            return\n        }\n        const rewards = gameHpr.getPlayerInfo(gameHpr.getUid())?.cityOutputMap[cell.index] || []\n        this.model.claimCityOutput(cell.index).then(err => {\n            if (err) {\n                return viewHelper.showAlert(err)\n            }\n            // gameHpr.addGainMassage(rewards)\n            rewards.forEach((m, i) => animHelper.playFlutterCellOutput(i * 0.4, m, this.topLayerNode_, cell.actPosition, this.key))\n        })\n    }\n\n    // path://root/cell_info/buttons/ancient_info_be\n    onClickAncientInfo(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell) {\n            const ancient = this.model.getAncientInfo(cell.index)\n            if (ancient) {\n                const build = new BuildObj().init(cell.index, ut.UID(), cc.v2(0, 0), ancient.id, ancient.lv)\n                viewHelper.showPnl('build/BuildAncientBase', build)\n            }\n        }\n    }\n\n    // path://root/cell_info/buttons/city_skin_be\n    onClickCitySkin(event: cc.Event.EventTouch, data: string) {\n        const cell = this.cellInfoCmpt.getCell()\n        if (cell && cell.cityId > 0) {\n            viewHelper.showPnl('main/SelectCitySkin', cell, (ok: boolean) => {\n                if (this.isValid && ok) {\n                    this.hideSelectCell(false)\n                }\n            })\n        }\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    private onNetReconnect() {\n        if (!gameHpr.guide.isOneGuideWorking()) { //这里如果在新手引导 就不要关闭了\n            this.selectCellNode_.Component(SelectCellCmpt).close()\n            this.cellInfoCmpt.close()\n            this.model.initCameraInfo()\n        }\n        this.updateMap(this.centre) //刷新地图显示\n        this.initMarch() //初始化行军\n    }\n\n    // 更新地块信息\n    private onUpdateCellInfo() {\n        this.updateMap(this.centre)\n    }\n\n    // 添加行军\n    private onAddMarch(data: BaseMarchObj) {\n        if (!data.isCanShowMarch()) {\n            return this.onRemoveMarch(data)\n        }\n        let march = this.marchs.find(m => m.uid === data.uid)\n        if (march) {\n            march.init(data, this.marchRoleNode_, this.key)\n            this.checkMarchLineOffset(data)\n        } else {\n            this.marchLineNode_.AddItem(it => {\n                march = this.marchs.add(it.Component(MarchCmpt).init(data, this.marchRoleNode_, this.key))\n                this.checkMarchLineOffset(data)\n            })\n        }\n    }\n\n    // 删除行军\n    private onRemoveMarch(data: BaseMarchObj) {\n        const march = this.marchs.remove('uid', data.uid)\n        if (march) {\n            march.clean()\n            this.checkMarchLineOffset(data)\n        }\n    }\n\n    // 刷新所有行军\n    private onUpdateAllMarch() {\n        this.initMarch()\n    }\n\n    // 隐藏文本\n    private onHideWorldText(val: boolean) {\n        val = !val\n        this.textNode_.active = val\n        this.ancientTextNode_.active = val\n        this.btinfoNode.children.forEach(m => m.Data && (m.Child('time').active = val))\n        this.tondenNode.children.forEach(m => m.Data && (m.Child('time').active = val))\n    }\n\n    // 关闭选择地块\n    private onCloseSelectCell(play: boolean) {\n        this.hideSelectCell(!!play)\n    }\n\n    // 刷新战斗状态\n    private onUpdateBattleDistInfo() {\n        const cells: MapCellObj[] = [], distMap = this.model.getBattleDistMap()\n        for (let index in distMap) {\n            const cell = this.tempShowCellMap[index]\n            cell && cells.push(cell)\n        }\n        this.battleNode.Items(cells, (it, data) => it.setPosition(data.actPosition))\n    }\n\n    // 刷新免战状态\n    private onUpdateAvoidWarDistInfo() {\n        this.updateIconNode()\n    }\n\n    // 刷新修建信息\n    private onUpdateBtCity(index: number) {\n        this.updateMap(this.centre)\n        if (this.cellInfoCmpt.getCell()?.actIndex === index) {\n            this.cellInfoCmpt.updateInfo()\n        }\n    }\n\n    // 刷新屯田信息\n    private onUpdateTonden(index: number) {\n        this.updateMap(this.centre)\n        if (this.cellInfoCmpt.getCell()?.actIndex === index) {\n            this.cellInfoCmpt.updateInfo()\n        }\n    }\n\n    // 刷新地图上面的军队分布情况  这里主动绘制一次\n    private onUpdateArmyDistInfo() {\n        this.updateIconNode()\n        this.cellInfoCmpt.updateArmyInfo()\n    }\n\n    // 移动地图\n    private onMapMoveTo(point: cc.Vec2, showCellInfo: boolean) {\n        if (this.centre.equals(point)) {\n            return showCellInfo && this.showSelectCell(this.model.getMapCellByPoint(point.clone().floor()))\n        } else if (!this.tempShowCellMap[mapHelper.pointToIndex(point)]) { //如果没有在当前绘制区域就移动到目标点\n            const start = this.centre.sub(point, this._temp_vec2_4).normalizeSelf().mulSelf(2).addSelf(point)\n            cameraCtrl.init(mapHelper.getPixelByPoint(start), mapHelper.MAP_SIZE, MAP_SHOW_OFFSET, cameraCtrl.zoomRatio)\n            this.updateMap(start.floor())\n            this.checkInCameraMarchLine()\n        }\n        // 移动\n        cameraCtrl.moveTo(0.25, mapHelper.getPixelByPoint(point).subSelf(cameraCtrl.getWinSizeHalf())).then(() => {\n            if (this.isActive && showCellInfo) {\n                this.showSelectCell(this.model.getMapCellByPoint(point.clone().floor()))\n            }\n        })\n    }\n\n    // 刷新玩家昵称\n    private onUpdatePlayerNickname(data: PlayerInfo) {\n        const it = this.textNode_.children.find(m => m.active && m.Data?.owner === data.uid)\n        if (it) {\n            this.updatePlayerNickname(it, data)\n        }\n    }\n\n    // 刷新玩家头像\n    private onUpdatePlayerHeadIcon(data: any) {\n        const it = this.textNode_.children.find(m => m.active && m.Data?.owner === data.uid)\n        if (it) {\n            resHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, this.INIT_KEY)\n        }\n    }\n\n    // 刷新联盟地图标记\n    private onUpdateAlliMapFlag() {\n        const cells: { cell: MapCellObj, flag: number }[] = [], mapFalg = gameHpr.alliance.getMapFlag()\n        for (let index in mapFalg) {\n            const cell = this.tempShowCellMap[index]\n            cell && cells.push({ cell, flag: mapFalg[index] })\n        }\n        this.mapFlagNode.Items(cells, (it, data) => {\n            it.setPosition(data.cell.actPosition)\n            it.Child('root/val', cc.Sprite).spriteFrame = resHelper.getMapFlagNumber(data.flag)\n        })\n    }\n\n    // 刷新行军线透明度\n    private onUpdateMarchOpacity() {\n        this.marchs.forEach(m => m.updateOpacity())\n    }\n\n    // 播放新的地块效果\n    private onPlayNewCellEffect() {\n        this.playNewCellEffect()\n    }\n\n    // 播放屯田结束效果\n    private onPlayCellTondenEffect() {\n        this.playCellTondenEffect()\n    }\n\n    // 播放地图表情\n    private onPlayCellEmoji() {\n        this.playCellEmoji()\n    }\n\n    // 刷新城市产出\n    private onUpdateCityOutput() {\n        const cells: { cell: MapCellObj, output: CTypeObj }[] = []\n        for (let index in this.tempShowCellMap) {\n            const cell = this.tempShowCellMap[index]\n            const output = cell.getOutputType()\n            if (output) {\n                cells.push({ cell, output: output })\n            }\n        }\n        this.outputNode.Items(cells, (it, data) => {\n            it.setPosition(data.cell.actPosition)\n            it.Child('city_output_be').active = data.cell.isOwn()\n            it.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(CTYPE_ICON[data.output.type])\n            it.Child('root/val', cc.Label).string = data.output.count > 1 ? data.output.count + '' : ''\n        })\n    }\n\n    // 改变季节完成\n    private onChangeSeasonComplete() {\n        this.playChangeSeason(this.model.getSeason().type)\n    }\n\n    // 刷新遗迹\n    private onUpdateAncientInfo(data: AncientObj) {\n        const index = data.index\n        const it = this.ancientTextNode_.children.find(m => m.active && m.Data?.index === index)\n        if (it) {\n            this.updateAncientTextInfo(it, data)\n        }\n    }\n\n    // 刷新城市皮肤\n    private onUpdateCitySkin(index: number) {\n        this.updateMap(this.centre)\n    }\n\n    // 若引导\n    private onWeakGuideShowNodeChoose(data: any) {\n        if (data.scene === 'main') {\n            guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key)\n        }\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private get isActive() { return this.isValid && this.isEnter() }\n\n    // 点击地图\n    private onClickMap(worldLocation: cc.Vec2) {\n        const cell = this.model.getMapCellByPoint(mapHelper.getPointByPixel(worldLocation))\n        if (cell && !this.selectCellNode_.Data) {\n            audioMgr.playSFX('click')\n            this.showSelectCell(cell)\n            cameraCtrl.redressPositionByRange(cell.actPosition, SELECT_CELL_INFO_BOX)\n        } else {\n            this.hideSelectCell()\n        }\n    }\n\n    // 刷新场景特效\n    private updateSeasonSeceneEffect() {\n        this.sceneEffect.clean()\n        const sceneEffectUrl = this.model.getSeason().getCurrSceneEffectUrl()\n        if (sceneEffectUrl) { //加载场景特效\n            this.sceneEffect.init(sceneEffectUrl, this.INIT_KEY)\n        }\n    }\n\n    // 绘制地图\n    private updateMap(centre: cc.Vec2) {\n        this.model.initDecorationUseLand()\n        this.preCameraZoomRatio = cameraCtrl.zoomRatio\n        this.centre.set(centre)\n        this.model.setCentre(centre)\n        // 绘制地面\n        const armyDistMap = this.player.getArmyDistMap(), battleDist = this.model.getBattleDistMap()\n        const mapFlag = gameHpr.alliance.getMapFlag()\n        const btCityMap = this.model.getBTCityQueueMap()\n        const tondenMap = this.model.getTondenQueueMap()\n        let di = 0, linei = 0, li = 0, mi = 0, ii = 0, ti = 0, bi = 0, mfi = 0, oi = 0, mti = 0, pli = 0\n        const texts: MapCellObj[] = [], tondens: any[] = [], btCitys: any[] = [], ancientTexts: AncientObj[] = []\n        // this.seawaveAnimNodePool?.reset()\n        this.cityAnimNodePool.reset()\n        this.tempShowCellMap = {}\n        const points = mapHelper.getRangePointsByPoint(centre, this.model.getMaxTileRange())\n        const seasonType = this.seasonType\n        let tempDecorationLoadMap = {}\n        for (let i = 0; i < points.length; i++) {\n            const point = points[i], cell = this.model.getMapCellByPoint(point)\n            const position = cell?.position || mapHelper.getPixelByPoint(point)\n            if (cell) {\n                const btInfo = btCityMap[cell.index], tondenInfo = tondenMap[cell.index]\n                this.tempShowCellMap[cell.index] = cell\n                if (cell.cityId > 0) {\n                    const animName = cell.cityId === CITY_FORT_NID ? 'city_2102_' + cell.getOwnType() : undefined\n                    const city = this.cityAnimNodePool.showNode(cell.getCityViewId(), cell.actPosition, true, animName)\n                    if (cell.cityId === CITY_MAIN_NID) {\n                        // 这里先获取后面用来显示文本\n                        texts.push(cell)\n                        // 是否有保护模式 绘制保护线\n                        const state = gameHpr.checkPlayerProtectModeState(cell.owner)\n                        if (state > 0) {\n                            resHelper.getNodeByIndex(this.protectLineNode, pli++, cell.actPosition).opacity = state === 1 ? 255 : 100\n                        }\n                    } else if (cell.isAncient()) {\n                        const info = this.model.getAncientInfo(cell.index)\n                        if (info) {\n                            ancientTexts.push(info) //遗迹\n                            city.Child('val', cc.MultiFrame).setFrame(info.lv === 20)\n                        }\n                    }\n                } else if (cell.cityId < 0) {\n                } else if (tondenInfo) { //绘制屯田地\n                    tondens.push({ tondenInfo, cell })\n                    resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon('land_tonden', seasonType)\n                } else if (cell.icon && (!btInfo || btInfo.id === 0)) {\n                    resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon(cell.icon, seasonType)\n                }\n                // 绘制修建信息\n                if (btInfo) {\n                    btCitys.push({ btInfo, cell })\n                    // 只绘制修建 不绘制拆除\n                    if (cell.cityId === 0 && btInfo.id > 0) {\n                        this.cityAnimNodePool.showNode(btInfo.id, cell.actPosition, false)\n                    }\n                }\n                // 绘制地图军队分布图标\n                if (!!armyDistMap[cell.index]) {\n                    // 下面是否主城\n                    const y = (!cell.isMainCity() && this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))?.isMainCity()) ? -6 : -22\n                    const pos = this._temp_vec2_3.set2(-22, y).addSelf(cell.position) //显示到左下角\n                    resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('army_min_icon')\n                }\n                // 绘制免战图标\n                if (cell.isCanShowAvoidWar()) {\n                    // 下面是否主城\n                    const y = (!cell.isMainCity() && this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))?.isMainCity()) ? -6 : -22\n                    const pos = this._temp_vec2_3.set2(22, y).addSelf(cell.getRightPosition(this._temp_vec2_5)) //显示到右下角\n                    resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('avoidwar_icon_1')\n                }\n                // 绘制战斗图标\n                if (!!battleDist[cell.index]) {\n                    resHelper.getNodeByIndex(this.battleNode, bi++, cell.actPosition)\n                }\n                const flag = mapFlag[cell.index]\n                if (flag) { //地图标记\n                    resHelper.getNodeByIndex(this.mapFlagNode, mfi++, cell.actPosition).Child('root/val', cc.Sprite).spriteFrame = resHelper.getMapFlagNumber(flag)\n                }\n                // 绘制产出\n                const output = cell.getOutputType()\n                if (output) {\n                    const oNode = resHelper.getNodeByIndex(this.outputNode, oi++, cell.actPosition)\n                    oNode.Data = cell\n                    oNode.Child('city_output_be').active = cell.isOwn()\n                    oNode.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(CTYPE_ICON[output.type])\n                    oNode.Child('root/val', cc.Label).string = output.count > 1 ? output.count + '' : ''\n                }\n                // 记录边框线\n                const borderLines = cell.owner ? cell.borderLines : []\n                if (borderLines.length > 0) {\n                    const lineItemNode = resHelper.getNodeByIndex(this.lineNode, linei++, position)\n                    viewHelper.updateCellBorderLines(lineItemNode, borderLines, cell.getBorderLineColor())\n                }\n                // 绘制遮罩\n                if (!cell.owner) {\n                    const maskItemNode = resHelper.getNodeByIndex(this.maskNode, mi++, position)\n                    maskItemNode.opacity = cell.getProtectOwner() ? 20 : 38\n                }\n                //绘制地图装饰\n                let decorationCell = cell\n                if (!decorationCell.decorationJson && this.model.getDecorationIndex(cell.index)) {\n                    decorationCell = this.model.getMapCells()[this.model.getDecorationIndex(cell.index)]\n                }\n                if (decorationCell.decorationJson && !tempDecorationLoadMap[decorationCell.index]) {\n                    tempDecorationLoadMap[decorationCell.index] = true\n                    let itemNode: cc.Node = null\n                    let iconName = this.model.getDecorationIcon(decorationCell)\n                    let frame = resHelper.getLandItemIcon(iconName, seasonType)\n                    switch (decorationCell.decorationJson.type) {\n                        case DecorationType.MOUNTAIN:\n                            itemNode = resHelper.getNodeByIndex(this.mountainNode, mti++, decorationCell.position)\n                            if (!decorationCell.mountainAnchor) {\n                                //偶数倍数时锚点偏移\n                                let size = frame.getOriginalSize()\n                                let anchorX = 0.5, anchorY = 0.5\n                                if (size.width / TILE_SIZE % 2 == 0) {\n                                    anchorX = (size.width / 2 - TILE_SIZE / 2) / size.width\n                                }\n                                if (size.height / TILE_SIZE % 2 == 0) {\n                                    anchorY = (size.height / 2 - TILE_SIZE / 2) / size.height\n                                }\n                                decorationCell.mountainAnchor = cc.v2(anchorX, anchorY)\n                            }\n                            itemNode.setAnchorPoint(decorationCell.mountainAnchor)\n                            break;\n                        default:\n                            itemNode = resHelper.getNodeByIndex(this.diNode, di++, decorationCell.position)\n                            break;\n                    }\n                    itemNode.Component(cc.Sprite).spriteFrame = frame\n                }\n\n            } else {\n                let landId = this.model.getRoundId(point.x, point.y)\n                if (landId) {\n                    let itemInfo = assetsMgr.getJsonData('land', landId)\n                    resHelper.getNodeByIndex(this.diNode, di++, position).Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon(itemInfo.icon, seasonType)\n                    resHelper.getNodeByIndex(this.maskNode, mi++, position)\n                }\n            }\n        }\n        // 隐藏多余的\n        resHelper.hideNodeByIndex(this.diNode, di)\n        resHelper.hideNodeByIndex(this.protectLineNode, pli)\n        resHelper.hideNodeByIndex(this.lineNode, linei)\n        resHelper.hideNodeByIndex(this.landNode, li)\n        resHelper.hideNodeByIndex(this.maskNode, mi)\n        resHelper.hideNodeByIndex(this.iconNode, ii)\n        resHelper.hideNodeByIndex(this.battleNode, bi)\n        resHelper.hideNodeByIndex(this.mapFlagNode, mfi)\n        resHelper.hideNodeByIndex(this.outputNode, oi)\n        resHelper.hideNodeByIndex(this.mountainNode, mti)\n        // this.seawaveAnimNodePool?.hideOtherNode()\n        this.cityAnimNodePool.hideOtherNode()\n        // 当前正在显示的\n        const showIndex = this.cellInfoCmpt.getCell()?.actIndex ?? -1\n        const zIndexMaxY = mapHelper.MAP_SIZE.y\n        const isCanShowText = !this.touchCmpt.isDraging()\n        // 绘制文本层\n        this.textNode_.Items(texts, (it, data) => {\n            const pos = data.actPosition, index = data.actIndex\n            const d = it.Data\n            const info = gameHpr.getPlayerInfo(data.owner)\n            it.setPosition(pos.x, pos.y + 76)\n            if (!d || d.owner !== data.owner || d.nickname !== info?.nickname || d.title !== info?.title || d.headIcon !== info?.headIcon) {\n                resHelper.loadPlayerHead(it.Child('head', cc.Sprite), info?.headIcon, this.INIT_KEY)\n                this.updatePlayerNickname(it, info)\n            }\n            it.active = showIndex !== index\n            it.Data = { index, owner: data.owner, nickname: info?.nickname, title: info?.title, headIcon: info?.headIcon }\n        })\n        this.textNode_.active = isCanShowText\n        // 绘制遗迹文本层\n        this.ancientTextNode_.Items(ancientTexts, (it, data) => {\n            const pos = data.cell.actPosition, index = data.index\n            it.setPosition(pos.x, pos.y + 72)\n            this.updateAncientTextInfo(it, data)\n            it.active = showIndex !== index\n            it.Data = { index }\n        })\n        this.ancientTextNode_.active = isCanShowText\n        // 绘制修建城市的信息\n        this.btinfoNode.Items(btCitys, (it, data) => {\n            const info: BTCityObj = data.btInfo, index = info.index\n            it.setPosition(data.cell.actPosition)\n            const timeNode = it.Child('time')\n            if (it.Data?.index !== index) {\n                const surplusTime = info.getSurplusTime()\n                timeNode.Color(info.id ? '#21DC2D' : '#FF9162')\n                timeNode.Component(cc.LabelTimer).run(surplusTime * 0.001)\n                // 动画\n                const anim = it.Child('anim', cc.Animation)\n                const elapsedTime = Math.max(0, info.needTime - surplusTime) * 0.001\n                const tween = cc.tween(it)\n                tween.stop()\n                if (elapsedTime < 0.62) {\n                    anim.play('cting_begin', elapsedTime)\n                    tween.delay(0.62 - elapsedTime).call(() => this.isValid && anim.play('cting_loop')).start()\n                } else {\n                    anim.play('cting_loop')\n                }\n            }\n            timeNode.active = isCanShowText && showIndex !== index\n            it.Data = { index }\n            it.zIndex = zIndexMaxY - data.cell.actPoint.y\n        })\n        // 绘制屯田信息\n        this.tondenNode.Items(tondens, (it, data) => {\n            const info: TondenObj = data.tondenInfo, index = info.index\n            it.setPosition(data.cell.actPosition)\n            const timeNode = it.Child('time')\n            if (it.Data?.index !== index) {\n                timeNode.Component(cc.LabelTimer).run(info.getSurplusTime() * 0.001)\n            }\n            timeNode.active = isCanShowText\n            it.Data = { index }\n            it.zIndex = zIndexMaxY - data.cell.actPoint.y\n        })\n    }\n\n    // 0.上 1.右 2.下 3.左 4.左上 5.右上 6.右下 7.左下\n    private getSeaLandIcon(point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {\n        if (point.x < minx) {\n            return point.y < miny ? 7 : (point.y < maxy ? 3 : 4)\n        } else if (point.x < maxx) {\n            return point.y < miny ? 2 : 0\n        }\n        return point.y < miny ? 6 : (point.y < maxy ? 1 : 5)\n    }\n\n    private setSeaLand(it: cc.Node, type: string, point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {\n        const dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy)\n        it.Component(cc.Sprite).spriteFrame = this.getLandIcon(`${type}_${Math.min(dir, 4)}`)\n    }\n\n    // 海浪\n    private setSeawaveLand(position: cc.Vec2, point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {\n        const dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy), no = Math.floor(dir / 4) + 1, angle = dir % 4\n        const it = this.seawaveAnimNodePool.showNode(no, position, true)\n        it.angle = angle * -90\n    }\n\n    private getLandIcon(icon: string) {\n        return resHelper.getLandIcon(icon)\n    }\n\n    private updatePlayerNickname(it: cc.Node, data: PlayerInfo) {\n        const nameLbl = it.Child('name/val', cc.Label)\n        nameLbl.string = ut.nameFormator(data?.nickname ?? '???', 7)\n        const titleLbl = it.Child('name/title', cc.Label)\n        if (titleLbl.setActive(!!data?.title)) {\n            const json = assetsMgr.getJsonData('title', data.title)\n            titleLbl.Color(json?.color || '#333333').setLocaleKey('titleText.' + json?.id)\n            nameLbl.node.y = -10\n        } else {\n            nameLbl.node.y = 0\n        }\n    }\n\n    private updateAncientTextInfo(it: cc.Node, data: AncientObj) {\n        it.Child('name').setLocaleKey('ui.ancient_name_text', data.name, assetsMgr.lang('ui.short_lv', data.lv || 1))\n        if (it.Child('time').active = data.state === 1 && !data.pauseState) {\n            it.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001)\n        }\n    }\n\n    // 刷新显示文本节点\n    private updateHideTextByIndex(index: number = -1) {\n        this.textNode_.children.forEach(m => m.active = !!m.Data && m.Data.index !== index)\n        this.ancientTextNode_.children.forEach(m => m.active = !!m.Data && m.Data.index !== index)\n        this.btinfoNode.children.forEach(m => m.Data && (m.Child('time').active = m.Data.index !== index))\n    }\n\n    // 刷新图标层\n    private updateIconNode() {\n        const offset1: cc.Vec2 = cc.v2(-22, -22)\n        const offset2: cc.Vec2 = cc.v2(22, -22)\n        const cells: any[] = [], armyDistMap = this.player.getArmyDistMap()\n        for (let key in this.tempShowCellMap) {\n            const cell: MapCellObj = this.tempShowCellMap[key]\n            if (cell.isCanShowAvoidWar()) {\n                cells.push({ position: cell.getRightPosition(), offset: offset2, icon: 'avoidwar_icon_1' })\n            }\n            if (armyDistMap[key]) {\n                cells.push({ position: cell.position, offset: offset1, icon: 'army_min_icon' })\n            }\n        }\n        this.iconNode.Items(cells, (it, data) => {\n            it.setPosition(this._temp_vec2_3.set(data.offset).addSelf(data.position))\n            it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon(data.icon)\n        })\n    }\n\n    // 显示选择地块\n    private showSelectCell(cell: MapCellObj) {\n        if (!cell || cell.landType == LandType.SEA || cell.landType == LandType.BEACH) {\n            return\n        } else if (cell.actIndex !== cell.index) {\n            cell = this.model.getMapCellByIndex(cell.actIndex)\n        }\n        const pos = this.selectCellNode_.Data = cell.actPosition\n        this.selectCellNode_.Component(SelectCellCmpt).open(pos, cell.getSize())\n        this.cellInfoCmpt.open(pos, cell)\n        // 隐藏文本节点\n        this.updateHideTextByIndex(cell.actIndex)\n    }\n\n    // 隐藏\n    private hideSelectCell(play: boolean = true) {\n        if (this.selectCellNode_.Data) {\n            this.selectCellNode_.Component(SelectCellCmpt).close()\n            this.cellInfoCmpt.close(play)\n            this.updateHideTextByIndex()\n        }\n    }\n\n    // 初始化行军\n    private initMarch() {\n        this.cleanMarch()\n        const list = this.model.getAllMarchs().filter(m => m.isCanShowMarch())\n        this.marchLineNode_.Items(list, (it, data) => {\n            const march = this.marchs.add(it.Component(MarchCmpt).init(data, this.marchRoleNode_, this.key))\n            march.isCheckLineOffset = false\n        })\n        this.marchs.forEach(m => !m.isCheckLineOffset && this.checkMarchLineOffset(m.getData()))\n    }\n\n    private cleanMarch() {\n        while (this.marchs.length > 0) {\n            this.marchs.pop().clean()\n        }\n        this.marchs = []\n        // resHelper.cleanNodeChildren(this.marchLineNode_) //这个注释了 不知道什么原因会出现行军线被消耗的情况\n        this.marchRoleNode_.removeAllChildren()\n    }\n\n    // 检测行军线偏移\n    private checkMarchLineOffset(data: BaseMarchObj) {\n        const others: MarchCmpt[] = []\n        this.marchs.forEach(m => {\n            const d = m.getData()\n            if (data.checkOtherMarchLine(d)) {\n                m.angleOffset = data.startIndex === d.startIndex ? 0 : -180\n                m.isCheckLineOffset = true\n                others.push(m)\n            }\n        })\n        const len = others.length\n        others.forEach((m, i) => m.updateLineOffset(i, len))\n    }\n\n    // 攻击地块\n    private async occupyCell(index: number) {\n        if (this.reqSelectArmysing) {\n            return\n        }\n        this.reqSelectArmysing = true\n        const { err, list, canGotoCount } = await this.player.getSelectArmys(index, 2, 0)\n        this.reqSelectArmysing = false\n        if (!this.isActive) {\n            return\n        } else if (err === ecode.NOT_IN_OCCUPY_TIME) {\n            this.hideSelectCell(false)\n            return viewHelper.showMessageBox('ui.not_in_occupy_time_tip')\n        } else if (err === ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击\n            this.hideSelectCell(false)\n            return viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip')\n        } else if (err) {\n            return viewHelper.showAlert(err)\n        } else if (list.length === 0) {\n            return viewHelper.showAlert('toast.not_idle_army')\n        }\n        viewHelper.showPnl('main/SelectArmy', 'occupy', index, list, canGotoCount, (armys: ArmyShortInfo[], isSameSpeed: boolean, autoBackType: number) => {\n            if (this.isActive) {\n                this.hideSelectCell(false)\n                this.model.occupyCell(armys, index, autoBackType, isSameSpeed).then(err => err && viewHelper.showAlert(err))\n            }\n        })\n    }\n\n    // 移动过去屯田\n    private async cellTonden(index: number) {\n        if (this.reqSelectArmysing) {\n            return\n        }\n        this.reqSelectArmysing = true\n        const { err, list, canGotoCount } = await this.player.getSelectArmys(index, 3, 0)\n        this.reqSelectArmysing = false\n        if (!this.isActive) {\n            return\n        } else if (err === ecode.BATTLEING) {\n            return viewHelper.showAlert('toast.battleing_not_tonden')\n        } else if (err) {\n            return viewHelper.showAlert(err)\n        } else if (list.length === 0) {\n            return viewHelper.showAlert('toast.not_idle_army')\n        }\n        viewHelper.showPnl('main/SelectTondenArmy', index, list, canGotoCount, (army: ArmyShortInfo) => {\n            if (this.isActive) {\n                this.hideSelectCell(false)\n                this.model.cellTonden(army, index).then(err => err && viewHelper.showAlert(err))\n            }\n        })\n    }\n\n    // 取消屯田\n    private async cancelTonden(index: number) {\n        const err = await this.model.cancelTonden(index)\n        if (err) {\n            return viewHelper.showAlert(err)\n        } else if (this.isActive) {\n            this.hideSelectCell(false)\n        }\n    }\n\n    // 移动到地块\n    private async moveToCell(index: number) {\n        if (this.reqSelectArmysing) {\n            return\n        }\n        this.reqSelectArmysing = true\n        const { err, list, canGotoCount } = await this.player.getSelectArmys(index, 1)\n        this.reqSelectArmysing = false\n        if (!this.isActive) {\n            return\n        } else if (err) {\n            return viewHelper.showAlert(err)\n        } else if (list.length === 0) {\n            return viewHelper.showAlert('toast.not_idle_army')\n        }\n        viewHelper.showPnl('main/SelectArmy', 'move', index, list, canGotoCount, (armys: ArmyShortInfo[], isSameSpeed: boolean) => {\n            if (this.isActive) {\n                this.hideSelectCell(false)\n                this.model.moveCellArmy(armys, index, isSameSpeed).then(err => err && viewHelper.showAlert(err))\n            }\n        })\n    }\n\n    // 播放新的地块效果\n    private playNewCellEffect() {\n        this.model.getNotPlayNewCells().forEach(index => {\n            const cell = this.tempShowCellMap[index]\n            if (cell) {\n                const json = cell.getResJson() || {}, keys = CELL_RES_FIELDS.filter(m => !!json[m])\n                const pos = cell.actPosition, isMore = keys.length > 1\n                keys.forEach((key, i) => animHelper.playFlutterCellRes(key, json[key], 0.3 + i * 0.2, isMore, this.topLayerNode_, pos, this.key))\n                animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key)\n                // 隐藏行军线\n                this.marchs.forEach(march => march.isHasIndex(index) && march.hide(1.2))\n            }\n        })\n    }\n\n    // 播放屯田结束的地块效果\n    private playCellTondenEffect() {\n        this.model.getNotPlayCellTondens().forEach(data => {\n            const cell = this.tempShowCellMap[data.index]\n            if (cell) {\n                const pos = cell.actPosition\n                const obj = {}\n                data.treasureIds?.forEach(id => {\n                    let idObj = obj[id]\n                    if (!idObj) {\n                        const json = assetsMgr.getJsonData('treasure', id)\n                        idObj = obj[id] = { count: 0, icon: 'treasure_' + (json?.lv || 1) + '_0' }\n                    }\n                    idObj.count += 1\n                })\n                for (let key in obj) {\n                    const data = obj[key]\n                    animHelper.playFlutterTreasure(data.icon, data.count, this.topLayerNode_, pos, this.key)\n                }\n                animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key)\n            }\n        })\n    }\n\n    // 播放地图表情\n    private playCellEmoji() {\n        const now = Date.now()\n        gameHpr.ground.getCellEmojis().forEach(m => {\n            const cell = this.tempShowCellMap[m.index], item = this.cellEmojiItemMap[Math.floor(m.emoji / 1000)]\n            if (cell && item) {\n                let node: cc.Node = this.cellEmojiMap[m.index]\n                if (node?.isValid) {\n                    node.Child('root').children.forEach(it => nodePoolMgr.put(it))\n                    node.destroy()\n                }\n                const startTime = Math.max(0, (now - m.getTime) * 0.001)\n                node = this.cellEmojiMap[m.index] = cc.instantiate2(item, this.cellEmojiNode_)\n                node.Data = m.index\n                node.setPosition(cell.actPosition)\n                node.zIndex = cell.point.y\n                animHelper.playCellEmoji(node, m.emoji, m.uid, startTime, this.key).then(n => {\n                    if (this.isValid && n?.isValid) {\n                        delete this.cellEmojiMap[n.Data]\n                        n.Child('root').children.forEach(it => nodePoolMgr.put(it))\n                        n.destroy()\n                    }\n                })\n            }\n        })\n    }\n\n    private testPlayNewCell(x: number, y: number, keys: string[], delay: number) {\n        // for (let i = 0; i < 5; i++) {\n        //     const position = mapHelper.getPixelByPoint(cc.v2(x + i, y)).clone()\n        //     animHelper.playFlutterCellRes('stone', 30, this.topLayerNode_, position, this.key)\n        //     animHelper.playNewCellEffect(this.cellEffectNode_, position, this.key)\n        // }\n        const pos = mapHelper.getPixelByPoint(cc.v2(x, y)).clone(), isMore = keys.length > 1\n        keys.forEach((key, i) => animHelper.playFlutterCellRes(key, 1, 0.3 + i * delay, isMore, this.topLayerNode_, pos, this.key))\n        animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key)\n    }\n\n    // 检测季节\n    private checkSeason() {\n        let oldType = this.user.getLocalPreferenceDataBySid(PreferenceKey.LAST_PLAY_SEASON_TYPE) ?? -1\n        this.seasonType = cc.misc.clampf(oldType, 0, 3)\n        if (resHelper.checkLandSkin(this.seasonType)) {\n            this.updateMap(this.model.getCentre()) //刷新地图显示\n        } else {\n            resHelper.initLandSkin(this.seasonType).then(() => this.isValid && this.updateMap(this.model.getCentre()))\n        }\n        // 弹界面\n        const curType = this.model.getSeason().type\n        if (oldType !== curType) {\n            popupPnlHelper.add({ key: 'main/SeasonSwitch' })\n        }\n    }\n\n    // 播放切换季节\n    private async playChangeSeason(type: number) {\n        this.seasonType = type\n        this.user.setLocalPreferenceDataBySid(PreferenceKey.LAST_PLAY_SEASON_TYPE, type)\n        this.hideSelectCell(false)\n        await resHelper.initLandSkin(type)\n        this.updateSeasonSeceneEffect()\n        this.updateMap(this.centre) //刷新地图显示\n        resHelper.cleanLandSkin()\n    }\n\n    update(dt: number) {\n        //\n        this.seawaveAnimNodePool?.update(dt)\n        // 检测是否需要填充地图\n        this.checkUpdateMap()\n        // 检测是否在相机范围\n        this.checkInCameraRange()\n    }\n\n    private checkUpdateMap() {\n        const point = mapHelper.getPointByPixel(cameraCtrl.getCentrePosition(), this._temp_vec2_1)\n        let size = Math.max(Math.abs(point.x - this.centre.x), Math.abs(point.y - this.centre.y))\n        if (size >= MAP_EXTRA_SIZE / 2 || this.preCameraZoomRatio !== cameraCtrl.zoomRatio) {\n            this.updateMap(point)\n            this.checkInCameraMarchLine()\n        }\n    }\n\n    // 检测只会在在相机范围内的行军线\n    private checkInCameraMarchLine() {\n        const uidMap = {}\n        this.marchs.forEach(m => {\n            m.checkUpdateInCamera()\n            uidMap[m.uid] = true\n        })\n        // 兼容检测是否有多余的行军角色\n        for (let i = this.marchRoleNode_.childrenCount - 1; i >= 0; i--) {\n            const node = this.marchRoleNode_.children[i]\n            if (!uidMap[node.Data?.uid]) {\n                node.destroy()\n            }\n        }\n    }\n\n    private checkInCameraRange() {\n        const position = cameraCtrl.getPosition()\n        if (this.preCameraPosition.equals(position)) {\n            return\n        }\n        this.preCameraPosition.set(position)\n        // 选择地块框\n        if (this.cellInfoCmpt?.checkNotInScreenRange()) {\n            this.hideSelectCell(false)\n        }\n    }\n}\n"]}