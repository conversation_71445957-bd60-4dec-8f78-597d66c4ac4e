"use strict";
cc._RF.push(module, '2721aJDkD1J0bjlAQDgFp+H', 'BaseBuildCmpt');
// app/script/view/area/BaseBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var MapHelper_1 = require("../../common/helper/MapHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 建筑
var BaseBuildCmpt = /** @class */ (function (_super) {
    __extends(BaseBuildCmpt, _super);
    function BaseBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.data = null;
        _this.owner = '';
        _this.body = null;
        _this.origin = cc.v2(); //起点
        _this.originY = 0; //实际地图的七点 像素
        _this.tempBodyPosition = cc.v2();
        _this.tempIndex = 0;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_position = cc.v2();
        return _this;
    }
    BaseBuildCmpt.prototype.init = function (data, origin, originY, owner) {
        this.data = data;
        this.origin.set(origin);
        this.originY = originY;
        this.owner = owner;
        this.body = this.FindChild('body');
        this.body.getPosition(this.tempBodyPosition);
        return this;
    };
    BaseBuildCmpt.prototype.clean = function () {
    };
    Object.defineProperty(BaseBuildCmpt.prototype, "id", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseBuildCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.uid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseBuildCmpt.prototype, "point", {
        get: function () { return this.data.point; },
        enumerable: false,
        configurable: true
    });
    BaseBuildCmpt.prototype.getBody = function () { return this.body; };
    BaseBuildCmpt.prototype.getTempPosition = function () { return this.getPosition(this._temp_position); };
    BaseBuildCmpt.prototype.getBodyOffsetTopPosition = function (y) {
        if (y === void 0) { y = 0; }
        var pos = this.getTempPosition();
        pos.y += y + this.getBuildY();
        return pos;
    };
    BaseBuildCmpt.prototype.getBuildY = function () {
        return (this.data.size.y - 1) * Constant_1.TILE_SIZE;
    };
    // 同步位置
    BaseBuildCmpt.prototype.syncPoint = function () {
        if (this.data) {
            this.node.setPosition(this.getActPixelByPoint(this.data.point));
        }
    };
    // 同步zindex
    BaseBuildCmpt.prototype.syncZindex = function () {
        if (this.data) {
            var y = this.node.y - this.originY;
            var index = (Constant_1.AREA_MAX_ZINDEX - y) * 10;
            if (this.data.id === Constant_1.BUILD_DRILLGROUND_NID) {
                index += 1;
            }
            this.tempIndex = this.node.zIndex = index;
        }
    };
    // 根据像素点获取网格点
    BaseBuildCmpt.prototype.getActPointByPixel = function (pixel) {
        return MapHelper_1.mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin);
    };
    // 根据网格点获取像素点
    BaseBuildCmpt.prototype.getActPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1);
    };
    // 重新同步
    BaseBuildCmpt.prototype.resync = function (data, owner) { return this; };
    // 刷新等级
    BaseBuildCmpt.prototype.updateLv = function (lv) { };
    // 设置是否可以点击
    BaseBuildCmpt.prototype.setCanClick = function (val) { };
    // 设置可以点击选择
    BaseBuildCmpt.prototype.setCanClickSelect = function (val) { };
    // 刷新升级动画
    BaseBuildCmpt.prototype.updateUpLvAnim = function () { };
    // 刷新训练士兵
    BaseBuildCmpt.prototype.updateDrillPawn = function () { };
    // 刷新打造装备
    BaseBuildCmpt.prototype.updateForgeEquip = function () { };
    BaseBuildCmpt = __decorate([
        ccclass
    ], BaseBuildCmpt);
    return BaseBuildCmpt;
}(cc.Component));
exports.default = BaseBuildCmpt;

cc._RF.pop();