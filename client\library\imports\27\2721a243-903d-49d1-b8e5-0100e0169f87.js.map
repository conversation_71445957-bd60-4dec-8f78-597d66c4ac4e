{"version": 3, "sources": ["assets\\app\\script\\view\\area\\BaseBuildCmpt.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAmG;AACnG,2DAA0D;AAGpD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,KAAK;AAEL;IAA2C,iCAAY;IAAvD;QAAA,qEAuFC;QArFU,UAAI,GAAa,IAAI,CAAA;QACrB,WAAK,GAAW,EAAE,CAAA;QAEf,UAAI,GAAY,IAAI,CAAA;QACpB,YAAM,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,IAAI;QAC9B,aAAO,GAAW,CAAC,CAAA,CAAC,YAAY;QAChC,sBAAgB,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QACtC,eAAS,GAAG,CAAC,CAAA;QAEZ,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,oBAAc,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;;IA0E7C,CAAC;IAxEU,4BAAI,GAAX,UAAY,IAAc,EAAE,MAAe,EAAE,OAAe,EAAE,KAAa;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAClC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAC5C,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,6BAAK,GAAZ;IACA,CAAC;IAED,sBAAW,6BAAE;aAAb,sBAAkB,aAAO,IAAI,CAAC,IAAI,0CAAE,EAAE,CAAA,CAAC,CAAC;;;OAAA;IACxC,sBAAW,8BAAG;aAAd,sBAAmB,aAAO,IAAI,CAAC,IAAI,0CAAE,GAAG,CAAA,CAAC,CAAC;;;OAAA;IAC1C,sBAAW,gCAAK;aAAhB,cAAqB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA,CAAC,CAAC;;;OAAA;IACtC,+BAAO,GAAd,cAAmB,OAAO,IAAI,CAAC,IAAI,CAAA,CAAC,CAAC;IAC9B,uCAAe,GAAtB,cAA2B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA,CAAC,CAAC;IAElE,gDAAwB,GAA/B,UAAgC,CAAa;QAAb,kBAAA,EAAA,KAAa;QACzC,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;QAClC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QAC7B,OAAO,GAAG,CAAA;IACd,CAAC;IAEM,iCAAS,GAAhB;QACI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAS,CAAA;IAC7C,CAAC;IAED,OAAO;IACA,iCAAS,GAAhB;QACI,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;SAClE;IACL,CAAC;IAED,WAAW;IACJ,kCAAU,GAAjB;QACI,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAA;YACpC,IAAI,KAAK,GAAG,CAAC,0BAAe,GAAG,CAAC,CAAC,GAAG,EAAE,CAAA;YACtC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,gCAAqB,EAAE;gBACxC,KAAK,IAAI,CAAC,CAAA;aACb;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;SAC5C;IACL,CAAC;IAED,aAAa;IACN,0CAAkB,GAAzB,UAA0B,KAAc;QACpC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACnF,CAAC;IAED,aAAa;IACN,0CAAkB,GAAzB,UAA0B,KAAc;QACpC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;IAClG,CAAC;IAED,OAAO;IACA,8BAAM,GAAb,UAAc,IAAS,EAAE,KAAa,IAAI,OAAO,IAAI,CAAA,CAAC,CAAC;IACvD,OAAO;IACA,gCAAQ,GAAf,UAAgB,EAAU,IAAI,CAAC;IAC/B,WAAW;IACJ,mCAAW,GAAlB,UAAmB,GAAY,IAAI,CAAC;IACpC,WAAW;IACJ,yCAAiB,GAAxB,UAAyB,GAAY,IAAI,CAAC;IAC1C,SAAS;IACF,sCAAc,GAArB,cAA0B,CAAC;IAC3B,SAAS;IACF,uCAAe,GAAtB,cAA2B,CAAC;IAC5B,SAAS;IACF,wCAAgB,GAAvB,cAA4B,CAAC;IAtFZ,aAAa;QADjC,OAAO;OACa,aAAa,CAuFjC;IAAD,oBAAC;CAvFD,AAuFC,CAvF0C,EAAE,CAAC,SAAS,GAuFtD;kBAvFoB,aAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { AREA_MAX_ZINDEX, BUILD_DRILLGROUND_NID, TILE_SIZE } from \"../../common/constant/Constant\";\r\nimport { mapHelper } from \"../../common/helper/MapHelper\";\r\nimport BuildObj from \"../../model/area/BuildObj\";\r\n\r\nconst { ccclass, property } = cc._decorator;\r\n\r\n// 建筑\r\n@ccclass\r\nexport default class BaseBuildCmpt extends cc.Component {\r\n\r\n    public data: BuildObj = null\r\n    public owner: string = ''\r\n\r\n    protected body: cc.Node = null\r\n    protected origin: cc.Vec2 = cc.v2() //起点\r\n    protected originY: number = 0 //实际地图的七点 像素\r\n    protected tempBodyPosition: cc.Vec2 = cc.v2()\r\n    public tempIndex = 0\r\n\r\n    private _temp_vec2_1: cc.Vec2 = cc.v2()\r\n    private _temp_vec2_2: cc.Vec2 = cc.v2()\r\n    private _temp_position: cc.Vec2 = cc.v2()\r\n\r\n    public init(data: BuildObj, origin: cc.Vec2, originY: number, owner: string) {\r\n        this.data = data\r\n        this.origin.set(origin)\r\n        this.originY = originY\r\n        this.owner = owner\r\n        this.body = this.FindChild('body')\r\n        this.body.getPosition(this.tempBodyPosition)\r\n        return this\r\n    }\r\n\r\n    public clean() {\r\n    }\r\n\r\n    public get id() { return this.data?.id }\r\n    public get uid() { return this.data?.uid }\r\n    public get point() { return this.data.point }\r\n    public getBody() { return this.body }\r\n    public getTempPosition() { return this.getPosition(this._temp_position) }\r\n\r\n    public getBodyOffsetTopPosition(y: number = 0) {\r\n        const pos = this.getTempPosition()\r\n        pos.y += y + this.getBuildY()\r\n        return pos\r\n    }\r\n\r\n    public getBuildY() {\r\n        return (this.data.size.y - 1) * TILE_SIZE\r\n    }\r\n\r\n    // 同步位置\r\n    public syncPoint() {\r\n        if (this.data) {\r\n            this.node.setPosition(this.getActPixelByPoint(this.data.point))\r\n        }\r\n    }\r\n\r\n    // 同步zindex\r\n    public syncZindex() {\r\n        if (this.data) {\r\n            const y = this.node.y - this.originY\r\n            let index = (AREA_MAX_ZINDEX - y) * 10\r\n            if (this.data.id === BUILD_DRILLGROUND_NID) {\r\n                index += 1\r\n            }\r\n            this.tempIndex = this.node.zIndex = index\r\n        }\r\n    }\r\n\r\n    // 根据像素点获取网格点\r\n    public getActPointByPixel(pixel: cc.Vec2) {\r\n        return mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin)\r\n    }\r\n\r\n    // 根据网格点获取像素点\r\n    public getActPixelByPoint(point: cc.Vec2) {\r\n        return mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1)\r\n    }\r\n\r\n    // 重新同步\r\n    public resync(data: any, owner: string) { return this }\r\n    // 刷新等级\r\n    public updateLv(lv: number) { }\r\n    // 设置是否可以点击\r\n    public setCanClick(val: boolean) { }\r\n    // 设置可以点击选择\r\n    public setCanClickSelect(val: boolean) { }\r\n    // 刷新升级动画\r\n    public updateUpLvAnim() { }\r\n    // 刷新训练士兵\r\n    public updateDrillPawn() { }\r\n    // 刷新打造装备\r\n    public updateForgeEquip() { }\r\n}"]}