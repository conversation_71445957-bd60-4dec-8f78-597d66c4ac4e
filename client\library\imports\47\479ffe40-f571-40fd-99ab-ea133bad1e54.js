"use strict";
cc._RF.push(module, '479ff5A9XFA/Zmr6hM7rR5U', 'Constant');
// app/script/common/constant/Constant.ts

"use strict";
/////////////// 所有常量（全大写单词间用下划线隔开）///////////////
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_BUILD_SIZE = exports.DEFAULT_AREA_SIZE = exports.DEFAULT_CITY_SIZE = exports.CREATE_ALLI_MAX_LV = exports.ARMY_PAWN_MAX_COUNT = exports.MODIFY_NICKNAME_GOLD = exports.IN_DONE_FORGE_GOLD = exports.IN_DONE_BT_GOLD = exports.DEFAULT_BT_QUEUE_COUNT = exports.INIT_RES_OUTPUT = exports.INIT_RES_COUNT = exports.INIT_RES_CAP = exports.AX_CAVALRY_ID = exports.PAWN_CROSSBOW_ID = exports.BUILD_TOWER_NID = exports.BUILD_FORT_NID = exports.BUILD_HOSPITAL_NID = exports.BUILD_HEROHALL_NID = exports.BUILD_ALLI_BAZAAR_NID = exports.BUILD_PLANT_NID = exports.BUILD_DRILLGROUND_NID = exports.BUILD_SMITHY_NID = exports.BUILD_BAZAAR_NID = exports.BUILD_EMBASSY_NID = exports.BUILD_BARRACKS_NID = exports.BUILD_WAREHOUSE_NID = exports.BUILD_GRANARY_NID = exports.BUILD_MAIN_NID = exports.BUILD_WALL_NID = exports.BUILD_QUARRY_ID = exports.BUILD_TIMBER_ID = exports.BUILD_FARM_ID = exports.CITY_LUOYANG_ID = exports.CITY_YANJING_ID = exports.CITY_JINLING_ID = exports.CITY_CHANGAN_ID = exports.ANCIENT_WALL_ID = exports.CITY_FORT_NID = exports.BUILD_FLAG_NID = exports.CITY_MAIN_NID = exports.DELAY_CLOSE_PNL_TIME = exports.LONG_PRESS_TIME = exports.MAX_ZINDEX = exports.AREA_MAX_ZINDEX = exports.BUILD_DRAG_OFFSETY = exports.MAP_EXTRA_SIZE = exports.TILE_SIZE_HALF = exports.TILE_SIZE = exports.MAP_SHOW_OFFSET = exports.CLICK_SPACE = void 0;
exports.REPLACEMENT_MIN_RES_COUNT = exports.REPLACEMENT_SERVICE_CHARGE = exports.CHAT_BARRAGE_COLOR = exports.NEED_MUTUAL_BUFF = exports.NEED_SHOW_BUFF = exports.BUFF_NODE_ZINDEX = exports.BATTLE_EFFECT_TYPE = exports.SHIELD_BUFF = exports.BUFF_SHOW_TYPE_TRAN = exports.ONE_USER_POPULARITY_CHANGE_INTERVAL = exports.CREATE_ALLI_COND = exports.CREATE_ALLI_COST = exports.CAN_EXIT_ALLI_TIME = exports.RESET_STUDY_SLOT_GOLD = exports.PAWN_SLOT_CONF = exports.EQUIP_SMELT_NEED_LV = exports.EQUIP_SLOT_EXCLUSIVE_LV = exports.EQUIP_SLOT_CONF = exports.POLICY_SLOT_CONF = exports.ADD_OUTPUT_TIME = exports.ADD_OUTPUT_RATIO = exports.ADD_OUTPUT_GOLD = exports.FREE_HEAD_ICONS = exports.FIXATION_MENU_MAX_COUNT = exports.FIXATION_MENU_CONFIG = exports.PLAYBACK_MULS = exports.ARMY_RECORD_DESC_CONF = exports.MAIL_STATE_COLOR = exports.ARMY_STATE_COLOR = exports.MARCH_ARMY_TIME_COLOR = exports.MARCH_ARMY_NAME_COLOR = exports.BUILD_EFFECT_TYPE_CONF = exports.CTYPE_NAME = exports.CTYPE_ICON = exports.CTYPE_ICON_URL = exports.RES_FIELDS_CTYPE = exports.PAWN_BUBBLE_OFFSETY = exports.CELL_RES_FIELDS = exports.SELECT_CELL_INFO_BOX = exports.RIVER_LINE_CONF = exports.BORDER_LINE_CONF = exports.DECORATION_MUD_CONF = exports.LAND_DI_CONF = exports.BATTLE_MAX_TIME = exports.TRANSIT_TIME = exports.MAIN_CITY_MARCH_SPEED = exports.UP_MARCH_SPEED_MUL = exports.DEFAULT_MAX_ADD_PAWN_TIMES = exports.DEFAULT_MAX_ARMY_COUNT = exports.BOSS_BUILD_SIZE = void 0;
exports.LOBBY_MODE_BUTTOM_NAME = exports.ALLI_APPLY_MAX_COUNT = exports.MAX_MAP_MARK_COUNT = exports.RES_MAP = exports.FIRE_PAWN_ID = exports.SPEAR_PAWN_ID = exports.DEFAULT_PET_ID = exports.SUMMON_LV = exports.HERO_SLOT_LV_COND = exports.HERO_REVIVES_TIME = exports.HERO_OPT_GIFT = exports.BUY_OPT_HERO_COST = exports.RESTORE_PORTRAYAL_GOLD_COST = exports.RESTORE_PORTRAYAL_WAR_TOKEN_COST = exports.PORTRAYAL_COMP_NEED_COUNT = exports.POINTSETS_ONE_GOLD_COST = exports.POINTSETS_ONE_COST = exports.NEXT_APPLY_CD = exports.SERVER_APPLY_CANCEL_CD = exports.SEND_TRUMPET_ACC_COST = exports.SEND_TRUMPET_COST = exports.CHAT_BANNED_REST_MAX_TIME = exports.CHAT_REST_MAX_TIME = exports.CHAT_TOLERATE_MAX_COUNT = exports.CHAT_SEND_INTERVAL = exports.CHAT_MAX_COUNT = exports.COLOR_NORMAL = exports.ANCIENT_SUP_TIME = exports.ANCIENT_SUP_COST_MUL = exports.SEASON_DURATION_TIME = exports.CAN_MIN_MARCH_SPEED = exports.OPEN_ALL_TREASURE_MIN_LAND_COUNT = exports.BATTLE_FORECAST_FREE_COUNT = exports.BATTLE_FORECAST_COST = exports.FRIENDS_MIN_LAND_COUNT = exports.SHOW_TIME_MAX_INTERVAL = exports.NOLIMIT_PCHAT_MAX_LAND = exports.OCCUPY_PLAYER_CELL_MIN_DIS = exports.REMOVE_PCHAT_TIME = exports.LAND_SCORE_CONF = exports.CONCURRENT_GAME_LIMIT = exports.NOT_OCCUPY_BY_MAX_LAND_COUNT = exports.NOT_OCCUPY_BY_SERVER_RUNTIME = exports.ALLI_JOB_COUNT = exports.ALLI_JOB_DESC = exports.LOGOUT_MAX_DAY = exports.LANGUAGE_TEXT_LIST = exports.UP_RECRUIT_PAWN_MUL = exports.RES_TRANSIT_CAP = exports.REPLACEMENT_TODAY_COUNT_MAP = void 0;
exports.DIFFICULTY_BG_COLOR = exports.AREA_DI_COLOR_CONF = exports.MAP_MASK_ITEM_COLOR = exports.CAMERA_BG_COLOR = exports.FACTORY_SLOT_CONF = exports.PAWN_COST_LV_LIST = exports.ALLI_LEADER_VOTE_MAX_COUNT = exports.STUDY_TO_BOOKTYPE = exports.PORTRAYAL_CHOSENONE_ODDS = exports.GO_HOSPITAL_CHANCE = exports.HOSPITAL_PAWN_LIMIT = exports.TONDEN_STAMINA_MUL = exports.TONDEN_GET_RES_RATIO = exports.TODAY_TONDEN_MAX_COUNT = exports.NOTICE_PERMISSION_CD = exports.PRIZE_QUESTION_TIME = exports.PRIZE_QUESTION_ID = exports.RECHARGE_BATTLE_PASS_EXP = exports.RECHARGE_BATTLE_PASS = exports.BATTLE_FIRE_COLOR = exports.BATTLE_HPBAR_COLOR = exports.RANK_SHOP_WAR_TOKEN_CONFIG = exports.SKEW_SIZE_HALF = exports.SKEW_SIZE = exports.SKEW_ANGLE = void 0;
var Enums_1 = require("./Enums");
// 点击间隔
var CLICK_SPACE = 10;
exports.CLICK_SPACE = CLICK_SPACE;
// 一格的大小
var TILE_SIZE = 80;
exports.TILE_SIZE = TILE_SIZE;
// 地图边界额外宽度
var MAP_EXTRA_SIZE = 2;
exports.MAP_EXTRA_SIZE = MAP_EXTRA_SIZE;
// 地图显示偏移
var MAP_SHOW_OFFSET = cc.v2(TILE_SIZE * 8, TILE_SIZE * 8);
exports.MAP_SHOW_OFFSET = MAP_SHOW_OFFSET;
//
var TILE_SIZE_HALF = cc.v2(TILE_SIZE * 0.5, TILE_SIZE * 0.5);
exports.TILE_SIZE_HALF = TILE_SIZE_HALF;
// 设施拖拽时候的高
var BUILD_DRAG_OFFSETY = TILE_SIZE_HALF.y - 24;
exports.BUILD_DRAG_OFFSETY = BUILD_DRAG_OFFSETY;
// 区域最高y坐标
var AREA_MAX_ZINDEX = 21 * TILE_SIZE;
exports.AREA_MAX_ZINDEX = AREA_MAX_ZINDEX;
// 层级最大值
var MAX_ZINDEX = 10000;
exports.MAX_ZINDEX = MAX_ZINDEX;
// 长按时间
var LONG_PRESS_TIME = 0.4;
exports.LONG_PRESS_TIME = LONG_PRESS_TIME;
// 延迟关闭pnl时间
var DELAY_CLOSE_PNL_TIME = 0.4;
exports.DELAY_CLOSE_PNL_TIME = DELAY_CLOSE_PNL_TIME;
// 主城id
var CITY_MAIN_NID = 1001;
exports.CITY_MAIN_NID = CITY_MAIN_NID;
// 要塞id
var CITY_FORT_NID = 2102;
exports.CITY_FORT_NID = CITY_FORT_NID;
// 4个遗迹
var ANCIENT_WALL_ID = 3000; //城墙
exports.ANCIENT_WALL_ID = ANCIENT_WALL_ID;
var CITY_CHANGAN_ID = 3001; //长安
exports.CITY_CHANGAN_ID = CITY_CHANGAN_ID;
var CITY_JINLING_ID = 3002; //金陵
exports.CITY_JINLING_ID = CITY_JINLING_ID;
var CITY_YANJING_ID = 3003; //燕京
exports.CITY_YANJING_ID = CITY_YANJING_ID;
var CITY_LUOYANG_ID = 3004; //洛阳
exports.CITY_LUOYANG_ID = CITY_LUOYANG_ID;
// 农场
var BUILD_FARM_ID = 2201;
exports.BUILD_FARM_ID = BUILD_FARM_ID;
// 伐木场
var BUILD_TIMBER_ID = 2202;
exports.BUILD_TIMBER_ID = BUILD_TIMBER_ID;
// 采石场
var BUILD_QUARRY_ID = 2203;
exports.BUILD_QUARRY_ID = BUILD_QUARRY_ID;
// 城墙建筑id
var BUILD_WALL_NID = 2000;
exports.BUILD_WALL_NID = BUILD_WALL_NID;
// 主城id
var BUILD_MAIN_NID = 2001;
exports.BUILD_MAIN_NID = BUILD_MAIN_NID;
// 仓库建筑id
var BUILD_GRANARY_NID = 2002;
exports.BUILD_GRANARY_NID = BUILD_GRANARY_NID;
// 粮仓建筑id
var BUILD_WAREHOUSE_NID = 2003;
exports.BUILD_WAREHOUSE_NID = BUILD_WAREHOUSE_NID;
// 兵营建筑id
var BUILD_BARRACKS_NID = 2004;
exports.BUILD_BARRACKS_NID = BUILD_BARRACKS_NID;
// 大使馆建筑id
var BUILD_EMBASSY_NID = 2005;
exports.BUILD_EMBASSY_NID = BUILD_EMBASSY_NID;
// 市场建筑id
var BUILD_BAZAAR_NID = 2006;
exports.BUILD_BAZAAR_NID = BUILD_BAZAAR_NID;
// 铁匠铺所建筑id
var BUILD_SMITHY_NID = 2008;
exports.BUILD_SMITHY_NID = BUILD_SMITHY_NID;
// 校场建筑id
var BUILD_DRILLGROUND_NID = 2011;
exports.BUILD_DRILLGROUND_NID = BUILD_DRILLGROUND_NID;
// 工厂建筑id
var BUILD_PLANT_NID = 2010;
exports.BUILD_PLANT_NID = BUILD_PLANT_NID;
// 联盟市场建筑id
var BUILD_ALLI_BAZAAR_NID = 2014;
exports.BUILD_ALLI_BAZAAR_NID = BUILD_ALLI_BAZAAR_NID;
// 英雄殿建筑id
var BUILD_HEROHALL_NID = 2015;
exports.BUILD_HEROHALL_NID = BUILD_HEROHALL_NID;
// 医馆建筑id
var BUILD_HOSPITAL_NID = 2016;
exports.BUILD_HOSPITAL_NID = BUILD_HOSPITAL_NID;
// 旗子id
var BUILD_FLAG_NID = 2101;
exports.BUILD_FLAG_NID = BUILD_FLAG_NID;
// 要塞建筑id
var BUILD_FORT_NID = 2102;
exports.BUILD_FORT_NID = BUILD_FORT_NID;
// 箭塔建筑id
var BUILD_TOWER_NID = 2103;
exports.BUILD_TOWER_NID = BUILD_TOWER_NID;
// 强弩兵ID
var PAWN_CROSSBOW_ID = 3305;
exports.PAWN_CROSSBOW_ID = PAWN_CROSSBOW_ID;
// 斧骑兵ID
var AX_CAVALRY_ID = 3406;
exports.AX_CAVALRY_ID = AX_CAVALRY_ID;
// 初始资源产量
var INIT_RES_OUTPUT = 120;
exports.INIT_RES_OUTPUT = INIT_RES_OUTPUT;
// 初始容量
var INIT_RES_CAP = 1000;
exports.INIT_RES_CAP = INIT_RES_CAP;
// 初始资源
var INIT_RES_COUNT = 700;
exports.INIT_RES_COUNT = INIT_RES_COUNT;
// 默认修建队列
var DEFAULT_BT_QUEUE_COUNT = 2;
exports.DEFAULT_BT_QUEUE_COUNT = DEFAULT_BT_QUEUE_COUNT;
// 立即完成修建需要的金币数
var IN_DONE_BT_GOLD = 30;
exports.IN_DONE_BT_GOLD = IN_DONE_BT_GOLD;
// 立即完成打造需要的金币数
var IN_DONE_FORGE_GOLD = 30;
exports.IN_DONE_FORGE_GOLD = IN_DONE_FORGE_GOLD;
// 修改昵称需要的金币数
var MODIFY_NICKNAME_GOLD = 500;
exports.MODIFY_NICKNAME_GOLD = MODIFY_NICKNAME_GOLD;
// 军队最大士兵个数
var ARMY_PAWN_MAX_COUNT = 9;
exports.ARMY_PAWN_MAX_COUNT = ARMY_PAWN_MAX_COUNT;
// 大使馆多少级可以创建联盟
var CREATE_ALLI_MAX_LV = 3;
exports.CREATE_ALLI_MAX_LV = CREATE_ALLI_MAX_LV;
// 
var DEFAULT_CITY_SIZE = cc.v2(1, 1); //默认的城市地块大小
exports.DEFAULT_CITY_SIZE = DEFAULT_CITY_SIZE;
var DEFAULT_AREA_SIZE = cc.v2(11, 11); //默认的区域大小
exports.DEFAULT_AREA_SIZE = DEFAULT_AREA_SIZE;
var DEFAULT_BUILD_SIZE = cc.v2(1, 1); //默认的建筑面积大小
exports.DEFAULT_BUILD_SIZE = DEFAULT_BUILD_SIZE;
var BOSS_BUILD_SIZE = cc.v2(3, 3); //boss
exports.BOSS_BUILD_SIZE = BOSS_BUILD_SIZE;
var DEFAULT_MAX_ARMY_COUNT = 5; //默认区域的最大容纳军队数量
exports.DEFAULT_MAX_ARMY_COUNT = DEFAULT_MAX_ARMY_COUNT;
var DEFAULT_MAX_ADD_PAWN_TIMES = 20; //默认最大补兵次数
exports.DEFAULT_MAX_ADD_PAWN_TIMES = DEFAULT_MAX_ADD_PAWN_TIMES;
// 加速行军倍数
var UP_MARCH_SPEED_MUL = 3;
exports.UP_MARCH_SPEED_MUL = UP_MARCH_SPEED_MUL;
// 城边加速倍数
var MAIN_CITY_MARCH_SPEED = {
    1: 3.5,
    2: 3,
    3: 2.5,
    4: 2,
    5: 1.5,
};
exports.MAIN_CITY_MARCH_SPEED = MAIN_CITY_MARCH_SPEED;
// 运送时间 格/小时
var TRANSIT_TIME = 300;
exports.TRANSIT_TIME = TRANSIT_TIME;
// 一场战斗最多持续时间 秒
var BATTLE_MAX_TIME = 3600 * 3;
exports.BATTLE_MAX_TIME = BATTLE_MAX_TIME;
// 地块底配置
var LAND_DI_CONF = [
    { list: [0, 0, 0, 0], no: '01' },
    { list: [0, 1, 1, 0], no: '02' },
    { list: [0, 1, 1, 1], no: '03' },
    { list: [0, 0, 1, 1], no: '04' },
    { list: [1, 1, 1, 0], no: '05' },
    { list: [1, 1, 1, 1], no: '06' },
    { list: [1, 0, 1, 1], no: '07' },
    { list: [1, 1, 0, 0], no: '08' },
    { list: [1, 1, 0, 1], no: '09' },
    { list: [1, 0, 0, 1], no: '10' },
    { list: [0, 0, 1, 0], no: '11' },
    { list: [1, 0, 1, 0], no: '12' },
    { list: [1, 0, 0, 0], no: '13' },
    { list: [0, 1, 0, 0], no: '14' },
    { list: [0, 1, 0, 1], no: '15' },
    { list: [0, 0, 0, 1], no: '16' },
];
exports.LAND_DI_CONF = LAND_DI_CONF;
// 地块底配置，方向：左上右下
var DECORATION_MUD_CONF = {
    '0011': '101',
    '1011': '102',
    '1001': '103',
    '0111': '104',
    '1101': '107',
    '0110': '108',
    '1110': '109',
    '1100': '110',
    '0010': '111',
    '1010': '112',
    '1000': '113',
    '0001': '114',
    '0101': '115',
    '0100': '116',
    '0000': '117',
    '1111': ['105', '106'],
};
exports.DECORATION_MUD_CONF = DECORATION_MUD_CONF;
// 边框线配置
var BORDER_LINE_CONF = [
    { size: cc.size(80, 4), pos: cc.v2(0, 38) },
    { size: cc.size(4, 80), pos: cc.v2(38, 0) },
    { size: cc.size(80, 4), pos: cc.v2(0, -38) },
    { size: cc.size(4, 80), pos: cc.v2(-38, 0) },
    { size: cc.size(4, 4), pos: cc.v2(-38, 38) },
    { size: cc.size(4, 4), pos: cc.v2(38, 38) },
    { size: cc.size(4, 4), pos: cc.v2(38, -38) },
    { size: cc.size(4, 4), pos: cc.v2(-38, -38) },
];
exports.BORDER_LINE_CONF = BORDER_LINE_CONF;
// 河流边框线配置
var RIVER_LINE_CONF = {
    0: { size: cc.size(80, 4), pos: cc.v2(0, 30) },
    111: { size: cc.size(4, 68), pos: cc.v2(38, -6) },
    112: { size: cc.size(4, 80), pos: cc.v2(38, 0) },
    121: { size: cc.size(4, 80), pos: cc.v2(38, -12) },
    122: { size: cc.size(4, 92), pos: cc.v2(38, -6) },
    311: { size: cc.size(4, 68), pos: cc.v2(-38, -6) },
    312: { size: cc.size(4, 80), pos: cc.v2(-38, 0) },
    321: { size: cc.size(4, 80), pos: cc.v2(-38, -12) },
    322: { size: cc.size(4, 92), pos: cc.v2(-38, -6) },
};
exports.RIVER_LINE_CONF = RIVER_LINE_CONF;
// 选择地块信息框大小
var SELECT_CELL_INFO_BOX = cc.rect(320, 320, 272, 320);
exports.SELECT_CELL_INFO_BOX = SELECT_CELL_INFO_BOX;
// 士兵气泡高度
var PAWN_BUBBLE_OFFSETY = 100;
exports.PAWN_BUBBLE_OFFSETY = PAWN_BUBBLE_OFFSETY;
// 地块资源配置列表字段
var CELL_RES_FIELDS = ['cereal', 'timber', 'stone'];
exports.CELL_RES_FIELDS = CELL_RES_FIELDS;
// 资源字段反向映射
var RES_FIELDS_CTYPE = {
    'cereal': Enums_1.CType.CEREAL,
    'timber': Enums_1.CType.TIMBER,
    'stone': Enums_1.CType.STONE,
};
exports.RES_FIELDS_CTYPE = RES_FIELDS_CTYPE;
// 通用类型对应图标url
var CTYPE_ICON_URL = (_a = {},
    _a[Enums_1.CType.TITLE] = 'icon/title_empty',
    _a[Enums_1.CType.WIN_POINT] = 'icon/win_point',
    _a[Enums_1.CType.HERO_OPT] = 'icon/hero_opt',
    _a[Enums_1.CType.UP_RECRUIT] = 'icon/up_recruit',
    _a);
exports.CTYPE_ICON_URL = CTYPE_ICON_URL;
// 通用类型对应图标url
var CTYPE_ICON = (_b = {},
    _b[Enums_1.CType.CEREAL] = 'cereal',
    _b[Enums_1.CType.TIMBER] = 'timber',
    _b[Enums_1.CType.STONE] = 'stone',
    _b[Enums_1.CType.GOLD] = 'gold',
    _b[Enums_1.CType.INGOT] = 'ingot',
    _b[Enums_1.CType.WAR_TOKEN] = 'war_token',
    _b[Enums_1.CType.EXP_BOOK] = 'exp_book',
    _b[Enums_1.CType.CEREAL_C] = 'cereal_c',
    _b[Enums_1.CType.IRON] = 'iron',
    _b[Enums_1.CType.UP_SCROLL] = 'up_scroll',
    _b[Enums_1.CType.FIXATOR] = 'fixator',
    _b[Enums_1.CType.BASE_RES] = 'base_res',
    _b[Enums_1.CType.BASE_RES_2] = 'base_res_2',
    _b[Enums_1.CType.STAMINA] = 'stamina',
    _b[Enums_1.CType.RANK_COIN] = 'rank_coin',
    _b);
exports.CTYPE_ICON = CTYPE_ICON;
// 通用类型对应的名字
var CTYPE_NAME = (_c = {},
    _c[Enums_1.CType.CEREAL] = 'ui.cereal',
    _c[Enums_1.CType.TIMBER] = 'ui.timber',
    _c[Enums_1.CType.STONE] = 'ui.stone',
    _c[Enums_1.CType.GOLD] = 'ui.gold',
    _c[Enums_1.CType.INGOT] = 'ui.ingot',
    _c[Enums_1.CType.WAR_TOKEN] = 'ui.war_token',
    _c[Enums_1.CType.EXP_BOOK] = 'ui.exp_book',
    _c[Enums_1.CType.CEREAL_C] = 'ui.cereal_c',
    _c[Enums_1.CType.IRON] = 'ui.iron',
    _c[Enums_1.CType.UP_SCROLL] = 'ui.up_scroll',
    _c[Enums_1.CType.FIXATOR] = 'ui.fixator',
    _c[Enums_1.CType.BASE_RES] = 'ui.base_res',
    _c);
exports.CTYPE_NAME = CTYPE_NAME;
// 建筑效果配置
var BUILD_EFFECT_TYPE_CONF = (_d = {},
    _d[Enums_1.CEffect.BT_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.BUILD_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.GRANARY_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.WAREHOUSE_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.XL_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.ALLIANCE_PERS] = { vtype: 'number' },
    _d[Enums_1.CEffect.MERCHANT_COUNT] = { vtype: 'number' },
    _d[Enums_1.CEffect.DRILL_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.WALL_HP] = { vtype: 'number' },
    _d[Enums_1.CEffect.FORGE_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.ARMY_COUNT] = { vtype: 'number' },
    _d[Enums_1.CEffect.RES_OUTPUT] = { vtype: 'number' },
    _d[Enums_1.CEffect.MARCH_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.UPLVING_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.GW_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.XL_2LV] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.TRANSIT_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MAIN_MARCH_MUL] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CITY_BUILD_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.TREASURE_AWARD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.FREE_RECAST] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.RARE_RES_OUTPUT] = { vtype: 'number' },
    _d[Enums_1.CEffect.MORE_RARE_RES] = { vtype: 'number' },
    _d[Enums_1.CEffect.LV_UP_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.TOWER_LV] = { vtype: 'number' },
    _d[Enums_1.CEffect.FARM_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.QUARRY_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MILL_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CURE_QUEUE] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MARKET_SERVICE_CHARGE] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CITY_COUNT_LIMIT] = { vtype: 'number' },
    _d[Enums_1.CEffect.TOWER_HP] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.OTHER_RES_ODDS] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CURE_CD] = { vtype: 'number', suffix: '%' },
    _d);
exports.BUILD_EFFECT_TYPE_CONF = BUILD_EFFECT_TYPE_CONF;
// 行军军队名字颜色
var MARCH_ARMY_NAME_COLOR = (_e = {},
    _e[Enums_1.MarchLineType.SELF_ARMY] = '#59A733',
    _e[Enums_1.MarchLineType.OTHER_ARMY] = '#C34B3F',
    _e[Enums_1.MarchLineType.ALLI_ARMY] = '#4F8FBA',
    _e);
exports.MARCH_ARMY_NAME_COLOR = MARCH_ARMY_NAME_COLOR;
// 行军军队时间颜色
var MARCH_ARMY_TIME_COLOR = (_f = {},
    _f[Enums_1.MarchLineType.SELF_ARMY] = '#FFFFFF',
    _f[Enums_1.MarchLineType.OTHER_ARMY] = '#FF9162',
    _f[Enums_1.MarchLineType.ALLI_ARMY] = '#7FD6FF',
    _f);
exports.MARCH_ARMY_TIME_COLOR = MARCH_ARMY_TIME_COLOR;
// 军队状态颜色
var ARMY_STATE_COLOR = (_g = {},
    _g[Enums_1.ArmyState.NONE] = '#936E5A',
    _g[Enums_1.ArmyState.MARCH] = '#936E5A',
    _g[Enums_1.ArmyState.FIGHT] = '#C34B3F',
    _g[Enums_1.ArmyState.DRILL] = '#59A733',
    _g[Enums_1.ArmyState.LVING] = '#59A733',
    _g[Enums_1.ArmyState.CURING] = '#59A733',
    _g[Enums_1.ArmyState.TONDEN] = '#59A733',
    _g);
exports.ARMY_STATE_COLOR = ARMY_STATE_COLOR;
// 邮件状态颜色
var MAIL_STATE_COLOR = (_h = {},
    _h[Enums_1.MailStateType.NONE] = '#C34B3F',
    _h[Enums_1.MailStateType.NOT_CLAIM] = '#C34B3F',
    _h[Enums_1.MailStateType.READ] = '#A18876',
    _h);
exports.MAIL_STATE_COLOR = MAIL_STATE_COLOR;
var COLOR_NORMAL = {
    DONE: '#21DC2D'
};
exports.COLOR_NORMAL = COLOR_NORMAL;
// 军队记录说明的配置
var ARMY_RECORD_DESC_CONF = {
    0: ['index'],
    1: ['index', 'target'],
    2: ['index', 'target'],
    3: ['index', 'target'],
    4: ['index'],
    5: ['index', 'target'],
    6: ['index'],
    7: ['index', 'target'],
};
exports.ARMY_RECORD_DESC_CONF = ARMY_RECORD_DESC_CONF;
// 回放倍数
var PLAYBACK_MULS = [
    { val: 4, text: '0.25x' },
    { val: 2, text: '0.5x' },
    { val: 1, text: '1x' },
    { val: 0.5, text: '2x' },
    { val: 0.25, text: '4x' },
];
exports.PLAYBACK_MULS = PLAYBACK_MULS;
// 固定到菜单配置
var FIXATION_MENU_CONFIG = {
    2001: 'build/BuildMainInfo',
    2004: 'build/BuildBarracks',
    2005: 'build/BuildEmbassy',
    2006: 'build/BuildBazaar',
    2008: 'build/BuildSmithy',
    2010: 'build/BuildFactory',
    2011: 'build/BuildDrillground',
    2012: 'build/BuildTower',
    2013: 'build/BuildTower',
    2014: 'build/BuildBazaar',
    2015: 'build/BuildHerohall',
    2016: 'build/BuildHospital',
};
exports.FIXATION_MENU_CONFIG = FIXATION_MENU_CONFIG;
var FIXATION_MENU_MAX_COUNT = 3; //固定到菜单最多个数
exports.FIXATION_MENU_MAX_COUNT = FIXATION_MENU_MAX_COUNT;
// 免费头像列表
var FREE_HEAD_ICONS = [
    'head_icon_free_001',
    'head_icon_free_002',
    'head_icon_free_003',
    'head_icon_free_004',
    'head_icon_free_005',
    'head_icon_free_006',
    'head_icon_free_007',
    'head_icon_free_008',
];
exports.FREE_HEAD_ICONS = FREE_HEAD_ICONS;
// 商城购买添加产量需要的金币
var ADD_OUTPUT_GOLD = 50;
exports.ADD_OUTPUT_GOLD = ADD_OUTPUT_GOLD;
var ADD_OUTPUT_RATIO = 20; //商城购买添加产量比例
exports.ADD_OUTPUT_RATIO = ADD_OUTPUT_RATIO;
var ADD_OUTPUT_TIME = 1 * 86400 * 1000; //商城购买添加产量持续时间
exports.ADD_OUTPUT_TIME = ADD_OUTPUT_TIME;
// 内政政策槽位配置
var POLICY_SLOT_CONF = [3, 5, 10, 15, 20];
exports.POLICY_SLOT_CONF = POLICY_SLOT_CONF;
// 装备槽位配置
var EQUIP_SLOT_CONF = [1, 3, 5, 7, 10, 12, 14, 16, 18, 20];
exports.EQUIP_SLOT_CONF = EQUIP_SLOT_CONF;
var EQUIP_SLOT_EXCLUSIVE_LV = { 10: true, 18: true };
exports.EQUIP_SLOT_EXCLUSIVE_LV = EQUIP_SLOT_EXCLUSIVE_LV;
// 装备融炼解锁等级
var EQUIP_SMELT_NEED_LV = [14, 20];
exports.EQUIP_SMELT_NEED_LV = EQUIP_SMELT_NEED_LV;
// 士兵槽位配置
var PAWN_SLOT_CONF = [1, 2, 4, 7];
exports.PAWN_SLOT_CONF = PAWN_SLOT_CONF;
// 研究每重置一次需要的金币
var RESET_STUDY_SLOT_GOLD = 50;
exports.RESET_STUDY_SLOT_GOLD = RESET_STUDY_SLOT_GOLD;
// 多长时间可以退出联盟
var CAN_EXIT_ALLI_TIME = 3600000 * 12;
exports.CAN_EXIT_ALLI_TIME = CAN_EXIT_ALLI_TIME;
// 创建联盟费用
var CREATE_ALLI_COST = '1,0,3000|2,0,2000|3,0,2000';
exports.CREATE_ALLI_COST = CREATE_ALLI_COST;
var CREATE_ALLI_COND = 100;
exports.CREATE_ALLI_COND = CREATE_ALLI_COND;
// 单个玩家给其他玩家改变人气间隔
var ONE_USER_POPULARITY_CHANGE_INTERVAL = 86400000 * 30;
exports.ONE_USER_POPULARITY_CHANGE_INTERVAL = ONE_USER_POPULARITY_CHANGE_INTERVAL;
// 外显buff 同时显示 层级
var BUFF_NODE_ZINDEX = (_j = {},
    _j[Enums_1.BuffType.SHIELD] = 1,
    _j[Enums_1.BuffType.PROTECTION_SHIELD] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD_001] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD_102] = 1,
    _j[Enums_1.BuffType.ABNEGATION_SHIELD] = 1,
    _j[Enums_1.BuffType.PARRY] = 2,
    _j[Enums_1.BuffType.PARRY_001] = 2,
    _j[Enums_1.BuffType.PARRY_102] = 2,
    _j[Enums_1.BuffType.WITHSTAND] = 2,
    _j[Enums_1.BuffType.JUMPSLASH_DAMAGE] = 2,
    _j[Enums_1.BuffType.BEHEADED_GENERAL] = 2,
    _j[Enums_1.BuffType.ANTICIPATION_DEFENSE] = 2,
    _j[Enums_1.BuffType.ANTICIPATION_ATTACK] = 2,
    _j[Enums_1.BuffType.DIZZINESS] = 3,
    _j[Enums_1.BuffType.PARALYSIS] = 3,
    _j[Enums_1.BuffType.PARALYSIS_UP] = 3,
    _j[Enums_1.BuffType.WIRE_CHAIN] = 3,
    _j[Enums_1.BuffType.SILENCE] = 4,
    _j[Enums_1.BuffType.CHAOS] = 4,
    _j[Enums_1.BuffType.POISONED_WINE] = 4,
    _j[Enums_1.BuffType.LIAN_PO_ATTACK] = 5,
    _j[Enums_1.BuffType.LIAN_PO_DEFEND] = 5,
    _j);
exports.BUFF_NODE_ZINDEX = BUFF_NODE_ZINDEX;
// 外显buff 同时显示
var NEED_SHOW_BUFF = (_k = {},
    _k[Enums_1.BuffType.SHIELD] = true,
    _k[Enums_1.BuffType.PROTECTION_SHIELD] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD_001] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD_102] = true,
    _k[Enums_1.BuffType.ABNEGATION_SHIELD] = true,
    _k[Enums_1.BuffType.PARRY] = true,
    _k[Enums_1.BuffType.PARRY_001] = true,
    _k[Enums_1.BuffType.PARRY_102] = true,
    _k[Enums_1.BuffType.WITHSTAND] = true,
    _k[Enums_1.BuffType.JUMPSLASH_DAMAGE] = true,
    _k[Enums_1.BuffType.BEHEADED_GENERAL] = true,
    _k[Enums_1.BuffType.ANTICIPATION_DEFENSE] = true,
    _k[Enums_1.BuffType.ANTICIPATION_ATTACK] = true,
    _k[Enums_1.BuffType.DIZZINESS] = true,
    _k[Enums_1.BuffType.PARALYSIS] = true,
    _k[Enums_1.BuffType.PARALYSIS_UP] = true,
    _k[Enums_1.BuffType.WIRE_CHAIN] = true,
    _k[Enums_1.BuffType.SILENCE] = true,
    _k[Enums_1.BuffType.CHAOS] = true,
    _k[Enums_1.BuffType.POISONED_WINE] = true,
    _k[Enums_1.BuffType.LIAN_PO_ATTACK] = true,
    _k[Enums_1.BuffType.LIAN_PO_DEFEND] = true,
    _k);
exports.NEED_SHOW_BUFF = NEED_SHOW_BUFF;
// 外显buff 互斥显示
var NEED_MUTUAL_BUFF = (_l = {},
    _l[Enums_1.BuffType.ARMOR_PENETRATION] = true,
    _l[Enums_1.BuffType.INSPIRE] = true,
    _l[Enums_1.BuffType.WORTHY_MONARCH] = true,
    _l[Enums_1.BuffType.DESTROY_WEAPONS] = true,
    _l[Enums_1.BuffType.POISONING_MAX_HP] = true,
    _l[Enums_1.BuffType.POISONING_CUR_HP] = true,
    _l[Enums_1.BuffType.INFECTION_PLAGUE] = true,
    _l[Enums_1.BuffType.BLEED] = true,
    _l[Enums_1.BuffType.DAMAGE_INCREASE] = true,
    _l[Enums_1.BuffType.DAMAGE_REDUCE] = true,
    _l[Enums_1.BuffType.GOD_WAR] = true,
    _l[Enums_1.BuffType.FEAR] = true,
    _l[Enums_1.BuffType.TIMIDITY] = true,
    _l[Enums_1.BuffType.TIGER_MANIA] = true,
    _l[Enums_1.BuffType.IRREMOVABILITY] = true,
    _l[Enums_1.BuffType.OVERLORD] = true,
    _l[Enums_1.BuffType.IGNITION] = true,
    _l[Enums_1.BuffType.RAGE] = true,
    _l);
exports.NEED_MUTUAL_BUFF = NEED_MUTUAL_BUFF;
// 外显buff 类型转换
var BUFF_SHOW_TYPE_TRAN = (_m = {},
    _m[Enums_1.BuffType.POISONING_CUR_HP] = Enums_1.BuffType.POISONING_MAX_HP,
    _m[Enums_1.BuffType.INFECTION_PLAGUE] = Enums_1.BuffType.POISONING_MAX_HP,
    _m[Enums_1.BuffType.DAMAGE_REDUCE] = Enums_1.BuffType.DESTROY_WEAPONS,
    _m[Enums_1.BuffType.WORTHY_MONARCH] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.GOD_WAR] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.TIGER_MANIA] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.IRREMOVABILITY] = Enums_1.BuffType.TIMIDITY,
    _m[Enums_1.BuffType.OVERLORD] = Enums_1.BuffType.TIMIDITY,
    _m);
exports.BUFF_SHOW_TYPE_TRAN = BUFF_SHOW_TYPE_TRAN;
// 护盾buff
var SHIELD_BUFF = (_o = {},
    _o[Enums_1.BuffType.SHIELD] = true,
    _o[Enums_1.BuffType.PROTECTION_SHIELD] = true,
    _o[Enums_1.BuffType.LOW_HP_SHIELD] = true,
    _o[Enums_1.BuffType.ATTACK_SHIELD] = true,
    _o[Enums_1.BuffType.SUCKBLOOD_SHIELD] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD_001] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD_102] = true,
    _o[Enums_1.BuffType.BATTLE_BEGIN_SHIELD] = true,
    _o[Enums_1.BuffType.KUROU_SHIELD] = true,
    _o[Enums_1.BuffType.SUCK_SHIELD] = true,
    _o[Enums_1.BuffType.ABNEGATION_SHIELD] = true,
    _o[Enums_1.BuffType.LONGITUDINAL_CLEFT_SHIELD] = true,
    _o[Enums_1.BuffType.CRIMSONGOLD_SHIELD] = true,
    _o[Enums_1.BuffType.BLACK_IRON_STAFF_SHIELD] = true,
    _o);
exports.SHIELD_BUFF = SHIELD_BUFF;
// 战斗特效类型
// 10000002.攻击 10000003.闪避 10000004.减伤 10000005.护盾
var BATTLE_EFFECT_TYPE = {
    ATTACK: [10000002],
    DAMAGE_REDUCTION: [10000004],
    SHIELD: [10000005],
    VALOR: [10000002, 10000003],
    WISDOM_COURAGE: [10000002, 10000004],
    KUROU: [10000002, 10000005],
    SAND_CLOCK: [10000006],
    TONDEN: [114001],
};
exports.BATTLE_EFFECT_TYPE = BATTLE_EFFECT_TYPE;
// 聊天弹幕颜色
var CHAT_BARRAGE_COLOR = {
    0: '#FFFFFF',
    1: '#5BB8FF',
    2: '#FF81F7',
};
exports.CHAT_BARRAGE_COLOR = CHAT_BARRAGE_COLOR;
// 和大自然交换资源手续费
var REPLACEMENT_SERVICE_CHARGE = 60;
exports.REPLACEMENT_SERVICE_CHARGE = REPLACEMENT_SERVICE_CHARGE;
// 和大自然交换最少资源
var REPLACEMENT_MIN_RES_COUNT = 100;
exports.REPLACEMENT_MIN_RES_COUNT = REPLACEMENT_MIN_RES_COUNT;
// 每日置换次数
var REPLACEMENT_TODAY_COUNT_MAP = {
    0: 3,
    1: 3,
    2: 3,
};
exports.REPLACEMENT_TODAY_COUNT_MAP = REPLACEMENT_TODAY_COUNT_MAP;
// 各个资源占用运送的容量
var RES_TRANSIT_CAP = (_p = {},
    _p[Enums_1.CType.CEREAL] = 1,
    _p[Enums_1.CType.TIMBER] = 1,
    _p[Enums_1.CType.STONE] = 1,
    _p[Enums_1.CType.EXP_BOOK] = 100,
    _p[Enums_1.CType.IRON] = 100,
    _p[Enums_1.CType.UP_SCROLL] = 500,
    _p[Enums_1.CType.FIXATOR] = 500,
    _p);
exports.RES_TRANSIT_CAP = RES_TRANSIT_CAP;
// 加速招募倍数
var UP_RECRUIT_PAWN_MUL = 0.125;
exports.UP_RECRUIT_PAWN_MUL = UP_RECRUIT_PAWN_MUL;
// 多语言名字
var LANGUAGE_TEXT_LIST = [
    { lang: 'en', text: 'ENGLISH' },
    { lang: 'cn', text: '简体中文' },
    { lang: 'hk', text: '繁體(港澳)' },
    { lang: 'tw', text: '繁體(臺灣)' },
    { lang: 'jp', text: '日本語' },
    { lang: 'kr', text: '한국어' },
    { lang: 'idl', text: 'Bahasa Indonesia' },
    { lang: 'th', text: 'ภาษาไทย' },
    { lang: 'vi', text: 'Tiếng Việt' },
];
exports.LANGUAGE_TEXT_LIST = LANGUAGE_TEXT_LIST;
// 注销账号等待时间
var LOGOUT_MAX_DAY = 86400000 * 7;
exports.LOGOUT_MAX_DAY = LOGOUT_MAX_DAY;
// 联盟职位说明
var ALLI_JOB_DESC = {
    0: [1, 2, 3, 4, 5, 7, 8],
    1: [3, 4, 5, 7],
    2: [6, 8],
    10: [0],
};
exports.ALLI_JOB_DESC = ALLI_JOB_DESC;
// 职位数量
var ALLI_JOB_COUNT = {
    0: 1,
    1: 1,
    2: 2,
    10: 40,
};
exports.ALLI_JOB_COUNT = ALLI_JOB_COUNT;
// 开服多久内不可攻占
var NOT_OCCUPY_BY_SERVER_RUNTIME = 86400000 * 3;
exports.NOT_OCCUPY_BY_SERVER_RUNTIME = NOT_OCCUPY_BY_SERVER_RUNTIME;
var NOT_OCCUPY_BY_MAX_LAND_COUNT = 100;
exports.NOT_OCCUPY_BY_MAX_LAND_COUNT = NOT_OCCUPY_BY_MAX_LAND_COUNT;
// 不同类型的区最多可玩几个区
var CONCURRENT_GAME_LIMIT = 1;
exports.CONCURRENT_GAME_LIMIT = CONCURRENT_GAME_LIMIT;
// 领地积分配置
var LAND_SCORE_CONF = {
    1: [[50, 2], [100, 1]],
    2: [[40, 4], [80, 2]],
    3: [[30, 6], [60, 3]],
    4: [[20, 8], [40, 4]],
    5: [[10, 10], [20, 5]],
};
exports.LAND_SCORE_CONF = LAND_SCORE_CONF;
// 多久才可以删除私聊
var REMOVE_PCHAT_TIME = 3600000 * 12;
exports.REMOVE_PCHAT_TIME = REMOVE_PCHAT_TIME;
// 攻占玩家领地要求最低距离
var OCCUPY_PLAYER_CELL_MIN_DIS = 5;
exports.OCCUPY_PLAYER_CELL_MIN_DIS = OCCUPY_PLAYER_CELL_MIN_DIS;
// 多少地可以无限制私聊
var NOLIMIT_PCHAT_MAX_LAND = 150;
exports.NOLIMIT_PCHAT_MAX_LAND = NOLIMIT_PCHAT_MAX_LAND;
// 聊天 显示时间的最大间隔
var SHOW_TIME_MAX_INTERVAL = 60000 * 1;
exports.SHOW_TIME_MAX_INTERVAL = SHOW_TIME_MAX_INTERVAL;
// 可申请添加好友最小地块数
var FRIENDS_MIN_LAND_COUNT = 100;
exports.FRIENDS_MIN_LAND_COUNT = FRIENDS_MIN_LAND_COUNT;
// 战斗预测费用
var BATTLE_FORECAST_COST = 30;
exports.BATTLE_FORECAST_COST = BATTLE_FORECAST_COST;
// 战斗预测免费次数
var BATTLE_FORECAST_FREE_COUNT = 5;
exports.BATTLE_FORECAST_FREE_COUNT = BATTLE_FORECAST_FREE_COUNT;
// 一键打开宝箱要求
var OPEN_ALL_TREASURE_MIN_LAND_COUNT = 100;
exports.OPEN_ALL_TREASURE_MIN_LAND_COUNT = OPEN_ALL_TREASURE_MIN_LAND_COUNT;
// 最小可修改的行军速度
var CAN_MIN_MARCH_SPEED = 30;
exports.CAN_MIN_MARCH_SPEED = CAN_MIN_MARCH_SPEED;
// 一个季节持续的时间
var SEASON_DURATION_TIME = 3 * 86400000;
exports.SEASON_DURATION_TIME = SEASON_DURATION_TIME;
// 遗迹加速资源倍数
var ANCIENT_SUP_COST_MUL = 40;
exports.ANCIENT_SUP_COST_MUL = ANCIENT_SUP_COST_MUL;
// 遗迹加速时间
var ANCIENT_SUP_TIME = 6 * 60000;
exports.ANCIENT_SUP_TIME = ANCIENT_SUP_TIME;
// 聊天相关
var CHAT_MAX_COUNT = 50;
exports.CHAT_MAX_COUNT = CHAT_MAX_COUNT;
var CHAT_SEND_INTERVAL = 6000; //发送聊天的预期间隔 (毫秒)
exports.CHAT_SEND_INTERVAL = CHAT_SEND_INTERVAL;
var CHAT_TOLERATE_MAX_COUNT = 3; //最多容忍多少次在间隔内发送
exports.CHAT_TOLERATE_MAX_COUNT = CHAT_TOLERATE_MAX_COUNT;
var CHAT_REST_MAX_TIME = 30000; //如果太频繁就休息一下
exports.CHAT_REST_MAX_TIME = CHAT_REST_MAX_TIME;
var CHAT_BANNED_REST_MAX_TIME = 60000 * 10; //禁言休息时间
exports.CHAT_BANNED_REST_MAX_TIME = CHAT_BANNED_REST_MAX_TIME;
// 发送喇叭费用
var SEND_TRUMPET_COST = 50;
exports.SEND_TRUMPET_COST = SEND_TRUMPET_COST;
var SEND_TRUMPET_ACC_COST = 25;
exports.SEND_TRUMPET_ACC_COST = SEND_TRUMPET_ACC_COST;
// 多长时间可以取消报名
var SERVER_APPLY_CANCEL_CD = 1 * 60 * 1000;
exports.SERVER_APPLY_CANCEL_CD = SERVER_APPLY_CANCEL_CD;
// 下次报名的等待时间
var NEXT_APPLY_CD = 6 * 1000;
exports.NEXT_APPLY_CD = NEXT_APPLY_CD;
// 点将一次的费用
var POINTSETS_ONE_COST = 10;
exports.POINTSETS_ONE_COST = POINTSETS_ONE_COST;
// 点击5次 金币费用
var POINTSETS_ONE_GOLD_COST = 598;
exports.POINTSETS_ONE_GOLD_COST = POINTSETS_ONE_GOLD_COST;
// 残卷合成画像 需要数量
var PORTRAYAL_COMP_NEED_COUNT = 3;
exports.PORTRAYAL_COMP_NEED_COUNT = PORTRAYAL_COMP_NEED_COUNT;
// 还原画像费用
var RESTORE_PORTRAYAL_WAR_TOKEN_COST = 50;
exports.RESTORE_PORTRAYAL_WAR_TOKEN_COST = RESTORE_PORTRAYAL_WAR_TOKEN_COST;
var RESTORE_PORTRAYAL_GOLD_COST = 598;
exports.RESTORE_PORTRAYAL_GOLD_COST = RESTORE_PORTRAYAL_GOLD_COST;
// 购买自选英雄费用 元宝
var BUY_OPT_HERO_COST = 999;
exports.BUY_OPT_HERO_COST = BUY_OPT_HERO_COST;
// 英雄自选礼包
var HERO_OPT_GIFT = {
    // 陈到, 徐盛, 张辽
    1: [310101, 320101, 340101],
    // 陈到, 李嗣业, 徐盛, 黄盖, 王异, 张辽, 徐晃
    2: [310101, 310401, 320101, 320401, 330301, 340101, 340601],
    // 3: 全自选
    // 陈到, 张郃, 李嗣业, 文鸯, 徐盛, 曹仁, 张飞, 黄盖, 刘宠, 王异, 曹休, 张辽, 许诸, 夏侯渊, 徐晃
    4: [310101, 310201, 310401, 310601, 320101, 320201, 320301, 320401, 330202, 330301, 330501, 340101, 340401, 340501, 340601],
};
exports.HERO_OPT_GIFT = HERO_OPT_GIFT;
// 英雄复活时间
var HERO_REVIVES_TIME = 3600000 * 5;
exports.HERO_REVIVES_TIME = HERO_REVIVES_TIME;
// 英雄槽位等级开启条件
var HERO_SLOT_LV_COND = [1, 10, 20];
exports.HERO_SLOT_LV_COND = HERO_SLOT_LV_COND;
// 养由基召唤时的对应等级
var SUMMON_LV = {
    1: 1,
    2: 2,
    3: 4,
    4: 6,
    5: 8,
    6: 10,
};
exports.SUMMON_LV = SUMMON_LV;
// 默认的宠物id
var DEFAULT_PET_ID = 4101;
exports.DEFAULT_PET_ID = DEFAULT_PET_ID;
// 矛
var SPEAR_PAWN_ID = 3701;
exports.SPEAR_PAWN_ID = SPEAR_PAWN_ID;
// 火
var FIRE_PAWN_ID = 3702;
exports.FIRE_PAWN_ID = FIRE_PAWN_ID;
// 资源
var RES_MAP = (_q = {},
    _q[Enums_1.CType.CEREAL] = true,
    _q[Enums_1.CType.TIMBER] = true,
    _q[Enums_1.CType.STONE] = true,
    _q[Enums_1.CType.BASE_RES] = true,
    _q[Enums_1.CType.EXP_BOOK] = true,
    _q[Enums_1.CType.IRON] = true,
    _q[Enums_1.CType.UP_SCROLL] = true,
    _q[Enums_1.CType.FIXATOR] = true,
    _q);
exports.RES_MAP = RES_MAP;
// 最多可标记多少个
var MAX_MAP_MARK_COUNT = 10;
exports.MAX_MAP_MARK_COUNT = MAX_MAP_MARK_COUNT;
// 申请联盟个数限制
var ALLI_APPLY_MAX_COUNT = 3;
exports.ALLI_APPLY_MAX_COUNT = ALLI_APPLY_MAX_COUNT;
// 大厅模式对应的底部
var LOBBY_MODE_BUTTOM_NAME = (_r = {},
    _r[Enums_1.LobbyModeType.FREE] = 'team',
    _r[Enums_1.LobbyModeType.NEWBIE] = 'team',
    _r[Enums_1.LobbyModeType.RANKED] = 'team',
    _r[Enums_1.LobbyModeType.SNAIL_ISLE] = 'twomiles',
    _r);
exports.LOBBY_MODE_BUTTOM_NAME = LOBBY_MODE_BUTTOM_NAME;
// 斜度
var SKEW_ANGLE = 45;
exports.SKEW_ANGLE = SKEW_ANGLE;
// 斜着的外宽高
var SKEW_SIZE = cc.size(16, 8);
exports.SKEW_SIZE = SKEW_SIZE;
// 斜着的内宽高 32 16
var SKEW_SIZE_HALF = cc.size(SKEW_SIZE.width * 0.5, SKEW_SIZE.height * 0.5);
exports.SKEW_SIZE_HALF = SKEW_SIZE_HALF;
// 段位商城的兵符配置
var RANK_SHOP_WAR_TOKEN_CONFIG = [
    { warToken: 10, coin: 10 },
    { warToken: 100, coin: 100 },
    { warToken: 1000, coin: 1000 },
    { warToken: 10000, coin: 10000 },
];
exports.RANK_SHOP_WAR_TOKEN_CONFIG = RANK_SHOP_WAR_TOKEN_CONFIG;
// 战斗的血条颜色
var BATTLE_HPBAR_COLOR = {
    m: { bar: '#8BE273', bg: '#162D20' },
    f: { bar: '#6DB5E2', bg: '#121D3A' },
    0: { bar: '#EE2A4A', bg: '#3B1316' },
    1: { bar: '#FF64B8', bg: '#41142C' },
    2: { bar: '#AD64FF', bg: '#281240' },
    3: { bar: '#FF9648', bg: '#4A2B14' },
};
exports.BATTLE_HPBAR_COLOR = BATTLE_HPBAR_COLOR;
// 战斗的火焰颜色
var BATTLE_FIRE_COLOR = {
    m: '#53B977',
    f: '#40A4E9',
    0: '#B90900',
    1: '#FF76F7',
    2: '#AD64FF',
    3: '#FFA836',
};
exports.BATTLE_FIRE_COLOR = BATTLE_FIRE_COLOR;
// 战令价格配置
var RECHARGE_BATTLE_PASS = 'jwm_up_book'; // $8.99
exports.RECHARGE_BATTLE_PASS = RECHARGE_BATTLE_PASS;
// 战令经验购买配置
var RECHARGE_BATTLE_PASS_EXP = [50, 100]; // 50元宝购买100经验
exports.RECHARGE_BATTLE_PASS_EXP = RECHARGE_BATTLE_PASS_EXP;
// 有奖问卷调查id
var PRIZE_QUESTION_ID = 99900001;
exports.PRIZE_QUESTION_ID = PRIZE_QUESTION_ID;
// 有奖问卷调查期限
var PRIZE_QUESTION_TIME = ['2024-12-26-06-00', '2024-12-30-06-00'];
exports.PRIZE_QUESTION_TIME = PRIZE_QUESTION_TIME;
// 打开允许通知弹窗的公共CD
var NOTICE_PERMISSION_CD = 24 * 60 * 60 * 1000; // 24小时
exports.NOTICE_PERMISSION_CD = NOTICE_PERMISSION_CD;
// 每日屯田次数
var TODAY_TONDEN_MAX_COUNT = 10;
exports.TODAY_TONDEN_MAX_COUNT = TODAY_TONDEN_MAX_COUNT;
// 屯田资源获取比例
var TONDEN_GET_RES_RATIO = 1;
exports.TONDEN_GET_RES_RATIO = TONDEN_GET_RES_RATIO;
// 屯田奖励点消耗倍数
var TONDEN_STAMINA_MUL = 3;
exports.TONDEN_STAMINA_MUL = TONDEN_STAMINA_MUL;
// 医馆伤兵上限
var HOSPITAL_PAWN_LIMIT = 200;
exports.HOSPITAL_PAWN_LIMIT = HOSPITAL_PAWN_LIMIT;
// 各等级士兵战败后回馆概率
var GO_HOSPITAL_CHANCE = {
    1: '0%',
    2: '100%',
    3: '100%',
    4: '100%',
    5: '100%',
    6: '100%',
};
exports.GO_HOSPITAL_CHANCE = GO_HOSPITAL_CHANCE;
// 画像的天选几率
var PORTRAYAL_CHOSENONE_ODDS = 0.005;
exports.PORTRAYAL_CHOSENONE_ODDS = PORTRAYAL_CHOSENONE_ODDS;
// 研究类型转评论类型
var STUDY_TO_BOOKTYPE = (_s = {},
    _s[Enums_1.StudyType.POLICY] = Enums_1.BookCommentType.POLICY,
    _s[Enums_1.StudyType.PAWN] = Enums_1.BookCommentType.PAWN,
    _s[Enums_1.StudyType.EQUIP] = Enums_1.BookCommentType.EQUIP,
    _s[Enums_1.StudyType.EXCLUSIVE] = Enums_1.BookCommentType.EQUIP,
    _s);
exports.STUDY_TO_BOOKTYPE = STUDY_TO_BOOKTYPE;
// 盟主投票最大次数
var ALLI_LEADER_VOTE_MAX_COUNT = 4;
exports.ALLI_LEADER_VOTE_MAX_COUNT = ALLI_LEADER_VOTE_MAX_COUNT;
// 招募动态资源每级系数
var PAWN_COST_LV_LIST = [1, 2, 4, 6, 8, 10];
exports.PAWN_COST_LV_LIST = PAWN_COST_LV_LIST;
// 工厂解锁配置
var FACTORY_SLOT_CONF = [5];
exports.FACTORY_SLOT_CONF = FACTORY_SLOT_CONF;
// 摄像机背景颜色
var CAMERA_BG_COLOR = ['#ACC961', '#88CA6E', '#E4B765', '#A7E2E3'];
exports.CAMERA_BG_COLOR = CAMERA_BG_COLOR;
//
var MAP_MASK_ITEM_COLOR = ['#2B8A85', '#1755AC', '#832E4F', '#8378C2'];
exports.MAP_MASK_ITEM_COLOR = MAP_MASK_ITEM_COLOR;
// 区域内的地面颜色
var AREA_DI_COLOR_CONF = [
    // 春
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#AFC864', battle: ['#CED974', '#BCD16A'], build: '#EFE28C' },
    },
    // 夏
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' },
    },
    // 秋
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' },
    },
    // 冬
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' },
    },
];
exports.AREA_DI_COLOR_CONF = AREA_DI_COLOR_CONF;
// 难度底板颜色
var DIFFICULTY_BG_COLOR = {
    1: '#81A514',
    2: '#6683AB',
    3: '#9C58BF',
    4: '#CE59A0',
    5: '#C34B3F',
};
exports.DIFFICULTY_BG_COLOR = DIFFICULTY_BG_COLOR;

cc._RF.pop();