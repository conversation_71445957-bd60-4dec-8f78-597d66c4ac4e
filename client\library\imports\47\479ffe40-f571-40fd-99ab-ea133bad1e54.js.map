{"version": 3, "sources": ["assets\\app\\script\\common\\constant\\Constant.ts"], "names": [], "mappings": ";;;;;AAAA,iDAAiD;;;;;;;AAEjD,iCAAsI;AAEtI,OAAO;AACP,IAAM,WAAW,GAAG,EAAE,CAAA;AAw2BlB,kCAAW;AAv2Bf,QAAQ;AACR,IAAM,SAAS,GAAG,EAAE,CAAA;AAw2BhB,8BAAS;AAv2Bb,WAAW;AACX,IAAM,cAAc,GAAG,CAAC,CAAA;AAw2BpB,wCAAc;AAt2BlB,SAAS;AACT,IAAM,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAA;AAk2BvD,0CAAe;AAh2BnB,EAAE;AACF,IAAM,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,GAAG,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAA;AAi2B1D,wCAAc;AA/1BlB,WAAW;AACX,IAAM,kBAAkB,GAAG,cAAc,CAAC,CAAC,GAAG,EAAE,CAAA;AAg2B5C,gDAAkB;AA91BtB,UAAU;AACV,IAAM,eAAe,GAAG,EAAE,GAAG,SAAS,CAAA;AA81BlC,0CAAe;AA71BnB,QAAQ;AACR,IAAM,UAAU,GAAG,KAAK,CAAA;AA61BpB,gCAAU;AA31Bd,OAAO;AACP,IAAM,eAAe,GAAG,GAAG,CAAA;AA21BvB,0CAAe;AAz1BnB,YAAY;AACZ,IAAM,oBAAoB,GAAG,GAAG,CAAA;AAy1B5B,oDAAoB;AAv1BxB,OAAO;AACP,IAAM,aAAa,GAAG,IAAI,CAAA;AAu1BtB,sCAAa;AAt1BjB,OAAO;AACP,IAAM,aAAa,GAAG,IAAI,CAAA;AAu1BtB,sCAAa;AAt1BjB,OAAO;AACP,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAs1B7B,0CAAe;AAr1BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAs1B7B,0CAAe;AAr1BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAs1B7B,0CAAe;AAr1BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAs1B7B,0CAAe;AAr1BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAs1B7B,0CAAe;AAr1BnB,KAAK;AACL,IAAM,aAAa,GAAG,IAAI,CAAA;AAq1BtB,sCAAa;AAp1BjB,MAAM;AACN,IAAM,eAAe,GAAG,IAAI,CAAA;AAo1BxB,0CAAe;AAn1BnB,MAAM;AACN,IAAM,eAAe,GAAG,IAAI,CAAA;AAm1BxB,0CAAe;AAl1BnB,SAAS;AACT,IAAM,cAAc,GAAG,IAAI,CAAA;AAk1BvB,wCAAc;AAj1BlB,OAAO;AACP,IAAM,cAAc,GAAG,IAAI,CAAA;AAi1BvB,wCAAc;AAh1BlB,SAAS;AACT,IAAM,iBAAiB,GAAG,IAAI,CAAA;AAg1B1B,8CAAiB;AA/0BrB,SAAS;AACT,IAAM,mBAAmB,GAAG,IAAI,CAAA;AA+0B5B,kDAAmB;AA90BvB,SAAS;AACT,IAAM,kBAAkB,GAAG,IAAI,CAAA;AA80B3B,gDAAkB;AA70BtB,UAAU;AACV,IAAM,iBAAiB,GAAG,IAAI,CAAA;AA60B1B,8CAAiB;AA50BrB,SAAS;AACT,IAAM,gBAAgB,GAAG,IAAI,CAAA;AA40BzB,4CAAgB;AA30BpB,WAAW;AACX,IAAM,gBAAgB,GAAG,IAAI,CAAA;AA20BzB,4CAAgB;AA10BpB,SAAS;AACT,IAAM,qBAAqB,GAAG,IAAI,CAAA;AA00B9B,sDAAqB;AAz0BzB,SAAS;AACT,IAAM,eAAe,GAAG,IAAI,CAAA;AAy0BxB,0CAAe;AAx0BnB,WAAW;AACX,IAAM,qBAAqB,GAAG,IAAI,CAAA;AAw0B9B,sDAAqB;AAv0BzB,UAAU;AACV,IAAM,kBAAkB,GAAG,IAAI,CAAA;AAu0B3B,gDAAkB;AAt0BtB,SAAS;AACT,IAAM,kBAAkB,GAAG,IAAI,CAAA;AAs0B3B,gDAAkB;AAr0BtB,OAAO;AACP,IAAM,cAAc,GAAG,IAAI,CAAA;AA8yBvB,wCAAc;AA7yBlB,SAAS;AACT,IAAM,cAAc,GAAG,IAAI,CAAA;AAm0BvB,wCAAc;AAl0BlB,SAAS;AACT,IAAM,eAAe,GAAG,IAAI,CAAA;AAk0BxB,0CAAe;AAh0BnB,QAAQ;AACR,IAAM,gBAAgB,GAAG,IAAI,CAAA;AAg0BzB,4CAAgB;AA/zBpB,QAAQ;AACR,IAAM,aAAa,GAAG,IAAI,CAAA;AA+zBtB,sCAAa;AA7zBjB,SAAS;AACT,IAAM,eAAe,GAAG,GAAG,CAAA;AA+zBvB,0CAAe;AA9zBnB,OAAO;AACP,IAAM,YAAY,GAAG,IAAI,CAAA;AA2zBrB,oCAAY;AA1zBhB,OAAO;AACP,IAAM,cAAc,GAAG,GAAG,CAAA;AA0zBtB,wCAAc;AAzzBlB,SAAS;AACT,IAAM,sBAAsB,GAAG,CAAC,CAAA;AA0zB5B,wDAAsB;AAxzB1B,eAAe;AACf,IAAM,eAAe,GAAG,EAAE,CAAA;AAwzBtB,0CAAe;AAvzBnB,eAAe;AACf,IAAM,kBAAkB,GAAG,EAAE,CAAA;AAuzBzB,gDAAkB;AAtzBtB,aAAa;AACb,IAAM,oBAAoB,GAAG,GAAG,CAAA;AAszB5B,oDAAoB;AApzBxB,WAAW;AACX,IAAM,mBAAmB,GAAG,CAAC,CAAA;AAozBzB,kDAAmB;AAnzBvB,eAAe;AACf,IAAM,kBAAkB,GAAG,CAAC,CAAA;AAmzBxB,gDAAkB;AAjzBtB,GAAG;AACH,IAAM,iBAAiB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAC,WAAW;AAizB7C,8CAAiB;AAhzBrB,IAAM,iBAAiB,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA,CAAC,SAAS;AAizB7C,8CAAiB;AAhzBrB,IAAM,kBAAkB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAC,WAAW;AAizB9C,gDAAkB;AAhzBtB,IAAM,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAC,MAAM;AAizBtC,0CAAe;AAhzBnB,IAAM,sBAAsB,GAAG,CAAC,CAAA,CAAC,eAAe;AAizB5C,wDAAsB;AAhzB1B,IAAM,0BAA0B,GAAG,EAAE,CAAA,CAAC,UAAU;AAizB5C,gEAA0B;AA/yB9B,SAAS;AACT,IAAM,kBAAkB,GAAG,CAAC,CAAA;AA+yBxB,gDAAkB;AA7yBtB,SAAS;AACT,IAAM,qBAAqB,GAAG;IAC1B,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;CACT,CAAA;AAuyBG,sDAAqB;AAryBzB,YAAY;AACZ,IAAM,YAAY,GAAG,GAAG,CAAA;AAqyBpB,oCAAY;AAnyBhB,eAAe;AACf,IAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAA;AAmyB5B,0CAAe;AAjyBnB,QAAQ;AACR,IAAM,YAAY,GAAG;IACjB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;CACnC,CAAA;AAgxBG,oCAAY;AA9wBhB,gBAAgB;AAChB,IAAM,mBAAmB,GAAG;IACxB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;CACzB,CAAA;AA6vBG,kDAAmB;AA3vBvB,QAAQ;AACR,IAAM,gBAAgB,GAAG;IACrB,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC3C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAC3C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;IAC3C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;CAChD,CAAA;AAkvBG,4CAAgB;AAhvBpB,UAAU;AACV,IAAM,eAAe,GAAG;IACpB,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC9C,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;IACjD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAChD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;IAClD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;IACjD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;IAClD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IACjD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;IACnD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;CACrD,CAAA;AAsuBG,0CAAe;AApuBnB,YAAY;AACZ,IAAM,oBAAoB,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAouBpD,oDAAoB;AAluBxB,SAAS;AACT,IAAM,mBAAmB,GAAG,GAAG,CAAA;AAmuB3B,kDAAmB;AAjuBvB,aAAa;AACb,IAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;AA+tBjD,0CAAe;AA7tBnB,WAAW;AACX,IAAM,gBAAgB,GAAG;IACrB,QAAQ,EAAE,aAAK,CAAC,MAAM;IACtB,QAAQ,EAAE,aAAK,CAAC,MAAM;IACtB,OAAO,EAAE,aAAK,CAAC,KAAK;CACvB,CAAA;AA0tBG,4CAAgB;AAxtBpB,cAAc;AACd,IAAM,cAAc;IAChB,GAAC,aAAK,CAAC,KAAK,IAAG,kBAAkB;IACjC,GAAC,aAAK,CAAC,SAAS,IAAG,gBAAgB;IACnC,GAAC,aAAK,CAAC,QAAQ,IAAG,eAAe;IACjC,GAAC,aAAK,CAAC,UAAU,IAAG,iBAAiB;OACxC,CAAA;AAmtBG,wCAAc;AAjtBlB,cAAc;AACd,IAAM,UAAU;IACZ,GAAC,aAAK,CAAC,MAAM,IAAG,QAAQ;IACxB,GAAC,aAAK,CAAC,MAAM,IAAG,QAAQ;IACxB,GAAC,aAAK,CAAC,KAAK,IAAG,OAAO;IACtB,GAAC,aAAK,CAAC,IAAI,IAAG,MAAM;IACpB,GAAC,aAAK,CAAC,KAAK,IAAG,OAAO;IACtB,GAAC,aAAK,CAAC,SAAS,IAAG,WAAW;IAC9B,GAAC,aAAK,CAAC,QAAQ,IAAG,UAAU;IAC5B,GAAC,aAAK,CAAC,QAAQ,IAAG,UAAU;IAC5B,GAAC,aAAK,CAAC,IAAI,IAAG,MAAM;IACpB,GAAC,aAAK,CAAC,SAAS,IAAG,WAAW;IAC9B,GAAC,aAAK,CAAC,OAAO,IAAG,SAAS;IAC1B,GAAC,aAAK,CAAC,QAAQ,IAAG,UAAU;IAC5B,GAAC,aAAK,CAAC,UAAU,IAAG,YAAY;IAChC,GAAC,aAAK,CAAC,OAAO,IAAG,SAAS;IAC1B,GAAC,aAAK,CAAC,SAAS,IAAG,WAAW;OACjC,CAAA;AAisBG,gCAAU;AA/rBd,YAAY;AACZ,IAAM,UAAU;IACZ,GAAC,aAAK,CAAC,MAAM,IAAG,WAAW;IAC3B,GAAC,aAAK,CAAC,MAAM,IAAG,WAAW;IAC3B,GAAC,aAAK,CAAC,KAAK,IAAG,UAAU;IACzB,GAAC,aAAK,CAAC,IAAI,IAAG,SAAS;IACvB,GAAC,aAAK,CAAC,KAAK,IAAG,UAAU;IACzB,GAAC,aAAK,CAAC,SAAS,IAAG,cAAc;IACjC,GAAC,aAAK,CAAC,QAAQ,IAAG,aAAa;IAC/B,GAAC,aAAK,CAAC,QAAQ,IAAG,aAAa;IAC/B,GAAC,aAAK,CAAC,IAAI,IAAG,SAAS;IACvB,GAAC,aAAK,CAAC,SAAS,IAAG,cAAc;IACjC,GAAC,aAAK,CAAC,OAAO,IAAG,YAAY;IAC7B,GAAC,aAAK,CAAC,QAAQ,IAAG,aAAa;OAClC,CAAA;AAkrBG,gCAAU;AAhrBd,SAAS;AACT,IAAM,sBAAsB;IACxB,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACvC,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC1C,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC5C,GAAC,eAAO,CAAC,KAAK,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACjD,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC5C,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC7C,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC1C,GAAC,eAAO,CAAC,OAAO,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACtC,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACzC,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACzC,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACtD,GAAC,eAAO,CAAC,MAAM,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACrC,GAAC,eAAO,CAAC,MAAM,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAClD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACtD,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAC1D,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACzD,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAC1D,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACvD,GAAC,eAAO,CAAC,eAAe,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC9C,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC5C,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC1C,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACvC,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACvD,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACzD,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACvD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACtD,GAAC,eAAO,CAAC,qBAAqB,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACjE,GAAC,eAAO,CAAC,gBAAgB,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC/C,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAC1D,GAAC,eAAO,CAAC,OAAO,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;OACtD,CAAA;AA6oBG,wDAAsB;AA3oB1B,WAAW;AACX,IAAM,qBAAqB;IACvB,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;IACpC,GAAC,qBAAa,CAAC,UAAU,IAAG,SAAS;IACrC,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;OACvC,CAAA;AAuoBG,sDAAqB;AAroBzB,WAAW;AACX,IAAM,qBAAqB;IACvB,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;IACpC,GAAC,qBAAa,CAAC,UAAU,IAAG,SAAS;IACrC,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;OACvC,CAAA;AAioBG,sDAAqB;AA/nBzB,SAAS;AACT,IAAM,gBAAgB;IAClB,GAAC,iBAAS,CAAC,IAAI,IAAG,SAAS;IAC3B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,MAAM,IAAG,SAAS;IAC7B,GAAC,iBAAS,CAAC,MAAM,IAAG,SAAS;OAChC,CAAA;AAunBG,4CAAgB;AArnBpB,SAAS;AACT,IAAM,gBAAgB;IAClB,GAAC,qBAAa,CAAC,IAAI,IAAG,SAAS;IAC/B,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;IACpC,GAAC,qBAAa,CAAC,IAAI,IAAG,SAAS;OAClC,CAAA;AAinBG,4CAAgB;AA/mBpB,IAAM,YAAY,GAAG;IACjB,IAAI,EAAE,SAAS;CAClB,CAAA;AAgqBG,oCAAY;AA9pBhB,YAAY;AACZ,IAAM,qBAAqB,GAAG;IAC1B,CAAC,EAAE,CAAC,OAAO,CAAC;IACZ,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,CAAC;IACZ,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,CAAC;IACZ,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;CACzB,CAAA;AAkmBG,sDAAqB;AAhmBzB,OAAO;AACP,IAAM,aAAa,GAAG;IAClB,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IACzB,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;IACxB,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;IACtB,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;IACxB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;CAC5B,CAAA;AA0lBG,sCAAa;AAxlBjB,UAAU;AACV,IAAM,oBAAoB,GAAG;IACzB,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE,qBAAqB;CAC9B,CAAA;AA2kBG,oDAAoB;AA1kBxB,IAAM,uBAAuB,GAAG,CAAC,CAAA,CAAC,WAAW;AA2kBzC,0DAAuB;AAzkB3B,SAAS;AACT,IAAM,eAAe,GAAG;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;CACvB,CAAA;AAgkBG,0CAAe;AA9jBnB,gBAAgB;AAChB,IAAM,eAAe,GAAG,EAAE,CAAA;AA8jBtB,0CAAe;AA7jBnB,IAAM,gBAAgB,GAAG,EAAE,CAAA,CAAC,YAAY;AA8jBpC,4CAAgB;AA7jBpB,IAAM,eAAe,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAA,CAAC,cAAc;AA8jBnD,0CAAe;AA5jBnB,WAAW;AACX,IAAM,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AA4jBvC,4CAAgB;AA1jBpB,SAAS;AACT,IAAM,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AA0jBxD,0CAAe;AAzjBnB,IAAM,uBAAuB,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAA;AA0jBlD,0DAAuB;AAzjB3B,WAAW;AACX,IAAM,mBAAmB,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AAyjBhC,kDAAmB;AAvjBvB,SAAS;AACT,IAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AAujB/B,wCAAc;AArjBlB,eAAe;AACf,IAAM,qBAAqB,GAAG,EAAE,CAAA;AAqjB5B,sDAAqB;AAnjBzB,aAAa;AACb,IAAM,kBAAkB,GAAG,OAAO,GAAG,EAAE,CAAA;AAmjBnC,gDAAkB;AAjjBtB,SAAS;AACT,IAAM,gBAAgB,GAAG,4BAA4B,CAAA;AAijBjD,4CAAgB;AAhjBpB,IAAM,gBAAgB,GAAG,GAAG,CAAA;AAijBxB,4CAAgB;AA/iBpB,kBAAkB;AAClB,IAAM,mCAAmC,GAAG,QAAQ,GAAG,EAAE,CAAA;AA+iBrD,kFAAmC;AA7iBvC,iBAAiB;AACjB,IAAM,gBAAgB;IAClB,GAAC,gBAAQ,CAAC,MAAM,IAAG,CAAC;IACpB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,CAAC;IAC/B,GAAC,gBAAQ,CAAC,eAAe,IAAG,CAAC;IAC7B,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,CAAC;IACjC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,CAAC;IACjC,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,CAAC;IAC/B,GAAC,gBAAQ,CAAC,KAAK,IAAG,CAAC;IACnB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,CAAC;IAC9B,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,CAAC;IAC9B,GAAC,gBAAQ,CAAC,oBAAoB,IAAG,CAAC;IAClC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,CAAC;IACjC,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,YAAY,IAAG,CAAC;IAC1B,GAAC,gBAAQ,CAAC,UAAU,IAAG,CAAC;IACxB,GAAC,gBAAQ,CAAC,OAAO,IAAG,CAAC;IACrB,GAAC,gBAAQ,CAAC,KAAK,IAAG,CAAC;IACnB,GAAC,gBAAQ,CAAC,aAAa,IAAG,CAAC;IAC3B,GAAC,gBAAQ,CAAC,cAAc,IAAG,CAAC;IAC5B,GAAC,gBAAQ,CAAC,cAAc,IAAG,CAAC;OAC/B,CAAA;AAwhBG,4CAAgB;AAthBpB,cAAc;AACd,IAAM,cAAc;IAChB,GAAC,gBAAQ,CAAC,MAAM,IAAG,IAAI;IACvB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,KAAK,IAAG,IAAI;IACtB,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,oBAAoB,IAAG,IAAI;IACrC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,YAAY,IAAG,IAAI;IAC7B,GAAC,gBAAQ,CAAC,UAAU,IAAG,IAAI;IAC3B,GAAC,gBAAQ,CAAC,OAAO,IAAG,IAAI;IACxB,GAAC,gBAAQ,CAAC,KAAK,IAAG,IAAI;IACtB,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;IAC/B,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;OAClC,CAAA;AA8fG,wCAAc;AA5flB,cAAc;AACd,IAAM,gBAAgB;IAClB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,OAAO,IAAG,IAAI;IACxB,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;IAC/B,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,KAAK,IAAG,IAAI;IACtB,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,OAAO,IAAG,IAAI;IACxB,GAAC,gBAAQ,CAAC,IAAI,IAAG,IAAI;IACrB,GAAC,gBAAQ,CAAC,QAAQ,IAAG,IAAI;IACzB,GAAC,gBAAQ,CAAC,WAAW,IAAG,IAAI;IAC5B,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;IAC/B,GAAC,gBAAQ,CAAC,QAAQ,IAAG,IAAI;IACzB,GAAC,gBAAQ,CAAC,QAAQ,IAAG,IAAI;IACzB,GAAC,gBAAQ,CAAC,IAAI,IAAG,IAAI;OACxB,CAAA;AAyeG,4CAAgB;AAvepB,cAAc;AACd,IAAM,mBAAmB;IACrB,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,gBAAQ,CAAC,gBAAgB;IACtD,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,gBAAQ,CAAC,gBAAgB;IACtD,GAAC,gBAAQ,CAAC,aAAa,IAAG,gBAAQ,CAAC,eAAe;IAClD,GAAC,gBAAQ,CAAC,cAAc,IAAG,gBAAQ,CAAC,OAAO;IAC3C,GAAC,gBAAQ,CAAC,OAAO,IAAG,gBAAQ,CAAC,OAAO;IACpC,GAAC,gBAAQ,CAAC,WAAW,IAAG,gBAAQ,CAAC,OAAO;IACxC,GAAC,gBAAQ,CAAC,cAAc,IAAG,gBAAQ,CAAC,QAAQ;IAC5C,GAAC,gBAAQ,CAAC,QAAQ,IAAG,gBAAQ,CAAC,QAAQ;OACzC,CAAA;AAwdG,kDAAmB;AAtdvB,SAAS;AACT,IAAM,WAAW;IACb,GAAC,gBAAQ,CAAC,MAAM,IAAG,IAAI;IACvB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,YAAY,IAAG,IAAI;IAC7B,GAAC,gBAAQ,CAAC,WAAW,IAAG,IAAI;IAC5B,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,yBAAyB,IAAG,IAAI;IAC1C,GAAC,gBAAQ,CAAC,kBAAkB,IAAG,IAAI;IACnC,GAAC,gBAAQ,CAAC,uBAAuB,IAAG,IAAI;OAC3C,CAAA;AAscG,kCAAW;AApcf,SAAS;AACT,kDAAkD;AAClD,IAAM,kBAAkB,GAAG;IACvB,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClB,gBAAgB,EAAE,CAAC,QAAQ,CAAC;IAC5B,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC3B,cAAc,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC3B,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtB,MAAM,EAAE,CAAC,MAAM,CAAC;CACnB,CAAA;AA0bG,gDAAkB;AAxbtB,SAAS;AACT,IAAM,kBAAkB,GAAG;IACvB,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;CACf,CAAA;AAubG,gDAAkB;AArbtB,cAAc;AACd,IAAM,0BAA0B,GAAG,EAAE,CAAA;AAqbjC,gEAA0B;AApb9B,aAAa;AACb,IAAM,yBAAyB,GAAG,GAAG,CAAA;AAobjC,8DAAyB;AAnb7B,SAAS;AACT,IAAM,2BAA2B,GAAG;IAChC,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;CACP,CAAA;AA+aG,kEAA2B;AA9a/B,cAAc;AACd,IAAM,eAAe;IACjB,GAAC,aAAK,CAAC,MAAM,IAAG,CAAC;IACjB,GAAC,aAAK,CAAC,MAAM,IAAG,CAAC;IACjB,GAAC,aAAK,CAAC,KAAK,IAAG,CAAC;IAChB,GAAC,aAAK,CAAC,QAAQ,IAAG,GAAG;IACrB,GAAC,aAAK,CAAC,IAAI,IAAG,GAAG;IACjB,GAAC,aAAK,CAAC,SAAS,IAAG,GAAG;IACtB,GAAC,aAAK,CAAC,OAAO,IAAG,GAAG;OACvB,CAAA;AAsaG,0CAAe;AApanB,SAAS;AACT,IAAM,mBAAmB,GAAG,KAAK,CAAA;AAoa7B,kDAAmB;AAlavB,QAAQ;AACR,IAAM,kBAAkB,GAAG;IACvB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;IAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;IAC5B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;IAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;IAC3B,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,kBAAkB,EAAE;IACzC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;IAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE;CACrC,CAAA;AAwZG,gDAAkB;AAtZtB,WAAW;AACX,IAAM,cAAc,GAAG,QAAQ,GAAG,CAAC,CAAA;AAsZ/B,wCAAc;AApZlB,SAAS;AACT,IAAM,aAAa,GAAG;IAClB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACT,EAAE,EAAE,CAAC,CAAC,CAAC;CACV,CAAA;AA+YG,sCAAa;AA7YjB,OAAO;AACP,IAAM,cAAc,GAAG;IACnB,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,EAAE,EAAE,EAAE;CACT,CAAA;AAwYG,wCAAc;AAtYlB,YAAY;AACZ,IAAM,4BAA4B,GAAG,QAAQ,GAAG,CAAC,CAAA;AAsY7C,oEAA4B;AArYhC,IAAM,4BAA4B,GAAG,GAAG,CAAA;AAsYpC,oEAA4B;AApYhC,gBAAgB;AAChB,IAAM,qBAAqB,GAAG,CAAC,CAAA;AAoY3B,sDAAqB;AAlYzB,SAAS;AACT,IAAM,eAAe,GAAG;IACpB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;CACzB,CAAA;AA4XG,0CAAe;AA1XnB,YAAY;AACZ,IAAM,iBAAiB,GAAG,OAAO,GAAG,EAAE,CAAA;AA0XlC,8CAAiB;AAxXrB,eAAe;AACf,IAAM,0BAA0B,GAAG,CAAC,CAAA;AAwXhC,gEAA0B;AAtX9B,aAAa;AACb,IAAM,sBAAsB,GAAG,GAAG,CAAA;AAsX9B,wDAAsB;AApX1B,eAAe;AACf,IAAM,sBAAsB,GAAG,KAAK,GAAG,CAAC,CAAA;AAoXpC,wDAAsB;AAlX1B,eAAe;AACf,IAAM,sBAAsB,GAAG,GAAG,CAAA;AAkX9B,wDAAsB;AAhX1B,SAAS;AACT,IAAM,oBAAoB,GAAG,EAAE,CAAA;AAgX3B,oDAAoB;AA9WxB,WAAW;AACX,IAAM,0BAA0B,GAAG,CAAC,CAAA;AA8WhC,gEAA0B;AA5W9B,WAAW;AACX,IAAM,gCAAgC,GAAG,GAAG,CAAA;AA4WxC,4EAAgC;AA1WpC,aAAa;AACb,IAAM,mBAAmB,GAAG,EAAE,CAAA;AA0W1B,kDAAmB;AAxWvB,YAAY;AACZ,IAAM,oBAAoB,GAAG,CAAC,GAAG,QAAQ,CAAA;AAwWrC,oDAAoB;AAtWxB,WAAW;AACX,IAAM,oBAAoB,GAAG,EAAE,CAAA;AAsW3B,oDAAoB;AArWxB,SAAS;AACT,IAAM,gBAAgB,GAAG,CAAC,GAAG,KAAK,CAAA;AAqW9B,4CAAgB;AAnWpB,OAAO;AACP,IAAM,cAAc,GAAW,EAAE,CAAA;AAoW7B,wCAAc;AAnWlB,IAAM,kBAAkB,GAAW,IAAI,CAAA,CAAC,gBAAgB;AAoWpD,gDAAkB;AAnWtB,IAAM,uBAAuB,GAAW,CAAC,CAAA,CAAC,eAAe;AAoWrD,0DAAuB;AAnW3B,IAAM,kBAAkB,GAAW,KAAK,CAAA,CAAC,YAAY;AAoWjD,gDAAkB;AAnWtB,IAAM,yBAAyB,GAAW,KAAK,GAAG,EAAE,CAAA,CAAC,QAAQ;AAoWzD,8DAAyB;AAlW7B,SAAS;AACT,IAAM,iBAAiB,GAAG,EAAE,CAAA;AAkWxB,8CAAiB;AAjWrB,IAAM,qBAAqB,GAAG,EAAE,CAAA;AAkW5B,sDAAqB;AAhWzB,aAAa;AACb,IAAM,sBAAsB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA;AAgWxC,wDAAsB;AA/V1B,YAAY;AACZ,IAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAA;AA+V1B,sCAAa;AA7VjB,UAAU;AACV,IAAM,kBAAkB,GAAG,EAAE,CAAA;AA6VzB,gDAAkB;AA5VtB,YAAY;AACZ,IAAM,uBAAuB,GAAG,GAAG,CAAA;AA4V/B,0DAAuB;AA1V3B,cAAc;AACd,IAAM,yBAAyB,GAAG,CAAC,CAAA;AA0V/B,8DAAyB;AAzV7B,SAAS;AACT,IAAM,gCAAgC,GAAG,EAAE,CAAA;AAyVvC,4EAAgC;AAxVpC,IAAM,2BAA2B,GAAG,GAAG,CAAA;AAyVnC,kEAA2B;AAvV/B,cAAc;AACd,IAAM,iBAAiB,GAAG,GAAG,CAAA;AAuVzB,8CAAiB;AArVrB,SAAS;AACT,IAAM,aAAa,GAAG;IAClB,aAAa;IACb,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3B,8BAA8B;IAC9B,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3D,SAAS;IACT,+DAA+D;IAC/D,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;CAC9H,CAAA;AA6UG,sCAAa;AA3UjB,SAAS;AACT,IAAM,iBAAiB,GAAG,OAAO,GAAG,CAAC,CAAA;AA2UjC,8CAAiB;AAzUrB,aAAa;AACb,IAAM,iBAAiB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAyUjC,8CAAiB;AAvUrB,cAAc;AACd,IAAM,SAAS,GAAG;IACd,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,EAAE;CACR,CAAA;AAgUG,8BAAS;AA9Tb,UAAU;AACV,IAAM,cAAc,GAAG,IAAI,CAAA;AA8TvB,wCAAc;AA7TlB,IAAI;AACJ,IAAM,aAAa,GAAG,IAAI,CAAA;AA6TtB,sCAAa;AA5TjB,IAAI;AACJ,IAAM,YAAY,GAAG,IAAI,CAAA;AA4TrB,oCAAY;AA1ThB,KAAK;AACL,IAAM,OAAO;IACT,GAAC,aAAK,CAAC,MAAM,IAAG,IAAI;IACpB,GAAC,aAAK,CAAC,MAAM,IAAG,IAAI;IACpB,GAAC,aAAK,CAAC,KAAK,IAAG,IAAI;IACnB,GAAC,aAAK,CAAC,QAAQ,IAAG,IAAI;IACtB,GAAC,aAAK,CAAC,QAAQ,IAAG,IAAI;IACtB,GAAC,aAAK,CAAC,IAAI,IAAG,IAAI;IAClB,GAAC,aAAK,CAAC,SAAS,IAAG,IAAI;IACvB,GAAC,aAAK,CAAC,OAAO,IAAG,IAAI;OACxB,CAAA;AAiTG,0BAAO;AA/SX,WAAW;AACX,IAAM,kBAAkB,GAAG,EAAE,CAAA;AA+SzB,gDAAkB;AA7StB,WAAW;AACX,IAAM,oBAAoB,GAAG,CAAC,CAAA;AA6S1B,oDAAoB;AA3SxB,YAAY;AACZ,IAAM,sBAAsB;IACxB,GAAC,qBAAa,CAAC,IAAI,IAAG,MAAM;IAC5B,GAAC,qBAAa,CAAC,MAAM,IAAG,MAAM;IAC9B,GAAC,qBAAa,CAAC,MAAM,IAAG,MAAM;IAC9B,GAAC,qBAAa,CAAC,UAAU,IAAG,UAAU;OACzC,CAAA;AAsSG,wDAAsB;AApS1B,KAAK;AACL,IAAM,UAAU,GAAG,EAAE,CAAA;AAoSjB,gCAAU;AAnSd,SAAS;AACT,IAAM,SAAS,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AAmS5B,8BAAS;AAlSb,eAAe;AACf,IAAM,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,EAAE,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA;AAkSzE,wCAAc;AA/RlB,YAAY;AACZ,IAAM,0BAA0B,GAAG;IAC/B,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;IAC1B,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE;IAC5B,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;IAC9B,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;CACnC,CAAA;AA0RG,gEAA0B;AAxR9B,UAAU;AACV,IAAM,kBAAkB,GAAG;IACvB,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;CACvC,CAAA;AAiRG,gDAAkB;AA/QtB,UAAU;AACV,IAAM,iBAAiB,GAAG;IACtB,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;CACf,CAAA;AAwQG,8CAAiB;AAtQrB,SAAS;AACT,IAAM,oBAAoB,GAAG,aAAa,CAAA,CAAC,QAAQ;AAsQ/C,oDAAoB;AApQxB,WAAW;AACX,IAAM,wBAAwB,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA,CAAC,cAAc;AAoQrD,4DAAwB;AAlQ5B,WAAW;AACX,IAAM,iBAAiB,GAAG,QAAQ,CAAA;AAkQ9B,8CAAiB;AAhQrB,WAAW;AACX,IAAM,mBAAmB,GAAG,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAA;AAgQhE,kDAAmB;AA9PvB,gBAAgB;AAChB,IAAM,oBAAoB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA,CAAC,OAAO;AA8PpD,oDAAoB;AA5PxB,SAAS;AACT,IAAM,sBAAsB,GAAG,EAAE,CAAA;AA4P7B,wDAAsB;AA3P1B,WAAW;AACX,IAAM,oBAAoB,GAAG,CAAC,CAAA;AA2P1B,oDAAoB;AA1PxB,YAAY;AACZ,IAAM,kBAAkB,GAAG,CAAC,CAAA;AA0PxB,gDAAkB;AAxPtB,SAAS;AACT,IAAM,mBAAmB,GAAG,GAAG,CAAA;AAwP3B,kDAAmB;AAtPvB,eAAe;AACf,IAAM,kBAAkB,GAAG;IACvB,CAAC,EAAE,IAAI;IACP,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;CACZ,CAAA;AA+OG,gDAAkB;AA7OtB,UAAU;AACV,IAAM,wBAAwB,GAAG,KAAK,CAAA;AA6OlC,4DAAwB;AA3O5B,YAAY;AACZ,IAAM,iBAAiB;IACnB,GAAC,iBAAS,CAAC,MAAM,IAAG,uBAAe,CAAC,MAAM;IAC1C,GAAC,iBAAS,CAAC,IAAI,IAAG,uBAAe,CAAC,IAAI;IACtC,GAAC,iBAAS,CAAC,KAAK,IAAG,uBAAe,CAAC,KAAK;IACxC,GAAC,iBAAS,CAAC,SAAS,IAAG,uBAAe,CAAC,KAAK;OAC/C,CAAA;AAsOG,8CAAiB;AApOrB,WAAW;AACX,IAAM,0BAA0B,GAAG,CAAC,CAAA;AAoOhC,gEAA0B;AAlO9B,aAAa;AACb,IAAM,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;AAkOzC,8CAAiB;AAhOrB,SAAS;AACT,IAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAA;AAgOzB,8CAAiB;AA9NrB,UAAU;AACV,IAAM,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;AA8NhE,0CAAe;AA5NnB,EAAE;AACF,IAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;AA4NpE,kDAAmB;AA1NvB,WAAW;AACX,IAAM,kBAAkB,GAAG;IACvB,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;IACD,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;IACD,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;IACD,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;CACJ,CAAA;AAyLG,gDAAkB;AAvLtB,SAAS;AACT,IAAM,mBAAmB,GAAG;IACxB,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;CACf,CAAA;AAiLG,kDAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["/////////////// 所有常量（全大写单词间用下划线隔开）///////////////\n\nimport { ArmyState, BookCommentType, BuffType, CEffect, CType, LobbyModeType, MailStateType, MarchLineType, StudyType } from \"./Enums\"\n\n// 点击间隔\nconst CLICK_SPACE = 10\n// 一格的大小\nconst TILE_SIZE = 80\n// 地图边界额外宽度\nconst MAP_EXTRA_SIZE = 2\n\n// 地图显示偏移\nconst MAP_SHOW_OFFSET = cc.v2(TILE_SIZE * 8, TILE_SIZE * 8)\n\n//\nconst TILE_SIZE_HALF = cc.v2(TILE_SIZE * 0.5, TILE_SIZE * 0.5)\n\n// 设施拖拽时候的高\nconst BUILD_DRAG_OFFSETY = TILE_SIZE_HALF.y - 24\n\n// 区域最高y坐标\nconst AREA_MAX_ZINDEX = 21 * TILE_SIZE\n// 层级最大值\nconst MAX_ZINDEX = 10000\n\n// 长按时间\nconst LONG_PRESS_TIME = 0.4\n\n// 延迟关闭pnl时间\nconst DELAY_CLOSE_PNL_TIME = 0.4\n\n// 主城id\nconst CITY_MAIN_NID = 1001\n// 要塞id\nconst CITY_FORT_NID = 2102\n// 4个遗迹\nconst ANCIENT_WALL_ID = 3000 //城墙\nconst CITY_CHANGAN_ID = 3001 //长安\nconst CITY_JINLING_ID = 3002 //金陵\nconst CITY_YANJING_ID = 3003 //燕京\nconst CITY_LUOYANG_ID = 3004 //洛阳\n// 农场\nconst BUILD_FARM_ID = 2201\n// 伐木场\nconst BUILD_TIMBER_ID = 2202\n// 采石场\nconst BUILD_QUARRY_ID = 2203\n// 城墙建筑id\nconst BUILD_WALL_NID = 2000\n// 主城id\nconst BUILD_MAIN_NID = 2001\n// 仓库建筑id\nconst BUILD_GRANARY_NID = 2002\n// 粮仓建筑id\nconst BUILD_WAREHOUSE_NID = 2003\n// 兵营建筑id\nconst BUILD_BARRACKS_NID = 2004\n// 大使馆建筑id\nconst BUILD_EMBASSY_NID = 2005\n// 市场建筑id\nconst BUILD_BAZAAR_NID = 2006\n// 铁匠铺所建筑id\nconst BUILD_SMITHY_NID = 2008\n// 校场建筑id\nconst BUILD_DRILLGROUND_NID = 2011\n// 工厂建筑id\nconst BUILD_PLANT_NID = 2010\n// 联盟市场建筑id\nconst BUILD_ALLI_BAZAAR_NID = 2014\n// 英雄殿建筑id\nconst BUILD_HEROHALL_NID = 2015\n// 医馆建筑id\nconst BUILD_HOSPITAL_NID = 2016\n// 旗子id\nconst BUILD_FLAG_NID = 2101\n// 要塞建筑id\nconst BUILD_FORT_NID = 2102\n// 箭塔建筑id\nconst BUILD_TOWER_NID = 2103\n\n// 强弩兵ID\nconst PAWN_CROSSBOW_ID = 3305\n// 斧骑兵ID\nconst AX_CAVALRY_ID = 3406\n\n// 初始资源产量\nconst INIT_RES_OUTPUT = 120\n// 初始容量\nconst INIT_RES_CAP = 1000\n// 初始资源\nconst INIT_RES_COUNT = 700\n// 默认修建队列\nconst DEFAULT_BT_QUEUE_COUNT = 2\n\n// 立即完成修建需要的金币数\nconst IN_DONE_BT_GOLD = 30\n// 立即完成打造需要的金币数\nconst IN_DONE_FORGE_GOLD = 30\n// 修改昵称需要的金币数\nconst MODIFY_NICKNAME_GOLD = 500\n\n// 军队最大士兵个数\nconst ARMY_PAWN_MAX_COUNT = 9\n// 大使馆多少级可以创建联盟\nconst CREATE_ALLI_MAX_LV = 3\n\n// \nconst DEFAULT_CITY_SIZE = cc.v2(1, 1) //默认的城市地块大小\nconst DEFAULT_AREA_SIZE = cc.v2(11, 11) //默认的区域大小\nconst DEFAULT_BUILD_SIZE = cc.v2(1, 1) //默认的建筑面积大小\nconst BOSS_BUILD_SIZE = cc.v2(3, 3) //boss\nconst DEFAULT_MAX_ARMY_COUNT = 5 //默认区域的最大容纳军队数量\nconst DEFAULT_MAX_ADD_PAWN_TIMES = 20 //默认最大补兵次数\n\n// 加速行军倍数\nconst UP_MARCH_SPEED_MUL = 3\n\n// 城边加速倍数\nconst MAIN_CITY_MARCH_SPEED = {\n    1: 3.5,\n    2: 3,\n    3: 2.5,\n    4: 2,\n    5: 1.5,\n}\n\n// 运送时间 格/小时\nconst TRANSIT_TIME = 300\n\n// 一场战斗最多持续时间 秒\nconst BATTLE_MAX_TIME = 3600 * 3\n\n// 地块底配置\nconst LAND_DI_CONF = [\n    { list: [0, 0, 0, 0], no: '01' },\n    { list: [0, 1, 1, 0], no: '02' },\n    { list: [0, 1, 1, 1], no: '03' },\n    { list: [0, 0, 1, 1], no: '04' },\n    { list: [1, 1, 1, 0], no: '05' },\n    { list: [1, 1, 1, 1], no: '06' },\n    { list: [1, 0, 1, 1], no: '07' },\n    { list: [1, 1, 0, 0], no: '08' },\n    { list: [1, 1, 0, 1], no: '09' },\n    { list: [1, 0, 0, 1], no: '10' },\n    { list: [0, 0, 1, 0], no: '11' },\n    { list: [1, 0, 1, 0], no: '12' },\n    { list: [1, 0, 0, 0], no: '13' },\n    { list: [0, 1, 0, 0], no: '14' },\n    { list: [0, 1, 0, 1], no: '15' },\n    { list: [0, 0, 0, 1], no: '16' },\n]\n\n// 地块底配置，方向：左上右下\nconst DECORATION_MUD_CONF = {\n    '0011': '101',\n    '1011': '102',\n    '1001': '103',\n    '0111': '104',\n    '1101': '107',\n    '0110': '108',\n    '1110': '109',\n    '1100': '110',\n    '0010': '111',\n    '1010': '112',\n    '1000': '113',\n    '0001': '114',\n    '0101': '115',\n    '0100': '116',\n    '0000': '117',\n    '1111': ['105', '106'],\n}\n\n// 边框线配置\nconst BORDER_LINE_CONF = [\n    { size: cc.size(80, 4), pos: cc.v2(0, 38) }, //上\n    { size: cc.size(4, 80), pos: cc.v2(38, 0) }, //右\n    { size: cc.size(80, 4), pos: cc.v2(0, -38) }, //下\n    { size: cc.size(4, 80), pos: cc.v2(-38, 0) }, //左\n    { size: cc.size(4, 4), pos: cc.v2(-38, 38) }, //左上\n    { size: cc.size(4, 4), pos: cc.v2(38, 38) }, //右上\n    { size: cc.size(4, 4), pos: cc.v2(38, -38) }, //右下\n    { size: cc.size(4, 4), pos: cc.v2(-38, -38) }, //左下\n]\n\n// 河流边框线配置\nconst RIVER_LINE_CONF = {\n    0: { size: cc.size(80, 4), pos: cc.v2(0, 30) }, //上\n    111: { size: cc.size(4, 68), pos: cc.v2(38, -6) }, //右 下短上短\n    112: { size: cc.size(4, 80), pos: cc.v2(38, 0) }, //右 下短上长\n    121: { size: cc.size(4, 80), pos: cc.v2(38, -12) }, //右 下长上短\n    122: { size: cc.size(4, 92), pos: cc.v2(38, -6) }, //右 下长上长\n    311: { size: cc.size(4, 68), pos: cc.v2(-38, -6) }, //左 下短上短\n    312: { size: cc.size(4, 80), pos: cc.v2(-38, 0) }, //左 下短上长\n    321: { size: cc.size(4, 80), pos: cc.v2(-38, -12) }, //左 下长上短\n    322: { size: cc.size(4, 92), pos: cc.v2(-38, -6) }, //左 下长上长\n}\n\n// 选择地块信息框大小\nconst SELECT_CELL_INFO_BOX = cc.rect(320, 320, 272, 320)\n\n// 士兵气泡高度\nconst PAWN_BUBBLE_OFFSETY = 100\n\n// 地块资源配置列表字段\nconst CELL_RES_FIELDS = ['cereal', 'timber', 'stone']\n\n// 资源字段反向映射\nconst RES_FIELDS_CTYPE = {\n    'cereal': CType.CEREAL,\n    'timber': CType.TIMBER,\n    'stone': CType.STONE,\n}\n\n// 通用类型对应图标url\nconst CTYPE_ICON_URL = {\n    [CType.TITLE]: 'icon/title_empty',\n    [CType.WIN_POINT]: 'icon/win_point',\n    [CType.HERO_OPT]: 'icon/hero_opt',\n    [CType.UP_RECRUIT]: 'icon/up_recruit',\n}\n\n// 通用类型对应图标url\nconst CTYPE_ICON = {\n    [CType.CEREAL]: 'cereal',\n    [CType.TIMBER]: 'timber',\n    [CType.STONE]: 'stone',\n    [CType.GOLD]: 'gold',\n    [CType.INGOT]: 'ingot',\n    [CType.WAR_TOKEN]: 'war_token',\n    [CType.EXP_BOOK]: 'exp_book',\n    [CType.CEREAL_C]: 'cereal_c',\n    [CType.IRON]: 'iron',\n    [CType.UP_SCROLL]: 'up_scroll',\n    [CType.FIXATOR]: 'fixator',\n    [CType.BASE_RES]: 'base_res',\n    [CType.BASE_RES_2]: 'base_res_2',\n    [CType.STAMINA]: 'stamina',\n    [CType.RANK_COIN]: 'rank_coin',\n}\n\n// 通用类型对应的名字\nconst CTYPE_NAME = {\n    [CType.CEREAL]: 'ui.cereal',\n    [CType.TIMBER]: 'ui.timber',\n    [CType.STONE]: 'ui.stone',\n    [CType.GOLD]: 'ui.gold',\n    [CType.INGOT]: 'ui.ingot',\n    [CType.WAR_TOKEN]: 'ui.war_token',\n    [CType.EXP_BOOK]: 'ui.exp_book',\n    [CType.CEREAL_C]: 'ui.cereal_c',\n    [CType.IRON]: 'ui.iron',\n    [CType.UP_SCROLL]: 'ui.up_scroll',\n    [CType.FIXATOR]: 'ui.fixator',\n    [CType.BASE_RES]: 'ui.base_res',\n}\n\n// 建筑效果配置\nconst BUILD_EFFECT_TYPE_CONF = {\n    [CEffect.BT_QUEUE]: { vtype: 'number' },\n    [CEffect.BUILD_CD]: { vtype: 'number', suffix: '%' }, //减少建造时间 2\n    [CEffect.GRANARY_CAP]: { vtype: 'number' }, //粮仓容量 3\n    [CEffect.WAREHOUSE_CAP]: { vtype: 'number' }, //仓库容量 4\n    [CEffect.XL_CD]: { vtype: 'number', suffix: '%' }, //减少步兵训练速度 5\n    [CEffect.ALLIANCE_PERS]: { vtype: 'number' }, //联盟人数 6\n    [CEffect.MERCHANT_COUNT]: { vtype: 'number' }, //商人数量 7\n    [CEffect.DRILL_QUEUE]: { vtype: 'number' }, //招募队列 8\n    [CEffect.WALL_HP]: { vtype: 'number' }, //城墙血量 9\n    [CEffect.FORGE_CD]: { vtype: 'number', suffix: '%' }, //打造装备速度 10\n    [CEffect.ARMY_COUNT]: { vtype: 'number' }, //军队数量 11\n    [CEffect.RES_OUTPUT]: { vtype: 'number' }, //资源产量 12\n    [CEffect.MARCH_CD]: { vtype: 'number', suffix: '%' }, //减少行军时间 13\n    [CEffect.UPLVING_CD]: { vtype: 'number', suffix: '%' }, //训练士兵时间 14\n    [CEffect.GW_CAP]: { vtype: 'number' }, //增加粮仓和仓库容量 15\n    [CEffect.XL_2LV]: { vtype: 'number', suffix: '%' }, //有一定几率训练出2级士兵 16\n    [CEffect.TRANSIT_CD]: { vtype: 'number', suffix: '%' }, //每个商人的运输量增加1000 减少商人运送时间 17\n    [CEffect.MAIN_MARCH_MUL]: { vtype: 'number', suffix: '%' }, //主城要塞行军时间加速 18\n    [CEffect.CITY_BUILD_CD]: { vtype: 'number', suffix: '%' }, //地面建筑修建时间减少 19 x\n    [CEffect.TREASURE_AWARD]: { vtype: 'number', suffix: '%' }, //宝箱奖励增加 20\n    [CEffect.FREE_RECAST]: { vtype: 'number', suffix: '%' }, //有一定几率免费重铸装备 21\n    [CEffect.RARE_RES_OUTPUT]: { vtype: 'number' }, //书铁每天增加 22\n    [CEffect.MORE_RARE_RES]: { vtype: 'number' }, //书铁超过100 每天增加 23\n    [CEffect.LV_UP_QUEUE]: { vtype: 'number' }, //训练队列 24\n    [CEffect.TOWER_LV]: { vtype: 'number' }, //哨塔等级 25\n    [CEffect.FARM_OUTPUT]: { vtype: 'number', suffix: '%' }, //农场产量 26\n    [CEffect.QUARRY_OUTPUT]: { vtype: 'number', suffix: '%' }, //采石场产量 27\n    [CEffect.MILL_OUTPUT]: { vtype: 'number', suffix: '%' }, //伐木场产量 28\n    [CEffect.CURE_QUEUE]: { vtype: 'number', suffix: '%' }, //治疗队列 29\n    [CEffect.MARKET_SERVICE_CHARGE]: { vtype: 'number', suffix: '%' }, //置换手续费 30\n    [CEffect.CITY_COUNT_LIMIT]: { vtype: 'number' }, //地面建筑上限 31\n    [CEffect.TOWER_HP]: { vtype: 'number', suffix: '%' }, //哨塔，要塞，城墙的耐久提高 35\n    [CEffect.OTHER_RES_ODDS]: { vtype: 'number', suffix: '%' }, //地块的其他资源掉落概率提高 36\n    [CEffect.CURE_CD]: { vtype: 'number', suffix: '%' }, // 治疗伤兵速度 37\n}\n\n// 行军军队名字颜色\nconst MARCH_ARMY_NAME_COLOR = {\n    [MarchLineType.SELF_ARMY]: '#59A733',\n    [MarchLineType.OTHER_ARMY]: '#C34B3F',\n    [MarchLineType.ALLI_ARMY]: '#4F8FBA',\n}\n\n// 行军军队时间颜色\nconst MARCH_ARMY_TIME_COLOR = {\n    [MarchLineType.SELF_ARMY]: '#FFFFFF',\n    [MarchLineType.OTHER_ARMY]: '#FF9162',\n    [MarchLineType.ALLI_ARMY]: '#7FD6FF',\n}\n\n// 军队状态颜色\nconst ARMY_STATE_COLOR = {\n    [ArmyState.NONE]: '#936E5A',\n    [ArmyState.MARCH]: '#936E5A',\n    [ArmyState.FIGHT]: '#C34B3F',\n    [ArmyState.DRILL]: '#59A733',\n    [ArmyState.LVING]: '#59A733',\n    [ArmyState.CURING]: '#59A733',\n    [ArmyState.TONDEN]: '#59A733',\n}\n\n// 邮件状态颜色\nconst MAIL_STATE_COLOR = {\n    [MailStateType.NONE]: '#C34B3F',\n    [MailStateType.NOT_CLAIM]: '#C34B3F',\n    [MailStateType.READ]: '#A18876',\n}\n\nconst COLOR_NORMAL = {\n    DONE: '#21DC2D'\n}\n\n// 军队记录说明的配置\nconst ARMY_RECORD_DESC_CONF = {\n    0: ['index'], //在{0}发生战斗\n    1: ['index', 'target'], //从{0}移动到{1}\n    2: ['index', 'target'], //从{0}移动到{1}，被撤回\n    3: ['index', 'target'], //在{0}被遣返回{1}\n    4: ['index'], //在{0}被强行解散\n    5: ['index', 'target'], //从{0}移动到{1}，被遣返\n    6: ['index'], //在{0}被强制撤离\n    7: ['index', 'target'], //从{0}移动到{1}，被强制撤离\n}\n\n// 回放倍数\nconst PLAYBACK_MULS = [\n    { val: 4, text: '0.25x' },\n    { val: 2, text: '0.5x' },\n    { val: 1, text: '1x' },\n    { val: 0.5, text: '2x' },\n    { val: 0.25, text: '4x' },\n]\n\n// 固定到菜单配置\nconst FIXATION_MENU_CONFIG = {\n    2001: 'build/BuildMainInfo', //主城\n    2004: 'build/BuildBarracks', //兵营\n    2005: 'build/BuildEmbassy', //联盟\n    2006: 'build/BuildBazaar', //自由市场\n    2008: 'build/BuildSmithy', //铁匠铺\n    2010: 'build/BuildFactory', //工厂\n    2011: 'build/BuildDrillground', //校场\n    2012: 'build/BuildTower', //里亭属\n    2013: 'build/BuildTower', //边塞营\n    2014: 'build/BuildBazaar', //联盟市场\n    2015: 'build/BuildHerohall', //英雄殿\n    2016: 'build/BuildHospital', // 医馆\n}\nconst FIXATION_MENU_MAX_COUNT = 3 //固定到菜单最多个数\n\n// 免费头像列表\nconst FREE_HEAD_ICONS = [\n    'head_icon_free_001',\n    'head_icon_free_002',\n    'head_icon_free_003',\n    'head_icon_free_004',\n    'head_icon_free_005',\n    'head_icon_free_006',\n    'head_icon_free_007',\n    'head_icon_free_008',\n]\n\n// 商城购买添加产量需要的金币\nconst ADD_OUTPUT_GOLD = 50\nconst ADD_OUTPUT_RATIO = 20 //商城购买添加产量比例\nconst ADD_OUTPUT_TIME = 1 * 86400 * 1000 //商城购买添加产量持续时间\n\n// 内政政策槽位配置\nconst POLICY_SLOT_CONF = [3, 5, 10, 15, 20]\n\n// 装备槽位配置\nconst EQUIP_SLOT_CONF = [1, 3, 5, 7, 10, 12, 14, 16, 18, 20]\nconst EQUIP_SLOT_EXCLUSIVE_LV = { 10: true, 18: true }\n// 装备融炼解锁等级\nconst EQUIP_SMELT_NEED_LV = [14, 20]\n\n// 士兵槽位配置\nconst PAWN_SLOT_CONF = [1, 2, 4, 7]\n\n// 研究每重置一次需要的金币\nconst RESET_STUDY_SLOT_GOLD = 50\n\n// 多长时间可以退出联盟\nconst CAN_EXIT_ALLI_TIME = 3600000 * 12\n\n// 创建联盟费用\nconst CREATE_ALLI_COST = '1,0,3000|2,0,2000|3,0,2000'\nconst CREATE_ALLI_COND = 100\n\n// 单个玩家给其他玩家改变人气间隔\nconst ONE_USER_POPULARITY_CHANGE_INTERVAL = 86400000 * 30\n\n// 外显buff 同时显示 层级\nconst BUFF_NODE_ZINDEX = {\n    [BuffType.SHIELD]: 1,\n    [BuffType.PROTECTION_SHIELD]: 1,\n    [BuffType.RODELERO_SHIELD]: 1,\n    [BuffType.RODELERO_SHIELD_001]: 1,\n    [BuffType.RODELERO_SHIELD_102]: 1,\n    [BuffType.ABNEGATION_SHIELD]: 1,\n    [BuffType.PARRY]: 2,\n    [BuffType.PARRY_001]: 2,\n    [BuffType.PARRY_102]: 2,\n    [BuffType.WITHSTAND]: 2,\n    [BuffType.JUMPSLASH_DAMAGE]: 2,\n    [BuffType.BEHEADED_GENERAL]: 2,\n    [BuffType.ANTICIPATION_DEFENSE]: 2,\n    [BuffType.ANTICIPATION_ATTACK]: 2,\n    [BuffType.DIZZINESS]: 3,\n    [BuffType.PARALYSIS]: 3,\n    [BuffType.PARALYSIS_UP]: 3,\n    [BuffType.WIRE_CHAIN]: 3,\n    [BuffType.SILENCE]: 4,\n    [BuffType.CHAOS]: 4,\n    [BuffType.POISONED_WINE]: 4,\n    [BuffType.LIAN_PO_ATTACK]: 5,\n    [BuffType.LIAN_PO_DEFEND]: 5,\n}\n\n// 外显buff 同时显示\nconst NEED_SHOW_BUFF = {\n    [BuffType.SHIELD]: true,\n    [BuffType.PROTECTION_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD_001]: true,\n    [BuffType.RODELERO_SHIELD_102]: true,\n    [BuffType.ABNEGATION_SHIELD]: true,\n    [BuffType.PARRY]: true,\n    [BuffType.PARRY_001]: true,\n    [BuffType.PARRY_102]: true,\n    [BuffType.WITHSTAND]: true,\n    [BuffType.JUMPSLASH_DAMAGE]: true,\n    [BuffType.BEHEADED_GENERAL]: true,\n    [BuffType.ANTICIPATION_DEFENSE]: true,\n    [BuffType.ANTICIPATION_ATTACK]: true,\n    [BuffType.DIZZINESS]: true,\n    [BuffType.PARALYSIS]: true,\n    [BuffType.PARALYSIS_UP]: true,\n    [BuffType.WIRE_CHAIN]: true,\n    [BuffType.SILENCE]: true,\n    [BuffType.CHAOS]: true,\n    [BuffType.POISONED_WINE]: true,\n    [BuffType.LIAN_PO_ATTACK]: true,\n    [BuffType.LIAN_PO_DEFEND]: true,\n}\n\n// 外显buff 互斥显示\nconst NEED_MUTUAL_BUFF = {\n    [BuffType.ARMOR_PENETRATION]: true,\n    [BuffType.INSPIRE]: true,\n    [BuffType.WORTHY_MONARCH]: true,\n    [BuffType.DESTROY_WEAPONS]: true,\n    [BuffType.POISONING_MAX_HP]: true,\n    [BuffType.POISONING_CUR_HP]: true,\n    [BuffType.INFECTION_PLAGUE]: true,\n    [BuffType.BLEED]: true,\n    [BuffType.DAMAGE_INCREASE]: true,\n    [BuffType.DAMAGE_REDUCE]: true,\n    [BuffType.GOD_WAR]: true,\n    [BuffType.FEAR]: true,\n    [BuffType.TIMIDITY]: true,\n    [BuffType.TIGER_MANIA]: true,\n    [BuffType.IRREMOVABILITY]: true,\n    [BuffType.OVERLORD]: true,\n    [BuffType.IGNITION]: true,\n    [BuffType.RAGE]: true,\n}\n\n// 外显buff 类型转换\nconst BUFF_SHOW_TYPE_TRAN = {\n    [BuffType.POISONING_CUR_HP]: BuffType.POISONING_MAX_HP,\n    [BuffType.INFECTION_PLAGUE]: BuffType.POISONING_MAX_HP,\n    [BuffType.DAMAGE_REDUCE]: BuffType.DESTROY_WEAPONS,\n    [BuffType.WORTHY_MONARCH]: BuffType.INSPIRE,\n    [BuffType.GOD_WAR]: BuffType.INSPIRE,\n    [BuffType.TIGER_MANIA]: BuffType.INSPIRE,\n    [BuffType.IRREMOVABILITY]: BuffType.TIMIDITY,\n    [BuffType.OVERLORD]: BuffType.TIMIDITY,\n}\n\n// 护盾buff\nconst SHIELD_BUFF = {\n    [BuffType.SHIELD]: true,\n    [BuffType.PROTECTION_SHIELD]: true,\n    [BuffType.LOW_HP_SHIELD]: true,\n    [BuffType.ATTACK_SHIELD]: true,\n    [BuffType.SUCKBLOOD_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD_001]: true,\n    [BuffType.RODELERO_SHIELD_102]: true,\n    [BuffType.BATTLE_BEGIN_SHIELD]: true,\n    [BuffType.KUROU_SHIELD]: true,\n    [BuffType.SUCK_SHIELD]: true,\n    [BuffType.ABNEGATION_SHIELD]: true,\n    [BuffType.LONGITUDINAL_CLEFT_SHIELD]: true,\n    [BuffType.CRIMSONGOLD_SHIELD]: true,\n    [BuffType.BLACK_IRON_STAFF_SHIELD]: true,\n}\n\n// 战斗特效类型\n// 10000002.攻击 10000003.闪避 10000004.减伤 10000005.护盾\nconst BATTLE_EFFECT_TYPE = {\n    ATTACK: [10000002],\n    DAMAGE_REDUCTION: [10000004],\n    SHIELD: [10000005],\n    VALOR: [10000002, 10000003],\n    WISDOM_COURAGE: [10000002, 10000004],\n    KUROU: [10000002, 10000005],\n    SAND_CLOCK: [10000006],\n    TONDEN: [114001],\n}\n\n// 聊天弹幕颜色\nconst CHAT_BARRAGE_COLOR = {\n    0: '#FFFFFF', //世界\n    1: '#5BB8FF', //联盟\n    2: '#FF81F7', //私聊\n}\n\n// 和大自然交换资源手续费\nconst REPLACEMENT_SERVICE_CHARGE = 60\n// 和大自然交换最少资源\nconst REPLACEMENT_MIN_RES_COUNT = 100\n// 每日置换次数\nconst REPLACEMENT_TODAY_COUNT_MAP = {\n    0: 3,\n    1: 3,\n    2: 3,\n}\n// 各个资源占用运送的容量\nconst RES_TRANSIT_CAP = {\n    [CType.CEREAL]: 1,\n    [CType.TIMBER]: 1,\n    [CType.STONE]: 1,\n    [CType.EXP_BOOK]: 100,\n    [CType.IRON]: 100,\n    [CType.UP_SCROLL]: 500,\n    [CType.FIXATOR]: 500,\n}\n\n// 加速招募倍数\nconst UP_RECRUIT_PAWN_MUL = 0.125\n\n// 多语言名字\nconst LANGUAGE_TEXT_LIST = [\n    { lang: 'en', text: 'ENGLISH' },\n    { lang: 'cn', text: '简体中文' },\n    { lang: 'hk', text: '繁體(港澳)' },\n    { lang: 'tw', text: '繁體(臺灣)' },\n    { lang: 'jp', text: '日本語' },\n    { lang: 'kr', text: '한국어' },\n    { lang: 'idl', text: 'Bahasa Indonesia' }, //印尼\n    { lang: 'th', text: 'ภาษาไทย' }, //泰语\n    { lang: 'vi', text: 'Tiếng Việt' }, //越南\n]\n\n// 注销账号等待时间\nconst LOGOUT_MAX_DAY = 86400000 * 7\n\n// 联盟职位说明\nconst ALLI_JOB_DESC = {\n    0: [1, 2, 3, 4, 5, 7, 8], //盟主\n    1: [3, 4, 5, 7], //副盟主\n    2: [6, 8], //军师\n    10: [0], //成员\n}\n\n// 职位数量\nconst ALLI_JOB_COUNT = {\n    0: 1, //盟主\n    1: 1, //副盟主\n    2: 2, //军师\n    10: 40,\n}\n\n// 开服多久内不可攻占\nconst NOT_OCCUPY_BY_SERVER_RUNTIME = 86400000 * 3\nconst NOT_OCCUPY_BY_MAX_LAND_COUNT = 100\n\n// 不同类型的区最多可玩几个区\nconst CONCURRENT_GAME_LIMIT = 1\n\n// 领地积分配置\nconst LAND_SCORE_CONF = {\n    1: [[50, 2], [100, 1]],\n    2: [[40, 4], [80, 2]],\n    3: [[30, 6], [60, 3]],\n    4: [[20, 8], [40, 4]],\n    5: [[10, 10], [20, 5]],\n}\n\n// 多久才可以删除私聊\nconst REMOVE_PCHAT_TIME = 3600000 * 12\n\n// 攻占玩家领地要求最低距离\nconst OCCUPY_PLAYER_CELL_MIN_DIS = 5\n\n// 多少地可以无限制私聊\nconst NOLIMIT_PCHAT_MAX_LAND = 150\n\n// 聊天 显示时间的最大间隔\nconst SHOW_TIME_MAX_INTERVAL = 60000 * 1\n\n// 可申请添加好友最小地块数\nconst FRIENDS_MIN_LAND_COUNT = 100\n\n// 战斗预测费用\nconst BATTLE_FORECAST_COST = 30\n\n// 战斗预测免费次数\nconst BATTLE_FORECAST_FREE_COUNT = 5\n\n// 一键打开宝箱要求\nconst OPEN_ALL_TREASURE_MIN_LAND_COUNT = 100\n\n// 最小可修改的行军速度\nconst CAN_MIN_MARCH_SPEED = 30\n\n// 一个季节持续的时间\nconst SEASON_DURATION_TIME = 3 * 86400000\n\n// 遗迹加速资源倍数\nconst ANCIENT_SUP_COST_MUL = 40\n// 遗迹加速时间\nconst ANCIENT_SUP_TIME = 6 * 60000\n\n// 聊天相关\nconst CHAT_MAX_COUNT: number = 50\nconst CHAT_SEND_INTERVAL: number = 6000 //发送聊天的预期间隔 (毫秒)\nconst CHAT_TOLERATE_MAX_COUNT: number = 3 //最多容忍多少次在间隔内发送\nconst CHAT_REST_MAX_TIME: number = 30000 //如果太频繁就休息一下\nconst CHAT_BANNED_REST_MAX_TIME: number = 60000 * 10 //禁言休息时间\n\n// 发送喇叭费用\nconst SEND_TRUMPET_COST = 50\nconst SEND_TRUMPET_ACC_COST = 25\n\n// 多长时间可以取消报名\nconst SERVER_APPLY_CANCEL_CD = 1 * 60 * 1000\n// 下次报名的等待时间\nconst NEXT_APPLY_CD = 6 * 1000\n\n// 点将一次的费用\nconst POINTSETS_ONE_COST = 10\n// 点击5次 金币费用\nconst POINTSETS_ONE_GOLD_COST = 598\n\n// 残卷合成画像 需要数量\nconst PORTRAYAL_COMP_NEED_COUNT = 3\n// 还原画像费用\nconst RESTORE_PORTRAYAL_WAR_TOKEN_COST = 50\nconst RESTORE_PORTRAYAL_GOLD_COST = 598\n\n// 购买自选英雄费用 元宝\nconst BUY_OPT_HERO_COST = 999\n\n// 英雄自选礼包\nconst HERO_OPT_GIFT = {\n    // 陈到, 徐盛, 张辽\n    1: [310101, 320101, 340101],\n    // 陈到, 李嗣业, 徐盛, 黄盖, 王异, 张辽, 徐晃\n    2: [310101, 310401, 320101, 320401, 330301, 340101, 340601],\n    // 3: 全自选\n    // 陈到, 张郃, 李嗣业, 文鸯, 徐盛, 曹仁, 张飞, 黄盖, 刘宠, 王异, 曹休, 张辽, 许诸, 夏侯渊, 徐晃\n    4: [310101, 310201, 310401, 310601, 320101, 320201, 320301, 320401, 330202, 330301, 330501, 340101, 340401, 340501, 340601],\n}\n\n// 英雄复活时间\nconst HERO_REVIVES_TIME = 3600000 * 5\n\n// 英雄槽位等级开启条件\nconst HERO_SLOT_LV_COND = [1, 10, 20]\n\n// 养由基召唤时的对应等级\nconst SUMMON_LV = {\n    1: 1,\n    2: 2,\n    3: 4,\n    4: 6,\n    5: 8,\n    6: 10,\n}\n\n// 默认的宠物id\nconst DEFAULT_PET_ID = 4101\n// 矛\nconst SPEAR_PAWN_ID = 3701\n// 火\nconst FIRE_PAWN_ID = 3702\n\n// 资源\nconst RES_MAP = {\n    [CType.CEREAL]: true,\n    [CType.TIMBER]: true,\n    [CType.STONE]: true,\n    [CType.BASE_RES]: true,\n    [CType.EXP_BOOK]: true,\n    [CType.IRON]: true,\n    [CType.UP_SCROLL]: true,\n    [CType.FIXATOR]: true,\n}\n\n// 最多可标记多少个\nconst MAX_MAP_MARK_COUNT = 10\n\n// 申请联盟个数限制\nconst ALLI_APPLY_MAX_COUNT = 3\n\n// 大厅模式对应的底部\nconst LOBBY_MODE_BUTTOM_NAME = {\n    [LobbyModeType.FREE]: 'team',\n    [LobbyModeType.NEWBIE]: 'team',\n    [LobbyModeType.RANKED]: 'team',\n    [LobbyModeType.SNAIL_ISLE]: 'twomiles',\n}\n\n// 斜度\nconst SKEW_ANGLE = 45\n// 斜着的外宽高\nconst SKEW_SIZE = cc.size(16, 8)\n// 斜着的内宽高 32 16\nconst SKEW_SIZE_HALF = cc.size(SKEW_SIZE.width * 0.5, SKEW_SIZE.height * 0.5)\n\n\n// 段位商城的兵符配置\nconst RANK_SHOP_WAR_TOKEN_CONFIG = [\n    { warToken: 10, coin: 10 },\n    { warToken: 100, coin: 100 },\n    { warToken: 1000, coin: 1000 },\n    { warToken: 10000, coin: 10000 },\n]\n\n// 战斗的血条颜色\nconst BATTLE_HPBAR_COLOR = {\n    m: { bar: '#8BE273', bg: '#162D20' },\n    f: { bar: '#6DB5E2', bg: '#121D3A' },\n    0: { bar: '#EE2A4A', bg: '#3B1316' },\n    1: { bar: '#FF64B8', bg: '#41142C' },\n    2: { bar: '#AD64FF', bg: '#281240' },\n    3: { bar: '#FF9648', bg: '#4A2B14' },\n}\n\n// 战斗的火焰颜色\nconst BATTLE_FIRE_COLOR = {\n    m: '#53B977',\n    f: '#40A4E9',\n    0: '#B90900',\n    1: '#FF76F7',\n    2: '#AD64FF',\n    3: '#FFA836',\n}\n\n// 战令价格配置\nconst RECHARGE_BATTLE_PASS = 'jwm_up_book' // $8.99\n\n// 战令经验购买配置\nconst RECHARGE_BATTLE_PASS_EXP = [50, 100] // 50元宝购买100经验\n\n// 有奖问卷调查id\nconst PRIZE_QUESTION_ID = 99900001\n\n// 有奖问卷调查期限\nconst PRIZE_QUESTION_TIME = ['2024-12-26-06-00', '2024-12-30-06-00']\n\n// 打开允许通知弹窗的公共CD\nconst NOTICE_PERMISSION_CD = 24 * 60 * 60 * 1000 // 24小时\n\n// 每日屯田次数\nconst TODAY_TONDEN_MAX_COUNT = 10\n// 屯田资源获取比例\nconst TONDEN_GET_RES_RATIO = 1\n// 屯田奖励点消耗倍数\nconst TONDEN_STAMINA_MUL = 3\n\n// 医馆伤兵上限\nconst HOSPITAL_PAWN_LIMIT = 200\n\n// 各等级士兵战败后回馆概率\nconst GO_HOSPITAL_CHANCE = {\n    1: '0%',\n    2: '100%',\n    3: '100%',\n    4: '100%',\n    5: '100%',\n    6: '100%',\n}\n\n// 画像的天选几率\nconst PORTRAYAL_CHOSENONE_ODDS = 0.005\n\n// 研究类型转评论类型\nconst STUDY_TO_BOOKTYPE = {\n    [StudyType.POLICY]: BookCommentType.POLICY,\n    [StudyType.PAWN]: BookCommentType.PAWN,\n    [StudyType.EQUIP]: BookCommentType.EQUIP,\n    [StudyType.EXCLUSIVE]: BookCommentType.EQUIP,\n}\n\n// 盟主投票最大次数\nconst ALLI_LEADER_VOTE_MAX_COUNT = 4\n\n// 招募动态资源每级系数\nconst PAWN_COST_LV_LIST = [1, 2, 4, 6, 8, 10]\n\n// 工厂解锁配置\nconst FACTORY_SLOT_CONF = [5]\n\n// 摄像机背景颜色\nconst CAMERA_BG_COLOR = ['#ACC961', '#88CA6E', '#E4B765', '#A7E2E3']\n\n//\nconst MAP_MASK_ITEM_COLOR = ['#2B8A85', '#1755AC', '#832E4F', '#8378C2']\n\n// 区域内的地面颜色\nconst AREA_DI_COLOR_CONF = [\n    // 春\n    {\n        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地\n        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食\n        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头\n        10: { bg: '#AFC864', battle: ['#CED974', '#BCD16A'], build: '#EFE28C' }, //主城\n    },\n    // 夏\n    {\n        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地\n        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食\n        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头\n        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' }, //主城\n    },\n    // 秋\n    {\n        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地\n        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食\n        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头\n        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' }, //主城\n    },\n    // 冬\n    {\n        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //荒地\n        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' }, //粮食\n        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头\n        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' }, //主城\n    },\n]\n\n// 难度底板颜色\nconst DIFFICULTY_BG_COLOR = {\n    1: '#81A514',\n    2: '#6683AB',\n    3: '#9C58BF',\n    4: '#CE59A0',\n    5: '#C34B3F',\n}\n\nexport {\n    CLICK_SPACE,\n    MAP_SHOW_OFFSET,\n    TILE_SIZE,\n    TILE_SIZE_HALF,\n    MAP_EXTRA_SIZE,\n    BUILD_DRAG_OFFSETY,\n    AREA_MAX_ZINDEX,\n    MAX_ZINDEX,\n    LONG_PRESS_TIME,\n    DELAY_CLOSE_PNL_TIME,\n    CITY_MAIN_NID,\n    BUILD_FLAG_NID,\n    CITY_FORT_NID,\n    ANCIENT_WALL_ID,\n    CITY_CHANGAN_ID,\n    CITY_JINLING_ID,\n    CITY_YANJING_ID,\n    CITY_LUOYANG_ID,\n    BUILD_FARM_ID,\n    BUILD_TIMBER_ID,\n    BUILD_QUARRY_ID,\n    BUILD_WALL_NID,\n    BUILD_MAIN_NID,\n    BUILD_GRANARY_NID,\n    BUILD_WAREHOUSE_NID,\n    BUILD_BARRACKS_NID,\n    BUILD_EMBASSY_NID,\n    BUILD_BAZAAR_NID,\n    BUILD_SMITHY_NID,\n    BUILD_DRILLGROUND_NID,\n    BUILD_PLANT_NID,\n    BUILD_ALLI_BAZAAR_NID,\n    BUILD_HEROHALL_NID,\n    BUILD_HOSPITAL_NID,\n    BUILD_FORT_NID,\n    BUILD_TOWER_NID,\n    PAWN_CROSSBOW_ID,\n    AX_CAVALRY_ID,\n    INIT_RES_CAP,\n    INIT_RES_COUNT,\n    INIT_RES_OUTPUT,\n    DEFAULT_BT_QUEUE_COUNT,\n    IN_DONE_BT_GOLD,\n    IN_DONE_FORGE_GOLD,\n    MODIFY_NICKNAME_GOLD,\n    ARMY_PAWN_MAX_COUNT,\n    CREATE_ALLI_MAX_LV,\n    DEFAULT_CITY_SIZE,\n    DEFAULT_AREA_SIZE,\n    DEFAULT_BUILD_SIZE,\n    BOSS_BUILD_SIZE,\n    DEFAULT_MAX_ARMY_COUNT,\n    DEFAULT_MAX_ADD_PAWN_TIMES,\n    UP_MARCH_SPEED_MUL,\n    MAIN_CITY_MARCH_SPEED,\n    TRANSIT_TIME,\n    BATTLE_MAX_TIME,\n    LAND_DI_CONF,\n    DECORATION_MUD_CONF,\n    BORDER_LINE_CONF,\n    RIVER_LINE_CONF,\n    SELECT_CELL_INFO_BOX,\n    CELL_RES_FIELDS,\n    PAWN_BUBBLE_OFFSETY,\n    RES_FIELDS_CTYPE,\n    CTYPE_ICON_URL,\n    CTYPE_ICON,\n    CTYPE_NAME,\n    BUILD_EFFECT_TYPE_CONF,\n    MARCH_ARMY_NAME_COLOR,\n    MARCH_ARMY_TIME_COLOR,\n    ARMY_STATE_COLOR,\n    MAIL_STATE_COLOR,\n    ARMY_RECORD_DESC_CONF,\n    PLAYBACK_MULS,\n    FIXATION_MENU_CONFIG,\n    FIXATION_MENU_MAX_COUNT,\n    FREE_HEAD_ICONS,\n    ADD_OUTPUT_GOLD,\n    ADD_OUTPUT_RATIO,\n    ADD_OUTPUT_TIME,\n    POLICY_SLOT_CONF,\n    EQUIP_SLOT_CONF,\n    EQUIP_SLOT_EXCLUSIVE_LV,\n    EQUIP_SMELT_NEED_LV,\n    PAWN_SLOT_CONF,\n    RESET_STUDY_SLOT_GOLD,\n    CAN_EXIT_ALLI_TIME,\n    CREATE_ALLI_COST,\n    CREATE_ALLI_COND,\n    ONE_USER_POPULARITY_CHANGE_INTERVAL,\n    BUFF_SHOW_TYPE_TRAN,\n    SHIELD_BUFF,\n    BATTLE_EFFECT_TYPE,\n    BUFF_NODE_ZINDEX,\n    NEED_SHOW_BUFF,\n    NEED_MUTUAL_BUFF,\n    CHAT_BARRAGE_COLOR,\n    REPLACEMENT_SERVICE_CHARGE,\n    REPLACEMENT_MIN_RES_COUNT,\n    REPLACEMENT_TODAY_COUNT_MAP,\n    RES_TRANSIT_CAP,\n    UP_RECRUIT_PAWN_MUL,\n    LANGUAGE_TEXT_LIST,\n    LOGOUT_MAX_DAY,\n    ALLI_JOB_DESC,\n    ALLI_JOB_COUNT,\n    NOT_OCCUPY_BY_SERVER_RUNTIME,\n    NOT_OCCUPY_BY_MAX_LAND_COUNT,\n    CONCURRENT_GAME_LIMIT,\n    LAND_SCORE_CONF,\n    REMOVE_PCHAT_TIME,\n    OCCUPY_PLAYER_CELL_MIN_DIS,\n    NOLIMIT_PCHAT_MAX_LAND,\n    SHOW_TIME_MAX_INTERVAL,\n    FRIENDS_MIN_LAND_COUNT,\n    BATTLE_FORECAST_COST,\n    BATTLE_FORECAST_FREE_COUNT,\n    OPEN_ALL_TREASURE_MIN_LAND_COUNT,\n    CAN_MIN_MARCH_SPEED,\n    SEASON_DURATION_TIME,\n    ANCIENT_SUP_COST_MUL,\n    ANCIENT_SUP_TIME,\n    COLOR_NORMAL,\n    CHAT_MAX_COUNT,\n    CHAT_SEND_INTERVAL,\n    CHAT_TOLERATE_MAX_COUNT,\n    CHAT_REST_MAX_TIME,\n    CHAT_BANNED_REST_MAX_TIME,\n    SEND_TRUMPET_COST,\n    SEND_TRUMPET_ACC_COST,\n    SERVER_APPLY_CANCEL_CD,\n    NEXT_APPLY_CD,\n    POINTSETS_ONE_COST,\n    POINTSETS_ONE_GOLD_COST,\n    PORTRAYAL_COMP_NEED_COUNT,\n    RESTORE_PORTRAYAL_WAR_TOKEN_COST,\n    RESTORE_PORTRAYAL_GOLD_COST,\n    BUY_OPT_HERO_COST,\n    HERO_OPT_GIFT,\n    HERO_REVIVES_TIME,\n    HERO_SLOT_LV_COND,\n    SUMMON_LV,\n    DEFAULT_PET_ID,\n    SPEAR_PAWN_ID,\n    FIRE_PAWN_ID,\n    RES_MAP,\n    MAX_MAP_MARK_COUNT,\n    ALLI_APPLY_MAX_COUNT,\n    LOBBY_MODE_BUTTOM_NAME,\n    SKEW_ANGLE,\n    SKEW_SIZE,\n    SKEW_SIZE_HALF,\n    RANK_SHOP_WAR_TOKEN_CONFIG,\n    BATTLE_HPBAR_COLOR,\n    BATTLE_FIRE_COLOR,\n    RECHARGE_BATTLE_PASS,\n    RECHARGE_BATTLE_PASS_EXP,\n    PRIZE_QUESTION_ID,\n    PRIZE_QUESTION_TIME,\n    NOTICE_PERMISSION_CD,\n    TODAY_TONDEN_MAX_COUNT,\n    TONDEN_GET_RES_RATIO,\n    TONDEN_STAMINA_MUL,\n    HOSPITAL_PAWN_LIMIT,\n    GO_HOSPITAL_CHANCE,\n    PORTRAYAL_CHOSENONE_ODDS,\n    STUDY_TO_BOOKTYPE,\n    ALLI_LEADER_VOTE_MAX_COUNT,\n    PAWN_COST_LV_LIST,\n    FACTORY_SLOT_CONF,\n    CAMERA_BG_COLOR,\n    MAP_MASK_ITEM_COLOR,\n    AREA_DI_COLOR_CONF,\n    DIFFICULTY_BG_COLOR,\n}"]}