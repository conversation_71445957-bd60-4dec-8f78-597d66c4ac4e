{"version": 3, "sources": ["assets\\app\\script\\view\\build\\BuildMainInfoPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAkE;AAClE,qDAAoD;AACpD,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAK5D,iEAA4D;AAEpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAkD,wCAAc;IAAhE;QAAA,qEA2JC;QAzJG,0BAA0B;QAClB,aAAO,GAAuB,IAAI,CAAA,CAAC,0BAA0B;QAC7D,gBAAU,GAAY,IAAI,CAAA,CAAC,sBAAsB;QACjD,eAAS,GAAY,IAAI,CAAA,CAAC,oCAAoC;QAC9D,oBAAc,GAAY,IAAI,CAAA,CAAC,kDAAkD;QACjF,eAAS,GAAkB,IAAI,CAAA,CAAC,uCAAuC;QAC/E,MAAM;QAEW,cAAQ,GAAW,eAAe,CAAA;QAC3C,SAAG,GAAW,CAAC,CAAA;QAEf,UAAI,GAAc,IAAI,CAAA;QACtB,YAAM,GAAgB,IAAI,CAAA;QAC1B,UAAI,GAAa,IAAI,CAAA;QACrB,mBAAa,GAAuB,IAAI,CAAA;;IA2IpD,CAAC;IAzIU,8CAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,mBAAmB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;SAC3E,CAAA;IACL,CAAC;IAEY,uCAAQ,GAArB;;;gBACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBACrC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,4BAAkB,CAAC,CAAA;;;;KACzE;IAEM,sCAAO,GAAd,UAAe,IAAc,EAAE,GAAY;QACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAClF,CAAC;IAEM,uCAAQ,GAAf;IACA,CAAC;IAEM,sCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,0BAA0B;IAC1B,0CAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACtC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1C,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACpD,IAAI,IAAI,KAAK,CAAC,EAAE;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,mBAAmB,EAAE,oBAAO,CAAC,qBAAqB,CAAC,oBAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;YAC9G,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,2BAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YACpE,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC5E,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACtI;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;SAC5B;IACL,CAAC;IAED,6CAA6C;IAC7C,wCAAS,GAAT,UAAU,KAA0B,EAAE,IAAY;QAC9C,oBAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAED,wCAAwC;IACxC,0CAAW,GAAX,UAAY,KAA0B,EAAE,CAAS;QAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAc,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACzE,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAA;QACnC,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,uBAAU,CAAC,SAAS,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,qBAAqB,CAAC,EAAE,CAAC,CAAA;SAC1H;aAAM,IAAI,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,GAAE,EAAE;YAC7B,OAAO,uBAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;SACrE;aAAM,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,mBAAmB,CAAC,CAAA;SACzD;QACD,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;IACjD,CAAC;IACD,MAAM;IACN,iHAAiH;IAEzG,8CAAe,GAAvB,UAAwB,IAAc;QAClC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE;YAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YACrC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;YACjE,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YACnI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,2BAAgB,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;SAChF;IACL,CAAC;IAED,OAAO;IACC,kDAAmB,GAA3B;QACI,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE;YAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;SAChD;IACL,CAAC;IACD,iHAAiH;IAEjH,KAAK;IACG,6CAAc,GAAtB,UAAuB,IAAa;QAApC,iBAsBC;QArBG,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;QAClE,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAA;QAC5C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,EAAE;YACpB,IAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;YAC1B,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,EAAE,CAAC,CAAA;YAClC,IAAM,QAAQ,GAAG,OAAO,IAAI,EAAE,EAAE,QAAQ,GAAG,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,GAAE,CAAA;YAC/D,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM;gBACnB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACf,+DAA+D;aAClE;iBAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM;gBAC1B,IAAM,SAAS,GAAG,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,CAAC,MAAM,CAAA,CAAA;gBAC1C,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,SAAS,CAAA;gBACjD,mHAAmH;aACtH;YACD,OAAO;YACP,IAAI,QAAQ,EAAE;gBACV,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aAClE;YACD,+DAA+D;QACnE,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,sBAAsB,EAAE,CAAA;IACjC,CAAC;IAED,OAAO;IACC,qDAAsB,GAA9B;QAAA,iBA2BC;QA1BG,IAAM,cAAc,GAAG,EAAE,CAAA;QACzB,IAAM,KAAK,GAAG,oBAAO,CAAC,wBAAwB,EAAE,CAAA;QAChD,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAA;QAC/B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;YACpC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;YAC/D,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;YACxE,IAAM,MAAM,GAAG,oBAAkB,KAAK,SAAM,CAAA;YAC5C,IAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAA;YAC5C,MAAM,CAAC,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;YACzD,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,IAAI,EAAE,KAAK;gBAC/C,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBAC/D,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,CAAA;gBAC/C,IAAI,KAAK,IAAI,EAAE,EAAE,EAAE,OAAO;oBACtB,qBAAS,CAAC,QAAQ,CAAC,cAAc,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;iBACxE;qBAAM,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO;oBAC7B,qBAAS,CAAC,YAAY,CAAC,oBAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;iBACzE;qBAAM;oBACH,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAA;iBACnD;YACL,CAAC,CAAC,CAAA;YACF,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC7F,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAA;gBACb,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,yBAAI,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,0CAAE,eAAe,KAAE,CAAC,CAAA;aACtE;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IA1JgB,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CA2JxC;IAAD,2BAAC;CA3JD,AA2JC,CA3JiD,EAAE,CAAC,WAAW,GA2J/D;kBA3JoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { POLICY_SLOT_CONF } from \"../../common/constant/Constant\";\nimport { ecode } from \"../../common/constant/ECode\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport BuildObj from \"../../model/area/BuildObj\";\nimport UserModel from \"../../model/common/UserModel\";\nimport PlayerModel from \"../../model/main/PlayerModel\";\nimport PolicyObj from \"../../model/main/PolicyObj\";\nimport BuildUnlockTipCmpt from \"../cmpt/BuildUnlockTipCmpt\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class BuildMainInfoPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce\n    private pagesNode_: cc.Node = null // path://root/pages_n\n    private landNode_: cc.Node = null // path://root/pages_n/0/info/land_n\n    private unlockTipNode_: cc.Node = null // path://root/pages_n/0/bottom/title/unlock_tip_n\n    private policySv_: cc.ScrollView = null // path://root/pages_n/1/info/policy_sv\n    //@end\n\n    private readonly PKEY_TAB: string = 'MAIN_INFO_TAB'\n    private tab: number = 0\n\n    private user: UserModel = null\n    private player: PlayerModel = null\n    private data: BuildObj = null\n    private unlockTipCmpt: BuildUnlockTipCmpt = null\n\n    public listenEventMaps() {\n        return [\n            { [EventType.UPDATE_BUILD_LV]: this.onUpdateBuildLv, enter: true },\n            { [EventType.UPDATE_POLICY_SLOTS]: this.onUpdatePolicySlots, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.user = this.getModel('user')\n        this.player = this.getModel('player')\n        this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt)\n    }\n\n    public onEnter(data: BuildObj, tab?: number) {\n        this.data = data\n        this.tabsTc_.Tabs(tab ?? (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0))\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/tabs_tc_tce\n    onClickTabs(event: cc.Toggle, data: string) {\n        !data && audioMgr.playSFX('click')\n        const type = this.tab = Number(event.node.name)\n        const node = this.pagesNode_.Swih(type)[0]\n        this.user.setTempPreferenceData(this.PKEY_TAB, type)\n        if (type === 0) {\n            this.landNode_.Child('val').setLocaleKey('ui.cur_land_count', gameHpr.getPlayerOweCellCount(gameHpr.getUid()))\n            this.unlockTipCmpt.updateInfo(this.data, POLICY_SLOT_CONF, this.key)\n            viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key)\n            viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key)\n        } else if (type === 1) {\n            this.showPolicyInfo(node)\n        }\n    }\n\n    // path://root/pages_n/0/bottom/buttons/up_be\n    onClickUp(event: cc.Event.EventTouch, data: string) {\n        gameHpr.clickBuildUp(this.data, this)\n    }\n\n    // path://root/pages_n/1/policy/slot_nbe\n    onClickSlot(event: cc.Event.EventTouch, _: string) {\n        audioMgr.playSFX('click')\n        const data: PolicyObj = event.target.Data, lv = Number(event.target.name)\n        const isUnlock = this.data.lv >= lv\n        if (!isUnlock) {\n            return viewHelper.showAlert('ui.lv_unlock_new', { params: [assetsMgr.lang('ui.short_lv', lv), 'ui.ceri_type_name_1'] })\n        } else if (!!data?.isYetStudy()) {\n            return viewHelper.showPnl('common/PolicyInfoBox', data.id, 'book')\n        } else if (!data || data.selectIds.length === 0) {\n            return viewHelper.showAlert(ecode.NEED_STUDY_PER_SLOT)\n        }\n        viewHelper.showPnl('build/StudySelect', data)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    private onUpdateBuildLv(data: BuildObj) {\n        if (this.data.uid === data.uid) {\n            const node = this.pagesNode_.Child(0)\n            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv)\n            viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key)\n            this.unlockTipCmpt.updateInfo(this.data, POLICY_SLOT_CONF, this.key, data.lv)\n        }\n    }\n\n    // 刷新政策\n    private onUpdatePolicySlots() {\n        if (this.tab === 1) {\n            this.showPolicyInfo(this.pagesNode_.Child(1))\n        }\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    // 内政\n    private showPolicyInfo(node: cc.Node) {\n        const slot = node.Child('policy/slot_nbe'), buildLv = this.data.lv\n        const policys = this.player.getPolicySlots()\n        slot.children.forEach(it => {\n            const lv = Number(it.name)\n            const data = it.Data = policys[lv]\n            const isUnlock = buildLv >= lv, isSelect = !!data?.isYetStudy()\n            if (!isUnlock) { //还未解锁\n                it.Swih('lock')\n                // state.Color('#C34A32').setLocaleKey('ui.need_lv_unlock', lv)\n            } else if (!isSelect) { //还未选择\n                const canSelect = !!data?.selectIds.length\n                it.Swih('add')[0].Child('dot').active = canSelect\n                // state.Color(canSelect ? '#49983C' : '#756963').setLocaleKey(canSelect ? 'ui.can_study' : 'ui.button_wait_study')\n            }\n            // 选择信息\n            if (isSelect) {\n                resHelper.loadPolicyIcon(data.id, it.Swih('icon')[0], this.key)\n            }\n            // it.Component(cc.Button).interactable = isUnlock && !isSelect\n        })\n        this.updatePolicyEffectInfo()\n    }\n\n    // 内政效果\n    private updatePolicyEffectInfo() {\n        const initItemHeight = 92\n        const datas = gameHpr.getPlayerPolicysBaseInfo()\n        this.policySv_.stopAutoScroll()\n        this.policySv_.Items(datas, (it, data, i) => {\n            it.Child('name/val').setLocaleKey('policyText.name_' + data.id)\n            const value = data.values[Math.min(data.up - 1, data.values.length - 1)]\n            const params = `<color=#4AB32E>${value}</c>`\n            const descRt = it.Child('desc', cc.RichText)\n            descRt.setLocaleKey('policyText.desc_' + data.id, params)\n            it.Child('policys').Items(data.styles, (item, style) => {\n                resHelper.loadPolicyIcon(data.id, item.Child('icon'), this.key)\n                const styleSpr = item.Child('style', cc.Sprite)\n                if (style >= 10) { // 季节政策\n                    resHelper.loadIcon('icon/season_' + (style % 10), styleSpr, this.key)\n                } else if (style === 2) { // 联盟政策\n                    resHelper.loadAlliIcon(gameHpr.alliance.getIcon(), styleSpr, this.key)\n                } else {\n                    styleSpr.Component(cc.Sprite).spriteFrame = null\n                }\n            })\n            const h = Math.max(initItemHeight, (initItemHeight - descRt.lineHeight) + descRt.node.height)\n            if (it.height !== h) {\n                it.height = h\n                it.children.forEach(m => m.Component(cc.Widget)?.updateAlignment())\n            }\n        })\n    }\n}\n"]}