{"version": 3, "sources": ["assets\\app\\script\\view\\menu\\MailListPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAkE;AAElE,qDAA4D;AAC5D,0DAAqD;AACrD,6DAAyD;AACzD,iEAAgE;AAChE,6DAA4D;AAGpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA6C,mCAAc;IAA3D;QAAA,qEAoHC;QAlHG,0BAA0B;QAClB,eAAS,GAAa,IAAI,CAAA,CAAC,4BAA4B;QACvD,aAAO,GAAkB,IAAI,CAAA,CAAC,sBAAsB;QACpD,kBAAY,GAAY,IAAI,CAAA,CAAC,wBAAwB;QACrD,iBAAW,GAAc,IAAI,CAAA,CAAC,oCAAoC;QAClE,gBAAU,GAAY,IAAI,CAAA,CAAC,iCAAiC;QACpE,MAAM;QAEE,WAAK,GAAc,IAAI,CAAA;;IA0GnC,CAAC;IAxGU,yCAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,QAAK,GAAE,IAAI;SACvE,CAAA;IACL,CAAC;IAEY,kCAAQ,GAArB;;;gBACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBAClC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAA;;;;KACvD;IAEM,iCAAO,GAAd;QAAA,iBAcC;QAbG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC7B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,CAAA;QACrC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;QAC1C,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAA,IAAI;YAC3B,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE,EAAE;gBAClC,OAAM;aACT;YACD,2BAAY,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;YACnC,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;YAChC,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACzB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,oBAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAA;IAC7D,CAAC;IAEM,kCAAQ,GAAf;IACA,CAAC;IAEM,iCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,2CAA2C;IAC3C,qCAAW,GAAX,UAAY,KAA0B,EAAE,CAAS;QAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAa,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;SAC5C;IACL,CAAC;IAED,iCAAiC;IACjC,sCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACjD,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;IACxC,CAAC;IAED,oCAAoC;IACpC,wCAAc,GAAd,UAAe,KAA0B,EAAE,IAAY;QAAvD,iBAQC;QAPG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,UAAA,GAAG;YAChC,IAAI,GAAG,EAAE;gBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aACnC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACrB,KAAI,CAAC,UAAU,CAAC,KAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAA;aAC7C;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,OAAO;IACC,sCAAY,GAApB,UAAqB,GAAW;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAA;IAC9C,CAAC;IAED,SAAS;IACD,2CAAiB,GAAzB,UAA0B,IAAc;QACpC,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,GAAG,MAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;QAC5E,IAAI,EAAE,EAAE;YACJ,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SACnC;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,qBAAa,CAAC,IAAI,EAAE;YACnC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAA;SACvC;IACL,CAAC;IAED,iHAAiH;IAEzG,oCAAU,GAAlB,UAAmB,KAAiB;QAApC,iBAiBC;QAhBG,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAArE,CAAqE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;QAC3H,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,qBAAa,CAAC,IAAI,EAA9B,CAA8B,CAAC,CAAA;QAC/E,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAA;QACtD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAA;QAC9C,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,CAAC;YACzB,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAC9B,KAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,qBAAa,CAAC,IAAI,CAAA,CAAC,YAAY;YAC/D,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;YACvF,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;YAC/D,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,CAAA;YAClC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAA;YAC/K,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;QACrI,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,qCAAW,GAAnB,UAAoB,EAAW,EAAE,KAAa;QAC1C,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,2BAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAA;IAC3F,CAAC;IAnHgB,eAAe;QADnC,OAAO;OACa,eAAe,CAoHnC;IAAD,sBAAC;CApHD,AAoHC,CApH4C,EAAE,CAAC,WAAW,GAoH1D;kBApHoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { MAIL_STATE_COLOR } from \"../../common/constant/Constant\";\nimport { MailInfo } from \"../../common/constant/DataType\";\nimport { MailStateType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { reddotHelper } from \"../../common/helper/ReddotHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport UserModel from \"../../model/common/UserModel\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class MailListPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private ttileLbl_: cc.Label = null // path://root/title/ttile_l\n    private listSv_: cc.ScrollView = null // path://root/list_sv\n    private loadingNode_: cc.Node = null // path://root/loading_n\n    private delReadBtn_: cc.Button = null // path://root/buttons/del_read_be_b\n    private writeNode_: cc.Node = null // path://root/buttons/write_be_n\n    //@end\n\n    private model: UserModel = null\n\n    public listenEventMaps() {\n        return [\n            { [EventType.REMOVE_MAIL]: this.onRemoveMail, enter: true },\n            { [EventType.UPDATE_MAIL_STATE]: this.onUpdateMailState, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.model = this.getModel('user')\n        this.ttileLbl_.setLocaleKey('ui.title_mail_list', 0)\n    }\n\n    public onEnter() {\n        this.listSv_.content.Swih('')\n        this.loadingNode_.active = true\n        this.delReadBtn_.interactable = false\n        this.listSv_.Child('empty').active = false\n        this.model.getMails().then(list => {\n            if (!this.isValid || !this.isEnter()) {\n                return\n            }\n            reddotHelper.set('new_mail', false)\n            this.loadingNode_.active = false\n            this.updateList(list)\n        })\n        this.writeNode_.active = gameHpr.alliance.isCanSendMail()\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/list_sv/view/content/item_be\n    onClickItem(event: cc.Event.EventTouch, _: string) {\n        audioMgr.playSFX('click')\n        const data: MailInfo = event.target.Data\n        if (data) {\n            viewHelper.showPnl('menu/MailInfo', data)\n        }\n    }\n\n    // path://root/buttons/write_be_n\n    onClickWrite(event: cc.Event.EventTouch, data: string) {\n        viewHelper.showPnl('menu/WriteMail')\n    }\n\n    // path://root/buttons/del_read_be_b\n    onClickDelRead(event: cc.Event.EventTouch, data: string) {\n        this.model.delAllReadMail().then(err => {\n            if (err) {\n                return viewHelper.showAlert(err)\n            } else if (this.isValid) {\n                this.updateList(this.model.getTempMails())\n            }\n        })\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // 删除邮件\n    private onRemoveMail(uid: string) {\n        this.updateList(this.model.getTempMails())\n    }\n\n    // 刷新邮件状态\n    private onUpdateMailState(data: MailInfo) {\n        const it = this.listSv_.content.children.find(m => m.Data?.uid === data.uid)\n        if (it) {\n            this.updateState(it, data.state)\n        }\n        if (data.state === MailStateType.READ) {\n            this.delReadBtn_.interactable = true\n        }\n    }\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private updateList(mails: MailInfo[]) {\n        const list = mails.sort((a, b) => a.state === b.state ? b.createTime - a.createTime : a.state - b.state), len = list.length\n        this.delReadBtn_.interactable = mails.some(m => m.state === MailStateType.READ)\n        this.ttileLbl_.setLocaleKey('ui.title_mail_list', len)\n        this.listSv_.Child('empty').active = len === 0\n        this.listSv_.stopAutoScroll()\n        this.listSv_.content.y = 0\n        this.listSv_.List(len, (it, i) => {\n            const data = it.Data = list[i]\n            this.updateState(it, data.state)\n            const isReaded = data.state === MailStateType.READ // 已读状态全部换色 \n            it.Child('title', cc.Label).Color(isReaded ? '#A18876' : '#3F332F').string = data.title\n            it.Child('sender/name').Color(isReaded ? '#A18876' : '#625450')\n            const isSys = data.sender === '-1'\n            it.Child('sender/val', cc.Label).Color(isReaded ? '#A18876' : isSys ? '#BE772B' : '#936E5A').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8)\n            it.Child('time', cc.Label).Color(isReaded ? '#A18876' : '#625450').string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime)\n        })\n    }\n\n    private updateState(it: cc.Node, state: number) {\n        it.Child('state').Color(MAIL_STATE_COLOR[state]).setLocaleKey('ui.mail_state_' + state)\n    }\n}\n"]}