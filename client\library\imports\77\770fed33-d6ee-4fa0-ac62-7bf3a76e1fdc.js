"use strict";
cc._RF.push(module, '770fe0z1u5PoKxie/Onbh/c', 'PlayerModel');
// app/script/model/main/PlayerModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ProtoHelper_1 = require("../../../proto/ProtoHelper");
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var DBHelper_1 = require("../../common/helper/DBHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var MerchantObj_1 = require("../bazaar/MerchantObj");
var BTInfoObj_1 = require("./BTInfoObj");
var CTypeObj_1 = require("../common/CTypeObj");
var EquipInfo_1 = require("./EquipInfo");
var ForgeEquipInfo_1 = require("./ForgeEquipInfo");
var OutputObj_1 = require("./OutputObj");
var PawnDrillInfoObj_1 = require("./PawnDrillInfoObj");
var PawnLevelingInfoObj_1 = require("./PawnLevelingInfoObj");
var PolicyObj_1 = require("./PolicyObj");
var SmeltEquipInfo_1 = require("./SmeltEquipInfo");
var TaskObj_1 = require("../common/TaskObj");
var GuideConfig_1 = require("../guide/GuideConfig");
var HeroSlotObj_1 = require("./HeroSlotObj");
var PawnCureInfoObj_1 = require("./PawnCureInfoObj");
var EquipSlotObj_1 = require("./EquipSlotObj");
var PawnSlotObj_1 = require("./PawnSlotObj");
/**
 * 玩家
 */
var PlayerModel = /** @class */ (function (_super) {
    __extends(PlayerModel, _super);
    function PlayerModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.user = null;
        _this.alliance = null;
        _this.initTime = 0; //初始化时间
        _this.cereal = null; //粮
        _this.timber = null; //木
        _this.stone = null; //石
        _this.expBook = 0; //经验书
        _this.iron = 0; //铁
        _this.upScroll = 0; //卷轴
        _this.fixator = 0; //固定器
        _this.cerealConsume = 0; //当前粮耗
        _this.granaryCap = 0; //粮仓容量
        _this.warehouseCap = 0; //仓库容量
        _this.stamina = 0; //当前奖励点
        _this.captureInfo = null; //被攻陷的信息
        _this.sumOnlineTime = 0; //累计在线时间
        _this.mainCityIndex = 0; //主城所在位置
        _this.mainBuilds = []; //主城当前的建筑列表
        _this.unlockPawnIds = []; //当前额外解锁的兵种列表
        _this.unlockEquipIds = []; //当前额外解锁的装备列表
        _this.btQueues = []; //当前的建造队列
        _this.pawnDrillQueueMap = new Map(); //士兵训练队列
        _this.pawnLevelingQueues = []; //士兵练级队列
        _this.baseArmys = []; //临时的军队列表
        _this.armyDistMap = {};
        _this.merchants = []; //商人列表
        _this.guideTasks = []; //新手任务列表
        _this.todayTasks = []; //每日任务列表
        _this.otherTasks = []; //其他任务列表
        _this.equips = []; //已有的装备列表
        _this.currForgeEquip = null; //当前打造装备信息
        _this.currSmeltEquip = null; //当前正在融炼得装备信息
        _this.configPawnMap = {}; //配置士兵的信息
        _this.citySkinConfigMap = {}; //城市皮肤配置的信息
        _this.fortAutoSupports = []; //要塞自动支援配置
        _this.addOutputSurplusTime = {}; //添加产量剩余时间
        _this.getAddOutputTime = 0;
        _this.policySlots = {}; //当前政策槽位列表
        _this.equipSlots = {}; //当前装备槽位列表
        _this.pawnSlots = {}; //当前士兵槽位列表
        _this.heroSlots = []; //当前英雄槽位信息
        _this.hidePChatChannels = {}; //隐藏的私聊频道
        _this.exitAllianceCount = 0; //退出联盟次数
        _this.todayOccupyCellCount = 0; //每日打地数量
        _this.accTotalGiveResCount = 0; //累计赠送资源数量
        _this.todayReplacementCount = 0; //每日置换次数
        _this.landScore = 0; //领地积分
        _this.occupyLandCountMap = {}; //历史攻占野地数量 key=地块等级 val=数量
        _this.maxOccupyLandDifficulty = 0; //历史最大攻占野地难度
        _this.killRecordMap = {}; //击杀数量 key=id val=数量
        _this.mapMarks = []; //地图标记
        _this.reCreateMainCityCount = 0; //重新创建主城次数
        _this.cellTondenCount = 0; //每日屯田次数
        _this.upRecruitPawnCount = 0; //加速招募士兵数量
        _this.freeRecruitPawnCount = 0; //免费招募士兵数量
        _this.freeLevingPawnCount = 0; //免费训练士兵数量
        _this.freeCurePawnCount = 0; //免费治疗士兵数量
        _this.freeForgeCount = 0; //免费打造/重铸数量
        _this.isSettled = false; //是否已结算（只有血战到底模式有该值）
        _this.armyMaxCount = 0; //军队最大数量
        _this.mainCityRect = { min: cc.v2(), max: cc.v2() };
        _this.injuryPawns = []; // 可治疗的伤兵
        _this.curingPawns = []; // 治疗中的伤兵
        _this.lastReqSelectArmysTime = 0; //最后一次请求军队时间
        _this.tempSelectArmyErr = '';
        _this.tempSelectArmyList = []; //临时的选择军队信息
        _this.tempSelectArmyCanGotoCount = 0; //临时的选择军队 可前往数量
        _this.lastReqArmysTime = 0; //最后一次请求军队时间
        _this.tempArmyList = []; //临时的军队信息
        _this.lastReqArmyMarchRecordTime = 0; //最后一次请求军队记录时间
        _this.tempArmyMarchRecordList = []; //军队记录列表
        _this.lastReqArmyBattleRecordTime = 0; //最后一次请求军队记录时间
        _this.tempArmyBattleRecordList = []; //军队记录列表
        _this.lastReqBazaarRecordTime = 0; //最后一次请求市场记录时间
        _this.tempBazaarRecordList = []; //市场记录列表
        _this.tempBattleForecastRets = []; //临时记录玩家战斗预测结果
        _this.tempStopPlayMessageSound = {}; //临时需要停止播放的音效
        _this.tempCanForgeEquips = null;
        _this.tempCanRecruitPawns = {};
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        return _this;
    }
    PlayerModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.user = this.getModel('user');
        this.alliance = this.getModel('alliance');
    };
    // 初始化信息
    PlayerModel.prototype.init = function (data, isLocal) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        cc.log('init player', data);
                        this.resetTempInfo();
                        this.lastReqSelectArmysTime = 0;
                        this.lastReqArmysTime = 0;
                        this.lastReqArmyMarchRecordTime = 0;
                        this.lastReqArmyBattleRecordTime = 0;
                        this.lastReqBazaarRecordTime = 0;
                        this.initTime = Date.now();
                        this.sumOnlineTime = data.sumOnlineTime || 0;
                        this.setCaptureInfo(data.captureInfo);
                        this.expBook = data.expBook || 0;
                        this.iron = data.iron || 0;
                        this.upScroll = data.upScroll || 0;
                        this.fixator = data.fixator || 0;
                        this.granaryCap = data.granaryCap || 1;
                        this.warehouseCap = data.warehouseCap || 1;
                        this.cereal = new OutputObj_1.default(EventType_1.default.UPDATE_CEREAL).fromSvr(data.cereal);
                        this.timber = new OutputObj_1.default(EventType_1.default.UPDATE_TIMBER).fromSvr(data.timber);
                        this.stone = new OutputObj_1.default(EventType_1.default.UPDATE_STONE).fromSvr(data.stone);
                        this.cerealConsume = data.cerealConsume || 0;
                        this.stamina = data.stamina || 0;
                        this.mainCityIndex = data.mainCityIndex || 0;
                        this.mainBuilds = data.builds || [];
                        this.unlockPawnIds = data.unlockPawnIds || [];
                        this.unlockEquipIds = data.unlockEquipIds || [];
                        this.updateBtQueue(data.btQueues || [], false);
                        this.updatePawnDrillQueue(data.pawnDrillQueues || {}, false);
                        this.updatePawnLevelingQueue(data.pawnLevelingQueues || [], false);
                        this.updatePawnCuringQueue(data.curingQueues || [], false);
                        this.updateArmyDists(data.armyDists || [], false);
                        this.merchants = (data.merchants || []).map(function (m) { return new MerchantObj_1.default().fromSvr(m); });
                        this.equips = (data.equips || []).map(function (m) { return new EquipInfo_1.default().fromSvr(m); });
                        this.configPawnMap = data.configPawnMap || {};
                        this.citySkinConfigMap = data.citySkinConfigMap || {};
                        this.fortAutoSupports = data.fortAutoSupports || [];
                        this.updateAddOutputTime(data.addOutputSurplusTime);
                        this.updatePolicySlots(data.policySlots || {}, false);
                        this.updatePawnSlots(data.pawnSlots || {}, false);
                        this.updateEquipSlots(data.equipSlots || {}, false);
                        this.updateCurrForgeEquip(data.currForgeEquip);
                        this.updateCurrSmeltEquip(data.currSmeltEquip);
                        this.heroSlots = (data.heroSlots || []).map(function (m) { return new HeroSlotObj_1.default().fromSvr(m); });
                        this.hidePChatChannels = data.hidePChatChannels || {};
                        this.exitAllianceCount = data.exitAllianceCount || 0;
                        this.updateArmyMaxCount(); //刷新军队最大数量
                        this.todayOccupyCellCount = data.todayOccupyCellCount || 0;
                        this.accTotalGiveResCount = data.accTotalGiveResCount || 0;
                        this.todayReplacementCount = data.todayReplacementCount || 0;
                        this.landScore = data.landScore || 0;
                        this.updateOccupyLandCountMap(data.occupyLandCountMap || {});
                        this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1;
                        this.killRecordMap = data.killRecordMap || {};
                        this.mapMarks = (data.mapMarks || []).map(function (m) { return { name: m.name, point: cc.v2(m.point) }; });
                        this.reCreateMainCityCount = data.reCreateMainCityCount || 0;
                        this.cellTondenCount = data.cellTondenCount || 0;
                        this.injuryPawns = data.injuryPawns || [];
                        this.upRecruitPawnCount = data.upRecruitPawnCount || 0;
                        this.freeRecruitPawnCount = data.freeRecruitPawnCount || 0;
                        this.freeLevingPawnCount = data.freeLevingPawnCount || 0;
                        this.freeCurePawnCount = data.freeCurePawnCount || 0;
                        this.freeForgeCount = data.freeForgeCount || 0;
                        this.isSettled = !!data.isSettled;
                        this.updateGuideTasks(data.guideTasks || [], false);
                        this.updateTodayTasks(data.todayTasks || [], false); //需要放到这个位置 因为需要前面的todayOccupyCellCount
                        this.updateOtherTasks(data.otherTasks || [], false);
                        // 主城的矩形区域
                        this.updateMainCityRect(this.mainCityIndex);
                        if (!!isLocal) return [3 /*break*/, 2];
                        // 获取联盟信息
                        return [4 /*yield*/, this.alliance.init(data.allianceUid || '')];
                    case 1:
                        // 获取联盟信息
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        // 宝箱
                        ReddotHelper_1.reddotHelper.set('treasure_main', !!data.hasNewTreasure);
                        if (this.guideTasks.length > 0) {
                            ReddotHelper_1.reddotHelper.unregister('guide_task');
                            ReddotHelper_1.reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1);
                        }
                        // 监听消息
                        this.net.on('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this);
                        return [2 /*return*/];
                }
            });
        });
    };
    PlayerModel.prototype.clean = function () {
        this.net.off('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this);
        this.tempBattleForecastRets = [];
        this.tempArmyList = [];
        this.mainBuilds = [];
        this.guideTasks = [];
        this.todayTasks = [];
        this.otherTasks = [];
        this.heroSlots = [];
        this.baseArmys = [];
        this.pawnDrillQueueMap.clear();
        this.pawnLevelingQueues = [];
        this.tempSelectArmyList = [];
        this.tempArmyMarchRecordList = [];
        this.tempArmyBattleRecordList = [];
        this.tempBazaarRecordList = [];
        this.resetTempInfo();
    };
    PlayerModel.prototype.resetTempInfo = function () {
        this.tempCanForgeEquips = null;
        this.tempCanRecruitPawns = {};
    };
    PlayerModel.prototype.getInitTIme = function () { return this.initTime; };
    PlayerModel.prototype.getToInitElapsedTime = function () { return Date.now() - this.initTime; }; //到初始化经过的时间
    PlayerModel.prototype.getCaptureInfo = function () { return this.captureInfo; };
    PlayerModel.prototype.isCapture = function () { var _a; return !!this.captureInfo || !((_a = this.mainBuilds) === null || _a === void 0 ? void 0 : _a.length); }; //是否沦陷
    PlayerModel.prototype.getMainCityIndex = function () { return this.mainCityIndex; };
    PlayerModel.prototype.getMainCityPoint = function () { return MapHelper_1.mapHelper.indexToPoint(this.mainCityIndex); };
    PlayerModel.prototype.getAlliance = function () { return this.alliance; };
    PlayerModel.prototype.getAllianceUid = function () { return this.alliance.getUid(); };
    PlayerModel.prototype.isHasAlliance = function () { return !!this.alliance.getUid(); };
    PlayerModel.prototype.getCereal = function () { var _a; return ((_a = this.cereal) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getCerealOp = function () { var _a; return ((_a = this.cereal) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setCereal = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.cereal.set(val, isEmit);
    };
    PlayerModel.prototype.changeCereal = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.cereal.change(val, isEmit);
    };
    PlayerModel.prototype.getTimber = function () { var _a; return ((_a = this.timber) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getTimberOp = function () { var _a; return ((_a = this.timber) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setTimber = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.timber.set(val, isEmit);
    };
    PlayerModel.prototype.changeTimber = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.timber.change(val, isEmit);
    };
    PlayerModel.prototype.getStone = function () { var _a; return ((_a = this.stone) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getStoneOp = function () { var _a; return ((_a = this.stone) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setStone = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.stone.set(val, isEmit);
    };
    PlayerModel.prototype.changeStone = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.stone.change(val, isEmit);
    };
    PlayerModel.prototype.getCerealConsume = function () { return this.cerealConsume; };
    PlayerModel.prototype.getCerealCapRatio = function () { return this.getCereal() / this.granaryCap; };
    PlayerModel.prototype.getTimberCapRatio = function () { return this.getTimber() / this.warehouseCap; };
    PlayerModel.prototype.getStoneCapRatio = function () { return this.getStone() / this.warehouseCap; };
    PlayerModel.prototype.getGranaryCap = function () { return this.granaryCap; };
    PlayerModel.prototype.getWarehouseCap = function () { return this.warehouseCap; };
    PlayerModel.prototype.getAddOutputSurplusTime = function () { return this.addOutputSurplusTime; };
    PlayerModel.prototype.getAddOutputElapsedTime = function () { return Date.now() - this.getAddOutputTime; };
    PlayerModel.prototype.getExitAllianceCount = function () { return this.exitAllianceCount; };
    PlayerModel.prototype.setExitAllianceCount = function (val) { this.exitAllianceCount = val; };
    PlayerModel.prototype.getHeroSlots = function () { return this.heroSlots; };
    PlayerModel.prototype.getHidePChatChannels = function () { return this.hidePChatChannels; };
    PlayerModel.prototype.getUnlockPawnIds = function () { return this.unlockPawnIds; };
    PlayerModel.prototype.getUnlockEquipIds = function () { return this.unlockEquipIds; };
    PlayerModel.prototype.getTodayOccupyCellCount = function () { return this.todayOccupyCellCount; };
    PlayerModel.prototype.addTodayOccupyCellCount = function (val) { return this.todayOccupyCellCount += val; };
    PlayerModel.prototype.getAccTotalGiveResCount = function () { return this.accTotalGiveResCount; };
    PlayerModel.prototype.addAccTotalGiveResCount = function (val) { return this.accTotalGiveResCount += val; };
    PlayerModel.prototype.getTodayReplacementCount = function () { return this.todayReplacementCount; };
    PlayerModel.prototype.setTodayReplacementCount = function (val) { this.todayReplacementCount = val; };
    PlayerModel.prototype.getUpRecruitPawnCount = function () { return this.upRecruitPawnCount; };
    PlayerModel.prototype.setUpRecruitPawnCount = function (val) { this.upRecruitPawnCount = val; };
    PlayerModel.prototype.getFreeRecruitPawnCount = function () { return this.freeRecruitPawnCount; };
    PlayerModel.prototype.setFreeRecruitPawnCount = function (val) { this.freeRecruitPawnCount = val; };
    PlayerModel.prototype.getFreeLevingPawnCount = function () { return this.freeLevingPawnCount; };
    PlayerModel.prototype.setFreeLevingPawnCount = function (val) { this.freeLevingPawnCount = val; };
    PlayerModel.prototype.getFreeCurePawnCount = function () { return this.freeCurePawnCount; };
    PlayerModel.prototype.setFreeCurePawnCount = function (val) { this.freeCurePawnCount = val; };
    PlayerModel.prototype.getFreeForgeCount = function () { return this.freeForgeCount; };
    PlayerModel.prototype.setFreeForgeCount = function (val) { this.freeForgeCount = val; };
    PlayerModel.prototype.getMaxOccupyLandDifficulty = function () { return this.maxOccupyLandDifficulty; };
    PlayerModel.prototype.getLandScore = function () { return this.landScore; };
    PlayerModel.prototype.getReCreateMainCityCount = function () { return this.reCreateMainCityCount; };
    PlayerModel.prototype.getCellTondenCount = function () { return this.cellTondenCount; };
    PlayerModel.prototype.getSumOnlineTime = function () {
        return this.sumOnlineTime + (Date.now() - this.initTime);
    };
    // 获取免费招募剩余次数
    PlayerModel.prototype.getFreeRecruitPawnSurplusCount = function () {
        var freeCount = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_DRILL_COUNT);
        if (GameHelper_1.gameHpr.isNoviceMode) {
            freeCount += 9; //新手村给9个
        }
        return Math.max(0, freeCount - this.freeRecruitPawnCount);
    };
    // 获取免费训练士兵剩余次数
    PlayerModel.prototype.getFreeLevingPawnSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_LEVING_COUNT) - this.freeLevingPawnCount);
    };
    // 获取免费治疗士兵剩余次数
    PlayerModel.prototype.getFreeCurePawnSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_FREE_COUNT) - this.freeCurePawnCount);
    };
    // 获取免费免费打造/重铸剩余次数
    PlayerModel.prototype.getfreeForgeSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_RECAST_COUNT) - this.freeForgeCount);
    };
    // 兼容刷新一下固定菜单
    PlayerModel.prototype.updateFixationMenuData = function () {
        var _this = this;
        var ids = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA) || [];
        if (!this.captureInfo) {
            var len = ids.length;
            ids.delete(function (id) { return !_this.mainBuilds.has('id', id); });
            if (ids.length !== len) {
                this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, ids);
            }
        }
        else if (ids.length > 0) {
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, []);
        }
    };
    PlayerModel.prototype.updateMainCityRect = function (index) {
        var pos = MapHelper_1.mapHelper.indexToPoint(index).mul(Constant_1.TILE_SIZE);
        this.mainCityRect.min.x = pos.x;
        this.mainCityRect.min.y = pos.y;
        this.mainCityRect.max.x = pos.x + Constant_1.TILE_SIZE * 2;
        this.mainCityRect.max.y = pos.y + Constant_1.TILE_SIZE * 2;
    };
    // 主城是否不在屏幕范围内
    PlayerModel.prototype.checkMainNotInScreenRange = function () {
        if (this.isCapture() && !GameHelper_1.gameHpr.isSpectate()) {
            return false;
        }
        var outMin = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(this.mainCityRect.min, this._temp_vec2_1);
        var outMax = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(this.mainCityRect.max, this._temp_vec2_2);
        return outMax.x <= 0 || outMax.y <= 0 || outMin.x >= cc.winSize.width || outMin.y >= cc.winSize.height;
    };
    // 经验书
    PlayerModel.prototype.getExpBook = function () { return this.expBook; };
    PlayerModel.prototype.setExpBook = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.expBook);
        this.expBook = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_EXP_BOOK, add);
        }
    };
    // 铁
    PlayerModel.prototype.getIron = function () { return this.iron; };
    PlayerModel.prototype.setIron = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.iron);
        this.iron = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_IRON, add);
        }
    };
    // 卷轴
    PlayerModel.prototype.getUpScroll = function () { return this.upScroll; };
    PlayerModel.prototype.setUpScroll = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.upScroll);
        this.upScroll = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_UPSCROLL, add);
        }
    };
    // 固定器
    PlayerModel.prototype.getFixator = function () { return this.fixator; };
    PlayerModel.prototype.setFixator = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.fixator);
        this.fixator = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_FIXATOR, add);
        }
    };
    // 奖励点
    PlayerModel.prototype.getStamina = function () { return this.stamina; };
    PlayerModel.prototype.setStamina = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.stamina);
        this.stamina = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_STAMINA, add);
        }
    };
    PlayerModel.prototype.setUnlockEquipIds = function (ids) {
        this.unlockEquipIds = ids || [];
        this.tempCanForgeEquips = null; //重新获取
    };
    PlayerModel.prototype.setUnlockPawnIds = function (ids) {
        this.unlockPawnIds = ids || [];
        this.tempCanRecruitPawns = {}; //重新获取
    };
    // 获取修建队列
    PlayerModel.prototype.getBtQueueCount = function () {
        return Constant_1.DEFAULT_BT_QUEUE_COUNT + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.BT_QUEUE);
    };
    // 设置被沦陷了
    PlayerModel.prototype.setCaptureInfo = function (data) {
        if (!(data === null || data === void 0 ? void 0 : data.uid)) {
            this.captureInfo = null;
        }
        else {
            this.captureInfo = data;
            var ids = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA);
            if (ids && ids.length > 0) {
                this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, []);
            }
            // 固定器效果重置
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.LOCK_EQUIP_EFFECT_CONF, {});
        }
    };
    // 主城建筑列表信息
    PlayerModel.prototype.getMainBuilds = function () { return this.mainBuilds; };
    PlayerModel.prototype.updateMainBuildInfo = function (data) {
        GameHelper_1.gameHpr.cleanUnlockBuildCondText(); //清理一下 因为建筑有改变
        var build = this.mainBuilds.find(function (m) { return m.uid === data.uid; });
        if (build) {
            build.lv = data.lv;
        }
        else {
            this.mainBuilds.push(data);
        }
        if (data.id === Enums_1.BUILD_NID.MAIN) {
            this.updateArmyMaxCount();
        }
        this.emit(EventType_1.default.MAIN_BUILD_CHANGE_LV, data);
    };
    // 获取建筑等级
    PlayerModel.prototype.getBuildLv = function (id) {
        var _a;
        return ((_a = this.mainBuilds.find(function (m) { return m.id === id; })) === null || _a === void 0 ? void 0 : _a.lv) || 0;
    };
    PlayerModel.prototype.getMainBuildLv = function () {
        return this.getBuildLv(Enums_1.BUILD_NID.MAIN);
    };
    PlayerModel.prototype.updateArmyMaxCount = function () {
        var _a;
        var lv = this.getMainBuildLv();
        var effect = GameHelper_1.gameHpr.stringToCEffects((_a = assetsMgr.getJsonData('buildAttr', Enums_1.BUILD_NID.MAIN * 1000 + lv)) === null || _a === void 0 ? void 0 : _a.effects)[0];
        this.armyMaxCount = this.isCapture() ? 0 : (effect === null || effect === void 0 ? void 0 : effect.value) || 4;
    };
    // 当前的军队最大数量
    PlayerModel.prototype.getArmyMaxCount = function () {
        return this.armyMaxCount + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.ARMY_COUNT);
    };
    // 军队数量是否满了
    PlayerModel.prototype.isArmyCountFull = function () {
        return this.baseArmys.length >= this.getArmyMaxCount();
    };
    // 军队分布信息
    PlayerModel.prototype.getBaseArmys = function () { return this.baseArmys; };
    PlayerModel.prototype.getArmyDistMap = function () { return this.armyDistMap; };
    PlayerModel.prototype.getDistArmysByIndex = function (index) { return this.armyDistMap[index] || []; };
    PlayerModel.prototype.updateArmyDists = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.armyDistMap = {};
        this.baseArmys.length = 0;
        datas.forEach(function (m) {
            var armys = [];
            m.armys.forEach(function (army) {
                army.index = m.index;
                if (army.state !== Enums_1.ArmyState.MARCH) {
                    armys.push(army);
                }
                _this.baseArmys.push(army);
                _this.updateTempArmyIndex(army);
            });
            if (armys.length > 0) {
                _this.armyDistMap[m.index] = armys;
            }
        });
        isEmit && this.emit(EventType_1.default.UPDATE_ARMY_DIST_INFO);
    };
    // 是否可以刷新产出
    PlayerModel.prototype.isCanUpdateOutput = function () {
        return this.user.getSid() > 0 || GameHelper_1.gameHpr.isNoviceMode;
    };
    // 刷新产出信息
    PlayerModel.prototype.updateOutput = function (data) {
        var _a, _b, _c;
        data = data || {};
        this.granaryCap = (_a = data.granaryCap) !== null && _a !== void 0 ? _a : this.granaryCap;
        this.warehouseCap = (_b = data.warehouseCap) !== null && _b !== void 0 ? _b : this.warehouseCap;
        this.cereal.updateInfo(data.cereal);
        this.timber.updateInfo(data.timber);
        this.stone.updateInfo(data.stone);
        this.cerealConsume = (_c = data.cerealConsume) !== null && _c !== void 0 ? _c : this.cerealConsume;
        // 如果只更新了容量
        if ((data.granaryCap || data.warehouseCap) && (!data.cereal && !data.timber && !data.stone)) {
            this.emit(EventType_1.default.UPDATE_RES_CAP);
        }
    };
    // 刷新产出信息（位标记更新）
    PlayerModel.prototype.updateOutputByFlags = function (data) {
        var _a, _b, _c;
        if (!this.isCanUpdateOutput()) {
            return;
        }
        else if ((data === null || data === void 0 ? void 0 : data.flag) === undefined) {
            return this.updateOutput(data);
        }
        else if (data.flag === 0) {
            return;
        }
        var granaryCapFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.GranaryCap);
        var warehouseCapFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarehouseCap);
        var cerealFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Cereal);
        var timberFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Timber);
        var stoneFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stone);
        var cerealConsumeFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CerealConsume);
        //
        if (granaryCapFlag)
            this.granaryCap = (_a = data.granaryCap) !== null && _a !== void 0 ? _a : this.granaryCap;
        if (warehouseCapFlag)
            this.warehouseCap = (_b = data.warehouseCap) !== null && _b !== void 0 ? _b : this.warehouseCap;
        if (cerealFlag)
            this.cereal.updateInfo(data.cereal);
        if (timberFlag)
            this.timber.updateInfo(data.timber);
        if (stoneFlag)
            this.stone.updateInfo(data.stone);
        if (cerealConsumeFlag)
            this.cerealConsume = (_c = data.cerealConsume) !== null && _c !== void 0 ? _c : this.cerealConsume;
        // 如果只更新了容量
        if ((granaryCapFlag || warehouseCapFlag) && (!cerealFlag && !timberFlag && !stoneFlag)) {
            this.emit(EventType_1.default.UPDATE_RES_CAP);
        }
    };
    // 刷新奖励信息
    PlayerModel.prototype.updateRewardItems = function (data) {
        if (data) {
            this.user.setGold(data.gold);
            this.user.setIngot(data.ingot);
            this.user.setWarToken(data.warToken);
            this.user.setTitles(data.titles);
            this.updateOutputByFlags(data);
            if (this.isCanUpdateOutput()) {
                this.setExpBook(data.expBook);
                this.setIron(data.iron);
                this.setUpScroll(data.upScroll);
                this.setFixator(data.fixator);
                this.setStamina(data.stamina);
                this.setUpRecruitPawnCount(data.upRecruitPawnCount);
            }
        }
    };
    // 刷新奖励信息（位标记更新）
    PlayerModel.prototype.updateRewardItemsByFlags = function (data) {
        if ((data === null || data === void 0 ? void 0 : data.flag) === undefined) {
            return this.updateRewardItems(data);
        }
        else if (data.flag) {
            this.updateOutputByFlags(data);
            if (this.isCanUpdateOutput()) {
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ExpBook))
                    this.setExpBook(data.expBook);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Iron))
                    this.setIron(data.iron);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpScroll))
                    this.setUpScroll(data.upScroll);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Fixator))
                    this.setFixator(data.fixator);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Equip))
                    this.setUnlockEquipIds(data.unlockEquipIds);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Pawn))
                    this.setUnlockPawnIds(data.unlockPawnIds);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stamina))
                    this.setStamina(data.stamina);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpRecruit))
                    this.setUpRecruitPawnCount(data.upRecruitPawnCount);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeRecruit))
                    this.setFreeRecruitPawnCount(data.freeRecruitPawnCount); //免费招募
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeLeving))
                    this.setFreeLevingPawnCount(data.freeLevingPawnCount); //免费训练
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeCure))
                    this.setFreeCurePawnCount(data.freeCurePawnCount); //免费治疗
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeForge))
                    this.setFreeForgeCount(data.freeForgeCount); //免费打造
            }
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Gold))
                this.user.setGold(data.gold);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Ingot))
                this.user.setIngot(data.ingot);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarToken))
                this.user.setWarToken(data.warToken);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Title))
                this.user.setTitles(data.titles);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.PawnSkin))
                this.user.setUnlockPawnSkinIds(data.pawnSkins);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Portrayal))
                this.user.setPortrayals(data.portrayals);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.SkinItemEnum))
                this.user.setSkinItemList(data.skinItems);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CitySkin))
                this.user.setUnlockCitySkinIds(data.citySkins);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.HeadIcon))
                this.user.setUnlockHeadIcons(data.unlockHeadIcons);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ChatEmoji))
                this.user.setUnlockChatEmojiIds(data.unlockChatEmojiIds);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.RankCoin))
                this.user.setRankCoin(data.rankCoin);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Botany))
                this.user.setUnlockBotanys(data.unlockBotanys);
        }
    };
    // 兵营训练队列
    PlayerModel.prototype.getPawnDrillQueues = function (uid) { return this.pawnDrillQueueMap.get(uid) || []; };
    PlayerModel.prototype.updatePawnDrillQueue = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.pawnDrillQueueMap.clear();
        for (var uid in datas) {
            this.pawnDrillQueueMap.set(uid, datas[uid].list.map(function (m) {
                var data = new PawnDrillInfoObj_1.default().fromSvr(m);
                // 添加训练完成消息
                if (data.surplusTime > 0) {
                    var type = Math.floor(data.id / 100);
                    GameHelper_1.gameHpr.addMessage({
                        key: 'ui.message_101',
                        params: ['pawnText.name_' + data.id, type === 35 ? 'ui.button_produce' : 'ui.button_drill'],
                        tag: data.uid,
                        delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                    });
                }
                return data;
            }));
        }
        // 检测
        this.pawnDrillQueueMap.forEach(function (arr, key) {
            if (arr.length === 1 && !arr[0].surplusTime) {
                _this.pawnDrillQueueMap.delete(key);
            }
        });
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_DRILL_QUEUE, this.mainCityIndex);
    };
    PlayerModel.prototype.getAllPawnDrillList = function () {
        var arr = [];
        this.pawnDrillQueueMap.forEach(function (m) { return arr.pushArr(m); });
        return arr;
    };
    // 获取某个军队训练士兵剩余时间
    PlayerModel.prototype.getSumDrillTimeByArmy = function (uid) {
        var maxTime = 0;
        this.pawnDrillQueueMap.forEach(function (arr) {
            var time = 0;
            arr.forEach(function (m) {
                if (m.surplusTime > 0) {
                    time += m.getSurplusTime();
                }
                else {
                    time += m.needTime;
                }
                if (m.auid === uid && time > maxTime) {
                    maxTime = time;
                }
            });
        });
        return { time: maxTime };
    };
    // 获取某个军队治疗士兵剩余时间
    PlayerModel.prototype.getSumCuringTimeByArmy = function (uid) {
        var maxTime = 0, time = 0;
        this.curingPawns.forEach(function (m) {
            if (m.surplusTime > 0) {
                time += m.getSurplusTime();
            }
            else {
                time += m.needTime;
            }
            if (m.auid === uid && time > maxTime) {
                maxTime = time;
            }
        });
        return { time: maxTime };
    };
    // 获取受伤的士兵
    PlayerModel.prototype.getInjuryPawns = function () {
        return this.injuryPawns;
    };
    PlayerModel.prototype.updateInjuryPawns = function (datas) {
        this.injuryPawns = datas;
    };
    PlayerModel.prototype.addInjuryPawn = function (data) {
        this.injuryPawns.push(data);
        this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
    };
    PlayerModel.prototype.removeInjuryPawn = function (uid) {
        this.injuryPawns.remove('uid', uid);
        this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
    };
    // 获取治疗中的士兵
    PlayerModel.prototype.getCuringPawnsQueue = function () {
        return this.curingPawns;
    };
    PlayerModel.prototype.updatePawnCuringQueue = function (datas, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.curingPawns.length = 0;
        for (var i = 0; i < datas.length; i++) {
            var data = new PawnCureInfoObj_1.default().fromSvr(datas[i]);
            // 添加治疗完成消息
            if (data.surplusTime > 0) {
                // const type = Math.floor(data.id / 100)
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_101',
                    params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv]), 'ui.button_cure'],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                });
            }
            this.curingPawns.push(data);
        }
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_CURING_QUEUE, this.mainCityIndex);
    };
    // 获取士兵练级队列
    PlayerModel.prototype.getPawnLevelingQueues = function () { return this.pawnLevelingQueues; };
    PlayerModel.prototype.updatePawnLevelingQueue = function (datas, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.pawnLevelingQueues = datas.map(function (m) {
            var data = new PawnLevelingInfoObj_1.default().fromSvr(m);
            // 添加训练完成消息
            if (data.surplusTime > 0) {
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_104',
                    params: ['pawnText.name_' + data.id, data.lv],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                });
            }
            return data;
        });
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_LVING_QUEUE, this.mainCityIndex);
    };
    // 是否在队列中
    PlayerModel.prototype.isInPawnLvingQueue = function (uid) { return this.pawnLevelingQueues.has('puid', uid); };
    // 获取某个军队士兵练级剩余时间
    PlayerModel.prototype.getSumLvingTimeByArmy = function (uid) {
        var maxTime = 0, isBattleing = GameHelper_1.gameHpr.isBattleingByIndex(this.mainCityIndex), pause = true;
        var time = 0;
        this.pawnLevelingQueues.forEach(function (m) {
            if (m.surplusTime > 0) {
                time += isBattleing ? m.surplusTime : m.getSurplusTime();
                pause = isBattleing;
            }
            else {
                time += m.needTime;
            }
            if (m.auid === uid && time > maxTime) {
                maxTime = time;
            }
        });
        return { time: maxTime, pause: pause };
    };
    PlayerModel.prototype.getConfigPawnMap = function () { return this.configPawnMap; };
    // 获取配置士兵的装备信息
    PlayerModel.prototype.getConfigPawnInfo = function (pawnId) {
        if (!pawnId) {
            return null;
        }
        var info = this.configPawnMap[pawnId], uid = info === null || info === void 0 ? void 0 : info.equipUid;
        var conf = {
            equip: { uid: '', attrs: [] },
            skinId: (info === null || info === void 0 ? void 0 : info.skinId) || 0,
            attackSpeed: (info === null || info === void 0 ? void 0 : info.attackSpeed) || 0,
        };
        // 兼容装备
        if (uid) {
            var equip = this.getEquipByUid(uid);
            if (equip && (!equip.isExclusive() || equip.checkExclusivePawn(pawnId))) {
                conf.equip = { uid: equip.uid, attrs: equip.attrs };
            }
            else {
                conf.equip = { uid: '', attrs: [] };
            }
            if (info) {
                info.equipUid = conf.equip.uid;
            }
        }
        return conf;
    };
    PlayerModel.prototype.changeConfigPawnInfo = function (id, equipUid, skinId, attackSpeed) {
        this.configPawnMap[id] = { equipUid: equipUid, skinId: skinId, attackSpeed: attackSpeed };
    };
    // 改变皮肤配置
    PlayerModel.prototype.changeConfigPawnInfoByData = function (data) {
        var _a, _b, _c;
        var conf = this.configPawnMap[data.id];
        if (conf) {
            conf.skinId = data.skinId;
            conf.equipUid = ((_a = data.equip) === null || _a === void 0 ? void 0 : _a.uid) || '';
        }
        else {
            this.configPawnMap[data.id] = { equipUid: ((_b = data.equip) === null || _b === void 0 ? void 0 : _b.uid) || '', skinId: data.skinId, attackSpeed: data.attackSpeed };
        }
        var buildId = (_c = data.baseJson) === null || _c === void 0 ? void 0 : _c.spawn_build_id;
        if (buildId) {
            this.tempCanRecruitPawns[buildId] = null;
        }
    };
    // 获取城市皮肤配置
    PlayerModel.prototype.getCitySkinConfigMap = function () { return this.citySkinConfigMap; };
    PlayerModel.prototype.getStudySlots = function (slots) {
        var list = [];
        for (var k in slots) {
            var slot = slots[k];
            if (slot.isYetStudy()) {
                list.push(slot);
            }
        }
        return list;
    };
    // ---------------------------------------------------------政策----------------------------------------------------------------
    // 当前政策槽位
    PlayerModel.prototype.getPolicySlots = function () { return this.policySlots; };
    // 获取已经研究的政策ids
    PlayerModel.prototype.getStudyPolicySlots = function () { return this.getStudySlots(this.policySlots); };
    // 刷新
    PlayerModel.prototype.updatePolicySlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.policySlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, PolicyObj_1.default);
        ReddotHelper_1.reddotHelper.set('can_study_policy', GameHelper_1.gameHpr.checkStudySlotsReddot(this.policySlots));
        isEmit && this.emit(EventType_1.default.UPDATE_POLICY_SLOTS);
    };
    // ---------------------------------------------------------士兵----------------------------------------------------------------
    // 当前士兵槽位
    PlayerModel.prototype.getPawnSlots = function () { return this.pawnSlots; };
    PlayerModel.prototype.getStudyPawnSlots = function () { return this.getStudySlots(this.pawnSlots); };
    PlayerModel.prototype.getPawnSlotByLv = function (lv) { return this.pawnSlots[lv]; };
    PlayerModel.prototype.updatePawnSlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.pawnSlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, PawnSlotObj_1.default);
        this.checkPawnSlotInfo();
        ReddotHelper_1.reddotHelper.set('can_study_pawn', GameHelper_1.gameHpr.checkStudySlotsReddot(this.pawnSlots));
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_SLOTS);
    };
    // 检测槽位
    PlayerModel.prototype.checkPawnSlotInfo = function () {
        var _this = this;
        Constant_1.PAWN_SLOT_CONF.forEach(function (lv) {
            var slot = _this.pawnSlots[lv];
            if (slot) {
                slot.initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(slot.id));
            }
            else {
                _this.pawnSlots[lv] = new PawnSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
        });
        this.tempCanRecruitPawns = {};
    };
    // 获取可以招募的士兵
    PlayerModel.prototype.getCanRecruitPawns = function (buildId) {
        var _this = this;
        var slots = this.tempCanRecruitPawns[buildId];
        if (slots) {
            return slots;
        }
        slots = this.tempCanRecruitPawns[buildId] = [];
        var hasMap = {};
        // 加入固定的槽位信息
        Constant_1.PAWN_SLOT_CONF.forEach(function (lv) {
            var slot = _this.pawnSlots[lv];
            if (slot) {
                hasMap[slot.id] = true;
            }
            else {
                slot = new PawnSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
            slots.push(slot);
        });
        // 加入英雄殿固定的几个槽位信息
        this.heroSlots.forEach(function (m) {
            var _a;
            var id = (_a = m.hero) === null || _a === void 0 ? void 0 : _a.avatarPawn;
            if (!id) {
                slots.push(new PawnSlotObj_1.default().fromSvr({ lv: -m.lv }).init());
            }
            else if (!hasMap[id]) {
                hasMap[id] = true;
                slots.push(new PawnSlotObj_1.default().fromSvr({ lv: -m.lv, id: id }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(id)));
            }
        });
        // 加入直接解锁的士兵
        this.unlockPawnIds.forEach(function (id) {
            var _a;
            if (!hasMap[id] && ((_a = assetsMgr.getJsonData('pawnBase', id)) === null || _a === void 0 ? void 0 : _a.spawn_build_id) === buildId) {
                hasMap[id] = true;
                slots.push(new PawnSlotObj_1.default().fromSvr({ id: id, lv: 1 }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(id)));
            }
        });
        return slots;
    };
    // 获取可生产的器械
    PlayerModel.prototype.getCanProduceMachines = function (buildId) {
        var _this = this;
        var slots = this.tempCanRecruitPawns[buildId];
        if (slots) {
            return slots;
        }
        slots = this.tempCanRecruitPawns[buildId] = [];
        assetsMgr.getJson('pawnBase').datas.filter(function (m) { return m.spawn_build_id === buildId && !m.need_unlock; }).forEach(function (m) {
            slots.push(new PawnSlotObj_1.default().fromSvr({ id: m.id, lv: m.need_build_lv }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(m.id)));
        });
        return slots;
    };
    // ---------------------------------------------------------装备----------------------------------------------------------------
    // 当前装备槽位
    PlayerModel.prototype.getEquipSlots = function () { return this.equipSlots; };
    PlayerModel.prototype.getStudyEquipSlots = function () { return this.getStudySlots(this.equipSlots); };
    PlayerModel.prototype.getEquipSlotByLv = function (lv) { return this.equipSlots[lv]; };
    PlayerModel.prototype.updateEquipSlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.equipSlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, EquipSlotObj_1.default);
        this.checkEquipSlotInfo();
        ReddotHelper_1.reddotHelper.set('can_study_equip', GameHelper_1.gameHpr.checkStudySlotsReddot(this.equipSlots));
        isEmit && this.emit(EventType_1.default.UPDATE_EQUIP_SLOTS);
    };
    // 将打造的装备更新到槽位里面
    PlayerModel.prototype.checkEquipSlotInfo = function () {
        var _this = this;
        var equipMap = {};
        this.equips.forEach(function (m) { return equipMap[m.uid] = m; });
        // 刷新已经打造的信息
        Constant_1.EQUIP_SLOT_CONF.forEach(function (lv) {
            var slot = _this.equipSlots[lv];
            if (!slot) {
                _this.equipSlots[lv] = new EquipSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
            else if (slot === null || slot === void 0 ? void 0 : slot.isYetStudy()) {
                slot.equip = equipMap[slot.uid];
            }
        });
        this.tempCanForgeEquips = null; //重新获取
    };
    // 获取可以打造的装备
    PlayerModel.prototype.getCanForgeEquips = function () {
        var _this = this;
        if (this.tempCanForgeEquips && this.tempCanForgeEquips.length > 0) {
            return this.tempCanForgeEquips;
        }
        var hasMap = {};
        this.tempCanForgeEquips = [];
        Constant_1.EQUIP_SLOT_CONF.forEach(function (lv) {
            var slot = _this.equipSlots[lv];
            if (slot) {
                hasMap[slot.id] = true;
                _this.tempCanForgeEquips.push(slot);
            }
        });
        this.equips.forEach(function (m) {
            if (!hasMap[m.id]) {
                hasMap[m.id] = true;
                _this.tempCanForgeEquips.push(new EquipSlotObj_1.default().fromSvr({ id: m.id }).init().setEquip(m));
            }
        });
        // 加入直接解锁的
        this.unlockEquipIds.forEach(function (m) {
            if (!hasMap[m]) {
                hasMap[m] = true;
                _this.tempCanForgeEquips.push(new EquipSlotObj_1.default().fromSvr({ id: m, lv: 1 }).init());
            }
        });
        return this.tempCanForgeEquips;
    };
    // 打造装备
    PlayerModel.prototype.forgeEquip = function (uid, lockEffect) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, equip;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqForgeEquip({ uid: uid, lockEffect: lockEffect })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            equip = this.getEquipByUid(uid);
                            if (equip) {
                                equip.nextForgeFree = !!data.nextForgeFree;
                            }
                            this.updateCurrForgeEquip(data.currForgeEquip);
                            this.updateRewardItemsByFlags(data.cost);
                            this.setFreeForgeCount(data.freeForgeCount || 0);
                            this.emit(EventType_1.default.FORGE_EQUIP_BEGIN);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 立即完成打造
    PlayerModel.prototype.inDoneForge = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.IN_DONE_FORGE_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_InDoneForge', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.user.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取当前打造装备信息
    PlayerModel.prototype.getCurrForgeEquip = function () { return this.currForgeEquip; };
    PlayerModel.prototype.updateCurrForgeEquip = function (data) {
        this.currForgeEquip = data ? new ForgeEquipInfo_1.default().fromSvr(data) : null;
        if (this.currForgeEquip) {
            this.currForgeEquip.isYetForge = !!this.equips.find(function (m) { return m.id === data.id; });
        }
    };
    // 刷新装备
    PlayerModel.prototype.updateEquip = function (data) {
        var equip = this.equips.find(function (m) { return m.uid === data.uid; });
        if (equip) {
            equip.updateInfo(data);
        }
        else {
            this.equips.push(new EquipInfo_1.default().fromSvr(data));
            this.checkEquipSlotInfo();
        }
        return !equip; //是否新装备
    };
    PlayerModel.prototype.updateEquipInfo = function (data) {
        var isNew = this.updateEquip(data);
        this.updatePawnEquipAttr(data.uid, data.attrs);
        this.tempCanRecruitPawns = {}; //重新获取可训练的士兵 因为士兵有可能有装备
        return isNew;
    };
    PlayerModel.prototype.getEquips = function () { return this.equips; };
    PlayerModel.prototype.getEquipById = function (id) { return id ? this.equips.find(function (m) { return m.id === id; }) : null; };
    PlayerModel.prototype.getEquipByUid = function (uid) { return uid ? this.equips.find(function (m) { return m.uid === uid; }) : null; };
    // 获取士兵可以携带的装备列表
    PlayerModel.prototype.getPawnEquips = function (pawnId) {
        return this.equips.filter(function (m) { return !m.exclusive_pawn || m.exclusive_pawn === pawnId; });
    };
    // 获取已经参与融炼的装备idMap
    PlayerModel.prototype.getYetSmeltEquipIdMap = function (uid) {
        var smeltEquipIdMap = {};
        this.equips.forEach(function (m) {
            if (m.uid !== uid) {
                m.smeltEffects.forEach(function (m) { return smeltEquipIdMap[m.id] = true; });
            }
        });
        return smeltEquipIdMap;
    };
    // 添加打造消息通知
    PlayerModel.prototype.addForgeMessage = function (id, isNew) {
        GameHelper_1.gameHpr.addMessage({
            key: isNew ? 'ui.message_102' : 'ui.message_103',
            params: ['equipText.name_' + id],
            tag: id + '',
        });
    };
    // 获取正在融炼的装备
    PlayerModel.prototype.getCurrSmeltEquip = function () { return this.currSmeltEquip; };
    // 刷新融炼装备
    PlayerModel.prototype.updateCurrSmeltEquip = function (data) {
        this.currSmeltEquip = data ? new SmeltEquipInfo_1.default().fromSvr(data) : null;
    };
    // 融炼装备
    PlayerModel.prototype.smeltEquip = function (mainUid, viceIds) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_SmeltingEquip', { mainUid: mainUid, viceIds: viceIds }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateCurrSmeltEquip(data.currSmeltEquip);
                            this.setFixator(data.fixator);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 还原融炼
    PlayerModel.prototype.restoreSmeltEquip = function (mainUid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_RestoreSmeltEquip', { mainUid: mainUid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateEquipInfo(data.equip);
                            this.emit(EventType_1.default.UPDATE_EQUIP_ATTR, data.equip.uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 刷新士兵的装备等级
    PlayerModel.prototype.updatePawnEquipAttr = function (uid, attrs) {
        var areaCenter = GameHelper_1.gameHpr.areaCenter;
        this.baseArmys.forEach(function (m) {
            var _a;
            var area = areaCenter.getArea(m.index);
            if (area && !area.isBattleing()) {
                (_a = area.getArmyByUid(m.uid)) === null || _a === void 0 ? void 0 : _a.pawns.forEach(function (pawn) { return pawn.updateEquipAttr(uid, attrs); });
            }
        });
    };
    // 还原装备属性
    PlayerModel.prototype.restoreEquipAttr = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqRestoreForge({ uid: uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setIron(data.iron);
                            this.updateEquipInfo(data.equip);
                            this.emit(EventType_1.default.UPDATE_EQUIP_ATTR, data.equip.uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 添加打造消息通知
    PlayerModel.prototype.addSmeltMessage = function (id) {
        GameHelper_1.gameHpr.addMessage({
            key: 'ui.message_109',
            tag: id + '',
        });
    };
    // 获取要塞自动资源配置
    PlayerModel.prototype.getFortAutoSupports = function () { return this.fortAutoSupports; };
    // 是否自动支援
    PlayerModel.prototype.isForAutoSupport = function (index) {
        var _a;
        return !!((_a = this.fortAutoSupports.find(function (m) { return m.index === index; })) === null || _a === void 0 ? void 0 : _a.isAuto);
    };
    // 刷新自动支援配置
    PlayerModel.prototype.updateForAutoSupport = function (index, isAuto) {
        var data = this.fortAutoSupports.find(function (m) { return m.index === index; });
        if (data) {
            data.isAuto = isAuto;
        }
        else {
            this.fortAutoSupports.push({ index: index, isAuto: isAuto });
        }
    };
    // 建筑升级效果通知
    PlayerModel.prototype.addBuildEffectMessage = function (data) {
        if (!Enums_1.BUILD_NID[data.id]) {
            return;
        }
        // 查找定义的效果文本
        var key = 'ui.msg_build_effect_' + Enums_1.BUILD_NID[data.id].toLowerCase();
        var localeText = assetsMgr.lang(key);
        if (localeText === key) {
            return;
        }
        var diff = DBHelper_1.default.buildEffectDelta(data.id, data.lv - 1, data.lv);
        var sign = '-';
        var unit = '%';
        if ([Enums_1.BUILD_NID.MAIN, Enums_1.BUILD_NID.WAREHOUSE, Enums_1.BUILD_NID.GRANARY, Enums_1.BUILD_NID.FREE_BAZAAR, Enums_1.BUILD_NID.ALLI_BAZAAR].includes(data.id)) {
            sign = '+';
            unit = '';
        }
        var delay = 0.5;
        if (diff > 0) {
            GameHelper_1.gameHpr.addMessage({
                key: key,
                params: [" <color=" + Constant_1.COLOR_NORMAL.DONE + ">" + sign + diff + unit + "</color>"],
                tag: data.uid + '_desc',
                delay: Math.max(0, data.getSurplusTime() * 0.001 - 1) + delay,
            });
            delay += 0.5;
        }
        // 兵营特有的二级解锁刀盾兵提示
        if (data.id === Enums_1.BUILD_NID.BARRACKS && data.lv === 2) {
            // 解锁刀盾兵提示
            GameHelper_1.gameHpr.addMessage({
                key: 'ui.msg_build_effect_barracks_2',
                params: ["<color=" + Constant_1.COLOR_NORMAL.DONE + "> " + assetsMgr.lang('pawnText.name_3201') + "</color>"],
                tag: data.uid + '_desc_2',
                delay: Math.max(0, data.getSurplusTime() * 0.001 - 1) + delay,
            });
        }
    };
    // 修建队列
    PlayerModel.prototype.getBtQueues = function () { return this.btQueues; };
    PlayerModel.prototype.updateBtQueue = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.btQueues = datas.map(function (m) {
            var data = new BTInfoObj_1.default().fromSvr(m);
            // 添加训练完成消息
            if (data.surplusTime > 0) {
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_101',
                    params: ['buildText.name_' + data.id, data.lv > 1 ? 'ui.button_up' : 'ui.button_build'],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                });
                _this.addBuildEffectMessage(data);
            }
            return data;
        });
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_BT_QUEUE);
            // 如果当前是在做引导 并且没有修建兵营的队列了
            if (GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.CHOOSE_BTING_BUTTON) && !this.btQueues.has('id', Enums_1.BUILD_NID.BARRACKS)) {
                GameHelper_1.gameHpr.guide.gotoNextStep(GuideConfig_1.GuideTagType.CHECK_CAN_XL_PAWN, true);
            }
        }
    };
    PlayerModel.prototype.removeLocalBTQueues = function (uid) {
        this.btQueues.remove('uid', uid);
    };
    // 取消修建
    PlayerModel.prototype.cancelBtToServer = function (index, uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqCancelBT({ index: index, uid: uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.updateBtQueue(data.queues);
                        this.updateOutputByFlags(data.output);
                        GameHelper_1.gameHpr.delMessageByTag(uid);
                        GameHelper_1.gameHpr.delMessageByTag(uid + '_desc');
                        GameHelper_1.gameHpr.delMessageByTag(uid + '_desc_2');
                        return [2 /*return*/];
                }
            });
        });
    };
    // 立即完成
    PlayerModel.prototype.inDoneBt = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.IN_DONE_BT_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqInDoneBt()];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            audioMgr.playSFX('common/sound_ui_023');
                            this.btQueues.forEach(function (m) {
                                GameHelper_1.gameHpr.delMessageByTag(m.uid);
                                GameHelper_1.gameHpr.message.delayEndByTag(m.uid + '_desc');
                                GameHelper_1.gameHpr.message.delayEndByTag(m.uid + '_desc_2');
                            });
                            this.updateBtQueue(data.queues);
                            this.user.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 是否有建筑在队列中
    PlayerModel.prototype.hasBuildInBtQueue = function (uid) {
        var it = this.btQueues.find(function (m) { return m.uid === uid; });
        return !!(it === null || it === void 0 ? void 0 : it.getSurplusTime());
    };
    PlayerModel.prototype.getBuildBtInfo = function (uid) {
        return this.btQueues.find(function (m) { return m.uid === uid; });
    };
    // 获取军队列表
    PlayerModel.prototype.getAllArmys = function (interval, wait) {
        if (interval === void 0) { interval = 0.5; }
        if (wait === void 0) { wait = true; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isCapture()) {
                            return [2 /*return*/, []]; //如果被沦陷了 直接返回
                        }
                        else if (interval > 0 && this.lastReqArmysTime > 0 && Date.now() - this.lastReqArmysTime <= interval * 1000) {
                            return [2 /*return*/, this.tempArmyList];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetPlayerArmys(wait)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmysTime = Date.now();
                        this.tempArmyList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        // 统计宝箱
                        this.tempArmyList.forEach(function (army) {
                            army.treasures = [];
                            army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
                        });
                        return [2 /*return*/, this.tempArmyList];
                }
            });
        });
    };
    PlayerModel.prototype.getTempArmyList = function () { return this.tempArmyList; };
    // 获取选择军队列表
    PlayerModel.prototype.getSelectArmys = function (index, type, interval) {
        if (interval === void 0) { interval = 1; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isCapture()) {
                            return [2 /*return*/, { list: [], canGotoCount: 0 }]; //如果被沦陷了 直接返回
                        }
                        else if (interval > 0 && this.lastReqSelectArmysTime > 0 && Date.now() - this.lastReqSelectArmysTime <= interval * 1000) {
                            return [2 /*return*/, { err: this.tempSelectArmyErr, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetSelectArmys({ index: index, type: type })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqSelectArmysTime = Date.now();
                        this.tempSelectArmyErr = err;
                        this.tempSelectArmyList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.tempSelectArmyCanGotoCount = (data === null || data === void 0 ? void 0 : data.canGotoCount) || 0;
                        // 统计宝箱
                        this.tempSelectArmyList.forEach(function (army) {
                            army.treasures = [];
                            army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
                        });
                        return [2 /*return*/, { err: err, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }];
                }
            });
        });
    };
    PlayerModel.prototype.getTempSelectArmyList = function () { return this.tempSelectArmyList; };
    // 刷新临时军队宝箱信息
    PlayerModel.prototype.updateTempArmyTreasureInfo = function (treasures, auid, puid) {
        this.updateTempArmyTreasureInfoOne(this.tempArmyList, treasures, auid, puid);
        this.updateTempArmyTreasureInfoOne(this.tempSelectArmyList, treasures, auid, puid);
        this.emit(EventType_1.default.UPDATE_ARMY_TREASURE, auid);
    };
    PlayerModel.prototype.updateTempArmyTreasureInfoOne = function (armys, treasures, auid, puid) {
        var army = armys.find(function (m) { return m.uid === auid; });
        if (army) {
            var pawn = army.pawns.find(function (m) { return m.uid === puid; });
            if (pawn) {
                pawn.treasures.length = 0;
                pawn.treasures.pushArr(treasures);
                army.treasures.length = 0;
                army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
            }
        }
    };
    // 刷新临时军队所在位置
    PlayerModel.prototype.updateTempArmyIndex = function (data) {
        this.updateTempArmyIndexOne(this.tempArmyList, data);
        this.updateTempArmyIndexOne(this.tempSelectArmyList, data);
        this.emit(EventType_1.default.UPDATE_ARMY_AREA_INDEX, data.uid, data.index);
    };
    PlayerModel.prototype.updateTempArmyIndexOne = function (armys, data) {
        var _a;
        var army = armys.find(function (m) { return m.uid === (data === null || data === void 0 ? void 0 : data.uid); });
        if (army) {
            army.index = (_a = data === null || data === void 0 ? void 0 : data.index) !== null && _a !== void 0 ? _a : army.index;
        }
    };
    // 获取商人列表
    PlayerModel.prototype.getMerchants = function () { return this.merchants; };
    // 刷新商人列表
    PlayerModel.prototype.updateMerchants = function (datas) {
        this.merchants = datas.map(function (m) { return new MerchantObj_1.default().fromSvr(m); });
        this.emit(EventType_1.default.UPDATE_MERCHANTS);
    };
    // 获取一个商人的运输量
    PlayerModel.prototype.getMerchantTransitCap = function () { return 1000 + (GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.TRANSIT_CD) > 0 ? 1000 : 0); };
    // 获取军队记录
    PlayerModel.prototype.getArmyMarchRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (GameHelper_1.gameHpr.isNoviceMode) {
                            return [2 /*return*/, []];
                        }
                        else if (this.lastReqArmyMarchRecordTime > 0 && Date.now() - this.lastReqArmyMarchRecordTime <= 5000) {
                            return [2 /*return*/, this.tempArmyMarchRecordList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetArmyRecords', { isBattle: false })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmyMarchRecordTime = Date.now();
                        if (err) {
                            this.tempArmyMarchRecordList = [];
                        }
                        else {
                            this.tempArmyMarchRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        }
                        return [2 /*return*/, this.tempArmyMarchRecordList];
                }
            });
        });
    };
    PlayerModel.prototype.getArmyBattleRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqArmyBattleRecordTime > 0 && Date.now() - this.lastReqArmyBattleRecordTime <= 5000) {
                            return [2 /*return*/, this.tempArmyBattleRecordList];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetBattleRecordsList()];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmyBattleRecordTime = Date.now();
                        if (err) {
                            this.tempArmyBattleRecordList = [];
                        }
                        else {
                            this.tempArmyBattleRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        }
                        return [2 /*return*/, this.tempArmyBattleRecordList];
                }
            });
        });
    };
    // 获取市场记录
    PlayerModel.prototype.getBazaarRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, uid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqBazaarRecordTime > 0 && Date.now() - this.lastReqBazaarRecordTime <= 5000) {
                            return [2 /*return*/, this.tempBazaarRecordList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetBazaarRecords', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        uid = GameHelper_1.gameHpr.getUid();
                        this.lastReqBazaarRecordTime = Date.now();
                        this.tempBazaarRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.tempBazaarRecordList.forEach(function (m) {
                            var _a, _b, _c;
                            m.nickname = (_a = m.names) === null || _a === void 0 ? void 0 : _a[0];
                            if (m.type === 6) {
                                m.type = m.type * 10 + (m.owner === uid ? 1 : 0);
                                m.nickname = m.owner === uid ? (_b = m.names) === null || _b === void 0 ? void 0 : _b[1] : (_c = m.names) === null || _c === void 0 ? void 0 : _c[0];
                            }
                            var res = m.res || {};
                            m.res0 = res.resType !== undefined ? new CTypeObj_1.default().init(res.resType, 0, res.resCount) : null;
                            m.res1 = res.costType !== undefined ? new CTypeObj_1.default().init(res.costType, 0, res.costCount) : null;
                            m.actRes = res.actCount !== undefined ? new CTypeObj_1.default().init(res.resType, 0, res.actCount) : null;
                        });
                        return [2 /*return*/, this.tempBazaarRecordList];
                }
            });
        });
    };
    // 获取新手任务列表
    PlayerModel.prototype.getGuideTasks = function () {
        return this.guideTasks;
    };
    PlayerModel.prototype.updateGuideTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.guideTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'guideTask');
            task && _this.guideTasks.push(task);
        });
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
        if (this.guideTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('guide_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('guide_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('guide_task');
            ReddotHelper_1.reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1);
        }
    };
    // 更新新手任务进度
    PlayerModel.prototype.updateGuideTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (info) {
                var _a;
                var data = _this.guideTasks.find(function (m) { return m.id === info.id; });
                if (!data) {
                    var task = new TaskObj_1.default().init(info, 'guideTask');
                    task && _this.guideTasks.push(task);
                }
                else {
                    (_a = data.cond) === null || _a === void 0 ? void 0 : _a.updateProgress(info.progress);
                }
            });
            this.updateGuideTaskState(false);
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetGuideTask = function () {
        var task = null;
        this.guideTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateGuideTaskState = function (isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (this.guideTasks.length === 0) {
            return;
        }
        this.guideTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetGuideTask();
        ReddotHelper_1.reddotHelper.set('guide_task', !!task);
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkGuideTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetGuideTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimTaskReward = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqClaimTaskReward({ id: id })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateGuideTasks(data.tasks);
                            this.updateGuideTaskState();
                            this.updateTodayTasks(data.todayTasks || []);
                            this.updateTodayTaskState();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取每日任务列表
    PlayerModel.prototype.getTodayTasks = function () {
        return this.todayTasks;
    };
    PlayerModel.prototype.updateTodayTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.todayTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'todayTask');
            task && _this.todayTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        if (this.todayTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('today_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('today_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('today_task');
            ReddotHelper_1.reddotHelper.register('today_task', this.checkTodayTaskState, this, 1);
        }
    };
    // 更新每日任务进度
    PlayerModel.prototype.updateTodayTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (taskInfo) { var _a, _b; return (_b = (_a = _this.todayTasks.find(function (m) { return m.id === taskInfo.id; })) === null || _a === void 0 ? void 0 : _a.cond) === null || _b === void 0 ? void 0 : _b.updateProgress(taskInfo.progress); });
            this.updateTodayTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetTodayTask = function () {
        var task = null;
        this.todayTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateTodayTaskState = function () {
        if (this.todayTasks.length === 0) {
            if (this.guideTasks.length === 0) {
                this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE);
            }
            return;
        }
        this.todayTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetTodayTask();
        ReddotHelper_1.reddotHelper.set('today_task', !!task);
        this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkTodayTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetTodayTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimTodayTaskReward = function (id, treasureIndex, selectIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ClaimTodayTaskReward', { id: id, treasureIndex: treasureIndex, selectIndex: selectIndex }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateTodayTasks(data.todayTasks);
                            this.updateTodayTaskState();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取其他任务列表
    PlayerModel.prototype.getOtherTasks = function () {
        return this.otherTasks;
    };
    PlayerModel.prototype.updateOtherTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.otherTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'otherTask');
            task && _this.otherTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        if (this.otherTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('other_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('other_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('other_task');
            ReddotHelper_1.reddotHelper.register('other_task', this.checkOtherTaskState, this, 1);
        }
    };
    // 更新其他任务进度
    PlayerModel.prototype.updateOtherTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (info) {
                var _a;
                var data = _this.otherTasks.find(function (m) { return m.id === info.id; });
                if (data) {
                    (_a = data.cond) === null || _a === void 0 ? void 0 : _a.updateProgress(info.progress);
                }
                else {
                    var task = new TaskObj_1.default().init(info, 'otherTask');
                    task && _this.otherTasks.push(task);
                }
            });
            this.updateOtherTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetOtherTask = function () {
        var task = null;
        this.otherTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateOtherTaskState = function () {
        if (this.otherTasks.length === 0) {
            return;
        }
        this.otherTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetOtherTask();
        ReddotHelper_1.reddotHelper.set('other_task', !!task);
        this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkOtherTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetOtherTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimOtherTaskReward = function (id, treasureIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ClaimOtherTaskReward', { id: id, treasureIndex: treasureIndex }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateOtherTasks(data.otherTasks);
                            if (this.otherTasks.length === 0) {
                                this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE);
                            }
                            else {
                                this.updateOtherTaskState();
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取所有任务
    PlayerModel.prototype.getPlayerAllTasks = function () {
        var tasks = [];
        tasks.pushArr(this.getGuideTasks());
        if (tasks.length === 0) {
            tasks.pushArr(this.getTodayTasks());
        }
        tasks.pushArr(this.getOtherTasks());
        return tasks;
    };
    PlayerModel.prototype.getPlayerTaskCount = function () {
        return (this.guideTasks.length || this.todayTasks.length) + this.otherTasks.length;
    };
    PlayerModel.prototype.getCangetPlayerTask = function () {
        return this.getCangetGuideTask() || this.getCangetTodayTask() || this.getCangetOtherTask();
    };
    // 刷新添加产量时间
    PlayerModel.prototype.updateAddOutputTime = function (timeMap) {
        this.addOutputSurplusTime = timeMap || {};
        this.getAddOutputTime = Date.now();
    };
    // 购买添加产量时间
    PlayerModel.prototype.buyAddOutputTime = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.ADD_OUTPUT_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_BuyAddOutput', { type: type }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateAddOutputTime(data.addOutputSurplusTime);
                            this.user.setGold(data.gold);
                            this.updateOutputByFlags(data.output);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 添加隐藏的私聊频道
    PlayerModel.prototype.addHidePChatChannels = function (channel, uid) {
        this.hidePChatChannels[channel] = uid;
    };
    PlayerModel.prototype.getOccupyLandCountMap = function () { return this.occupyLandCountMap; };
    // 刷新攻占领地数量
    PlayerModel.prototype.updateOccupyLandCountMap = function (data) {
        this.occupyLandCountMap = {};
        for (var key in data) {
            this.occupyLandCountMap[key] = data[key].arr;
        }
    };
    // 击杀数量
    PlayerModel.prototype.getKillRecordMap = function () { return this.killRecordMap; };
    PlayerModel.prototype.recordKillCount = function (id, count) {
        var val = this.killRecordMap[id] || 0;
        this.killRecordMap[id] = val + count;
    };
    // 记录结果
    PlayerModel.prototype.getBattleForecastRetData = function (index, key) {
        var _a;
        return (_a = this.tempBattleForecastRets.find(function (m) { return m.index === index && m.key === key; })) === null || _a === void 0 ? void 0 : _a.data;
    };
    PlayerModel.prototype.setBattleForecastRetMap = function (index, key, data) {
        var it = this.tempBattleForecastRets.find(function (m) { return m.index === index && m.key === key; });
        if (it) {
            it.data = data;
            return;
        }
        else if (this.tempBattleForecastRets.length > 30) {
            this.tempBattleForecastRets.shift();
        }
        this.tempBattleForecastRets.push({ index: index, key: key, data: data });
    };
    // 刷新士兵的装备等级
    PlayerModel.prototype.updatePawnHeroAttr = function (id, attrs) {
        var areaCenter = GameHelper_1.gameHpr.areaCenter;
        this.baseArmys.forEach(function (m) {
            var _a;
            var area = areaCenter.getArea(m.index);
            if (area && !area.isBattleing()) {
                (_a = area.getArmyByUid(m.uid)) === null || _a === void 0 ? void 0 : _a.pawns.forEach(function (pawn) { return pawn.updateHeroAttr(id, attrs); });
            }
        });
    };
    // 刷新单个槽位信息
    PlayerModel.prototype.updateHeroSlotOne = function (data) {
        var index = Constant_1.HERO_SLOT_LV_COND.indexOf(data.lv);
        if (index === -1) {
            return;
        }
        var info = this.heroSlots[index];
        if (info) {
            info.fromSvr(data);
        }
        else {
            this.heroSlots[index] = new HeroSlotObj_1.default().fromSvr(data);
        }
        this.emit(EventType_1.default.UPDATE_HERO_SLOT_INFO);
    };
    // 供奉英雄
    PlayerModel.prototype.worshipHero = function (index, id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqWorshipHero({ index: index, id: id })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateHeroSlotOne(data.slot);
                            this.setUnlockPawnIds(data.unlockPawnIds);
                            this.updatePawnSlots(data.pawnSlots);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 改变士兵画像
    PlayerModel.prototype.changePawnPortrayal = function (index, armyUid, uid, portrayalId) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnPortrayal({ index: index, armyUid: armyUid, uid: uid, portrayalId: portrayalId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateHeroSlotOne(data.slot);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 检测某个士兵是否可化身
    PlayerModel.prototype.checkCanAvatarPawn = function (id) {
        return this.heroSlots.some(function (m) { return !!m.hero && !m.avatarArmyUID && !m.isDie() && m.hero.avatarPawn === id; });
    };
    PlayerModel.prototype.getHeroSlotByPawnId = function (id) {
        return this.heroSlots.find(function (m) { var _a; return ((_a = m.hero) === null || _a === void 0 ? void 0 : _a.avatarPawn) === id; });
    };
    PlayerModel.prototype.findMapMark = function (point) {
        return this.mapMarks.find(function (m) { return m.point.equals(point); });
    };
    PlayerModel.prototype.getMapMarks = function () {
        return this.mapMarks;
    };
    // 添加标记
    PlayerModel.prototype.addMapMark = function (data) {
        var mark = this.findMapMark(data.point);
        if (mark) {
            mark.name = data.name;
        }
        else {
            this.mapMarks.push(data);
        }
        this.emit(EventType_1.default.UPDATE_MAP_MARK);
    };
    // 删除标记
    PlayerModel.prototype.removeMapMark = function (point) {
        return __awaiter(this, void 0, void 0, function () {
            var index, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        index = this.mapMarks.findIndex(function (m) { return m.point.equals(point); });
                        if (index === -1) {
                            return [2 /*return*/, ''];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_RemoveMapMark', { point: point }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.mapMarks.splice(index, 1);
                            this.emit(EventType_1.default.UPDATE_MAP_MARK);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 更新玩家信息
    PlayerModel.prototype.OnUpdatePlayerInfo = function (data) {
        var _this = this;
        cc.log('OnUpdatePlayerInfo', data.list);
        data.list.forEach(function (m) {
            var data = m['data_' + m.type];
            if (m.type === Enums_1.NotifyType.OUTPUT) { //产出
                _this.updateOutputByFlags(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_ITEMS) { //更新通用物品
                _this.updateRewardItemsByFlags(data);
            }
            else if (m.type === Enums_1.NotifyType.BT_QUEUE) { //建造队列
                _this.updateBtQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.BUILD_UP) { //建筑升级
                _this.updateMainBuildInfo(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_DRILL_QUEUE) { //训练队列
                _this.updatePawnDrillQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_LEVELING_QUEUE) { //练级队列
                _this.updatePawnLevelingQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_CURING_QUEUE) { // 刷新士兵治疗队列
                _this.updatePawnCuringQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_INJURY_ADD) { // 添加受伤的士兵
                _this.addInjuryPawn(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_INJURY_REMOVE) { // 受伤士兵移除
                _this.removeInjuryPawn(data);
            }
            else if (m.type === Enums_1.NotifyType.ARMY_DIST) { //军队分布
                _this.updateArmyDists(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_MERCHANT) { //更新商人
                _this.updateMerchants(data);
            }
            else if (m.type === Enums_1.NotifyType.FORGE_EQUIP_RET) { //打造装备结果
                _this.updateCurrForgeEquip(null);
                var isNew = _this.updateEquipInfo(data);
                _this.emit(EventType_1.default.FORGE_EQUIP_COMPLETE, data.uid);
                _this.addForgeMessage(data.uid.split('_')[0], isNew);
            }
            else if (m.type === Enums_1.NotifyType.SMELT_EQUIP_RET) { //融炼装备结果
                _this.updateCurrSmeltEquip(null);
                _this.updateEquipInfo(data);
                _this.emit(EventType_1.default.SMELT_EQUIP_COMPLETE, data.uid);
                _this.addSmeltMessage(data.uid.split('_')[0]);
            }
            else if (m.type === Enums_1.NotifyType.ADD_OUTPUT_TIME) { //刷新添加的产量时间
                _this.updateAddOutputTime(data);
                _this.emit(EventType_1.default.UPDATE_ADD_OUTPUT_TIME);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_GENERAL_TASKS) { //刷新常规任务
                GameHelper_1.gameHpr.task.updateGeneralTasks(data || []);
            }
            else if (m.type === Enums_1.NotifyType.NEW_TREASURE) { //是否有新的宝箱
                ReddotHelper_1.reddotHelper.set('treasure_main', !!data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TODAY_INFO) { //每日信息
                _this.todayOccupyCellCount = data.todayOccupyCellCount || 0;
                _this.todayReplacementCount = data.todayReplacementCount || 0;
                _this.cellTondenCount = data.cellTondenCount || 0;
                _this.updateTodayTasks(data.todayTasks || []);
                _this.updateTodayTaskState();
                _this.emit(EventType_1.default.UPDATE_TONDEN_COUNT);
            }
            else if (m.type === Enums_1.NotifyType.CELL_TONDEN_COUNT) { //刷新屯田次数
                _this.cellTondenCount = data || 0;
                _this.emit(EventType_1.default.UPDATE_TONDEN_COUNT);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TASKS) { //刷新任务进度
                _this.updateGuideTasksProgress(data.guideTasks || []);
                _this.updateTodayTasksProgress(data.todayTasks || []);
                _this.updateOtherTasksProgress(data.otherTasks || []);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_LAND_SCORE) { //刷新玩家领地积分
                _this.landScore = data.landScore || 0;
                _this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1;
                _this.updateOccupyLandCountMap(data.occupyLandCountMap || {});
            }
            else if (m.type === Enums_1.NotifyType.CHANGE_HERO_SLOT_INFO) { //英雄殿信息改变
                _this.updateHeroSlotOne(data);
            }
            else if (m.type === Enums_1.NotifyType.COMPENSATE) { //战损补偿通知
                ViewHelper_1.viewHelper.showMessageBox('ui.battle_compensate_tip', { okText: 'ui.button_gotit' });
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_POLICY_SLOT) { //政策槽位更新
                _this.updatePolicySlots(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_EQUIP_SLOT) { //装备槽位更新
                _this.updateEquipSlots(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_PAWN_SLOT) { //士兵槽位更新
                _this.updatePawnSlots(data);
            }
        });
    };
    __decorate([
        ut.syncLock
    ], PlayerModel.prototype, "getBazaarRecords", null);
    PlayerModel = __decorate([
        mc.addmodel('player')
    ], PlayerModel);
    return PlayerModel;
}(mc.BaseModel));
exports.default = PlayerModel;

cc._RF.pop();