{"version": 3, "sources": ["assets\\app\\script\\model\\main\\PlayerModel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAwD;AACxD,6DAA2D;AAC3D,2DAA0M;AAE1M,qDAAmD;AACnD,qDAAwH;AACxH,0DAAoD;AACpD,yDAAmD;AACnD,6DAAwD;AACxD,2DAAyD;AACzD,2DAAyD;AACzD,iEAA+D;AAC/D,6DAA2D;AAG3D,qDAA+C;AAC/C,yCAAmC;AACnC,+CAAyC;AACzC,yCAAmC;AACnC,mDAA6C;AAE7C,yCAAmC;AACnC,uDAAiD;AACjD,6DAAuD;AACvD,yCAAmC;AACnC,mDAA6C;AAC7C,6CAAuC;AAEvC,oDAAmD;AACnD,6CAAuC;AACvC,qDAA+C;AAC/C,+CAAyC;AACzC,6CAAuC;AAGvC;;GAEG;AAEH;IAAyC,+BAAY;IAArD;QAAA,qEA2yDC;QAzyDW,SAAG,GAAiB,IAAI,CAAA;QACxB,UAAI,GAAc,IAAI,CAAA;QACtB,cAAQ,GAAkB,IAAI,CAAA;QAE9B,cAAQ,GAAW,CAAC,CAAA,CAAC,OAAO;QAE5B,YAAM,GAAc,IAAI,CAAA,CAAC,GAAG;QAC5B,YAAM,GAAc,IAAI,CAAA,CAAC,GAAG;QAC5B,WAAK,GAAc,IAAI,CAAA,CAAC,GAAG;QAC3B,aAAO,GAAW,CAAC,CAAA,CAAC,KAAK;QACzB,UAAI,GAAW,CAAC,CAAA,CAAC,GAAG;QACpB,cAAQ,GAAW,CAAC,CAAA,CAAC,IAAI;QACzB,aAAO,GAAW,CAAC,CAAA,CAAC,KAAK;QACzB,mBAAa,GAAW,CAAC,CAAA,CAAC,MAAM;QAChC,gBAAU,GAAW,CAAC,CAAA,CAAC,MAAM;QAC7B,kBAAY,GAAW,CAAC,CAAA,CAAC,MAAM;QAC/B,aAAO,GAAW,CAAC,CAAA,CAAC,OAAO;QAC3B,iBAAW,GAAQ,IAAI,CAAA,CAAC,QAAQ;QAChC,mBAAa,GAAW,CAAC,CAAA,CAAC,QAAQ;QAElC,mBAAa,GAAW,CAAC,CAAA,CAAC,QAAQ;QAClC,gBAAU,GAA8C,EAAE,CAAA,CAAC,WAAW;QACtE,mBAAa,GAAa,EAAE,CAAA,CAAC,aAAa;QAC1C,oBAAc,GAAa,EAAE,CAAA,CAAC,aAAa;QAC3C,cAAQ,GAAgB,EAAE,CAAA,CAAC,SAAS;QACpC,uBAAiB,GAAoC,IAAI,GAAG,EAAE,CAAA,CAAC,QAAQ;QACvE,wBAAkB,GAA0B,EAAE,CAAA,CAAC,QAAQ;QACvD,eAAS,GAAmB,EAAE,CAAA,CAAC,SAAS;QACxC,iBAAW,GAAsC,EAAE,CAAA;QACnD,eAAS,GAAkB,EAAE,CAAA,CAAC,MAAM;QACpC,gBAAU,GAAc,EAAE,CAAA,CAAC,QAAQ;QACnC,gBAAU,GAAc,EAAE,CAAA,CAAC,QAAQ;QACnC,gBAAU,GAAc,EAAE,CAAA,CAAC,QAAQ;QACnC,YAAM,GAAgB,EAAE,CAAA,CAAC,SAAS;QAClC,oBAAc,GAAmB,IAAI,CAAA,CAAC,UAAU;QAChD,oBAAc,GAAmB,IAAI,CAAA,CAAC,aAAa;QACnD,mBAAa,GAAiF,EAAE,CAAA,CAAC,SAAS;QAC1G,uBAAiB,GAA8B,EAAE,CAAA,CAAC,WAAW;QAC7D,sBAAgB,GAAU,EAAE,CAAA,CAAC,UAAU;QACvC,0BAAoB,GAA8B,EAAE,CAAA,CAAC,UAAU;QAC/D,sBAAgB,GAAW,CAAC,CAAA;QAC5B,iBAAW,GAAiC,EAAE,CAAA,CAAC,UAAU;QACzD,gBAAU,GAAoC,EAAE,CAAA,CAAC,UAAU;QAC3D,eAAS,GAAmC,EAAE,CAAA,CAAC,UAAU;QACzD,eAAS,GAAkB,EAAE,CAAA,CAAC,UAAU;QACxC,uBAAiB,GAA8B,EAAE,CAAA,CAAC,SAAS;QAC3D,uBAAiB,GAAW,CAAC,CAAA,CAAC,QAAQ;QACtC,0BAAoB,GAAW,CAAC,CAAA,CAAC,QAAQ;QACzC,0BAAoB,GAAW,CAAC,CAAA,CAAC,UAAU;QAC3C,2BAAqB,GAAW,CAAC,CAAA,CAAC,QAAQ;QAC1C,eAAS,GAAW,CAAC,CAAA,CAAC,MAAM;QAC5B,wBAAkB,GAAgC,EAAE,CAAA,CAAC,0BAA0B;QAC/E,6BAAuB,GAAW,CAAC,CAAA,CAAC,YAAY;QAChD,mBAAa,GAA8B,EAAE,CAAA,CAAC,oBAAoB;QAClE,cAAQ,GAAuC,EAAE,CAAA,CAAC,MAAM;QACxD,2BAAqB,GAAW,CAAC,CAAA,CAAC,UAAU;QAC5C,qBAAe,GAAW,CAAC,CAAA,CAAC,QAAQ;QACpC,wBAAkB,GAAW,CAAC,CAAA,CAAC,UAAU;QACzC,0BAAoB,GAAW,CAAC,CAAA,CAAC,UAAU;QAC3C,yBAAmB,GAAW,CAAC,CAAA,CAAC,UAAU;QAC1C,uBAAiB,GAAW,CAAC,CAAA,CAAC,UAAU;QACxC,oBAAc,GAAW,CAAC,CAAA,CAAC,WAAW;QACtC,eAAS,GAAY,KAAK,CAAA,CAAC,oBAAoB;QAE/C,kBAAY,GAAW,CAAC,CAAA,CAAC,QAAQ;QACjC,kBAAY,GAAmC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAA;QAC7E,iBAAW,GAAqB,EAAE,CAAA,CAAC,SAAS;QAC5C,iBAAW,GAAsB,EAAE,CAAA,CAAC,SAAS;QAC7C,4BAAsB,GAAW,CAAC,CAAA,CAAC,YAAY;QAC/C,uBAAiB,GAAW,EAAE,CAAA;QAC9B,wBAAkB,GAAoB,EAAE,CAAA,CAAC,WAAW;QACpD,gCAA0B,GAAW,CAAC,CAAA,CAAC,eAAe;QACtD,sBAAgB,GAAW,CAAC,CAAA,CAAC,YAAY;QACzC,kBAAY,GAAoB,EAAE,CAAA,CAAC,SAAS;QAC5C,gCAA0B,GAAW,CAAC,CAAA,CAAC,cAAc;QACrD,6BAAuB,GAAU,EAAE,CAAA,CAAC,QAAQ;QAC5C,iCAA2B,GAAW,CAAC,CAAA,CAAC,cAAc;QACtD,8BAAwB,GAAU,EAAE,CAAA,CAAC,QAAQ;QAC7C,6BAAuB,GAAW,CAAC,CAAA,CAAC,cAAc;QAClD,0BAAoB,GAAU,EAAE,CAAA,CAAC,QAAQ;QACzC,4BAAsB,GAAU,EAAE,CAAA,CAAC,cAAc;QACjD,8BAAwB,GAAQ,EAAE,CAAA,CAAC,aAAa;QAChD,wBAAkB,GAAmB,IAAI,CAAA;QACzC,yBAAmB,GAAqC,EAAE,CAAA;QAE1D,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;;IAmtD3C,CAAC;IAjtDU,8BAAQ,GAAf;QACI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;IAC7C,CAAC;IAED,QAAQ;IACK,0BAAI,GAAjB,UAAkB,IAAS,EAAE,OAAiB;;;;;wBAC1C,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;wBAC3B,IAAI,CAAC,aAAa,EAAE,CAAA;wBACpB,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAA;wBAC/B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAA;wBACzB,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAA;wBACnC,IAAI,CAAC,2BAA2B,GAAG,CAAC,CAAA;wBACpC,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAA;wBAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;wBAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAA;wBAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;wBACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA;wBAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,CAAA;wBAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;wBAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA;wBAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAA;wBACtC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAA;wBAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAS,CAAC,mBAAS,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBACzE,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAS,CAAC,mBAAS,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBACzE,IAAI,CAAC,KAAK,GAAG,IAAI,mBAAS,CAAC,mBAAS,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;wBACtE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAA;wBAC5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA;wBAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAA;wBAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAA;wBACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAA;wBAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,CAAA;wBAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBAC9C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBAC5D,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,kBAAkB,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBAClE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBAC1D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBACjD,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAA;wBAC9E,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,mBAAS,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAA1B,CAA0B,CAAC,CAAA;wBACtE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAA;wBAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAA;wBACrD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,EAAE,CAAA;wBACnD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;wBACnD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBACrD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBACjD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBACnD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;wBAC9C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;wBAC9C,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAA;wBAC9E,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAA;wBACrD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAA;wBACpD,IAAI,CAAC,kBAAkB,EAAE,CAAA,CAAC,UAAU;wBACpC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAA;wBAC1D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAA;wBAC1D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAA;wBAC5D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,CAAA;wBACpC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAA;wBAC5D,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,IAAI,CAAC,CAAA;wBAChE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAA;wBAC7C,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAA,CAAC,CAAC,CAAC,CAAA;wBAClG,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAA;wBAC5D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA;wBAChD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAA;wBACzC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAA;wBACtD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAA;wBAC1D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAA;wBACxD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAA;wBACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,CAAC,CAAA;wBAC9C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;wBACjC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBACnD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA,CAAC,sCAAsC;wBAC1F,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;wBACnD,UAAU;wBACV,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;6BACvC,CAAC,OAAO,EAAR,wBAAQ;wBACR,SAAS;wBACT,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,EAAA;;wBADhD,SAAS;wBACT,SAAgD,CAAA;;;wBAEpD,KAAK;wBACL,2BAAY,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;wBACxD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC5B,2BAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;4BACrC,2BAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;yBACzE;wBACD,OAAO;wBACP,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,yBAAyB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;;;;;KACxE;IAEM,2BAAK,GAAZ;QACI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;QACtE,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAA;QAChC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QACnB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QACnB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QAC9B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAA;QAC5B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAA;QAC5B,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAA;QACjC,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAA;QAClC,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAA;QAC9B,IAAI,CAAC,aAAa,EAAE,CAAA;IACxB,CAAC;IAEO,mCAAa,GAArB;QACI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;QAC9B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAA;IACjC,CAAC;IAEM,iCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IACtC,0CAAoB,GAA3B,cAAgC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC,EAAC,WAAW;IACxE,oCAAc,GAArB,cAA0B,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;IAC5C,+BAAS,GAAhB,sBAAqB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,QAAC,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAA,CAAA,CAAC,CAAC,EAAC,MAAM;IAC5E,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAChD,sCAAgB,GAAvB,cAA4B,OAAO,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA,CAAC,CAAC;IACxE,iCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IACtC,oCAAc,GAArB,cAA0B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAA,CAAC,CAAC;IAClD,mCAAa,GAApB,cAAyB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAA,CAAC,CAAC;IACnD,+BAAS,GAAhB,sBAAqB,OAAO,OAAA,IAAI,CAAC,MAAM,0CAAE,KAAK,KAAI,CAAC,CAAA,CAAC,CAAC;IAC9C,iCAAW,GAAlB,sBAAuB,OAAO,OAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,KAAI,CAAC,CAAA,CAAC,CAAC;IACjD,+BAAS,GAAhB,UAAiB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAAC,CAAC;IAC/E,kCAAY,GAAnB,UAAoB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAAC,CAAC;IAC5F,+BAAS,GAAhB,sBAAqB,OAAO,OAAA,IAAI,CAAC,MAAM,0CAAE,KAAK,KAAI,CAAC,CAAA,CAAC,CAAC;IAC9C,iCAAW,GAAlB,sBAAuB,OAAO,OAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,KAAI,CAAC,CAAA,CAAC,CAAC;IACjD,+BAAS,GAAhB,UAAiB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAAC,CAAC;IAC/E,kCAAY,GAAnB,UAAoB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAAC,CAAC;IAC5F,8BAAQ,GAAf,sBAAoB,OAAO,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,KAAI,CAAC,CAAA,CAAC,CAAC;IAC5C,gCAAU,GAAjB,sBAAsB,OAAO,OAAA,IAAI,CAAC,KAAK,0CAAE,MAAM,KAAI,CAAC,CAAA,CAAC,CAAC;IAC/C,8BAAQ,GAAf,UAAgB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAAC,CAAC;IAC7E,iCAAW,GAAlB,UAAmB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAAC,CAAC;IAC1F,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAChD,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IACjE,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;IACnE,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;IACjE,mCAAa,GAApB,cAAyB,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAC1C,qCAAe,GAAtB,cAA2B,OAAO,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;IAC9C,6CAAuB,GAA9B,cAAmC,OAAO,IAAI,CAAC,oBAAoB,CAAA,CAAC,CAAC;IAC9D,6CAAuB,GAA9B,cAAmC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAA,CAAC,CAAC;IACvE,0CAAoB,GAA3B,cAAgC,OAAO,IAAI,CAAC,iBAAiB,CAAA,CAAC,CAAC;IACxD,0CAAoB,GAA3B,UAA4B,GAAW,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAA,CAAC,CAAC;IAClE,kCAAY,GAAnB,cAAwB,OAAO,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC;IACxC,0CAAoB,GAA3B,cAAgC,OAAO,IAAI,CAAC,iBAAiB,CAAA,CAAC,CAAC;IACxD,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAChD,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;IAClD,6CAAuB,GAA9B,cAAmC,OAAO,IAAI,CAAC,oBAAoB,CAAA,CAAC,CAAC;IAC9D,6CAAuB,GAA9B,UAA+B,GAAW,IAAI,OAAO,IAAI,CAAC,oBAAoB,IAAI,GAAG,CAAA,CAAC,CAAC;IAChF,6CAAuB,GAA9B,cAAmC,OAAO,IAAI,CAAC,oBAAoB,CAAA,CAAC,CAAC;IAC9D,6CAAuB,GAA9B,UAA+B,GAAW,IAAI,OAAO,IAAI,CAAC,oBAAoB,IAAI,GAAG,CAAA,CAAC,CAAC;IAChF,8CAAwB,GAA/B,cAAoC,OAAO,IAAI,CAAC,qBAAqB,CAAA,CAAC,CAAC;IAChE,8CAAwB,GAA/B,UAAgC,GAAW,IAAI,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAA,CAAC,CAAC;IAC1E,2CAAqB,GAA5B,cAAiC,OAAO,IAAI,CAAC,kBAAkB,CAAA,CAAC,CAAC;IAC1D,2CAAqB,GAA5B,UAA6B,GAAW,IAAI,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAA,CAAC,CAAC;IACpE,6CAAuB,GAA9B,cAAmC,OAAO,IAAI,CAAC,oBAAoB,CAAA,CAAC,CAAC;IAC9D,6CAAuB,GAA9B,UAA+B,GAAW,IAAI,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAA,CAAC,CAAC;IACxE,4CAAsB,GAA7B,cAAkC,OAAO,IAAI,CAAC,mBAAmB,CAAA,CAAC,CAAC;IAC5D,4CAAsB,GAA7B,UAA8B,GAAW,IAAI,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAA,CAAC,CAAC;IACtE,0CAAoB,GAA3B,cAAgC,OAAO,IAAI,CAAC,iBAAiB,CAAA,CAAC,CAAC;IACxD,0CAAoB,GAA3B,UAA4B,GAAW,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAA,CAAC,CAAC;IAClE,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;IAClD,uCAAiB,GAAxB,UAAyB,GAAW,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,CAAA,CAAC,CAAC;IAC5D,gDAA0B,GAAjC,cAAsC,OAAO,IAAI,CAAC,uBAAuB,CAAA,CAAC,CAAC;IAEpE,kCAAY,GAAnB,cAAwB,OAAO,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC;IACxC,8CAAwB,GAA/B,cAAoC,OAAO,IAAI,CAAC,qBAAqB,CAAA,CAAC,CAAC;IAChE,wCAAkB,GAAzB,cAA8B,OAAO,IAAI,CAAC,eAAe,CAAA,CAAC,CAAC;IAEpD,sCAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC5D,CAAC;IAED,aAAa;IACN,oDAA8B,GAArC;QACI,IAAI,SAAS,GAAG,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,gBAAgB,CAAC,CAAA;QACvE,IAAI,oBAAO,CAAC,YAAY,EAAE;YACtB,SAAS,IAAI,CAAC,CAAA,CAAC,QAAQ;SAC1B;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAA;IAC7D,CAAC;IAED,eAAe;IACR,mDAA6B,GAApC;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAA;IAC3G,CAAC;IAED,eAAe;IACR,iDAA2B,GAAlC;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAA;IACvG,CAAC;IAED,kBAAkB;IACX,8CAAwB,GAA/B;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAA;IACtG,CAAC;IAED,aAAa;IACN,4CAAsB,GAA7B;QAAA,iBAWC;QAVG,IAAM,GAAG,GAAa,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAA;QACnG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAA;YACtB,GAAG,CAAC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,EAA9B,CAA8B,CAAC,CAAA;YAChD,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAA;aAC/E;SACJ;aAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAA;SAC9E;IACL,CAAC;IAEM,wCAAkB,GAAzB,UAA0B,KAAa;QACnC,IAAM,GAAG,GAAG,qBAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAS,CAAC,CAAA;QACxD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;QAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;QAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,oBAAS,GAAG,CAAC,CAAA;QAC/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,oBAAS,GAAG,CAAC,CAAA;IACnD,CAAC;IAED,cAAc;IACP,+CAAyB,GAAhC;QACI,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAO,CAAC,UAAU,EAAE,EAAE;YAC3C,OAAO,KAAK,CAAA;SACf;QACD,IAAM,MAAM,GAAG,uBAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QACzF,IAAM,MAAM,GAAG,uBAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QACzF,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,CAAA;IAC1G,CAAC;IAED,MAAM;IACC,gCAAU,GAAjB,cAAsB,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;IACpC,gCAAU,GAAjB,UAAkB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QACjD,IAAI,GAAG,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;YACjC,OAAM;SACT;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC9B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,eAAe,EAAE,GAAG,CAAC,CAAA;SAC5C;IACL,CAAC;IAED,IAAI;IACG,6BAAO,GAAd,cAAmB,OAAO,IAAI,CAAC,IAAI,CAAA,CAAC,CAAC;IAC9B,6BAAO,GAAd,UAAe,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAC9C,IAAI,GAAG,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;YACjC,OAAM;SACT;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC3B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,WAAW,EAAE,GAAG,CAAC,CAAA;SACxC;IACL,CAAC;IAED,KAAK;IACE,iCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IACtC,iCAAW,GAAlB,UAAmB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAClD,IAAI,GAAG,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;YACjC,OAAM;SACT;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC/B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,eAAe,EAAE,GAAG,CAAC,CAAA;SAC5C;IACL,CAAC;IAED,MAAM;IACC,gCAAU,GAAjB,cAAsB,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;IACpC,gCAAU,GAAjB,UAAkB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QACjD,IAAI,GAAG,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;YACjC,OAAM;SACT;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC9B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;SAC3C;IACL,CAAC;IAED,MAAM;IACC,gCAAU,GAAjB,cAAsB,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;IACpC,gCAAU,GAAjB,UAAkB,GAAW,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QACjD,IAAI,GAAG,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;YACjC,OAAM;SACT;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC9B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;SAC3C;IACL,CAAC;IAEM,uCAAiB,GAAxB,UAAyB,GAAa;QAClC,IAAI,CAAC,cAAc,GAAG,GAAG,IAAI,EAAE,CAAA;QAC/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA,CAAC,MAAM;IACzC,CAAC;IAEM,sCAAgB,GAAvB,UAAwB,GAAa;QACjC,IAAI,CAAC,aAAa,GAAG,GAAG,IAAI,EAAE,CAAA;QAC9B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAA,CAAC,MAAM;IACxC,CAAC;IAED,SAAS;IACF,qCAAe,GAAtB;QACI,OAAO,iCAAsB,GAAG,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,QAAQ,CAAC,CAAA;IACnF,CAAC;IAED,SAAS;IACF,oCAAc,GAArB,UAAsB,IAAS;QAC3B,IAAI,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,CAAA,EAAE;YACZ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;SAC1B;aAAM;YACH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,IAAM,GAAG,GAAa,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,kBAAkB,CAAC,CAAA;YAC7F,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAA;aAC9E;YACD,UAAU;YACV,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAA;SAClF;IACL,CAAC;IAED,WAAW;IACJ,mCAAa,GAApB,cAAyB,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAC1C,yCAAmB,GAA1B,UAA2B,IAAS;QAChC,oBAAO,CAAC,wBAAwB,EAAE,CAAA,CAAC,cAAc;QACjD,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;QAC3D,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;SACrB;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC7B;QACD,IAAI,IAAI,CAAC,EAAE,KAAK,iBAAS,CAAC,IAAI,EAAE;YAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAA;SAC5B;QACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAA;IACnD,CAAC;IAED,SAAS;IACF,gCAAU,GAAjB,UAAkB,EAAU;;QACxB,OAAO,OAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,EAAE,EAAX,CAAW,CAAC,0CAAE,EAAE,KAAI,CAAC,CAAA;IAC1D,CAAC;IAEM,oCAAc,GAArB;QACI,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAA;IAC1C,CAAC;IAEO,wCAAkB,GAA1B;;QACI,IAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QAChC,IAAM,MAAM,GAAG,oBAAO,CAAC,gBAAgB,OAAC,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,iBAAS,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,0CAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;QACnH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,KAAI,CAAC,CAAA;IACjE,CAAC;IAED,YAAY;IACL,qCAAe,GAAtB;QACI,OAAO,IAAI,CAAC,YAAY,GAAG,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,UAAU,CAAC,CAAA;IAChF,CAAC;IAED,WAAW;IACJ,qCAAe,GAAtB;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,CAAA;IAC1D,CAAC;IAED,SAAS;IACF,kCAAY,GAAnB,cAAwB,OAAO,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC;IACxC,oCAAc,GAArB,cAA0B,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;IAC5C,yCAAmB,GAA1B,UAA2B,KAAa,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA,CAAC,CAAC;IAC3E,qCAAe,GAAtB,UAAuB,KAAY,EAAE,MAAsB;QAA3D,iBAkBC;QAlBoC,uBAAA,EAAA,aAAsB;QACvD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;QACrB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;QACzB,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YACX,IAAM,KAAK,GAAG,EAAE,CAAA;YAChB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;gBAChB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAA;gBACpB,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,EAAE;oBAChC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACnB;gBACD,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACzB,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAClC,CAAC,CAAC,CAAA;YACF,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;aACpC;QACL,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,CAAC,CAAA;IACxD,CAAC;IAED,WAAW;IACH,uCAAiB,GAAzB;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,oBAAO,CAAC,YAAY,CAAA;IACzD,CAAC;IAED,SAAS;IACD,kCAAY,GAApB,UAAqB,IAAS;;QAC1B,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;QACjB,IAAI,CAAC,UAAU,SAAG,IAAI,CAAC,UAAU,mCAAI,IAAI,CAAC,UAAU,CAAA;QACpD,IAAI,CAAC,YAAY,SAAG,IAAI,CAAC,YAAY,mCAAI,IAAI,CAAC,YAAY,CAAA;QAC1D,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACjC,IAAI,CAAC,aAAa,SAAG,IAAI,CAAC,aAAa,mCAAI,IAAI,CAAC,aAAa,CAAA;QAC7D,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACzF,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,cAAc,CAAC,CAAA;SACtC;IACL,CAAC;IAED,gBAAgB;IACT,yCAAmB,GAA1B,UAA2B,IAAS;;QAChC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC3B,OAAM;SACT;aAAM,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,SAAS,EAAE;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;SACjC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;YACxB,OAAM;SACT;QACD,IAAM,cAAc,GAAG,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;QACxF,IAAM,gBAAgB,GAAG,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAA;QAC5F,IAAM,UAAU,GAAG,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAChF,IAAM,UAAU,GAAG,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAChF,IAAM,SAAS,GAAG,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAC9E,IAAM,iBAAiB,GAAG,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;QAC9F,EAAE;QACF,IAAI,cAAc;YAAE,IAAI,CAAC,UAAU,SAAG,IAAI,CAAC,UAAU,mCAAI,IAAI,CAAC,UAAU,CAAA;QACxE,IAAI,gBAAgB;YAAE,IAAI,CAAC,YAAY,SAAG,IAAI,CAAC,YAAY,mCAAI,IAAI,CAAC,YAAY,CAAA;QAChF,IAAI,UAAU;YAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnD,IAAI,UAAU;YAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnD,IAAI,SAAS;YAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAChD,IAAI,iBAAiB;YAAE,IAAI,CAAC,aAAa,SAAG,IAAI,CAAC,aAAa,mCAAI,IAAI,CAAC,aAAa,CAAA;QACpF,WAAW;QACX,IAAI,CAAC,cAAc,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,EAAE;YACpF,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,cAAc,CAAC,CAAA;SACtC;IACL,CAAC;IAED,SAAS;IACF,uCAAiB,GAAxB,UAAyB,IAAS;QAC9B,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAChC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAC9B,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;aACtD;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,8CAAwB,GAA/B,UAAgC,IAAS;QACrC,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;SACtC;aAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAC9B,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC1B,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC;oBAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACjG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;oBAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACxF,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;oBAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACpG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC;oBAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACjG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;oBAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;gBAC7G,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;oBAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBAC1G,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC;oBAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACjG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;oBAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;gBACzH,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC;oBAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA,CAAC,MAAM;gBACtI,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC;oBAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA,CAAC,MAAM;gBACnI,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;oBAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA,CAAC,MAAM;gBAC7H,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;oBAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA,CAAC,MAAM;aAC3H;YACD,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC7F,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAChG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACzG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAClG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnH,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC9G,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAClH,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnH,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACvH,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;YAC9H,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACzG,IAAI,yBAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;SACpH;IACL,CAAC;IAED,SAAS;IACF,wCAAkB,GAAzB,UAA0B,GAAW,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA,CAAC,CAAC;IAChF,0CAAoB,GAA3B,UAA4B,KAAU,EAAE,MAAsB;QAA9D,iBA0BC;QA1BuC,uBAAA,EAAA,aAAsB;QAC1D,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QAC9B,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;YACnB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC;gBACjD,IAAM,IAAI,GAAG,IAAI,0BAAgB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;gBAC9C,WAAW;gBACX,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;oBACtB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAA;oBACtC,oBAAO,CAAC,UAAU,CAAC;wBACf,GAAG,EAAE,gBAAgB;wBACrB,MAAM,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,CAAC;wBAC3F,GAAG,EAAE,IAAI,CAAC,GAAG;wBACb,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;qBAExD,CAAC,CAAA;iBACL;gBACD,OAAO,IAAI,CAAA;YACf,CAAC,CAAC,CAAC,CAAA;SACN;QACD,KAAK;QACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,GAAG;YACpC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;gBACzC,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;aACrC;QACL,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;IAC9E,CAAC;IACM,yCAAmB,GAA1B;QACI,IAAM,GAAG,GAAuB,EAAE,CAAA;QAClC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAd,CAAc,CAAC,CAAA;QACnD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,iBAAiB;IACV,2CAAqB,GAA5B,UAA6B,GAAW;QACpC,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAA,GAAG;YAC9B,IAAI,IAAI,GAAG,CAAC,CAAA;YACZ,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC;gBACT,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,EAAE;oBACnB,IAAI,IAAI,CAAC,CAAC,cAAc,EAAE,CAAA;iBAC7B;qBAAM;oBACH,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAA;iBACrB;gBACD,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,GAAG,OAAO,EAAE;oBAClC,OAAO,GAAG,IAAI,CAAA;iBACjB;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QACF,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED,iBAAiB;IACV,4CAAsB,GAA7B,UAA8B,GAAW;QACrC,IAAI,OAAO,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAA;QACzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,CAAC;YACtB,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,EAAE;gBACnB,IAAI,IAAI,CAAC,CAAC,cAAc,EAAE,CAAA;aAC7B;iBAAM;gBACH,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAA;aACrB;YACD,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,GAAG,OAAO,EAAE;gBAClC,OAAO,GAAG,IAAI,CAAA;aACjB;QACL,CAAC,CAAC,CAAA;QACF,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED,UAAU;IACH,oCAAc,GAArB;QACI,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAEM,uCAAiB,GAAxB,UAAyB,KAAU;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;IAC5B,CAAC;IAEM,mCAAa,GAApB,UAAqB,IAAoB;QACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,wBAAwB,CAAC,CAAA;IACjD,CAAC;IAEM,sCAAgB,GAAvB,UAAwB,GAAW;QAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,wBAAwB,CAAC,CAAA;IACjD,CAAC;IAED,WAAW;IACJ,yCAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAEM,2CAAqB,GAA5B,UAA6B,KAAU,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAC3D,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAA;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,IAAI,GAAG,IAAI,yBAAe,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;YACpD,WAAW;YACX,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBACtB,yCAAyC;gBACzC,oBAAO,CAAC,UAAU,CAAC;oBACf,GAAG,EAAE,gBAAgB;oBACrB,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,gBAAgB,CAAC;oBAChG,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;iBACxD,CAAC,CAAA;aACL;YACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC9B;QACD,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;IAC/E,CAAC;IAED,WAAW;IACJ,2CAAqB,GAA5B,cAAiC,OAAO,IAAI,CAAC,kBAAkB,CAAA,CAAC,CAAC;IAC1D,6CAAuB,GAA9B,UAA+B,KAAY,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAC/D,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC;YACjC,IAAM,IAAI,GAAG,IAAI,6BAAmB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YACjD,WAAW;YACX,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBACtB,oBAAO,CAAC,UAAU,CAAC;oBACf,GAAG,EAAE,gBAAgB;oBACrB,MAAM,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC7C,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;iBACxD,CAAC,CAAA;aACL;YACD,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;IAC9E,CAAC;IAED,SAAS;IACF,wCAAkB,GAAzB,UAA0B,GAAW,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA,CAAC,CAAC;IAE1F,iBAAiB;IACV,2CAAqB,GAA5B,UAA6B,GAAW;QACpC,IAAI,OAAO,GAAG,CAAC,EAAE,WAAW,GAAG,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,KAAK,GAAG,IAAI,CAAA;QAC3F,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAA,CAAC;YAC7B,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,EAAE;gBACnB,IAAI,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAA;gBACxD,KAAK,GAAG,WAAW,CAAA;aACtB;iBAAM;gBACH,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAA;aACrB;YACD,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,GAAG,OAAO,EAAE;gBAClC,OAAO,GAAG,IAAI,CAAA;aACjB;QACL,CAAC,CAAC,CAAA;QACF,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,OAAA,EAAE,CAAA;IACnC,CAAC;IAEM,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IACvD,cAAc;IACP,uCAAiB,GAAxB,UAAyB,MAAc;QACnC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,IAAI,CAAA;SACd;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAA;QAC3D,IAAI,IAAI,GAAG;YACP,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;YAC7B,MAAM,EAAE,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,KAAI,CAAC;YACzB,WAAW,EAAE,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,KAAI,CAAC;SACtC,CAAA;QACD,OAAO;QACP,IAAI,GAAG,EAAE;YACL,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YACrC,IAAI,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE;gBACrE,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAA;aACtD;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAA;aACtC;YACD,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAA;aACjC;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,0CAAoB,GAA3B,UAA4B,EAAU,EAAE,QAAgB,EAAE,MAAc,EAAE,WAAmB;QACzF,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,UAAA,EAAE,MAAM,QAAA,EAAE,WAAW,aAAA,EAAE,CAAA;IAC9D,CAAC;IAED,SAAS;IACF,gDAA0B,GAAjC,UAAkC,IAAa;;QAC3C,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;YACzB,IAAI,CAAC,QAAQ,GAAG,OAAA,IAAI,CAAC,KAAK,0CAAE,GAAG,KAAI,EAAE,CAAA;SACxC;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAA,IAAI,CAAC,KAAK,0CAAE,GAAG,KAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAA;SACxH;QACD,IAAM,OAAO,SAAG,IAAI,CAAC,QAAQ,0CAAE,cAAc,CAAA;QAC7C,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;SAC3C;IACL,CAAC;IAED,WAAW;IACJ,0CAAoB,GAA3B,cAAgC,OAAO,IAAI,CAAC,iBAAiB,CAAA,CAAC,CAAC;IAEvD,mCAAa,GAArB,UAAsB,KAAsC;QACxD,IAAM,IAAI,GAAU,EAAE,CAAA;QACtB,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE;YACjB,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YACrB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aAClB;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED,8HAA8H;IAC9H,SAAS;IACF,oCAAc,GAArB,cAA0B,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;IACnD,eAAe;IACR,yCAAmB,GAA1B,cAA4C,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA,CAAC,CAAC;IACzF,KAAK;IACE,uCAAiB,GAAxB,UAAyB,KAAU,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QACvD,IAAI,CAAC,WAAW,GAAG,oBAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,mBAAS,CAAC,CAAA;QAC/D,2BAAY,CAAC,GAAG,CAAC,kBAAkB,EAAE,oBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;QACrF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,mBAAmB,CAAC,CAAA;IACtD,CAAC;IAED,8HAA8H;IAC9H,SAAS;IACF,kCAAY,GAAnB,cAAwB,OAAO,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC;IACxC,uCAAiB,GAAxB,cAA4C,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA,CAAC,CAAC;IAChF,qCAAe,GAAtB,UAAuB,EAAU,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA,CAAC,CAAC;IACzD,qCAAe,GAAtB,UAAuB,KAAU,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QACrD,IAAI,CAAC,SAAS,GAAG,oBAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,qBAAW,CAAC,CAAA;QAC/D,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,2BAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,oBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;QACjF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,CAAC,CAAA;IACpD,CAAC;IAED,OAAO;IACC,uCAAiB,GAAzB;QAAA,iBAUC;QATG,yBAAc,CAAC,OAAO,CAAC,UAAA,EAAE;YACrB,IAAM,IAAI,GAAG,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;YAC/B,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,QAAQ,CAAC,KAAI,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;aACrE;iBAAM;gBACH,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,IAAA,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;aAChE;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAA;IACjC,CAAC;IAED,YAAY;IACL,wCAAkB,GAAzB,UAA0B,OAAe;QAAzC,iBAmCC;QAlCG,IAAI,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAA;QAC7C,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAA;SACf;QACD,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAA;QAC9C,IAAM,MAAM,GAAG,EAAE,CAAA;QACjB,YAAY;QACZ,yBAAc,CAAC,OAAO,CAAC,UAAA,EAAE;YACrB,IAAI,IAAI,GAAG,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;YAC7B,IAAI,IAAI,EAAE;gBACN,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;aACzB;iBAAM;gBACH,IAAI,GAAG,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,IAAA,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;aAClD;YACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC,CAAC,CAAA;QACF,iBAAiB;QACjB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC;;YACpB,IAAM,EAAE,SAAG,CAAC,CAAC,IAAI,0CAAE,UAAU,CAAA;YAC7B,IAAI,CAAC,EAAE,EAAE;gBACL,KAAK,CAAC,IAAI,CAAC,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;aAC9D;iBAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;gBACpB,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;gBACjB,KAAK,CAAC,IAAI,CAAC,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,IAAA,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAI,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aAC3H;QACL,CAAC,CAAC,CAAA;QACF,YAAY;QACZ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,EAAE;;YACzB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,OAAA,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,0CAAE,cAAc,MAAK,OAAO,EAAE;gBAClF,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;gBACjB,KAAK,CAAC,IAAI,CAAC,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,IAAA,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAI,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aACvH;QACL,CAAC,CAAC,CAAA;QACF,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,WAAW;IACJ,2CAAqB,GAA5B,UAA6B,OAAe;QAA5C,iBAUC;QATG,IAAI,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAA;QAC7C,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAA;SACf;QACD,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAA;QAC9C,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,cAAc,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW,EAA9C,CAA8C,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;YACrG,KAAK,CAAC,IAAI,CAAC,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAI,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QAC9I,CAAC,CAAC,CAAA;QACF,OAAO,KAAK,CAAA;IAChB,CAAC;IACD,8HAA8H;IAC9H,SAAS;IACF,mCAAa,GAApB,cAAyB,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAC1C,wCAAkB,GAAzB,cAA8C,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,CAAC,CAAC;IACnF,sCAAgB,GAAvB,UAAwB,EAAU,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA,CAAC,CAAC;IAC3D,sCAAgB,GAAvB,UAAwB,KAAU,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QACtD,IAAI,CAAC,UAAU,GAAG,oBAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,sBAAY,CAAC,CAAA;QACjE,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,2BAAY,CAAC,GAAG,CAAC,iBAAiB,EAAE,oBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAA;QACnF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,kBAAkB,CAAC,CAAA;IACrD,CAAC;IAED,gBAAgB;IACR,wCAAkB,GAA1B;QAAA,iBAaC;QAZG,IAAM,QAAQ,GAAG,EAAE,CAAA;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAnB,CAAmB,CAAC,CAAA;QAC7C,YAAY;QACZ,0BAAe,CAAC,OAAO,CAAC,UAAA,EAAE;YACtB,IAAM,IAAI,GAAG,KAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,EAAE;gBACP,KAAI,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,IAAI,sBAAY,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,IAAA,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;aAClE;iBAAM,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,IAAI;gBAC3B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aAClC;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA,CAAC,MAAM;IACzC,CAAC;IAED,YAAY;IACL,uCAAiB,GAAxB;QAAA,iBA2BC;QA1BG,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/D,OAAO,IAAI,CAAC,kBAAkB,CAAA;SACjC;QACD,IAAM,MAAM,GAAG,EAAE,CAAA;QACjB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAA;QAC5B,0BAAe,CAAC,OAAO,CAAC,UAAA,EAAE;YACtB,IAAM,IAAI,GAAG,KAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;YAChC,IAAI,IAAI,EAAE;gBACN,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;gBACtB,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aACrC;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBACf,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;gBACnB,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,sBAAY,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;aAC5F;QACL,CAAC,CAAC,CAAA;QACF,UAAU;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBACZ,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;gBAChB,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,sBAAY,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;aACpF;QACL,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAC,kBAAkB,CAAA;IAClC,CAAC;IAED,OAAO;IACM,gCAAU,GAAvB,UAAwB,GAAW,EAAE,UAAkB;;;;;4BAC7B,qBAAM,qBAAS,CAAC,aAAa,CAAC,EAAE,GAAG,KAAA,EAAE,UAAU,YAAA,EAAE,CAAC,EAAA;;wBAAlE,KAAgB,SAAkD,EAAhE,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACA,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;4BACrC,IAAI,KAAK,EAAE;gCACP,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;6BAC7C;4BACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;4BAC9C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,CAAA;4BAChD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,CAAC,CAAA;yBACzC;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,SAAS;IACI,iCAAW,GAAxB;;;;;;wBACI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,6BAAkB,EAAE;4BAC1C,sBAAO,aAAK,CAAC,eAAe,EAAA;yBAC/B;wBACqB,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAAvE,KAAgB,SAAuD,EAArE,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;yBAC/B;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,aAAa;IACN,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;IAClD,0CAAoB,GAA3B,UAA4B,IAAS;QACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,wBAAc,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACtE,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAhB,CAAgB,CAAC,CAAA;SAC7E;IACL,CAAC;IAED,OAAO;IACA,iCAAW,GAAlB,UAAmB,IAAS;QACxB,IAAM,KAAK,GAAc,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;QAClE,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SACzB;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAS,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;YAC/C,IAAI,CAAC,kBAAkB,EAAE,CAAA;SAC5B;QACD,OAAO,CAAC,KAAK,CAAA,CAAC,OAAO;IACzB,CAAC;IAEO,qCAAe,GAAvB,UAAwB,IAAS;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QAC9C,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAA,CAAC,uBAAuB;QACrD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEM,+BAAS,GAAhB,cAAqB,OAAO,IAAI,CAAC,MAAM,CAAA,CAAC,CAAC;IAClC,kCAAY,GAAnB,UAAoB,EAAU,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,EAAE,EAAX,CAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA,CAAC,CAAC;IAClF,mCAAa,GAApB,UAAqB,GAAW,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA,CAAC,CAAC;IAE9F,gBAAgB;IACT,mCAAa,GAApB,UAAqB,MAAc;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,cAAc,KAAK,MAAM,EAAhD,CAAgD,CAAC,CAAA;IACpF,CAAC;IAED,mBAAmB;IACZ,2CAAqB,GAA5B,UAA6B,GAAW;QACpC,IAAM,eAAe,GAA+B,EAAE,CAAA;QACtD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACjB,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;gBACf,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAA5B,CAA4B,CAAC,CAAA;aAC5D;QACL,CAAC,CAAC,CAAA;QACF,OAAO,eAAe,CAAA;IAC1B,CAAC;IAED,WAAW;IACH,qCAAe,GAAvB,UAAwB,EAAU,EAAE,KAAc;QAC9C,oBAAO,CAAC,UAAU,CAAC;YACf,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB;YAChD,MAAM,EAAE,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAChC,GAAG,EAAE,EAAE,GAAG,EAAE;SACf,CAAC,CAAA;IACN,CAAC;IAED,YAAY;IACL,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;IAEzD,SAAS;IACD,0CAAoB,GAA5B,UAA6B,IAAS;QAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,wBAAc,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC1E,CAAC;IAED,OAAO;IACM,gCAAU,GAAvB,UAAwB,OAAe,EAAE,OAAiB;;;;;4BAChC,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,OAAO,SAAA,EAAE,OAAO,SAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAA3F,KAAgB,SAA2E,EAAzF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;4BAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;yBAChC;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,OAAO;IACM,uCAAiB,GAA9B,UAA+B,OAAe;;;;;4BACpB,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,2BAA2B,EAAE,EAAE,OAAO,SAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAAtF,KAAgB,SAAsE,EAApF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;4BAChC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;yBACzD;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,YAAY;IACJ,yCAAmB,GAA3B,UAA4B,GAAW,EAAE,KAAiB;QACtD,IAAM,UAAU,GAAG,oBAAO,CAAC,UAAU,CAAA;QACrC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC;;YACpB,IAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC7B,MAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,0CAAE,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,EAAhC,CAAgC,EAAC;aACpF;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACI,sCAAgB,GAA7B,UAA8B,GAAW;;;;;4BACf,qBAAM,qBAAS,CAAC,eAAe,CAAC,EAAE,GAAG,KAAA,EAAE,CAAC,EAAA;;wBAAxD,KAAgB,SAAwC,EAAtD,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BACvB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;4BAChC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;yBACzD;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,WAAW;IACH,qCAAe,GAAvB,UAAwB,EAAU;QAC9B,oBAAO,CAAC,UAAU,CAAC;YACf,GAAG,EAAE,gBAAgB;YACrB,GAAG,EAAE,EAAE,GAAG,EAAE;SACf,CAAC,CAAA;IACN,CAAC;IAED,aAAa;IACN,yCAAmB,GAA1B,cAA+B,OAAO,IAAI,CAAC,gBAAgB,CAAA,CAAC,CAAC;IAE7D,SAAS;IACF,sCAAgB,GAAvB,UAAwB,KAAa;;QACjC,OAAO,CAAC,QAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,KAAK,EAAjB,CAAiB,CAAC,0CAAE,MAAM,CAAA,CAAA;IACvE,CAAC;IAED,WAAW;IACJ,0CAAoB,GAA3B,UAA4B,KAAa,EAAE,MAAe;QACtD,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,KAAK,EAAjB,CAAiB,CAAC,CAAA;QAC7D,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;SACvB;aAAM;YACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,CAAA;SAChD;IACL,CAAC;IAED,WAAW;IACH,2CAAqB,GAA7B,UAA8B,IAAe;QACzC,IAAI,CAAC,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACrB,OAAM;SACT;QACD,YAAY;QACZ,IAAM,GAAG,GAAG,sBAAsB,GAAG,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;QACrE,IAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACtC,IAAI,UAAU,KAAK,GAAG,EAAE;YACpB,OAAM;SACT;QACD,IAAI,IAAI,GAAG,kBAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACnE,IAAI,IAAI,GAAG,GAAG,CAAA;QACd,IAAI,IAAI,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,iBAAS,CAAC,IAAI,EAAE,iBAAS,CAAC,SAAS,EAAE,iBAAS,CAAC,OAAO,EAAE,iBAAS,CAAC,WAAW,EAAE,iBAAS,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAC1H,IAAI,GAAG,GAAG,CAAA;YACV,IAAI,GAAG,EAAE,CAAA;SACZ;QACD,IAAI,KAAK,GAAG,GAAG,CAAA;QACf,IAAI,IAAI,GAAG,CAAC,EAAE;YACV,oBAAO,CAAC,UAAU,CAAC;gBACf,GAAG,KAAA;gBACH,MAAM,EAAE,CAAC,aAAW,uBAAY,CAAC,IAAI,SAAI,IAAI,GAAG,IAAI,GAAG,IAAI,aAAU,CAAC;gBACtE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;gBACvB,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK;aAChE,CAAC,CAAA;YACF,KAAK,IAAI,GAAG,CAAA;SACf;QACD,iBAAiB;QACjB,IAAI,IAAI,CAAC,EAAE,KAAK,iBAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;YACjD,UAAU;YACV,oBAAO,CAAC,UAAU,CAAC;gBACf,GAAG,EAAE,gCAAgC;gBACrC,MAAM,EAAE,CAAC,YAAU,uBAAY,CAAC,IAAI,UAAK,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAU,CAAC;gBACxF,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;gBACzB,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK;aAChE,CAAC,CAAA;SACL;IACL,CAAC;IAED,OAAO;IACA,iCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IACtC,mCAAa,GAApB,UAAqB,KAAY,EAAE,MAAsB;QAAzD,iBAuBC;QAvBkC,uBAAA,EAAA,aAAsB;QACrD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC;YACvB,IAAM,IAAI,GAAG,IAAI,mBAAS,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YACvC,WAAW;YACX,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBACtB,oBAAO,CAAC,UAAU,CAAC;oBACf,GAAG,EAAE,gBAAgB;oBACrB,MAAM,EAAE,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,CAAC;oBACvF,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;iBAExD,CAAC,CAAA;gBACF,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;aACnC;YACD,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;QACF,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,eAAe,CAAC,CAAA;YACpC,yBAAyB;YACzB,IAAI,oBAAO,CAAC,KAAK,CAAC,SAAS,CAAC,0BAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAS,CAAC,QAAQ,CAAC,EAAE;gBAC3G,oBAAO,CAAC,KAAK,CAAC,YAAY,CAAC,0BAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;aACnE;SACJ;IACL,CAAC;IAEM,yCAAmB,GAA1B,UAA2B,GAAW;QAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IACpC,CAAC;IAED,OAAO;IACM,sCAAgB,GAA7B,UAA8B,KAAa,EAAE,GAAW;;;;;4BAC9B,qBAAM,qBAAS,CAAC,WAAW,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAC,EAAA;;wBAA3D,KAAgB,SAA2C,EAAzD,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;wBACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBAC/B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBACrC,oBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;wBAC5B,oBAAO,CAAC,eAAe,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA;wBACtC,oBAAO,CAAC,eAAe,CAAC,GAAG,GAAG,SAAS,CAAC,CAAA;;;;;KAC3C;IAED,OAAO;IACM,8BAAQ,GAArB;;;;;;wBACI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,0BAAe,EAAE;4BACvC,sBAAO,aAAK,CAAC,eAAe,EAAA;yBAC/B;wBACqB,qBAAM,qBAAS,CAAC,WAAW,EAAE,EAAA;;wBAA7C,KAAgB,SAA6B,EAA3C,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAA;4BACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;gCACnB,oBAAO,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;gCAC9B,oBAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA;gCAC9C,oBAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,CAAA;4BACpD,CAAC,CAAC,CAAA;4BACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;4BAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;yBAC/B;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,YAAY;IACL,uCAAiB,GAAxB,UAAyB,GAAW;QAChC,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;QACjD,OAAO,CAAC,EAAC,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,cAAc,GAAE,CAAA;IACjC,CAAC;IACM,oCAAc,GAArB,UAAsB,GAAW;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;IACjD,CAAC;IAED,SAAS;IACI,iCAAW,GAAxB,UAAyB,QAAsB,EAAE,IAAoB;QAA5C,yBAAA,EAAA,cAAsB;QAAE,qBAAA,EAAA,WAAoB;;;;;;wBACjE,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;4BAClB,sBAAO,EAAE,EAAA,CAAC,aAAa;yBAC1B;6BAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,gBAAgB,IAAI,QAAQ,GAAG,IAAI,EAAE;4BAC3G,sBAAO,IAAI,CAAC,YAAY,EAAA;yBAC3B;wBACqB,qBAAM,qBAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAA;;wBAAvD,KAAgB,SAAuC,EAArD,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;wBAClC,IAAI,CAAC,YAAY,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,CAAA;wBACpC,OAAO;wBACP,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,IAAI;4BAC1B,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;4BACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAnF,CAAmF,CAAC,EAAhH,CAAgH,CAAC,CAAA;wBAChJ,CAAC,CAAC,CAAA;wBACF,sBAAO,IAAI,CAAC,YAAY,EAAA;;;;KAC3B;IACM,qCAAe,GAAtB,cAA2B,OAAO,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;IAErD,WAAW;IACE,oCAAc,GAA3B,UAA4B,KAAa,EAAE,IAAY,EAAE,QAAoB;QAApB,yBAAA,EAAA,YAAoB;;;;;;wBACzE,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;4BAClB,sBAAO,EAAE,IAAI,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,EAAA,CAAC,aAAa;yBACrD;6BAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,sBAAsB,IAAI,QAAQ,GAAG,IAAI,EAAE;4BACvH,sBAAO,EAAE,GAAG,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,IAAI,CAAC,0BAA0B,EAAE,EAAA;yBACvH;wBACqB,qBAAM,qBAAS,CAAC,iBAAiB,CAAC,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,CAAC,EAAA;;wBAAlE,KAAgB,SAAkD,EAAhE,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;wBACxC,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAA;wBAC5B,IAAI,CAAC,kBAAkB,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,CAAA;wBAC1C,IAAI,CAAC,0BAA0B,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,KAAI,CAAC,CAAA;wBACzD,OAAO;wBACP,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAA,IAAI;4BAChC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;4BACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAnF,CAAmF,CAAC,EAAhH,CAAgH,CAAC,CAAA;wBAChJ,CAAC,CAAC,CAAA;wBACF,sBAAO,EAAE,GAAG,KAAA,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,IAAI,CAAC,0BAA0B,EAAE,EAAA;;;;KAC/F;IACM,2CAAqB,GAA5B,cAAiC,OAAO,IAAI,CAAC,kBAAkB,CAAA,CAAC,CAAC;IAEjE,aAAa;IACN,gDAA0B,GAAjC,UAAkC,SAAgB,EAAE,IAAY,EAAE,IAAY;QAC1E,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAC5E,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAClF,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAA;IACnD,CAAC;IACO,mDAA6B,GAArC,UAAsC,KAAsB,EAAE,SAAgB,EAAE,IAAY,EAAE,IAAY;QACtG,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,EAAd,CAAc,CAAC,CAAA;QAC5C,IAAI,IAAI,EAAE;YACN,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,EAAd,CAAc,CAAC,CAAA;YACjD,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;gBACzB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;gBACjC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;gBACzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAnF,CAAmF,CAAC,EAAhH,CAAgH,CAAC,CAAA;aAC/I;SACJ;IACL,CAAC;IAED,aAAa;IACL,yCAAmB,GAA3B,UAA4B,IAAS;QACjC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IACrE,CAAC;IACO,4CAAsB,GAA9B,UAA+B,KAAsB,EAAE,IAAS;;QAC5D,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,MAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,CAAA,EAAnB,CAAmB,CAAC,CAAA;QACjD,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,KAAK,SAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,mCAAI,IAAI,CAAC,KAAK,CAAA;SACzC;IACL,CAAC;IAED,SAAS;IACF,kCAAY,GAAnB,cAAwB,OAAO,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC;IAC/C,SAAS;IACF,qCAAe,GAAtB,UAAuB,KAAY;QAC/B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAA;QAC7D,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,gBAAgB,CAAC,CAAA;IACzC,CAAC;IACD,aAAa;IACN,2CAAqB,GAA5B,cAAiC,OAAO,IAAI,GAAG,CAAC,oBAAO,CAAC,qBAAqB,CAAC,eAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,CAAC;IAEnH,SAAS;IACI,yCAAmB,GAAhC;;;;;;wBACI,IAAI,oBAAO,CAAC,YAAY,EAAE;4BACtB,sBAAO,EAAE,EAAA;yBACZ;6BAAM,IAAI,IAAI,CAAC,0BAA0B,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,0BAA0B,IAAI,IAAI,EAAE;4BACpG,sBAAO,IAAI,CAAC,uBAAuB,EAAA;yBACtC;wBACqB,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,wBAAwB,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAA;;wBAArF,KAAgB,SAAqE,EAAnF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;wBAC5C,IAAI,GAAG,EAAE;4BACL,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAA;yBACpC;6BAAM;4BACH,IAAI,CAAC,uBAAuB,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,CAAA;yBAClD;wBACD,sBAAO,IAAI,CAAC,uBAAuB,EAAA;;;;KACtC;IAEY,0CAAoB,GAAjC;;;;;;wBACI,IAAI,IAAI,CAAC,2BAA2B,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,2BAA2B,IAAI,IAAI,EAAE;4BAC/F,sBAAO,IAAI,CAAC,wBAAwB,EAAA;yBACvC;wBACqB,qBAAM,qBAAS,CAAC,uBAAuB,EAAE,EAAA;;wBAAzD,KAAgB,SAAyC,EAAvD,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;wBAC7C,IAAI,GAAG,EAAE;4BACL,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAA;yBACrC;6BAAM;4BACH,IAAI,CAAC,wBAAwB,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,CAAA;yBACnD;wBACD,sBAAO,IAAI,CAAC,wBAAwB,EAAA;;;;KACvC;IAED,SAAS;IAEI,sCAAgB,GAA7B;;;;;;wBACI,IAAI,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,uBAAuB,IAAI,IAAI,EAAE;4BACvF,sBAAO,IAAI,CAAC,oBAAoB,EAAA;yBACnC;wBACqB,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,EAAA;;wBAAtE,KAAgB,SAAsD,EAApE,GAAG,SAAA,EAAE,IAAI,UAAA;wBACX,GAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;wBAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;wBACzC,IAAI,CAAC,oBAAoB,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,CAAA;wBAC5C,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAA,CAAC;;4BAC/B,CAAC,CAAC,QAAQ,SAAG,CAAC,CAAC,KAAK,0CAAG,CAAC,CAAC,CAAA;4BACzB,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;gCACd,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gCAChD,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,OAAC,CAAC,CAAC,KAAK,0CAAG,CAAC,EAAE,CAAC,OAAC,CAAC,CAAC,KAAK,0CAAG,CAAC,CAAC,CAAA;6BAC7D;4BACD,IAAM,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,CAAA;4BACvB,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;4BAC7F,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;4BAChG,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;wBACpG,CAAC,CAAC,CAAA;wBACF,sBAAO,IAAI,CAAC,oBAAoB,EAAA;;;;KACnC;IAED,WAAW;IACJ,mCAAa,GAApB;QACI,OAAO,IAAI,CAAC,UAAU,CAAA;IAC1B,CAAC;IACM,sCAAgB,GAAvB,UAAwB,KAAY,EAAE,MAAsB;QAA5D,iBAeC;QAfqC,uBAAA,EAAA,aAAsB;QACxD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,KAAK,CAAC,OAAO,CAAC,UAAA,QAAQ;YAClB,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;YACtD,IAAI,IAAI,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QACF,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,CAAC,CAAA;SAC9C;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,2BAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;SACxC;aAAM,IAAI,2BAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YAC1D,2BAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;YACrC,2BAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;SACzE;IACL,CAAC;IAED,WAAW;IACJ,8CAAwB,GAA/B,UAAgC,KAAY,EAAE,MAAsB;QAApE,iBAcC;QAd6C,uBAAA,EAAA,aAAsB;QAChE,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE;YACf,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;;gBACd,IAAM,IAAI,GAAG,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAhB,CAAgB,CAAC,CAAA;gBACxD,IAAI,CAAC,IAAI,EAAE;oBACP,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;oBAClD,IAAI,IAAI,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACrC;qBAAM;oBACH,MAAA,IAAI,CAAC,IAAI,0CAAE,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAC;iBAC3C;YACL,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAA;YAChC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,CAAC,CAAA;SACxD;IACL,CAAC;IAEM,wCAAkB,GAAzB;QACI,IAAI,IAAI,GAAY,IAAI,CAAA;QACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC;YACrB,IAAM,KAAK,GAAG,CAAC,CAAC,mBAAmB,EAAE,CAAA;YACrC,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,iBAAS,CAAC,MAAM,EAAE;gBACrC,IAAI,GAAG,CAAC,CAAA;aACX;QACL,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACf,CAAC;IAED,SAAS;IACF,0CAAoB,GAA3B,UAA4B,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAC9C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAM;SACT;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,EAA/B,CAA+B,CAAC,CAAA;QAC/D,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACtC,2BAAY,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;QACtC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;IAChE,CAAC;IAED,OAAO;IACC,yCAAmB,GAA3B,UAA4B,GAAY;QACpC,IAAI,GAAG,EAAE;YACL,OAAO,GAAG,CAAA;SACb;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAA;QACjD,IAAI,EAAE,KAAK,GAAG,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;SACrD;QACD,OAAO,EAAE,CAAA;IACb,CAAC;IAED,SAAS;IACI,qCAAe,GAA5B,UAA6B,EAAU;;;;;4BACb,qBAAM,qBAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,IAAA,EAAE,CAAC,EAAA;;wBAA1D,KAAgB,SAA0C,EAAxD,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;4BAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;4BACjC,IAAI,CAAC,oBAAoB,EAAE,CAAA;4BAC3B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;4BAC5C,IAAI,CAAC,oBAAoB,EAAE,CAAA;yBAC9B;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,WAAW;IACJ,mCAAa,GAApB;QACI,OAAO,IAAI,CAAC,UAAU,CAAA;IAC1B,CAAC;IACO,sCAAgB,GAAxB,UAAyB,KAAY,EAAE,MAAsB;QAA7D,iBAaC;QAbsC,uBAAA,EAAA,aAAsB;QACzD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,KAAK,CAAC,OAAO,CAAC,UAAA,QAAQ;YAClB,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;YACtD,IAAI,IAAI,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,CAAC,CAAA;QACrD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,2BAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;SACxC;aAAM,IAAI,2BAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YAC1D,2BAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;YACrC,2BAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;SACzE;IACL,CAAC;IAED,WAAW;IACH,8CAAwB,GAAhC,UAAiC,KAAY,EAAE,MAAsB;QAArE,iBAMC;QAN8C,uBAAA,EAAA,aAAsB;QACjE,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE;YACf,KAAK,CAAC,OAAO,CAAC,UAAA,QAAQ,mCAAI,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,EAApB,CAAoB,CAAC,0CAAE,IAAI,0CAAE,cAAc,CAAC,QAAQ,CAAC,QAAQ,IAAC,CAAC,CAAA;YACnH,IAAI,CAAC,oBAAoB,EAAE,CAAA;YAC3B,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,CAAC,CAAA;SACxD;IACL,CAAC;IAEM,wCAAkB,GAAzB;QACI,IAAI,IAAI,GAAY,IAAI,CAAA;QACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC;YACrB,IAAM,KAAK,GAAG,CAAC,CAAC,mBAAmB,EAAE,CAAA;YACrC,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,iBAAS,CAAC,MAAM,EAAE;gBACrC,IAAI,GAAG,CAAC,CAAA;aACX;QACL,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACf,CAAC;IAED,SAAS;IACF,0CAAoB,GAA3B;QACI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,CAAC,CAAA;aAC/C;YACD,OAAM;SACT;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,EAA/B,CAA+B,CAAC,CAAA;QAC/D,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACtC,2BAAY,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;IACtD,CAAC;IAED,OAAO;IACC,yCAAmB,GAA3B,UAA4B,GAAY;QACpC,IAAI,GAAG,EAAE;YACL,OAAO,GAAG,CAAA;SACb;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAA;QACjD,IAAI,EAAE,KAAK,GAAG,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;SACrD;QACD,OAAO,EAAE,CAAA;IACb,CAAC;IAED,SAAS;IACI,0CAAoB,GAAjC,UAAkC,EAAU,EAAE,aAAqB,EAAE,WAAmB;;;;;4BAC9D,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,8BAA8B,EAAE,EAAE,EAAE,IAAA,EAAE,aAAa,eAAA,EAAE,WAAW,aAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAAhH,KAAgB,SAAgG,EAA9G,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;4BAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;4BACtC,IAAI,CAAC,oBAAoB,EAAE,CAAA;yBAC9B;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,WAAW;IACJ,mCAAa,GAApB;QACI,OAAO,IAAI,CAAC,UAAU,CAAA;IAC1B,CAAC;IACO,sCAAgB,GAAxB,UAAyB,KAAY,EAAE,MAAsB;QAA7D,iBAaC;QAbsC,uBAAA,EAAA,aAAsB;QACzD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,KAAK,CAAC,OAAO,CAAC,UAAA,QAAQ;YAClB,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;YACtD,IAAI,IAAI,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,CAAC,CAAA;QACrD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,2BAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;SACxC;aAAM,IAAI,2BAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YAC1D,2BAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;YACrC,2BAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;SACzE;IACL,CAAC;IAED,WAAW;IACH,8CAAwB,GAAhC,UAAiC,KAAY,EAAE,MAAsB;QAArE,iBAcC;QAd8C,uBAAA,EAAA,aAAsB;QACjE,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE;YACf,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;;gBACd,IAAM,IAAI,GAAG,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAhB,CAAgB,CAAC,CAAA;gBACxD,IAAI,IAAI,EAAE;oBACN,MAAA,IAAI,CAAC,IAAI,0CAAE,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAC;iBAC3C;qBAAM;oBACH,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;oBAClD,IAAI,IAAI,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACrC;YACL,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,oBAAoB,EAAE,CAAA;YAC3B,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,CAAC,CAAA;SACxD;IACL,CAAC;IAEM,wCAAkB,GAAzB;QACI,IAAI,IAAI,GAAY,IAAI,CAAA;QACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC;YACrB,IAAM,KAAK,GAAG,CAAC,CAAC,mBAAmB,EAAE,CAAA;YACrC,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,iBAAS,CAAC,MAAM,EAAE;gBACrC,IAAI,GAAG,CAAC,CAAA;aACX;QACL,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACf,CAAC;IAED,SAAS;IACF,0CAAoB,GAA3B;QACI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAM;SACT;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,EAA/B,CAA+B,CAAC,CAAA;QAC/D,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACtC,2BAAY,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;IACtD,CAAC;IAED,OAAO;IACC,yCAAmB,GAA3B,UAA4B,GAAY;QACpC,IAAI,GAAG,EAAE;YACL,OAAO,GAAG,CAAA;SACb;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAA;QACjD,IAAI,EAAE,KAAK,GAAG,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;SACrD;QACD,OAAO,EAAE,CAAA;IACb,CAAC;IAED,SAAS;IACI,0CAAoB,GAAjC,UAAkC,EAAU,EAAE,aAAqB;;;;;4BACzC,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,8BAA8B,EAAE,EAAE,EAAE,IAAA,EAAE,aAAa,eAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAAnG,KAAgB,SAAmF,EAAjG,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;4BAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;4BACtC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gCAC9B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,CAAC,CAAA;6BAC/C;iCAAM;gCACH,IAAI,CAAC,oBAAoB,EAAE,CAAA;6BAC9B;yBACJ;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,SAAS;IACF,uCAAiB,GAAxB;QACI,IAAM,KAAK,GAAc,EAAE,CAAA;QAC3B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;QACnC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;SACtC;QACD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;QACnC,OAAO,KAAK,CAAA;IAChB,CAAC;IAEM,wCAAkB,GAAzB;QACI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAA;IACtF,CAAC;IAEM,yCAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC9F,CAAC;IAED,WAAW;IACH,yCAAmB,GAA3B,UAA4B,OAAY;QACpC,IAAI,CAAC,oBAAoB,GAAG,OAAO,IAAI,EAAE,CAAA;QACzC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IACtC,CAAC;IAED,WAAW;IACE,sCAAgB,GAA7B,UAA8B,IAAW;;;;;;wBACrC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,0BAAe,EAAE;4BACvC,sBAAO,aAAK,CAAC,eAAe,EAAA;yBAC/B;wBACqB,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,sBAAsB,EAAE,EAAE,IAAI,MAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAA9E,KAAgB,SAA8D,EAA5E,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;4BACnD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BAC5B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;yBACxC;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,YAAY;IACL,0CAAoB,GAA3B,UAA4B,OAAe,EAAE,GAAW;QACpD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,GAAG,CAAA;IACzC,CAAC;IAEM,2CAAqB,GAA5B,cAAiC,OAAO,IAAI,CAAC,kBAAkB,CAAA,CAAC,CAAC;IACjE,WAAW;IACH,8CAAwB,GAAhC,UAAiC,IAAS;QACtC,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAA;QAC5B,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAA;SAC/C;IACL,CAAC;IAED,OAAO;IACA,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAChD,qCAAe,GAAtB,UAAuB,EAAU,EAAE,KAAa;QAC5C,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,KAAK,CAAA;IACxC,CAAC;IAED,OAAO;IACA,8CAAwB,GAA/B,UAAgC,KAAa,EAAE,GAAW;;QACtD,aAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAlC,CAAkC,CAAC,0CAAE,IAAI,CAAA;IAC1F,CAAC;IAEM,6CAAuB,GAA9B,UAA+B,KAAa,EAAE,GAAW,EAAE,IAAS;QAChE,IAAM,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAlC,CAAkC,CAAC,CAAA;QACpF,IAAI,EAAE,EAAE;YACJ,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,EAAE,EAAE;YAChD,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAA;SACtC;QACD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,YAAY;IACL,wCAAkB,GAAzB,UAA0B,EAAU,EAAE,KAAY;QAC9C,IAAM,UAAU,GAAG,oBAAO,CAAC,UAAU,CAAA;QACrC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC;;YACpB,IAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC7B,MAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,0CAAE,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,EAA9B,CAA8B,EAAC;aAClF;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,WAAW;IACJ,uCAAiB,GAAxB,UAAyB,IAAS;QAC9B,IAAM,KAAK,GAAG,4BAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAChD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QAClC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SACrB;aAAM;YACH,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,qBAAW,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SAC1D;QACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,CAAC,CAAA;IAC9C,CAAC;IAED,OAAO;IACM,iCAAW,GAAxB,UAAyB,KAAa,EAAE,EAAU;;;;;4BACxB,qBAAM,qBAAS,CAAC,cAAc,CAAC,EAAE,KAAK,OAAA,EAAE,EAAE,IAAA,EAAE,CAAC,EAAA;;wBAA7D,KAAgB,SAA6C,EAA3D,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BACjC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;4BACzC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;yBACvC;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,SAAS;IACI,yCAAmB,GAAhC,UAAiC,KAAa,EAAE,OAAe,EAAE,GAAW,EAAE,WAAmB;;;;;4BACvE,qBAAM,qBAAS,CAAC,sBAAsB,CAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,GAAG,KAAA,EAAE,WAAW,aAAA,EAAE,CAAC,EAAA;;wBAA5F,KAAgB,SAA4E,EAA1F,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;yBACpC;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,cAAc;IACP,wCAAkB,GAAzB,UAA0B,EAAU;QAChC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,EAAtE,CAAsE,CAAC,CAAA;IAC3G,CAAC;IAEM,yCAAmB,GAA1B,UAA2B,EAAU;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,UAAU,MAAK,EAAE,CAAA,EAAA,CAAC,CAAA;IAC9D,CAAC;IAEM,iCAAW,GAAlB,UAAmB,KAAc;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAArB,CAAqB,CAAC,CAAA;IACzD,CAAC;IAEM,iCAAW,GAAlB;QACI,OAAO,IAAI,CAAC,QAAQ,CAAA;IACxB,CAAC;IAED,OAAO;IACA,gCAAU,GAAjB,UAAkB,IAAS;QACvB,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACzC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;SACxB;aAAM;YACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC3B;QACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,eAAe,CAAC,CAAA;IACxC,CAAC;IAED,OAAO;IACM,mCAAa,GAA1B,UAA2B,KAAc;;;;;;wBAC/B,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAArB,CAAqB,CAAC,CAAA;wBACjE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;4BACd,sBAAO,EAAE,EAAA;yBACZ;wBACqB,qBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,KAAK,OAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAAhF,KAAgB,SAAgE,EAA9E,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,CAAC,GAAG,EAAE;4BACN,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;4BAC9B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,eAAe,CAAC,CAAA;yBACvC;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IACD,+GAA+G;IAC/G,SAAS;IACD,wCAAkB,GAA1B,UAA2B,IAAsC;QAAjE,iBAyEC;QAxEG,EAAE,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC;YACf,IAAM,IAAI,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;YAChC,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,MAAM,EAAE,EAAE,IAAI;gBACpC,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;aACjC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,YAAY,EAAE,EAAE,QAAQ;gBACrD,KAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAA;aACtC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,QAAQ,EAAE,EAAE,MAAM;gBAC/C,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;aAC3B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,QAAQ,EAAE,EAAE,MAAM;gBAC/C,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;aACjC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,gBAAgB,EAAE,EAAE,MAAM;gBACvD,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;aAClC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,mBAAmB,EAAE,EAAE,MAAM;gBAC1D,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,iBAAiB,EAAE,EAAE,WAAW;gBAC7D,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;aACnC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,UAAU;gBAC1D,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;aAC3B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,kBAAkB,EAAE,EAAE,SAAS;gBAC5D,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;aAC9B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,SAAS,EAAE,EAAE,MAAM;gBAChD,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;aAC7B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,MAAM;gBACtD,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;aAC7B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,QAAQ;gBACxD,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;gBAC/B,IAAM,KAAK,GAAG,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBACxC,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;gBACnD,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;aACtD;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,QAAQ;gBACxD,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;gBAC/B,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC1B,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;gBACnD,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aAC/C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,WAAW;gBAC3D,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBAC9B,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,CAAC,CAAA;aAC9C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,oBAAoB,EAAE,EAAE,QAAQ;gBAC7D,oBAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAA;aAC9C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,YAAY,EAAE,EAAE,SAAS;gBACtD,2BAAY,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;aAC5C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,iBAAiB,EAAE,EAAE,MAAM;gBACxD,KAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAA;gBAC1D,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAA;gBAC5D,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA;gBAChD,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;gBAC5C,KAAI,CAAC,oBAAoB,EAAE,CAAA;gBAC3B,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,mBAAmB,CAAC,CAAA;aAC3C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,iBAAiB,EAAE,EAAE,QAAQ;gBAC1D,KAAI,CAAC,eAAe,GAAG,IAAI,IAAI,CAAC,CAAA;gBAChC,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,mBAAmB,CAAC,CAAA;aAC3C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,YAAY,EAAE,EAAE,QAAQ;gBACrD,KAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;gBACpD,KAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;gBACpD,KAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;aACvD;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,iBAAiB,EAAE,EAAE,UAAU;gBAC5D,KAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,CAAA;gBACpC,KAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,IAAI,CAAC,CAAA;gBAChE,KAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAA;aAC/D;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,qBAAqB,EAAE,EAAE,SAAS;gBAC/D,KAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;aAC/B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,UAAU,EAAE,EAAE,QAAQ;gBACnD,uBAAU,CAAC,cAAc,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAA;aACvF;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,kBAAkB,EAAE,EAAE,QAAQ;gBAC3D,KAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;aAC/B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,iBAAiB,EAAE,EAAE,QAAQ;gBAC1D,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;aAC9B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAU,CAAC,gBAAgB,EAAE,EAAE,QAAQ;gBACzD,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;aAC7B;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IA1fD;QADC,EAAE,CAAC,QAAQ;uDAqBX;IAp0CgB,WAAW;QAD/B,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;OACD,WAAW,CA2yD/B;IAAD,kBAAC;CA3yDD,AA2yDC,CA3yDwC,EAAE,CAAC,SAAS,GA2yDpD;kBA3yDoB,WAAW", "file": "", "sourceRoot": "/", "sourcesContent": ["import { protoHelper } from \"../../../proto/ProtoHelper\"\nimport { cameraCtrl } from \"../../common/camera/CameraCtrl\"\nimport { ADD_OUTPUT_GOLD, COLOR_NORMAL, DEFAULT_BT_QUEUE_COUNT, EQUIP_SLOT_CONF, HERO_SLOT_LV_COND, IN_DONE_BT_GOLD, IN_DONE_FORGE_GOLD, PAWN_SLOT_CONF, TILE_SIZE } from \"../../common/constant/Constant\"\nimport { ArmyBaseInfo, ArmyShortInfo, InjuryPawnInfo } from \"../../common/constant/DataType\"\nimport { ecode } from \"../../common/constant/ECode\"\nimport { ArmyState, BUILD_NID, CEffect, CType, NotifyType, PreferenceKey, TaskState } from \"../../common/constant/Enums\"\nimport EventType from \"../../common/event/EventType\"\nimport DBHelper from \"../../common/helper/DBHelper\"\nimport { gameHpr } from \"../../common/helper/GameHelper\"\nimport { mapHelper } from \"../../common/helper/MapHelper\"\nimport { netHelper } from \"../../common/helper/NetHelper\"\nimport { reddotHelper } from \"../../common/helper/ReddotHelper\"\nimport { viewHelper } from \"../../common/helper/ViewHelper\"\nimport AllianceModel from \"./AllianceModel\"\nimport PawnObj from \"../area/PawnObj\"\nimport MerchantObj from \"../bazaar/MerchantObj\"\nimport BTInfoObj from \"./BTInfoObj\"\nimport CTypeObj from \"../common/CTypeObj\"\nimport EquipInfo from \"./EquipInfo\"\nimport ForgeEquipInfo from \"./ForgeEquipInfo\"\nimport NetworkModel from \"../common/NetworkModel\"\nimport OutputObj from \"./OutputObj\"\nimport PawnDrillInfoObj from \"./PawnDrillInfoObj\"\nimport PawnLevelingInfoObj from \"./PawnLevelingInfoObj\"\nimport PolicyObj from \"./PolicyObj\"\nimport SmeltEquipInfo from \"./SmeltEquipInfo\"\nimport TaskObj from \"../common/TaskObj\"\nimport UserModel from \"../common/UserModel\"\nimport { GuideTagType } from \"../guide/GuideConfig\"\nimport HeroSlotObj from \"./HeroSlotObj\"\nimport PawnCureInfoObj from \"./PawnCureInfoObj\"\nimport EquipSlotObj from \"./EquipSlotObj\"\nimport PawnSlotObj from \"./PawnSlotObj\"\nimport BaseStudyObj from \"./BaseStudyObj\"\n\n/**\n * 玩家\n */\*************('player')\nexport default class PlayerModel extends mc.BaseModel {\n\n    private net: NetworkModel = null\n    private user: UserModel = null\n    private alliance: AllianceModel = null\n\n    private initTime: number = 0 //初始化时间\n\n    private cereal: OutputObj = null //粮\n    private timber: OutputObj = null //木\n    private stone: OutputObj = null //石\n    private expBook: number = 0 //经验书\n    private iron: number = 0 //铁\n    private upScroll: number = 0 //卷轴\n    private fixator: number = 0 //固定器\n    private cerealConsume: number = 0 //当前粮耗\n    private granaryCap: number = 0 //粮仓容量\n    private warehouseCap: number = 0 //仓库容量\n    private stamina: number = 0 //当前奖励点\n    private captureInfo: any = null //被攻陷的信息\n    private sumOnlineTime: number = 0 //累计在线时间\n\n    private mainCityIndex: number = 0 //主城所在位置\n    private mainBuilds: { uid: string, id: number, lv: number }[] = [] //主城当前的建筑列表\n    private unlockPawnIds: number[] = [] //当前额外解锁的兵种列表\n    private unlockEquipIds: number[] = [] //当前额外解锁的装备列表\n    private btQueues: BTInfoObj[] = [] //当前的建造队列\n    private pawnDrillQueueMap: Map<string, PawnDrillInfoObj[]> = new Map() //士兵训练队列\n    private pawnLevelingQueues: PawnLevelingInfoObj[] = [] //士兵练级队列\n    private baseArmys: ArmyBaseInfo[] = [] //临时的军队列表\n    private armyDistMap: { [key: number]: ArmyBaseInfo[] } = {}\n    private merchants: MerchantObj[] = [] //商人列表\n    private guideTasks: TaskObj[] = [] //新手任务列表\n    private todayTasks: TaskObj[] = [] //每日任务列表\n    private otherTasks: TaskObj[] = [] //其他任务列表\n    private equips: EquipInfo[] = [] //已有的装备列表\n    private currForgeEquip: ForgeEquipInfo = null //当前打造装备信息\n    private currSmeltEquip: SmeltEquipInfo = null //当前正在融炼得装备信息\n    private configPawnMap: { [key: number]: { equipUid: string, skinId: number, attackSpeed: number } } = {} //配置士兵的信息\n    private citySkinConfigMap: { [key: number]: number } = {} //城市皮肤配置的信息\n    private fortAutoSupports: any[] = [] //要塞自动支援配置\n    private addOutputSurplusTime: { [key: number]: number } = {} //添加产量剩余时间\n    private getAddOutputTime: number = 0\n    private policySlots: { [key: number]: PolicyObj } = {} //当前政策槽位列表\n    private equipSlots: { [key: number]: EquipSlotObj } = {} //当前装备槽位列表\n    private pawnSlots: { [key: number]: PawnSlotObj } = {} //当前士兵槽位列表\n    private heroSlots: HeroSlotObj[] = [] //当前英雄槽位信息\n    private hidePChatChannels: { [key: string]: string } = {} //隐藏的私聊频道\n    private exitAllianceCount: number = 0 //退出联盟次数\n    private todayOccupyCellCount: number = 0 //每日打地数量\n    private accTotalGiveResCount: number = 0 //累计赠送资源数量\n    private todayReplacementCount: number = 0 //每日置换次数\n    private landScore: number = 0 //领地积分\n    private occupyLandCountMap: { [key: number]: number[] } = {} //历史攻占野地数量 key=地块等级 val=数量\n    private maxOccupyLandDifficulty: number = 0 //历史最大攻占野地难度\n    private killRecordMap: { [key: number]: number } = {} //击杀数量 key=id val=数量\n    private mapMarks: { name: string, point: cc.Vec2 }[] = [] //地图标记\n    private reCreateMainCityCount: number = 0 //重新创建主城次数\n    private cellTondenCount: number = 0 //每日屯田次数\n    private upRecruitPawnCount: number = 0 //加速招募士兵数量\n    private freeRecruitPawnCount: number = 0 //免费招募士兵数量\n    private freeLevingPawnCount: number = 0 //免费训练士兵数量\n    private freeCurePawnCount: number = 0 //免费治疗士兵数量\n    private freeForgeCount: number = 0 //免费打造/重铸数量\n    private isSettled: boolean = false //是否已结算（只有血战到底模式有该值）\n\n    private armyMaxCount: number = 0 //军队最大数量\n    private mainCityRect: { min: cc.Vec2, max: cc.Vec2 } = { min: cc.v2(), max: cc.v2() }\n    private injuryPawns: InjuryPawnInfo[] = [] // 可治疗的伤兵\n    private curingPawns: PawnCureInfoObj[] = [] // 治疗中的伤兵\n    private lastReqSelectArmysTime: number = 0 //最后一次请求军队时间\n    private tempSelectArmyErr: string = ''\n    private tempSelectArmyList: ArmyShortInfo[] = [] //临时的选择军队信息\n    private tempSelectArmyCanGotoCount: number = 0 //临时的选择军队 可前往数量\n    private lastReqArmysTime: number = 0 //最后一次请求军队时间\n    private tempArmyList: ArmyShortInfo[] = [] //临时的军队信息\n    private lastReqArmyMarchRecordTime: number = 0 //最后一次请求军队记录时间\n    private tempArmyMarchRecordList: any[] = [] //军队记录列表\n    private lastReqArmyBattleRecordTime: number = 0 //最后一次请求军队记录时间\n    private tempArmyBattleRecordList: any[] = [] //军队记录列表\n    private lastReqBazaarRecordTime: number = 0 //最后一次请求市场记录时间\n    private tempBazaarRecordList: any[] = [] //市场记录列表\n    private tempBattleForecastRets: any[] = [] //临时记录玩家战斗预测结果\n    private tempStopPlayMessageSound: any = {} //临时需要停止播放的音效\n    private tempCanForgeEquips: EquipSlotObj[] = null\n    private tempCanRecruitPawns: { [key: number]: PawnSlotObj[] } = {}\n\n    private _temp_vec2_1: cc.Vec2 = cc.v2()\n    private _temp_vec2_2: cc.Vec2 = cc.v2()\n\n    public onCreate() {\n        this.net = this.getModel('net')\n        this.user = this.getModel('user')\n        this.alliance = this.getModel('alliance')\n    }\n\n    // 初始化信息\n    public async init(data: any, isLocal?: boolean) {\n        cc.log('init player', data)\n        this.resetTempInfo()\n        this.lastReqSelectArmysTime = 0\n        this.lastReqArmysTime = 0\n        this.lastReqArmyMarchRecordTime = 0\n        this.lastReqArmyBattleRecordTime = 0\n        this.lastReqBazaarRecordTime = 0\n        this.initTime = Date.now()\n        this.sumOnlineTime = data.sumOnlineTime || 0\n        this.setCaptureInfo(data.captureInfo)\n        this.expBook = data.expBook || 0\n        this.iron = data.iron || 0\n        this.upScroll = data.upScroll || 0\n        this.fixator = data.fixator || 0\n        this.granaryCap = data.granaryCap || 1\n        this.warehouseCap = data.warehouseCap || 1\n        this.cereal = new OutputObj(EventType.UPDATE_CEREAL).fromSvr(data.cereal)\n        this.timber = new OutputObj(EventType.UPDATE_TIMBER).fromSvr(data.timber)\n        this.stone = new OutputObj(EventType.UPDATE_STONE).fromSvr(data.stone)\n        this.cerealConsume = data.cerealConsume || 0\n        this.stamina = data.stamina || 0\n        this.mainCityIndex = data.mainCityIndex || 0\n        this.mainBuilds = data.builds || []\n        this.unlockPawnIds = data.unlockPawnIds || []\n        this.unlockEquipIds = data.unlockEquipIds || []\n        this.updateBtQueue(data.btQueues || [], false)\n        this.updatePawnDrillQueue(data.pawnDrillQueues || {}, false)\n        this.updatePawnLevelingQueue(data.pawnLevelingQueues || [], false)\n        this.updatePawnCuringQueue(data.curingQueues || [], false)\n        this.updateArmyDists(data.armyDists || [], false)\n        this.merchants = (data.merchants || []).map(m => new MerchantObj().fromSvr(m))\n        this.equips = (data.equips || []).map(m => new EquipInfo().fromSvr(m))\n        this.configPawnMap = data.configPawnMap || {}\n        this.citySkinConfigMap = data.citySkinConfigMap || {}\n        this.fortAutoSupports = data.fortAutoSupports || []\n        this.updateAddOutputTime(data.addOutputSurplusTime)\n        this.updatePolicySlots(data.policySlots || {}, false)\n        this.updatePawnSlots(data.pawnSlots || {}, false)\n        this.updateEquipSlots(data.equipSlots || {}, false)\n        this.updateCurrForgeEquip(data.currForgeEquip)\n        this.updateCurrSmeltEquip(data.currSmeltEquip)\n        this.heroSlots = (data.heroSlots || []).map(m => new HeroSlotObj().fromSvr(m))\n        this.hidePChatChannels = data.hidePChatChannels || {}\n        this.exitAllianceCount = data.exitAllianceCount || 0\n        this.updateArmyMaxCount() //刷新军队最大数量\n        this.todayOccupyCellCount = data.todayOccupyCellCount || 0\n        this.accTotalGiveResCount = data.accTotalGiveResCount || 0\n        this.todayReplacementCount = data.todayReplacementCount || 0\n        this.landScore = data.landScore || 0\n        this.updateOccupyLandCountMap(data.occupyLandCountMap || {})\n        this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1\n        this.killRecordMap = data.killRecordMap || {}\n        this.mapMarks = (data.mapMarks || []).map(m => { return { name: m.name, point: cc.v2(m.point) } })\n        this.reCreateMainCityCount = data.reCreateMainCityCount || 0\n        this.cellTondenCount = data.cellTondenCount || 0\n        this.injuryPawns = data.injuryPawns || []\n        this.upRecruitPawnCount = data.upRecruitPawnCount || 0\n        this.freeRecruitPawnCount = data.freeRecruitPawnCount || 0\n        this.freeLevingPawnCount = data.freeLevingPawnCount || 0\n        this.freeCurePawnCount = data.freeCurePawnCount || 0\n        this.freeForgeCount = data.freeForgeCount || 0\n        this.isSettled = !!data.isSettled\n        this.updateGuideTasks(data.guideTasks || [], false)\n        this.updateTodayTasks(data.todayTasks || [], false) //需要放到这个位置 因为需要前面的todayOccupyCellCount\n        this.updateOtherTasks(data.otherTasks || [], false)\n        // 主城的矩形区域\n        this.updateMainCityRect(this.mainCityIndex)\n        if (!isLocal) {\n            // 获取联盟信息\n            await this.alliance.init(data.allianceUid || '')\n        }\n        // 宝箱\n        reddotHelper.set('treasure_main', !!data.hasNewTreasure)\n        if (this.guideTasks.length > 0) {\n            reddotHelper.unregister('guide_task')\n            reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1)\n        }\n        // 监听消息\n        this.net.on('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this)\n    }\n\n    public clean() {\n        this.net.off('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this)\n        this.tempBattleForecastRets = []\n        this.tempArmyList = []\n        this.mainBuilds = []\n        this.guideTasks = []\n        this.todayTasks = []\n        this.otherTasks = []\n        this.heroSlots = []\n        this.baseArmys = []\n        this.pawnDrillQueueMap.clear()\n        this.pawnLevelingQueues = []\n        this.tempSelectArmyList = []\n        this.tempArmyMarchRecordList = []\n        this.tempArmyBattleRecordList = []\n        this.tempBazaarRecordList = []\n        this.resetTempInfo()\n    }\n\n    private resetTempInfo() {\n        this.tempCanForgeEquips = null\n        this.tempCanRecruitPawns = {}\n    }\n\n    public getInitTIme() { return this.initTime }\n    public getToInitElapsedTime() { return Date.now() - this.initTime } //到初始化经过的时间\n    public getCaptureInfo() { return this.captureInfo }\n    public isCapture() { return !!this.captureInfo || !this.mainBuilds?.length } //是否沦陷\n    public getMainCityIndex() { return this.mainCityIndex }\n    public getMainCityPoint() { return mapHelper.indexToPoint(this.mainCityIndex) }\n    public getAlliance() { return this.alliance }\n    public getAllianceUid() { return this.alliance.getUid() }\n    public isHasAlliance() { return !!this.alliance.getUid() }\n    public getCereal() { return this.cereal?.value || 0 }\n    public getCerealOp() { return this.cereal?.opHour || 0 }\n    public setCereal(val: number, isEmit: boolean = true) { this.cereal.set(val, isEmit) }\n    public changeCereal(val: number, isEmit: boolean = true) { return this.cereal.change(val, isEmit) }\n    public getTimber() { return this.timber?.value || 0 }\n    public getTimberOp() { return this.timber?.opHour || 0 }\n    public setTimber(val: number, isEmit: boolean = true) { this.timber.set(val, isEmit) }\n    public changeTimber(val: number, isEmit: boolean = true) { return this.timber.change(val, isEmit) }\n    public getStone() { return this.stone?.value || 0 }\n    public getStoneOp() { return this.stone?.opHour || 0 }\n    public setStone(val: number, isEmit: boolean = true) { this.stone.set(val, isEmit) }\n    public changeStone(val: number, isEmit: boolean = true) { return this.stone.change(val, isEmit) }\n    public getCerealConsume() { return this.cerealConsume }\n    public getCerealCapRatio() { return this.getCereal() / this.granaryCap }\n    public getTimberCapRatio() { return this.getTimber() / this.warehouseCap }\n    public getStoneCapRatio() { return this.getStone() / this.warehouseCap }\n    public getGranaryCap() { return this.granaryCap }\n    public getWarehouseCap() { return this.warehouseCap }\n    public getAddOutputSurplusTime() { return this.addOutputSurplusTime }\n    public getAddOutputElapsedTime() { return Date.now() - this.getAddOutputTime }\n    public getExitAllianceCount() { return this.exitAllianceCount }\n    public setExitAllianceCount(val: number) { this.exitAllianceCount = val }\n    public getHeroSlots() { return this.heroSlots }\n    public getHidePChatChannels() { return this.hidePChatChannels }\n    public getUnlockPawnIds() { return this.unlockPawnIds }\n    public getUnlockEquipIds() { return this.unlockEquipIds }\n    public getTodayOccupyCellCount() { return this.todayOccupyCellCount }\n    public addTodayOccupyCellCount(val: number) { return this.todayOccupyCellCount += val }\n    public getAccTotalGiveResCount() { return this.accTotalGiveResCount }\n    public addAccTotalGiveResCount(val: number) { return this.accTotalGiveResCount += val }\n    public getTodayReplacementCount() { return this.todayReplacementCount }\n    public setTodayReplacementCount(val: number) { this.todayReplacementCount = val }\n    public getUpRecruitPawnCount() { return this.upRecruitPawnCount }\n    public setUpRecruitPawnCount(val: number) { this.upRecruitPawnCount = val }\n    public getFreeRecruitPawnCount() { return this.freeRecruitPawnCount }\n    public setFreeRecruitPawnCount(val: number) { this.freeRecruitPawnCount = val }\n    public getFreeLevingPawnCount() { return this.freeLevingPawnCount }\n    public setFreeLevingPawnCount(val: number) { this.freeLevingPawnCount = val }\n    public getFreeCurePawnCount() { return this.freeCurePawnCount }\n    public setFreeCurePawnCount(val: number) { this.freeCurePawnCount = val }\n    public getFreeForgeCount() { return this.freeForgeCount }\n    public setFreeForgeCount(val: number) { this.freeForgeCount = val }\n    public getMaxOccupyLandDifficulty() { return this.maxOccupyLandDifficulty }\n\n    public getLandScore() { return this.landScore }\n    public getReCreateMainCityCount() { return this.reCreateMainCityCount }\n    public getCellTondenCount() { return this.cellTondenCount }\n\n    public getSumOnlineTime() {\n        return this.sumOnlineTime + (Date.now() - this.initTime)\n    }\n\n    // 获取免费招募剩余次数\n    public getFreeRecruitPawnSurplusCount() {\n        let freeCount = gameHpr.getPlayerPolicyEffect(CEffect.FREE_DRILL_COUNT)\n        if (gameHpr.isNoviceMode) {\n            freeCount += 9 //新手村给9个\n        }\n        return Math.max(0, freeCount - this.freeRecruitPawnCount)\n    }\n\n    // 获取免费训练士兵剩余次数\n    public getFreeLevingPawnSurplusCount() {\n        return Math.max(0, gameHpr.getPlayerPolicyEffect(CEffect.FREE_LEVING_COUNT) - this.freeLevingPawnCount)\n    }\n\n    // 获取免费治疗士兵剩余次数\n    public getFreeCurePawnSurplusCount() {\n        return Math.max(0, gameHpr.getPlayerPolicyEffect(CEffect.CURE_FREE_COUNT) - this.freeCurePawnCount)\n    }\n\n    // 获取免费免费打造/重铸剩余次数\n    public getfreeForgeSurplusCount() {\n        return Math.max(0, gameHpr.getPlayerPolicyEffect(CEffect.FREE_RECAST_COUNT) - this.freeForgeCount)\n    }\n\n    // 兼容刷新一下固定菜单\n    public updateFixationMenuData() {\n        const ids: number[] = this.user.getLocalPreferenceDataBySid(PreferenceKey.FIXATION_MENU_DATA) || []\n        if (!this.captureInfo) {\n            const len = ids.length\n            ids.delete(id => !this.mainBuilds.has('id', id))\n            if (ids.length !== len) {\n                this.user.setLocalPreferenceDataBySid(PreferenceKey.FIXATION_MENU_DATA, ids)\n            }\n        } else if (ids.length > 0) {\n            this.user.setLocalPreferenceDataBySid(PreferenceKey.FIXATION_MENU_DATA, [])\n        }\n    }\n\n    public updateMainCityRect(index: number) {\n        const pos = mapHelper.indexToPoint(index).mul(TILE_SIZE)\n        this.mainCityRect.min.x = pos.x\n        this.mainCityRect.min.y = pos.y\n        this.mainCityRect.max.x = pos.x + TILE_SIZE * 2\n        this.mainCityRect.max.y = pos.y + TILE_SIZE * 2\n    }\n\n    // 主城是否不在屏幕范围内\n    public checkMainNotInScreenRange() {\n        if (this.isCapture() && !gameHpr.isSpectate()) {\n            return false\n        }\n        const outMin = cameraCtrl.getWorldToScreenPoint(this.mainCityRect.min, this._temp_vec2_1)\n        const outMax = cameraCtrl.getWorldToScreenPoint(this.mainCityRect.max, this._temp_vec2_2)\n        return outMax.x <= 0 || outMax.y <= 0 || outMin.x >= cc.winSize.width || outMin.y >= cc.winSize.height\n    }\n\n    // 经验书\n    public getExpBook() { return this.expBook }\n    public setExpBook(val: number, isEmit: boolean = true) {\n        if (val === undefined || isNaN(val)) {\n            return\n        }\n        const add = Math.floor(val - this.expBook)\n        this.expBook = Math.floor(val)\n        if (isEmit) {\n            this.emit(EventType.UPDATE_EXP_BOOK, add)\n        }\n    }\n\n    // 铁\n    public getIron() { return this.iron }\n    public setIron(val: number, isEmit: boolean = true) {\n        if (val === undefined || isNaN(val)) {\n            return\n        }\n        const add = Math.floor(val - this.iron)\n        this.iron = Math.floor(val)\n        if (isEmit) {\n            this.emit(EventType.UPDATE_IRON, add)\n        }\n    }\n\n    // 卷轴\n    public getUpScroll() { return this.upScroll }\n    public setUpScroll(val: number, isEmit: boolean = true) {\n        if (val === undefined || isNaN(val)) {\n            return\n        }\n        const add = Math.floor(val - this.upScroll)\n        this.upScroll = Math.floor(val)\n        if (isEmit) {\n            this.emit(EventType.UPDATE_UPSCROLL, add)\n        }\n    }\n\n    // 固定器\n    public getFixator() { return this.fixator }\n    public setFixator(val: number, isEmit: boolean = true) {\n        if (val === undefined || isNaN(val)) {\n            return\n        }\n        const add = Math.floor(val - this.fixator)\n        this.fixator = Math.floor(val)\n        if (isEmit) {\n            this.emit(EventType.UPDATE_FIXATOR, add)\n        }\n    }\n\n    // 奖励点\n    public getStamina() { return this.stamina }\n    public setStamina(val: number, isEmit: boolean = true) {\n        if (val === undefined || isNaN(val)) {\n            return\n        }\n        const add = Math.floor(val - this.stamina)\n        this.stamina = Math.floor(val)\n        if (isEmit) {\n            this.emit(EventType.UPDATE_STAMINA, add)\n        }\n    }\n\n    public setUnlockEquipIds(ids: number[]) {\n        this.unlockEquipIds = ids || []\n        this.tempCanForgeEquips = null //重新获取\n    }\n\n    public setUnlockPawnIds(ids: number[]) {\n        this.unlockPawnIds = ids || []\n        this.tempCanRecruitPawns = {} //重新获取\n    }\n\n    // 获取修建队列\n    public getBtQueueCount() {\n        return DEFAULT_BT_QUEUE_COUNT + gameHpr.getPlayerPolicyEffect(CEffect.BT_QUEUE)\n    }\n\n    // 设置被沦陷了\n    public setCaptureInfo(data: any) {\n        if (!data?.uid) {\n            this.captureInfo = null\n        } else {\n            this.captureInfo = data\n            const ids: number[] = this.user.getLocalPreferenceDataBySid(PreferenceKey.FIXATION_MENU_DATA)\n            if (ids && ids.length > 0) {\n                this.user.setLocalPreferenceDataBySid(PreferenceKey.FIXATION_MENU_DATA, [])\n            }\n            // 固定器效果重置\n            this.user.setLocalPreferenceDataBySid(PreferenceKey.LOCK_EQUIP_EFFECT_CONF, {})\n        }\n    }\n\n    // 主城建筑列表信息\n    public getMainBuilds() { return this.mainBuilds }\n    public updateMainBuildInfo(data: any) {\n        gameHpr.cleanUnlockBuildCondText() //清理一下 因为建筑有改变\n        const build = this.mainBuilds.find(m => m.uid === data.uid)\n        if (build) {\n            build.lv = data.lv\n        } else {\n            this.mainBuilds.push(data)\n        }\n        if (data.id === BUILD_NID.MAIN) {\n            this.updateArmyMaxCount()\n        }\n        this.emit(EventType.MAIN_BUILD_CHANGE_LV, data)\n    }\n\n    // 获取建筑等级\n    public getBuildLv(id: number) {\n        return this.mainBuilds.find(m => m.id === id)?.lv || 0\n    }\n\n    public getMainBuildLv() {\n        return this.getBuildLv(BUILD_NID.MAIN)\n    }\n\n    private updateArmyMaxCount() {\n        const lv = this.getMainBuildLv()\n        const effect = gameHpr.stringToCEffects(assetsMgr.getJsonData('buildAttr', BUILD_NID.MAIN * 1000 + lv)?.effects)[0]\n        this.armyMaxCount = this.isCapture() ? 0 : effect?.value || 4\n    }\n\n    // 当前的军队最大数量\n    public getArmyMaxCount() {\n        return this.armyMaxCount + gameHpr.getPlayerPolicyEffect(CEffect.ARMY_COUNT)\n    }\n\n    // 军队数量是否满了\n    public isArmyCountFull() {\n        return this.baseArmys.length >= this.getArmyMaxCount()\n    }\n\n    // 军队分布信息\n    public getBaseArmys() { return this.baseArmys }\n    public getArmyDistMap() { return this.armyDistMap }\n    public getDistArmysByIndex(index: number) { return this.armyDistMap[index] || [] }\n    public updateArmyDists(datas: any[], isEmit: boolean = true) {\n        this.armyDistMap = {}\n        this.baseArmys.length = 0\n        datas.forEach(m => {\n            const armys = []\n            m.armys.forEach(army => {\n                army.index = m.index\n                if (army.state !== ArmyState.MARCH) {\n                    armys.push(army)\n                }\n                this.baseArmys.push(army)\n                this.updateTempArmyIndex(army)\n            })\n            if (armys.length > 0) {\n                this.armyDistMap[m.index] = armys\n            }\n        })\n        isEmit && this.emit(EventType.UPDATE_ARMY_DIST_INFO)\n    }\n\n    // 是否可以刷新产出\n    private isCanUpdateOutput() {\n        return this.user.getSid() > 0 || gameHpr.isNoviceMode\n    }\n\n    // 刷新产出信息\n    private updateOutput(data: any) {\n        data = data || {}\n        this.granaryCap = data.granaryCap ?? this.granaryCap\n        this.warehouseCap = data.warehouseCap ?? this.warehouseCap\n        this.cereal.updateInfo(data.cereal)\n        this.timber.updateInfo(data.timber)\n        this.stone.updateInfo(data.stone)\n        this.cerealConsume = data.cerealConsume ?? this.cerealConsume\n        // 如果只更新了容量\n        if ((data.granaryCap || data.warehouseCap) && (!data.cereal && !data.timber && !data.stone)) {\n            this.emit(EventType.UPDATE_RES_CAP)\n        }\n    }\n\n    // 刷新产出信息（位标记更新）\n    public updateOutputByFlags(data: any) {\n        if (!this.isCanUpdateOutput()) {\n            return\n        } else if (data?.flag === undefined) {\n            return this.updateOutput(data)\n        } else if (data.flag === 0) {\n            return\n        }\n        const granaryCapFlag = protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.GranaryCap)\n        const warehouseCapFlag = protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarehouseCap)\n        const cerealFlag = protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Cereal)\n        const timberFlag = protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Timber)\n        const stoneFlag = protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stone)\n        const cerealConsumeFlag = protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CerealConsume)\n        //\n        if (granaryCapFlag) this.granaryCap = data.granaryCap ?? this.granaryCap\n        if (warehouseCapFlag) this.warehouseCap = data.warehouseCap ?? this.warehouseCap\n        if (cerealFlag) this.cereal.updateInfo(data.cereal)\n        if (timberFlag) this.timber.updateInfo(data.timber)\n        if (stoneFlag) this.stone.updateInfo(data.stone)\n        if (cerealConsumeFlag) this.cerealConsume = data.cerealConsume ?? this.cerealConsume\n        // 如果只更新了容量\n        if ((granaryCapFlag || warehouseCapFlag) && (!cerealFlag && !timberFlag && !stoneFlag)) {\n            this.emit(EventType.UPDATE_RES_CAP)\n        }\n    }\n\n    // 刷新奖励信息\n    public updateRewardItems(data: any) {\n        if (data) {\n            this.user.setGold(data.gold)\n            this.user.setIngot(data.ingot)\n            this.user.setWarToken(data.warToken)\n            this.user.setTitles(data.titles)\n            this.updateOutputByFlags(data)\n            if (this.isCanUpdateOutput()) {\n                this.setExpBook(data.expBook)\n                this.setIron(data.iron)\n                this.setUpScroll(data.upScroll)\n                this.setFixator(data.fixator)\n                this.setStamina(data.stamina)\n                this.setUpRecruitPawnCount(data.upRecruitPawnCount)\n            }\n        }\n    }\n\n    // 刷新奖励信息（位标记更新）\n    public updateRewardItemsByFlags(data: any) {\n        if (data?.flag === undefined) {\n            return this.updateRewardItems(data)\n        } else if (data.flag) {\n            this.updateOutputByFlags(data)\n            if (this.isCanUpdateOutput()) {\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ExpBook)) this.setExpBook(data.expBook)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Iron)) this.setIron(data.iron)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpScroll)) this.setUpScroll(data.upScroll)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Fixator)) this.setFixator(data.fixator)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Equip)) this.setUnlockEquipIds(data.unlockEquipIds)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Pawn)) this.setUnlockPawnIds(data.unlockPawnIds)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stamina)) this.setStamina(data.stamina)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpRecruit)) this.setUpRecruitPawnCount(data.upRecruitPawnCount)\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeRecruit)) this.setFreeRecruitPawnCount(data.freeRecruitPawnCount) //免费招募\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeLeving)) this.setFreeLevingPawnCount(data.freeLevingPawnCount) //免费训练\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeCure)) this.setFreeCurePawnCount(data.freeCurePawnCount) //免费治疗\n                if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeForge)) this.setFreeForgeCount(data.freeForgeCount) //免费打造\n            }\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Gold)) this.user.setGold(data.gold)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Ingot)) this.user.setIngot(data.ingot)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarToken)) this.user.setWarToken(data.warToken)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Title)) this.user.setTitles(data.titles)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.PawnSkin)) this.user.setUnlockPawnSkinIds(data.pawnSkins)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Portrayal)) this.user.setPortrayals(data.portrayals)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.SkinItemEnum)) this.user.setSkinItemList(data.skinItems)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CitySkin)) this.user.setUnlockCitySkinIds(data.citySkins)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.HeadIcon)) this.user.setUnlockHeadIcons(data.unlockHeadIcons)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ChatEmoji)) this.user.setUnlockChatEmojiIds(data.unlockChatEmojiIds)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.RankCoin)) this.user.setRankCoin(data.rankCoin)\n            if (protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Botany)) this.user.setUnlockBotanys(data.unlockBotanys)\n        }\n    }\n\n    // 兵营训练队列\n    public getPawnDrillQueues(uid: string) { return this.pawnDrillQueueMap.get(uid) || [] }\n    public updatePawnDrillQueue(datas: any, isEmit: boolean = true) {\n        this.pawnDrillQueueMap.clear()\n        for (let uid in datas) {\n            this.pawnDrillQueueMap.set(uid, datas[uid].list.map(m => {\n                const data = new PawnDrillInfoObj().fromSvr(m)\n                // 添加训练完成消息\n                if (data.surplusTime > 0) {\n                    const type = Math.floor(data.id / 100)\n                    gameHpr.addMessage({\n                        key: 'ui.message_101',\n                        params: ['pawnText.name_' + data.id, type === 35 ? 'ui.button_produce' : 'ui.button_drill'],\n                        tag: data.uid,\n                        delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),\n                        // check: () => this.pawnDrillQueueMap.get(uid).has('uid', data.uid),\n                    })\n                }\n                return data\n            }))\n        }\n        // 检测\n        this.pawnDrillQueueMap.forEach((arr, key) => {\n            if (arr.length === 1 && !arr[0].surplusTime) {\n                this.pawnDrillQueueMap.delete(key)\n            }\n        })\n        isEmit && this.emit(EventType.UPDATE_PAWN_DRILL_QUEUE, this.mainCityIndex)\n    }\n    public getAllPawnDrillList() {\n        const arr: PawnDrillInfoObj[] = []\n        this.pawnDrillQueueMap.forEach(m => arr.pushArr(m))\n        return arr\n    }\n\n    // 获取某个军队训练士兵剩余时间\n    public getSumDrillTimeByArmy(uid: string) {\n        let maxTime = 0\n        this.pawnDrillQueueMap.forEach(arr => {\n            let time = 0\n            arr.forEach(m => {\n                if (m.surplusTime > 0) {\n                    time += m.getSurplusTime()\n                } else {\n                    time += m.needTime\n                }\n                if (m.auid === uid && time > maxTime) {\n                    maxTime = time\n                }\n            })\n        })\n        return { time: maxTime }\n    }\n\n    // 获取某个军队治疗士兵剩余时间\n    public getSumCuringTimeByArmy(uid: string) {\n        let maxTime = 0, time = 0\n        this.curingPawns.forEach(m => {\n            if (m.surplusTime > 0) {\n                time += m.getSurplusTime()\n            } else {\n                time += m.needTime\n            }\n            if (m.auid === uid && time > maxTime) {\n                maxTime = time\n            }\n        })\n        return { time: maxTime }\n    }\n\n    // 获取受伤的士兵\n    public getInjuryPawns() {\n        return this.injuryPawns\n    }\n\n    public updateInjuryPawns(datas: any) {\n        this.injuryPawns = datas\n    }\n\n    public addInjuryPawn(data: InjuryPawnInfo) {\n        this.injuryPawns.push(data)\n        this.emit(EventType.UPDATE_PAWN_INJURY_QUEUE)\n    }\n\n    public removeInjuryPawn(uid: string) {\n        this.injuryPawns.remove('uid', uid)\n        this.emit(EventType.UPDATE_PAWN_INJURY_QUEUE)\n    }\n\n    // 获取治疗中的士兵\n    public getCuringPawnsQueue() {\n        return this.curingPawns\n    }\n\n    public updatePawnCuringQueue(datas: any, isEmit: boolean = true) {\n        this.curingPawns.length = 0\n        for (let i = 0; i < datas.length; i++) {\n            const data = new PawnCureInfoObj().fromSvr(datas[i])\n            // 添加治疗完成消息\n            if (data.surplusTime > 0) {\n                // const type = Math.floor(data.id / 100)\n                gameHpr.addMessage({\n                    key: 'ui.message_101',\n                    params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv]), 'ui.button_cure'],\n                    tag: data.uid,\n                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),\n                })\n            }\n            this.curingPawns.push(data)\n        }\n        isEmit && this.emit(EventType.UPDATE_PAWN_CURING_QUEUE, this.mainCityIndex)\n    }\n\n    // 获取士兵练级队列\n    public getPawnLevelingQueues() { return this.pawnLevelingQueues }\n    public updatePawnLevelingQueue(datas: any[], isEmit: boolean = true) {\n        this.pawnLevelingQueues = datas.map(m => {\n            const data = new PawnLevelingInfoObj().fromSvr(m)\n            // 添加训练完成消息\n            if (data.surplusTime > 0) {\n                gameHpr.addMessage({\n                    key: 'ui.message_104',\n                    params: ['pawnText.name_' + data.id, data.lv],\n                    tag: data.uid,\n                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),\n                })\n            }\n            return data\n        })\n        isEmit && this.emit(EventType.UPDATE_PAWN_LVING_QUEUE, this.mainCityIndex)\n    }\n\n    // 是否在队列中\n    public isInPawnLvingQueue(uid: string) { return this.pawnLevelingQueues.has('puid', uid) }\n\n    // 获取某个军队士兵练级剩余时间\n    public getSumLvingTimeByArmy(uid: string) {\n        let maxTime = 0, isBattleing = gameHpr.isBattleingByIndex(this.mainCityIndex), pause = true\n        let time = 0\n        this.pawnLevelingQueues.forEach(m => {\n            if (m.surplusTime > 0) {\n                time += isBattleing ? m.surplusTime : m.getSurplusTime()\n                pause = isBattleing\n            } else {\n                time += m.needTime\n            }\n            if (m.auid === uid && time > maxTime) {\n                maxTime = time\n            }\n        })\n        return { time: maxTime, pause }\n    }\n\n    public getConfigPawnMap() { return this.configPawnMap }\n    // 获取配置士兵的装备信息\n    public getConfigPawnInfo(pawnId: number) {\n        if (!pawnId) {\n            return null\n        }\n        let info = this.configPawnMap[pawnId], uid = info?.equipUid\n        let conf = {\n            equip: { uid: '', attrs: [] },\n            skinId: info?.skinId || 0,\n            attackSpeed: info?.attackSpeed || 0,\n        }\n        // 兼容装备\n        if (uid) {\n            const equip = this.getEquipByUid(uid)\n            if (equip && (!equip.isExclusive() || equip.checkExclusivePawn(pawnId))) {\n                conf.equip = { uid: equip.uid, attrs: equip.attrs }\n            } else {\n                conf.equip = { uid: '', attrs: [] }\n            }\n            if (info) {\n                info.equipUid = conf.equip.uid\n            }\n        }\n        return conf\n    }\n\n    public changeConfigPawnInfo(id: number, equipUid: string, skinId: number, attackSpeed: number) {\n        this.configPawnMap[id] = { equipUid, skinId, attackSpeed }\n    }\n\n    // 改变皮肤配置\n    public changeConfigPawnInfoByData(data: PawnObj) {\n        const conf = this.configPawnMap[data.id]\n        if (conf) {\n            conf.skinId = data.skinId\n            conf.equipUid = data.equip?.uid || ''\n        } else {\n            this.configPawnMap[data.id] = { equipUid: data.equip?.uid || '', skinId: data.skinId, attackSpeed: data.attackSpeed }\n        }\n        const buildId = data.baseJson?.spawn_build_id\n        if (buildId) {\n            this.tempCanRecruitPawns[buildId] = null\n        }\n    }\n\n    // 获取城市皮肤配置\n    public getCitySkinConfigMap() { return this.citySkinConfigMap }\n\n    private getStudySlots(slots: { [key: number]: BaseStudyObj }) {\n        const list: any[] = []\n        for (let k in slots) {\n            const slot = slots[k]\n            if (slot.isYetStudy()) {\n                list.push(slot)\n            }\n        }\n        return list\n    }\n\n    // ---------------------------------------------------------政策----------------------------------------------------------------\n    // 当前政策槽位\n    public getPolicySlots() { return this.policySlots }\n    // 获取已经研究的政策ids\n    public getStudyPolicySlots(): PolicyObj[] { return this.getStudySlots(this.policySlots) }\n    // 刷新\n    public updatePolicySlots(slots: any, isEmit: boolean = true) {\n        this.policySlots = gameHpr.fromSvrByStudyData(slots, PolicyObj)\n        reddotHelper.set('can_study_policy', gameHpr.checkStudySlotsReddot(this.policySlots))\n        isEmit && this.emit(EventType.UPDATE_POLICY_SLOTS)\n    }\n\n    // ---------------------------------------------------------士兵----------------------------------------------------------------\n    // 当前士兵槽位\n    public getPawnSlots() { return this.pawnSlots }\n    public getStudyPawnSlots(): PawnSlotObj[] { return this.getStudySlots(this.pawnSlots) }\n    public getPawnSlotByLv(lv: number) { return this.pawnSlots[lv] }\n    public updatePawnSlots(slots: any, isEmit: boolean = true) {\n        this.pawnSlots = gameHpr.fromSvrByStudyData(slots, PawnSlotObj)\n        this.checkPawnSlotInfo()\n        reddotHelper.set('can_study_pawn', gameHpr.checkStudySlotsReddot(this.pawnSlots))\n        isEmit && this.emit(EventType.UPDATE_PAWN_SLOTS)\n    }\n\n    // 检测槽位\n    private checkPawnSlotInfo() {\n        PAWN_SLOT_CONF.forEach(lv => {\n            const slot = this.pawnSlots[lv]\n            if (slot) {\n                slot.initPawn(this.mainCityIndex, this.getConfigPawnInfo(slot.id))\n            } else {\n                this.pawnSlots[lv] = new PawnSlotObj().fromSvr({ lv }).init()\n            }\n        })\n        this.tempCanRecruitPawns = {}\n    }\n\n    // 获取可以招募的士兵\n    public getCanRecruitPawns(buildId: number) {\n        let slots = this.tempCanRecruitPawns[buildId]\n        if (slots) {\n            return slots\n        }\n        slots = this.tempCanRecruitPawns[buildId] = []\n        const hasMap = {}\n        // 加入固定的槽位信息\n        PAWN_SLOT_CONF.forEach(lv => {\n            let slot = this.pawnSlots[lv]\n            if (slot) {\n                hasMap[slot.id] = true\n            } else {\n                slot = new PawnSlotObj().fromSvr({ lv }).init()\n            }\n            slots.push(slot)\n        })\n        // 加入英雄殿固定的几个槽位信息\n        this.heroSlots.forEach(m => {\n            const id = m.hero?.avatarPawn\n            if (!id) {\n                slots.push(new PawnSlotObj().fromSvr({ lv: -m.lv }).init())\n            } else if (!hasMap[id]) {\n                hasMap[id] = true\n                slots.push(new PawnSlotObj().fromSvr({ lv: -m.lv, id }).init().initPawn(this.mainCityIndex, this.getConfigPawnInfo(id)))\n            }\n        })\n        // 加入直接解锁的士兵\n        this.unlockPawnIds.forEach(id => {\n            if (!hasMap[id] && assetsMgr.getJsonData('pawnBase', id)?.spawn_build_id === buildId) {\n                hasMap[id] = true\n                slots.push(new PawnSlotObj().fromSvr({ id, lv: 1 }).init().initPawn(this.mainCityIndex, this.getConfigPawnInfo(id)))\n            }\n        })\n        return slots\n    }\n\n    // 获取可生产的器械\n    public getCanProduceMachines(buildId: number) {\n        let slots = this.tempCanRecruitPawns[buildId]\n        if (slots) {\n            return slots\n        }\n        slots = this.tempCanRecruitPawns[buildId] = []\n        assetsMgr.getJson('pawnBase').datas.filter(m => m.spawn_build_id === buildId && !m.need_unlock).forEach(m => {\n            slots.push(new PawnSlotObj().fromSvr({ id: m.id, lv: m.need_build_lv }).init().initPawn(this.mainCityIndex, this.getConfigPawnInfo(m.id)))\n        })\n        return slots\n    }\n    // ---------------------------------------------------------装备----------------------------------------------------------------\n    // 当前装备槽位\n    public getEquipSlots() { return this.equipSlots }\n    public getStudyEquipSlots(): EquipSlotObj[] { return this.getStudySlots(this.equipSlots) }\n    public getEquipSlotByLv(lv: number) { return this.equipSlots[lv] }\n    public updateEquipSlots(slots: any, isEmit: boolean = true) {\n        this.equipSlots = gameHpr.fromSvrByStudyData(slots, EquipSlotObj)\n        this.checkEquipSlotInfo()\n        reddotHelper.set('can_study_equip', gameHpr.checkStudySlotsReddot(this.equipSlots))\n        isEmit && this.emit(EventType.UPDATE_EQUIP_SLOTS)\n    }\n\n    // 将打造的装备更新到槽位里面\n    private checkEquipSlotInfo() {\n        const equipMap = {}\n        this.equips.forEach(m => equipMap[m.uid] = m)\n        // 刷新已经打造的信息\n        EQUIP_SLOT_CONF.forEach(lv => {\n            const slot = this.equipSlots[lv]\n            if (!slot) {\n                this.equipSlots[lv] = new EquipSlotObj().fromSvr({ lv }).init()\n            } else if (slot?.isYetStudy()) {\n                slot.equip = equipMap[slot.uid]\n            }\n        })\n        this.tempCanForgeEquips = null //重新获取\n    }\n\n    // 获取可以打造的装备\n    public getCanForgeEquips() {\n        if (this.tempCanForgeEquips && this.tempCanForgeEquips.length > 0) {\n            return this.tempCanForgeEquips\n        }\n        const hasMap = {}\n        this.tempCanForgeEquips = []\n        EQUIP_SLOT_CONF.forEach(lv => {\n            const slot = this.equipSlots[lv]\n            if (slot) {\n                hasMap[slot.id] = true\n                this.tempCanForgeEquips.push(slot)\n            }\n        })\n        this.equips.forEach(m => {\n            if (!hasMap[m.id]) {\n                hasMap[m.id] = true\n                this.tempCanForgeEquips.push(new EquipSlotObj().fromSvr({ id: m.id }).init().setEquip(m))\n            }\n        })\n        // 加入直接解锁的\n        this.unlockEquipIds.forEach(m => {\n            if (!hasMap[m]) {\n                hasMap[m] = true\n                this.tempCanForgeEquips.push(new EquipSlotObj().fromSvr({ id: m, lv: 1 }).init())\n            }\n        })\n        return this.tempCanForgeEquips\n    }\n\n    // 打造装备\n    public async forgeEquip(uid: string, lockEffect: number) {\n        const { err, data } = await netHelper.reqForgeEquip({ uid, lockEffect })\n        if (!err) {\n            const equip = this.getEquipByUid(uid)\n            if (equip) {\n                equip.nextForgeFree = !!data.nextForgeFree\n            }\n            this.updateCurrForgeEquip(data.currForgeEquip)\n            this.updateRewardItemsByFlags(data.cost)\n            this.setFreeForgeCount(data.freeForgeCount || 0)\n            this.emit(EventType.FORGE_EQUIP_BEGIN)\n        }\n        return err\n    }\n\n    // 立即完成打造\n    public async inDoneForge() {\n        if (this.user.getGold() < IN_DONE_FORGE_GOLD) {\n            return ecode.GOLD_NOT_ENOUGH\n        }\n        const { err, data } = await this.net.request('game/HD_InDoneForge', {}, true)\n        if (!err) {\n            this.user.setGold(data.gold)\n        }\n        return err\n    }\n\n    // 获取当前打造装备信息\n    public getCurrForgeEquip() { return this.currForgeEquip }\n    public updateCurrForgeEquip(data: any) {\n        this.currForgeEquip = data ? new ForgeEquipInfo().fromSvr(data) : null\n        if (this.currForgeEquip) {\n            this.currForgeEquip.isYetForge = !!this.equips.find(m => m.id === data.id)\n        }\n    }\n\n    // 刷新装备\n    public updateEquip(data: any) {\n        const equip: EquipInfo = this.equips.find(m => m.uid === data.uid)\n        if (equip) {\n            equip.updateInfo(data)\n        } else {\n            this.equips.push(new EquipInfo().fromSvr(data))\n            this.checkEquipSlotInfo()\n        }\n        return !equip //是否新装备\n    }\n\n    private updateEquipInfo(data: any) {\n        const isNew = this.updateEquip(data)\n        this.updatePawnEquipAttr(data.uid, data.attrs)\n        this.tempCanRecruitPawns = {} //重新获取可训练的士兵 因为士兵有可能有装备\n        return isNew\n    }\n\n    public getEquips() { return this.equips }\n    public getEquipById(id: number) { return id ? this.equips.find(m => m.id === id) : null }\n    public getEquipByUid(uid: string) { return uid ? this.equips.find(m => m.uid === uid) : null }\n\n    // 获取士兵可以携带的装备列表\n    public getPawnEquips(pawnId: number) {\n        return this.equips.filter(m => !m.exclusive_pawn || m.exclusive_pawn === pawnId)\n    }\n\n    // 获取已经参与融炼的装备idMap\n    public getYetSmeltEquipIdMap(uid: string) {\n        const smeltEquipIdMap: { [key: number]: boolean } = {}\n        this.equips.forEach(m => {\n            if (m.uid !== uid) {\n                m.smeltEffects.forEach(m => smeltEquipIdMap[m.id] = true)\n            }\n        })\n        return smeltEquipIdMap\n    }\n\n    // 添加打造消息通知\n    private addForgeMessage(id: number, isNew: boolean) {\n        gameHpr.addMessage({\n            key: isNew ? 'ui.message_102' : 'ui.message_103',\n            params: ['equipText.name_' + id],\n            tag: id + '',\n        })\n    }\n\n    // 获取正在融炼的装备\n    public getCurrSmeltEquip() { return this.currSmeltEquip }\n\n    // 刷新融炼装备\n    private updateCurrSmeltEquip(data: any) {\n        this.currSmeltEquip = data ? new SmeltEquipInfo().fromSvr(data) : null\n    }\n\n    // 融炼装备\n    public async smeltEquip(mainUid: string, viceIds: number[]) {\n        const { err, data } = await this.net.request('game/HD_SmeltingEquip', { mainUid, viceIds }, true)\n        if (!err) {\n            this.updateCurrSmeltEquip(data.currSmeltEquip)\n            this.setFixator(data.fixator)\n        }\n        return err\n    }\n\n    // 还原融炼\n    public async restoreSmeltEquip(mainUid: string) {\n        const { err, data } = await this.net.request('game/HD_RestoreSmeltEquip', { mainUid }, true)\n        if (!err) {\n            this.updateEquipInfo(data.equip)\n            this.emit(EventType.UPDATE_EQUIP_ATTR, data.equip.uid)\n        }\n        return err\n    }\n\n    // 刷新士兵的装备等级\n    private updatePawnEquipAttr(uid: string, attrs: number[][]) {\n        const areaCenter = gameHpr.areaCenter\n        this.baseArmys.forEach(m => {\n            const area = areaCenter.getArea(m.index)\n            if (area && !area.isBattleing()) {\n                area.getArmyByUid(m.uid)?.pawns.forEach(pawn => pawn.updateEquipAttr(uid, attrs))\n            }\n        })\n    }\n\n    // 还原装备属性\n    public async restoreEquipAttr(uid: string) {\n        const { err, data } = await netHelper.reqRestoreForge({ uid })\n        if (!err) {\n            this.setIron(data.iron)\n            this.updateEquipInfo(data.equip)\n            this.emit(EventType.UPDATE_EQUIP_ATTR, data.equip.uid)\n        }\n        return err\n    }\n\n    // 添加打造消息通知\n    private addSmeltMessage(id: number) {\n        gameHpr.addMessage({\n            key: 'ui.message_109',\n            tag: id + '',\n        })\n    }\n\n    // 获取要塞自动资源配置\n    public getFortAutoSupports() { return this.fortAutoSupports }\n\n    // 是否自动支援\n    public isForAutoSupport(index: number) {\n        return !!this.fortAutoSupports.find(m => m.index === index)?.isAuto\n    }\n\n    // 刷新自动支援配置\n    public updateForAutoSupport(index: number, isAuto: boolean) {\n        let data = this.fortAutoSupports.find(m => m.index === index)\n        if (data) {\n            data.isAuto = isAuto\n        } else {\n            this.fortAutoSupports.push({ index, isAuto })\n        }\n    }\n\n    // 建筑升级效果通知\n    private addBuildEffectMessage(data: BTInfoObj) {\n        if (!BUILD_NID[data.id]) {\n            return\n        }\n        // 查找定义的效果文本\n        const key = 'ui.msg_build_effect_' + BUILD_NID[data.id].toLowerCase()\n        const localeText = assetsMgr.lang(key)\n        if (localeText === key) {\n            return\n        }\n        let diff = DBHelper.buildEffectDelta(data.id, data.lv - 1, data.lv)\n        let sign = '-'\n        let unit = '%'\n        if ([BUILD_NID.MAIN, BUILD_NID.WAREHOUSE, BUILD_NID.GRANARY, BUILD_NID.FREE_BAZAAR, BUILD_NID.ALLI_BAZAAR].includes(data.id)) {\n            sign = '+'\n            unit = ''\n        }\n        let delay = 0.5\n        if (diff > 0) {\n            gameHpr.addMessage({\n                key,\n                params: [` <color=${COLOR_NORMAL.DONE}>${sign}${diff}${unit}</color>`],\n                tag: data.uid + '_desc',\n                delay: Math.max(0, data.getSurplusTime() * 0.001 - 1) + delay,\n            })\n            delay += 0.5\n        }\n        // 兵营特有的二级解锁刀盾兵提示\n        if (data.id === BUILD_NID.BARRACKS && data.lv === 2) {\n            // 解锁刀盾兵提示\n            gameHpr.addMessage({\n                key: 'ui.msg_build_effect_barracks_2',\n                params: [`<color=${COLOR_NORMAL.DONE}> ${assetsMgr.lang('pawnText.name_3201')}</color>`],\n                tag: data.uid + '_desc_2',\n                delay: Math.max(0, data.getSurplusTime() * 0.001 - 1) + delay,\n            })\n        }\n    }\n\n    // 修建队列\n    public getBtQueues() { return this.btQueues }\n    public updateBtQueue(datas: any[], isEmit: boolean = true) {\n        this.btQueues = datas.map(m => {\n            const data = new BTInfoObj().fromSvr(m)\n            // 添加训练完成消息\n            if (data.surplusTime > 0) {\n                gameHpr.addMessage({\n                    key: 'ui.message_101',\n                    params: ['buildText.name_' + data.id, data.lv > 1 ? 'ui.button_up' : 'ui.button_build'],\n                    tag: data.uid,\n                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),\n                    // check: () => this.pawnDrillQueueMap.get(uid).has('uid', data.uid),\n                })\n                this.addBuildEffectMessage(data)\n            }\n            return data\n        })\n        if (isEmit) {\n            this.emit(EventType.UPDATE_BT_QUEUE)\n            // 如果当前是在做引导 并且没有修建兵营的队列了\n            if (gameHpr.guide.isCurrTag(GuideTagType.CHOOSE_BTING_BUTTON) && !this.btQueues.has('id', BUILD_NID.BARRACKS)) {\n                gameHpr.guide.gotoNextStep(GuideTagType.CHECK_CAN_XL_PAWN, true)\n            }\n        }\n    }\n\n    public removeLocalBTQueues(uid: string) {\n        this.btQueues.remove('uid', uid)\n    }\n\n    // 取消修建\n    public async cancelBtToServer(index: number, uid: string) {\n        const { err, data } = await netHelper.reqCancelBT({ index, uid })\n        if (err) {\n            return viewHelper.showAlert(err)\n        }\n        this.updateBtQueue(data.queues)\n        this.updateOutputByFlags(data.output)\n        gameHpr.delMessageByTag(uid)\n        gameHpr.delMessageByTag(uid + '_desc')\n        gameHpr.delMessageByTag(uid + '_desc_2')\n    }\n\n    // 立即完成\n    public async inDoneBt() {\n        if (this.user.getGold() < IN_DONE_BT_GOLD) {\n            return ecode.GOLD_NOT_ENOUGH\n        }\n        const { err, data } = await netHelper.reqInDoneBt()\n        if (!err) {\n            audioMgr.playSFX('common/sound_ui_023')\n            this.btQueues.forEach(m => {\n                gameHpr.delMessageByTag(m.uid)\n                gameHpr.message.delayEndByTag(m.uid + '_desc')\n                gameHpr.message.delayEndByTag(m.uid + '_desc_2')\n            })\n            this.updateBtQueue(data.queues)\n            this.user.setGold(data.gold)\n        }\n        return err\n    }\n\n    // 是否有建筑在队列中\n    public hasBuildInBtQueue(uid: string) {\n        const it = this.btQueues.find(m => m.uid === uid)\n        return !!it?.getSurplusTime()\n    }\n    public getBuildBtInfo(uid: string) {\n        return this.btQueues.find(m => m.uid === uid)\n    }\n\n    // 获取军队列表\n    public async getAllArmys(interval: number = 0.5, wait: boolean = true) {\n        if (this.isCapture()) {\n            return [] //如果被沦陷了 直接返回\n        } else if (interval > 0 && this.lastReqArmysTime > 0 && Date.now() - this.lastReqArmysTime <= interval * 1000) {\n            return this.tempArmyList\n        }\n        const { err, data } = await netHelper.reqGetPlayerArmys(wait)\n        this.lastReqArmysTime = Date.now()\n        this.tempArmyList = data?.list || []\n        // 统计宝箱\n        this.tempArmyList.forEach(army => {\n            army.treasures = []\n            army.pawns.forEach(pawn => pawn.treasures.forEach(m => army.treasures.push(gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid))))\n        })\n        return this.tempArmyList\n    }\n    public getTempArmyList() { return this.tempArmyList }\n\n    // 获取选择军队列表\n    public async getSelectArmys(index: number, type: number, interval: number = 1) {\n        if (this.isCapture()) {\n            return { list: [], canGotoCount: 0 } //如果被沦陷了 直接返回\n        } else if (interval > 0 && this.lastReqSelectArmysTime > 0 && Date.now() - this.lastReqSelectArmysTime <= interval * 1000) {\n            return { err: this.tempSelectArmyErr, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }\n        }\n        const { err, data } = await netHelper.reqGetSelectArmys({ index, type })\n        this.lastReqSelectArmysTime = Date.now()\n        this.tempSelectArmyErr = err\n        this.tempSelectArmyList = data?.list || []\n        this.tempSelectArmyCanGotoCount = data?.canGotoCount || 0\n        // 统计宝箱\n        this.tempSelectArmyList.forEach(army => {\n            army.treasures = []\n            army.pawns.forEach(pawn => pawn.treasures.forEach(m => army.treasures.push(gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid))))\n        })\n        return { err, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }\n    }\n    public getTempSelectArmyList() { return this.tempSelectArmyList }\n\n    // 刷新临时军队宝箱信息\n    public updateTempArmyTreasureInfo(treasures: any[], auid: string, puid: string) {\n        this.updateTempArmyTreasureInfoOne(this.tempArmyList, treasures, auid, puid)\n        this.updateTempArmyTreasureInfoOne(this.tempSelectArmyList, treasures, auid, puid)\n        this.emit(EventType.UPDATE_ARMY_TREASURE, auid)\n    }\n    private updateTempArmyTreasureInfoOne(armys: ArmyShortInfo[], treasures: any[], auid: string, puid: string) {\n        const army = armys.find(m => m.uid === auid)\n        if (army) {\n            const pawn = army.pawns.find(m => m.uid === puid)\n            if (pawn) {\n                pawn.treasures.length = 0\n                pawn.treasures.pushArr(treasures)\n                army.treasures.length = 0\n                army.pawns.forEach(pawn => pawn.treasures.forEach(m => army.treasures.push(gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid))))\n            }\n        }\n    }\n\n    // 刷新临时军队所在位置\n    private updateTempArmyIndex(data: any) {\n        this.updateTempArmyIndexOne(this.tempArmyList, data)\n        this.updateTempArmyIndexOne(this.tempSelectArmyList, data)\n        this.emit(EventType.UPDATE_ARMY_AREA_INDEX, data.uid, data.index)\n    }\n    private updateTempArmyIndexOne(armys: ArmyShortInfo[], data: any) {\n        const army = armys.find(m => m.uid === data?.uid)\n        if (army) {\n            army.index = data?.index ?? army.index\n        }\n    }\n\n    // 获取商人列表\n    public getMerchants() { return this.merchants }\n    // 刷新商人列表\n    public updateMerchants(datas: any[]) {\n        this.merchants = datas.map(m => new MerchantObj().fromSvr(m))\n        this.emit(EventType.UPDATE_MERCHANTS)\n    }\n    // 获取一个商人的运输量\n    public getMerchantTransitCap() { return 1000 + (gameHpr.getPlayerPolicyEffect(CEffect.TRANSIT_CD) > 0 ? 1000 : 0) }\n\n    // 获取军队记录\n    public async getArmyMarchRecords() {\n        if (gameHpr.isNoviceMode) {\n            return []\n        } else if (this.lastReqArmyMarchRecordTime > 0 && Date.now() - this.lastReqArmyMarchRecordTime <= 5000) {\n            return this.tempArmyMarchRecordList\n        }\n        const { err, data } = await this.net.request('game/HD_GetArmyRecords', { isBattle: false })\n        this.lastReqArmyMarchRecordTime = Date.now()\n        if (err) {\n            this.tempArmyMarchRecordList = []\n        } else {\n            this.tempArmyMarchRecordList = data?.list || []\n        }\n        return this.tempArmyMarchRecordList\n    }\n\n    public async getArmyBattleRecords() {\n        if (this.lastReqArmyBattleRecordTime > 0 && Date.now() - this.lastReqArmyBattleRecordTime <= 5000) {\n            return this.tempArmyBattleRecordList\n        }\n        const { err, data } = await netHelper.reqGetBattleRecordsList()\n        this.lastReqArmyBattleRecordTime = Date.now()\n        if (err) {\n            this.tempArmyBattleRecordList = []\n        } else {\n            this.tempArmyBattleRecordList = data?.list || []\n        }\n        return this.tempArmyBattleRecordList\n    }\n\n    // 获取市场记录\n    @ut.syncLock\n    public async getBazaarRecords() {\n        if (this.lastReqBazaarRecordTime > 0 && Date.now() - this.lastReqBazaarRecordTime <= 5000) {\n            return this.tempBazaarRecordList\n        }\n        const { err, data } = await this.net.request('game/HD_GetBazaarRecords', {})\n        const uid = gameHpr.getUid()\n        this.lastReqBazaarRecordTime = Date.now()\n        this.tempBazaarRecordList = data?.list || []\n        this.tempBazaarRecordList.forEach(m => {\n            m.nickname = m.names?.[0]\n            if (m.type === 6) {\n                m.type = m.type * 10 + (m.owner === uid ? 1 : 0)\n                m.nickname = m.owner === uid ? m.names?.[1] : m.names?.[0]\n            }\n            const res = m.res || {}\n            m.res0 = res.resType !== undefined ? new CTypeObj().init(res.resType, 0, res.resCount) : null\n            m.res1 = res.costType !== undefined ? new CTypeObj().init(res.costType, 0, res.costCount) : null\n            m.actRes = res.actCount !== undefined ? new CTypeObj().init(res.resType, 0, res.actCount) : null\n        })\n        return this.tempBazaarRecordList\n    }\n\n    // 获取新手任务列表\n    public getGuideTasks() {\n        return this.guideTasks\n    }\n    public updateGuideTasks(tasks: any[], isEmit: boolean = true) {\n        this.guideTasks = []\n        tasks.forEach(taskInfo => {\n            const task = new TaskObj().init(taskInfo, 'guideTask')\n            task && this.guideTasks.push(task)\n        })\n        if (isEmit) {\n            this.emit(EventType.UPDATE_GUIDE_TASK_LIST)\n        }\n        if (this.guideTasks.length === 0) {\n            reddotHelper.unregister('guide_task')\n        } else if (reddotHelper.getRegisterCount('guide_task') === 0) {\n            reddotHelper.unregister('guide_task')\n            reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1)\n        }\n    }\n\n    // 更新新手任务进度\n    public updateGuideTasksProgress(tasks: any[], isEmit: boolean = true) {\n        if (tasks?.length) {\n            tasks.forEach(info => {\n                const data = this.guideTasks.find(m => m.id === info.id)\n                if (!data) {\n                    const task = new TaskObj().init(info, 'guideTask')\n                    task && this.guideTasks.push(task)\n                } else {\n                    data.cond?.updateProgress(info.progress)\n                }\n            })\n            this.updateGuideTaskState(false)\n            isEmit && this.emit(EventType.UPDATE_GUIDE_TASK_LIST)\n        }\n    }\n\n    public getCangetGuideTask() {\n        let task: TaskObj = null\n        this.guideTasks.forEach(m => {\n            const state = m.checkUpdateComplete()\n            if (!task && state === TaskState.CANGET) {\n                task = m\n            }\n        })\n        return task\n    }\n\n    // 刷新任务状态\n    public updateGuideTaskState(isEmit: boolean = true) {\n        if (this.guideTasks.length === 0) {\n            return\n        }\n        this.guideTasks.sort((a, b) => a.getSortVal() - b.getSortVal())\n        const task = this.getCangetGuideTask()\n        reddotHelper.set('guide_task', !!task)\n        isEmit && this.emit(EventType.UPDATE_GUIDE_TASK_STATE, task)\n    }\n\n    // 红点检测\n    private checkGuideTaskState(val: boolean) {\n        if (val) {\n            return val\n        }\n        let task = this.getCangetGuideTask(), ok = !!task\n        if (ok !== val) {\n            this.emit(EventType.UPDATE_GUIDE_TASK_STATE, task)\n        }\n        return ok\n    }\n\n    // 领取任务奖励\n    public async claimTaskReward(id: number) {\n        const { err, data } = await netHelper.reqClaimTaskReward({ id })\n        if (!err) {\n            this.updateRewardItemsByFlags(data.rewards)\n            this.updateGuideTasks(data.tasks)\n            this.updateGuideTaskState()\n            this.updateTodayTasks(data.todayTasks || [])\n            this.updateTodayTaskState()\n        }\n        return err\n    }\n\n    // 获取每日任务列表\n    public getTodayTasks() {\n        return this.todayTasks\n    }\n    private updateTodayTasks(tasks: any[], isEmit: boolean = true) {\n        this.todayTasks = []\n        tasks.forEach(taskInfo => {\n            const task = new TaskObj().init(taskInfo, 'todayTask')\n            task && this.todayTasks.push(task)\n        })\n        isEmit && this.emit(EventType.UPDATE_GUIDE_TASK_LIST)\n        if (this.todayTasks.length === 0) {\n            reddotHelper.unregister('today_task')\n        } else if (reddotHelper.getRegisterCount('today_task') === 0) {\n            reddotHelper.unregister('today_task')\n            reddotHelper.register('today_task', this.checkTodayTaskState, this, 1)\n        }\n    }\n\n    // 更新每日任务进度\n    private updateTodayTasksProgress(tasks: any[], isEmit: boolean = true) {\n        if (tasks?.length) {\n            tasks.forEach(taskInfo => this.todayTasks.find(m => m.id === taskInfo.id)?.cond?.updateProgress(taskInfo.progress))\n            this.updateTodayTaskState()\n            isEmit && this.emit(EventType.UPDATE_GUIDE_TASK_LIST)\n        }\n    }\n\n    public getCangetTodayTask() {\n        let task: TaskObj = null\n        this.todayTasks.forEach(m => {\n            const state = m.checkUpdateComplete()\n            if (!task && state === TaskState.CANGET) {\n                task = m\n            }\n        })\n        return task\n    }\n\n    // 刷新任务状态\n    public updateTodayTaskState() {\n        if (this.todayTasks.length === 0) {\n            if (this.guideTasks.length === 0) {\n                this.emit(EventType.UPDATE_GUIDE_TASK_STATE)\n            }\n            return\n        }\n        this.todayTasks.sort((a, b) => a.getSortVal() - b.getSortVal())\n        const task = this.getCangetTodayTask()\n        reddotHelper.set('today_task', !!task)\n        this.emit(EventType.UPDATE_GUIDE_TASK_STATE, task)\n    }\n\n    // 红点检测\n    private checkTodayTaskState(val: boolean) {\n        if (val) {\n            return val\n        }\n        let task = this.getCangetTodayTask(), ok = !!task\n        if (ok !== val) {\n            this.emit(EventType.UPDATE_GUIDE_TASK_STATE, task)\n        }\n        return ok\n    }\n\n    // 领取任务奖励\n    public async claimTodayTaskReward(id: number, treasureIndex: number, selectIndex: number) {\n        const { err, data } = await this.net.request('game/HD_ClaimTodayTaskReward', { id, treasureIndex, selectIndex }, true)\n        if (!err) {\n            this.updateRewardItemsByFlags(data.rewards)\n            this.updateTodayTasks(data.todayTasks)\n            this.updateTodayTaskState()\n        }\n        return err\n    }\n\n    // 获取其他任务列表\n    public getOtherTasks() {\n        return this.otherTasks\n    }\n    private updateOtherTasks(tasks: any[], isEmit: boolean = true) {\n        this.otherTasks = []\n        tasks.forEach(taskInfo => {\n            const task = new TaskObj().init(taskInfo, 'otherTask')\n            task && this.otherTasks.push(task)\n        })\n        isEmit && this.emit(EventType.UPDATE_GUIDE_TASK_LIST)\n        if (this.otherTasks.length === 0) {\n            reddotHelper.unregister('other_task')\n        } else if (reddotHelper.getRegisterCount('other_task') === 0) {\n            reddotHelper.unregister('other_task')\n            reddotHelper.register('other_task', this.checkOtherTaskState, this, 1)\n        }\n    }\n\n    // 更新其他任务进度\n    private updateOtherTasksProgress(tasks: any[], isEmit: boolean = true) {\n        if (tasks?.length) {\n            tasks.forEach(info => {\n                const data = this.otherTasks.find(m => m.id === info.id)\n                if (data) {\n                    data.cond?.updateProgress(info.progress)\n                } else {\n                    const task = new TaskObj().init(info, 'otherTask')\n                    task && this.otherTasks.push(task)\n                }\n            })\n            this.updateOtherTaskState()\n            isEmit && this.emit(EventType.UPDATE_GUIDE_TASK_LIST)\n        }\n    }\n\n    public getCangetOtherTask() {\n        let task: TaskObj = null\n        this.otherTasks.forEach(m => {\n            const state = m.checkUpdateComplete()\n            if (!task && state === TaskState.CANGET) {\n                task = m\n            }\n        })\n        return task\n    }\n\n    // 刷新任务状态\n    public updateOtherTaskState() {\n        if (this.otherTasks.length === 0) {\n            return\n        }\n        this.otherTasks.sort((a, b) => a.getSortVal() - b.getSortVal())\n        const task = this.getCangetOtherTask()\n        reddotHelper.set('other_task', !!task)\n        this.emit(EventType.UPDATE_GUIDE_TASK_STATE, task)\n    }\n\n    // 红点检测\n    private checkOtherTaskState(val: boolean) {\n        if (val) {\n            return val\n        }\n        let task = this.getCangetOtherTask(), ok = !!task\n        if (ok !== val) {\n            this.emit(EventType.UPDATE_GUIDE_TASK_STATE, task)\n        }\n        return ok\n    }\n\n    // 领取任务奖励\n    public async claimOtherTaskReward(id: number, treasureIndex: number) {\n        const { err, data } = await this.net.request('game/HD_ClaimOtherTaskReward', { id, treasureIndex }, true)\n        if (!err) {\n            this.updateRewardItemsByFlags(data.rewards)\n            this.updateOtherTasks(data.otherTasks)\n            if (this.otherTasks.length === 0) {\n                this.emit(EventType.UPDATE_GUIDE_TASK_STATE)\n            } else {\n                this.updateOtherTaskState()\n            }\n        }\n        return err\n    }\n\n    // 获取所有任务\n    public getPlayerAllTasks() {\n        const tasks: TaskObj[] = []\n        tasks.pushArr(this.getGuideTasks())\n        if (tasks.length === 0) {\n            tasks.pushArr(this.getTodayTasks())\n        }\n        tasks.pushArr(this.getOtherTasks())\n        return tasks\n    }\n\n    public getPlayerTaskCount() {\n        return (this.guideTasks.length || this.todayTasks.length) + this.otherTasks.length\n    }\n\n    public getCangetPlayerTask() {\n        return this.getCangetGuideTask() || this.getCangetTodayTask() || this.getCangetOtherTask()\n    }\n\n    // 刷新添加产量时间\n    private updateAddOutputTime(timeMap: any) {\n        this.addOutputSurplusTime = timeMap || {}\n        this.getAddOutputTime = Date.now()\n    }\n\n    // 购买添加产量时间\n    public async buyAddOutputTime(type: CType) {\n        if (this.user.getGold() < ADD_OUTPUT_GOLD) {\n            return ecode.GOLD_NOT_ENOUGH\n        }\n        const { err, data } = await this.net.request('game/HD_BuyAddOutput', { type }, true)\n        if (!err) {\n            this.updateAddOutputTime(data.addOutputSurplusTime)\n            this.user.setGold(data.gold)\n            this.updateOutputByFlags(data.output)\n        }\n        return err\n    }\n\n    // 添加隐藏的私聊频道\n    public addHidePChatChannels(channel: string, uid: string) {\n        this.hidePChatChannels[channel] = uid\n    }\n\n    public getOccupyLandCountMap() { return this.occupyLandCountMap }\n    // 刷新攻占领地数量\n    private updateOccupyLandCountMap(data: any) {\n        this.occupyLandCountMap = {}\n        for (let key in data) {\n            this.occupyLandCountMap[key] = data[key].arr\n        }\n    }\n\n    // 击杀数量\n    public getKillRecordMap() { return this.killRecordMap }\n    public recordKillCount(id: number, count: number) {\n        const val = this.killRecordMap[id] || 0\n        this.killRecordMap[id] = val + count\n    }\n\n    // 记录结果\n    public getBattleForecastRetData(index: number, key: string) {\n        return this.tempBattleForecastRets.find(m => m.index === index && m.key === key)?.data\n    }\n\n    public setBattleForecastRetMap(index: number, key: string, data: any) {\n        const it = this.tempBattleForecastRets.find(m => m.index === index && m.key === key)\n        if (it) {\n            it.data = data\n            return\n        } else if (this.tempBattleForecastRets.length > 30) {\n            this.tempBattleForecastRets.shift()\n        }\n        this.tempBattleForecastRets.push({ index, key, data })\n    }\n\n    // 刷新士兵的装备等级\n    public updatePawnHeroAttr(id: number, attrs: any[]) {\n        const areaCenter = gameHpr.areaCenter\n        this.baseArmys.forEach(m => {\n            const area = areaCenter.getArea(m.index)\n            if (area && !area.isBattleing()) {\n                area.getArmyByUid(m.uid)?.pawns.forEach(pawn => pawn.updateHeroAttr(id, attrs))\n            }\n        })\n    }\n\n    // 刷新单个槽位信息\n    public updateHeroSlotOne(data: any) {\n        const index = HERO_SLOT_LV_COND.indexOf(data.lv)\n        if (index === -1) {\n            return\n        }\n        const info = this.heroSlots[index]\n        if (info) {\n            info.fromSvr(data)\n        } else {\n            this.heroSlots[index] = new HeroSlotObj().fromSvr(data)\n        }\n        this.emit(EventType.UPDATE_HERO_SLOT_INFO)\n    }\n\n    // 供奉英雄\n    public async worshipHero(index: number, id: number) {\n        const { err, data } = await netHelper.reqWorshipHero({ index, id })\n        if (!err) {\n            this.updateHeroSlotOne(data.slot)\n            this.setUnlockPawnIds(data.unlockPawnIds)\n            this.updatePawnSlots(data.pawnSlots)\n        }\n        return err\n    }\n\n    // 改变士兵画像\n    public async changePawnPortrayal(index: number, armyUid: string, uid: string, portrayalId: number) {\n        const { err, data } = await netHelper.reqChangePawnPortrayal({ index, armyUid, uid, portrayalId })\n        if (!err) {\n            this.updateHeroSlotOne(data.slot)\n        }\n        return err\n    }\n\n    // 检测某个士兵是否可化身\n    public checkCanAvatarPawn(id: number) {\n        return this.heroSlots.some(m => !!m.hero && !m.avatarArmyUID && !m.isDie() && m.hero.avatarPawn === id)\n    }\n\n    public getHeroSlotByPawnId(id: number) {\n        return this.heroSlots.find(m => m.hero?.avatarPawn === id)\n    }\n\n    public findMapMark(point: cc.Vec2) {\n        return this.mapMarks.find(m => m.point.equals(point))\n    }\n\n    public getMapMarks() {\n        return this.mapMarks\n    }\n\n    // 添加标记\n    public addMapMark(data: any) {\n        const mark = this.findMapMark(data.point)\n        if (mark) {\n            mark.name = data.name\n        } else {\n            this.mapMarks.push(data)\n        }\n        this.emit(EventType.UPDATE_MAP_MARK)\n    }\n\n    // 删除标记\n    public async removeMapMark(point: cc.Vec2) {\n        const index = this.mapMarks.findIndex(m => m.point.equals(point))\n        if (index === -1) {\n            return ''\n        }\n        const { err, data } = await this.net.request('game/HD_RemoveMapMark', { point }, true)\n        if (!err) {\n            this.mapMarks.splice(index, 1)\n            this.emit(EventType.UPDATE_MAP_MARK)\n        }\n        return err\n    }\n    // ----------------------------------------- net listener function --------------------------------------------\n    // 更新玩家信息\n    private OnUpdatePlayerInfo(data: { list: { type: NotifyType }[] }) {\n        cc.log('OnUpdatePlayerInfo', data.list)\n        data.list.forEach(m => {\n            const data = m['data_' + m.type]\n            if (m.type === NotifyType.OUTPUT) { //产出\n                this.updateOutputByFlags(data)\n            } else if (m.type === NotifyType.UPDATE_ITEMS) { //更新通用物品\n                this.updateRewardItemsByFlags(data)\n            } else if (m.type === NotifyType.BT_QUEUE) { //建造队列\n                this.updateBtQueue(data)\n            } else if (m.type === NotifyType.BUILD_UP) { //建筑升级\n                this.updateMainBuildInfo(data)\n            } else if (m.type === NotifyType.PAWN_DRILL_QUEUE) { //训练队列\n                this.updatePawnDrillQueue(data)\n            } else if (m.type === NotifyType.PAWN_LEVELING_QUEUE) { //练级队列\n                this.updatePawnLevelingQueue(data)\n            } else if (m.type === NotifyType.PAWN_CURING_QUEUE) { // 刷新士兵治疗队列\n                this.updatePawnCuringQueue(data)\n            } else if (m.type === NotifyType.PAWN_INJURY_ADD) { // 添加受伤的士兵\n                this.addInjuryPawn(data)\n            } else if (m.type === NotifyType.PAWN_INJURY_REMOVE) { // 受伤士兵移除\n                this.removeInjuryPawn(data)\n            } else if (m.type === NotifyType.ARMY_DIST) { //军队分布\n                this.updateArmyDists(data)\n            } else if (m.type === NotifyType.UPDATE_MERCHANT) { //更新商人\n                this.updateMerchants(data)\n            } else if (m.type === NotifyType.FORGE_EQUIP_RET) { //打造装备结果\n                this.updateCurrForgeEquip(null)\n                const isNew = this.updateEquipInfo(data)\n                this.emit(EventType.FORGE_EQUIP_COMPLETE, data.uid)\n                this.addForgeMessage(data.uid.split('_')[0], isNew)\n            } else if (m.type === NotifyType.SMELT_EQUIP_RET) { //融炼装备结果\n                this.updateCurrSmeltEquip(null)\n                this.updateEquipInfo(data)\n                this.emit(EventType.SMELT_EQUIP_COMPLETE, data.uid)\n                this.addSmeltMessage(data.uid.split('_')[0])\n            } else if (m.type === NotifyType.ADD_OUTPUT_TIME) { //刷新添加的产量时间\n                this.updateAddOutputTime(data)\n                this.emit(EventType.UPDATE_ADD_OUTPUT_TIME)\n            } else if (m.type === NotifyType.UPDATE_GENERAL_TASKS) { //刷新常规任务\n                gameHpr.task.updateGeneralTasks(data || [])\n            } else if (m.type === NotifyType.NEW_TREASURE) { //是否有新的宝箱\n                reddotHelper.set('treasure_main', !!data)\n            } else if (m.type === NotifyType.UPDATE_TODAY_INFO) { //每日信息\n                this.todayOccupyCellCount = data.todayOccupyCellCount || 0\n                this.todayReplacementCount = data.todayReplacementCount || 0\n                this.cellTondenCount = data.cellTondenCount || 0\n                this.updateTodayTasks(data.todayTasks || [])\n                this.updateTodayTaskState()\n                this.emit(EventType.UPDATE_TONDEN_COUNT)\n            } else if (m.type === NotifyType.CELL_TONDEN_COUNT) { //刷新屯田次数\n                this.cellTondenCount = data || 0\n                this.emit(EventType.UPDATE_TONDEN_COUNT)\n            } else if (m.type === NotifyType.UPDATE_TASKS) { //刷新任务进度\n                this.updateGuideTasksProgress(data.guideTasks || [])\n                this.updateTodayTasksProgress(data.todayTasks || [])\n                this.updateOtherTasksProgress(data.otherTasks || [])\n            } else if (m.type === NotifyType.UPDATE_LAND_SCORE) { //刷新玩家领地积分\n                this.landScore = data.landScore || 0\n                this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1\n                this.updateOccupyLandCountMap(data.occupyLandCountMap || {})\n            } else if (m.type === NotifyType.CHANGE_HERO_SLOT_INFO) { //英雄殿信息改变\n                this.updateHeroSlotOne(data)\n            } else if (m.type === NotifyType.COMPENSATE) { //战损补偿通知\n                viewHelper.showMessageBox('ui.battle_compensate_tip', { okText: 'ui.button_gotit' })\n            } else if (m.type === NotifyType.UPDATE_POLICY_SLOT) { //政策槽位更新\n                this.updatePolicySlots(data)\n            } else if (m.type === NotifyType.UPDATE_EQUIP_SLOT) { //装备槽位更新\n                this.updateEquipSlots(data)\n            } else if (m.type === NotifyType.UPDATE_PAWN_SLOT) { //士兵槽位更新\n                this.updatePawnSlots(data)\n            }\n        })\n    }\n}"]}