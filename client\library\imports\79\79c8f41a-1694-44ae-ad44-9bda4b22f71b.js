"use strict";
cc._RF.push(module, '79c8fQaFpRErq1Em9pLIvcb', 'PawnCmpt');
// app/script/view/area/PawnCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WxHelper_1 = require("../../common/helper/WxHelper");
var OutlineShaderCtrl_1 = require("../../common/shader/OutlineShaderCtrl");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var FrameAnimationCmpt_1 = require("../cmpt/FrameAnimationCmpt");
var HPBarCmpt_1 = require("./HPBarCmpt");
var PawnAnimConf_1 = require("./PawnAnimConf");
var PawnAnimationCmpt_1 = require("./PawnAnimationCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 士兵
var PawnCmpt = /** @class */ (function (_super) {
    __extends(PawnCmpt, _super);
    function PawnCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.data = null;
        _this.body = null;
        _this.curSkinId = 0;
        _this.curPortrayalId = 0;
        _this.animNode = null;
        _this.animCmpt = null;
        _this.touchCmpt = null;
        _this.hpBar = null;
        _this.animNodeInitY = 0;
        _this.origin = cc.v2(); //起点
        _this.originY = 0;
        _this.tempBodyPosition = cc.v2();
        _this.prePoint = cc.v2();
        _this.prePositionY = -1;
        _this.preAnger = -1;
        _this.curShieldValue = -1; //当前护盾值
        _this.preShieldValue = -1;
        _this.preActioning = false;
        _this.preStateUid = '';
        _this.preAnimName = '';
        _this.currAnimName = '';
        _this.isDie = false; //是否在视图层死亡
        _this.isShowStandShield = false; //是否显示立盾
        _this.isShowBuffMap = {}; //外显buff
        _this.mutualBuffType = 0; //当前显示的互斥buff
        _this.isLoadBuffMap = {};
        _this.buffNodes = []; //buff节点列表
        _this.buffIconCmpt = null; //buff图标容器
        _this.isDiaup = false;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_position = cc.v2();
        _this.tempIndex = 0;
        return _this;
    }
    PawnCmpt.prototype.init = function (data, origin, key) {
        var _this = this;
        this.reset(data.uid);
        this.data = data;
        this.key = key;
        this.curSkinId = data.skinId;
        this.curPortrayalId = data.getPortrayalId();
        this.origin.set(origin);
        this.originY = origin.y * Constant_1.TILE_SIZE;
        this.body = this.FindChild('body');
        this.body.getPosition(this.tempBodyPosition);
        this.updatePosition(true);
        this.updateZIndex();
        this.updateAnger();
        this.updateShieldValue();
        this.preStateUid = '';
        this.animNode = this.FindChild('body/anim');
        this.animCmpt = this.node.addComponent(PawnAnimationCmpt_1.default).init(this.animNode.getComponent(cc.Sprite), data.getViewId(), key);
        if (this.animNodeInitY === 0) {
            this.animNodeInitY = this.animNode.y;
        }
        this.animNode.scale = 1;
        this.animNode.y = this.animNodeInitY;
        // 设置体型
        this.updateAnimScale(1 + data.getBuffValue(Enums_1.BuffType.FEED_INTENSIFY) * 0.02);
        // 非战斗单位不用加点击
        var isNoncombat = data.isNoncombat();
        if (!isNoncombat) {
            this.touchCmpt = this.FindChild('touch').addComponent(ClickTouchCmpt_1.default).on(this.onClick, this); /* .setPlayAction(true).setTarget(this.body) */
        }
        if (data.isBuilding() || isNoncombat) {
            // 秦良玉的矛 特殊处理下
            this.body.scaleX = data.id === Constant_1.SPEAR_PAWN_ID ? data.enterDir : 1;
            if (data.id !== Constant_1.FIRE_PAWN_ID || data.enterDir === 2) {
                this.playAnimation('create', function () {
                    if (_this.isValid) {
                        _this.loadHPBar();
                        _this.playAnimation('idle');
                    }
                });
                return this;
            }
        }
        else if (data.isHasBuff(Enums_1.BuffType.STAND_SHIELD)) {
            this.loadHPBar();
            this.playAnimation('stand_shield'); //如果有立盾
            return this;
        }
        this.loadHPBar();
        this.playAnimation('idle');
        return this;
    };
    PawnCmpt.prototype.reset = function (uid) {
        this.stopSFXByKey('move_sound', uid);
        this.putAllBuff();
        this.isShowStandShield = false;
        this.isShowBuffMap = {};
        this.mutualBuffType = 0;
        this.isLoadBuffMap = {};
    };
    PawnCmpt.prototype.putAllBuff = function () {
        for (var k in Constant_1.NEED_SHOW_BUFF) {
            this.putBuff(Number(k));
        }
        this.putBuff(this.mutualBuffType);
    };
    // 重新同步一下信息
    PawnCmpt.prototype.resync = function (data, jump) {
        var _a, _b, _c, _d, _e, _f;
        if (jump === void 0) { jump = false; }
        this.reset(data.uid);
        // this.animCmpt?.stop()
        (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.resetMove();
        var point = this.getActPoint();
        this.data = data;
        if (!jump && point && !data.isBattleing() && data.aIndex >= 0 && !data.point.equals(point)) {
            this.prePoint.set(data.point);
            this.updatePositionForMove(point, data.point.clone());
        }
        else {
            this.prePoint.set2(-1, -1);
            this.updatePosition();
            this.updateZIndex();
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(data);
        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.updateAnger(data.getAngerRatio());
        this.showPawnLv((_d = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_LV)) !== null && _d !== void 0 ? _d : 1);
        this.showPawnEquip(this.isCanShowPawnEquip());
        this.setCanClick(true);
        this.preStateUid = '';
        (_e = this.animCmpt) === null || _e === void 0 ? void 0 : _e.initModel();
        // 秦良玉没矛状态
        if (((_f = data.getPortrayalSkill()) === null || _f === void 0 ? void 0 : _f.id) === Enums_1.HeroType.QIN_LIANGYU && data.getState() === Enums_1.PawnState.STAND) {
            if (data.isHasBuff(Enums_1.BuffType.BARB)) {
                this.playAnimation('idle_barb');
            }
            else {
                this.playAnimation('idle');
            }
        }
        return this;
    };
    PawnCmpt.prototype.clean = function (release) {
        var _a, _b, _c, _d, _e, _f;
        if (!this.isValid || !this.node) {
            return cc.error('clean error?');
        }
        this.unscheduleAllCallbacks();
        this.stopSFXByKey('move_sound', this.uid);
        this.node.stopAllActions();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.clean();
        (_b = this.animCmpt) === null || _b === void 0 ? void 0 : _b.clean();
        this.isShowStandShield = false;
        this.isShowBuffMap = {};
        this.mutualBuffType = 0;
        this.isLoadBuffMap = {};
        nodePoolMgr.put((_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.node);
        this.hpBar = null;
        this.buffNodes.forEach(function (m) { return nodePoolMgr.put(m); });
        this.buffNodes.length = 0;
        (_d = this.buffIconCmpt) === null || _d === void 0 ? void 0 : _d.clean();
        nodePoolMgr.put((_e = this.buffIconCmpt) === null || _e === void 0 ? void 0 : _e.node);
        this.buffIconCmpt = null;
        this.node.destroy();
        release && assetsMgr.releaseTempRes((_f = this.data) === null || _f === void 0 ? void 0 : _f.getPrefabUrl(), this.key);
        this.data = null;
    };
    Object.defineProperty(PawnCmpt.prototype, "id", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.uid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "cuid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.cuid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "point", {
        get: function () { return this.data.point; },
        enumerable: false,
        configurable: true
    });
    PawnCmpt.prototype.getBody = function () { return this.body; };
    PawnCmpt.prototype.getTempPosition = function () { return this.getPosition(this._temp_position); };
    PawnCmpt.prototype.getAbsUid = function () { return this.uid + (this.curPortrayalId || this.curSkinId || this.id); };
    PawnCmpt.prototype.getActPoint = function () {
        return this.getActPointByPixel(this.getTempPosition());
    };
    // 根据像素点获取网格点
    PawnCmpt.prototype.getActPointByPixel = function (pixel) {
        return MapHelper_1.mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin);
    };
    // 根据网格点获取像素点
    PawnCmpt.prototype.getActPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1);
    };
    // 播放化身
    PawnCmpt.prototype.playAvatarHeroAnim = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var it, anim;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('pawn/AVATAR_HERO', this.key)];
                    case 1:
                        it = _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = this.node;
                        it.zIndex = 20;
                        it.active = true;
                        anim = it.Child('val', FrameAnimationCmpt_1.default);
                        return [4 /*yield*/, anim.init('avatar_' + id, this.key)];
                    case 2:
                        _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        anim.play('avatar');
                        this.playSFX('sound_074');
                        ut.wait(2.42).then(function () { return nodePoolMgr.put(it); });
                        return [4 /*yield*/, ut.wait(1.98)];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.playAnimation = function (name, cb, startTime, intervalMul) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f;
        if (name === 'move' && this.data.getState() === Enums_1.PawnState.MOVE) {
            this.playSFXByKey('move_sound', '', { loop: true, tag: this.uid });
        }
        else {
            this.fadeOutSFXByKey('move_sound', this.uid);
        }
        if (this.isDiaup && name !== 'idle') {
            this.isDiaup = false;
        }
        if (name !== 'die' && name !== 'stand_shield' && ((_a = this.data) === null || _a === void 0 ? void 0 : _a.isHasBuff(Enums_1.BuffType.STAND_SHIELD))) {
            this.currAnimName = name;
            if (name !== 'hit') {
            }
            else if (((_c = (_b = this.data.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.CAO_REN) { //曹仁需要播放受击动画
                (_d = this.animCmpt) === null || _d === void 0 ? void 0 : _d.play('stand_hit', function () { return _this.isValid && _this.playAnimation('stand_shield'); }, startTime);
            }
            else {
                ut.wait(0.5, this).then(function () {
                    var _a, _b, _c;
                    if (_this.isValid && ((_b = (_a = _this.data) === null || _a === void 0 ? void 0 : _a.state) === null || _b === void 0 ? void 0 : _b.type) === Enums_1.PawnState.HIT) {
                        _this.data.changeState(Enums_1.PawnState.STAND);
                        _this.currAnimName = ((_c = _this.data) === null || _c === void 0 ? void 0 : _c.isHasBuff(Enums_1.BuffType.STAND_SHIELD)) ? 'stand_shield' : 'idle';
                    }
                });
            }
            return; //如果是立盾状态就不播放其他动画了
        }
        else if (((_e = this.animCmpt) === null || _e === void 0 ? void 0 : _e.playAnimName) === 'create' && name !== 'die') {
            return cb && cb(); //如果还在播放创建 其他动画就不要播放了 死亡还是可以的
        }
        if (this.isPullStringState(name)) {
            name = name + '_pull'; //检测是否有拉弦状态
        }
        else if (this.isBarbState(name)) {
            name = name + '_barb'; //检测秦良玉手上是否有矛
        }
        if (this.data.id === Constant_1.FIRE_PAWN_ID && name === 'idle') {
            name = 'fire_' + this.data.lv; //火
        }
        this.currAnimName = name;
        (_f = this.animCmpt) === null || _f === void 0 ? void 0 : _f.play(name, cb, startTime);
    };
    // 播放音效
    PawnCmpt.prototype.playSFXByKey = function (key, suffix, opts) {
        if (suffix === void 0) { suffix = ''; }
        if (this.data.isHero()) {
            var prev = this.data.portrayal.json[key];
            if (prev) {
                return this.playSFX(prev + suffix, opts);
            }
        }
        else if (this.data.skinId > 0) {
            var json = assetsMgr.getJsonData('pawnSkin', this.data.skinId);
            var prev = json === null || json === void 0 ? void 0 : json[key];
            if (prev) {
                return this.playSFX(prev + suffix, opts);
            }
        }
        if (this.data.baseJson) {
            var url = this.data.baseJson[key];
            if (url) {
                this.playSFX(url + suffix, opts);
            }
        }
    };
    // 播放音效
    PawnCmpt.prototype.playSFX = function (url, opts) {
        var _a;
        if (!url) {
            return;
        }
        url = (_a = PawnAnimConf_1.PAWN_SOUND_CONF_TRANSITION[url]) !== null && _a !== void 0 ? _a : url;
        audioMgr.playSFX('pawn/' + url, opts);
    };
    PawnCmpt.prototype.stopSFX = function (url, tag) {
        if (!url) {
            return;
        }
        audioMgr.stopSFX('pawn/' + url, tag);
    };
    PawnCmpt.prototype.stopSFXByKey = function (key, tag) {
        if (!this.data) {
            return;
        }
        var url = null;
        if (this.data.isHero()) {
            url = this.data.portrayal.json[key];
        }
        else if (this.data.baseJson) {
            url = this.data.baseJson[key];
        }
        this.stopSFX(url, tag);
    };
    PawnCmpt.prototype.fadeOutSFXByKey = function (key, tag) {
        var url = null;
        if (this.data.isHero()) {
            url = 'pawn/' + this.data.portrayal.json[key];
        }
        else if (this.data.baseJson) {
            url = 'pawn/' + this.data.baseJson[key];
        }
        if (url) {
            audioMgr.fadeOutSFX(0.15, url, tag);
        }
    };
    // 是否拉弦状态
    PawnCmpt.prototype.isPullStringState = function (name) {
        var _a;
        if (((_a = this.data) === null || _a === void 0 ? void 0 : _a.id) !== Constant_1.PAWN_CROSSBOW_ID) {
            return false;
        }
        else if (name !== 'idle' && name !== 'move' && name !== 'hit' && name !== 'diaup' && name !== 'die') {
            return false;
        }
        return !!this.data.getSkillByType(Enums_1.PawnSkillType.PULL_STRING) && this.data.getCurAnger() !== 0;
    };
    // 是否无矛状态
    PawnCmpt.prototype.isBarbState = function (name) {
        var _a, _b, _c;
        if (((_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) !== Enums_1.HeroType.QIN_LIANGYU || !((_c = this.data) === null || _c === void 0 ? void 0 : _c.isHasBuff(Enums_1.BuffType.BARB))) {
            return false;
        }
        return name === 'idle' || name === 'hit' || name === 'diaup';
    };
    // 设置方向
    PawnCmpt.prototype.setDir = function (dir) {
        if (this.data.isBuilding()) {
            this.body.scaleX = 1; //建筑不需要翻转
        }
        else if (dir !== 0) {
            this.body.scaleX = ut.normalizeNumber(dir);
        }
    };
    // 加载血条
    PawnCmpt.prototype.loadHPBar = function () {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0:
                        if (this.data.id === Constant_1.FIRE_PAWN_ID) {
                            return [2 /*return*/, (_a = this.body.Child('bar')) === null || _a === void 0 ? void 0 : _a.Color(GameHelper_1.gameHpr.getBattleFireBarColor(this.data))];
                        }
                        else if (this.data.maxHp === 0 || this.data.isNoncombat()) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get(this.data.getHPBarPrefabUrl(), this.key)];
                    case 1:
                        it = _e.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = this.node;
                        it.zIndex = 10;
                        it.active = true;
                        if (this.data.isBoss()) {
                            it.setPosition(0, 113);
                        }
                        else {
                            it.setPosition(0, this.data.isHero() ? 56 : 46);
                        }
                        this.hpBar = it.addComponent(HPBarCmpt_1.default).init(this.data);
                        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.updateAnger(this.data.getAngerRatio());
                        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.updateShieldValue(this.data.getShieldValue(), this.data.curHp, this.data.getMaxHp());
                        this.showPawnLv((_d = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_LV)) !== null && _d !== void 0 ? _d : 1);
                        this.showPawnEquip(this.isCanShowPawnEquip());
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.showPawnLv = function (val) {
        if (this.hpBar) {
            this.hpBar.Child('root').opacity = val ? 255 : 0;
            var armyNameLbl = this.hpBar.Child('army_name', cc.Label);
            if (armyNameLbl) {
                armyNameLbl.string = (val === 1 && this.data.armyName) ? ut.nameFormator(this.data.armyName, 4) : '';
            }
            this.hpBar.Child('lv', cc.Label).string = (val === 1 && this.data.lv && !this.data.isMachine() && !this.data.isBuilding()) ? this.data.lv + '' : '';
        }
    };
    // 是否可以显示装备信息 这里如果还没有铁匠铺就不会显示
    PawnCmpt.prototype.isCanShowPawnEquip = function () {
        var player = GameHelper_1.gameHpr.player;
        return !!GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP) && (player.isCapture() || player.getMainBuilds().some(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID && m.lv >= 1; }));
    };
    // 是否可以显示没有装备的提示
    PawnCmpt.prototype.isCanShowNotEquipTip = function () {
        return !this.data.equip.id && this.data.isCanWearEquip() && this.data.isOwner() && GameHelper_1.gameHpr.player.getEquips().length > 0;
    };
    // 显示装备信息
    PawnCmpt.prototype.showPawnEquip = function (val) {
        var _a, _b;
        if (!this.hpBar || !this.hpBar.Child('equip')) {
        }
        else if (this.hpBar.Child('equip').active = val && this.data.isCanWearEquip()) {
            this.updateShowPawnEquipInfo();
            (_a = this.hpBar.Child('not_equip')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else {
            (_b = this.hpBar.Child('not_equip')) === null || _b === void 0 ? void 0 : _b.setActive(this.isCanShowNotEquipTip());
        }
    };
    PawnCmpt.prototype.updateShowPawnEquip = function () {
        var _a, _b;
        if (!this.hpBar || !this.hpBar.Child('equip')) {
            return;
        }
        else if (this.hpBar.Child('equip').active) {
            this.updateShowPawnEquipInfo();
            (_a = this.hpBar.Child('not_equip')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else {
            (_b = this.hpBar.Child('not_equip')) === null || _b === void 0 ? void 0 : _b.setActive(this.isCanShowNotEquipTip());
        }
        this.hpBar.initInfo();
    };
    PawnCmpt.prototype.updateShowPawnEquipInfo = function () {
        var _a, _b;
        this.hpBar.Child('equip/add').active = !((_a = this.data.equip) === null || _a === void 0 ? void 0 : _a.id) && this.data.isOwner();
        var spr = this.hpBar.Child('equip/val', cc.Sprite);
        if ((_b = this.data.equip) === null || _b === void 0 ? void 0 : _b.id) {
            ResHelper_1.resHelper.loadEquipIcon(this.data.equip.id, spr, this.key, this.data.equip.getSmeltCount());
        }
        else {
            spr.spriteFrame = null;
        }
    };
    PawnCmpt.prototype.showOutline = function (v) {
        var outline = this.getComponent(OutlineShaderCtrl_1.default);
        if (!outline) {
            outline = this.addComponent(OutlineShaderCtrl_1.default);
            outline.setTarget(outline.FindChild('body/anim').Component(cc.Sprite));
            outline.setOutlineSize(4);
            outline.setColor(cc.Color.WHITE);
        }
        outline.setVisible(v);
    };
    // 被点击了
    PawnCmpt.prototype.onClick = function () {
        if (this.data) {
            audioMgr.playSFX('click');
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.data);
            // this.showOutline(true)
        }
    };
    // 设置是否可以点击
    PawnCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 移动位置
    PawnCmpt.prototype.movePoint = function (point) {
        this.node.setPosition(this.getActPixelByPoint(point));
    };
    // 取消编辑
    PawnCmpt.prototype.cancel = function () {
        this.node.setPosition(this.getActPixelByPoint(this.data.point));
    };
    // 确认编辑
    PawnCmpt.prototype.confirm = function () {
        var point = this.getActPoint();
        this.point.set(point);
        this.prePoint.set(point);
    };
    PawnCmpt.prototype.update = function (dt) {
        if (!this.data) {
            return;
        }
        this.updateZIndex();
        this.updateAnger();
        this.updateBuff();
        this.updateShieldValue();
        this.updateState();
        this.updateCheckDie();
    };
    // 同步zindex
    PawnCmpt.prototype.updateZIndex = function () {
        var _a, _b;
        if (this.prePositionY !== this.node.y || (this.preActioning !== !!((_a = this.data) === null || _a === void 0 ? void 0 : _a.actioning) || this.preAnimName !== this.currAnimName)) {
            this.prePositionY = this.node.y;
            this.preActioning = !!((_b = this.data) === null || _b === void 0 ? void 0 : _b.actioning);
            this.preAnimName = this.currAnimName;
            var y = this.prePositionY - this.originY;
            var add = 0;
            if (this.data.getPawnType() === Enums_1.PawnType.NONCOMBAT) {
                add = 3; //非战斗单位在最上层
            }
            else if (this.preActioning) {
                var state = this.data.getState();
                add = state === Enums_1.PawnState.ATTACK || state >= Enums_1.PawnState.SKILL ? 2 : 1;
            }
            else if (this.preAnimName === 'die') {
                return;
            }
            else {
                add = !this.preAnimName || this.preAnimName === 'create' || this.preAnimName === 'stand_shield' || this.preAnimName === 'idle' || this.preAnimName === 'idle_pull' ? 0 : 1;
            }
            var index = (Constant_1.AREA_MAX_ZINDEX - y) * 10 + add;
            if (index !== this.node.zIndex) {
                this.tempIndex = this.node.zIndex = index;
            }
            // cc.log('updateZIndex', this.data.id, this.data.uid, this.preActioning, this.preAnimName, add)
        }
    };
    // 同步位置
    PawnCmpt.prototype.updatePosition = function (init) {
        if (init || !this.prePoint.equals(this.data.point)) {
            this.prePoint.set(this.data.point);
            this.node.setPosition(this.getActPixelByPoint(this.data.point));
        }
    };
    PawnCmpt.prototype.updatePositionForMove = function (sp, ep) {
        return __awaiter(this, void 0, void 0, function () {
            var data, points, area, time;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        data = this.data;
                        points = [sp, ep];
                        area = GameHelper_1.gameHpr.areaCenter.getArea(data.index);
                        if (!area) return [3 /*break*/, 2];
                        return [4 /*yield*/, GameHelper_1.gameHpr.getPawnASatr(data.uid).init(function (x, y) { return area.checkIsBattleArea(x, y); }).search(sp, ep)];
                    case 1:
                        points = _a.sent();
                        _a.label = 2;
                    case 2:
                        time = MapHelper_1.mapHelper.getMoveNeedTime(points, 400);
                        data.changeState(Enums_1.PawnState.EDIT_MOVE, { paths: points, needMoveTime: time });
                        return [4 /*yield*/, ut.wait(time * 0.001)];
                    case 3:
                        _a.sent();
                        if (data.getState() < Enums_1.PawnState.STAND) {
                            data.changeState(Enums_1.PawnState.NONE);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新怒气
    PawnCmpt.prototype.updateAnger = function () {
        var _a;
        if (this.preAnger !== this.data.curAnger) {
            this.preAnger = this.data.curAnger;
            (_a = this.hpBar) === null || _a === void 0 ? void 0 : _a.updateAnger(this.data.getAngerRatio());
        }
    };
    // 刷新白盾
    PawnCmpt.prototype.updateShieldValue = function () {
        var _a;
        if (this.preShieldValue !== this.curShieldValue) {
            this.preShieldValue = this.curShieldValue;
            (_a = this.hpBar) === null || _a === void 0 ? void 0 : _a.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp());
        }
    };
    // 刷新buff效果
    PawnCmpt.prototype.updateBuff = function () {
        var _this = this;
        this.curShieldValue = 0;
        var showBuffTypeMap = {}, mutualBuff = 0, standShield = false, feedIntensifyValue = 0;
        this.data.buffs.forEach(function (m) {
            if (Constant_1.SHIELD_BUFF[m.type]) { //记录护盾值
                _this.curShieldValue += m.value;
            }
            if (Constant_1.NEED_SHOW_BUFF[m.type]) {
                showBuffTypeMap[m.type] = true;
            }
            else if (Constant_1.NEED_MUTUAL_BUFF[m.type]) {
                mutualBuff = m.type; //互斥buff
            }
            else if (m.type === Enums_1.BuffType.STAND_SHIELD) { //立盾
                standShield = true;
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //投喂强化
                feedIntensifyValue = m.value;
            }
        });
        // 刷新外显buff
        for (var k in Constant_1.NEED_SHOW_BUFF) {
            var type = Number(k);
            this.updateShowBuff(type, showBuffTypeMap[type]);
        }
        // 刷新互斥buff
        this.updateMutualBuff(mutualBuff);
        // 刷新立盾
        this.updateStandShield(standShield);
        // 体型
        this.updateAnimScale(1 + feedIntensifyValue * 0.02);
    };
    // 刷新立盾
    PawnCmpt.prototype.updateStandShield = function (val) {
        if (!this.isShowStandShield && val) {
            this.isShowStandShield = true;
            this.playAnimation('stand_shield');
        }
        else if (this.isShowStandShield && !val) {
            this.isShowStandShield = false;
            if (this.currAnimName !== 'shield_end') {
                this.playAnimation('idle');
            }
        }
    };
    // 刷新外显buff
    PawnCmpt.prototype.updateShowBuff = function (type, val) {
        if (this.isLoadBuffMap[type]) {
        }
        else if (!this.isShowBuffMap[type] && val) {
            this.showBuff(type);
            this.isShowBuffMap[type] = true;
        }
        else if (this.isShowBuffMap[type] && !val) {
            this.isShowBuffMap[type] = false;
            this.putBuff(type);
        }
    };
    // 刷新互斥buff效果 就是只会显示一个效果
    PawnCmpt.prototype.updateMutualBuff = function (type) {
        if (this.mutualBuffType === type) {
            return;
        }
        else if (this.mutualBuffType) {
            this.putBuff(this.mutualBuffType);
            this.mutualBuffType = 0;
        }
        if (type && !this.isLoadBuffMap[type]) {
            this.mutualBuffType = type;
            this.showBuff(type);
        }
    };
    // 显示一个buff
    PawnCmpt.prototype.showBuff = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var showType, name, it, cmpt;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        showType = Constant_1.BUFF_SHOW_TYPE_TRAN[type] || type;
                        name = 'BUFF_' + showType;
                        it = this.buffNodes.find(function (m) { return m.name === name; });
                        if (!it) return [3 /*break*/, 1];
                        this.isLoadBuffMap[showType] = false;
                        return [3 /*break*/, 5];
                    case 1:
                        if (!!this.isLoadBuffMap) return [3 /*break*/, 2];
                        return [2 /*return*/, WxHelper_1.wxHelper.errorAndFilter('showBuff', '!this.isLoadBuffMap')];
                    case 2:
                        if (!this.isLoadBuffMap[showType]) return [3 /*break*/, 3];
                        return [2 /*return*/];
                    case 3:
                        this.isLoadBuffMap[showType] = true;
                        return [4 /*yield*/, nodePoolMgr.get('buff/' + name, this.key)];
                    case 4:
                        it = _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        this.isLoadBuffMap[showType] = false;
                        this.buffNodes.push(it);
                        _a.label = 5;
                    case 5:
                        it.opacity = 255;
                        it.parent = this.node;
                        it.zIndex = Constant_1.BUFF_NODE_ZINDEX[showType] || 10;
                        cmpt = it.Component(PawnAnimationCmpt_1.default).init(it.Child('body/anim', cc.Sprite), showType, this.key);
                        if (showType === Enums_1.BuffType.SHIELD
                            || showType === Enums_1.BuffType.PROTECTION_SHIELD
                            || showType === Enums_1.BuffType.RODELERO_SHIELD
                            || showType === Enums_1.BuffType.RODELERO_SHIELD_001
                            || showType === Enums_1.BuffType.RODELERO_SHIELD_102
                            || showType === Enums_1.BuffType.ABNEGATION_SHIELD
                            || showType === Enums_1.BuffType.POISONED_WINE) {
                            cmpt.play('trigger', function () { return cmpt.isValid && cmpt.play('stand'); });
                        }
                        else {
                            cmpt.play('stand');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.putBuff = function (type) {
        var showType = Constant_1.BUFF_SHOW_TYPE_TRAN[type] || type;
        var name = 'BUFF_' + showType;
        var node = this.buffNodes.remove('name', name);
        if (!node) {
        }
        else if (type === Enums_1.BuffType.SHIELD
            || showType === Enums_1.BuffType.PROTECTION_SHIELD
            || type === Enums_1.BuffType.RODELERO_SHIELD
            || type === Enums_1.BuffType.RODELERO_SHIELD_001
            || type === Enums_1.BuffType.RODELERO_SHIELD_102
            || type === Enums_1.BuffType.ABNEGATION_SHIELD) {
            node.Component(PawnAnimationCmpt_1.default).play('die', function () { return nodePoolMgr.put(node); });
        }
        else {
            nodePoolMgr.put(node);
        }
    };
    // 刷新体型
    PawnCmpt.prototype.updateAnimScale = function (val) {
        if (this.animNode.scale !== val) {
            this.animNode.scale = val;
            this.animNode.y = this.animNodeInitY + (this.animNodeInitY + 36) * (val - 1);
        }
    };
    // 检测是否死亡
    PawnCmpt.prototype.updateCheckDie = function () {
        var _this = this;
        var _a, _b;
        if (!this.isDie && ((_a = this.data) === null || _a === void 0 ? void 0 : _a.isDie())) {
            this.isDie = true;
            var name = (_b = this.animCmpt) === null || _b === void 0 ? void 0 : _b.playAnimName;
            if (name !== 'hit' && name !== 'die' && name !== 'hit_pull' && name !== 'die_pull') {
                this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, _this.data.aIndex, _this.data.uid); });
            }
        }
    };
    // 同步状态信息
    PawnCmpt.prototype.updateState = function () {
        var _a;
        if (GameHelper_1.gameHpr.playback.isSimulating) {
            return;
        }
        else if (!((_a = this.data) === null || _a === void 0 ? void 0 : _a.state) || this.preStateUid === this.data.state.uid || this.isDie) {
            return;
        }
        this.preStateUid = this.data.state.uid;
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
        var state = this.data.state.type, data = this.data.state.data;
        // cc.log('updateState', this.uid, this.point.ID(), PawnState[state])
        // this.data.actioning = this.data.actioning || (state !== PawnState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动
        if (state === Enums_1.PawnState.STAND) { //待机
            this.doStand();
        }
        else if (state === Enums_1.PawnState.MOVE || state === Enums_1.PawnState.EDIT_MOVE) { //移动
            this.doMove(data);
        }
        else if (state === Enums_1.PawnState.ATTACK) { //攻击
            this.doAttack(data);
        }
        else if (state === Enums_1.PawnState.HIT) { //受击
            this.doHit(data);
        }
        else if (state === Enums_1.PawnState.DIAUP) { //击飞
            this.doDiaup(data);
        }
        else if (state === Enums_1.PawnState.HEAL) { //回血
            this.doHeal(data);
        }
        else if (state === Enums_1.PawnState.DEDUCT_HP) { //掉血
            this.doDeductHp(data);
        }
        else if (state === Enums_1.PawnState.ADD_ANGER) { //加怒气
            this.doAddAnger(data);
        }
        else if (state === Enums_1.PawnState.FEAR) { //恐惧
            this.doFear(data);
        }
        else if (state === Enums_1.PawnState.DIE) { //直接死亡
            this.doDie(data);
        }
        else if (state >= Enums_1.PawnState.SKILL && state <= Enums_1.PawnState.SKILL_MAX) { //技能
            this.doSkill(data);
        }
        else {
            this.playAnimation('idle');
        }
        // 通知聚焦
        // if (state === PawnState.MOVE || state === PawnState.ATTACK || (state >= PawnState.SKILL && state <= PawnState.SKILL_MAX)) {
        //     eventCenter.emit(EventType.FOCUS_PAWN, { index: this.data.aIndex, uid: this.data.uid, point: this.data.point })
        // }
    };
    // 待机
    PawnCmpt.prototype.doStand = function () {
        var _a, _b;
        var animName = (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.playAnimName;
        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle
            this.playAnimation('idle');
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.initInfo();
        this.updatePosition();
    };
    // 移动
    PawnCmpt.prototype.doMove = function (data) {
        var _this = this;
        var _a, _b;
        this.updatePosition();
        var paths = (data.paths || []).map(function (m) { return _this.getActPixelByPoint(cc.v2(m)).clone(); });
        var currMoveTime = (_a = data.currMoveTime) !== null && _a !== void 0 ? _a : 0;
        var needMoveTime = (_b = data.needMoveTime) !== null && _b !== void 0 ? _b : 0;
        // 计算各个距离信息
        var sumDis = 0, arr = [];
        for (var i = 1, l = paths.length; i < l; i++) {
            var curr = paths[i], prep = paths[i - 1], speed = curr.sub(prep, this._temp_vec2_3);
            var dis = speed.mag();
            sumDis += dis;
            arr.push({ dis: dis, progress: sumDis, speed: speed.normalize(), prep: prep, pos: curr });
        }
        var ratio = currMoveTime / needMoveTime;
        var startPos = null, list = [];
        for (var i = 0, l = arr.length; i < l; i++) {
            var m = arr[i], pr = m.progress / sumDis;
            if (ratio > pr) {
                continue;
            }
            else if (!startPos) { //找出起点
                var dr = m.dis / sumDis;
                var r = Math.max(ratio - (pr - dr), 0);
                var d = sumDis * r; //超出的一段距离
                startPos = m.speed.mul(d).addSelf(m.prep);
                m.dis -= d; //减去已经走过的路
            }
            list.push({ time: m.dis / sumDis * needMoveTime, endPos: m.pos });
        }
        if (!startPos) {
            return;
        }
        // 开始移动
        this.playAnimation('move');
        this.node.setPosition(startPos);
        this.animCmpt.resetMove();
        this.runMove(list);
    };
    PawnCmpt.prototype.runMove = function (list) {
        var _this = this;
        if (!this.isValid) {
        }
        else if (list.length > 0) {
            var d = list.shift(), pos = d.endPos;
            this.setDir(pos.x - this.node.x);
            this.animCmpt.moveNodeOne(0, d.time, this.getPosition(), pos, function () { return _this.runMove(list); });
        }
        else if (this.data.getState() === Enums_1.PawnState.MOVE || this.data.getState() === Enums_1.PawnState.EDIT_MOVE) {
            this.playAnimation('idle');
        }
    };
    // 攻击
    PawnCmpt.prototype.doAttack = function (data) {
        var _this = this;
        var _a;
        var currAttackTime = (_a = data.currAttackTime) !== null && _a !== void 0 ? _a : 0;
        var targetPoint = data.targetPoint || this.point;
        var suffix = data.instabilityAttackIndex || '';
        this.updatePosition();
        this.setDir(targetPoint.x - this.point.x);
        this.playSFXByKey('attack_sound', suffix);
        this.playAnimation('attack' + suffix, function () { return _this.isValid && _this.playAnimation('idle'); }, currAttackTime);
    };
    // 技能
    PawnCmpt.prototype.doSkill = function (data) {
        var _this = this;
        var _a, _b, _c;
        var currAttackTime = (_a = data.currAttackTime) !== null && _a !== void 0 ? _a : 0;
        var targetPoint = data.targetPoint || this.point;
        var skill = data.skill, heroSkill = this.data.getPortrayalSkill();
        // 位移
        if (!this.prePoint.equals(this.data.point) && ((skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_208
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_212
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_213
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_219
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_306
            || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_QIONG)) {
            this.prePoint.set(this.data.point);
            var pos = this.getActPixelByPoint(this.data.point);
            this.setDir(pos.x - this.node.x);
            var params = skill.params;
            if (!heroSkill) {
            }
            else if (heroSkill.id === Enums_1.HeroType.ZHANG_FEI
                || heroSkill.id === Enums_1.HeroType.XU_CHU
                || heroSkill.id === Enums_1.HeroType.PEI_XINGYAN
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_QIONG
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.GAO_SHUN
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.HUO_QUBING
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.DIAN_WEI) {
                params = heroSkill.params; //张飞 高顺 许褚 裴行俨 秦琼
            }
            else if (this.data.skinId === 3404103) {
                params = '0.36,0.6'; //重骑冬季皮肤
            }
            var _d = __read(ut.stringToNumbers(params, ','), 2), delay = _d[0], time = _d[1];
            this.animCmpt.resetMove().setMoveDelay((delay !== null && delay !== void 0 ? delay : 0) * 1000).moveNodeOne(0, (time !== null && time !== void 0 ? time : 0.1) * 1000, this.getPosition(), pos, function () { return _this.setDir(targetPoint.x - _this.point.x); });
        }
        else {
            this.updatePosition();
            this.setDir(targetPoint.x - this.point.x);
        }
        if (data.sound !== undefined) {
            this.playSFX(data.sound);
        }
        else {
            this.playSFXByKey('skill_sound');
        }
        var isStandShield = (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_205; //立盾
        var isSpearthrowing = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_LIANGYU && !data.skillName;
        this.playAnimation(data.skillName || 'skill', function () {
            if (!_this.isValid) {
            }
            else if (isStandShield) {
                _this.playAnimation('stand_shield');
            }
            else if (isSpearthrowing) {
                _this.playAnimation('idle_barb');
            }
            else {
                _this.playAnimation('idle');
            }
        }, currAttackTime);
        // 播放粉碎大地 地面效果
        if ((skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_215) {
            var type = skill.type, delay = 0.9;
            // 黄盖特殊处理下
            if (((_c = (_b = this.data.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.HUANG_GAI) {
                type = 215001;
                delay = 0.6;
            }
            eventCenter.emit(EventType_1.default.PLAY_BATTLE_EFFECT, {
                type: type, delay: delay,
                index: this.data.aIndex,
                point: this.data.point,
            });
        }
        // 秦良玉 收矛
        if (data.skillName === 'recycle_spear') {
            eventCenter.emit(EventType_1.default.PLAY_BULLET_FLY, {
                bulletId: 5022,
                currTime: currAttackTime,
                needTime: 1000,
                index: this.data.aIndex,
                startPoint: targetPoint,
                targetPoint: this.data.point,
            });
        }
    };
    // 受击
    PawnCmpt.prototype.doHit = function (data) {
        var _this = this;
        var _a, _b, _c, _d, _e;
        var index = this.data.aIndex;
        var damage = (_a = data.damage) !== null && _a !== void 0 ? _a : 0;
        var trueDamage = (_b = data.trueDamage) !== null && _b !== void 0 ? _b : 0;
        var isCrit = !!data.isCrit; //暴击
        var heal = (_c = data.heal) !== null && _c !== void 0 ? _c : 0; //回复
        var isDodge = damage === -1; //闪避
        var isParry = damage === -2; //格挡
        var isTurntheblade = damage === -3; //招架
        var isWithstand = damage === -4; //抵挡
        damage = isDodge || isParry || isTurntheblade || isWithstand ? 0 : damage;
        var attackPoint = data.attackPoint || this.point;
        var isDie = this.isDie = !!data.isDie;
        var time = (_d = data.time) !== null && _d !== void 0 ? _d : 0; //经过的时间
        var sound = data.sound; //受击音效
        var uid = this.uid;
        var isDiaup = this.isDiaup;
        this.isDiaup = false;
        this.setDir(attackPoint.x - this.point.x);
        (_e = this.hpBar) === null || _e === void 0 ? void 0 : _e.play();
        if (damage + trueDamage === 0) {
            return this.playAnimation('idle');
        }
        else if (isDie) {
            if (this.data.getPawnType() !== Enums_1.PawnType.NONCOMBAT) {
                this.node.zIndex = 0;
            }
            this.putAllBuff();
        }
        else if (isDiaup) { //如果没有死亡且上一个动作是击飞
            return this.playAnimation('idle');
        }
        var animName = 'hit';
        if (isDie) {
            animName = 'die';
            this.playSFXByKey('die_sound');
        }
        else if (sound) {
            this.playSFX(sound);
        }
        this.playAnimation(animName, function () {
            if (isDie) {
                eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid);
            }
            else if (_this.isValid) {
                _this.playAnimation('idle');
            }
        });
    };
    // 直接死亡
    PawnCmpt.prototype.doDie = function (data) {
        var _a;
        var index = this.data.aIndex;
        var uid = this.uid;
        if (!this.data.isNoncombat()) {
            this.node.zIndex = 0;
        }
        this.putAllBuff();
        this.playSFX((_a = this.data.baseJson) === null || _a === void 0 ? void 0 : _a.die_sound);
        this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid); });
    };
    // 击飞
    PawnCmpt.prototype.doDiaup = function (data) {
        var _this = this;
        var _a, _b;
        var time = (_a = data.time) !== null && _a !== void 0 ? _a : 0; //经过的时间
        var attackPoint = data.attackPoint || this.prePoint;
        this.setDir(attackPoint.x - this.point.x);
        if (time > 0) {
            this.prePoint.set(this.data.point);
            this.playSFX('sound_037_1');
            this.playAnimation('diaup');
            this.isDiaup = true;
            var pos = this.getActPixelByPoint(this.data.point);
            this.animCmpt.resetMove().setMoveParabolaHeight((_b = data.parabolaHeight) !== null && _b !== void 0 ? _b : 20).moveNodeOne(0, time, this.getPosition(), pos, function () {
                if (_this.isValid && !_this.isDie) {
                    _this.playAnimation('idle');
                }
            });
        }
        else {
            this.playAnimation('idle');
            this.updatePosition();
        }
    };
    // 恐惧
    PawnCmpt.prototype.doFear = function (data) {
        var _this = this;
        var _a;
        var time = (_a = data.time) !== null && _a !== void 0 ? _a : 0; //经过的时间
        if (time > 0 && !this.prePoint.equals(this.data.point)) {
            this.setDir(this.point.x - this.prePoint.x);
            this.prePoint.set(this.data.point);
            var pos = this.getActPixelByPoint(this.data.point);
            this.playAnimation('move', null, 0, 0.5);
            this.animCmpt.resetMove().moveNodeOne(0, time, this.getPosition(), pos, function () {
                if (_this.isValid && !_this.isDie) {
                    _this.playAnimation('idle');
                }
            });
        }
        else {
            this.playAnimation('idle');
            this.updatePosition();
        }
    };
    // 回血
    PawnCmpt.prototype.doHeal = function (data) {
        var _a, _b, _c, _d;
        var index = this.data.aIndex;
        var val = (_a = data.val) !== null && _a !== void 0 ? _a : 0;
        var time = (_b = data.time) !== null && _b !== void 0 ? _b : 0;
        var uid = this.uid;
        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.play();
        if (this.preShieldValue > 0) { //这里主动刷新一下护盾
            (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp());
        }
    };
    // 掉血
    PawnCmpt.prototype.doDeductHp = function (data) {
        var _a, _b, _c, _d, _e;
        var index = this.data.aIndex;
        var damage = (_a = data.damage) !== null && _a !== void 0 ? _a : 0;
        var trueDamage = (_b = data.trueDamage) !== null && _b !== void 0 ? _b : 0;
        var time = (_c = data.time) !== null && _c !== void 0 ? _c : 0;
        var isDie = this.isDie = !!data.isDie;
        var uid = this.uid;
        if (isDie) {
            (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.setActive(false);
            this.node.zIndex = 0;
            this.putAllBuff();
            this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid); });
        }
        else {
            (_e = this.hpBar) === null || _e === void 0 ? void 0 : _e.play();
        }
    };
    // 添加怒气
    PawnCmpt.prototype.doAddAnger = function (data) {
        this.updateAnger();
        // this.doStand()
    };
    PawnCmpt = __decorate([
        ccclass
    ], PawnCmpt);
    return PawnCmpt;
}(cc.Component));
exports.default = PawnCmpt;

cc._RF.pop();