{"version": 3, "sources": ["assets\\app\\script\\view\\area\\PawnCmpt.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAmO;AACnO,qDAAoH;AACpH,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,2DAA0D;AAC1D,6DAA4D;AAC5D,yDAAwD;AACxD,2EAAsE;AAGtE,yDAAoD;AACpD,iEAA4D;AAE5D,yCAAoC;AACpC,+CAA4D;AAC5D,yDAAoD;AAE9C,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,KAAK;AAEL;IAAsC,4BAAY;IAAlD;QAAA,qEAs/BC;QAp/BW,SAAG,GAAW,EAAE,CAAA;QAEjB,UAAI,GAAY,IAAI,CAAA;QACpB,UAAI,GAAY,IAAI,CAAA;QACpB,eAAS,GAAW,CAAC,CAAA;QACrB,oBAAc,GAAW,CAAC,CAAA;QACzB,cAAQ,GAAY,IAAI,CAAA;QACxB,cAAQ,GAAsB,IAAI,CAAA;QAClC,eAAS,GAAmB,IAAI,CAAA;QAChC,WAAK,GAAc,IAAI,CAAA;QAEvB,mBAAa,GAAW,CAAC,CAAA;QACzB,YAAM,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,IAAI;QAC9B,aAAO,GAAW,CAAC,CAAA;QACnB,sBAAgB,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAEnC,cAAQ,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC3B,kBAAY,GAAW,CAAC,CAAC,CAAA;QACzB,cAAQ,GAAW,CAAC,CAAC,CAAA;QACrB,oBAAc,GAAW,CAAC,CAAC,CAAA,CAAC,OAAO;QACnC,oBAAc,GAAW,CAAC,CAAC,CAAA;QAC3B,kBAAY,GAAY,KAAK,CAAA;QAC7B,iBAAW,GAAW,EAAE,CAAA;QACxB,iBAAW,GAAW,EAAE,CAAA;QACxB,kBAAY,GAAW,EAAE,CAAA;QACzB,WAAK,GAAY,KAAK,CAAA,CAAC,UAAU;QACjC,uBAAiB,GAAY,KAAK,CAAA,CAAC,QAAQ;QAC3C,mBAAa,GAAQ,EAAE,CAAA,CAAC,QAAQ;QAChC,oBAAc,GAAa,CAAC,CAAA,CAAC,aAAa;QAE1C,mBAAa,GAAQ,EAAE,CAAA;QACvB,eAAS,GAAc,EAAE,CAAA,CAAC,UAAU;QACpC,kBAAY,GAAiB,IAAI,CAAA,CAAC,UAAU;QAC5C,aAAO,GAAY,KAAK,CAAA;QAExB,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,oBAAc,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAElC,eAAS,GAAG,CAAC,CAAA;;IA48BxB,CAAC;IA18BU,uBAAI,GAAX,UAAY,IAAa,EAAE,MAAe,EAAE,GAAW;QAAvD,iBAiDC;QAhDG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,GAAG,oBAAS,CAAA;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAClC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QACzB,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,2BAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,GAAG,CAAC,CAAA;QAC5H,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAA;QACvB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAA;QACpC,OAAO;QACP,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAQ,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAA;QAC3E,aAAa;QACb,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QACtC,IAAI,CAAC,WAAW,EAAE;YACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,wBAAc,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA,CAAA,+CAA+C;SAC9I;QACD,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,WAAW,EAAE;YAClC,cAAc;YACd,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK,wBAAa,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAChE,IAAI,IAAI,CAAC,EAAE,KAAK,uBAAY,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;gBACjD,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;oBACzB,IAAI,KAAI,CAAC,OAAO,EAAE;wBACd,KAAI,CAAC,SAAS,EAAE,CAAA;wBAChB,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;qBAC7B;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,IAAI,CAAA;aACd;SACJ;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAQ,CAAC,YAAY,CAAC,EAAE;YAC9C,IAAI,CAAC,SAAS,EAAE,CAAA;YAChB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAA,CAAC,OAAO;YAC1C,OAAO,IAAI,CAAA;SACd;QACD,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QAC1B,OAAO,IAAI,CAAA;IACf,CAAC;IAEO,wBAAK,GAAb,UAAc,GAAW;QACrB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC,CAAA;QACpC,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;QAC9B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;QACvB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;QACvB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;IAC3B,CAAC;IAEO,6BAAU,GAAlB;QACI,KAAK,IAAI,CAAC,IAAI,yBAAc,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;SAC1B;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IACrC,CAAC;IAED,WAAW;IACJ,yBAAM,GAAb,UAAc,IAAa,EAAE,IAAqB;;QAArB,qBAAA,EAAA,YAAqB;QAC9C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACpB,wBAAwB;QACxB,MAAA,IAAI,CAAC,QAAQ,0CAAE,SAAS,GAAE;QAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACxF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;SACxD;aAAM;YACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAA;YACrB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,CAAC,IAAI,EAAC;QACtB,MAAA,IAAI,CAAC,KAAK,0CAAE,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,EAAC;QAC7C,IAAI,CAAC,UAAU,OAAC,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,YAAY,CAAC,mCAAI,CAAC,CAAC,CAAA;QACrF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAA;QAC7C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;QACrB,MAAA,IAAI,CAAC,QAAQ,0CAAE,SAAS,GAAE;QAC1B,UAAU;QACV,IAAI,OAAA,IAAI,CAAC,iBAAiB,EAAE,0CAAE,EAAE,MAAK,gBAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAS,CAAC,KAAK,EAAE;YAC9F,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE;gBAC/B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;aAClC;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;aAC7B;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,wBAAK,GAAZ,UAAa,OAAiB;;QAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC7B,OAAO,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;SAClC;QACD,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC7B,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;QACzC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAA;QAC1B,MAAA,IAAI,CAAC,SAAS,0CAAE,KAAK,GAAE;QACvB,MAAA,IAAI,CAAC,QAAQ,0CAAE,KAAK,GAAE;QACtB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;QAC9B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;QACvB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;QACvB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;QACvB,WAAW,CAAC,GAAG,OAAC,IAAI,CAAC,KAAK,0CAAE,IAAI,CAAC,CAAA;QACjC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAA;QAC/C,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;QACzB,MAAA,IAAI,CAAC,YAAY,0CAAE,KAAK,GAAE;QAC1B,WAAW,CAAC,GAAG,OAAC,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QACnB,OAAO,IAAI,SAAS,CAAC,cAAc,OAAC,IAAI,CAAC,IAAI,0CAAE,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,CAAA;QACxE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAED,sBAAW,wBAAE;aAAb,sBAAkB,aAAO,IAAI,CAAC,IAAI,0CAAE,EAAE,CAAA,CAAC,CAAC;;;OAAA;IACxC,sBAAW,yBAAG;aAAd,sBAAmB,aAAO,IAAI,CAAC,IAAI,0CAAE,GAAG,CAAA,CAAC,CAAC;;;OAAA;IAC1C,sBAAW,0BAAI;aAAf,sBAAoB,aAAO,IAAI,CAAC,IAAI,0CAAE,IAAI,CAAA,CAAC,CAAC;;;OAAA;IAC5C,sBAAW,2BAAK;aAAhB,cAAqB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA,CAAC,CAAC;;;OAAA;IACtC,0BAAO,GAAd,cAAmB,OAAO,IAAI,CAAC,IAAI,CAAA,CAAC,CAAC;IAC9B,kCAAe,GAAtB,cAA2B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA,CAAC,CAAC;IAClE,4BAAS,GAAhB,cAAqB,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,EAAE,CAAC,CAAA,CAAC,CAAC;IAEpF,8BAAW,GAAlB;QACI,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,aAAa;IACN,qCAAkB,GAAzB,UAA0B,KAAc;QACpC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACnF,CAAC;IAED,aAAa;IACN,qCAAkB,GAAzB,UAA0B,KAAc;QACpC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;IAClG,CAAC;IAED,OAAO;IACM,qCAAkB,GAA/B,UAAgC,EAAU;;;;;4BAC3B,qBAAM,WAAW,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAAxD,EAAE,GAAG,SAAmD;wBAC9D,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;4BAC7B,sBAAO,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA;yBAC7B;wBACD,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;wBACrB,EAAE,CAAC,MAAM,GAAG,EAAE,CAAA;wBACd,EAAE,CAAC,MAAM,GAAG,IAAI,CAAA;wBACV,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,4BAAkB,CAAC,CAAA;wBAChD,qBAAM,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAAzC,SAAyC,CAAA;wBACzC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;4BAC7B,sBAAO,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA;yBAC7B;wBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;wBACnB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;wBACzB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAA;wBAC7C,qBAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA;;wBAAnB,SAAmB,CAAA;;;;;KACtB;IAEO,gCAAa,GAArB,UAAsB,IAAY,EAAE,EAAa,EAAE,SAAkB,EAAE,WAAoB;QAA3F,iBAoCC;;QAnCG,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAS,CAAC,IAAI,EAAE;YAC5D,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;SACrE;aAAM;YACH,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SAC/C;QACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,MAAM,EAAE;YACjC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;SACvB;QACD,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,cAAc,WAAI,IAAI,CAAC,IAAI,0CAAE,SAAS,CAAC,gBAAQ,CAAC,YAAY,EAAC,EAAE;YAC1F,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;YACxB,IAAI,IAAI,KAAK,KAAK,EAAE;aACnB;iBAAM,IAAI,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,0CAAE,KAAK,0CAAE,EAAE,MAAK,gBAAQ,CAAC,OAAO,EAAE,EAAE,YAAY;gBAC1E,MAAA,IAAI,CAAC,QAAQ,0CAAE,IAAI,CAAC,WAAW,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,aAAa,CAAC,cAAc,CAAC,EAAlD,CAAkD,EAAE,SAAS,EAAC;aACxG;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;;oBACpB,IAAI,KAAI,CAAC,OAAO,IAAI,aAAA,KAAI,CAAC,IAAI,0CAAE,KAAK,0CAAE,IAAI,MAAK,iBAAS,CAAC,GAAG,EAAE;wBAC1D,KAAI,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAS,CAAC,KAAK,CAAC,CAAA;wBACtC,KAAI,CAAC,YAAY,GAAG,OAAA,KAAI,CAAC,IAAI,0CAAE,SAAS,CAAC,gBAAQ,CAAC,YAAY,GAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAA;qBAC5F;gBACL,CAAC,CAAC,CAAA;aACL;YACD,OAAM,CAAC,kBAAkB;SAC5B;aAAM,IAAI,OAAA,IAAI,CAAC,QAAQ,0CAAE,YAAY,MAAK,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;YACnE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAA,CAAC,6BAA6B;SAClD;QACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,GAAG,IAAI,GAAG,OAAO,CAAA,CAAC,WAAW;SACpC;aAAM,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;YAC/B,IAAI,GAAG,IAAI,GAAG,OAAO,CAAA,CAAC,aAAa;SACtC;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,uBAAY,IAAI,IAAI,KAAK,MAAM,EAAE;YAClD,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA,CAAC,GAAG;SACpC;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,MAAA,IAAI,CAAC,QAAQ,0CAAE,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC;IAC5C,CAAC;IAED,OAAO;IACC,+BAAY,GAApB,UAAqB,GAAW,EAAE,MAAmB,EAAE,IAAU;QAA/B,uBAAA,EAAA,WAAmB;QACjD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YACpB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC1C,IAAI,IAAI,EAAE;gBACN,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE,IAAI,CAAC,CAAA;aAC3C;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAChE,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,GAAG,CAAC,CAAA;YACxB,IAAI,IAAI,EAAE;gBACN,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE,IAAI,CAAC,CAAA;aAC3C;SACJ;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACpB,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YACnC,IAAI,GAAG,EAAE;gBACL,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,IAAI,CAAC,CAAA;aACnC;SACJ;IACL,CAAC;IAED,OAAO;IACC,0BAAO,GAAf,UAAgB,GAAW,EAAE,IAAU;;QACnC,IAAI,CAAC,GAAG,EAAE;YACN,OAAM;SACT;QACD,GAAG,SAAG,yCAA0B,CAAC,GAAG,CAAC,mCAAI,GAAG,CAAA;QAC5C,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAEO,0BAAO,GAAf,UAAgB,GAAW,EAAE,GAAY;QACrC,IAAI,CAAC,GAAG,EAAE;YACN,OAAM;SACT;QACD,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;IACxC,CAAC;IAEO,+BAAY,GAApB,UAAqB,GAAW,EAAE,GAAY;QAC1C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAM;SACT;QACD,IAAI,GAAG,GAAG,IAAI,CAAA;QACd,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YACpB,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SACtC;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC3B,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;SAChC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAC1B,CAAC;IAEO,kCAAe,GAAvB,UAAwB,GAAW,EAAE,GAAY;QAC7C,IAAI,GAAG,GAAG,IAAI,CAAA;QACd,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YACpB,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAChD;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC3B,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;SAC1C;QACD,IAAI,GAAG,EAAE;YACL,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;SACtC;IACL,CAAC;IAED,SAAS;IACD,oCAAiB,GAAzB,UAA0B,IAAY;;QAClC,IAAI,OAAA,IAAI,CAAC,IAAI,0CAAE,EAAE,MAAK,2BAAgB,EAAE;YACpC,OAAO,KAAK,CAAA;SACf;aAAM,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,KAAK,EAAE;YACnG,OAAO,KAAK,CAAA;SACf;QACD,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;IACjG,CAAC;IAED,SAAS;IACD,8BAAW,GAAnB,UAAoB,IAAY;;QAC5B,IAAI,aAAA,IAAI,CAAC,IAAI,0CAAE,iBAAiB,4CAAI,EAAE,MAAK,gBAAQ,CAAC,WAAW,IAAI,QAAC,IAAI,CAAC,IAAI,0CAAE,SAAS,CAAC,gBAAQ,CAAC,IAAI,EAAC,EAAE;YACrG,OAAO,KAAK,CAAA;SACf;QACD,OAAO,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,OAAO,CAAA;IAChE,CAAC;IAED,OAAO;IACC,yBAAM,GAAd,UAAe,GAAW;QACtB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA,CAAC,SAAS;SACjC;aAAM,IAAI,GAAG,KAAK,CAAC,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;SAC7C;IACL,CAAC;IAED,OAAO;IACO,4BAAS,GAAvB;;;;;;;wBACI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,uBAAY,EAAE;4BAC/B,4BAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAAE,KAAK,CAAC,oBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAC;yBACjF;6BAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;4BACzD,sBAAM;yBACT;wBACU,qBAAM,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAAnE,EAAE,GAAG,SAA8D;wBACzE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;4BAC7B,sBAAO,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA;yBAC7B;wBACD,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;wBACrB,EAAE,CAAC,MAAM,GAAG,EAAE,CAAA;wBACd,EAAE,CAAC,MAAM,GAAG,IAAI,CAAA;wBAChB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;4BACpB,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;yBACzB;6BAAM;4BACH,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;yBAClD;wBACD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,YAAY,CAAC,mBAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACvD,MAAA,IAAI,CAAC,KAAK,0CAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAC;wBAClD,MAAA,IAAI,CAAC,KAAK,0CAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC;wBAChG,IAAI,CAAC,UAAU,OAAC,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,YAAY,CAAC,mCAAI,CAAC,CAAC,CAAA;wBACrF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAA;;;;;KAChD;IAEM,6BAAU,GAAjB,UAAkB,GAAW;QACzB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAChD,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;YAC3D,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;aACvG;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;SACtJ;IACL,CAAC;IAED,6BAA6B;IACrB,qCAAkB,GAA1B;QACI,IAAM,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAA;QAC7B,OAAO,CAAC,CAAC,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,2BAAgB,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAtC,CAAsC,CAAC,CAAC,CAAA;IACnL,CAAC;IAED,gBAAgB;IACR,uCAAoB,GAA5B;QACI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,oBAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,CAAA;IAC5H,CAAC;IAED,SAAS;IACF,gCAAa,GAApB,UAAqB,GAAY;;QAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;SAC9C;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC7E,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAC9B,MAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;SAClD;aAAM;YACH,MAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,0CAAE,SAAS,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAC;SACxE;IACL,CAAC;IACM,sCAAmB,GAA1B;;QACI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAC3C,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YACzC,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAC9B,MAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;SAClD;aAAM;YACH,MAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,0CAAE,SAAS,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAC;SACxE;QACD,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAA;IACzB,CAAC;IAEO,0CAAuB,GAA/B;;QACI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,QAAC,IAAI,CAAC,IAAI,CAAC,KAAK,0CAAE,EAAE,CAAA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QAClF,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,CAAA;QACpD,UAAI,IAAI,CAAC,IAAI,CAAC,KAAK,0CAAE,EAAE,EAAE;YACrB,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;SAC9F;aAAM;YACH,GAAG,CAAC,WAAW,GAAG,IAAI,CAAA;SACzB;IACL,CAAC;IAEO,8BAAW,GAAnB,UAAoB,CAAU;QAC1B,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,2BAAiB,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,2BAAiB,CAAC,CAAA;YAC9C,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;YACtE,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;YACzB,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;SACnC;QACD,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;IACzB,CAAC;IAED,OAAO;IACC,0BAAO,GAAf;QACI,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACzB,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,yBAAyB;SAC5B;IACL,CAAC;IAED,WAAW;IACJ,8BAAW,GAAlB,UAAmB,GAAY;QAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,GAAG,CAAA;SACpC;IACL,CAAC;IAED,OAAO;IACA,4BAAS,GAAhB,UAAiB,KAAc;QAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAA;IACzD,CAAC;IAED,OAAO;IACA,yBAAM,GAAb;QACI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;IACnE,CAAC;IAED,OAAO;IACA,0BAAO,GAAd;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAChC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACrB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED,yBAAM,GAAN,UAAO,EAAU;QACb,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAM;SACT;QACD,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,WAAW;IACH,+BAAY,GAApB;;QACI,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,QAAC,IAAI,CAAC,IAAI,0CAAE,SAAS,CAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE;YAC/H,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAC/B,IAAI,CAAC,YAAY,GAAG,CAAC,QAAC,IAAI,CAAC,IAAI,0CAAE,SAAS,CAAA,CAAA;YAC1C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAA;YACpC,IAAM,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAA;YAC1C,IAAI,GAAG,GAAG,CAAC,CAAA;YACX,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,gBAAQ,CAAC,SAAS,EAAE;gBAChD,GAAG,GAAG,CAAC,CAAA,CAAC,WAAW;aACtB;iBAAM,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;gBAClC,GAAG,GAAG,KAAK,KAAK,iBAAS,CAAC,MAAM,IAAI,KAAK,IAAI,iBAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aACvE;iBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;gBACnC,OAAM;aACT;iBAAM;gBACH,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aAC7K;YACD,IAAM,KAAK,GAAG,CAAC,0BAAe,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,CAAA;YAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;aAC5C;YACD,gGAAgG;SACnG;IACL,CAAC;IAED,OAAO;IACC,iCAAc,GAAtB,UAAuB,IAAc;QACjC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAChD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;SAClE;IACL,CAAC;IAEa,wCAAqB,GAAnC,UAAoC,EAAW,EAAE,EAAW;;;;;;wBAClD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;wBAClB,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;wBACf,IAAI,GAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;6BAC/C,IAAI,EAAJ,wBAAI;wBACK,qBAAM,oBAAO,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAC,CAAS,EAAE,CAAS,IAAK,OAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAA;;wBAAzH,MAAM,GAAG,SAAgH,CAAA;;;wBAEvH,IAAI,GAAG,qBAAS,CAAC,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;wBACnD,IAAI,CAAC,WAAW,CAAC,iBAAS,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;wBAC5E,qBAAM,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,EAAA;;wBAA3B,SAA2B,CAAA;wBAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG,iBAAS,CAAC,KAAK,EAAE;4BACnC,IAAI,CAAC,WAAW,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAA;yBACnC;;;;;KACJ;IAED,OAAO;IACC,8BAAW,GAAnB;;QACI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;YAClC,MAAA,IAAI,CAAC,KAAK,0CAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAC;SACrD;IACL,CAAC;IAED,OAAO;IACC,oCAAiB,GAAzB;;QACI,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,cAAc,EAAE;YAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;YACzC,MAAA,IAAI,CAAC,KAAK,0CAAE,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC;SAC5F;IACL,CAAC;IAED,WAAW;IACH,6BAAU,GAAlB;QAAA,iBA4BC;QA3BG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;QACvB,IAAI,eAAe,GAAG,EAAE,EAAE,UAAU,GAAG,CAAC,EAAE,WAAW,GAAG,KAAK,EAAE,kBAAkB,GAAG,CAAC,CAAA;QACrF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YACrB,IAAI,sBAAW,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO;gBAC9B,KAAI,CAAC,cAAc,IAAI,CAAC,CAAC,KAAK,CAAA;aACjC;YACD,IAAI,yBAAc,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACxB,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;aACjC;iBAAM,IAAI,2BAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAA,CAAC,QAAQ;aAC/B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,YAAY,EAAE,EAAE,IAAI;gBAC/C,WAAW,GAAG,IAAI,CAAA;aACrB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,EAAE,EAAE,MAAM;gBACnD,kBAAkB,GAAG,CAAC,CAAC,KAAK,CAAA;aAC/B;QACL,CAAC,CAAC,CAAA;QACF,WAAW;QACX,KAAK,IAAI,CAAC,IAAI,yBAAc,EAAE;YAC1B,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;YACtB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAA;SACnD;QACD,WAAW;QACX,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;QACjC,OAAO;QACP,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QACnC,KAAK;QACL,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,kBAAkB,GAAG,IAAI,CAAC,CAAA;IACvD,CAAC;IAED,OAAO;IACC,oCAAiB,GAAzB,UAA0B,GAAY;QAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,GAAG,EAAE;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;YAC7B,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAA;SACrC;aAAM,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;YAC9B,IAAI,IAAI,CAAC,YAAY,KAAK,YAAY,EAAE;gBACpC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;aAC7B;SACJ;IACL,CAAC;IAED,WAAW;IACH,iCAAc,GAAtB,UAAuB,IAAc,EAAE,GAAY;QAC/C,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;SAC7B;aAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;YACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;SAClC;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;YAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SACrB;IACL,CAAC;IAED,wBAAwB;IAChB,mCAAgB,GAAxB,UAAyB,IAAc;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAC9B,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,cAAc,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACjC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;SAC1B;QACD,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;SACtB;IACL,CAAC;IAED,WAAW;IACG,2BAAQ,GAAtB,UAAuB,IAAc;;;;;;wBAC3B,QAAQ,GAAa,8BAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA;wBACtD,IAAI,GAAG,OAAO,GAAG,QAAQ,CAAA;wBAC3B,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC,CAAA;6BAC9C,EAAE,EAAF,wBAAE;wBACF,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;;;6BAC7B,CAAC,IAAI,CAAC,aAAa,EAAnB,wBAAmB;wBAC1B,sBAAO,mBAAQ,CAAC,cAAc,CAAC,UAAU,EAAE,qBAAqB,CAAC,EAAA;;6BAC1D,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAA5B,wBAA4B;wBACnC,sBAAM;;wBAEN,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;wBAC9B,qBAAM,WAAW,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAApD,EAAE,GAAG,SAA+C,CAAA;wBACpD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;4BAC7B,sBAAO,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAA;yBAC7B;wBACD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;wBACpC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;;;wBAE3B,EAAE,CAAC,OAAO,GAAG,GAAG,CAAA;wBAChB,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;wBACrB,EAAE,CAAC,MAAM,GAAG,2BAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;wBACtC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,2BAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;wBACvG,IAAI,QAAQ,KAAK,gBAAQ,CAAC,MAAM;+BACzB,QAAQ,KAAK,gBAAQ,CAAC,iBAAiB;+BACvC,QAAQ,KAAK,gBAAQ,CAAC,eAAe;+BACrC,QAAQ,KAAK,gBAAQ,CAAC,mBAAmB;+BACzC,QAAQ,KAAK,gBAAQ,CAAC,mBAAmB;+BACzC,QAAQ,KAAK,gBAAQ,CAAC,iBAAiB;+BACvC,QAAQ,KAAK,gBAAQ,CAAC,aAAa,EACxC;4BACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAM,OAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAlC,CAAkC,CAAC,CAAA;yBACjE;6BAAM;4BACH,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;yBACrB;;;;;KACJ;IAEO,0BAAO,GAAf,UAAgB,IAAc;QAC1B,IAAM,QAAQ,GAAG,8BAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA;QAClD,IAAM,IAAI,GAAG,OAAO,GAAG,QAAQ,CAAA;QAC/B,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,IAAI,EAAE;SACV;aAAM,IAAI,IAAI,KAAK,gBAAQ,CAAC,MAAM;eAC5B,QAAQ,KAAK,gBAAQ,CAAC,iBAAiB;eACvC,IAAI,KAAK,gBAAQ,CAAC,eAAe;eACjC,IAAI,KAAK,gBAAQ,CAAC,mBAAmB;eACrC,IAAI,KAAK,gBAAQ,CAAC,mBAAmB;eACrC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,EACxC;YACE,IAAI,CAAC,SAAS,CAAC,2BAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,cAAM,OAAA,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAArB,CAAqB,CAAC,CAAA;SAC7E;aAAM;YACH,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;SACxB;IACL,CAAC;IAED,OAAO;IACC,kCAAe,GAAvB,UAAwB,GAAW;QAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,GAAG,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAA;YACzB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;SAC/E;IACL,CAAC;IAED,SAAS;IACD,iCAAc,GAAtB;QAAA,iBAQC;;QAPG,IAAI,CAAC,IAAI,CAAC,KAAK,WAAI,IAAI,CAAC,IAAI,0CAAE,KAAK,GAAE,EAAE;YACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;YACjB,IAAM,IAAI,SAAG,IAAI,CAAC,QAAQ,0CAAE,YAAY,CAAA;YACxC,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,UAAU,EAAE;gBAChF,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,cAAM,OAAA,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,WAAW,EAAE,KAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAxE,CAAwE,CAAC,CAAA;aAC5G;SACJ;IACL,CAAC;IAED,SAAS;IACD,8BAAW,GAAnB;;QACI,IAAI,oBAAO,CAAC,QAAQ,CAAC,YAAY,EAAE;YAC/B,OAAM;SACT;aAAM,IAAI,QAAC,IAAI,CAAC,IAAI,0CAAE,KAAK,CAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE;YACpF,OAAM;SACT;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAA;QACtC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAA;QAC1B,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;QAC/D,qEAAqE;QACrE,2HAA2H;QAC3H,IAAI,KAAK,KAAK,iBAAS,CAAC,KAAK,EAAE,EAAE,IAAI;YACjC,IAAI,CAAC,OAAO,EAAE,CAAA;SACjB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,IAAI,IAAI,KAAK,KAAK,iBAAS,CAAC,SAAS,EAAE,EAAE,IAAI;YACxE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,MAAM,EAAE,EAAE,IAAI;YACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;SACtB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,GAAG,EAAE,EAAE,IAAI;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;SACnB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,KAAK,EAAE,EAAE,IAAI;YACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SACrB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,IAAI,EAAE,EAAE,IAAI;YACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,SAAS,EAAE,EAAE,IAAI;YAC5C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SACxB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,SAAS,EAAE,EAAE,KAAK;YAC7C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SACxB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,IAAI,EAAE,EAAE,IAAI;YACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;aAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,GAAG,EAAE,EAAE,MAAM;YACxC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;SACnB;aAAM,IAAI,KAAK,IAAI,iBAAS,CAAC,KAAK,IAAI,KAAK,IAAI,iBAAS,CAAC,SAAS,EAAE,EAAE,IAAI;YACvE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SACrB;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;SAC7B;QACD,OAAO;QACP,8HAA8H;QAC9H,sHAAsH;QACtH,IAAI;IACR,CAAC;IAED,KAAK;IACG,0BAAO,GAAf;;QACI,IAAM,QAAQ,SAAG,IAAI,CAAC,QAAQ,0CAAE,YAAY,CAAA;QAC5C,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,WAAW,EAAE,EAAE,mBAAmB;YACtE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;SAC7B;QACD,MAAA,IAAI,CAAC,KAAK,0CAAE,QAAQ,GAAE;QACtB,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,KAAK;IACG,yBAAM,GAAd,UAAe,IAAS;QAAxB,iBAoCC;;QAnCG,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAM,KAAK,GAAc,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAzC,CAAyC,CAAC,CAAA;QAC/F,IAAM,YAAY,SAAG,IAAI,CAAC,YAAY,mCAAI,CAAC,CAAA;QAC3C,IAAM,YAAY,SAAG,IAAI,CAAC,YAAY,mCAAI,CAAC,CAAA;QAC3C,WAAW;QACX,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,CAAA;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;YACrF,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAA;YACvB,MAAM,IAAI,GAAG,CAAA;YACb,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAA;SAC5F;QACD,IAAI,KAAK,GAAG,YAAY,GAAG,YAAY,CAAA;QACvC,IAAI,QAAQ,GAAY,IAAI,EAAE,IAAI,GAAG,EAAE,CAAA;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,QAAQ,GAAG,MAAM,CAAA;YAC1C,IAAI,KAAK,GAAG,EAAE,EAAE;gBACZ,SAAQ;aACX;iBAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM;gBAC1B,IAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,MAAM,CAAA;gBACzB,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;gBACxC,IAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAA,CAAC,SAAS;gBAC9B,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;gBACzC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAA,CAAC,UAAU;aACxB;YACD,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,MAAM,GAAG,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;SACpE;QACD,IAAI,CAAC,QAAQ,EAAE;YACX,OAAM;SACT;QACD,OAAO;QACP,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QAC1B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAA;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC;IACO,0BAAO,GAAf,UAAgB,IAAW;QAA3B,iBASC;QARG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;SAClB;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,MAAM,CAAA;YACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAChC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,GAAG,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAlB,CAAkB,CAAC,CAAA;SAC1F;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAS,CAAC,SAAS,EAAE;YAChG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;SAC7B;IACL,CAAC;IAED,KAAK;IACG,2BAAQ,GAAhB,UAAiB,IAAS;QAA1B,iBAQC;;QAPG,IAAM,cAAc,SAAG,IAAI,CAAC,cAAc,mCAAI,CAAC,CAAA;QAC/C,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAA;QAClD,IAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,IAAI,EAAE,CAAA;QAChD,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACzC,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;QACzC,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,MAAM,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAA1C,CAA0C,EAAE,cAAc,CAAC,CAAA;IAC3G,CAAC;IAED,KAAK;IACG,0BAAO,GAAf,UAAgB,IAAS;QAAzB,iBA6EC;;QA5EG,IAAM,cAAc,SAAG,IAAI,CAAC,cAAc,mCAAI,CAAC,CAAA;QAC/C,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAA;QAClD,IAAM,KAAK,GAAiB,IAAI,CAAC,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACjF,KAAK;QACL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAa,CAAC,SAAS;eAC/E,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAa,CAAC,SAAS;eACvC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAa,CAAC,SAAS;eACvC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAa,CAAC,SAAS;eACvC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAa,CAAC,SAAS;eACvC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,SAAS,CAC1C,EAAE;YACC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClC,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAChC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;YACzB,IAAI,CAAC,SAAS,EAAE;aACf;iBAAM,IAAI,SAAS,CAAC,EAAE,KAAK,gBAAQ,CAAC,SAAS;mBACvC,SAAS,CAAC,EAAE,KAAK,gBAAQ,CAAC,MAAM;mBAChC,SAAS,CAAC,EAAE,KAAK,gBAAQ,CAAC,WAAW;mBACrC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,SAAS;mBACpC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,QAAQ;mBACnC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,UAAU;mBACrC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,QAAQ,EACxC;gBACE,MAAM,GAAG,SAAS,CAAC,MAAM,CAAA,CAAC,iBAAiB;aAC9C;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE;gBACrC,MAAM,GAAG,UAAU,CAAA,CAAC,QAAQ;aAC/B;YACG,IAAA,KAAA,OAAgB,EAAE,CAAC,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,IAAA,EAA9C,KAAK,QAAA,EAAE,IAAI,QAAmC,CAAA;YACnD,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,CAAC,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,GAAG,EAAE,cAAM,OAAA,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAzC,CAAyC,CAAC,CAAA;SAC7K;aAAM;YACH,IAAI,CAAC,cAAc,EAAE,CAAA;YACrB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;SAC5C;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SAC3B;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAA;SACnC;QACD,IAAM,aAAa,GAAG,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAa,CAAC,SAAS,CAAA,CAAC,IAAI;QAClE,IAAM,eAAe,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACjF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,EAAE;YAC1C,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE;aAClB;iBAAM,IAAI,aAAa,EAAE;gBACtB,KAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAA;aACrC;iBAAM,IAAI,eAAe,EAAE;gBACxB,KAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;aAClC;iBAAM;gBACH,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;aAC7B;QACL,CAAC,EAAE,cAAc,CAAC,CAAA;QAClB,cAAc;QACd,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAa,CAAC,SAAS,EAAE;YACzC,IAAI,IAAI,GAAW,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAA;YAC1C,UAAU;YACV,IAAI,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,0CAAE,KAAK,0CAAE,EAAE,MAAK,gBAAQ,CAAC,SAAS,EAAE;gBACvD,IAAI,GAAG,MAAM,CAAA;gBACb,KAAK,GAAG,GAAG,CAAA;aACd;YACD,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,kBAAkB,EAAE;gBAC3C,IAAI,MAAA,EAAE,KAAK,OAAA;gBACX,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;gBACvB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;aACzB,CAAC,CAAA;SACL;QACD,SAAS;QACT,IAAI,IAAI,CAAC,SAAS,KAAK,eAAe,EAAE;YACpC,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,eAAe,EAAE;gBACxC,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,cAAc;gBACxB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;gBACvB,UAAU,EAAE,WAAW;gBACvB,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;aAC/B,CAAC,CAAA;SACL;IACL,CAAC;IAED,KAAK;IACG,wBAAK,GAAb,UAAc,IAAS;QAAvB,iBA4CC;;QA3CG,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC9B,IAAI,MAAM,SAAG,IAAI,CAAC,MAAM,mCAAI,CAAC,CAAA;QAC7B,IAAM,UAAU,SAAG,IAAI,CAAC,UAAU,mCAAI,CAAC,CAAA;QACvC,IAAM,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA,CAAC,IAAI;QACjC,IAAM,IAAI,SAAG,IAAI,CAAC,IAAI,mCAAI,CAAC,CAAA,CAAC,IAAI;QAChC,IAAM,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAA,CAAC,IAAI;QAClC,IAAM,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAA,CAAC,IAAI;QAClC,IAAM,cAAc,GAAG,MAAM,KAAK,CAAC,CAAC,CAAA,CAAC,IAAI;QACzC,IAAM,WAAW,GAAG,MAAM,KAAK,CAAC,CAAC,CAAA,CAAC,IAAI;QACtC,MAAM,GAAG,OAAO,IAAI,OAAO,IAAI,cAAc,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;QACzE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAA;QAClD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QACvC,IAAM,IAAI,SAAG,IAAI,CAAC,IAAI,mCAAI,CAAC,CAAA,CAAC,OAAO;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA,CAAC,MAAM;QAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACzC,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,GAAE;QAClB,IAAI,MAAM,GAAG,UAAU,KAAK,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;SACpC;aAAM,IAAI,KAAK,EAAE;YACd,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,gBAAQ,CAAC,SAAS,EAAE;gBAChD,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;aACvB;YACD,IAAI,CAAC,UAAU,EAAE,CAAA;SACpB;aAAM,IAAI,OAAO,EAAE,EAAE,iBAAiB;YACnC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;SACpC;QACD,IAAI,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI,KAAK,EAAE;YACP,QAAQ,GAAG,KAAK,CAAA;YAChB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;SACjC;aAAM,IAAI,KAAK,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;SACtB;QACD,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YACzB,IAAI,KAAK,EAAE;gBACP,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;aACtD;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACrB,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;aAC7B;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACC,wBAAK,GAAb,UAAc,IAAS;;QACnB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;SACvB;QACD,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,OAAO,OAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAA;QAC3C,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,cAAM,OAAA,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,CAAC,EAAnD,CAAmD,CAAC,CAAA;IACxF,CAAC;IAED,KAAK;IACG,0BAAO,GAAf,UAAgB,IAAS;QAAzB,iBAmBC;;QAlBG,IAAM,IAAI,SAAG,IAAI,CAAC,IAAI,mCAAI,CAAC,CAAA,CAAC,OAAO;QACnC,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAA;QACrD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACzC,IAAI,IAAI,GAAG,CAAC,EAAE;YACV,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;YAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACnB,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpD,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,qBAAqB,OAAC,IAAI,CAAC,cAAc,mCAAI,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,GAAG,EAAE;gBACrH,IAAI,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,KAAK,EAAE;oBAC7B,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;iBAC7B;YACL,CAAC,CAAC,CAAA;SACL;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAA;SACxB;IACL,CAAC;IAED,KAAK;IACG,yBAAM,GAAd,UAAe,IAAS;QAAxB,iBAgBC;;QAfG,IAAM,IAAI,SAAG,IAAI,CAAC,IAAI,mCAAI,CAAC,CAAA,CAAC,OAAO;QACnC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;YAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAClC,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpD,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;YACxC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,GAAG,EAAE;gBACpE,IAAI,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,KAAK,EAAE;oBAC7B,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;iBAC7B;YACL,CAAC,CAAC,CAAA;SACL;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAA;SACxB;IACL,CAAC;IAED,KAAK;IACG,yBAAM,GAAd,UAAe,IAAS;;QACpB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC9B,IAAM,GAAG,SAAG,IAAI,CAAC,GAAG,mCAAI,CAAC,CAAA;QACzB,IAAM,IAAI,SAAG,IAAI,CAAC,IAAI,mCAAI,CAAC,CAAA;QAC3B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,GAAE;QAClB,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,EAAE,YAAY;YACvC,MAAA,IAAI,CAAC,KAAK,0CAAE,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC;SAC5F;IACL,CAAC;IAED,KAAK;IACG,6BAAU,GAAlB,UAAmB,IAAS;;QACxB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC9B,IAAM,MAAM,SAAG,IAAI,CAAC,MAAM,mCAAI,CAAC,CAAA;QAC/B,IAAM,UAAU,SAAG,IAAI,CAAC,UAAU,mCAAI,CAAC,CAAA;QACvC,IAAM,IAAI,SAAG,IAAI,CAAC,IAAI,mCAAI,CAAC,CAAA;QAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QACvC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAI,KAAK,EAAE;YACP,MAAA,IAAI,CAAC,KAAK,0CAAE,SAAS,CAAC,KAAK,EAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YACpB,IAAI,CAAC,UAAU,EAAE,CAAA;YACjB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,cAAM,OAAA,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,CAAC,EAAnD,CAAmD,CAAC,CAAA;SACvF;aAAM;YACH,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,GAAE;SACrB;IACL,CAAC;IAED,OAAO;IACC,6BAAU,GAAlB,UAAmB,IAAS;QACxB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,iBAAiB;IACrB,CAAC;IAr/BgB,QAAQ;QAD5B,OAAO;OACa,QAAQ,CAs/B5B;IAAD,eAAC;CAt/BD,AAs/BC,CAt/BqC,EAAE,CAAC,SAAS,GAs/BjD;kBAt/BoB,QAAQ", "file": "", "sourceRoot": "/", "sourcesContent": ["import { AREA_MAX_ZINDEX, BUFF_NODE_ZINDEX, BUFF_SHOW_TYPE_TRAN, BUILD_SMITHY_NID, FIRE_PAWN_ID, NEED_MUTUAL_BUFF, NEED_SHOW_BUFF, PAWN_CROSSBOW_ID, SHIELD_BUFF, SPEAR_PAWN_ID, TILE_SIZE } from \"../../common/constant/Constant\";\nimport { BuffType, HeroType, PawnSkillType, PawnState, PawnType, PreferenceKey } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { mapHelper } from \"../../common/helper/MapHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport { wxHelper } from \"../../common/helper/WxHelper\";\nimport OutlineShaderCtrl from \"../../common/shader/OutlineShaderCtrl\";\nimport PawnObj from \"../../model/area/PawnObj\";\nimport PawnSkillObj from \"../../model/area/PawnSkillObj\";\nimport ClickTouchCmpt from \"../cmpt/ClickTouchCmpt\";\nimport FrameAnimationCmpt from \"../cmpt/FrameAnimationCmpt\";\nimport BuffIconCmpt from \"./BuffIconCmpt\";\nimport HPBarCmpt from \"./HPBarCmpt\";\nimport { PAWN_SOUND_CONF_TRANSITION } from \"./PawnAnimConf\";\nimport PawnAnimationCmpt from \"./PawnAnimationCmpt\";\n\nconst { ccclass, property } = cc._decorator;\n\n// 士兵\n@ccclass\nexport default class PawnCmpt extends cc.Component {\n\n    private key: string = ''\n\n    public data: PawnObj = null\n    public body: cc.Node = null\n    public curSkinId: number = 0\n    public curPortrayalId: number = 0\n    private animNode: cc.Node = null\n    private animCmpt: PawnAnimationCmpt = null\n    private touchCmpt: ClickTouchCmpt = null\n    private hpBar: HPBarCmpt = null\n\n    private animNodeInitY: number = 0\n    private origin: cc.Vec2 = cc.v2() //起点\n    private originY: number = 0\n    private tempBodyPosition: cc.Vec2 = cc.v2()\n\n    private prePoint: cc.Vec2 = cc.v2()\n    private prePositionY: number = -1\n    private preAnger: number = -1\n    private curShieldValue: number = -1 //当前护盾值\n    private preShieldValue: number = -1\n    private preActioning: boolean = false\n    private preStateUid: string = ''\n    private preAnimName: string = ''\n    private currAnimName: string = ''\n    private isDie: boolean = false //是否在视图层死亡\n    private isShowStandShield: boolean = false //是否显示立盾\n    private isShowBuffMap: any = {} //外显buff\n    private mutualBuffType: BuffType = 0 //当前显示的互斥buff\n\n    private isLoadBuffMap: any = {}\n    private buffNodes: cc.Node[] = [] //buff节点列表\n    private buffIconCmpt: BuffIconCmpt = null //buff图标容器\n    private isDiaup: boolean = false\n\n    private _temp_vec2_1: cc.Vec2 = cc.v2()\n    private _temp_vec2_2: cc.Vec2 = cc.v2()\n    private _temp_vec2_3: cc.Vec2 = cc.v2()\n    private _temp_position: cc.Vec2 = cc.v2()\n\n    public tempIndex = 0\n\n    public init(data: PawnObj, origin: cc.Vec2, key: string) {\n        this.reset(data.uid)\n        this.data = data\n        this.key = key\n        this.curSkinId = data.skinId\n        this.curPortrayalId = data.getPortrayalId()\n        this.origin.set(origin)\n        this.originY = origin.y * TILE_SIZE\n        this.body = this.FindChild('body')\n        this.body.getPosition(this.tempBodyPosition)\n        this.updatePosition(true)\n        this.updateZIndex()\n        this.updateAnger()\n        this.updateShieldValue()\n        this.preStateUid = ''\n        this.animNode = this.FindChild('body/anim')\n        this.animCmpt = this.node.addComponent(PawnAnimationCmpt).init(this.animNode.getComponent(cc.Sprite), data.getViewId(), key)\n        if (this.animNodeInitY === 0) {\n            this.animNodeInitY = this.animNode.y\n        }\n        this.animNode.scale = 1\n        this.animNode.y = this.animNodeInitY\n        // 设置体型\n        this.updateAnimScale(1 + data.getBuffValue(BuffType.FEED_INTENSIFY) * 0.02)\n        // 非战斗单位不用加点击\n        const isNoncombat = data.isNoncombat()\n        if (!isNoncombat) {\n            this.touchCmpt = this.FindChild('touch').addComponent(ClickTouchCmpt).on(this.onClick, this)/* .setPlayAction(true).setTarget(this.body) */\n        }\n        if (data.isBuilding() || isNoncombat) {\n            // 秦良玉的矛 特殊处理下\n            this.body.scaleX = data.id === SPEAR_PAWN_ID ? data.enterDir : 1\n            if (data.id !== FIRE_PAWN_ID || data.enterDir === 2) {\n                this.playAnimation('create', () => {\n                    if (this.isValid) {\n                        this.loadHPBar()\n                        this.playAnimation('idle')\n                    }\n                })\n                return this\n            }\n        } else if (data.isHasBuff(BuffType.STAND_SHIELD)) {\n            this.loadHPBar()\n            this.playAnimation('stand_shield') //如果有立盾\n            return this\n        }\n        this.loadHPBar()\n        this.playAnimation('idle')\n        return this\n    }\n\n    private reset(uid: string) {\n        this.stopSFXByKey('move_sound', uid)\n        this.putAllBuff()\n        this.isShowStandShield = false\n        this.isShowBuffMap = {}\n        this.mutualBuffType = 0\n        this.isLoadBuffMap = {}\n    }\n\n    private putAllBuff() {\n        for (let k in NEED_SHOW_BUFF) {\n            this.putBuff(Number(k))\n        }\n        this.putBuff(this.mutualBuffType)\n    }\n\n    // 重新同步一下信息\n    public resync(data: PawnObj, jump: boolean = false) {\n        this.reset(data.uid)\n        // this.animCmpt?.stop()\n        this.animCmpt?.resetMove()\n        const point = this.getActPoint()\n        this.data = data\n        if (!jump && point && !data.isBattleing() && data.aIndex >= 0 && !data.point.equals(point)) {\n            this.prePoint.set(data.point)\n            this.updatePositionForMove(point, data.point.clone())\n        } else {\n            this.prePoint.set2(-1, -1)\n            this.updatePosition()\n            this.updateZIndex()\n        }\n        this.hpBar?.init(data)\n        this.hpBar?.updateAnger(data.getAngerRatio())\n        this.showPawnLv(gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_LV) ?? 1)\n        this.showPawnEquip(this.isCanShowPawnEquip())\n        this.setCanClick(true)\n        this.preStateUid = ''\n        this.animCmpt?.initModel()\n        // 秦良玉没矛状态\n        if (data.getPortrayalSkill()?.id === HeroType.QIN_LIANGYU && data.getState() === PawnState.STAND) {\n            if (data.isHasBuff(BuffType.BARB)) {\n                this.playAnimation('idle_barb')\n            } else {\n                this.playAnimation('idle')\n            }\n        }\n        return this\n    }\n\n    public clean(release?: boolean) {\n        if (!this.isValid || !this.node) {\n            return cc.error('clean error?')\n        }\n        this.unscheduleAllCallbacks()\n        this.stopSFXByKey('move_sound', this.uid)\n        this.node.stopAllActions()\n        this.touchCmpt?.clean()\n        this.animCmpt?.clean()\n        this.isShowStandShield = false\n        this.isShowBuffMap = {}\n        this.mutualBuffType = 0\n        this.isLoadBuffMap = {}\n        nodePoolMgr.put(this.hpBar?.node)\n        this.hpBar = null\n        this.buffNodes.forEach(m => nodePoolMgr.put(m))\n        this.buffNodes.length = 0\n        this.buffIconCmpt?.clean()\n        nodePoolMgr.put(this.buffIconCmpt?.node)\n        this.buffIconCmpt = null\n        this.node.destroy()\n        release && assetsMgr.releaseTempRes(this.data?.getPrefabUrl(), this.key)\n        this.data = null\n    }\n\n    public get id() { return this.data?.id }\n    public get uid() { return this.data?.uid }\n    public get cuid() { return this.data?.cuid }\n    public get point() { return this.data.point }\n    public getBody() { return this.body }\n    public getTempPosition() { return this.getPosition(this._temp_position) }\n    public getAbsUid() { return this.uid + (this.curPortrayalId || this.curSkinId || this.id) }\n\n    public getActPoint() {\n        return this.getActPointByPixel(this.getTempPosition())\n    }\n\n    // 根据像素点获取网格点\n    public getActPointByPixel(pixel: cc.Vec2) {\n        return mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin)\n    }\n\n    // 根据网格点获取像素点\n    public getActPixelByPoint(point: cc.Vec2) {\n        return mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1)\n    }\n\n    // 播放化身\n    public async playAvatarHeroAnim(id: number) {\n        const it = await nodePoolMgr.get('pawn/AVATAR_HERO', this.key)\n        if (!this.isValid || !this.data) {\n            return nodePoolMgr.put(it)\n        }\n        it.parent = this.node\n        it.zIndex = 20\n        it.active = true\n        const anim = it.Child('val', FrameAnimationCmpt)\n        await anim.init('avatar_' + id, this.key)\n        if (!this.isValid || !this.data) {\n            return nodePoolMgr.put(it)\n        }\n        anim.play('avatar')\n        this.playSFX('sound_074')\n        ut.wait(2.42).then(() => nodePoolMgr.put(it))\n        await ut.wait(1.98)\n    }\n\n    private playAnimation(name: string, cb?: Function, startTime?: number, intervalMul?: number) {\n        if (name === 'move' && this.data.getState() === PawnState.MOVE) {\n            this.playSFXByKey('move_sound', '', { loop: true, tag: this.uid })\n        } else {\n            this.fadeOutSFXByKey('move_sound', this.uid)\n        }\n        if (this.isDiaup && name !== 'idle') {\n            this.isDiaup = false\n        }\n        if (name !== 'die' && name !== 'stand_shield' && this.data?.isHasBuff(BuffType.STAND_SHIELD)) {\n            this.currAnimName = name\n            if (name !== 'hit') {\n            } else if (this.data.portrayal?.skill?.id === HeroType.CAO_REN) { //曹仁需要播放受击动画\n                this.animCmpt?.play('stand_hit', () => this.isValid && this.playAnimation('stand_shield'), startTime)\n            } else {\n                ut.wait(0.5, this).then(() => {\n                    if (this.isValid && this.data?.state?.type === PawnState.HIT) {\n                        this.data.changeState(PawnState.STAND)\n                        this.currAnimName = this.data?.isHasBuff(BuffType.STAND_SHIELD) ? 'stand_shield' : 'idle'\n                    }\n                })\n            }\n            return //如果是立盾状态就不播放其他动画了\n        } else if (this.animCmpt?.playAnimName === 'create' && name !== 'die') {\n            return cb && cb() //如果还在播放创建 其他动画就不要播放了 死亡还是可以的\n        }\n        if (this.isPullStringState(name)) {\n            name = name + '_pull' //检测是否有拉弦状态\n        } else if (this.isBarbState(name)) {\n            name = name + '_barb' //检测秦良玉手上是否有矛\n        }\n        if (this.data.id === FIRE_PAWN_ID && name === 'idle') {\n            name = 'fire_' + this.data.lv //火\n        }\n        this.currAnimName = name\n        this.animCmpt?.play(name, cb, startTime)\n    }\n\n    // 播放音效\n    private playSFXByKey(key: string, suffix: string = '', opts?: any) {\n        if (this.data.isHero()) {\n            const prev = this.data.portrayal.json[key]\n            if (prev) {\n                return this.playSFX(prev + suffix, opts)\n            }\n        } else if (this.data.skinId > 0) {\n            const json = assetsMgr.getJsonData('pawnSkin', this.data.skinId)\n            const prev = json?.[key]\n            if (prev) {\n                return this.playSFX(prev + suffix, opts)\n            }\n        }\n        if (this.data.baseJson) {\n            const url = this.data.baseJson[key]\n            if (url) {\n                this.playSFX(url + suffix, opts)\n            }\n        }\n    }\n\n    // 播放音效\n    private playSFX(url: string, opts?: any) {\n        if (!url) {\n            return\n        }\n        url = PAWN_SOUND_CONF_TRANSITION[url] ?? url\n        audioMgr.playSFX('pawn/' + url, opts)\n    }\n\n    private stopSFX(url: string, tag?: string) {\n        if (!url) {\n            return\n        }\n        audioMgr.stopSFX('pawn/' + url, tag)\n    }\n\n    private stopSFXByKey(key: string, tag?: string) {\n        if (!this.data) {\n            return\n        }\n        let url = null\n        if (this.data.isHero()) {\n            url = this.data.portrayal.json[key]\n        } else if (this.data.baseJson) {\n            url = this.data.baseJson[key]\n        }\n        this.stopSFX(url, tag)\n    }\n\n    private fadeOutSFXByKey(key: string, tag?: string) {\n        let url = null\n        if (this.data.isHero()) {\n            url = 'pawn/' + this.data.portrayal.json[key]\n        } else if (this.data.baseJson) {\n            url = 'pawn/' + this.data.baseJson[key]\n        }\n        if (url) {\n            audioMgr.fadeOutSFX(0.15, url, tag)\n        }\n    }\n\n    // 是否拉弦状态\n    private isPullStringState(name: string) {\n        if (this.data?.id !== PAWN_CROSSBOW_ID) {\n            return false\n        } else if (name !== 'idle' && name !== 'move' && name !== 'hit' && name !== 'diaup' && name !== 'die') {\n            return false\n        }\n        return !!this.data.getSkillByType(PawnSkillType.PULL_STRING) && this.data.getCurAnger() !== 0\n    }\n\n    // 是否无矛状态\n    private isBarbState(name: string) {\n        if (this.data?.getPortrayalSkill()?.id !== HeroType.QIN_LIANGYU || !this.data?.isHasBuff(BuffType.BARB)) {\n            return false\n        }\n        return name === 'idle' || name === 'hit' || name === 'diaup'\n    }\n\n    // 设置方向\n    private setDir(dir: number) {\n        if (this.data.isBuilding()) {\n            this.body.scaleX = 1 //建筑不需要翻转\n        } else if (dir !== 0) {\n            this.body.scaleX = ut.normalizeNumber(dir)\n        }\n    }\n\n    // 加载血条\n    private async loadHPBar() {\n        if (this.data.id === FIRE_PAWN_ID) {\n            return this.body.Child('bar')?.Color(gameHpr.getBattleFireBarColor(this.data))\n        } else if (this.data.maxHp === 0 || this.data.isNoncombat()) {\n            return\n        }\n        const it = await nodePoolMgr.get(this.data.getHPBarPrefabUrl(), this.key)\n        if (!this.isValid || !this.data) {\n            return nodePoolMgr.put(it)\n        }\n        it.parent = this.node\n        it.zIndex = 10\n        it.active = true\n        if (this.data.isBoss()) {\n            it.setPosition(0, 113)\n        } else {\n            it.setPosition(0, this.data.isHero() ? 56 : 46)\n        }\n        this.hpBar = it.addComponent(HPBarCmpt).init(this.data)\n        this.hpBar?.updateAnger(this.data.getAngerRatio())\n        this.hpBar?.updateShieldValue(this.data.getShieldValue(), this.data.curHp, this.data.getMaxHp())\n        this.showPawnLv(gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_LV) ?? 1)\n        this.showPawnEquip(this.isCanShowPawnEquip())\n    }\n\n    public showPawnLv(val: number) {\n        if (this.hpBar) {\n            this.hpBar.Child('root').opacity = val ? 255 : 0\n            const armyNameLbl = this.hpBar.Child('army_name', cc.Label)\n            if (armyNameLbl) {\n                armyNameLbl.string = (val === 1 && this.data.armyName) ? ut.nameFormator(this.data.armyName, 4) : ''\n            }\n            this.hpBar.Child('lv', cc.Label).string = (val === 1 && this.data.lv && !this.data.isMachine() && !this.data.isBuilding()) ? this.data.lv + '' : ''\n        }\n    }\n\n    // 是否可以显示装备信息 这里如果还没有铁匠铺就不会显示\n    private isCanShowPawnEquip() {\n        const player = gameHpr.player\n        return !!gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_EQUIP) && (player.isCapture() || player.getMainBuilds().some(m => m.id === BUILD_SMITHY_NID && m.lv >= 1))\n    }\n\n    // 是否可以显示没有装备的提示\n    private isCanShowNotEquipTip() {\n        return !this.data.equip.id && this.data.isCanWearEquip() && this.data.isOwner() && gameHpr.player.getEquips().length > 0\n    }\n\n    // 显示装备信息\n    public showPawnEquip(val: boolean) {\n        if (!this.hpBar || !this.hpBar.Child('equip')) {\n        } else if (this.hpBar.Child('equip').active = val && this.data.isCanWearEquip()) {\n            this.updateShowPawnEquipInfo()\n            this.hpBar.Child('not_equip')?.setActive(false)\n        } else {\n            this.hpBar.Child('not_equip')?.setActive(this.isCanShowNotEquipTip())\n        }\n    }\n    public updateShowPawnEquip() {\n        if (!this.hpBar || !this.hpBar.Child('equip')) {\n            return\n        } else if (this.hpBar.Child('equip').active) {\n            this.updateShowPawnEquipInfo()\n            this.hpBar.Child('not_equip')?.setActive(false)\n        } else {\n            this.hpBar.Child('not_equip')?.setActive(this.isCanShowNotEquipTip())\n        }\n        this.hpBar.initInfo()\n    }\n\n    private updateShowPawnEquipInfo() {\n        this.hpBar.Child('equip/add').active = !this.data.equip?.id && this.data.isOwner()\n        const spr = this.hpBar.Child('equip/val', cc.Sprite)\n        if (this.data.equip?.id) {\n            resHelper.loadEquipIcon(this.data.equip.id, spr, this.key, this.data.equip.getSmeltCount())\n        } else {\n            spr.spriteFrame = null\n        }\n    }\n\n    private showOutline(v: boolean) {\n        let outline = this.getComponent(OutlineShaderCtrl)\n        if (!outline) {\n            outline = this.addComponent(OutlineShaderCtrl)\n            outline.setTarget(outline.FindChild('body/anim').Component(cc.Sprite))\n            outline.setOutlineSize(4)\n            outline.setColor(cc.Color.WHITE)\n        }\n        outline.setVisible(v)\n    }\n\n    // 被点击了\n    private onClick() {\n        if (this.data) {\n            audioMgr.playSFX('click')\n            viewHelper.showPnl('area/PawnInfo', this.data)\n            // this.showOutline(true)\n        }\n    }\n\n    // 设置是否可以点击\n    public setCanClick(val: boolean) {\n        if (this.touchCmpt) {\n            this.touchCmpt.interactable = val\n        }\n    }\n\n    // 移动位置\n    public movePoint(point: cc.Vec2) {\n        this.node.setPosition(this.getActPixelByPoint(point))\n    }\n\n    // 取消编辑\n    public cancel() {\n        this.node.setPosition(this.getActPixelByPoint(this.data.point))\n    }\n\n    // 确认编辑\n    public confirm() {\n        const point = this.getActPoint()\n        this.point.set(point)\n        this.prePoint.set(point)\n    }\n\n    update(dt: number) {\n        if (!this.data) {\n            return\n        }\n        this.updateZIndex()\n        this.updateAnger()\n        this.updateBuff()\n        this.updateShieldValue()\n        this.updateState()\n        this.updateCheckDie()\n    }\n\n    // 同步zindex\n    private updateZIndex() {\n        if (this.prePositionY !== this.node.y || (this.preActioning !== !!this.data?.actioning || this.preAnimName !== this.currAnimName)) {\n            this.prePositionY = this.node.y\n            this.preActioning = !!this.data?.actioning\n            this.preAnimName = this.currAnimName\n            const y = this.prePositionY - this.originY\n            let add = 0\n            if (this.data.getPawnType() === PawnType.NONCOMBAT) {\n                add = 3 //非战斗单位在最上层\n            } else if (this.preActioning) {\n                const state = this.data.getState()\n                add = state === PawnState.ATTACK || state >= PawnState.SKILL ? 2 : 1\n            } else if (this.preAnimName === 'die') {\n                return\n            } else {\n                add = !this.preAnimName || this.preAnimName === 'create' || this.preAnimName === 'stand_shield' || this.preAnimName === 'idle' || this.preAnimName === 'idle_pull' ? 0 : 1\n            }\n            const index = (AREA_MAX_ZINDEX - y) * 10 + add\n            if (index !== this.node.zIndex) {\n                this.tempIndex = this.node.zIndex = index\n            }\n            // cc.log('updateZIndex', this.data.id, this.data.uid, this.preActioning, this.preAnimName, add)\n        }\n    }\n\n    // 同步位置\n    private updatePosition(init?: boolean) {\n        if (init || !this.prePoint.equals(this.data.point)) {\n            this.prePoint.set(this.data.point)\n            this.node.setPosition(this.getActPixelByPoint(this.data.point))\n        }\n    }\n\n    private async updatePositionForMove(sp: cc.Vec2, ep: cc.Vec2) {\n        const data = this.data\n        let points = [sp, ep]\n        const area = gameHpr.areaCenter.getArea(data.index)\n        if (area) {\n            points = await gameHpr.getPawnASatr(data.uid).init((x: number, y: number) => area.checkIsBattleArea(x, y)).search(sp, ep)\n        }\n        const time = mapHelper.getMoveNeedTime(points, 400)\n        data.changeState(PawnState.EDIT_MOVE, { paths: points, needMoveTime: time })\n        await ut.wait(time * 0.001)\n        if (data.getState() < PawnState.STAND) {\n            data.changeState(PawnState.NONE)\n        }\n    }\n\n    // 刷新怒气\n    private updateAnger() {\n        if (this.preAnger !== this.data.curAnger) {\n            this.preAnger = this.data.curAnger\n            this.hpBar?.updateAnger(this.data.getAngerRatio())\n        }\n    }\n\n    // 刷新白盾\n    private updateShieldValue() {\n        if (this.preShieldValue !== this.curShieldValue) {\n            this.preShieldValue = this.curShieldValue\n            this.hpBar?.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp())\n        }\n    }\n\n    // 刷新buff效果\n    private updateBuff() {\n        this.curShieldValue = 0\n        let showBuffTypeMap = {}, mutualBuff = 0, standShield = false, feedIntensifyValue = 0\n        this.data.buffs.forEach(m => {\n            if (SHIELD_BUFF[m.type]) { //记录护盾值\n                this.curShieldValue += m.value\n            }\n            if (NEED_SHOW_BUFF[m.type]) {\n                showBuffTypeMap[m.type] = true\n            } else if (NEED_MUTUAL_BUFF[m.type]) {\n                mutualBuff = m.type //互斥buff\n            } else if (m.type === BuffType.STAND_SHIELD) { //立盾\n                standShield = true\n            } else if (m.type === BuffType.FEED_INTENSIFY) { //投喂强化\n                feedIntensifyValue = m.value\n            }\n        })\n        // 刷新外显buff\n        for (let k in NEED_SHOW_BUFF) {\n            const type = Number(k)\n            this.updateShowBuff(type, showBuffTypeMap[type])\n        }\n        // 刷新互斥buff\n        this.updateMutualBuff(mutualBuff)\n        // 刷新立盾\n        this.updateStandShield(standShield)\n        // 体型\n        this.updateAnimScale(1 + feedIntensifyValue * 0.02)\n    }\n\n    // 刷新立盾\n    private updateStandShield(val: boolean) {\n        if (!this.isShowStandShield && val) {\n            this.isShowStandShield = true\n            this.playAnimation('stand_shield')\n        } else if (this.isShowStandShield && !val) {\n            this.isShowStandShield = false\n            if (this.currAnimName !== 'shield_end') {\n                this.playAnimation('idle')\n            }\n        }\n    }\n\n    // 刷新外显buff\n    private updateShowBuff(type: BuffType, val: boolean) {\n        if (this.isLoadBuffMap[type]) {\n        } else if (!this.isShowBuffMap[type] && val) {\n            this.showBuff(type)\n            this.isShowBuffMap[type] = true\n        } else if (this.isShowBuffMap[type] && !val) {\n            this.isShowBuffMap[type] = false\n            this.putBuff(type)\n        }\n    }\n\n    // 刷新互斥buff效果 就是只会显示一个效果\n    private updateMutualBuff(type: BuffType) {\n        if (this.mutualBuffType === type) {\n            return\n        } else if (this.mutualBuffType) {\n            this.putBuff(this.mutualBuffType)\n            this.mutualBuffType = 0\n        }\n        if (type && !this.isLoadBuffMap[type]) {\n            this.mutualBuffType = type\n            this.showBuff(type)\n        }\n    }\n\n    // 显示一个buff\n    private async showBuff(type: BuffType) {\n        const showType: BuffType = BUFF_SHOW_TYPE_TRAN[type] || type\n        const name = 'BUFF_' + showType\n        let it = this.buffNodes.find(m => m.name === name)\n        if (it) {\n            this.isLoadBuffMap[showType] = false\n        } else if (!this.isLoadBuffMap) {\n            return wxHelper.errorAndFilter('showBuff', '!this.isLoadBuffMap')\n        } else if (this.isLoadBuffMap[showType]) {\n            return\n        } else {\n            this.isLoadBuffMap[showType] = true\n            it = await nodePoolMgr.get('buff/' + name, this.key)\n            if (!this.isValid || !this.data) {\n                return nodePoolMgr.put(it)\n            }\n            this.isLoadBuffMap[showType] = false\n            this.buffNodes.push(it)\n        }\n        it.opacity = 255\n        it.parent = this.node\n        it.zIndex = BUFF_NODE_ZINDEX[showType] || 10\n        const cmpt = it.Component(PawnAnimationCmpt).init(it.Child('body/anim', cc.Sprite), showType, this.key)\n        if (showType === BuffType.SHIELD\n            || showType === BuffType.PROTECTION_SHIELD\n            || showType === BuffType.RODELERO_SHIELD\n            || showType === BuffType.RODELERO_SHIELD_001\n            || showType === BuffType.RODELERO_SHIELD_102\n            || showType === BuffType.ABNEGATION_SHIELD\n            || showType === BuffType.POISONED_WINE\n        ) {\n            cmpt.play('trigger', () => cmpt.isValid && cmpt.play('stand'))\n        } else {\n            cmpt.play('stand')\n        }\n    }\n\n    private putBuff(type: BuffType) {\n        const showType = BUFF_SHOW_TYPE_TRAN[type] || type\n        const name = 'BUFF_' + showType\n        const node = this.buffNodes.remove('name', name)\n        if (!node) {\n        } else if (type === BuffType.SHIELD\n            || showType === BuffType.PROTECTION_SHIELD\n            || type === BuffType.RODELERO_SHIELD\n            || type === BuffType.RODELERO_SHIELD_001\n            || type === BuffType.RODELERO_SHIELD_102\n            || type === BuffType.ABNEGATION_SHIELD\n        ) {\n            node.Component(PawnAnimationCmpt).play('die', () => nodePoolMgr.put(node))\n        } else {\n            nodePoolMgr.put(node)\n        }\n    }\n\n    // 刷新体型\n    private updateAnimScale(val: number) {\n        if (this.animNode.scale !== val) {\n            this.animNode.scale = val\n            this.animNode.y = this.animNodeInitY + (this.animNodeInitY + 36) * (val - 1)\n        }\n    }\n\n    // 检测是否死亡\n    private updateCheckDie() {\n        if (!this.isDie && this.data?.isDie()) {\n            this.isDie = true\n            const name = this.animCmpt?.playAnimName\n            if (name !== 'hit' && name !== 'die' && name !== 'hit_pull' && name !== 'die_pull') {\n                this.playAnimation('die', () => eventCenter.emit(EventType.REMOVE_PAWN, this.data.aIndex, this.data.uid))\n            }\n        }\n    }\n\n    // 同步状态信息\n    private updateState() {\n        if (gameHpr.playback.isSimulating) {\n            return\n        } else if (!this.data?.state || this.preStateUid === this.data.state.uid || this.isDie) {\n            return\n        }\n        this.preStateUid = this.data.state.uid\n        this.node.stopAllActions()\n        this.unscheduleAllCallbacks()\n        const state = this.data.state.type, data = this.data.state.data\n        // cc.log('updateState', this.uid, this.point.ID(), PawnState[state])\n        // this.data.actioning = this.data.actioning || (state !== PawnState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动\n        if (state === PawnState.STAND) { //待机\n            this.doStand()\n        } else if (state === PawnState.MOVE || state === PawnState.EDIT_MOVE) { //移动\n            this.doMove(data)\n        } else if (state === PawnState.ATTACK) { //攻击\n            this.doAttack(data)\n        } else if (state === PawnState.HIT) { //受击\n            this.doHit(data)\n        } else if (state === PawnState.DIAUP) { //击飞\n            this.doDiaup(data)\n        } else if (state === PawnState.HEAL) { //回血\n            this.doHeal(data)\n        } else if (state === PawnState.DEDUCT_HP) { //掉血\n            this.doDeductHp(data)\n        } else if (state === PawnState.ADD_ANGER) { //加怒气\n            this.doAddAnger(data)\n        } else if (state === PawnState.FEAR) { //恐惧\n            this.doFear(data)\n        } else if (state === PawnState.DIE) { //直接死亡\n            this.doDie(data)\n        } else if (state >= PawnState.SKILL && state <= PawnState.SKILL_MAX) { //技能\n            this.doSkill(data)\n        } else {\n            this.playAnimation('idle')\n        }\n        // 通知聚焦\n        // if (state === PawnState.MOVE || state === PawnState.ATTACK || (state >= PawnState.SKILL && state <= PawnState.SKILL_MAX)) {\n        //     eventCenter.emit(EventType.FOCUS_PAWN, { index: this.data.aIndex, uid: this.data.uid, point: this.data.point })\n        // }\n    }\n\n    // 待机\n    private doStand() {\n        const animName = this.animCmpt?.playAnimName\n        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle\n            this.playAnimation('idle')\n        }\n        this.hpBar?.initInfo()\n        this.updatePosition()\n    }\n\n    // 移动\n    private doMove(data: any) {\n        this.updatePosition()\n        const paths: cc.Vec2[] = (data.paths || []).map(m => this.getActPixelByPoint(cc.v2(m)).clone())\n        const currMoveTime = data.currMoveTime ?? 0\n        const needMoveTime = data.needMoveTime ?? 0\n        // 计算各个距离信息\n        let sumDis = 0, arr = []\n        for (let i = 1, l = paths.length; i < l; i++) {\n            const curr = paths[i], prep = paths[i - 1], speed = curr.sub(prep, this._temp_vec2_3)\n            const dis = speed.mag()\n            sumDis += dis\n            arr.push({ dis: dis, progress: sumDis, speed: speed.normalize(), prep: prep, pos: curr })\n        }\n        let ratio = currMoveTime / needMoveTime\n        let startPos: cc.Vec2 = null, list = []\n        for (let i = 0, l = arr.length; i < l; i++) {\n            const m = arr[i], pr = m.progress / sumDis\n            if (ratio > pr) {\n                continue\n            } else if (!startPos) { //找出起点\n                const dr = m.dis / sumDis\n                const r = Math.max(ratio - (pr - dr), 0)\n                const d = sumDis * r //超出的一段距离\n                startPos = m.speed.mul(d).addSelf(m.prep)\n                m.dis -= d //减去已经走过的路\n            }\n            list.push({ time: m.dis / sumDis * needMoveTime, endPos: m.pos })\n        }\n        if (!startPos) {\n            return\n        }\n        // 开始移动\n        this.playAnimation('move')\n        this.node.setPosition(startPos)\n        this.animCmpt.resetMove()\n        this.runMove(list)\n    }\n    private runMove(list: any[]) {\n        if (!this.isValid) {\n        } else if (list.length > 0) {\n            const d = list.shift(), pos = d.endPos\n            this.setDir(pos.x - this.node.x)\n            this.animCmpt.moveNodeOne(0, d.time, this.getPosition(), pos, () => this.runMove(list))\n        } else if (this.data.getState() === PawnState.MOVE || this.data.getState() === PawnState.EDIT_MOVE) {\n            this.playAnimation('idle')\n        }\n    }\n\n    // 攻击\n    private doAttack(data: any) {\n        const currAttackTime = data.currAttackTime ?? 0\n        const targetPoint = data.targetPoint || this.point\n        const suffix = data.instabilityAttackIndex || ''\n        this.updatePosition()\n        this.setDir(targetPoint.x - this.point.x)\n        this.playSFXByKey('attack_sound', suffix)\n        this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime)\n    }\n\n    // 技能\n    private doSkill(data: any) {\n        const currAttackTime = data.currAttackTime ?? 0\n        const targetPoint = data.targetPoint || this.point\n        const skill: PawnSkillObj = data.skill, heroSkill = this.data.getPortrayalSkill()\n        // 位移\n        if (!this.prePoint.equals(this.data.point) && (skill?.type === PawnSkillType.SKILL_208\n            || skill?.type === PawnSkillType.SKILL_212\n            || skill?.type === PawnSkillType.SKILL_213\n            || skill?.type === PawnSkillType.SKILL_219\n            || skill?.type === PawnSkillType.SKILL_306\n            || heroSkill?.id === HeroType.QIN_QIONG\n        )) {\n            this.prePoint.set(this.data.point)\n            const pos = this.getActPixelByPoint(this.data.point)\n            this.setDir(pos.x - this.node.x)\n            let params = skill.params\n            if (!heroSkill) {\n            } else if (heroSkill.id === HeroType.ZHANG_FEI\n                || heroSkill.id === HeroType.XU_CHU\n                || heroSkill.id === HeroType.PEI_XINGYAN\n                || heroSkill?.id === HeroType.QIN_QIONG\n                || heroSkill?.id === HeroType.GAO_SHUN\n                || heroSkill?.id === HeroType.HUO_QUBING\n                || heroSkill?.id === HeroType.DIAN_WEI\n            ) {\n                params = heroSkill.params //张飞 高顺 许褚 裴行俨 秦琼\n            } else if (this.data.skinId === 3404103) {\n                params = '0.36,0.6' //重骑冬季皮肤\n            }\n            let [delay, time] = ut.stringToNumbers(params, ',')\n            this.animCmpt.resetMove().setMoveDelay((delay ?? 0) * 1000).moveNodeOne(0, (time ?? 0.1) * 1000, this.getPosition(), pos, () => this.setDir(targetPoint.x - this.point.x))\n        } else {\n            this.updatePosition()\n            this.setDir(targetPoint.x - this.point.x)\n        }\n        if (data.sound !== undefined) {\n            this.playSFX(data.sound)\n        } else {\n            this.playSFXByKey('skill_sound')\n        }\n        const isStandShield = skill?.type === PawnSkillType.SKILL_205 //立盾\n        const isSpearthrowing = heroSkill?.id === HeroType.QIN_LIANGYU && !data.skillName\n        this.playAnimation(data.skillName || 'skill', () => {\n            if (!this.isValid) {\n            } else if (isStandShield) {\n                this.playAnimation('stand_shield')\n            } else if (isSpearthrowing) {\n                this.playAnimation('idle_barb')\n            } else {\n                this.playAnimation('idle')\n            }\n        }, currAttackTime)\n        // 播放粉碎大地 地面效果\n        if (skill?.type === PawnSkillType.SKILL_215) {\n            let type: number = skill.type, delay = 0.9\n            // 黄盖特殊处理下\n            if (this.data.portrayal?.skill?.id === HeroType.HUANG_GAI) {\n                type = 215001\n                delay = 0.6\n            }\n            eventCenter.emit(EventType.PLAY_BATTLE_EFFECT, {\n                type, delay,\n                index: this.data.aIndex,\n                point: this.data.point,\n            })\n        }\n        // 秦良玉 收矛\n        if (data.skillName === 'recycle_spear') {\n            eventCenter.emit(EventType.PLAY_BULLET_FLY, {\n                bulletId: 5022,\n                currTime: currAttackTime,\n                needTime: 1000,\n                index: this.data.aIndex,\n                startPoint: targetPoint,\n                targetPoint: this.data.point,\n            })\n        }\n    }\n\n    // 受击\n    private doHit(data: any) {\n        const index = this.data.aIndex\n        let damage = data.damage ?? 0\n        const trueDamage = data.trueDamage ?? 0\n        const isCrit = !!data.isCrit //暴击\n        const heal = data.heal ?? 0 //回复\n        const isDodge = damage === -1 //闪避\n        const isParry = damage === -2 //格挡\n        const isTurntheblade = damage === -3 //招架\n        const isWithstand = damage === -4 //抵挡\n        damage = isDodge || isParry || isTurntheblade || isWithstand ? 0 : damage\n        const attackPoint = data.attackPoint || this.point\n        const isDie = this.isDie = !!data.isDie\n        const time = data.time ?? 0 //经过的时间\n        const sound = data.sound //受击音效\n        const uid = this.uid\n        const isDiaup = this.isDiaup\n        this.isDiaup = false\n        this.setDir(attackPoint.x - this.point.x)\n        this.hpBar?.play()\n        if (damage + trueDamage === 0) {\n            return this.playAnimation('idle')\n        } else if (isDie) {\n            if (this.data.getPawnType() !== PawnType.NONCOMBAT) {\n                this.node.zIndex = 0\n            }\n            this.putAllBuff()\n        } else if (isDiaup) { //如果没有死亡且上一个动作是击飞\n            return this.playAnimation('idle')\n        }\n        let animName = 'hit'\n        if (isDie) {\n            animName = 'die'\n            this.playSFXByKey('die_sound')\n        } else if (sound) {\n            this.playSFX(sound)\n        }\n        this.playAnimation(animName, () => {\n            if (isDie) {\n                eventCenter.emit(EventType.REMOVE_PAWN, index, uid)\n            } else if (this.isValid) {\n                this.playAnimation('idle')\n            }\n        })\n    }\n\n    // 直接死亡\n    private doDie(data: any) {\n        const index = this.data.aIndex\n        const uid = this.uid\n        if (!this.data.isNoncombat()) {\n            this.node.zIndex = 0\n        }\n        this.putAllBuff()\n        this.playSFX(this.data.baseJson?.die_sound)\n        this.playAnimation('die', () => eventCenter.emit(EventType.REMOVE_PAWN, index, uid))\n    }\n\n    // 击飞\n    private doDiaup(data: any) {\n        const time = data.time ?? 0 //经过的时间\n        const attackPoint = data.attackPoint || this.prePoint\n        this.setDir(attackPoint.x - this.point.x)\n        if (time > 0) {\n            this.prePoint.set(this.data.point)\n            this.playSFX('sound_037_1')\n            this.playAnimation('diaup')\n            this.isDiaup = true\n            const pos = this.getActPixelByPoint(this.data.point)\n            this.animCmpt.resetMove().setMoveParabolaHeight(data.parabolaHeight ?? 20).moveNodeOne(0, time, this.getPosition(), pos, () => {\n                if (this.isValid && !this.isDie) {\n                    this.playAnimation('idle')\n                }\n            })\n        } else {\n            this.playAnimation('idle')\n            this.updatePosition()\n        }\n    }\n\n    // 恐惧\n    private doFear(data: any) {\n        const time = data.time ?? 0 //经过的时间\n        if (time > 0 && !this.prePoint.equals(this.data.point)) {\n            this.setDir(this.point.x - this.prePoint.x)\n            this.prePoint.set(this.data.point)\n            const pos = this.getActPixelByPoint(this.data.point)\n            this.playAnimation('move', null, 0, 0.5)\n            this.animCmpt.resetMove().moveNodeOne(0, time, this.getPosition(), pos, () => {\n                if (this.isValid && !this.isDie) {\n                    this.playAnimation('idle')\n                }\n            })\n        } else {\n            this.playAnimation('idle')\n            this.updatePosition()\n        }\n    }\n\n    // 回血\n    private doHeal(data: any) {\n        const index = this.data.aIndex\n        const val = data.val ?? 0\n        const time = data.time ?? 0\n        const uid = this.uid\n        this.hpBar?.play()\n        if (this.preShieldValue > 0) { //这里主动刷新一下护盾\n            this.hpBar?.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp())\n        }\n    }\n\n    // 掉血\n    private doDeductHp(data: any) {\n        const index = this.data.aIndex\n        const damage = data.damage ?? 0\n        const trueDamage = data.trueDamage ?? 0\n        const time = data.time ?? 0\n        const isDie = this.isDie = !!data.isDie\n        const uid = this.uid\n        if (isDie) {\n            this.hpBar?.setActive(false)\n            this.node.zIndex = 0\n            this.putAllBuff()\n            this.playAnimation('die', () => eventCenter.emit(EventType.REMOVE_PAWN, index, uid))\n        } else {\n            this.hpBar?.play()\n        }\n    }\n\n    // 添加怒气\n    private doAddAnger(data: any) {\n        this.updateAnger()\n        // this.doStand()\n    }\n}"]}