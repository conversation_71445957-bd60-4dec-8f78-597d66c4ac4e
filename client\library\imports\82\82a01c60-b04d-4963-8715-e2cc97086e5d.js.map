{"version": 3, "sources": ["assets\\app\\script\\model\\area\\ArmyObj.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAuD;AACvD,6DAAwD;AACxD,qCAA+B;AAE/B,UAAU;AACV;IAAA;QAEW,WAAM,GAAW,CAAC,CAAA,CAAC,QAAQ;QAC3B,aAAQ,GAAW,CAAC,CAAC,CAAA,CAAC,MAAM;QAC5B,QAAG,GAAW,EAAE,CAAA;QAChB,SAAI,GAAW,EAAE,CAAA;QACjB,UAAK,GAAW,EAAE,CAAA,CAAC,KAAK;QACxB,UAAK,GAAc,EAAE,CAAA,CAAC,MAAM;QAC5B,eAAU,GAAa,EAAE,CAAA,CAAC,UAAU;QACpC,eAAU,GAAW,CAAC,CAAA,CAAC,MAAM;QAC7B,sBAAiB,GAAW,CAAC,CAAA,CAAC,QAAQ;QACtC,UAAK,GAAc,iBAAS,CAAC,IAAI,CAAA;QACjC,gBAAW,GAAqB,EAAE,CAAA,CAAC,SAAS;IAqMvD,CAAC;IAnMU,sBAAI,GAAX,UAAY,KAAa,EAAE,KAAa,EAAE,IAAY;QAClD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,EAAE,CAAC,GAAG,EAAE,CAAA;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QACrB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,iBAAS,CAAC,IAAI,CAAA;QAC3B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAA;QAC3B,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,yBAAO,GAAd,UAAe,IAAS;;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,QAAQ,SAAG,IAAI,CAAC,QAAQ,mCAAI,CAAC,CAAC,CAAA;QACnC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE,CAAA;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAA;QAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAA;QAC7B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,CAAA;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,iBAAS,CAAC,IAAI,CAAA;QACzC,IAAI,CAAC,iBAAiB,eAAG,IAAI,CAAC,sBAAsB,EAAE,0CAAE,UAAU,mCAAI,CAAC,CAAA;QACvE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAA;QAC3D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAA;QACzC,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,uBAAK,GAAZ;QACI,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;SAChC,CAAA;IACL,CAAC;IAEM,6BAAW,GAAlB;QACI,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC;gBACnB,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA;YAC5E,CAAC,CAAC;SACL,CAAA;IACL,CAAC;IAED,sBAAW,0BAAK;aAAhB,cAAqB,OAAO,IAAI,CAAC,MAAM,CAAA,CAAC,CAAC;aACzC,UAAiB,GAAW,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAA,CAAC,CAAC;;;OADV;IAGzC,UAAU;IACH,yBAAO,GAAd;QACI,OAAO,IAAI,CAAC,KAAK,KAAK,oBAAO,CAAC,MAAM,EAAE,CAAA;IAC1C,CAAC;IAED,QAAQ;IACD,6BAAW,GAAlB;QACI,OAAO,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,CAAA;IACzC,CAAC;IAED,SAAS;IACF,0BAAQ,GAAf;QACI,OAAO,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,MAAM,CAAA;IAC1C,CAAC;IAED,QAAQ;IACD,2BAAS,GAAhB;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,EAAE,EAAV,CAAU,CAAC,CAAA;IAC3C,CAAC;IAEO,6BAAW,GAAnB,UAAoB,KAAY;QAAhC,iBAWC;QAVG,SAAS;QACT,IAAM,MAAM,GAAG,EAAE,CAAA;QACjB,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAApB,CAAoB,CAAC,CAAA;QACxC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;aAC1B;SACJ;QACD,QAAQ;QACR,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAA;IACvC,CAAC;IAED,OAAO;IACA,4BAAU,GAAjB,UAAkB,GAAW;QACzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IACjC,CAAC;IAED,OAAO;IACA,yBAAO,GAAd,UAAe,IAAS;;QACpB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;QACnD,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;SACtD;aAAM,IAAI,IAAI,CAAC,GAAG,YAAK,oBAAO,CAAC,cAAc,0CAAE,GAAG,CAAA,EAAE;YACjD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC/F;aAAM;YACH,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,iBAAO,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SACtF;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC7B,OAAO,IAAI,CAAA;IACf,CAAC;IAED,WAAW;IACJ,iCAAe,GAAtB;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;IAC/E,CAAC;IAED,YAAY;IACL,iCAAe,GAAtB;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,KAAK,EAAE,EAAV,CAAU,CAAC,CAAC,MAAM,CAAA;IACpD,CAAC;IAED,WAAW;IACJ,gCAAc,GAArB;QACI,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,CAAA;IAC3D,CAAC;IAED,WAAW;IACJ,+BAAa,GAApB;QACI,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,CAAA;IAC3D,CAAC;IAED,aAAa;IACN,yCAAuB,GAA9B;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,GAAG,IAAK,OAAA,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAA1B,CAA0B,EAAE,CAAC,CAAC,CAAA;IACzE,CAAC;IAED,aAAa;IACN,qCAAmB,GAA1B;QACI,IAAM,GAAG,GAAmB,EAAE,CAAA;QAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,EAAxB,CAAwB,CAAC,CAAA;QACjD,OAAO,GAAG,CAAA;IACd,CAAC;IACD,sBAAW,8BAAS;aAApB,cAAyB,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAA,CAAC,CAAC;;;OAAA;IAE5D,SAAS;IACF,+BAAa,GAApB,UAAqB,KAAY;QAC7B,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAM;SACT;QACD,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAApB,CAAoB,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YACxB,IAAI,KAAK,EAAE;gBACP,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;aACpB;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,qIAAqI;IAErI,aAAa;IACN,wCAAsB,GAA7B;QACI,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,GAAY,IAAI,CAAA;QACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,UAAU,IAAI,KAAK,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;gBACjF,KAAK,GAAG,CAAC,CAAC,UAAU,CAAA;gBACpB,IAAI,GAAG,CAAC,CAAA;aACX;QACL,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAChC,CAAC;IAED,OAAO;IACA,gCAAc,GAArB;QACI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAjB,CAAiB,CAAC,CAAA;IAC9C,CAAC;IAED,SAAS;IACF,+BAAa,GAApB,UAAqB,MAAiB;QAClC,IAAM,GAAG,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,CAAA;QAC1B,IAAI,CAAC,GAAG,EAAE;YACN,OAAM;SACT;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,CAAC,GAAG,GAAG,EAAE;gBACT,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;aACpC;iBAAM;gBACH,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;aACpC;SACJ;IACL,CAAC;IAED,SAAS;IACF,6BAAW,GAAlB;QACI,OAAO,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,oBAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,EAA1C,CAA0C,CAAC,CAAA;IACtK,CAAC;IAEM,qCAAmB,GAA1B,UAA2B,KAAU;QACjC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAxC,CAAwC,CAAC,CAAA;IACrE,CAAC;IACL,cAAC;AAAD,CAjNA,AAiNC,IAAA", "file": "", "sourceRoot": "/", "sourcesContent": ["import { InjuryPawnInfo, TreasureInfo } from \"../../common/constant/DataType\"\nimport { ArmyState } from \"../../common/constant/Enums\"\nimport { gameHpr } from \"../../common/helper/GameHelper\"\nimport PawnObj from \"./PawnObj\"\n\n// 战场里面的军队\nexport default class ArmyObj {\n\n    public aIndex: number = 0 //所属哪个区域\n    public enterDir: number = -1 //进入方向\n    public uid: string = ''\n    public name: string = ''\n    public owner: string = '' //拥有者\n    public pawns: PawnObj[] = [] //士兵列表\n    public drillPawns: number[] = [] //训练中的士兵列表\n    public marchSpeed: number = 0 //行军速度\n    public defaultMarchSpeed: number = 0 //默认行军速度\n    public state: ArmyState = ArmyState.NONE\n    public curingPawns: InjuryPawnInfo[] = [] // 治疗中的士兵\n\n    public init(index: number, owner: string, name: string) {\n        this.aIndex = index\n        this.uid = 'temp_' + ut.UID()\n        this.owner = owner\n        this.name = name\n        this.pawns.length = 0\n        this.drillPawns.length = 0\n        this.state = ArmyState.NONE\n        this.curingPawns.length = 0\n        return this\n    }\n\n    public fromSvr(data: any) {\n        this.aIndex = data.index\n        this.enterDir = data.enterDir ?? -1\n        this.uid = data.uid || ''\n        this.name = data.name || ''\n        this.owner = data.owner || ''\n        this.updatePawns(data.pawns || [])\n        this.drillPawns = data.drillPawns || []\n        this.state = data.state || ArmyState.NONE\n        this.defaultMarchSpeed = this.getPawnByMinMarchSpeed()?.marchSpeed ?? 0\n        this.marchSpeed = data.marchSpeed || this.defaultMarchSpeed\n        this.curingPawns = data.curingPawns || []\n        return this\n    }\n\n    public strip() {\n        return {\n            index: this.aIndex,\n            uid: this.uid,\n            name: this.name,\n            owner: this.owner,\n            pawns: this.pawns.map(p => p.strip()),\n            drillPawns: this.drillPawns,\n            state: this.state,\n            enterDir: this.enterDir,\n            marchSpeed: this.marchSpeed,\n            curingPawns: this.curingPawns,\n        }\n    }\n\n    public toPawnsByHP() {\n        return {\n            pawns: this.pawns.map(p => {\n                return { uid: p.uid, curHp: Math.min(p.curHp, p.maxHp), maxHp: p.maxHp }\n            })\n        }\n    }\n\n    public get index() { return this.aIndex }\n    public set index(val: number) { this.aIndex = val }\n\n    // 是否自己的军队\n    public isOwner() {\n        return this.owner === gameHpr.getUid()\n    }\n\n    // 是否战斗中\n    public isBattleing() {\n        return this.state === ArmyState.FIGHT\n    }\n\n    // 是否在治疗中\n    public isCuring() {\n        return this.state === ArmyState.CURING\n    }\n\n    // 是否有英雄\n    public isHasHero() {\n        return this.pawns.some(m => m.isHero())\n    }\n\n    private updatePawns(pawns: any[]) {\n        // 先删除没有的\n        const uidMap = {}\n        pawns.forEach(m => uidMap[m.uid] = true)\n        for (let i = this.pawns.length - 1; i >= 0; i--) {\n            if (!uidMap[this.pawns[i].uid]) {\n                this.pawns.splice(i, 1)\n            }\n        }\n        // 一个个添加\n        pawns.forEach(m => this.addPawn(m))\n    }\n\n    // 删除士兵\n    public removePawn(uid: string) {\n        this.pawns.remove('uid', uid)\n    }\n\n    // 添加士兵\n    public addPawn(data: any) {\n        let pawn = this.pawns.find(m => m.uid === data.uid)\n        if (pawn) {\n            pawn.fromSvr(data, this.uid, this.owner, this.name)\n        } else if (data.uid === gameHpr.uiShowPawnData?.uid) {\n            pawn = this.pawns.add(gameHpr.uiShowPawnData.fromSvr(data, this.uid, this.owner, this.name))\n        } else {\n            pawn = this.pawns.add(new PawnObj().fromSvr(data, this.uid, this.owner, this.name))\n        }\n        pawn.enterDir = this.enterDir\n        return pawn\n    }\n\n    // 获取实际士兵个数\n    public getActPawnCount() {\n        return this.pawns.length + this.drillPawns.length + this.curingPawns.length\n    }\n\n    // 获取士兵的实际数量\n    public getPawnActCount() {\n        return this.pawns.filter(m => !m.isDie()).length\n    }\n\n    // 是否可以训练士兵\n    public isCanDrillPawn() {\n        return this.isOwner() && this.state !== ArmyState.MARCH\n    }\n\n    // 是否可以治疗士兵\n    public isCanCurePawn() {\n        return this.isOwner() && this.state !== ArmyState.MARCH\n    }\n\n    // 获取所有士兵宝箱数量\n    public getAllPawnTreasureCount() {\n        return this.pawns.reduce((val, cur) => cur.treasures.length + val, 0)\n    }\n\n    // 获取所有士兵宝箱列表\n    public getAllPawnTreasures() {\n        const arr: TreasureInfo[] = []\n        this.pawns.forEach(m => arr.pushArr(m.treasures))\n        return arr\n    }\n    public get treasures() { return this.getAllPawnTreasures() }\n\n    // 设置士兵位置\n    public setPawnsPoint(pawns: any[]) {\n        if (!pawns || pawns.length === 0) {\n            return\n        }\n        const obj = {}\n        pawns.forEach(m => obj[m.uid] = m.point)\n        this.pawns.forEach(m => {\n            const point = obj[m.uid]\n            if (point) {\n                m.setPoint(point)\n            }\n        })\n    }\n\n    /////////////////////////////////////////////////////////// 新手村相关 ///////////////////////////////////////////////////////////////////\n\n    // 获取行军速度最少士兵\n    public getPawnByMinMarchSpeed() {\n        let speed = -1, pawn: PawnObj = null\n        this.pawns.forEach(m => {\n            if (speed === -1 || m.marchSpeed < speed || (m.marchSpeed == speed && m.skinId > 0)) {\n                speed = m.marchSpeed\n                pawn = m\n            }\n        })\n        return pawn || this.pawns[0]\n    }\n\n    // 回复血量\n    public recoverAllPawn() {\n        this.pawns.forEach(m => m.curHp = m.maxHp)\n    }\n\n    // 设置士兵位置\n    public setPawnPoints(points: cc.Vec2[]) {\n        const len = points?.length\n        if (!len) {\n            return\n        }\n        for (let i = 0, l = this.pawns.length; i < l; i++) {\n            if (i < len) {\n                this.pawns[i].setPoint(points[i])\n            } else {\n                this.pawns[i].setPoint(points[0])\n            }\n        }\n    }\n\n    // 是否可以战斗\n    public isCanBattle() {\n        return this.state === ArmyState.NONE && this.drillPawns.length === 0 && this.pawns.length > 0 && !this.pawns.some(m => gameHpr.noviceServer.checkPawnLving(m.uid))\n    }\n\n    public updatePawnEquipAttr(equip: any) {\n        this.pawns.forEach(m => m.updateEquipAttr(equip.id, equip.attrs))\n    }\n}"]}