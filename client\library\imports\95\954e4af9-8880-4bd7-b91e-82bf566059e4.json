[{"__type__": "cc.Prefab", "_name": "AreaArmyPnl", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "AreaArmyPnl", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}], "_active": true, "_components": [{"__id__": 291}], "_prefab": {"__id__": 292}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1520}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [360, 760, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "_name": "close", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "2e0619b1-2abd-4484-b871-fb3e27117e08"}, "fileId": "6cOdl6wARPBLlKqVeWR9ev", "sync": true}, {"__type__": "cc.Node", "_name": "root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 5}, {"__id__": 20}, {"__id__": 27}, {"__id__": 54}, {"__id__": 206}], "_active": true, "_components": [{"__id__": 289}], "_prefab": {"__id__": 290}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 676, "height": 920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "di", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 6}, {"__id__": 10}, {"__id__": 14}], "_active": true, "_components": [{"__id__": 18}], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 676, "height": 920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "0", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 7}, {"__id__": 8}], "_prefab": {"__id__": 9}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 676, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 434, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "56faf42b-39d4-43fc-9406-7a1180bdf27f"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": -27, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "eb6eb232-ec74-403c-9e26-8d6eee3b7357"}, "fileId": "2a+b3KzvdBwKSJTVRxVU7j", "sync": false}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80, 518.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9e892d75-5ebf-4bb1-bab5-8d8138291aeb"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 242.5, "_top": -117, "_bottom": 900.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 117, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "eb6eb232-ec74-403c-9e26-8d6eee3b7357"}, "fileId": "5dG8flNAlNHbp2/5hrQFIi", "sync": false}, {"__type__": "cc.Node", "_name": "2", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 676, "height": 920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "85b1f3cf-aaa7-4761-8430-8efdcc904747"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 63, "_originalHeight": 63, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "eb6eb232-ec74-403c-9e26-8d6eee3b7357"}, "fileId": "24KrhEH7hCka3/LWgqZOal", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "eb6eb232-ec74-403c-9e26-8d6eee3b7357"}, "fileId": "fdSSqPJ+9NoYIr5E31zGhu", "sync": false}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 21}], "_active": true, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 430, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 23}], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 63, "g": 51, "b": 47, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 32, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "key": "ui.title_area_army", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5EU+y3Z1MJYOxki/6sTtJ", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 318, "_right": 318, "_top": 0, "_bottom": 792, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 52, "_originalHeight": 56, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cMS0656NPlZSRjVBi/6+5", "sync": false}, {"__type__": "cc.Node", "_name": "info", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 28}, {"__id__": 37}, {"__id__": 48}], "_active": true, "_components": [{"__id__": 51}, {"__id__": 52}], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 636, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-318, 372, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [{"__id__": 29}], "_active": true, "_components": [{"__id__": 33}, {"__id__": 34}, {"__id__": 35}], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "tab_l", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 86, "g": 76, "b": 73, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 24, "_lineHeight": 28, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "key": "", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ad4s2TGj1AJoxj9JrqRBvF", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ae17756e-5eb7-4f7a-ae97-33a85fab71a0"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 48, "height": 48}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 20, "_N$paddingRight": 28, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1cuCBpq75MfJ9LSnwY9FXt", "sync": false}, {"__type__": "cc.Node", "_name": "show_pawn_equip_te_t", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [{"__id__": 38}, {"__id__": 41}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 45}, {"__id__": 46}], "_prefab": {"__id__": 47}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [612, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 37}, "_children": [], "_active": true, "_components": [{"__id__": 39}], "_prefab": {"__id__": 40}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "efe20822-c4bc-4eeb-b970-1e87694e33aa"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5189QH45Ii4tU9u1yFUg7", "sync": false}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "_parent": {"__id__": 37}, "_children": [], "_active": false, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7e0a460b-d9ed-4942-96c2-2fdeeba20cab"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98m5GLtn1N4ahZQiQaAmkJ", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "415672e8-2b53-43c1-9b35-de992af2902e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 38}, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 42}, "checkEvents": [], "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 32, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68aJKhf9JGUqdPOFfDKJiJ", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 49}], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 228, "g": 212, "b": 191, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 636, "height": 6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "be819611-97c0-4f1a-bd2f-519a6ff68608"}, "_type": 2, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7YZoEOGJKuovlCCtSw49X", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 20, "_right": 20, "_top": 64, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 96, "_originalHeight": 0, "_id": ""}, {"__type__": "023dcrXH55EkqAxhaUbc405", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "line": {"__id__": 48}, "nodes": [{"__id__": 28}, {"__id__": 37}], "interval": 12, "isLeft": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13FlXKqbVKlZ3qCe5FNaLN", "sync": false}, {"__type__": "cc.Node", "_name": "list_sv", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 55}, {"__id__": 199}], "_active": true, "_components": [{"__id__": 203}, {"__id__": 204}], "_prefab": {"__id__": 205}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 668, "height": 676}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [{"__id__": 56}], "_active": true, "_components": [{"__id__": 196}, {"__id__": 197}], "_prefab": {"__id__": 198}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 668, "height": 676}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 338, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [{"__id__": 57}], "_active": true, "_components": [{"__id__": 194}], "_prefab": {"__id__": 195}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 636, "height": 168}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "item", "_objFlags": 0, "_parent": {"__id__": 56}, "_children": [{"__id__": 58}, {"__id__": 105}, {"__id__": 135}], "_active": true, "_components": [{"__id__": 191}, {"__id__": 192}], "_prefab": {"__id__": 193}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 233, "g": 221, "b": 199, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 636, "height": 168}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "top", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 59}, {"__id__": 84}, {"__id__": 93}], "_active": true, "_components": [], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 636, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "name", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [{"__id__": 60}, {"__id__": 65}, {"__id__": 71}], "_active": true, "_components": [{"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-306, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [], "_active": true, "_components": [{"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 86, "g": 76, "b": 73, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.5]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "bedffkn/YlJ56p0Ln9TCNta", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "retinaScale": 2, "_id": ""}, {"__type__": "c682cTjk+FOq5M2BV16gBn/", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "maxWidth": 172, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fLXJJEQZHa5DQuCV5ddYF", "sync": false}, {"__type__": "cc.Node", "_name": "edit", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [{"__id__": 66}], "_active": true, "_components": [{"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [28, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "edit_name_be", "_objFlags": 0, "_parent": {"__id__": 65}, "_children": [], "_active": true, "_components": [{"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 65}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2PDCA9CNKDJSQup0xQyk2", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b0de9e0b-1276-402e-b655-3669b548c119"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4z4eslmFA6JldDZ1o9sO1", "sync": false}, {"__type__": "cc.Node", "_name": "other", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [{"__id__": 72}, {"__id__": 75}], "_active": true, "_components": [{"__id__": 80}], "_prefab": {"__id__": 81}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [56, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [{"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [14, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72zqSAfARKt4gNo4wVFfZI", "sync": false}, {"__type__": "cc.Node", "_name": "name", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [{"__id__": 76}, {"__id__": 77}, {"__id__": 78}], "_prefab": {"__id__": 79}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 0.5]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 22, "_lineHeight": 24, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "bedffkn/YlJ56p0Ln9TCNta", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "retinaScale": 2, "_id": ""}, {"__type__": "c682cTjk+FOq5M2BV16gBn/", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "maxWidth": 160, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5JfW5khZMwZSnF6a8RCLQ", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 4, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8AQoVabNEJJ7VLTJy4pzh", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 88, "height": 40}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 12, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9e5L2d5sZNqocm59gfPzS6", "sync": false}, {"__type__": "cc.Node", "_name": "march_speed_be", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [{"__id__": 85}], "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}, {"__id__": 90}, {"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [306, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [], "_active": true, "_components": [{"__id__": 86}], "_prefab": {"__id__": 87}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -14, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6sV1d/hFAxZd28VAN+Gq7", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 22, "_lineHeight": 24, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "key": "", "fontName": "", "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 84}, "_id": ""}, {"__type__": "356cc80BMlDxpmbLcfx1E6c", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "duration": 0.1, "positionOffset": {"__type__": "cc.Vec2", "x": 4, "y": 0}, "angleOffset": 0, "sound": 1, "soundPath": "", "soundVolume": 1, "event": 0, "eventName": "", "eventParams": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d11iYaAiRKqI0rY+2yPfTw", "sync": false}, {"__type__": "cc.Node", "_name": "alli", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [{"__id__": 94}, {"__id__": 97}], "_active": true, "_components": [{"__id__": 102}], "_prefab": {"__id__": 103}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [306, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 95}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-16, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bailDM31BJbL22KbnokO76", "sync": false}, {"__type__": "cc.Node", "_name": "name", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 98}, {"__id__": 99}, {"__id__": 100}], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 182, "g": 165, "b": 145, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 22, "_lineHeight": 24, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "bedffkn/YlJ56p0Ln9TCNta", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "retinaScale": 2, "_id": ""}, {"__type__": "c682cTjk+FOq5M2BV16gBn/", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "maxWidth": 160, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e25ulh0gZBH6gf5rMWj+3O", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 28, "height": 32}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 4, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01IWa+p29LcY9T7H+T0SdQ", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eDlq7avdKl7tG8Hw42QbV", "sync": false}, {"__type__": "cc.Node", "_name": "pawns", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 106}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 133}], "_prefab": {"__id__": 134}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 604, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-302, -84, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "pawn_be", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 107}, {"__id__": 110}, {"__id__": 113}, {"__id__": 124}], "_active": true, "_components": [{"__id__": 130}], "_prefab": {"__id__": 131}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [30, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [], "_active": true, "_components": [{"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -27, 0, 0, 0, 0, 1, 0.84, 0.84, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f46kHcSYdNpJNMZxmieo0G", "sync": false}, {"__type__": "cc.Node", "_name": "lv", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [], "_active": true, "_components": [{"__id__": 111}], "_prefab": {"__id__": 112}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 16.8, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20, -16, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "2", "_N$string": "2", "_fontSize": 24, "_lineHeight": 28, "_enableWrapText": true, "_N$file": {"__uuid__": "1d3b5905-5559-4287-be97-cacf75721ef9"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a84+F3yU5Gobri/qhSI3WH", "sync": false}, {"__type__": "cc.Node", "_name": "show", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 114}, {"__id__": 117}], "_active": true, "_components": [], "_prefab": {"__id__": 123}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "speed", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 115}], "_prefab": {"__id__": 116}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 235, "g": 214, "b": 200, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 15.4, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23, 18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "8", "_N$string": "8", "_fontSize": 22, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "1d3b5905-5559-4287-be97-cacf75721ef9"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78QvDcb99KdZl7EqSTUP1y", "sync": false}, {"__type__": "cc.Node", "_name": "equip", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [{"__id__": 118}], "_active": true, "_components": [{"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18, 18, 0, 0, 0, 0, 1, 0.3, 0.3, 0.3]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 117}, "_children": [], "_active": true, "_components": [{"__id__": 119}], "_prefab": {"__id__": 120}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 118}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b2DQ1tsCVHoqhFO05tgbIW", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "16c28dab-219e-47c9-b2c1-9433c3560f75"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1rypsLclCtYcc6ceXZPjz", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51tvDYlphItba+2Sf/yiWh", "sync": false}, {"__type__": "cc.Node", "_name": "hp", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 125}], "_active": true, "_components": [{"__id__": 128}], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 127, "g": 122, "b": 119, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 124}, "_children": [], "_active": true, "_components": [{"__id__": 126}], "_prefab": {"__id__": 127}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 242, "g": 107, "b": 80, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 3, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0.5, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35n5LZXFtJCq1zndUoQnDH", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edkleQlbpHlJacYsmOS988", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.95, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 106}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbqQQ5mN1HY5/D9sO/KgcD", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 604, "height": 60}, "_resize": 1, "_N$layoutType": 3, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 8, "_N$spacingY": 12, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 16, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76DG8TMFFCTrNCbZ4510z8", "sync": false}, {"__type__": "cc.Node", "_name": "bottom", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 136}, {"__id__": 154}, {"__id__": 172}], "_active": true, "_components": [], "_prefab": {"__id__": 190}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 636, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -142, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "stateScroll", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [{"__id__": 137}], "_active": true, "_components": [{"__id__": 151}, {"__id__": 152}], "_prefab": {"__id__": 153}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 420, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-306, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "state", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [{"__id__": 138}], "_active": true, "_components": [{"__id__": 149}], "_prefab": {"__id__": 150}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 4, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "item", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [{"__id__": 139}, {"__id__": 143}], "_active": true, "_components": [{"__id__": 147}], "_prefab": {"__id__": 148}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 4, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 140}, {"__id__": 141}], "_prefab": {"__id__": 142}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 22, "_lineHeight": 32, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "key": "", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c394nfnBNGyKRC6VDsNffp", "sync": false}, {"__type__": "cc.Node", "_name": "time", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 144}, {"__id__": 145}], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 22, "_lineHeight": 32, "_enableWrapText": true, "_N$file": {"__uuid__": "be2e9bbf-0bd7-49b0-84b7-9f64faaa863a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "37b09imgMpEbpFEmXhZUc6V", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "format": "h:mm:ss", "endTime": 0, "prefix": "", "suffix": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5HtJkTX1Jo7nKQwJug4Mw", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 138}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 4, "height": 40}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 4, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5JNUr0gVEd4jwGO56EEke", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 4, "height": 40}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 20, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4lYEyTwtOGqx1WJGkrX0E", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "3991dKytutMAb15IMRiGeX2", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "target": {"__id__": 137}, "speed": 30, "delay": 2, "isVertical": false, "isLoopScroll": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "berhwUAo1HKLJrSUZ/jfGA", "sync": false}, {"__type__": "cc.Node", "_name": "treasure_be", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [{"__id__": 155}, {"__id__": 158}], "_active": true, "_components": [{"__id__": 168}, {"__id__": 169}, {"__id__": 170}], "_prefab": {"__id__": 171}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [306, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 154}, "_children": [], "_active": true, "_components": [{"__id__": 156}], "_prefab": {"__id__": 157}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-162, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 155}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9717fbfc-d199-4ca7-89c5-6f551b6cd210"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f87yQSwLhN3YqB+06CKcbM", "sync": false}, {"__type__": "cc.Node", "_name": "treasure", "_objFlags": 0, "_parent": {"__id__": 154}, "_children": [{"__id__": 159}, {"__id__": 163}], "_active": true, "_components": [{"__id__": 166}], "_prefab": {"__id__": 167}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "desc", "_objFlags": 0, "_parent": {"__id__": 158}, "_children": [], "_active": true, "_components": [{"__id__": 160}, {"__id__": 161}], "_prefab": {"__id__": 162}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 89, "g": 167, "b": 51, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 24, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "key": "", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abqtTOFbpNvqMNmc299rAf", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 158}, "_children": [], "_active": true, "_components": [{"__id__": 164}], "_prefab": {"__id__": 165}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 89, "g": 167, "b": 51, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edU6kAQrVOMb5Quf5q4ggp", "sync": false}, {"__type__": "7f930zlmPtBOZ7Rw+0CPNYQ", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "key": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16uolY01FIAZnwBK3aWVdE", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 176, "height": 40}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 8, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 154}, "_id": ""}, {"__type__": "356cc80BMlDxpmbLcfx1E6c", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "duration": 0.1, "positionOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "angleOffset": 0, "sound": 1, "soundPath": "", "soundVolume": 1, "event": 0, "eventName": "", "eventParams": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e21jDRuvhLr4Xn78i2sDiS", "sync": false}, {"__type__": "cc.Node", "_name": "force_revoke_be", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [{"__id__": 173}, {"__id__": 176}], "_active": true, "_components": [{"__id__": 186}, {"__id__": 187}, {"__id__": 188}], "_prefab": {"__id__": 189}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [306, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 174}], "_prefab": {"__id__": 175}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-162, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a49dd485-b11d-49b7-801c-5db4f2dd27b5"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b01YL8yMZPh5RLdrN6y/f/", "sync": false}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 177}, {"__id__": 181}], "_active": true, "_components": [{"__id__": 184}], "_prefab": {"__id__": 185}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "desc", "_objFlags": 0, "_parent": {"__id__": 176}, "_children": [], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}], "_prefab": {"__id__": 180}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 195, "g": 74, "b": 50, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 24, "_lineHeight": 28, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "key": "", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dAv6ZWt5OHqa+TnEWersz", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 176}, "_children": [], "_active": true, "_components": [{"__id__": 182}], "_prefab": {"__id__": 183}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 215, "g": 99, "b": 77, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "084dG+kfJJ4b3RySbk0pHe", "sync": false}, {"__type__": "7f930zlmPtBOZ7Rw+0CPNYQ", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "key": "ui.button_force_revoke", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8+DhaSHRMK6IMsSM6O+HM", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 176, "height": 40}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 8, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 172}, "_id": ""}, {"__type__": "356cc80BMlDxpmbLcfx1E6c", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "duration": 0.1, "positionOffset": {"__type__": "cc.Vec2", "x": 0, "y": -4}, "angleOffset": 0, "sound": 1, "soundPath": "", "soundVolume": 1, "event": 0, "eventName": "", "eventParams": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bcHXRGfMVBeqBRzBOyVWWN", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5GqtYvMhBvoeKmTIsoKz5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "83baaf38-a983-4893-99eb-ce0060466261"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 636, "height": 168}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 6, "_N$paddingBottom": 6, "_N$spacingX": 0, "_N$spacingY": 8, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3efM/W0HpP0bK/5Q6k+6Cp", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 636, "height": 168}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 8, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2kW4YXPJEhIwX8L7HnKCb", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43uEBkvHJOarZK8ytImkPm", "sync": false}, {"__type__": "cc.Node", "_name": "empty", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [], "_active": false, "_components": [{"__id__": 200}, {"__id__": 201}], "_prefab": {"__id__": 202}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5ce07632-0780-4ae5-8fb5-7588b2004551"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cpcZJJBBL7aN5dkyZhb2Y", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.5, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 56}, "content": {"__id__": 56}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 4, "_right": 4, "_top": 124, "_bottom": 120, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 660, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89gb2C2/hDsYm3F3/FoUK9", "sync": false}, {"__type__": "cc.Node", "_name": "tabs_tc_tce", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 207}, {"__id__": 246}], "_active": true, "_components": [{"__id__": 285}, {"__id__": 286}, {"__id__": 287}], "_prefab": {"__id__": 288}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -400, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "0", "_objFlags": 0, "_parent": {"__id__": 206}, "_children": [{"__id__": 208}, {"__id__": 226}], "_active": true, "_components": [{"__id__": 244}], "_prefab": {"__id__": 245}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-150, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [{"__id__": 209}], "_active": true, "_components": [{"__id__": 223}, {"__id__": 224}], "_prefab": {"__id__": 225}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lay", "_objFlags": 0, "_parent": {"__id__": 208}, "_children": [{"__id__": 210}, {"__id__": 213}, {"__id__": 217}], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}], "_prefab": {"__id__": 222}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 85.6, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 211}], "_prefab": {"__id__": 212}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-29.799999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b8cab08f-5ecd-4cd7-813d-4aabae21b26a"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56LsQjCUFJ6anSNy2mH8C1", "sync": false}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 214}, {"__id__": 215}], "_prefab": {"__id__": 216}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 63, "g": 51, "b": 47, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.799999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 26, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "key": "ui.area_army_0", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cc7KT+Q/hHNLSfpZhYcTZN", "sync": false}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 0, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 218}], "_prefab": {"__id__": 219}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 51.6, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [17.000000000000004, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 217}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "(3/5)", "_N$string": "(3/5)", "_fontSize": 24, "_lineHeight": 28, "_enableWrapText": true, "_N$file": {"__uuid__": "be2e9bbf-0bd7-49b0-84b7-9f64faaa863a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1a60YwXJNY7l5R9AdN6Ax", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 85.6, "height": 64}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 4, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "c682cTjk+FOq5M2BV16gBn/", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "maxWidth": 256, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5Fe2f0K5KmrMlLLB7XeiF", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a8d0bed7-45c0-496e-909f-5935e66c54df"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 72, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9fyNf1gBlB0qMkINJNVN7I", "sync": false}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [{"__id__": 227}], "_active": true, "_components": [{"__id__": 241}, {"__id__": 242}], "_prefab": {"__id__": 243}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lay", "_objFlags": 0, "_parent": {"__id__": 226}, "_children": [{"__id__": 228}, {"__id__": 231}, {"__id__": 235}], "_active": true, "_components": [{"__id__": 238}, {"__id__": 239}], "_prefab": {"__id__": 240}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 85.6, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 227}, "_children": [], "_active": true, "_components": [{"__id__": 229}], "_prefab": {"__id__": 230}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-29.799999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 228}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b8cab08f-5ecd-4cd7-813d-4aabae21b26a"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8dbaMPN5IyI007CfV5ViT", "sync": false}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 227}, "_children": [], "_active": true, "_components": [{"__id__": 232}, {"__id__": 233}], "_prefab": {"__id__": 234}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 63, "g": 51, "b": 47, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.799999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 26, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "key": "ui.area_army_0", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aaFz27FVtEfYICmN+R/0TE", "sync": false}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 0, "_parent": {"__id__": 227}, "_children": [], "_active": true, "_components": [{"__id__": 236}], "_prefab": {"__id__": 237}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 51.6, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [17.000000000000004, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 235}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "(3/5)", "_N$string": "(3/5)", "_fontSize": 24, "_lineHeight": 28, "_enableWrapText": true, "_N$file": {"__uuid__": "be2e9bbf-0bd7-49b0-84b7-9f64faaa863a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5e5G+jAFlIi4QP2dtnxK0D", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 227}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 85.6, "height": 64}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 4, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "c682cTjk+FOq5M2BV16gBn/", "_name": "", "_objFlags": 0, "node": {"__id__": 227}, "_enabled": true, "maxWidth": 256, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8cuOFkfVpNWJS6O8eZczyb", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a9b39a8d-7809-48ca-b1cb-d8f4fcbde36b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 72, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38qblBoF9BfIu8zSFZzrQS", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 207}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 208}, "_N$isChecked": true, "toggleGroup": null, "checkMark": {"__id__": 241}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eR8HIbxNCA7jCotHlcgu2", "sync": false}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "_parent": {"__id__": 206}, "_children": [{"__id__": 247}, {"__id__": 265}], "_active": true, "_components": [{"__id__": 283}], "_prefab": {"__id__": 284}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [150, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [{"__id__": 248}], "_active": true, "_components": [{"__id__": 262}, {"__id__": 263}], "_prefab": {"__id__": 264}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lay", "_objFlags": 0, "_parent": {"__id__": 247}, "_children": [{"__id__": 249}, {"__id__": 252}, {"__id__": 256}], "_active": true, "_components": [{"__id__": 259}, {"__id__": 260}], "_prefab": {"__id__": 261}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 85.6, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 248}, "_children": [], "_active": true, "_components": [{"__id__": 250}], "_prefab": {"__id__": 251}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-29.799999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "20323b87-14dc-4bc8-8823-d6f35eb99a84"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2b/vpIBCxBorsjd2wZwbRi", "sync": false}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 248}, "_children": [], "_active": true, "_components": [{"__id__": 253}, {"__id__": 254}], "_prefab": {"__id__": 255}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 63, "g": 51, "b": 47, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.799999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 252}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 26, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 252}, "_enabled": true, "key": "ui.area_army_1", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96M5h78clN2rcg3gxDFfnx", "sync": false}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 0, "_parent": {"__id__": 248}, "_children": [], "_active": true, "_components": [{"__id__": 257}], "_prefab": {"__id__": 258}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 51.6, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [17.000000000000004, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 256}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "(3/5)", "_N$string": "(3/5)", "_fontSize": 24, "_lineHeight": 28, "_enableWrapText": true, "_N$file": {"__uuid__": "be2e9bbf-0bd7-49b0-84b7-9f64faaa863a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70beGFMLJBjpDYXVVm2uh4", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 85.6, "height": 64}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 4, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "c682cTjk+FOq5M2BV16gBn/", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "maxWidth": 256, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8b+3AFy+tJ5Zk9sUwcMeTl", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a8d0bed7-45c0-496e-909f-5935e66c54df"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 72, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bdEOSrn9FGn5LShusydZ3B", "sync": false}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [{"__id__": 266}], "_active": false, "_components": [{"__id__": 280}, {"__id__": 281}], "_prefab": {"__id__": 282}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 336, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lay", "_objFlags": 0, "_parent": {"__id__": 265}, "_children": [{"__id__": 267}, {"__id__": 270}, {"__id__": 274}], "_active": true, "_components": [{"__id__": 277}, {"__id__": 278}], "_prefab": {"__id__": 279}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 189.6, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 266}, "_children": [], "_active": true, "_components": [{"__id__": 268}], "_prefab": {"__id__": 269}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-81.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "20323b87-14dc-4bc8-8823-d6f35eb99a84"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3eoFRfXgpJi6V+87KOj4Kv", "sync": false}, {"__type__": "cc.Node", "_name": "val", "_objFlags": 0, "_parent": {"__id__": 266}, "_children": [], "_active": true, "_components": [{"__id__": 271}, {"__id__": 272}], "_prefab": {"__id__": 273}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 63, "g": 51, "b": 47, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.799999999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 270}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 26, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "9705eZTZHVNNalGAojhJWC6", "_name": "", "_objFlags": 0, "node": {"__id__": 270}, "_enabled": true, "key": "ui.area_army_1", "fontName": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71xk+VTwNOt5BDWXHn8mH7", "sync": false}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 0, "_parent": {"__id__": 266}, "_children": [], "_active": true, "_components": [{"__id__": 275}], "_prefab": {"__id__": 276}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 147, "g": 110, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 51.6, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [69, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "(3/5)", "_N$string": "(3/5)", "_fontSize": 24, "_lineHeight": 28, "_enableWrapText": true, "_N$file": {"__uuid__": "be2e9bbf-0bd7-49b0-84b7-9f64faaa863a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_retinaScale": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79Cn5iicJP6omCqR5MPZ1X", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 266}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 189.6, "height": 64}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 4, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "c682cTjk+FOq5M2BV16gBn/", "_name": "", "_objFlags": 0, "node": {"__id__": 266}, "_enabled": true, "maxWidth": 256, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2e4BQA4mJI4a7dRFJ8W3FN", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a9b39a8d-7809-48ca-b1cb-d8f4fcbde36b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 72, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7eLxGKgpJ3rlfo2eX0H1F", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": {"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 247}, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 280}, "checkEvents": [], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86UoWEtWNNcKe8oc4YuO4T", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 580, "height": 64}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 20, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.ToggleContainer", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "allowSwitchOff": false, "checkEvents": [], "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": -8, "_bottom": 28, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aeVBtBUMNFNa4g5IyiCc8P", "sync": false}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98+0BcywJP3YYDu+9BlbMp", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 500, "_originalHeight": 700, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]