{"version": 3, "sources": ["assets\\app\\script\\view\\main\\ArmyListPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAyF;AAEzF,qDAAuE;AACvE,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,2DAA0D;AAC1D,2DAA0D;AAC1D,6DAA4D;AAE5D,yDAAoD;AAE5C,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA6C,mCAAc;IAA3D;QAAA,qEAwTC;QAtTG,0BAA0B;QAClB,aAAO,GAAuB,IAAI,CAAA,CAAC,0BAA0B;QAC7D,gBAAU,GAAY,IAAI,CAAA,CAAC,sBAAsB;QACjD,kBAAY,GAAY,IAAI,CAAA,CAAC,wBAAwB;QAC7D,MAAM;QAEE,YAAM,GAAgB,IAAI,CAAA;;IAgTtC,CAAC;IA9SU,yCAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACxE,GAAC,mBAAS,CAAC,sBAAsB,IAAG,IAAI,CAAC,qBAAqB,EAAE,QAAK,GAAE,IAAI;SAChF,CAAA;IACL,CAAC;IAEY,kCAAQ,GAArB;;;gBACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;;;;KACxC;IAEM,iCAAO,GAAd;QACI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IAEM,kCAAQ,GAAf;IACA,CAAC;IAEM,iCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,sDAAsD;IACtD,oCAAU,GAAV,UAAW,KAA0B,EAAE,CAAS;;QAC5C,IAAM,IAAI,GAAkB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACpD,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,oBAAO,CAAC,aAAa,aAAC,IAAI,CAAC,KAAK,0CAAE,WAAW,mCAAI,IAAI,CAAC,KAAK,CAAC,CAAA;SAC/D;IACL,CAAC;IAED,0BAA0B;IAC1B,qCAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACtC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAA;QAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1C,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;SACzB;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;SACjC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;SAClC;IACL,CAAC;IAED,wDAAwD;IACxD,yCAAe,GAAf,UAAgB,KAA0B,EAAE,CAAS;QACjD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,CAAA;QACtD,IAAI,GAAG,EAAE;YACL,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;SAC3B;IACL,CAAC;IAED,2DAA2D;IAC3D,yCAAe,GAAf,UAAgB,KAA0B,EAAE,CAAS;;QACjD,IAAM,IAAI,GAAkB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACpD,IAAI,CAAC,IAAI,EAAE;YACP,uDAAuD;YACvD,mDAAmD;SACtD;aAAM,IAAI,OAAA,IAAI,CAAC,SAAS,0CAAE,MAAM,IAAG,CAAC,EAAE;YACnC,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;SAC5D;IACL,CAAC;IAED,oEAAoE;IACpE,2CAAiB,GAAjB,UAAkB,KAA0B,EAAE,KAAa;QAA3D,iBAUC;QATG,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACjF,IAAI,GAAG,EAAE;YACL,gDAAgD;YAChD,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,GAAG,EAAE,mCAAmC,EAAE,EAAE,UAAC,IAAY,EAAE,SAAiB,EAAE,MAAgD;gBACxK,IAAI,oBAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,MAAM,QAAA,EAAE,UAAU,EAAE,EAAE,GAAG,KAAA,EAAE,KAAK,OAAA,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE;oBAC1F,uBAAU,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,IAAI,EAAE,EAA3B,CAA2B,CAAC,CAAA;iBAC3F;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,yEAAyE;IACzE,iDAAuB,GAAvB,UAAwB,KAA0B,EAAE,CAAS;QACzD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,EAAE,IAAI,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,KAAI,EAAE,CAAA;QAC7F,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;SACvC;IACL,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,SAAS;IACD,8CAAoB,GAA5B,UAA6B,IAAY;;QACrC,IAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,GAAG,MAAK,IAAI,CAAA,EAAA,CAAC,CAAA;QAC1G,IAAI,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,IAAI,EAAE;YACV,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,EAAd,CAAc,CAAC,CAAA;YACpE,IAAM,SAAS,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,SAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,mCAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAA;YAC1E,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,SAAS,IAAI,EAAE,CAAC,CAAA;SAC/C;IACL,CAAC;IAED,aAAa;IACL,+CAAqB,GAA7B,UAA8B,IAAY;;QACtC,IAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,GAAG,MAAK,IAAI,CAAA,EAAA,CAAC,CAAA;QAC1G,IAAI,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,IAAI,EAAE;YACV,IAAM,MAAI,GAAG,EAAE,CAAC,IAAI,CAAA;YACpB,MAAI,CAAC,KAAK,GAAG,oBAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,KAAK,MAAI,CAAC,GAAG,EAAtB,CAAsB,CAAC,CAAA;YACxE,MAAI,CAAC,GAAG,GAAG,oBAAO,CAAC,eAAe,aAAC,MAAI,CAAC,KAAK,0CAAE,WAAW,mCAAI,MAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAA;YACzG,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,MAAI,CAAC,CAAA;SAC/B;IACL,CAAC;IACD,iHAAiH;IAEjH,SAAS;IACD,qCAAW,GAAnB,UAAoB,IAAa;QAAjC,iBAuDC;QAtDG,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACnB,IAAM,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QACpF,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAA;QAClD,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;QACxB,QAAQ,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,GAAG,YAAY,CAAC,CAAA;QAC/D,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;YACvC,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE,EAAE;gBAClC,OAAM;aACT;YACD,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;YAChC,IAAM,KAAK,GAAG,KAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;YAC5C,IAAM,MAAM,GAAG,EAAE,CAAA;YACjB,oBAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAArB,CAAqB,CAAC,CAAA;YAC7D,IAAM,cAAc,GAAG,EAAE,CAAA;YACzB,KAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAA7B,CAA6B,CAAC,CAAA;YAC/E,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC;;gBACV,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;gBACvB,CAAC,CAAC,MAAM,GAAG,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;gBAC1D,CAAC,CAAC,GAAG,GAAG,oBAAO,CAAC,eAAe,aAAC,CAAC,CAAC,KAAK,0CAAE,WAAW,mCAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC3E,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;gBACX,IAAI,EAAE,GAAG,KAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,EAAE,GAAG,KAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,cAAc,CAAC,CAAA;gBAChG,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAA;YAC9C,CAAC,CAAC,CAAA;YACF,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;YACvB,SAAS,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAA;YAC5B,QAAQ,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,GAAG,GAAG,GAAG,KAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAA;YACrF,EAAE,CAAC,cAAc,EAAE,CAAA;YACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;YAChB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,CAAC;gBACf,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;gBAC9B,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;gBAC7C,KAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;gBAC5B,IAAI,KAAK,GAAU,IAAI,CAAC,KAAK,EAAE,UAAU,GAAG,KAAK,CAAA;gBACjD,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,UAAC,KAAK,EAAE,IAAI;;oBACxF,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAA;oBAC/F,IAAM,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAA;oBAChE,IAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAA;oBACvD,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAA,IAAI,CAAC,SAAS,0CAAE,EAAE,KAAI,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;oBAC7F,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;oBACxD,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAA;oBAC5G,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;wBACjD,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;qBACvE;oBACD,IAAI,OAAO,EAAE;wBACT,UAAU,GAAG,IAAI,CAAA;qBACpB;gBACL,CAAC,CAAC,CAAA;gBACF,uBAAU,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;gBAClE,KAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;YAC/C,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,0CAAgB,GAAxB,UAAyB,IAAmB,EAAE,cAAmB;QAC7D,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,IAAI,EAAE;YAC/B,OAAO,IAAI,CAAC,KAAK,CAAA;SACpB;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACnC,OAAO,iBAAS,CAAC,KAAK,CAAA;SACzB;aAAM,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YACpC,OAAO,iBAAS,CAAC,MAAM,CAAA;SAC1B;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,EAAvB,CAAuB,CAAC,EAAE;YACtD,OAAO,iBAAS,CAAC,KAAK,CAAA;SACzB;aAAM,IAAI,IAAI,CAAC,MAAM,EAAE;YACpB,OAAO,iBAAS,CAAC,MAAM,CAAA;SAC1B;QACD,OAAO,IAAI,CAAC,KAAK,CAAA;IACrB,CAAC;IAEO,uCAAa,GAArB,UAAsB,IAAa,EAAE,IAAmB;;QACpD,IAAM,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;QACxD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACjE,IAAI,OAAO,CAAC,MAAM,GAAG,QAAQ,IAAI,UAAU,EAAE;YACzC,uBAAU,CAAC,kBAAkB,CAAC,OAAO,cAAE,IAAI,CAAC,KAAK,0CAAE,WAAW,mCAAI,IAAI,CAAC,KAAK,CAAC,CAAA;SAChF;QACD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAC/C,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAA;SAC1C;IACL,CAAC;IAED,SAAS;IACD,4CAAkB,GAA1B,UAA2B,EAAW,EAAE,SAAyB;QAC7D,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC,MAAM,CAAA;QACtE,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,GAAG,CAAC,EAAE;YACjC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,wBAAc,CAAC,CAAC,MAAM,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAA;SACxF;IACL,CAAC;IAED,aAAa;IACL,6CAAmB,GAA3B,UAA4B,IAAa;QAAzC,iBAuBC;QAtBG,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACzE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;QAC/B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;QACxB,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,UAAA,IAAI;YACvC,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE,EAAE;gBAClC,OAAM;aACT;YACD,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;YACvB,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;YAChC,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;gBAC9B,SAAS,CAAC,YAAY,CAAC,oBAAO,CAAC,YAAY,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAA;aAC/G;YACD,EAAE,CAAC,cAAc,EAAE,CAAA;YACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;YAChB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;gBACpB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;gBACd,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAA;gBACnD,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;gBAChF,KAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YACnC,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED,aAAa;IACL,8CAAoB,GAA5B,UAA6B,IAAa;QAA1C,iBAgCC;QA/BG,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACzE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;QAC/B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;QACxB,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,UAAA,IAAI;YACxC,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,OAAO,EAAE,EAAE;gBAClC,OAAM;aACT;YACD,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;YACvB,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;YAChC,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;gBAC9B,SAAS,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAA;aACxD;YACD,EAAE,CAAC,cAAc,EAAE,CAAA;YACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;YAChB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,CAAC;gBACvB,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;gBACpB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;gBAC5D,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,mBAAmB,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;gBAC/E,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;gBAC5F,KAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;gBACxE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,oBAAoB,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAA;gBACpH,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;gBAC/E,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,2BAA2B,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAClH,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACjF,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,CAAA;gBACzB,EAAE,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;gBAC7E,EAAE,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC,MAAM,GAAG,CAAC,oBAAO,CAAC,YAAY,CAAA;gBACpE,EAAE,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;YACxE,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,0CAAgB,GAAxB,UAAyB,EAAW,EAAE,IAAS;QAC3C,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,KAAK,GAAa,gCAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACrF,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE;YAC3B,QAAQ,CAAC,YAAY,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC;gBACjE,IAAI,CAAC,KAAK,OAAO,EAAE;oBACf,OAAO,qBAAmB,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,oBAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,QAAK,CAAA;iBAC9J;qBAAM,IAAI,CAAC,KAAK,QAAQ,EAAE;oBACvB,OAAO,qBAAmB,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,oBAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC,QAAK,CAAA;iBAClK;gBACD,OAAO,EAAE,CAAA;YACb,CAAC,CAAC,CAAC,CAAA;SACN;IACL,CAAC;IAED,SAAS;IACK,8CAAoB,GAAlC,UAAmC,SAAiB,EAAE,IAAc;;;;;4BAC1C,qBAAM,qBAAS,CAAC,uBAAuB,CAAC,EAAE,SAAS,WAAA,EAAE,IAAI,MAAA,EAAE,CAAC,EAAA;;wBAA5E,KAAgB,SAA4D,EAA1E,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM,IAAI,IAAI,CAAC,OAAO,EAAE;4BACrB,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;yBACzD;;;;;KACJ;IAED,OAAO;IACO,wCAAc,GAA5B,UAA6B,GAAW;;;;;;wBACpC,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;wBAEpB,qBAAM,oBAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,EAAA;;wBAA/C,GAAG,GAAG,SAAyC;wBACrD,IAAI,GAAG,EAAE;4BACL,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;4BACjC,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;wBACD,qBAAM,uBAAU,CAAC,WAAW,CAAC,UAAU,CAAC,EAAA;;wBAAxC,SAAwC,CAAA;wBACxC,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;wBACjC,uBAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;;;;;KAClC;IAvTgB,eAAe;QADnC,OAAO;OACa,eAAe,CAwTnC;IAAD,sBAAC;CAxTD,AAwTC,CAxT4C,EAAE,CAAC,WAAW,GAwT1D;kBAxToB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ARMY_RECORD_DESC_CONF, ARMY_STATE_COLOR } from \"../../common/constant/Constant\";\nimport { ArmyShortInfo, TreasureInfo } from \"../../common/constant/DataType\";\nimport { ArmyState, PreferenceKey } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { mapHelper } from \"../../common/helper/MapHelper\";\nimport { netHelper } from \"../../common/helper/NetHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport PlayerModel from \"../../model/main/PlayerModel\";\nimport TextButtonCmpt from \"../cmpt/TextButtonCmpt\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class ArmyListPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce\n    private pagesNode_: cc.Node = null // path://root/pages_n\n    private loadingNode_: cc.Node = null // path://root/loading_n\n    //@end\n\n    private player: PlayerModel = null\n\n    public listenEventMaps() {\n        return [\n            { [EventType.UPDATE_ARMY_TREASURE]: this.onUpdateArmyTreasure, enter: true },\n            { [EventType.UPDATE_ARMY_AREA_INDEX]: this.onUpdateArmyAreaIndex, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.player = this.getModel('player')\n    }\n\n    public onEnter() {\n        this.tabsTc_.Tabs(0)\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/pages_n/0/list/view/content/item/pos_be\n    onClickPos(event: cc.Event.EventTouch, _: string) {\n        const data: ArmyShortInfo = event.target.parent.Data\n        if (data) {\n            this.hide()\n            gameHpr.gotoTargetPos(data.march?.targetIndex ?? data.index)\n        }\n    }\n\n    // path://root/tabs_tc_tce\n    onClickTabs(event: cc.Toggle, data: string) {\n        !data && audioMgr.playSFX('click')\n        const type = event.node.name\n        const node = this.pagesNode_.Swih(type)[0]\n        if (type === '0') {\n            this.showAllArmy(node)\n        } else if (type === '1') {\n            this.showArmyMarchRecord(node)\n        } else if (type === '2') {\n            this.showArmyBattleRecord(node)\n        }\n    }\n\n    // path://root/pages_n/2/view/content/item/5/playback_be\n    onClickPlayback(event: cc.Event.EventTouch, _: string) {\n        const data = event.target.parent.Data, uid = data?.uid\n        if (uid) {\n            this.playbackBattle(uid)\n        }\n    }\n\n    // path://root/pages_n/0/list/view/content/item/treasure_be\n    onClickTreasure(event: cc.Event.EventTouch, _: string) {\n        const data: ArmyShortInfo = event.target.parent.Data\n        if (!data) {\n            // } else if (gameHpr.isBattleingByIndex(data.index)) {\n            //     return viewHelper.showAlert(ecode.BATTLEING)\n        } else if (data.treasures?.length > 0) {\n            viewHelper.showPnl('common/TreasureList', data.treasures)\n        }\n    }\n\n    // path://root/pages_n/2/view/content/item/5/buttons/send_to_chat_be\n    onClickSendToChat(event: cc.Event.EventTouch, _data: string) {\n        const data = event.target.parent.parent.Data, uid = data?.uid, index = data.index\n        if (uid) {\n            // mapHelper.indexToPoint(data.armyIndex).Join()\n            viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_battle_record_to_chat_tip' }, (type: number, childType: number, select: { type: PreferenceKey, channel: string }) => {\n                if (gameHpr.chat.sendChat(type, childType, '', { select, battleInfo: { uid, index } }) === 0) {\n                    viewHelper.showPnl('common/Chat', { tab: type }).then(() => this.isValid && this.hide())\n                }\n            })\n        }\n    }\n\n    // path://root/pages_n/2/view/content/item/5/buttons/battle_statistics_be\n    onClickBattleStatistics(event: cc.Event.EventTouch, _: string) {\n        const data = event.target.parent.parent.Data, uid = data?.uid, uids = data?.armyUidList || []\n        if (uid && uids.length > 0) {\n            this.showBattleStatistics(uid, uids)\n        }\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // 刷新士兵宝箱\n    private onUpdateArmyTreasure(auid: string) {\n        const it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(m => m.Data?.uid === auid)\n        if (it?.Data) {\n            const army = this.player.getTempArmyList().find(m => m.uid === auid)\n            const treasures = it.Data.treasures = army?.treasures ?? it.Data.treasures\n            this.updateArmyTreasure(it, treasures || [])\n        }\n    }\n\n    // 刷新军队所在区域位置\n    private onUpdateArmyAreaIndex(auid: string) {\n        const it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(m => m.Data?.uid === auid)\n        if (it?.Data) {\n            const data = it.Data\n            data.march = gameHpr.world.getMarchs().find(m => m.armyUid === data.uid)\n            data.dis = gameHpr.getToMapCellDis(data.march?.targetIndex ?? data.index, this.player.getMainCityIndex())\n            this.updateArmyPos(it, data)\n        }\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    // 显示所有军队\n    private showAllArmy(node: cc.Node) {\n        const sv = node.Child('list', cc.ScrollView)\n        sv.content.Swih('')\n        const emptyNode = sv.Child('empty'), countLbl = node.Child('title/bg/val', cc.Label)\n        const armyMaxCount = this.player.getArmyMaxCount()\n        emptyNode.active = false\n        countLbl.setLocaleKey('ui.own_army_count', '0/' + armyMaxCount)\n        this.loadingNode_.active = true\n        this.player.getAllArmys(3, false).then(list => {\n            if (!this.isValid || !this.isEnter()) {\n                return\n            }\n            this.loadingNode_.active = false\n            const index = this.player.getMainCityIndex()\n            const marchs = {}\n            gameHpr.world.getMarchs().forEach(x => marchs[x.armyUid] = x)\n            const lvingPawnLvMap = {}\n            this.player.getPawnLevelingQueues().forEach(m => lvingPawnLvMap[m.puid] = m.lv)\n            list.forEach(m => {\n                m.march = marchs[m.uid]\n                m.tonden = gameHpr.world.getArmyTondenInfo(m.index, m.uid)\n                m.dis = gameHpr.getToMapCellDis(m.march?.targetIndex ?? m.index, index)\n            })\n            list.sort((a, b) => {\n                let aw = this.getArmySortState(a, lvingPawnLvMap), bw = this.getArmySortState(b, lvingPawnLvMap)\n                return aw === bw ? a.dis - b.dis : bw - aw\n            })\n            const len = list.length\n            emptyNode.active = len === 0\n            countLbl.setLocaleKey('ui.own_army_count', len + '/' + this.player.getArmyMaxCount())\n            sv.stopAutoScroll()\n            sv.content.y = 0\n            sv.List(len, (it, i) => {\n                const data = it.Data = list[i]\n                it.Child('name', cc.Label).string = data.name\n                this.updateArmyPos(it, data)\n                let pawns: any[] = data.pawns, isHasLving = false\n                it.Child('pawns').Items(pawns.concat(data.drillPawns).concat(data.curingPawns), (node2, pawn) => {\n                    const icon = node2.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime\n                    const isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring\n                    const lv = isLving ? lvingPawnLvMap[pawn.uid] : pawn.lv\n                    resHelper.loadPawnHeadMiniIcon(isId ? pawn : (pawn.portrayal?.id || pawn.id), icon, this.key)\n                    icon.opacity = (isId || isLving || isCuring) ? 120 : 255\n                    node2.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv\n                    if (node2.Child('hp').active = (!isId && !isCuring)) {\n                        node2.Child('hp/bar', cc.Sprite).fillRange = pawn.hp[0] / pawn.hp[1]\n                    }\n                    if (isLving) {\n                        isHasLving = true\n                    }\n                })\n                viewHelper.updateArmyState(it, data, data.march, isHasLving, true)\n                this.updateArmyTreasure(it, data.treasures)\n            })\n        })\n    }\n\n    private getArmySortState(army: ArmyShortInfo, lvingPawnLvMap: any) {\n        if (army.state !== ArmyState.NONE) {\n            return army.state\n        } else if (army.drillPawns.length > 0) {\n            return ArmyState.DRILL\n        } else if (army.curingPawns.length > 0) {\n            return ArmyState.CURING\n        } else if (army.pawns.some(m => !!lvingPawnLvMap[m.uid])) {\n            return ArmyState.LVING\n        } else if (army.tonden) {\n            return ArmyState.TONDEN\n        }\n        return army.state\n    }\n\n    private updateArmyPos(node: cc.Node, data: ArmyShortInfo) {\n        const isMarching = !!data.march, isHasDis = data.dis > 0\n        const posNode = node.Child('pos_be'), disNode = node.Child('dis')\n        if (posNode.active = isHasDis || isMarching) {\n            viewHelper.updatePositionView(posNode, data.march?.targetIndex ?? data.index)\n        }\n        if (disNode.active = !isHasDis && !posNode.active) {\n            disNode.setLocaleKey('ui.in_main_city')\n        }\n    }\n\n    // 刷新宝箱信息\n    private updateArmyTreasure(it: cc.Node, treasures: TreasureInfo[]) {\n        const node = it.Child('treasure_be'), treasureCount = treasures.length\n        if (node.active = treasureCount > 0) {\n            node.Child('treasure', TextButtonCmpt).setKey('ui.get_treasure_count', treasureCount)\n        }\n    }\n\n    // 显示军队行军记录列表\n    private showArmyMarchRecord(node: cc.Node) {\n        const sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty')\n        sv.content.Swih('')\n        this.loadingNode_.active = true\n        emptyNode.active = false\n        this.player.getArmyMarchRecords().then(list => {\n            if (!this.isValid || !this.isEnter()) {\n                return\n            }\n            const len = list.length\n            this.loadingNode_.active = false\n            if (emptyNode.active = len === 0) {\n                emptyNode.setLocaleKey(gameHpr.isNoviceMode ? 'ui.army_march_record_empty_1' : 'ui.army_march_record_empty')\n            }\n            sv.stopAutoScroll()\n            sv.content.y = 0\n            sv.Items(list, (it, data) => {\n                it.Data = data\n                it.Child('0/name', cc.Label).string = data.armyName\n                it.Child('0/time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time)\n                this.updateRecordInfo(it, data)\n            })\n        })\n    }\n\n    // 显示军队战斗记录列表\n    private showArmyBattleRecord(node: cc.Node) {\n        const sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty')\n        sv.content.Swih('')\n        this.loadingNode_.active = true\n        emptyNode.active = false\n        this.player.getArmyBattleRecords().then(list => {\n            if (!this.isValid || !this.isEnter()) {\n                return\n            }\n            const len = list.length\n            this.loadingNode_.active = false\n            if (emptyNode.active = len === 0) {\n                emptyNode.setLocaleKey('ui.army_battle_record_empty')\n            }\n            sv.stopAutoScroll()\n            sv.content.y = 0\n            sv.List(list.length, (it, i) => {\n                const data = list[i]\n                it.Child('win/bg').Color(data.isWin ? '#EB9E4E' : '#96B2C8')\n                it.Child('win/bg/val').setLocaleKey('ui.battle_result_' + Number(!!data.isWin))\n                it.Child('win/time', cc.Label).string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.beginTime)\n                this.updateRecordInfo(it.Child('0'), { type: 0, armyIndex: data.index })\n                it.Child('1/val').setLocaleKey('ui.end_battle_time', ut.millisecondFormat(data.endTime - data.beginTime, 'h:mm:ss'))\n                it.Child('2/val').setLocaleKey('ui.battle_army_count', data.armyUidList.length)\n                it.Child('3/val').setLocaleKey('ui.alli_battle_record_0_1', (data.invalidInfo[1] || 0) + (data.validInfo[1] || 0))\n                it.Child('4/val').setLocaleKey('ui.alli_battle_record_1_1', data.deadInfo.length)\n                it.Child('5').Data = data\n                it.Child('5/buttons/battle_statistics_be').active = !!data.armyUidList.length\n                it.Child('5/buttons/send_to_chat_be').active = !gameHpr.isNoviceMode\n                it.Child('5/playback_be', cc.Button).interactable = !!data.isCanPlay\n            })\n        })\n    }\n\n    private updateRecordInfo(it: cc.Node, data: any) {\n        const descNode = it.Child('desc'), texts: string[] = ARMY_RECORD_DESC_CONF[data.type]\n        if (descNode.active = !!texts) {\n            descNode.setLocaleKey('ui.army_record_desc_' + data.type, texts.map(m => {\n                if (m === 'index') {\n                    return ` <color=#564C49>${assetsMgr.lang('ui.position', gameHpr.getCellBaseNameByIndex(data.armyIndex), mapHelper.indexToPoint(data.armyIndex).Join())}</>`\n                } else if (m === 'target') {\n                    return ` <color=#564C49>${assetsMgr.lang('ui.position', gameHpr.getCellBaseNameByIndex(data.targetIndex), mapHelper.indexToPoint(data.targetIndex).Join())}</>`\n                }\n                return ''\n            }))\n        }\n    }\n\n    // 显示战斗统计\n    private async showBattleStatistics(battleUid: string, uids: string[]) {\n        const { err, data } = await netHelper.reqGetArmyRecordsByUids({ battleUid, uids })\n        if (err) {\n            return viewHelper.showAlert(err)\n        } else if (this.isValid) {\n            viewHelper.showPnl('main/BattleStatistics', data.list)\n        }\n    }\n\n    // 回放战斗\n    private async playbackBattle(uid: string) {\n        viewHelper.showWindLoading(true)\n        // 获取战斗数据\n        const err = await gameHpr.playback.setRecordById(uid)\n        if (err) {\n            viewHelper.showWindLoading(false)\n            return viewHelper.showAlert(err)\n        }\n        await viewHelper.preloadWind('playback')\n        viewHelper.showWindLoading(false)\n        viewHelper.gotoWind('playback')\n    }\n}\n"]}