{"version": 3, "sources": ["assets\\app\\script\\view\\area\\AreaWindCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAqD;AACrD,2DAA0D;AAC1D,2DAA0D;AAC1D,6DAA4D;AAK5D,6DAA4D;AAC5D,uCAAkC;AAGlC,6DAA4D;AAC5D,qDAAgD;AAChD,yDAAoD;AACpD,wDAAmD;AACnD,yCAAoC;AACpC,2DAAiI;AACjI,iDAA4C;AAC5C,qDAAuE;AACvE,qDAAoD;AACpD,gEAA2D;AAC3D,6DAAyD;AACzD,yDAAoD;AACpD,2DAA0D;AAC1D,+DAA8D;AAItD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA0C,gCAAe;IAAzD;QAAA,qEAmxCC;QAjxCG,0BAA0B;QAClB,cAAQ,GAAY,IAAI,CAAA,CAAC,oBAAoB;QAC7C,eAAS,GAAY,IAAI,CAAA,CAAC,2BAA2B;QACrD,kBAAY,GAAY,IAAI,CAAA,CAAC,+BAA+B;QAC5D,gBAAU,GAAY,IAAI,CAAA,CAAC,4BAA4B;QACvD,qBAAe,GAAY,IAAI,CAAA,CAAC,4BAA4B;QAC5D,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,mBAAa,GAAY,IAAI,CAAA,CAAC,0BAA0B;QACxD,sBAAgB,GAAY,IAAI,CAAA,CAAC,6BAA6B;QAC9D,oBAAc,GAAY,IAAI,CAAA,CAAC,2BAA2B;QAClE,MAAM;QAEW,eAAS,GAAY,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAEzC,YAAM,GAAY,IAAI,CAAA;QACtB,cAAQ,GAAY,IAAI,CAAA;QACxB,eAAS,GAAiB,IAAI,CAAA;QAE9B,WAAK,GAAY,IAAI,CAAA;QACrB,gBAAU,GAAoB,IAAI,CAAA;QAClC,YAAM,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QACzB,wBAAkB,GAAW,CAAC,CAAA;QAC9B,cAAQ,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,MAAM;QAClC,eAAS,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,MAAM;QACnC,iBAAW,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,SAAS;QACxC,gBAAU,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,QAAQ;QACtC,iBAAW,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,MAAM;QAErC,WAAK,GAAc,EAAE,CAAA,CAAC,MAAM;QAC5B,YAAM,GAAc,EAAE,CAAA,CAAC,MAAM;QAC7B,eAAS,GAAc,EAAE,CAAA,CAAC,MAAM;QAChC,qBAAe,GAAc,EAAE,CAAA,CAAC,IAAI;QACpC,YAAM,GAAoB,EAAE,CAAA,CAAC,MAAM;QACnC,cAAQ,GAA8B,EAAE,CAAA;QACxC,WAAK,GAAe,EAAE,CAAA,CAAC,MAAM;QAC7B,aAAO,GAAQ,EAAE,CAAA;QACjB,gBAAU,GAAY,IAAI,CAAA;QAC1B,WAAK,GAAc,IAAI,CAAA,CAAC,IAAI;QAE5B,mBAAa,GAAc,IAAI,CAAA,CAAC,SAAS;QACzC,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;QACvC,kBAAY,GAAiB,IAAI,CAAA;QACjC,mBAAa,GAAY,KAAK,CAAA,CAAC,WAAW;QAC1C,eAAS,GAAgC,EAAE,CAAA,CAAC,UAAU;QACtD,oBAAc,GAAW,CAAC,CAAA;QAE1B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;;IAguC3C,CAAC;IA9tCU,sCAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,kBAAQ,CAAC,aAAa,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC1D,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,QAAK,GAAE,IAAI;sBAClE,GAAC,mBAAS,CAAC,kBAAkB,IAAG,IAAI,CAAC,kBAAkB,EAAE,QAAK,GAAE,IAAI;sBACpE,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,UAAU,IAAG,IAAI,CAAC,WAAW,EAAE,QAAK,GAAE,IAAI;sBACrD,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACzE,GAAC,mBAAS,CAAC,aAAa,IAAG,IAAI,CAAC,aAAa,EAAE,QAAK,GAAE,IAAI;sBAC1D,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;sBACvE,GAAC,mBAAS,CAAC,SAAS,IAAG,IAAI,CAAC,UAAU,EAAE,QAAK,GAAE,IAAI;sBACnD,GAAC,mBAAS,CAAC,YAAY,IAAG,IAAI,CAAC,aAAa,EAAE,QAAK,GAAE,IAAI;sBACzD,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,kBAAkB,IAAG,IAAI,CAAC,kBAAkB,EAAE,QAAK,GAAE,IAAI;sBACpE,GAAC,mBAAS,CAAC,aAAa,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC3D,GAAC,mBAAS,CAAC,cAAc,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC5D,GAAC,mBAAS,CAAC,mBAAmB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;sBACtE,GAAC,mBAAS,CAAC,QAAQ,IAAG,IAAI,CAAC,SAAS,EAAE,QAAK,GAAE,IAAI;sBACjD,GAAC,mBAAS,CAAC,QAAQ,IAAG,IAAI,CAAC,SAAS,EAAE,QAAK,GAAE,IAAI;sBACjD,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,QAAK,GAAE,IAAI;sBAClE,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,aAAa,IAAG,IAAI,CAAC,aAAa,EAAE,QAAK,GAAE,IAAI;sBAC1D,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,kBAAkB,IAAG,IAAI,CAAC,kBAAkB,EAAE,QAAK,GAAE,IAAI;sBACpE,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,kBAAkB,IAAG,IAAI,CAAC,kBAAkB,EAAE,QAAK,GAAE,IAAI;sBACpE,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC7E,GAAC,mBAAS,CAAC,UAAU,IAAG,IAAI,CAAC,WAAW,EAAE,QAAK,GAAE,IAAI;sBACrD,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC7E,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;uBAC7E,IAAC,mBAAS,CAAC,wBAAwB,IAAG,IAAI,CAAC,sBAAsB,EAAE,SAAK,GAAE,IAAI;uBAC9E,IAAC,mBAAS,CAAC,mBAAmB,IAAG,IAAI,CAAC,kBAAkB,EAAE,SAAK,GAAE,IAAI;uBACrE,IAAC,mBAAS,CAAC,sBAAsB,IAAG,IAAI,CAAC,qBAAqB,EAAE,SAAK,GAAE,IAAI;uBAC3E,IAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,SAAK,GAAE,IAAI;uBAClE,IAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,SAAK,GAAE,IAAI;uBAChE,IAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,qBAAqB,EAAE,SAAK,GAAE,IAAI;uBAC1E,IAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,SAAK,GAAE,IAAI;uBAClE,IAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,oBAAoB,EAAE,SAAK,GAAE,IAAI;uBACxE,IAAC,mBAAS,CAAC,2BAA2B,IAAG,IAAI,CAAC,yBAAyB,EAAE,SAAK,GAAE,IAAI;SACzF,CAAA;IACL,CAAC;IAEY,+BAAQ,GAArB;;;;;wBACI,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;wBACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;wBAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;wBAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,sBAAY,CAAC,CAAA;wBACnE,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;wBACnC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAA;wBACpC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;wBAC7B,OAAO;wBACP,qBAAM,uBAAU,CAAC,UAAU,CAAC,aAAa,CAAC,EAAA;;wBAD1C,OAAO;wBACP,SAA0C,CAAA;;;;;KAC7C;IAEY,8BAAO,GAApB;;;;;;;wBACI,KAAA,IAAI,CAAA;wBAAS,qBAAM,IAAI,CAAC,UAAU,CAAC,cAAc,aAAC,oBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,0CAAE,KAAK,mCAAI,CAAC,CAAC,CAAC,EAAA;;wBAA3F,GAAK,KAAK,GAAG,SAA8E,CAAA;wBAC3F,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;4BACb,sBAAM;yBACT;wBACD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;wBACvC,OAAO;wBACP,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;wBACtC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;wBACxC,mBAAmB;wBACnB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;wBACzC,cAAc;wBACd,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;wBAC/D,UAAU;wBACV,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;wBAEvD,KAAK,GAAG,oBAAO,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;wBAC5F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;wBACxB,QAAQ;wBACR,qBAAM,IAAI,CAAC,QAAQ,EAAE,EAAA;;wBADrB,QAAQ;wBACR,SAAqB,CAAA;;;;;KACxB;IAEM,8BAAO,GAAd,UAAe,OAAgB;QAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,uBAAU,CAAC,QAAQ,CAAC,oBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAA;YAChD,IAAI,oBAAO,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE;gBAC3B,uBAAU,CAAC,cAAc,CAAC,aAAK,CAAC,OAAO,CAAC,CAAA;aAC3C;YACD,OAAM;SACT;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAA;QAC3B,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAA;QAC9B,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,cAAc,GAAG,oBAAO,CAAC,KAAK,CAAC,aAAa,EAAE,CAAA;QACnD,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAA;QACjC,SAAS;QACT,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;QAC/D,UAAU;QACV,IAAM,EAAE,GAAG,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,eAAe,CAAC,CAAA;QAC7E,uBAAU,CAAC,IAAI,CAAC,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAC3F,OAAO;QACP,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,OAAO;QACP,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,OAAO;QACP,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAA;QACnC,KAAK;QACL,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,0BAA0B,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;SACpE;aAAM;YACH,uBAAU,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SAChD;QACD,EAAE;QACF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/C,EAAE;QACF,oBAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAA;IACjD,CAAC;IAEM,8BAAO,GAAd;QACI,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,eAAe,EAAE,uBAAU,CAAC,SAAS,CAAC,CAAA;QACxF,uBAAU,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QACjC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QACtB,oBAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QAC/B,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,qBAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACxC,qBAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC1C,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QACnC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,KAAK,CAAA;QAC5B,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAA;QACtC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,KAAK,CAAA;QAC/B,WAAW,CAAC,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,uBAAU,CAAC,KAAK,EAAE,CAAA;QAClB,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;QAC9B,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;IACjC,CAAC;IAEM,8BAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,MAAM;IACN,iHAAiH;IAEzG,qCAAc,GAAtB;QACI,IAAI,CAAC,MAAM,EAAE,CAAA;IACjB,CAAC;IAEO,wCAAiB,GAAzB;QACI,OAAO,IAAI,CAAC,OAAO,EAAE,CAAA;IACzB,CAAC;IAED,YAAY;IACJ,yCAAkB,GAA1B,UAA2B,IAAe,EAAE,KAAa;QACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAA;QACrC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAA;YACpE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SACjE;IACL,CAAC;IAED,WAAW;IACH,uCAAgB,GAAxB,UAAyB,IAAe;QACpC,YAAY;QACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAA/C,CAA+C,CAAC,CAAA;QACzE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,EAApB,CAAoB,CAAC,CAAA;QAC7C,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,OAAO;IACC,kCAAW,GAAnB,UAAoB,IAAe,EAAE,GAAY;QAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,MAAM;QACxD,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAA;QACpC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,CAAC,CAAA;IACpE,CAAC;IAED,WAAW;IACH,2CAAoB,GAA5B,UAA6B,IAAY;QACrC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iCAAiC,EAAE;YACnF,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAA;QAC/B,IAAI,IAAI,KAAK,QAAQ,EAAE,EAAE,IAAI;YACzB,IAAI,CAAC,MAAM,EAAE,CAAA;SAChB;aAAM,IAAI,IAAI,KAAK,IAAI,EAAE,EAAE,IAAI;YAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,OAAO,uBAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aAC9C;YACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;YAC7D,mCAAmC;SACtC;QACD,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA,CAAC,QAAQ;QAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAA,CAAC,QAAQ;QAC7D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAnB,CAAmB,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,EAAE,CAAA,CAAC,UAAU;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,OAAO;IACC,oCAAa,GAArB,UAAsB,KAAa,EAAE,GAAW;QAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;YACxD,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;QAChD,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,EAApB,CAAoB,CAAC,CAAA;YAC9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,EAApB,CAAoB,CAAC,CAAA;YAC7C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAnB,CAAmB,CAAC,CAAA;YACxD,MAAM;YACN,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;YAC1B,QAAQ;YACR,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;YACzC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;YACxB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;SAC3G;IACL,CAAC;IAED,YAAY;IACJ,0CAAmB,GAA3B,UAA4B,IAAY;QACpC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;YAC3E,uBAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;YACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAnB,CAAmB,CAAC,CAAA;YAC7C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAnB,CAAmB,CAAC,CAAA;YAC5C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,KAAK,EAAhB,CAAgB,CAAC,CAAA;YACrD,IAAI,CAAC,aAAa,EAAE,CAAA;SACvB;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE,EAAE,IAAI;YAChC,IAAI,CAAC,UAAU,EAAE,CAAA;SACpB;aAAM,IAAI,IAAI,KAAK,IAAI,EAAE;YACtB,IAAI,CAAC,WAAW,EAAE,CAAA;SACrB;IACL,CAAC;IAED,OAAO;IACC,iCAAU,GAAlB,UAAmB,IAAc;QAAjC,iBAaC;;QAZG,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YACnC,IAAM,SAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAA;YACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;gBAC5B,IAAI,IAAI,IAAI,KAAI,CAAC,QAAQ,EAAE,IAAI,SAAO,EAAE;oBACpC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;oBAC3B,cAAc;oBACd,uBAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;oBAC9B,KAAK;oBACL,oCAAoC;iBACvC;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,OAAO;IACC,oCAAa,GAArB,UAAsB,IAAc;;QAChC,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YACnC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YACjD,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;gBACtB,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;aAC1D;SACJ;IACL,CAAC;IAED,SAAS;IACD,sCAAe,GAAvB,UAAwB,IAAc;;QAClC,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;SACtC;aAAM,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SAC7B;aAAM;YACH,MAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,0CAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAC;SAC/D;IACL,CAAC;IAED,SAAS;IACD,yCAAkB,GAA1B,UAA2B,IAAc;;QACrC,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YACnC,MAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,0CAAE,SAAS,GAAE;SACzD;IACL,CAAC;IAED,OAAO;IACC,qCAAc,GAAtB,UAAuB,KAAa;;QAChC,IAAI,KAAK,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YAC7B,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAC;SAC/B;IACL,CAAC;IAED,SAAS;IACD,0CAAmB,GAA3B,UAA4B,IAAgB;;QACxC,IAAI,IAAI,CAAC,KAAK,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YAClC,OAAM;SACT;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,EAAlB,CAAkB,CAAC,CAAA;QACvD,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACvB,KAAK,CAAC,cAAc,EAAE,CAAA;SACzB;IACL,CAAC;IAED,OAAO;IACC,qCAAc,GAAtB,UAAuB,KAAa;;QAChC,IAAI,KAAK,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YAC7B,IAAI,CAAC,UAAU,EAAE,CAAA;SACpB;IACL,CAAC;IAED,OAAO;IACC,gCAAS,GAAjB,UAAkB,IAAa;QAA/B,iBAIC;;QAHG,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAA;SAC9C;IACL,CAAC;IAED,OAAO;IACC,mCAAY,GAApB,UAAqB,IAAa;QAAlC,iBASC;;QARG,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBAChB,IAAM,CAAC,GAAG,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,OAAO,MAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;gBACpF,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;oBACV,KAAI,CAAC,SAAS,CAAC,KAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;iBACnD;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,SAAS;IACD,mCAAY,GAApB,UAAqB,IAAa;QAAlC,iBAeC;;QAdG,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YACnC,OAAM;SACT;QACD,SAAS;QACT,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YACvB,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,OAAO,MAAK,IAAI,CAAC,GAAG,EAAE;gBAC9B,SAAQ;aACX;iBAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE;gBACtC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBACvB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;aAC1B;SACJ;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAA;IAC/C,CAAC;IAED,SAAS;IACD,sCAAe,GAAvB,UAAwB,KAAa;QACjC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE;YAC5B,IAAI,CAAC,SAAS,EAAE,CAAA;SACnB;IACL,CAAC;IAED,OAAO;IACC,gCAAS,GAAjB,UAAkB,KAAa,EAAE,IAAa;QAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SACxB;IACL,CAAC;IAED,OAAO;IACC,mCAAY,GAApB,UAAqB,KAAa,EAAE,GAAW;QAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;SACtD;IACL,CAAC;IAED,OAAO;IACC,wCAAiB,GAAzB,UAA0B,KAAa;;QACnC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE;YAC5B,OAAM;SACT;QACD,cAAc;QACd,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,cAAc;QACd,MAAA,IAAI,CAAC,YAAY,0CAAE,MAAM,GAAE;QAC3B,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,QAAQ;QACR,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAC;QAC5B,QAAQ;QACR,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,OAAO;QACP,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,0BAA0B,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACjE,EAAE;QACF,oBAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IAC7B,CAAC;IAED,OAAO;IACC,sCAAe,GAAvB,UAAwB,KAAa;;QACjC,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,MAAK,KAAK,EAAE;YAC7B,OAAM;SACT;aAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,eAAe;YACpH,OAAO,IAAI,CAAC,OAAO,EAAE,CAAA;SACxB;QACD,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACrB,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,OAAO;QACP,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,0BAA0B,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACjE,EAAE;QACF,oBAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC9B,CAAC;IAED,OAAO;IACC,oCAAa,GAArB,UAAsB,IAAS;;QAC3B,IAAI,IAAI,CAAC,KAAK,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;YAClC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;gBAC1B,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,GAAE;aACrB;YACD,uBAAU,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACpI;IACL,CAAC;IAED,OAAO;IACC,sCAAe,GAAvB,UAAwB,IAAS;;QAC7B,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,MAAK,IAAI,CAAC,KAAK,EAAE;YAClC,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,0CAAE,WAAW,EAAE,CAAA;YAC3H,IAAI,GAAG,EAAE;gBACL,uBAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;aACzE;SACJ;IACL,CAAC;IAED,SAAS;IACD,yCAAkB,GAA1B,UAA2B,IAAS;;QAChC,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,MAAK,IAAI,CAAC,KAAK,EAAE;YAClC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;YACrD,IAAI,IAAI,EAAE;gBACN,uBAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;aAC5F;SACJ;IACL,CAAC;IAED,SAAS;IACD,sCAAe,GAAvB,UAAwB,IAAS;;QAC7B,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,MAAK,IAAI,CAAC,KAAK,EAAE;YAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,CAAA;YAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,CAAA;YAC/D,uBAAU,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SAC/D;IACL,CAAC;IAED,SAAS;IACD,yCAAkB,GAA1B,UAA2B,IAAS;;QAChC,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,MAAK,IAAI,CAAC,KAAK,EAAE;YAClC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAA;YACnD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAA;YAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;gBACrB,IAAI,GAAG,IAAI,CAAC,aAAa,CAAA;aAC5B;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBAC7B,IAAI,GAAG,IAAI,CAAC,SAAS,CAAA;gBACrB,IAAI,CAAC,MAAM,GAAG,CAAC,0BAAe,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,oBAAS,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;aAC1F;YACD,uBAAU,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACpD;IACL,CAAC;IAED,OAAO;IACC,sCAAe,GAAvB,UAAwB,KAAa,EAAE,GAAW,EAAE,IAAS;;QACzD,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,MAAK,KAAK,EAAE;YAC7B,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;SAC9B;IACL,CAAC;IAED,SAAS;IACD,6CAAsB,GAA9B,UAA+B,KAAa,EAAE,IAAY;;QACtD,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,MAAK,KAAK,EAAE;YAC7B,uBAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;SACzB;IACL,CAAC;IAED,OAAO;IACC,kCAAW,GAAnB,UAAoB,IAAS;;QACzB,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,OAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAA,EAAE;YACnC,OAAM;SACT,CAAA;;YAEG;QACJ,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC5C,IAAI,uBAAU,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE;YACxC,uBAAU,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;SACpC;IACL,CAAC;IAED,SAAS;IACD,sCAAe,GAAvB;QACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,cAAc,EAAE,EAAlB,CAAkB,CAAC,CAAA;IAChD,CAAC;IAED,SAAS;IACD,6CAAsB,GAA9B,UAA+B,KAAa;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,eAAe,EAAE,EAAnB,CAAmB,CAAC,CAAA;SAChD;IACL,CAAC;IAED,WAAW;IACH,yCAAkB,GAA1B,UAA2B,GAAW;QAClC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAjB,CAAiB,CAAC,CAAA;IAC9C,CAAC;IAED,WAAW;IACH,4CAAqB,GAA7B,UAA8B,GAAY;QACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,EAApB,CAAoB,CAAC,CAAA;IACjD,CAAC;IAED,SAAS;IACD,wCAAiB,GAAzB,UAA0B,IAAa;;QACnC,MAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,0CAAE,mBAAmB,GAAE;IACnE,CAAC;IAED,SAAS;IACD,uCAAgB,GAAxB,UAAyB,IAAa;QAClC,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACV,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,MAAM,EAAE;gBAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;aACxB;SACJ;QACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,eAAe,EAAE,EAAnB,CAAmB,CAAC,CAAA;IACjD,CAAC;IAED,OAAO;IACC,4CAAqB,GAA7B,UAA8B,IAAa;QAA3C,iBAiBC;QAhBG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAM;SACT;QACD,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACV,IAAM,MAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAI,MAAI,CAAC,cAAc,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;gBAC3C,MAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;oBAC5C,IAAI,KAAI,CAAC,QAAQ,EAAE,EAAE;wBACjB,KAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;wBACvB,KAAI,CAAC,SAAS,CAAC,MAAI,CAAC,CAAA;wBACpB,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;qBACxB;gBACL,CAAC,CAAC,CAAA;aACL;SACJ;IACL,CAAC;IAED,SAAS;IACD,wCAAiB,GAAzB;;QACI,MAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,2BAAgB,EAAzB,CAAyB,CAAC,0CAAE,gBAAgB,GAAE;IACxE,CAAC;IAED,SAAS;IACD,2CAAoB,GAA5B;;QACI,MAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,2BAAgB,EAAzB,CAAyB,CAAC,0CAAE,gBAAgB,GAAE;IACxE,CAAC;IAED,MAAM;IACE,gDAAyB,GAAjC,UAAkC,IAAS;QACvC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACvB,yBAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACvE;IACL,CAAC;IACD,iHAAiH;IAEzG,+BAAQ,GAAhB,sBAAqB,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,QAAC,IAAI,CAAC,KAAK,0CAAE,MAAM,CAAA,CAAA,CAAC,CAAC;IAE1D,sCAAe,GAAvB,UAAwB,KAAc;QAClC,OAAO,KAAK,IAAI,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAA;IAC5F,CAAC;IAEO,sCAAe,GAAvB,UAAwB,KAAc;QAClC,OAAO,KAAK,IAAI,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAC7E,CAAC;IAEO,2CAAoB,GAA5B,UAA6B,KAAc;QACvC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;IACvG,CAAC;IAEO,4BAAK,GAAb;;QACI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACjC,MAAA,IAAI,CAAC,KAAK,0CAAE,SAAS,CAAC,KAAK,EAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,oBAAO,CAAC,iBAAiB,EAAE,CAAA;QAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC3B,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,oBAAoB;QACpB,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,aAAa,EAAE,CAAA;IACxB,CAAC;IAED,WAAW;IACG,6BAAM,GAApB;;;;;;;wBACI,oBAAO,CAAC,iBAAiB,EAAE,CAAA;wBAC3B,cAAc;wBACd,IAAI,CAAC,qBAAqB,EAAE,CAAA;wBAC5B,IAAI,CAAC,cAAc,EAAE,CAAA;wBACrB,cAAc;wBACd,MAAA,IAAI,CAAC,YAAY,0CAAE,MAAM,GAAE;wBAC3B,IAAI,CAAC,aAAa,EAAE,CAAA;wBAEd,KAAK,GAAG,oBAAO,CAAC,KAAK,CAAA;wBAC3B,KAAA,IAAI,CAAA;wBAAS,qBAAM,IAAI,CAAC,UAAU,CAAC,cAAc,aAAC,KAAK,CAAC,WAAW,EAAE,0CAAE,KAAK,mCAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAA;;wBAAzF,GAAK,KAAK,GAAG,SAA4E,CAAA;wBACzF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;4BACb,sBAAO,uBAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAA;yBAClD;6BAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,eAAe;4BACpH,sBAAO,IAAI,CAAC,OAAO,EAAE,EAAA;yBACxB;wBACD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;wBAC1B,OAAO;wBACP,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAA;wBACnC,OAAO;wBACP,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;wBACxD,KAAK;wBACL,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAC;wBAC5B,OAAO;wBACP,IAAI,CAAC,UAAU,EAAE,CAAA;wBACjB,OAAO;wBACP,IAAI,CAAC,SAAS,EAAE,CAAA;wBAChB,OAAO;wBACP,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;;;;;KAClD;IAED,OAAO;IACO,8BAAO,GAArB;;;;;wBACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;wBACpC,IAAI,CAAC,KAAK,EAAE,CAAA;wBACZ,qBAAM,IAAI,CAAC,OAAO,EAAE,EAAA;;wBAApB,SAAoB,CAAA;wBACpB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;wBAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;wBAClB,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,mBAAmB,CAAC,CAAA;;;;;KAC3C;IAED,QAAQ;IACM,+BAAQ,GAAtB;;;;;;wBACI,IAAI,CAAC,UAAU,EAAE,CAAA;6BACb,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAApB,wBAAoB;wBAEV,qBAAM,SAAS,CAAC,WAAW,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAA1E,GAAG,GAAG,SAAoE;wBAC9E,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;4BACvB,sBAAM;yBACT;wBACG,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;wBAC/C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;wBAEpD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA;wBACjC,uBAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;wBAChJ,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;6BACzB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAf,wBAAe;6BAEX,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAvB,wBAAuB;wBACvB,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAvB,SAAuB,CAAA;;;6BAGvB,CAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAA,EAAtB,wBAAsB;wBAChB,qBAAM,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAAxE,GAAG,GAAG,SAAkE,CAAA;wBACxE,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;4BACrB,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;4BAC7D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;yBACxC;;;wBAGT,IAAI,CAAC,oBAAoB,EAAE,CAAA;;;oBAE/B,WAAW;oBACX,qBAAM,IAAI,CAAC,qBAAqB,EAAE;wBAClC,SAAS;sBADyB;;wBADlC,WAAW;wBACX,SAAkC,CAAA;wBAClC,SAAS;wBACT,qBAAM,IAAI,CAAC,eAAe,CAAC,oBAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAA;;wBADvE,SAAS;wBACT,SAAuE,CAAA;;;;;KAC1E;IAEa,iCAAU,GAAxB;;;;;;;wBACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;yBAC7B;wBACD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;wBACf,MAAM,GAAG,CAAC,CAAA;wBAChB,qBAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAO,CAAC;;;;;4CACvC,GAAG,GAAG,UAAQ,MAAM,cAAS,MAAM,SAAI,CAAC,CAAC,IAAI,SAAI,CAAC,CAAC,GAAK,CAAA;4CAC5D,IAAI,CAAC,CAAC,KAAK,EAAE;gDACT,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,KAAK,CAAA;6CACvB;4CACW,qBAAM,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;4CAA3D,GAAG,GAAG,SAAqD;4CACjE,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;gDACf,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;gDAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;gDAC3B,IAAI,CAAC,WAAW,CAAC,qBAAS,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;gDAC7F,IAAI,CAAC,YAAY,CAAC,wBAAc,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;gDAC5D,IAAI,CAAC,MAAM,GAAG,0BAAe,GAAG,IAAI,CAAC,CAAC,CAAA;gDACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;6CACxB;;;;iCACJ,CAAC,CAAC,EAAA;;wBAdH,SAcG,CAAA;;;;;KACN;IAEO,iCAAU,GAAlB;;QACI,MAAA,IAAI,CAAC,UAAU,0CAAE,OAAO,GAAE;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,MAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,GAAE;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;SAC7B;QACD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;IACzB,CAAC;IAED,QAAQ;IACM,mCAAY,GAA1B;;;;;;;wBACU,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;wBAChC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;4BACtC,sBAAO,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,CAAC;oCAC3B,IAAM,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC,CAAA;oCAC3G,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;oCACvC,IAAI,CAAC,MAAM,GAAG,0BAAe,GAAG,IAAI,CAAC,CAAC,CAAA;gCAC1C,CAAC,CAAC,EAAA;yBACL;wBACW,qBAAM,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAArE,GAAG,GAAG,SAA+D;wBAC3E,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;4BACrB,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;gCAChB,IAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,KAAI,CAAC,SAAS,CAAC,EAAE,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC,CAAA;gCACjI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;gCACvC,IAAI,CAAC,MAAM,GAAG,0BAAe,GAAG,IAAI,CAAC,CAAC,CAAA;gCACtC,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BAC1B,CAAC,CAAC,CAAA;yBACL;;;;;KACJ;IAEO,kCAAW,GAAnB;QACI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;SAC9B;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;IAC1B,CAAC;IAED,SAAS;IACK,sCAAe,GAA7B,UAA8B,IAAY;;;;;;;wBACtC,IAAI,CAAC,IAAI,EAAE;4BACP,sBAAO,IAAI,CAAC,cAAc,EAAE,EAAA;yBAC/B;wBACK,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA;wBACnC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;4BAC5E,sBAAO,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,CAAC;oCAC3B,IAAM,IAAI,GAAG,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC,CAAA;oCAC9G,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;oCACrB,IAAI,CAAC,MAAM,GAAG,0BAAe,GAAG,IAAI,CAAC,CAAC,CAAA;gCAC1C,CAAC,CAAC,EAAA;yBACL;wBACD,IAAI,CAAC,cAAc,EAAE,CAAA;wBACT,qBAAM,SAAS,CAAC,WAAW,CAAC,sBAAsB,GAAG,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAArF,GAAG,GAAG,SAA+E;wBAC3F,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;4BACrB,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;gCAChB,IAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,KAAI,CAAC,SAAS,CAAC,EAAE,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC,CAAA;gCACjI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAA;gCAC1B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gCACrB,IAAI,CAAC,MAAM,GAAG,0BAAe,GAAG,IAAI,CAAC,CAAC,CAAA;gCACtC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BAC7B,CAAC,CAAC,CAAA;yBACL;;;;;KACJ;IAEO,qCAAc,GAAtB;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;SACjC;QACD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED,WAAW;IACG,4CAAqB,GAAnC;;;;;;wBACI,IAAI,CAAC,oBAAoB,EAAE,CAAA;wBACrB,UAAU,GAAsC,CAAC,CAAA;wBACjD,IAAI,GAAG,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;wBAC3F,GAAG,GAAG,mBAAiB,UAAU,uBAAkB,QAAU,CAAA;wBACvD,qBAAM,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAA3D,GAAG,GAAG,SAAqD;wBACjE,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;4BACf,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;4BAC5D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,oBAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,oBAAS,CAAC,CAAA;4BAC9E,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;yBAClC;;;;;KACJ;IAEO,2CAAoB,GAA5B;QACI,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACpC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;SACvC;QACD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAA;IACnC,CAAC;IAED,QAAQ;IACM,iCAAU,GAAxB,UAAyB,gBAA0B;;;;;;;wBAC/C,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;wBAEZ,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;wBAChC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAxB,CAAwB,CAAC,CAAA;wBAC7C,KAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;4BAC9C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gCACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;6BAC/C;yBACJ;wBACD,qBAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAnB,CAAmB,CAAC,CAAC,EAAA;;wBAAvD,SAAuD,CAAA;;;;;KAC1D;IAEa,kCAAW,GAAzB,UAA0B,IAAc;;;;;;;wBACpC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;4BAClB,sBAAO,IAAI,EAAA;yBACd;wBACG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;wBACrD,IAAI,KAAK,EAAE;4BACP,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;4BAC3B,sBAAO,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAA;yBAC9C;wBACW,qBAAM,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAA3E,GAAG,GAAG,SAAqE;wBACjF,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;4BAC1B,sBAAO,IAAI,EAAA;yBACd;6BAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;4BACtC,sBAAO,IAAI,EAAA,CAAC,cAAc;yBAC7B;6BAAM,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;4BAC1C,sBAAO,IAAI,EAAA;yBACd;wBACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;wBAC3B,KAAK,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,uBAAa,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,oBAAS,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;wBACtJ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;wBACvB,sBAAO,KAAK,EAAA;;;;KACf;IAEO,kCAAW,GAAnB;QACI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAA;SAC5B;QACD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;IACtB,CAAC;IAEO,iCAAU,GAAlB,UAAmB,IAAmB;QAClC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,KAAK,EAAE,CAAA;YACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SACjC;IACL,CAAC;IAED,SAAS;IACD,mCAAY,GAApB,UAAqB,EAAU;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,CAAA;SAC1D;IACL,CAAC;IAED,SAAS;IACD,2CAAoB,GAA5B;QACI,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAA;YACzC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,wBAAa,EAAE;gBACrC,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;gBACtE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;aACtC;iBAAM,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE;gBAC/B,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;gBACtE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;aACtC;iBAAM;gBACH,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBACjD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;aACtC;YACD,IAAI,CAAC,MAAM,GAAG,CAAC,0BAAe,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,oBAAS,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;SACrF;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAM,GAAG,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACvD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;YAC9C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;SACtD;IACL,CAAC;IAED,SAAS;IACD,oCAAa,GAArB,UAAsB,IAAe;QACjC,QAAQ;QACR,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;QACpC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAA;QAC5B,WAAW;QACX,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,UAAU;QACV,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC7C,SAAS;QACT,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IACzE,CAAC;IAED,iBAAiB;IACT,4CAAqB,GAA7B;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;SAC7B;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YACrC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAA;SAC9B;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;SAC1G;IACL,CAAC;IAED,SAAS;IACD,qCAAc,GAAtB;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAA;QACpC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;IACjC,CAAC;IAED,SAAS;IACD,oCAAa,GAArB;;QACI,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,KAAK,SAAG,IAAI,CAAC,IAAI,0CAAE,QAAQ,EAAE,CAAA;YAC/D,IAAI,KAAK,IAAI,KAAK,GAAG,iBAAS,CAAC,KAAK,EAAE;gBAClC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAA;aACxC;SACJ;QACD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QACnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,KAAK,EAAE,CAAA;QACtD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;IAC9B,CAAC;IAEO,iCAAU,GAAlB;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAA;SAC3B;QACD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;IACrB,CAAC;IAED,QAAQ;IACM,gCAAS,GAAvB;;;;;gBAEU,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,MAAM,GAAG,EAAE,CAAA;gBACnD,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,IAAI,EAA5B,CAA4B,CAAC,CAAA;gBAChD,KAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBACvC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;oBACvB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;wBACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;qBAC7C;iBACJ;gBACD,sBAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAC,EAAA;;;KACzD;IAEa,iCAAU,GAAxB,UAAyB,IAAa;;;;;;;wBAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE;4BAC3B,sBAAO,IAAI,EAAA;yBACd;wBACG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;wBACnD,IAAI,CAAC,IAAI,EAAE;yBACV;6BAAM,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,cAAc,EAAE,EAAE;4BACxF,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;4BAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;yBACvB;6BAAM,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;4BACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;4BAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;4BACpB,sBAAO,IAAI,EAAA;yBACd;6BAAM;4BACH,sBAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAA;yBAC3B;wBACW,qBAAM,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAA3E,GAAG,GAAG,SAAqE;wBACjF,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;4BAC1B,sBAAO,IAAI,EAAA;yBACd;6BAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;4BAC/B,sBAAO,IAAI,EAAA,CAAC,QAAQ;yBACvB;6BAAM,IAAI,IAAI,CAAC,MAAM,YAAK,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAA,EAAE;4BAC1C,sBAAO,IAAI,EAAA;yBACd;wBAEK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;wBACpB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;wBACnE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;4BACvB,sBAAO,IAAI,EAAA;yBACd;wBACD,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,kBAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;wBACxG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;wBAC7B,sBAAO,IAAI,EAAA;;;;KACd;IAEO,gCAAS,GAAjB,UAAkB,IAAc,EAAE,OAAiB;QAC/C,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;SACtB;IACL,CAAC;IAED,UAAU;IACF,uCAAgB,GAAxB;;QACI,OAAO,CAAC,QAAC,IAAI,CAAC,aAAa,0CAAE,IAAI,CAAA,CAAA;IACrC,CAAC;IAED,UAAU;IACF,sCAAe,GAAvB;;QACI,OAAO,CAAC,QAAC,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAA,CAAA;IACpC,CAAC;IAED,OAAO;IACC,gCAAS,GAAjB,UAAkB,MAAe;QAAjC,iBA8CC;QA7CG,IAAM,UAAU,GAAG,mCAAmC,CAAA,CAAC,CAAA;QACvD,IAAM,IAAI,GAAG,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,eAAe,GAAG,6BAAkB,CAAC,UAAU,CAAC,CAAA;QAChH,IAAM,SAAS,GAAsB,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,CAAA;QAClG,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QACpE,WAAW;QACX,uBAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;QACnC,EAAE;QACF,iDAAiD;QACjD,0BAA0B;QAC1B,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA;QAC5E,IAAM,MAAM,GAAG,qBAAS,CAAC,qBAAqB,CAAC,MAAM,EAAE,oBAAO,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAA;QACvF,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAA;QAClC,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,KAAK,EAAE,CAAC;YACnC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,KAAI,CAAC,UAAU,CAAC,CAAC,CAAA;YACtE,IAAM,EAAE,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAA;YACpD,IAAM,KAAK,GAAG,EAAE,CAAC,IAAI,GAAG,qBAAS,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAA;YACrE,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;YACtB,EAAE,CAAC,WAAW,CAAC,qBAAS,CAAC,eAAe,CAAC,KAAK,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC,CAAA;YACnE,IAAI,qBAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE,KAAK;gBAC3C,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAA;aAC7C;iBAAM,IAAI,qBAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM;gBACtD,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAA;gBACjD,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,cAAc,CAAC,CAAA;gBAC3E,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;gBACjD,IAAI,KAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC,EAAE;oBACrC,IAAM,EAAE,GAAG,qBAAS,CAAC,cAAc,CAAC,KAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,KAAI,CAAC,YAAY,CAAC,CAAA;oBAC3E,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;oBACd,EAAE,CAAC,MAAM,GAAG,KAAI,CAAC,eAAe,EAAE,CAAA;iBACrC;aACJ;iBAAM,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM;gBACxB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,cAAc,CAAC,CAAA;gBAC3E,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;oBAC1E,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;iBAC5B;qBAAM,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;oBAC9C,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;iBACtB;aACJ;iBAAM,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ;gBACvC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;aACjG;iBAAM;gBACH,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAA;aAC7C;QACL,CAAC,CAAC,CAAA;QACF,QAAQ;QACR,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;IAChD,CAAC;IAEO,kCAAW,GAAnB,UAAoB,IAAY;QAC5B,OAAO,qBAAS,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;IAC/D,CAAC;IAED,OAAO;IACC,iCAAU,GAAlB,UAAmB,aAAsB;QACrC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE;YAC/B,OAAM;SACT;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAA;QACjD,IAAI,CAAC,KAAK,EAAE;YACR,OAAM;SACT;aAAM,IAAI,qBAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YAClE,OAAM;SACT;QACD,YAAY;QACZ,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QACnE,IAAI,qBAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YAC9D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;SAC7B;aAAM;YACH,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;SAChC;IACL,CAAC;IAED,SAAS;IACD,qCAAc,GAAtB,UAAuB,KAAc;QACjC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;YAC1E,OAAM;SACT;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAA;QACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAA9C,CAA8C,CAAC,EAAE;YACvH,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;YAChG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SACvB;IACL,CAAC;IAED,SAAS;IACD,uCAAgB,GAAxB,UAAyB,KAAc;QACnC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YACpD,OAAM;SACT;aAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;YACzC,IAAI,CAAC,aAAa,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAA;YAClD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,CAAC,CAAA;SACjF;IACL,CAAC;IAED,MAAM;IACE,kCAAW,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YACvE,OAAM;SACT;QACD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE;YACxB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACxD,IAAI,CAAC,KAAK,EAAE;aACX;iBAAM,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACrD,uBAAU,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAA;aAC9C;iBAAM;gBACH,uBAAU,CAAC,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;aACtD;SACJ;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE;YAC7B,uBAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;SAClE;aAAM;YACH,uBAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;SACzD;IACL,CAAC;IAEO,sCAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAM,OAAK,GAAG,IAAI,CAAC,KAAK,CAAA;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,sBAAY,EAAE,CAAC,IAAI,CAAC,UAAC,CAAS,EAAE,CAAS,IAAK,OAAA,OAAK,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAzE,CAAyE,CAAC,CAAA;SACnJ;QACD,OAAO,IAAI,CAAC,YAAY,CAAA;IAC5B,CAAC;IAED,OAAO;IACO,+BAAQ,GAAtB,UAAuB,KAAc;;;;;6BAC7B,IAAI,CAAC,YAAY,EAAjB,wBAAiB;wBACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;wBACzB,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;wBAC5C,qBAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,EAAA;;wBAAhD,SAAgD,CAAA;wBAChD,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;4BACjB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;4BAC1B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;yBAChD;;;;;;KAER;IAED,SAAS;IACK,kCAAW,GAAzB,UAA0B,IAAc,EAAE,KAAc;;;;;;;wBACpD,IAAI,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAA,IAAI,OAAA,IAAI,CAAC,IAAI,0CAAE,QAAQ,QAAO,iBAAS,CAAC,SAAS,EAAE;4BAC9D,sBAAM;yBACT;wBACK,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;wBAChB,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;wBACvB,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,oBAAO,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAC,CAAS,EAAE,CAAS,IAAK,OAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAvE,CAAuE,CAAC,CAAA;wBACrJ,qBAAM,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,EAAA;;wBAAnC,MAAM,GAAG,SAA0B;wBACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;4BACzC,sBAAM;yBACT;6BAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;4BAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;yBAClC;wBACK,IAAI,GAAG,qBAAS,CAAC,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;wBACnD,IAAI,CAAC,WAAW,CAAC,iBAAS,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;wBAC5E,qBAAM,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,EAAA;;wBAA3B,SAA2B,CAAA;wBAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG,iBAAS,CAAC,KAAK,EAAE;4BACnC,IAAI,CAAC,WAAW,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAA;yBACnC;;;;;KACJ;IAED,OAAO;IACO,iCAAU,GAAxB;;;;;;;;wBACI,IAAI,QAAC,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAA,EAAE;4BAC1B,sBAAM;yBACT;wBACK,IAAI,GAAG,IAAI,CAAC,YAAY,CAAA;wBACxB,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAA;wBAC3B,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAA;wBACxC,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;wBAE1B,KAAK,GAAe,EAAE,EAAE,UAAU,GAAG,EAAE,CAAA;wBAC7C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;4BAChB,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;6BAClB;iCAAM,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,OAAO,MAAK,OAAO,EAAE;gCACpC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;6BAChB;iCAAM;gCACH,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAA;6BAClC;wBACL,CAAC,CAAC,CAAA;wBACI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAA;wBAC1B,IAAI,KAAK,KAAK,CAAC,EAAE;4BACb,sBAAM;yBACT;wBAEK,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;wBACtE,eAAe;wBACf,MAAM,CAAC,MAAM,CAAC,UAAA,CAAC;4BACX,IAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAzB,CAAyB,CAAC,CAAA;4BACzD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gCACV,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gCAClB,OAAO,IAAI,CAAA;6BACd;4BACD,OAAO,KAAK,CAAA;wBAChB,CAAC,CAAC,CAAA;wBACF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;4BACrB,sBAAM;yBACT;wBACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;wBACzB,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;wBAC5C,qBAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAA7B,CAA6B,CAAC,CAAC,EAAA;;wBAAtE,SAAsE,CAAA;wBACtE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;4BACjB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;4BAC1B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;yBAChD;;;;;KACJ;IAED,YAAY;IACE,kCAAW,GAAzB;;;;;;;wBACI,IAAI,QAAC,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAA,IAAI,IAAI,CAAC,aAAa,EAAE;4BAChD,sBAAM;yBACT;wBACK,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAA;wBAC7B,KAAK,GAAG,EAAE,CAAA;wBACV,SAAS,GAAG,EAAE,CAAA;wBACpB,KAAS,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;4BACtB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;4BAC1B,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;4BAChC,SAAS,CAAC,IAAI,CAAC;gCACX,GAAG,EAAE,IAAI,CAAC,GAAG;gCACb,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE;6BACxB,CAAC,CAAA;4BACF,IAAI,QAAC,IAAI,CAAC,KAAK,0CAAE,MAAM,CAAC,KAAK,EAAC,EAAE;gCAC5B,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;6BACvD;yBACJ;6BACG,CAAA,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,EAAhB,wBAAgB;wBACM,qBAAM,qBAAS,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,EAAA;;wBAAjH,KAAgB,SAAiG,EAA/G,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;4BACzB,sBAAM;yBACT;;;wBAEL,uBAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;wBACnC,KAAS,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;4BAC5B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAA;yBAChC;wBACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;wBACxC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAnB,CAAmB,CAAC,CAAA;wBAC7C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAnB,CAAmB,CAAC,CAAA;wBAC5C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,KAAK,EAAhB,CAAgB,CAAC,CAAA;wBACrD,IAAI,CAAC,aAAa,EAAE,CAAA;;;;;KACvB;IAED,6BAAM,GAAN,UAAO,EAAU;;QACb,IAAI,QAAC,IAAI,CAAC,KAAK,0CAAE,MAAM,CAAA,EAAE;YACrB,OAAM;SACT;QACD,aAAa;QACb,wBAAwB;IAC5B,CAAC;IAEO,qCAAc,GAAtB;QACI,IAAM,KAAK,GAAG,qBAAS,CAAC,eAAe,CAAC,uBAAU,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QAC1F,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB,KAAK,uBAAU,CAAC,SAAS,EAAE;YAChF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;SACxB;IACL,CAAC;IAlxCgB,YAAY;QADhC,OAAO;OACa,YAAY,CAmxChC;IAAD,mBAAC;CAnxCD,AAmxCC,CAnxCyC,EAAE,CAAC,YAAY,GAmxCxD;kBAnxCoB,YAAY", "file": "", "sourceRoot": "/", "sourcesContent": ["import EventType from \"../../common/event/EventType\";\nimport { mapHelper } from \"../../common/helper/MapHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport BuildObj from \"../../model/area/BuildObj\";\nimport AreaCenterModel from \"../../model/area/AreaCenterModel\";\nimport AreaObj from \"../../model/area/AreaObj\";\nimport BuildCmpt from \"./BuildCmpt\";\nimport { animHelper } from \"../../common/helper/AnimHelper\";\nimport PawnCmpt from \"./PawnCmpt\";\nimport PawnObj from \"../../model/area/PawnObj\";\nimport ArmyObj from \"../../model/area/ArmyObj\";\nimport { cameraCtrl } from \"../../common/camera/CameraCtrl\";\nimport MapTouchCmpt from \"../cmpt/MapTouchCmpt\";\nimport ClickTouchCmpt from \"../cmpt/ClickTouchCmpt\";\nimport NetEvent from \"../../common/event/NetEvent\";\nimport HPBarCmpt from \"./HPBarCmpt\";\nimport { AREA_DI_COLOR_CONF, AREA_MAX_ZINDEX, BUILD_SMITHY_NID, CITY_MAIN_NID, TILE_SIZE } from \"../../common/constant/Constant\";\nimport BaseBuildCmpt from \"./BaseBuildCmpt\";\nimport { PawnState, PreferenceKey } from \"../../common/constant/Enums\";\nimport { ecode } from \"../../common/constant/ECode\";\nimport SearchCircle from \"../../common/astar/SearchCircle\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport SelectCellCmpt from \"../cmpt/SelectCellCmpt\";\nimport { netHelper } from \"../../common/helper/NetHelper\";\nimport { guideHelper } from \"../../common/helper/GuideHelper\";\nimport AncientObj from \"../../model/main/AncientObj\";\nimport { AreaLandColorInfo } from \"../../common/constant/DataType\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class AreaWindCtrl extends mc.BaseWindCtrl {\n\n    //@autocode property begin\n    private mapNode_: cc.Node = null // path://root/map_n\n    private gridNode_: cc.Node = null // path://root/map_n/grid_n\n    private skillDiNode_: cc.Node = null // path://root/map_n/skill_di_n\n    private buildNode_: cc.Node = null // path://root/map_n/build_n\n    private selectPawnNode_: cc.Node = null // path://root/select_pawn_n\n    private roleNode_: cc.Node = null // path://root/role_n\n    private topLayerNode_: cc.Node = null // path://root/top_layer_n\n    private editJiantouNode_: cc.Node = null // path://root/edit_jiantou_n\n    private weakGuideNode_: cc.Node = null // path://root/weak_guide_n\n    //@end\n\n    private readonly PAWN_SIZE: cc.Vec2 = cc.v2(1, 1)\n\n    private diNode: cc.Node = null\n    private maskNode: cc.Node = null\n    private touchCmpt: MapTouchCmpt = null\n\n    private model: AreaObj = null\n    private areaCenter: AreaCenterModel = null\n    private centre: cc.Vec2 = cc.v2()\n    private preCameraZoomRatio: number = 0\n    private areaSize: cc.Vec2 = cc.v2() //战场大小\n    private buildSize: cc.Vec2 = cc.v2() //建筑区域\n    private areaActSize: cc.Vec2 = cc.v2() //战场的实际大小\n    private borderSize: cc.Vec2 = cc.v2() //地图边框宽度\n    private buildOrigin: cc.Vec2 = cc.v2() //建筑起点\n\n    private walls: cc.Node[] = [] //城墙列表\n    private flames: cc.Node[] = [] //火焰列表\n    private alliFlags: cc.Node[] = [] //联盟旗帜\n    private areaOutDecorate: cc.Node[] = [] //装饰\n    private builds: BaseBuildCmpt[] = [] //建筑列表\n    private buildMap: { [key: string]: number } = {}\n    private pawns: PawnCmpt[] = [] //士兵列表\n    private pawnMap: any = {}\n    private wallLvNode: cc.Node = null\n    private hpBar: HPBarCmpt = null //血条\n\n    private currEditBuild: BuildCmpt = null //当前编辑的建筑\n    private currEditPawn: PawnCmpt = null //当前编辑的士兵\n    private searchCircle: SearchCircle = null\n    private isPawnMoveing: boolean = false //当前是否士兵移动中\n    private editPawns: { [key: string]: PawnCmpt } = {} //编辑过的士兵列表\n    private tempSeasonType: number = 0\n\n    private _temp_vec2_0: cc.Vec2 = cc.v2()\n    private _temp_vec2_1: cc.Vec2 = cc.v2()\n    private _temp_vec2_2: cc.Vec2 = cc.v2()\n    private _temp_vec2_3: cc.Vec2 = cc.v2()\n\n    public listenEventMaps() {\n        return [\n            { [NetEvent.NET_RECONNECT]: this.onNetReconnect, enter: true },\n            { [EventType.REENTER_AREA_WIND]: this.onReenterAreaWind, enter: true },\n            { [EventType.SHOW_BUILD_JIANTOU]: this.onShowBuildJiantou, enter: true },\n            { [EventType.LONG_PRESS_BUILD]: this.onLongPressBuild, enter: true },\n            { [EventType.MOVE_BUILD]: this.onMoveBuild, enter: true },\n            { [EventType.CLICK_EDIT_BUILD_MENU]: this.onClickEditBuildMenu, enter: true },\n            { [EventType.EDIT_PAWN_POS]: this.onEditPawnPos, enter: true },\n            { [EventType.CLICK_EDIT_PAWN_MENU]: this.onClickEditPawnMenu, enter: true },\n            { [EventType.ADD_BUILD]: this.onAddBuild, enter: true },\n            { [EventType.REMOVE_BUILD]: this.onRemoveBuild, enter: true },\n            { [EventType.UPDATE_BUILD_LV]: this.onUpdateBuildLv, enter: true },\n            { [EventType.UPDATE_BUILD_POINT]: this.onUpdateBuildPoint, enter: true },\n            { [EventType.UPDATE_BUILDS]: this.onUpdateBuilds, enter: true },\n            { [EventType.UPDATE_AREA_HP]: this.onUpdateAreaHp, enter: true },\n            { [EventType.UPDATE_ANCIENT_INFO]: this.onUpdateAncientInfo, enter: true },\n            { [EventType.ADD_ARMY]: this.onAddArmy, enter: true },\n            { [EventType.ADD_PAWN]: this.onAddPawn, enter: true },\n            { [EventType.REMOVE_ARMY]: this.onRemoveArmy, enter: true },\n            { [EventType.UPDATE_ARMY]: this.onUpdateArmy, enter: true },\n            { [EventType.UPDATE_ALL_ARMY]: this.onUpdateAllArmy, enter: true },\n            { [EventType.REMOVE_PAWN]: this.onRemovePawn, enter: true },\n            { [EventType.AREA_BATTLE_BEGIN]: this.onAreaBattleBegin, enter: true },\n            { [EventType.AREA_BATTLE_END]: this.onAreaBattleEnd, enter: true },\n            { [EventType.AREA_MAIN_HIT]: this.onAreaMainHit, enter: true },\n            { [EventType.PLAY_FLUTTER_HP]: this.onPlayFlutterHp, enter: true },\n            { [EventType.PLAY_FLUTTER_ANGER]: this.onPlayFlutterAnger, enter: true },\n            { [EventType.PLAY_BULLET_FLY]: this.onPlayBulletFly, enter: true },\n            { [EventType.PLAY_BATTLE_EFFECT]: this.onPlayBattleEffect, enter: true },\n            { [EventType.PLAY_BATTLE_SFX]: this.onPlayBattleSfx, enter: true },\n            { [EventType.PLAY_BATTLE_SCENE_SHAKE]: this.onPlayBattleSceneShake, enter: true },\n            { [EventType.FOCUS_PAWN]: this.onFocusPawn, enter: true },\n            { [EventType.UPDATE_BT_QUEUE]: this.onUpdateBtQueue, enter: true },\n            { [EventType.UPDATE_PAWN_DRILL_QUEUE]: this.onUpdatePawnDrillQueue, enter: true },\n            { [EventType.UPDATE_PAWN_LVING_QUEUE]: this.onUpdatePawnDrillQueue, enter: true },\n            { [EventType.UPDATE_PAWN_CURING_QUEUE]: this.onUpdatePawnDrillQueue, enter: true },\n            { [EventType.CHANGE_SHOW_PAWN_LV]: this.onChangeShowPawnLv, enter: true },\n            { [EventType.CHANGE_SHOW_PAWN_EQUIP]: this.onChangeShowPawnEquip, enter: true },\n            { [EventType.CHANGE_PAWN_EQUIP]: this.onChangePawnEquip, enter: true },\n            { [EventType.CHANGE_PAWN_SKIN]: this.onChangePawnSkin, enter: true },\n            { [EventType.CHANGE_PAWN_PORTRAYAL]: this.onChangePawnPortrayal, enter: true },\n            { [EventType.FORGE_EQUIP_BEGIN]: this.onForgeEquipBegin, enter: true },\n            { [EventType.FORGE_EQUIP_COMPLETE]: this.onForgeEquipComplete, enter: true },\n            { [EventType.WEAK_GUIDE_SHOW_NODE_CHOOSE]: this.onWeakGuideShowNodeChoose, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.setParam({ isClean: false })\n        this.areaCenter = this.getModel('areaCenter')\n        this.diNode = this.mapNode_.FindChild('di')\n        this.maskNode = this.mapNode_.FindChild('mask')\n        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt)\n        this.selectPawnNode_.active = false\n        this.editJiantouNode_.active = false\n        this.gridNode_.active = false\n        // 加载UI\n        await viewHelper.preloadPnl('area/AreaUI')\n    }\n\n    public async onReady() {\n        this.model = await this.areaCenter.reqAreaByIndex(gameHpr.world.getLookCell()?.index ?? -1)\n        if (!this.model) {\n            return\n        }\n        this.areaCenter.setLookArea(this.model)\n        // 区域大小\n        this.areaSize.set(this.model.areaSize)\n        this.buildSize.set(this.model.buildSize)\n        // 获取地图边框的宽度 至少都有2格\n        this.model.getBorderSize(this.borderSize)\n        // 重新计算地图的真实大小\n        this.borderSize.mul(2, this.areaActSize).addSelf(this.areaSize)\n        // 计算建筑的起点\n        this.model.buildOrigin.add(this.borderSize, this.buildOrigin)\n        // 分帧创建地块\n        const range = gameHpr.world.getMaxTileRange(), count = (range.x * 2 + 1) * (range.y * 2 + 1)\n        this.diNode.Items(count)\n        // 初始化城墙\n        await this.initWall()\n    }\n\n    public onEnter(reenter: boolean) {\n        if (!this.model) {\n            viewHelper.gotoWind(gameHpr.world.getSceneKey())\n            if (gameHpr.net.isConnected()) {\n                viewHelper.showMessageBox(ecode.UNKNOWN)\n            }\n            return\n        }\n        this.buildNode_.Data = true\n        this.topLayerNode_.Data = true\n        this.model.setActive(true)\n        this.tempSeasonType = gameHpr.world.getSeasonType()\n        // 刷新宝箱红点\n        this.model.updateTreasureReddot()\n        // 设置中心位置\n        this.areaActSize.mul(0.5, this.centre).subSelf(cc.v2(0.5, 0.5))\n        // 初始化相机位置\n        const zr = gameHpr.user.getLocalPreferenceData(PreferenceKey.AREA_ZOOM_RATIO)\n        cameraCtrl.init(mapHelper.getPixelByPoint(this.centre), this.areaActSize, cc.Vec2.ZERO, zr)\n        // 绘制士兵\n        this.initPawns()\n        // 绘制建筑\n        this.initBuilds()\n        // 刷新地图\n        this.updateMap(this.centre.floor())\n        // UI\n        if (reenter) {\n            this.emit(EventType.UPDATE_AREA_BATTLE_TIME_UI, this.model.index)\n        } else {\n            viewHelper.showPnl('area/AreaUI', this.model)\n        }\n        //\n        this.touchCmpt.init(this.onClickMap.bind(this))\n        //\n        gameHpr.playAreaBgm(this.model.isBattleing())\n    }\n\n    public onLeave() {\n        gameHpr.user.setLocalPreferenceData(PreferenceKey.AREA_ZOOM_RATIO, cameraCtrl.zoomRatio)\n        viewHelper.hidePnl('area/AreaUI')\n        this.touchCmpt.clean()\n        gameHpr.world.setLookCell(null)\n        this.clean()\n        this.cleanPawns()\n        resHelper.cleanNodeChildren(this.diNode)\n        resHelper.cleanNodeChildren(this.maskNode)\n        this.buildNode_.removeAllChildren()\n        this.buildNode_.Data = false\n        this.topLayerNode_.removeAllChildren()\n        this.topLayerNode_.Data = false\n        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key)\n        animHelper.clean()\n        assetsMgr.releaseTempResByTag(this.key)\n        audioMgr.releaseByMod('build')\n        audioMgr.releaseByMod('pawn')\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    private onNetReconnect() {\n        this.reinit()\n    }\n\n    private onReenterAreaWind() {\n        return this.reenter()\n    }\n\n    // 显示编辑家具的箭头\n    private onShowBuildJiantou(item: BuildCmpt, index: number) {\n        this.editJiantouNode_.active = !!item\n        if (item) {\n            this.editJiantouNode_.setPosition(item.getBodyOffsetTopPosition(68))\n            this.editJiantouNode_.Component(cc.MultiFrame).setFrame(index)\n        }\n    }\n\n    // 长按选中一个家具\n    private onLongPressBuild(item: BuildCmpt) {\n        // 设置可以点击选择了\n        this.builds.forEach(m => item.uid !== m.uid && m.setCanClickSelect(true))\n        this.pawns.forEach(m => m.setCanClick(false))\n        // 显示编辑UI\n        this.openEditBuild(item)\n    }\n\n    // 移动建筑\n    private onMoveBuild(item: BuildCmpt, pos: cc.Vec2) {\n        const point = item.getActPointByPixel(pos)\n        this.model.amendBuildPoint(point, item.data.size) //修正一下\n        item.setOffsetPositionByPoint(point)\n        item.updateEditState(this.model.getBuildGroundPointMap(), point)\n    }\n\n    // 点击编辑建筑菜单\n    private onClickEditBuildMenu(type: string) {\n        if (!this.currEditBuild || !this.currEditBuild.data /* || this.model.isBattleing() */) {\n            return\n        }\n        const item = this.currEditBuild\n        if (type === 'cancel') { //取消\n            item.cancel()\n        } else if (type === 'ok') { //确定\n            if (item.editState) {\n                return viewHelper.showAlert(item.editState)\n            }\n            item.confirm(item.getActPointByPixel(item.getTempPosition()))\n            // audioMgr.playSFX('area/sound06')\n        }\n        viewHelper.hidePnl('area/EditBuild') //隐藏编辑UI\n        this.builds.forEach(m => m.setCanClickSelect(false)) //关闭点击选择\n        this.pawns.forEach(m => m.setCanClick(true))\n        item.syncZindex() //同步zindex\n        this.closeEditBuild()\n    }\n\n    // 编辑士兵\n    private onEditPawnPos(index: number, uid: string) {\n        if (this.model.index !== index || this.model.isBattleing()) {\n            return\n        }\n        const pawn = this.pawns.find(m => m.uid === uid)\n        if (pawn) {\n            this.builds.forEach(m => m.setCanClick(false))\n            this.pawns.forEach(m => m.setCanClick(false))\n            this.maskNode.children.forEach(m => m.active = !!m.Data)\n            // 行动中\n            pawn.data.actioning = true\n            // 显示pnl\n            viewHelper.showPnl('area/EditPawn', pawn)\n            this.currEditPawn = pawn\n            this.selectPawnNode_.Component(SelectCellCmpt).open(this.currEditPawn.getTempPosition(), this.PAWN_SIZE)\n        }\n    }\n\n    // 点击编辑士兵的菜单\n    private onClickEditPawnMenu(type: string) {\n        if (!this.currEditPawn || !this.currEditPawn.data || this.model.isBattleing()) {\n            viewHelper.hidePnl('area/EditPawn')\n            this.builds.forEach(m => m.setCanClick(true))\n            this.pawns.forEach(m => m.setCanClick(true))\n            this.maskNode.children.forEach(m => m.active = false)\n            this.closeEditPawn()\n        } else if (type === 'gather') { //集合\n            this.gatherPawn()\n        } else if (type === 'ok') {\n            this.editPawnEnd()\n        }\n    }\n\n    // 添加建筑\n    private onAddBuild(data: BuildObj) {\n        if (data.aIndex === this.model?.index) {\n            const isOwner = this.model.isOwner()\n            this.createBuild(data).then(item => {\n                if (item && this.isActive() && isOwner) {\n                    const body = item.getBody()\n                    // 摄像机移动到这个位置来\n                    cameraCtrl.setTargetOnce(body)\n                    // 扫光\n                    // animHelper.playFlashLight([body])\n                }\n            })\n        }\n    }\n\n    // 删除建筑\n    private onRemoveBuild(data: BuildObj) {\n        if (data.aIndex === this.model?.index) {\n            const build = this.builds.remove('uid', data.uid)\n            if (build) {\n                this.cleanBuild(build)\n                assetsMgr.releaseTempRes(data.getPrefabUrl(), this.key)\n            }\n        }\n    }\n\n    // 刷新建筑等级\n    private onUpdateBuildLv(data: BuildObj) {\n        if (data.aIndex !== this.model?.index) {\n        } else if (data.uid === this.model.wall.uid) {\n            this.updateWallLv(data.lv)\n        } else {\n            this.builds.find(m => m.uid === data.uid)?.updateLv(data.lv)\n        }\n    }\n\n    // 刷新建筑位置\n    private onUpdateBuildPoint(data: BuildObj) {\n        if (data.aIndex === this.model?.index) {\n            this.builds.find(m => m.uid === data.uid)?.syncPoint()\n        }\n    }\n\n    // 刷新血量\n    private onUpdateAreaHp(index: number) {\n        if (index === this.model?.index) {\n            this.hpBar?.init(this.model)\n        }\n    }\n\n    // 刷新遗迹信息\n    private onUpdateAncientInfo(data: AncientObj) {\n        if (data.index !== this.model?.index) {\n            return\n        }\n        const build = this.builds.find(m => m.data.isAncient())\n        if (build) {\n            build.updateLv(data.lv)\n            build.updateUpLvAnim()\n        }\n    }\n\n    // 刷新城市\n    private onUpdateBuilds(index: number) {\n        if (index === this.model?.index) {\n            this.initBuilds()\n        }\n    }\n\n    // 添加军队\n    private onAddArmy(data: ArmyObj) {\n        if (data.aIndex === this.model?.index) {\n            data.pawns.forEach(m => this.createPawn(m))\n        }\n    }\n\n    // 删除军队\n    private onRemoveArmy(data: ArmyObj) {\n        if (data.aIndex === this.model?.index) {\n            data.pawns.forEach(m => {\n                const i = this.pawns.findIndex(p => p.uid === m.uid && p.data?.armyUid === data.uid)\n                if (i !== -1) {\n                    this.cleanPawn(this.pawns.splice(i, 1)[0], true)\n                }\n            })\n        }\n    }\n\n    // 更新军队信息\n    private onUpdateArmy(data: ArmyObj) {\n        if (data.aIndex !== this.model?.index) {\n            return\n        }\n        // 先删除没有的\n        for (let i = this.pawns.length - 1; i >= 0; i--) {\n            const m = this.pawns[i]\n            if (m.data?.armyUid !== data.uid) {\n                continue\n            } else if (!data.pawns.has('uid', m.uid)) {\n                this.pawns.splice(i, 1)\n                this.cleanPawn(m, true)\n            }\n        }\n        data.pawns.forEach(m => this.createPawn(m))\n    }\n\n    // 更新所有军队\n    private onUpdateAllArmy(index: number) {\n        if (this.model.index === index) {\n            this.initPawns()\n        }\n    }\n\n    // 添加士兵\n    private onAddPawn(index: number, data: PawnObj) {\n        if (this.model.index === index) {\n            this.createPawn(data)\n        }\n    }\n\n    // 删除士兵\n    private onRemovePawn(index: number, uid: string) {\n        if (this.model.index === index) {\n            this.cleanPawn(this.pawns.remove('uid', uid), true)\n        }\n    }\n\n    // 战斗开始\n    private onAreaBattleBegin(index: number) {\n        if (this.model.index !== index) {\n            return\n        }\n        // 关闭当前正在编辑的建筑\n        this.checkConfirmEditBuild()\n        this.closeEditBuild()\n        // 关闭当前正在编辑的士兵\n        this.currEditPawn?.cancel()\n        this.closeEditPawn()\n        // 初始化血量\n        this.hpBar?.init(this.model)\n        // 初始化士兵\n        this.initPawns()\n        // 战斗时间\n        this.emit(EventType.UPDATE_AREA_BATTLE_TIME_UI, this.model.index)\n        //\n        gameHpr.playAreaBgm(true)\n    }\n\n    // 战斗结束\n    private onAreaBattleEnd(index: number) {\n        if (this.model?.index !== index) {\n            return\n        } else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制\n            return this.reenter()\n        }\n        this.hpBar?.init(this.model)\n        this.initBuilds(true)\n        this.initPawns()\n        // 战斗时间\n        this.emit(EventType.UPDATE_AREA_BATTLE_TIME_UI, this.model.index)\n        //\n        gameHpr.playAreaBgm(false)\n    }\n\n    // 受到伤害\n    private onAreaMainHit(data: any) {\n        if (data.index === this.model?.index) {\n            if (this.model.isBattleing()) {\n                this.hpBar?.play()\n            }\n            animHelper.playFlutterHp({ type: 'isDamage', value: data.value }, this.topLayerNode_, this.getPixelByPoint(data.point), this.key)\n        }\n    }\n\n    // 播放飘血\n    private onPlayFlutterHp(data: any) {\n        if (this.model?.index === data.index) {\n            const pos = data.point ? this.getPixelByPoint(data.point).clone() : this.pawns.find(m => m.uid === data.uid)?.getPosition()\n            if (pos) {\n                animHelper.readyPlayFlutterHp(data, pos, this.topLayerNode_, this.key)\n            }\n        }\n    }\n\n    // 播放增加怒气\n    private onPlayFlutterAnger(data: any) {\n        if (this.model?.index === data.index) {\n            const pawn = this.pawns.find(m => m.uid === data.uid)\n            if (pawn) {\n                animHelper.playFlutterAnger(data.value, this.topLayerNode_, pawn.getPosition(), this.key)\n            }\n        }\n    }\n\n    // 播放子弹飞行\n    private onPlayBulletFly(data: any) {\n        if (this.model?.index === data.index) {\n            data.startPos = this.getPixelByPoint(data.startPoint).clone()\n            data.targetPos = this.getPixelByPoint(data.targetPoint).clone()\n            animHelper.playBulletFly(data, this.topLayerNode_, this.key)\n        }\n    }\n\n    // 播放战斗特效\n    private onPlayBattleEffect(data: any) {\n        if (this.model?.index === data.index) {\n            data.pos = this.getPixelByPoint(data.point).clone()\n            let root = this.skillDiNode_\n            if (data.root === 'top') {\n                root = this.topLayerNode_\n            } else if (data.root === 'role') {\n                root = this.roleNode_\n                data.zIndex = (AREA_MAX_ZINDEX - (data.pos.y - this.borderSize.y * TILE_SIZE)) * 10 + 3\n            }\n            animHelper.playBattleEffect(data, root, this.key)\n        }\n    }\n\n    // 播放音效\n    private onPlayBattleSfx(index: number, url: string, data: any) {\n        if (this.model?.index === index) {\n            audioMgr.playSFX(url, data)\n        }\n    }\n\n    // 播放屏幕抖动\n    private onPlayBattleSceneShake(index: number, time: number) {\n        if (this.model?.index === index) {\n            cameraCtrl.shake(time)\n        }\n    }\n\n    // 聚焦士兵\n    private onFocusPawn(data: any) {\n        if (this.model?.index !== data?.index) {\n            return\n        }/*  else if (cameraCtrl.getNoDragTime() < 5000) {\n            return //多久没拖动才可以聚焦\n        } */\n        const pos = this.getPixelByPoint(data.point)\n        if (cameraCtrl.isInScreenRangeByWorld(pos)) {\n            cameraCtrl.moveTo(0.5, pos, true)\n        }\n    }\n\n    // 刷新修建队列\n    private onUpdateBtQueue() {\n        this.builds.forEach(m => m.updateUpLvAnim())\n    }\n\n    // 刷新训练队列\n    private onUpdatePawnDrillQueue(index: number) {\n        if (this.model.index === index) {\n            this.builds.forEach(m => m.updateDrillPawn())\n        }\n    }\n\n    // 切换显示士兵等级\n    private onChangeShowPawnLv(val: number) {\n        this.pawns.forEach(m => m.showPawnLv(val))\n    }\n\n    // 切换显示士兵装备\n    private onChangeShowPawnEquip(val: boolean) {\n        this.pawns.forEach(m => m.showPawnEquip(val))\n    }\n\n    // 切换士兵装备\n    private onChangePawnEquip(data: PawnObj) {\n        this.pawns.find(m => m.uid === data.uid)?.updateShowPawnEquip()\n    }\n\n    // 切换士兵皮肤\n    private onChangePawnSkin(data: PawnObj) {\n        const i = this.pawns.findIndex(m => m.uid === data.uid)\n        if (i !== -1) {\n            const pawn = this.pawns[i]\n            if (pawn.curSkinId !== data.skinId) {\n                this.pawns.splice(i, 1)\n                this.cleanPawn(pawn)\n                this.createPawn(data)\n            }\n        }\n        this.builds.forEach(m => m.updateDrillPawn())\n    }\n\n    // 化身英雄\n    private onChangePawnPortrayal(data: PawnObj) {\n        if (!data.portrayal) {\n            return\n        }\n        const i = this.pawns.findIndex(m => m.uid === data.uid)\n        if (i !== -1) {\n            const pawn = this.pawns[i]\n            if (pawn.curPortrayalId !== data.portrayal.id) {\n                pawn.playAvatarHeroAnim(data.portrayal.id).then(() => {\n                    if (this.isActive()) {\n                        this.pawns.splice(i, 1)\n                        this.cleanPawn(pawn)\n                        this.createPawn(data)\n                    }\n                })\n            }\n        }\n    }\n\n    // 打造装备开始\n    private onForgeEquipBegin() {\n        this.builds.find(m => m.id === BUILD_SMITHY_NID)?.updateForgeEquip()\n    }\n\n    // 打造装备完成\n    private onForgeEquipComplete() {\n        this.builds.find(m => m.id === BUILD_SMITHY_NID)?.updateForgeEquip()\n    }\n\n    // 若引导\n    private onWeakGuideShowNodeChoose(data: any) {\n        if (data.scene === 'area') {\n            guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key)\n        }\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private isActive() { return this.isValid && !!this.model?.active }\n\n    private getPixelByPoint(point: cc.Vec2) {\n        return point && mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_3))\n    }\n\n    private getPointByPixel(pixel: cc.Vec2) {\n        return pixel && mapHelper.getPointByPixel(pixel).subSelf(this.borderSize)\n    }\n\n    private getBuildPixelByPoint(point: cc.Vec2) {\n        return mapHelper.getPixelByPoint(point.add(this.buildOrigin, this._temp_vec2_1), this._temp_vec2_1)\n    }\n\n    private clean() {\n        this.areaCenter.setLookArea(null)\n        this.model?.setActive(false)\n        this.model = null\n        gameHpr.cleanPawnAstarMap()\n        this.searchCircle = null\n        this.cleanWalls()\n        this.cleanFlames()\n        this.cleanAlliFlags()\n        this.cleanAreaOutDecorate()\n        this.cheanBuilds()\n        // this.cleanPawns()\n        this.closeEditBuild()\n        this.closeEditPawn()\n    }\n\n    // 重连之后的初始化\n    private async reinit() {\n        gameHpr.cleanPawnAstarMap()\n        // 关闭当前正在编辑的建筑\n        this.checkConfirmEditBuild()\n        this.closeEditBuild()\n        // 关闭当前正在编辑的士兵\n        this.currEditPawn?.cancel()\n        this.closeEditPawn()\n        // 重新获取\n        const world = gameHpr.world\n        this.model = await this.areaCenter.reqAreaByIndex(world.getLookCell()?.index ?? -1, true)\n        if (!this.model) {\n            return viewHelper.gotoWind(world.getSceneKey())\n        } else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制\n            return this.reenter()\n        }\n        this.model.setActive(true)\n        // 刷新地图\n        this.updateMap(this.centre.floor())\n        // 城墙等级\n        this.model.wall && this.updateWallLv(this.model.wall.lv)\n        // 血条\n        this.hpBar?.init(this.model)\n        // 绘制建筑\n        this.initBuilds()\n        // 绘制士兵\n        this.initPawns()\n        // 战斗时间\n        this.emit(EventType.REINIT_AREA_UI, this.model)\n    }\n\n    // 重新绘制\n    private async reenter() {\n        this.emit(mc.Event.READY_BEGIN_WIND)\n        this.clean()\n        await this.onReady()\n        this.emit(mc.Event.READY_END_WIND)\n        this.onEnter(true)\n        this.emit(EventType.UPDATE_REENTER_AREA)\n    }\n\n    // 初始化城墙\n    private async initWall() {\n        this.cleanWalls()\n        if (!this.model.isBoss()) {\n            // 加载血条\n            let pfb = await assetsMgr.loadTempRes('wall/WALL_HP_BAR', cc.Prefab, this.key)\n            if (!pfb || !this.isValid) {\n                return\n            }\n            let node = cc.instantiate2(pfb, this.roleNode_)\n            this.hpBar = node.addComponent(HPBarCmpt).init(this.model)\n            // 绘网格\n            const size = this.model.buildSize\n            viewHelper.drawGrid(this.gridNode_.Component(cc.Graphics), cc.v2(size.x - 2, size.y - 2), cc.v2(this.buildOrigin.x + 1, this.buildOrigin.y + 1))\n            this.gridNode_.active = false\n            if (this.model.wall) {\n                // 初始化墙 遗迹不绘制城墙\n                if (!this.model.isAncient()) {\n                    await this.createWall()\n                }\n                // 加载一个墙的等级\n                if (this.model.wall.lv > 0) {\n                    pfb = await assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, this.key)\n                    if (pfb && this.isValid) {\n                        node = this.wallLvNode = cc.instantiate2(pfb, this.roleNode_)\n                        this.updateWallLv(this.model.wall.lv)\n                    }\n                }\n            }\n            this.updateWallHpPosition()\n        }\n        // 创建区域外的装饰\n        await this.createAreaOutDecorate()\n        // 创建联盟旗帜\n        await this.createAlliFlags(gameHpr.getPlayerAlliIcon(this.model.owner))\n    }\n\n    private async createWall() {\n        while (this.walls.length > 0) {\n            this.walls.pop().destroy()\n        }\n        this.walls.length = 0\n        const skinId = 0\n        await Promise.all(this.model.walls.map(async (m) => {\n            let url = `wall/${skinId}/WALL_${skinId}_${m.type}_${m.dir}`\n            if (m.index) {\n                url += '_' + m.index\n            }\n            const pfb = await assetsMgr.loadTempRes(url, cc.Prefab, this.key)\n            if (pfb && this.isValid) {\n                const node = cc.instantiate2(pfb, this.buildNode_)\n                node.Data = this.model.wall\n                node.setPosition(mapHelper.getPixelByPoint(m.point.add(this.buildOrigin, this._temp_vec2_1)))\n                node.addComponent(ClickTouchCmpt).on(this.onClickWall, this)\n                node.zIndex = AREA_MAX_ZINDEX - node.y\n                this.walls.push(node)\n            }\n        }))\n    }\n\n    private cleanWalls() {\n        this.wallLvNode?.destroy()\n        this.wallLvNode = null\n        this.hpBar?.clean()\n        this.hpBar = null\n        while (this.walls.length > 0) {\n            this.walls.pop().destroy()\n        }\n        this.walls.length = 0\n    }\n\n    // 初始化火焰\n    private async createFlames() {\n        const flames = this.model.flames\n        if (this.flames.length === flames.length) {\n            return flames.forEach((point, i) => {\n                const node = this.flames[i], pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))\n                node.setPosition(pos.x - 2, pos.y + 26)\n                node.zIndex = AREA_MAX_ZINDEX - node.y\n            })\n        }\n        const pfb = await assetsMgr.loadTempRes('build/FLAME', cc.Prefab, this.key)\n        if (pfb && this.isValid) {\n            flames.forEach(point => {\n                const node = cc.instantiate2(pfb, this.roleNode_), pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))\n                node.setPosition(pos.x - 2, pos.y + 26)\n                node.zIndex = AREA_MAX_ZINDEX - node.y\n                this.flames.push(node)\n            })\n        }\n    }\n\n    private cleanFlames() {\n        while (this.flames.length > 0) {\n            this.flames.pop().destroy()\n        }\n        this.flames.length = 0\n    }\n\n    // 创建联盟旗帜\n    private async createAlliFlags(icon: number) {\n        if (!icon) {\n            return this.cleanAlliFlags()\n        }\n        const points = this.model.alliFlags\n        if (this.alliFlags.length === points.length && this.alliFlags[0].Data === icon) {\n            return points.forEach((point, i) => {\n                const node = this.alliFlags[i], pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))\n                node.setPosition(pos)\n                node.zIndex = AREA_MAX_ZINDEX - node.y\n            })\n        }\n        this.cleanAlliFlags()\n        const pfb = await assetsMgr.loadTempRes('alli_flag/ALLI_FLAG_' + icon, cc.Prefab, this.key)\n        if (pfb && this.isValid) {\n            points.forEach(point => {\n                const node = cc.instantiate2(pfb, this.roleNode_), pos = mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_1))\n                node.Child('body').y = -28\n                node.setPosition(pos)\n                node.zIndex = AREA_MAX_ZINDEX - node.y\n                this.alliFlags.push(node)\n            })\n        }\n    }\n\n    private cleanAlliFlags() {\n        while (this.alliFlags.length > 0) {\n            this.alliFlags.pop().destroy()\n        }\n        this.alliFlags.length = 0\n    }\n\n    // 创建区域外的装饰\n    private async createAreaOutDecorate() {\n        this.cleanAreaOutDecorate()\n        const seasonType = /* gameHpr.world.getSeasonType() */0\n        const cell = gameHpr.world.getMapCellByIndex(this.model.index), drawType = cell.getLandDrawType()\n        const url = `area_decorate/${seasonType}/AREA_DECORATE_${drawType}`\n        const pfb = await assetsMgr.loadTempRes(url, cc.Prefab, this.key)\n        if (pfb && this.isValid) {\n            const node = cc.instantiate2(pfb, this.mapNode_.Child('bg'))\n            node.setPosition(this.borderSize.x * TILE_SIZE, this.borderSize.y * TILE_SIZE)\n            this.areaOutDecorate.push(node)\n        }\n    }\n\n    private cleanAreaOutDecorate() {\n        while (this.areaOutDecorate.length > 0) {\n            this.areaOutDecorate.pop().destroy()\n        }\n        this.areaOutDecorate.length = 0\n    }\n\n    // 初始化建筑\n    private async initBuilds(playOccupyEffect?: boolean) {\n        this.buildMap = {}\n        // 先删除没有的\n        const builds = this.model.builds\n        builds.forEach(m => this.buildMap[m.uid] = 1)\n        for (let i = this.builds.length - 1; i >= 0; i--) {\n            if (!this.buildMap[this.builds[i].uid]) {\n                this.cleanBuild(this.builds.splice(i, 1)[0])\n            }\n        }\n        await Promise.all(builds.map(m => this.createBuild(m)))\n    }\n\n    private async createBuild(data: BuildObj) {\n        if (!this.isActive()) {\n            return null\n        }\n        let build = this.builds.find(m => m.uid === data.uid)\n        if (build) {\n            this.buildMap[data.uid] = 2\n            return build.resync(data, this.model.owner)\n        }\n        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)\n        if (!pfb || !this.isActive()) {\n            return null\n        } else if (this.buildMap[data.uid] === 2) {\n            return null //防止重复创建或创建没有的\n        } else if (data.aIndex !== this.model?.index) {\n            return null\n        }\n        this.buildMap[data.uid] = 2\n        build = cc.instantiate2(pfb, this.roleNode_).getComponent(BaseBuildCmpt).init(data, this.buildOrigin, this.borderSize.y * TILE_SIZE, this.model.owner)\n        this.builds.push(build)\n        return build\n    }\n\n    private cheanBuilds() {\n        while (this.builds.length > 0) {\n            this.builds.pop().clean()\n        }\n        this.buildMap = {}\n    }\n\n    private cleanBuild(data: BaseBuildCmpt) {\n        if (data) {\n            data.clean()\n            delete this.buildMap[data.uid]\n        }\n    }\n\n    // 刷新墙的等级\n    private updateWallLv(lv: number) {\n        if (this.wallLvNode) {\n            this.wallLvNode.Child('val', cc.Label).string = '' + lv\n        }\n    }\n\n    // 刷新血条位置\n    private updateWallHpPosition() {\n        if (this.hpBar) {\n            let node = this.hpBar.node, pos = cc.v2()\n            if (this.model.cityId === CITY_MAIN_NID) {\n                pos = mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin))\n                node.setPosition(pos.x, pos.y + 25)\n            } else if (this.model.isAncient()) {\n                pos = mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin))\n                node.setPosition(pos.x, pos.y + 25)\n            } else {\n                pos = mapHelper.getPixelByPoint(this.buildOrigin)\n                node.setPosition(pos.x, pos.y + 47)\n            }\n            node.zIndex = (AREA_MAX_ZINDEX - (pos.y - this.borderSize.y * TILE_SIZE)) * 10 + 1\n        }\n        if (this.wallLvNode) {\n            const pos = mapHelper.getPixelByPoint(this.buildOrigin)\n            this.wallLvNode.setPosition(pos.x, pos.y + 16)\n            this.wallLvNode.zIndex = this.hpBar.node.zIndex + 1\n        }\n    }\n\n    // 打开编辑建筑\n    private openEditBuild(item: BuildCmpt) {\n        // 显示pnl\n        viewHelper.showPnl('area/EditBuild')\n        this.gridNode_.active = true\n        // 如果之前有就放下\n        this.checkConfirmEditBuild()\n        this.currEditBuild = item\n        // 刷新一下地面点\n        this.model.updateBuildGroundPoints(item.data)\n        // 刷新编辑状态\n        item.updateEditState(this.model.getBuildGroundPointMap(), item.point)\n    }\n\n    // 如果有建筑在编辑状态 就放下\n    private checkConfirmEditBuild() {\n        if (!this.isEditBuildState()) {\n        } else if (this.currEditBuild.editState) {\n            this.currEditBuild.cancel()\n        } else {\n            this.currEditBuild.confirm(this.currEditBuild.getActPointByPixel(this.currEditBuild.getTempPosition()))\n        }\n    }\n\n    // 关闭编辑建筑\n    private closeEditBuild() {\n        this.currEditBuild = null\n        this.editJiantouNode_.active = false\n        this.gridNode_.active = false\n    }\n\n    // 关闭编辑士兵\n    private closeEditPawn() {\n        for (let key in this.editPawns) {\n            const pawn = this.editPawns[key], state = pawn.data?.getState()\n            if (state && state < PawnState.STAND) {\n                pawn.data.changeState(PawnState.NONE)\n            }\n        }\n        this.editPawns = {}\n        this.currEditPawn = null\n        this.selectPawnNode_.Component(SelectCellCmpt).close()\n        this.isPawnMoveing = false\n    }\n\n    private cleanPawns() {\n        while (this.pawns.length > 0) {\n            this.pawns.pop().clean()\n        }\n        this.pawnMap = {}\n    }\n\n    // 初始化士兵\n    private async initPawns() {\n        // 先删除没有的\n        const pawns = this.model.getAllPawns(), uidMap = {}\n        pawns.forEach(m => uidMap[m.getAbsUid()] = true)\n        for (let i = this.pawns.length - 1; i >= 0; i--) {\n            const m = this.pawns[i]\n            if (!uidMap[m.getAbsUid()] || !m.data || m.data.isDie()) {\n                this.cleanPawn(this.pawns.splice(i, 1)[0])\n            }\n        }\n        return Promise.all(pawns.map(m => this.createPawn(m)))\n    }\n\n    private async createPawn(data: PawnObj) {\n        if (!this.isActive() || !data) {\n            return null\n        }\n        let pawn = this.pawns.find(m => m.uid === data.uid)\n        if (!pawn) {\n        } else if (pawn.curSkinId !== data.skinId || pawn.curPortrayalId !== data.getPortrayalId()) {\n            this.pawns.remove('uid', data.uid)\n            this.cleanPawn(pawn)\n        } else if (data.isDie()) {\n            this.pawns.remove('uid', data.uid)\n            this.cleanPawn(pawn)\n            return null\n        } else {\n            return pawn.resync(data)\n        }\n        const pfb = await assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)\n        if (!pfb || !this.isActive()) {\n            return null\n        } else if (this.pawnMap[data.uid]) {\n            return null //防止多次创建\n        } else if (data.aIndex !== this.model?.index) {\n            return null\n        }\n        // 重新获取以防数据不统一\n        const uid = data.uid\n        data = this.model.getPawn(uid) || this.model.getBattleTempPawn(uid)\n        if (!data || data.isDie()) {\n            return null\n        }\n        pawn = cc.instantiate2(pfb, this.roleNode_).getComponent(PawnCmpt).init(data, this.borderSize, this.key)\n        this.pawns.push(pawn)\n        this.pawnMap[data.uid] = pawn\n        return pawn\n    }\n\n    private cleanPawn(pawn: PawnCmpt, release?: boolean) {\n        if (pawn) {\n            delete this.pawnMap[pawn.uid]\n            pawn.clean(release)\n        }\n    }\n\n    // 是否编辑建筑中\n    private isEditBuildState() {\n        return !!this.currEditBuild?.data\n    }\n\n    // 是否编辑小兵中\n    private isEditPawnState() {\n        return !!this.currEditPawn?.data\n    }\n\n    // 绘制地图\n    private updateMap(centre: cc.Vec2) {\n        const seasonType = /* gameHpr.world.getSeasonType() */0\n        const cell = gameHpr.world.getMapCellByIndex(this.model.index), seasonColorConf = AREA_DI_COLOR_CONF[seasonType]\n        const colorConf: AreaLandColorInfo = seasonColorConf[cell.getLandDrawType()] || seasonColorConf[0]\n        const areaSize = this.model.areaSize, oYindex = (areaSize.x + 1) % 2\n        // 设置整个背景颜色\n        cameraCtrl.setBgColor(colorConf.bg)\n        //\n        // this.preCameraZoomRatio = cameraCtrl.zoomRatio\n        // this.centre.set(centre)\n        const buildOrigin = this.model.buildOrigin, buildSize = this.model.buildSize\n        const points = mapHelper.getRangePointsByPoint(centre, gameHpr.world.getMaxTileRange())\n        const isBoss = this.model.isBoss()\n        let mi = 0\n        this.diNode.Items(points, (it, point, i) => {\n            const x = point.x - this.borderSize.x, y = point.y - this.borderSize.y\n            const bx = x - buildOrigin.x, by = y - buildOrigin.y\n            const index = it.Data = mapHelper.pointToIndexByNumer(x, y, areaSize)\n            const id = x + '_' + y\n            it.setPosition(mapHelper.getPixelByPoint(point, this._temp_vec2_0))\n            if (mapHelper.isBorder(x, y, areaSize)) { //边界外\n                it.Component(cc.Sprite).spriteFrame = null\n            } else if (mapHelper.isBorder(bx, by, buildSize)) { //战斗区域\n                const idx = y % 2 === 0 ? index : index + oYindex\n                it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('comm_area_01')\n                it.Color(colorConf.battle[Number(idx % 2 !== 0)])\n                if (this.model.banPlacePawnPointMap[id]) {\n                    const mn = resHelper.getNodeByIndex(this.maskNode, mi++, this._temp_vec2_0)\n                    mn.Data = true\n                    mn.active = this.isEditPawnState()\n                }\n            } else if (!isBoss) { //建筑区域\n                it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('comm_area_01')\n                if (bx === 0 || bx === buildSize.x - 1 || by === 0 || by === buildSize.y - 1) {\n                    it.Color(colorConf.build)\n                } else if (cell.isMainCity() || cell.isAncient()) {\n                    it.Color('#D6DBAA')\n                }\n            } else if (bx === 1 && by === 1) { //boss位置\n                it.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('land_area_boss_' + cell.landType)\n            } else {\n                it.Component(cc.Sprite).spriteFrame = null\n            }\n        })\n        // 隐藏多余的\n        resHelper.hideNodeByIndex(this.maskNode, mi)\n    }\n\n    private getLandIcon(icon: string) {\n        return resHelper.getLandItemIcon(icon, this.tempSeasonType)\n    }\n\n    // 点击地图\n    private onClickMap(worldLocation: cc.Vec2) {\n        if (!this.model || !worldLocation) {\n            return\n        }\n        const point = this.getPointByPixel(worldLocation)\n        if (!point) {\n            return\n        } else if (mapHelper.isBorder(point.x, point.y, this.model.areaSize)) {\n            return\n        }\n        // 是否点击的建筑区域\n        const bpoint = point.sub(this.model.buildOrigin, this._temp_vec2_1)\n        if (mapHelper.isBorder(bpoint.x, bpoint.y, this.model.buildSize)) {\n            this.onClickMapArea(point)\n        } else {\n            this.onClickBuildArea(bpoint)\n        }\n    }\n\n    // 点击地图区域\n    private onClickMapArea(point: cc.Vec2) {\n        if (!this.isEditPawnState() || this.isEditBuildState() || this.isPawnMoveing) {\n            return\n        }\n        const uid = this.currEditPawn.uid\n        if (!this.model.banPlacePawnPointMap[point.ID()] && !this.pawns.some(m => m.uid !== uid && m.getActPoint().equals(point))) {\n            this.selectPawnNode_.Component(SelectCellCmpt).open(this.getPixelByPoint(point), this.PAWN_SIZE)\n            this.movePawn(point)\n        }\n    }\n\n    // 点击建筑区域\n    private onClickBuildArea(point: cc.Vec2) {\n        if (!this.isEditBuildState() || this.isEditPawnState()) {\n            return\n        } else if (!this.model.isBuildBorder(point)) {\n            this.currEditBuild.setOffsetPositionByPoint(point)\n            this.currEditBuild.updateEditState(this.model.getBuildGroundPointMap(), point)\n        }\n    }\n\n    // 点击墙\n    private onClickWall() {\n        if (!this.model.wall || this.isEditBuildState() || this.isEditPawnState()) {\n            return\n        }\n        audioMgr.playSFX('click')\n        if (this.model.isAncient()) {\n            const build = this.model.getBuildById(this.model.cityId)\n            if (!build) {\n            } else if (gameHpr.checkIsOneAlliance(this.model.owner)) {\n                viewHelper.showPnl(build.getUIUrl(), build)\n            } else {\n                viewHelper.showPnl('build/BuildAncientBase', build)\n            }\n        } else if (this.model.isOwner()) {\n            viewHelper.showPnl(this.model.wall.getUIUrl(), this.model.wall)\n        } else {\n            viewHelper.showPnl('build/BuildCity', this.model.wall)\n        }\n    }\n\n    private getSearchCircle() {\n        if (!this.searchCircle) {\n            const model = this.model\n            this.searchCircle = new SearchCircle().init((x: number, y: number) => model.checkIsBattleArea(x, y) && !model.banPlacePawnPointMap[x + '_' + y])\n        }\n        return this.searchCircle\n    }\n\n    // 移动士兵\n    private async movePawn(point: cc.Vec2) {\n        if (this.currEditPawn) {\n            this.isPawnMoveing = true\n            this.emit(EventType.EDIT_PAWN_MOVEING, true)\n            await this.movePawnOne(this.currEditPawn, point)\n            if (this.isActive()) {\n                this.isPawnMoveing = false\n                this.emit(EventType.EDIT_PAWN_MOVEING, false)\n            }\n        }\n    }\n\n    // 移动单个士兵\n    private async movePawnOne(pawn: PawnCmpt, point: cc.Vec2) {\n        if (!pawn?.data || pawn.data?.getState() === PawnState.EDIT_MOVE) {\n            return\n        }\n        const data = pawn.data\n        const sp = pawn.getActPoint()\n        const area = this.model, as = gameHpr.getPawnASatr(data.uid).init((x: number, y: number) => area.checkIsBattleArea(x, y) && !area.banPlacePawnPointMap[x + '_' + y])\n        const points = await as.search(sp, point)\n        if (!this.isActive() || points.length === 0) {\n            return\n        } else if (!this.editPawns[pawn.uid]) {\n            this.editPawns[pawn.uid] = pawn\n        }\n        const time = mapHelper.getMoveNeedTime(points, 400)\n        data.changeState(PawnState.EDIT_MOVE, { paths: points, needMoveTime: time })\n        await ut.wait(time * 0.001)\n        if (data.getState() < PawnState.STAND) {\n            data.changeState(PawnState.NONE)\n        }\n    }\n\n    // 集合士兵\n    private async gatherPawn() {\n        if (!this.currEditPawn?.data) {\n            return\n        }\n        const pawn = this.currEditPawn\n        const uid = this.currEditPawn.uid\n        const armyUid = this.currEditPawn.data.armyUid\n        const point = pawn.getActPoint()\n        // 获取一个军队的\n        const pawns: PawnCmpt[] = [], otherPawns = {}\n        this.pawns.forEach(m => {\n            if (m.uid === uid) {\n            } else if (m.data?.armyUid === armyUid) {\n                pawns.push(m)\n            } else {\n                otherPawns[m.point.ID()] = true\n            }\n        })\n        const count = pawns.length\n        if (count === 0) {\n            return\n        }\n        // 获取位置\n        const points = this.getSearchCircle().search(point, count, otherPawns)\n        // 删除已经在这个位置的士兵\n        points.delete(m => {\n            const i = pawns.findIndex(p => p.getActPoint().equals(m))\n            if (i !== -1) {\n                pawns.splice(i, 1)\n                return true\n            }\n            return false\n        })\n        if (points.length === 0) {\n            return\n        }\n        this.isPawnMoveing = true\n        this.emit(EventType.EDIT_PAWN_MOVEING, true)\n        await Promise.all(points.map((m, i) => this.movePawnOne(pawns[i], m)))\n        if (this.isActive()) {\n            this.isPawnMoveing = false\n            this.emit(EventType.EDIT_PAWN_MOVEING, false)\n        }\n    }\n\n    // 保存编辑士兵的信息\n    private async editPawnEnd() {\n        if (!this.currEditPawn?.data || this.isPawnMoveing) {\n            return\n        }\n        const info = this.currEditPawn.data\n        const pawns = []\n        const moveInfos = []\n        for (let key in this.editPawns) {\n            const pawn = this.editPawns[key]\n            const point = pawn.getActPoint()\n            moveInfos.push({\n                uid: pawn.uid,\n                point: point.toJson()\n            })\n            if (!pawn.point?.equals(point)) {\n                pawns.push({ uid: pawn.uid, point: point.toJson() })\n            }\n        }\n        if (pawns.length > 0) {\n            const { err, data } = await netHelper.reqMoveAreaPawns({ index: info.aIndex, armyUid: info.armyUid, pawns: moveInfos })\n            if (err) {\n                return viewHelper.showAlert(err)\n            } else if (!this.isActive()) {\n                return\n            }\n        }\n        viewHelper.hidePnl('area/EditPawn')\n        for (let key in this.editPawns) {\n            this.editPawns[key].confirm()\n        }\n        this.currEditPawn.data.actioning = false\n        this.builds.forEach(m => m.setCanClick(true))\n        this.pawns.forEach(m => m.setCanClick(true))\n        this.maskNode.children.forEach(m => m.active = false)\n        this.closeEditPawn()\n    }\n\n    update(dt: number) {\n        if (!this.model?.active) {\n            return\n        }\n        // 检测是否需要填充地图\n        // this.checkUpdateMap()\n    }\n\n    private checkUpdateMap() {\n        const point = mapHelper.getPointByPixel(cameraCtrl.getCentrePosition(), this._temp_vec2_2)\n        if (!this.centre.equals(point) || this.preCameraZoomRatio !== cameraCtrl.zoomRatio) {\n            this.updateMap(point)\n        }\n    }\n}\n"]}