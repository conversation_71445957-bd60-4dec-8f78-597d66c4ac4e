{"version": 3, "sources": ["assets\\app\\script\\view\\area\\PawnInfoPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA4G;AAE5G,qDAAoD;AACpD,qDAAuJ;AAEvJ,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,2DAA0D;AAC1D,6DAA4D;AAMpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA6C,mCAAc;IAA3D;QAAA,qEA+pCC;QA7pCG,0BAA0B;QAClB,eAAS,GAAY,IAAI,CAAA,CAAC,wBAAwB;QAClD,gBAAU,GAAa,IAAI,CAAA,CAAC,8CAA8C;QAC1E,yBAAmB,GAAa,IAAI,CAAA,CAAC,kEAAkE;QACvG,eAAS,GAAY,IAAI,CAAA,CAAC,wBAAwB;QAClD,gBAAU,GAAY,IAAI,CAAA,CAAC,sBAAsB;QACjD,gBAAU,GAAY,IAAI,CAAA,CAAC,sBAAsB;QACjD,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,iBAAW,GAAY,IAAI,CAAA,CAAC,uBAAuB;QACnD,eAAS,GAAY,IAAI,CAAA,CAAC,0BAA0B;QACpD,wBAAkB,GAAY,IAAI,CAAA,CAAC,8BAA8B;QACjE,mBAAa,GAAY,IAAI,CAAA,CAAC,6CAA6C;QAC3E,uBAAiB,GAAY,IAAI,CAAA,CAAC,kDAAkD;QACpF,0BAAoB,GAAY,IAAI,CAAA,CAAC,kDAAkD;QACvF,yBAAmB,GAAY,IAAI,CAAA,CAAC,+BAA+B;QACnE,oBAAc,GAAY,IAAI,CAAA,CAAC,+CAA+C;QAC9E,wBAAkB,GAAY,IAAI,CAAA,CAAC,oDAAoD;QACvF,2BAAqB,GAAY,IAAI,CAAA,CAAC,oDAAoD;QAC1F,uBAAiB,GAAY,IAAI,CAAA,CAAC,6BAA6B;QAC/D,wBAAkB,GAAY,IAAI,CAAA,CAAC,8BAA8B;QACzE,MAAM;QAEE,UAAI,GAAY,IAAI,CAAA;QACpB,UAAI,GAAY,IAAI,CAAA;QACpB,eAAS,GAAmB,IAAI,CAAA;QAChC,YAAM,GAAW,EAAE,CAAA;QACnB,oBAAc,GAAW,CAAC,CAAA;QAC1B,iBAAW,GAAW,EAAE,CAAA;QACxB,eAAS,GAAW,CAAC,CAAA;QACrB,cAAQ,GAAW,CAAC,CAAA;QACpB,mBAAa,GAAW,CAAC,CAAC,CAAA;QAC1B,mBAAa,GAAY,KAAK,CAAA;QAE9B,eAAS,GAAY,KAAK,CAAA;QAC1B,gBAAU,GAAY,KAAK,CAAA;QAC3B,iBAAW,GAAY,KAAK,CAAA;;IA0nCxC,CAAC;IAxnCU,yCAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACxE,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;SAC5D,CAAA;IACL,CAAC;IAEY,kCAAQ,GAArB;;;gBACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBAClC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;gBACtC,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,KAAK,CAAA;gBACvC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;;;;KACzC;IAEM,iCAAO,GAAd,UAAe,IAAa,EAAE,SAAyB,EAAE,MAAc;;QACnE,oBAAO,CAAC,cAAc,GAAG,IAAI,CAAA;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAA;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAA;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAA;QAC1B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QAC9B,IAAI,OAAO,IAAI,CAAC,oBAAO,CAAC,UAAU,EAAE,IAAI,CAAC,oBAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAClF,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;SAClB;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,CAAC,OAAO;QACpC,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QAC7H,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAS,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,WAAW,CAAC,CAAA;QACrJ,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,CAAA,CAAC,MAAM;QAC7G,IAAM,QAAQ,GAAG,EAAE,CAAC,YAAY,KAAK,MAAM,CAAA,CAAC,SAAS;QACrD,IAAM,iBAAiB,GAAG,MAAM,KAAK,aAAa,CAAA;QAClD,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,IAAI,gBAAQ,CAAC,OAAO,CAAA,CAAC,MAAM;QACtD,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA,CAAC,MAAM;QACnC,IAAM,cAAc,GAAG,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAS,CAAC,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,WAAW,CAAC,CAAA;QAC3H,KAAK;QACL,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACxF,WAAW,CAAC,MAAM,GAAG,IAAI,CAAA;QACzB,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,cAAc;YAC/B,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAA;YACnB,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SAC1E;aAAM;YACH,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAA;YACnB,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACtE;QACD,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,UAAU,IAAI,MAAM,KAAK,MAAM,CAAC,CAAA;QACnJ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAC9E,SAAS;QACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,KAAK,CAAA;QACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,KAAK,CAAA;QACrC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,cAAc,EAAE;YAC3D,IAAM,GAAG,GAAG,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACvF,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;SACnE;QACD,KAAK;QACL,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,OAAO;QACP,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;QACxD,IAAI,WAAW,CAAC,MAAM,GAAG,MAAM,KAAK,MAAM,EAAE;YACxC,IAAM,WAAW,GAAG,CAAC,SAAS,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,CAAA;YAChF,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,WAAW,CAAA;YAC9C,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,WAAW,CAAA;YAC9C,IAAI,WAAW,EAAE;gBACb,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;aAC1D;iBAAM;gBACH,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;aACpE;SACJ;QACD,KAAK;QACL,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACrD,KAAK;QACL,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,UAAU;QACV,IAAM,KAAK,GAAG,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,0CAAE,KAAK,0CAAE,EAAE,MAAK,gBAAQ,CAAC,UAAU,CAAA;QACpE,IAAM,OAAO,GAAG,CAAC,SAAS,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,CAAC,CAAC,SAAS,IAAI,KAAK,CAAC,CAAA;QAC9E,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;YACxD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC1C,KAAK;YACL,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,cAAc,IAAI,UAAU,CAAA;YACjE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAA;YACjD,KAAK;YACL,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,cAAc,IAAI,KAAK,CAAA;YAC1D,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;YACzC,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,EAAE;gBACxB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;aAC9B;YACD,KAAK;YACL,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACjC,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;aAC1C;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACnB;SACJ;QACD,SAAS;QACT,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,KAAK,CAAA;QACzC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;QACtC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,cAAc,EAAE;YAC5D,IAAM,GAAG,GAAG,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;YACxF,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;SACpE;QACD,KAAK;QACL,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAA;QAC3B,IAAM,cAAc,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,GAAG,QAAQ,IAAI,CAAC,OAAO,CAAA;QACtH,IAAI,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,QAAQ,IAAI,UAAU,EAAE;YACxD,OAAO;YACP,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,cAAc,EAAE;gBACnD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;aAC/F;YACD,OAAO;YACP,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,SAAS,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAA;gBAC3E,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;aAC3C;YACD,IAAM,UAAU,GAAG,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpD,OAAO;YACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,UAAU,EAAE;gBAC1C,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,KAAI,KAAK,EAAE,CAAC,CAAC,CAAA;aAChG;YACD,OAAO;YACP,IAAM,QAAQ,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,YAAY,CAAA;YACzC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,CAAC,QAAQ,EAAE;gBACtD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAA;aACrD;SACJ;QACD,KAAK;QACL,GAAG;YACC,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjD,IAAM,SAAS,GAAG,SAAS,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAA;YAC7D,IAAI,SAAS,EAAE;gBACX,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;aACjC;iBAAM,IAAI,SAAS,EAAE;gBAClB,IAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnE,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE;oBAC5B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAA;iBACtG;qBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,EAAE;oBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAA;iBAC1D;aACJ;iBAAM;gBACH,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAA;gBAC/B,MAAK;aACR;YACD,IAAM,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAA;YAC7B,IAAM,OAAO,GAAG,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACnD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAA;YAC9B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAA;YACjG,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;YACtD,IAAM,WAAW,GAAG,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,CAAA;YACpE,IAAI,UAAU,CAAC,MAAM,GAAG,WAAW,EAAE;gBACjC,IAAM,IAAI,GAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;gBAClE,IAAM,WAAW,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,EAAE,EAAV,CAAU,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,IAAI,EAAP,CAAO,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBAChM,UAAU,CAAC,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;aAC/C;SACJ,QAAQ,KAAK,EAAC;QACf,SAAS;QACT,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAA;QAC/B,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAA;QAC1B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA;IACzE,CAAC;IAEM,kCAAQ,GAAf;QACI,oBAAO,CAAC,cAAc,GAAG,IAAI,CAAA;QAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,KAAK,CAAA;QACvC,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAEM,iCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,4DAA4D;IAC5D,4CAAkB,GAAlB,UAAmB,KAA0B,EAAE,IAAY;QACvD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAClD,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;IAChE,CAAC;IAED,wCAAwC;IACxC,wCAAc,GAAd,UAAe,KAA0B,EAAE,IAAY;QACnD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACnE,uBAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAED,4CAA4C;IAC5C,4CAAkB,GAAlB,UAAmB,KAA0B,EAAE,CAAS;QAAxD,iBAUC;QATG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,EAAE;YACvC,OAAO,uBAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC,kCAAkC,EAAE;gBAC3H,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,WAAW,CAAC,KAAI,CAAC,SAAS,CAAC,EAAhD,CAAgD;gBAC1D,MAAM,EAAE,cAAQ,CAAC;aACpB,CAAC,CAAA;SACL;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACpC,CAAC;IAED,4CAA4C;IAC5C,4CAAkB,GAAlB,UAAmB,KAA0B,EAAE,CAAS;QAAxD,iBAYC;QAXG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,EAAE;YACvC,OAAO,uBAAU,CAAC,cAAc,CAAC,kCAAkC,EAAE;gBACjE,EAAE,EAAE;oBACA,KAAI,CAAC,WAAW,CAAC,KAAI,CAAC,SAAS,CAAC,CAAA;gBACpC,CAAC;gBACD,MAAM,EAAE,cAAQ,CAAC;aACpB,CAAC,CAAA;SACL;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACpC,CAAC;IAED,yCAAyC;IACzC,0CAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACrD,IAAI,CAAC,aAAa,EAAE,CAAA;IACxB,CAAC;IAED,+BAA+B;IAC/B,+CAAqB,GAArB,UAAsB,KAA0B,EAAE,IAAY;QAC1D,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,oEAAoE;IACpE,0CAAgB,GAAhB,UAAiB,KAA0B,EAAE,CAAS;QAAtD,iBAmBC;QAlBG,IAAM,IAAI,GAAc,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACzC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QAC3B,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACrD,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE,cAAc;YACvC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;gBACnD,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;iBACZ;qBAAM,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,qBAAa,CAAC,kBAAkB,EAAE;oBACzD,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,KAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;iBACnD;qBAAM,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,qBAAa,CAAC,aAAa,EAAE;oBACpD,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,KAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAA;iBAC3D;YACL,CAAC,CAAC,CAAA;SACL;QACD,IAAI,oBAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,cAAc,EAAE,CAAA;SACxB;aAAM;YACH,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;SACnC;IACL,CAAC;IAED,gDAAgD;IAChD,yCAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;QACpD,IAAI,CAAC,YAAY,EAAE,CAAA;IACvB,CAAC;IAED,8BAA8B;IAC9B,8CAAoB,GAApB,UAAqB,KAA0B,EAAE,IAAY;QACzD,IAAI,CAAC,aAAa,EAAE,CAAA;IACxB,CAAC;IAED,qDAAqD;IACrD,yCAAe,GAAf,UAAgB,KAA0B,EAAE,CAAS;QAArD,iBAeC;QAdG,IAAM,IAAI,GAAY,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACvC,IAAI,IAAI,EAAE;YACN,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;gBACxC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,oBAAoB,CAAC,CAAA;aAC1D;YACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;SAC1C;aAAM,IAAI,oBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE;YACzC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,gBAAgB,CAAC,CAAA;SACtD;QACD,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAC,IAAY;YACjD,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;aAC9B;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,2CAA2C;IAC3C,sCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACjD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAChE,CAAC;IAED,yCAAyC;IACzC,0CAAgB,GAAhB,UAAiB,KAA0B,EAAE,CAAS;QAClD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAc,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACzC,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,EAAE,EAAE;YACV,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;SAClD;aAAM;YACH,IAAI,CAAC,aAAa,EAAE,CAAA;SACvB;IACL,CAAC;IAED,sCAAsC;IACtC,oCAAU,GAAV,UAAW,KAA0B,EAAE,IAAY;QAC/C,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,oBAAO,CAAC,MAAM,EAAE,EAAE;YACtC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,uBAAuB,CAAC,CAAA;SAC7D;aAAM;YACH,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;SACjE;IACL,CAAC;IAED,wBAAwB;IACxB,qCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,uBAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IACvD,CAAC;IAED,8BAA8B;IAC9B,8CAAoB,GAApB,UAAqB,KAA0B,EAAE,IAAY;QACzD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAA;IAC5E,CAAC;IAED,kEAAkE;IAClE,yCAAe,GAAf,UAAgB,KAA0B,EAAE,CAAS;QACjD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC9B,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;IAC3C,CAAC;IAED,wBAAwB;IACxB,qCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,CAAC,YAAY,EAAE,CAAA;IACvB,CAAC;IAED,sDAAsD;IACtD,wCAAc,GAAd,UAAe,KAA0B,EAAE,CAAS;QAApD,iBAcC;QAbG,IAAM,IAAI,GAAiB,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC5C,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YACvB,IAAI,oBAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;gBACpC,OAAO,uBAAU,CAAC,iBAAiB,EAAE,CAAA;aACxC;YACD,oBAAO,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;gBACtC,IAAI,GAAG,EAAE;oBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;iBACnC;qBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;oBACrB,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;iBAC9B;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,+CAA+C;IAC/C,0CAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACrD,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAA;QACxC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAA;QACrC,IAAM,GAAG,GAAG,CAAC,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAA;QAC/F,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;YACtD,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAA;QACrD,CAAC,CAAC,CAAA;IACN,CAAC;IAED,oDAAoD;IACpD,8CAAoB,GAApB,UAAqB,KAA0B,EAAE,IAAY;QACzD,IAAM,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,EAAhC,CAAgC,CAAC,CAAA;QACvG,IAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACpC,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAA;QAC5E,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,KAAK,CAAA;QACzC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;QACtC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;IACrE,CAAC;IAED,+BAA+B;IAC/B,qCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAApD,iBAkBC;QAjBG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YAC5D,OAAM;SACT;QACD,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,EAAE,UAAO,EAAW;;;;gBAC7D,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACtB,sBAAO,IAAI,EAAA;iBACd;qBAAM,IAAI,oBAAO,CAAC,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;oBACzC,uBAAU,CAAC,SAAS,CAAC,sBAAsB,EAAE,EAAE,MAAM,EAAE,CAAC,qBAAU,CAAC,aAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAA;oBACvF,sBAAO,KAAK,EAAA;iBACf;gBACK,IAAI,GAAG,oBAAO,CAAC,kBAAkB,aAAC,IAAI,CAAC,IAAI,0CAAE,QAAQ,0CAAE,OAAO,CAAC,CAAA;gBACrE,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,aAAK,CAAC,QAAQ,EAAE;oBAC/B,uBAAU,CAAC,SAAS,CAAC,wBAAwB,EAAE,EAAE,MAAM,EAAE,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;oBACrG,sBAAO,KAAK,EAAA;iBACf;gBACD,sBAAO,IAAI,CAAC,eAAe,EAAE,EAAA;;aAChC,CAAC,CAAA;IACN,CAAC;IAED,uCAAuC;IACvC,yCAAe,GAAf,UAAgB,KAA0B,EAAE,CAAS;QACjD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC9B,IAAI,CAAC,IAAI,EAAE;SACV;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,EAAE,IAAI;YAClC,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;SACzD;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,EAAE,IAAI;YAClC,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SACxD;aAAM;YACH,uBAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAA;SACjD;IACL,CAAC;IAED,6CAA6C;IAC7C,uCAAa,GAAb,UAAc,KAA0B,EAAE,IAAY;QAClD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACrC,IAAM,GAAG,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAA;QAC3B,IAAI,EAAE,GAAG,CAAC,EAAE;YACR,EAAE,GAAG,CAAC,CAAA;SACT;aAAM,IAAI,EAAE,GAAG,CAAC,EAAE;YACf,EAAE,GAAG,CAAC,CAAA;SACT;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IACjC,CAAC;IAED,iCAAiC;IACjC,uCAAa,GAAb,UAAc,KAA0B,EAAE,CAAS;QAAnD,iBA2BC;QA1BG,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAM,IAAI,GAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAClE,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,EAAE,EAAV,CAAU,CAAC,EAAE;YACzC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,oBAAoB,CAAC,CAAA;SAC1D;aAAM,IAAI,oBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,iBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YAC5D,OAAO,uBAAU,CAAC,SAAS,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,CAAC,iBAAiB,GAAG,iBAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;SACjH;aAAM,IAAI,oBAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,IAAI,EAAP,CAAO,CAAC,EAAE;YAC1D,OAAO,uBAAU,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA,CAAC,cAAc;SAC1E;QACD,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE,UAAC,WAAmB;YACrE,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,WAAW,EAAE;gBAC/B,OAAM;aACT;YACD,oBAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;gBACzF,IAAI,GAAG,EAAE;oBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;iBACnC;qBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;oBACrB,KAAI,CAAC,IAAI,EAAE,CAAA;oBACX,uBAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;oBACnC,OAAO;oBACP,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;iBAC5F;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED,8CAA8C;IAC9C,+CAAqB,GAArB,UAAsB,KAA0B,EAAE,IAAY;QAC1D,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACrB,uBAAU,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SAC9F;IACL,CAAC;IAED,uCAAuC;IACvC,wCAAc,GAAd,UAAe,KAA0B,EAAE,CAAS;QAChD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC5B,IAAI,EAAE,EAAE;YACJ,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,EAAE,oBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;SACvE;aAAM;YACH,IAAI,CAAC,WAAW,EAAE,CAAA;SACrB;IACL,CAAC;IAED,uCAAuC;IACvC,wCAAc,GAAd,UAAe,KAA0B,EAAE,IAAY;QACnD,IAAI,CAAC,WAAW,EAAE,CAAA;IACtB,CAAC;IAED,6BAA6B;IAC7B,6CAAmB,GAAnB,UAAoB,KAA0B,EAAE,IAAY;QACxD,IAAI,CAAC,YAAY,EAAE,CAAA;IACvB,CAAC;IAED,gEAAgE;IAChE,wCAAc,GAAd,UAAe,KAA0B,EAAE,CAAS;QAChD,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC5B,IAAI,EAAE,KAAK,yBAAc,IAAI,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE;YAChE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;SACzB;QACD,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAA;IAChC,CAAC;IAED,6CAA6C;IAC7C,yCAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;QACpD,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAA;QACvC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAA;QACpC,IAAM,GAAG,GAAG,CAAC,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAA;QAC9F,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAA;QACrD,CAAC,CAAC,CAAA;IACN,CAAC;IAED,kDAAkD;IAClD,6CAAmB,GAAnB,UAAoB,KAA0B,EAAE,IAAY;QACxD,IAAM,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,EAAhC,CAAgC,CAAC,CAAA;QACtG,IAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACpC,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAA;QAC3E,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,KAAK,CAAA;QACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,KAAK,CAAA;QACrC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;IACpE,CAAC;IAED,2CAA2C;IAC3C,2CAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QAA1D,iBAUC;QATG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,EAAE;YACvC,OAAO,uBAAU,CAAC,cAAc,CAAC,iCAAiC,EAAE;gBAChE,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,UAAU,CAAC,KAAI,CAAC,SAAS,CAAC,EAA/C,CAA+C;gBACzD,MAAM,EAAE,cAAQ,CAAC;aACpB,CAAC,CAAA;SACL;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACnC,CAAC;IACD,MAAM;IACN,iHAAiH;IAEzG,8CAAoB,GAA5B,UAA6B,IAAa;QACtC,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;SACpE;IACL,CAAC;IAEO,sCAAY,GAApB,UAAqB,IAAa;QAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,GAAG,EAAE;YAChC,IAAM,KAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;YACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,KAAG,EAAb,CAAa,CAAC,IAAI,IAAI,CAAC,IAAI,CAAA;SAC/D;IACL,CAAC;IAED,SAAS;IACD,sCAAY,GAApB;QACI,IAAI,CAAC,WAAW,EAAE,CAAA;IACtB,CAAC;IAED,SAAS;IACD,0CAAgB,GAAxB;QACI,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACrD,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACpD,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;IACjE,CAAC;IACD,iHAAiH;IAEzG,kCAAQ,GAAhB;QACI,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAA;QACzF,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;YACjE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;YAC9D,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YAClC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;SACxC;aAAM,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE;YAC7C,IAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,GAAG,oBAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC9G,IAAM,OAAO,GAAG,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAA;YAClE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;YAC5D,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;YACpE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;SACxE;aAAM;YACH,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;SAClE;IACL,CAAC;IAEO,sCAAY,GAApB;QACI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,uBAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IAC5G,CAAC;IAEO,4CAAkB,GAA1B;QACI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;YACrC,OAAO;YACP,IAAI,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAChD,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAA;YAChG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;YACzD,IAAI,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YACjD,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;YACjD,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAA;YAC7E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,IAAI,GAAG,IAAI,CAAC,aAAa,CAAA;YACzB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAA;YAC/F,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,OAAO;YACP,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC7C,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAA;YACxG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;YAC1D,IAAI,CAAC,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAClD,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;YACnD,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAA;YAC3G,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,IAAI,GAAG,IAAI,CAAC,cAAc,CAAA;YAC1B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAA;YACtG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,OAAO;YACP,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC3C,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA;YACpG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YAC/C,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA;YACvG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,OAAO;YACP,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC5C,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAA;YACzG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;SAC/B;IACL,CAAC;IAED,sFAAsF;IAC9E,sCAAY,GAApB;QAAA,iBA0BC;;QAzBG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;QAChF,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAA;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAClD,IAAM,KAAK,GAAG,oBAAO,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;QACzE,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAA;QACpC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YACrB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC/B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YACzC,qBAAS,CAAC,gBAAgB,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,EAAE,KAAI,KAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;QAC3E,CAAC,CAAC,CAAA;QACF,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC7B,IAAI,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,MAAM,EAAf,CAAe,CAAC,CAAA;QACjD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,CAAC,CAAA;YACT,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,eAAG,KAAK,CAAC,KAAK,CAAC,0CAAE,EAAE,mCAAI,CAAC,CAAA;SACpD;QACD,IAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;QACpE,IAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAA;QAC7H,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAA;QAChC,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;QACnG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;IAC3C,CAAC;IAEO,uCAAa,GAArB,UAAsB,MAAc;QAChC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;QACzC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAA;QACxD,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;QACtC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;YACzB,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YACxF,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;gBACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnC,oBAAO,CAAC,MAAM,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACvD;gBACD,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;aAC1D;SACJ;IACL,CAAC;IAEO,8CAAoB,GAA5B,UAA6B,IAAkB;;QAC3C,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAClD,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,EAAE;;YACzD,IAAM,EAAE,GAAG,OAAA,EAAE,CAAC,IAAI,0CAAE,EAAE,KAAI,CAAC,CAAA;YAC3B,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK,EAAE,CAAA;YACxE,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,KAAK,EAAE,CAAA;QACzD,CAAC,CAAC,CAAA;QACF,QAAQ;QACR,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;YACvD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAA;YACnB,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACnF;QACD,OAAO;QACP,IAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,OAAA,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,0CAAE,IAAI,KAAI,EAAE,CAAC,CAAC,CAAC,sBAAsB,CAAA;QACtG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;QACrC,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACrC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC5C,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAA;SACnH;QACD,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACvC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;YACjC,IAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;YAC/C,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;YACnB,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;SAClE;IACL,CAAC;IAED,sFAAsF;IAC9E,yCAAe,GAAvB,UAAwB,IAAa,EAAE,KAAiB;QACpD,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC5C,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,CAAA;QACpE,IAAM,cAAc,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAS,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,CAAA;QAC/I,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,EAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,CAAA,IAAI,IAAI,CAAC,UAAU,IAAI,cAAc,CAAA;QACzF,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,EAAE;YACX,qBAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;SAC1F;aAAM,IAAI,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;YACrD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,CAAA;SAClF;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SAChB;IACL,CAAC;IAED,SAAS;IACD,uCAAa,GAArB;QAAA,iBAwCC;;QAvCG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;YACzC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,SAAS,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAA;QACtC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,IAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACnD,IAAM,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAA;QAC9E,IAAM,SAAS,GAAG,EAAE,CAAC,eAAe,aAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,0CAAG,CAAC,2CAAG,MAAM,EAAE,GAAG,CAAC,CAAA;QAC1H,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;QACxC,MAAM,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YACb,IAAI,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC9D,IAAI,IAAI,EAAE;gBACN,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC7B,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aAChC;iBAAM;gBACH,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACjC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aACpC;YACD,OAAO,EAAE,GAAG,EAAE,CAAA;QAClB,CAAC,CAAC,CAAA;QACF,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAA;QAC9D,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAA;QACpC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,IAAI;YACtB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAI,CAAC,GAAG,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;YACjF,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;QACxC,CAAC,CAAC,CAAA;QACF,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,EAAE;YACZ,IAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAnB,CAAmB,CAAC,CAAA;YACxD,IAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;YACpE,IAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAA;YAC7H,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAA;YAChC,EAAE,CAAC,cAAc,EAAE,CAAA;YACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;SACtG;aAAM;YACH,EAAE,CAAC,YAAY,EAAE,CAAA;SACpB;QACD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;IACrC,CAAC;IAED,SAAS;IACD,+CAAqB,GAA7B,UAA8B,KAAgB;QAA9C,iBA6BC;QA5BG,IAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACnD,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,EAAE;YACzD,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAA;YACpB,IAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,OAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,CAAA,CAAA;YACnE,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,MAAM,CAAA;QAClD,CAAC,CAAC,CAAA;QACF,QAAQ;QACR,IAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QACzD,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,EAAE;YACX,qBAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;SAC1F;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SACnB;QACD,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,EAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,CAAA,CAAA;QACvC,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,OAAO,CAAA;QAC/D,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACV,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,CAAA,CAAC,EAAE;YAC3B,uBAAU,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YACjD,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACrB,IAAI,KAAI,CAAC,OAAO,EAAE;oBACd,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;oBAC/C,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAA;iBACrD;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAEO,wCAAc,GAAtB;QACI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,KAAK,CAAA;QACvC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAA;IACrE,CAAC;IAED,sFAAsF;IAC9E,uCAAa,GAArB,UAAsB,IAAa,EAAE,EAAW;QAC5C,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,yBAAc,CAAA;QACxD,qBAAS,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IACnE,CAAC;IAED,SAAS;IACD,qCAAW,GAAnB;QAAA,iBA4BC;QA3BG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;YACzC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,SAAS,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAA;QACpC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACjD,IAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAzC,CAAyC,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,EAA3B,CAA2B,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;QACtK,IAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAA;QACzD,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,IAAM,aAAa,GAAG,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;QACvD,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;YACpB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAA;YACjB,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC5B,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;YACvD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK,yBAAc,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACnF,CAAC,CAAC,CAAA;QACF,IAAI,EAAE,EAAE;YACJ,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,EAAE,EAAX,CAAW,CAAC,CAAA;YAC9C,IAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;YACpE,IAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAA;YAC7H,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAA;YAChC,EAAE,CAAC,cAAc,EAAE,CAAA;YACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;SACtG;aAAM;YACH,EAAE,CAAC,YAAY,EAAE,CAAA;SACpB;QACD,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAA;IAChC,CAAC;IAED,SAAS;IACD,6CAAmB,GAA3B,UAA4B,EAAU;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACjD,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,EAAE;YACzD,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,CAAA;YAC1C,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,CAAA;QACzD,CAAC,CAAC,CAAA;QACF,QAAQ;QACR,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QACrD,IAAI,EAAE,EAAE;YACJ,qBAAS,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACpE;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SACnB;QACD,OAAO;QACP,IAAM,EAAE,GAAG,EAAE,KAAK,yBAAc,IAAI,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,oBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACvG,uBAAU,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;IACxD,CAAC;IAEO,sCAAY,GAApB;QACI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,KAAK,CAAA;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAA;IACjE,CAAC;IAED,sFAAsF;IAC9E,wCAAc,GAAtB,UAAuB,IAAc,EAAE,QAAiB;QACpD,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAC3C,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QACzC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAA;IAC1D,CAAC;IAED,SAAS;IACD,sCAAY,GAApB;;QACI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAA;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAClD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAA;QAC7B,IAAM,GAAG,GAAG,OAAA,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,0CAAE,KAAK,KAAI,EAAE,CAAA;QACrE,IAAM,KAAK,GAAc,EAAE,CAAA;QAC3B,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC;YACT,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE;gBACd,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;aACnB;iBAAM,IAAI,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,eAAe,EAAE,GAAG,8BAAmB,EAAE;gBACxE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aAChB;QACL,CAAC,CAAC,CAAA;QACF,IAAI,KAAK,CAAC,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE;YACjD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACnB;QACD,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;YAC1C,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,MAAM,GAAG,GAAG,MAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,CAAA,CAAA;YAChC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,CAAA;YAC1F,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;YACvC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,MAAM,CAAA;YAClC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAA;YAC9B,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,MAAM,CAAA;QAClD,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,uCAAa,GAArB;QACI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;IAC1C,CAAC;IAED,OAAO;IACO,oCAAU,GAAxB,UAAyB,UAAkB,EAAE,WAAoB;;;;;;wBACvD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;wBAChB,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;wBACnB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;wBACtB,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;wBACd,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;wBAC9B,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAA;wBACzB,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;wBACpB,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;wBACxB,IAAI,CAAC,WAAW,EAAE;4BACd,IAAI,CAAC,OAAO,GAAG,UAAU,CAAA;yBAC5B;wBACqB,qBAAM,qBAAS,CAAC,iBAAiB,CAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,GAAG,KAAA,EAAE,UAAU,YAAA,EAAE,WAAW,aAAA,EAAE,WAAW,aAAA,EAAE,QAAQ,UAAA,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAE,CAAC,EAAA;;wBAAzI,KAAgB,SAAyH,EAAvI,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;4BACtB,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;wBACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;wBACrB,IAAI,IAAI,CAAC,OAAO,EAAE;4BACd,IAAI,CAAC,cAAc,GAAG,WAAW,CAAA;4BACjC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAA;4BAC3B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAA;4BACvB,IAAI,CAAC,cAAc,EAAE,CAAA;4BACrB,IAAI,CAAC,aAAa,EAAE,CAAA;yBACvB;wBACD,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE;4BAC7B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;4BACjD,IAAI,CAAC,IAAI,EAAE,CAAA;yBACd;;;;;KACJ;IAED,KAAK;IACG,mCAAS,GAAjB,UAAkB,IAAa,EAAE,SAAyB;QAA1D,iBAeC;;QAdG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YACrC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;YACtB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,MAAM,KAAI,CAAC,EAAE,OAAA,IAAI,CAAC,QAAQ,0CAAE,OAAO,KAAI,CAAC,CAAC,CAAA;YACzE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,CAAC,EAAE,CAAC;;gBACrB,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBACvC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;gBACnC,IAAI,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,QAAQ,EAAE;oBACnD,IAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBACjD,qBAAS,CAAC,QAAQ,CAAC,gBAAgB,GAAG,CAAC,OAAA,QAAQ,CAAC,IAAI,0CAAE,EAAE,KAAI,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;iBACtH;YACL,CAAC,CAAC,CAAA;SACL;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SAChB;IACL,CAAC;IAED,SAAS;IACD,qCAAW,GAAnB;QAAA,iBAyDC;;QAxDG,IAAM,KAAK,GAAc,EAAE,EAAE,WAAW,GAAc,EAAE,CAAA;QACxD,IAAM,SAAS,SAAG,IAAI,CAAC,IAAI,CAAC,SAAS,0CAAE,KAAK,CAAA;QAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;YACrB,IAAI,CAAC,CAAC,QAAQ,KAAK,CAAC,EAAE;gBAClB,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aAC7B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,oBAAoB,EAAE;gBAC1F,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,oBAAoB,CAAC,EAA1F,CAA0F,CAAC,CAAA;gBACtH,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAA;iBAC3B;qBAAM;oBACH,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBAChB;gBACD,OAAM;aACT;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,eAAe,EAAE;gBAC5C,CAAC,CAAC,SAAS,eAAG,KAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,uBAAe,CAAC,YAAY,CAAC,0CAAE,KAAK,mCAAI,CAAC,CAAA;aACzF;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,EAAE,EAAE,KAAK;gBACrD,IAAM,MAAM,GAAG,KAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,uBAAe,CAAC,iBAAiB,CAAC,CAAA;gBAChF,CAAC,CAAC,SAAS,GAAG,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,KAAI,CAAC,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,KAAI,CAAC,CAAC,CAAA;gBACrD,IAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC9B,IAAI,CAAC,EAAE;oBACH,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;iBAC/B;aACJ;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,EAAE,EAAE,WAAW;gBAC/C,CAAC,CAAC,SAAS,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;aAC1E;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,WAAW,EAAE,EAAE,OAAO;gBACjD,CAAC,CAAC,SAAS,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAA;aAC1E;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,gBAAgB,EAAE,EAAE,SAAS;gBACxD,CAAC,CAAC,SAAS,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;aACzE;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,EAAE,EAAE,QAAQ;gBACxD,CAAC,CAAC,SAAS,GAAG,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aAC5C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,YAAY,EAAE,EAAE,WAAW;gBACtD,CAAC,CAAC,SAAS,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;aAC3E;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,UAAU,EAAE,EAAE,OAAO;gBAChD,CAAC,CAAC,SAAS,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,EAAE,MAAK,gBAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;aAC3E;YACD,IAAI,CAAC,CAAC,IAAI,EAAE;gBACR,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aAChB;QACL,CAAC,CAAC,CAAA;QACF,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YACZ,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAA;YAC9D,OAAO,EAAE,GAAG,EAAE,CAAA;QAClB,CAAC,CAAC,CAAA;QACF,QAAQ;QACR,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;YAC3B,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAS,CAAC,CAAA;SACjD;QACD,QAAQ;QACR,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAS,CAAC,CAAA;SACxE;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YACjC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnD,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACvE,CAAC,CAAC,CAAA;IACN,CAAC;IAED,WAAW;IACG,yCAAe,GAA7B;;;;;4BACI,qBAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAA,CAAC,SAAS;;wBAA3C,SAAiC,CAAA,CAAC,SAAS;wBACrC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;wBACA,qBAAM,qBAAS,CAAC,sBAAsB,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAA;;wBAApH,KAAgB,SAAoG,EAAlH,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;4BACzB,sBAAO,KAAK,EAAA;yBACf;wBACD,oBAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBAClD,uBAAU,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;wBAChD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;wBAC7B,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE;4BAC7B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;yBACzD;wBACD,sBAAO,IAAI,EAAA;;;;KACd;IAEO,mCAAS,GAAjB,UAAkB,IAAa,EAAE,EAAU;QACvC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;QACvB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,gBAAgB,EAAE,CAAA;SAC1B;IACL,CAAC;IAED,WAAW;IACG,0CAAgB,GAA9B,UAA+B,IAAc;;;;;;wBACzC,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;4BACxB,sBAAM;yBACT;wBACK,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;wBAChB,OAAO,GAAG,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAA;wBAC7C,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBAC3E,QAAQ,GAAG,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAA;6BAElI,CAAA,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAA,EAAxC,wBAAwC;wBAClC,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;wBACvF,qBAAM,qBAAS,CAAC,wBAAwB,CAAC,EAAE,EAAE,IAAA,EAAE,QAAQ,UAAA,EAAE,MAAM,QAAA,EAAE,WAAW,aAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAA3F,GAAG,GAAG,SAAqF;wBACjG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;4BACV,oBAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAA;yBACzE;wBACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;;;6BAC/C,CAAA,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAA,EAA1C,wBAA0C;wBAC3C,SAAS,GAAG,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;wBACxF,QAAQ,GAAG,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;wBAChF,qBAAM,qBAAS,CAAC,iBAAiB,CAAC;gCAC1C,KAAK,EAAE,IAAI,CAAC,MAAM;gCAClB,OAAO,EAAE,IAAI,CAAC,OAAO;gCACrB,GAAG,EAAE,IAAI,CAAC,GAAG;gCACb,WAAW,EAAE,IAAI,CAAC,WAAW;gCAC7B,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;gCACxB,SAAS,EAAE,SAAS;gCACpB,MAAM,EAAE,IAAI,CAAC,MAAM;gCACnB,QAAQ,EAAE,QAAQ;gCAClB,KAAK,EAAE,IAAI,CAAC,KAAK;6BACpB,EAAE,IAAI,CAAC,EAAA;;wBAVF,GAAG,GAAG,SAUJ;wBACR,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,SAAS,IAAI,OAAO,EAAE;4BACpC,uBAAU,CAAC,SAAS,CAAC,8BAA8B,GAAG,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAA;yBAC1H;wBACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;;;;;;KAE7D;IAED,OAAO;IACC,qCAAW,GAAnB,UAAoB,IAAoB;QAAxC,iBA6BC;QA5BG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAM;SACT;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA;QACvC,qBAAS,CAAC,kBAAkB,CAAC,EAAE,KAAK,OAAA,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;;YACtE,IAAI,GAAG,CAAC,GAAG,EAAE;gBACT,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACvC;iBAAM;gBACH,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;gBACrB,oBAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAC/C,MAAA,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAC;gBAClE,oBAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAChD,oBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;gBAC5B,UAAI,IAAI,CAAC,QAAQ,0CAAE,MAAM,EAAE;oBACvB,uBAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE;wBACrC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,qBAAqB;wBAC5D,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,IAAI,EAAE,IAAI,CAAC,QAAQ;qBACtB,CAAC,CAAA;iBACL;aACJ;YACD,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACC,qCAAW,GAAnB,UAAoB,IAAoB;QAAxC,iBAqBC;QApBG,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QAClB,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QAClB,qBAAS,CAAC,kBAAkB,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;;YACjD,IAAI,GAAG,CAAC,GAAG,EAAE;gBACT,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACvC;iBAAM;gBACH,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;gBACrB,oBAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAClD,oBAAO,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACnD,oBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;gBAC5B,UAAI,IAAI,CAAC,QAAQ,0CAAE,MAAM,EAAE;oBACvB,uBAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAAE,IAAA,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;iBACrG;aACJ;YACD,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACC,oCAAU,GAAlB,UAAmB,IAAoB;QAAvC,iBA6BC;QA5BG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAM;SACT;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,qBAAS,CAAC,iBAAiB,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;;YAChD,IAAI,GAAG,CAAC,GAAG,EAAE;gBACT,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACvC;iBAAM;gBACH,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;gBACrB,oBAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAC/C,MAAA,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,0CAAE,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAC;gBACjE,oBAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACjD,oBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;gBAC5B,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,wBAAwB,CAAC,CAAA;gBAC7C,UAAI,IAAI,CAAC,QAAQ,0CAAE,MAAM,EAAE;oBACvB,uBAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE;wBACrC,IAAI,EAAE,oBAAoB;wBAC1B,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,IAAI,EAAE,IAAI,CAAC,QAAQ;qBACtB,CAAC,CAAA;iBACL;aACJ;YACD,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IA9pCgB,eAAe;QADnC,OAAO;OACa,eAAe,CA+pCnC;IAAD,sBAAC;CA/pCD,AA+pCC,CA/pC4C,EAAE,CAAC,WAAW,GA+pC1D;kBA/pCoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ARMY_PAWN_MAX_COUNT, CTYPE_NAME, DEFAULT_PET_ID, SUMMON_LV } from \"../../common/constant/Constant\";\nimport { PawnSkinInfo, TreasureInfo } from \"../../common/constant/DataType\";\nimport { ecode } from \"../../common/constant/ECode\";\nimport { BUILD_NID, BuffType, CType, EquipEffectType, HeroType, PawnSkillType, PawnState, PawnType, PreferenceKey } from \"../../common/constant/Enums\";\nimport { IPawnDrillInfo } from \"../../common/constant/Interface\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { netHelper } from \"../../common/helper/NetHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport ArmyObj from \"../../model/area/ArmyObj\";\nimport BuffObj from \"../../model/area/BuffObj\";\nimport PawnObj from \"../../model/area/PawnObj\";\nimport EquipInfo from \"../../model/main/EquipInfo\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class PawnInfoPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private headNode_: cc.Node = null // path://root/head_be_n\n    private lvEditLbl_: cc.Label = null // path://root/head_be_n/lv/edit/num/lv_edit_l\n    private attackSpeedEditLbl_: cc.Label = null // path://root/head_be_n/attack_speed/edit/num/attack_speed_edit_l\n    private attrNode_: cc.Node = null // path://root/attr_n_be\n    private skillNode_: cc.Node = null // path://root/skill_n\n    private equipNode_: cc.Node = null // path://root/equip_n\n    private fromNode_: cc.Node = null // path://root/from_n\n    private buttonNode_: cc.Node = null // path://root/button_n\n    private buffNode_: cc.Node = null // path://root/buff/buff_n\n    private selectSkinBoxNode_: cc.Node = null // path://select_skin_box_be_n\n    private syncSkinNode_: cc.Node = null // path://select_skin_box_be_n/sync_skin_be_n\n    private syncSkinMaskNode_: cc.Node = null // path://select_skin_box_be_n/sync_skin_mask_be_n\n    private settingSyncSkinNode_: cc.Node = null // path://select_skin_box_be_n/setting_sync_skin_n\n    private selectEquipBoxNode_: cc.Node = null // path://select_equip_box_be_n\n    private syncEquipNode_: cc.Node = null // path://select_equip_box_be_n/sync_equip_be_n\n    private syncEquipMaskNode_: cc.Node = null // path://select_equip_box_be_n/sync_equip_mask_be_n\n    private settingSyncEquipNode_: cc.Node = null // path://select_equip_box_be_n/setting_sync_equip_n\n    private selectPetBoxNode_: cc.Node = null // path://select_pet_box_be_n\n    private selectArmyBoxNode_: cc.Node = null // path://select_army_box_be_n\n    //@end\n\n    private root: cc.Node = null\n    private data: PawnObj = null\n    private drillInfo: IPawnDrillInfo = null\n    private fromTo: string = ''\n    private preAttackSpeed: number = 0\n    private preEquipUid: string = ''\n    private preSkinId: number = 0\n    private prePetId: number = 0\n    private preRootHeight: number = -1\n    private isCanEditSkin: boolean = false\n\n    private isCanEdit: boolean = false\n    private isConfPawn: boolean = false\n    private isBattleing: boolean = false\n\n    public listenEventMaps() {\n        return [\n            { [EventType.UPDATE_PAWN_TREASURE]: this.onUpdatePawnTreasure, enter: true },\n            { [EventType.UPDATE_ARMY]: this.onUpdateArmy, enter: true },\n            { [EventType.UPDATE_BUFF]: this.onUpdateBuff, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.root = this.FindChild('root')\n        this.selectSkinBoxNode_.active = false\n        this.selectEquipBoxNode_.active = false\n        this.selectArmyBoxNode_.active = false\n    }\n\n    public onEnter(data: PawnObj, drillInfo: IPawnDrillInfo, fromTo: string) {\n        gameHpr.uiShowPawnData = data\n        this.data = data\n        this.drillInfo = drillInfo\n        this.fromTo = fromTo\n        this.preAttackSpeed = data.attackSpeed\n        this.preEquipUid = data.equip.uid\n        this.prePetId = data.petId\n        const isOwner = data.isOwner()\n        if (isOwner && !gameHpr.isSpectate() && !gameHpr.user.isHasPawnSkinById(data.skinId)) {\n            data.skinId = 0\n        }\n        this.preSkinId = data.skinId\n        this.data.recordCurrHp(true) //先记录一下\n        const isBattleing = this.isBattleing = data.isBattleing() || gameHpr.isBattleingByIndex(data.aIndex), hasOwner = !!data.owner\n        const isCanEdit = this.isCanEdit = isOwner && !isBattleing && data.getState() === PawnState.NONE && !drillInfo && (!fromTo || fromTo === 'area_army')\n        const isConfPawn = this.isConfPawn = !data.uid && !drillInfo && fromTo !== 'ceri' && fromTo !== 'book' //配置士兵\n        const isInArea = mc.currWindName === 'area' //是否在战斗场景\n        const isFromDrillground = fromTo === 'drillground'\n        const isMachine = data.type >= PawnType.MACHINE //是否器械\n        const isHero = data.isHero() //是否英雄\n        const isCanEditEquip = isOwner && !isBattleing && data.getState() === PawnState.NONE && (!fromTo || fromTo === 'area_army')\n        // 头像\n        const editNode = this.headNode_.Child('edit'), headValNode = this.headNode_.Child('val')\n        headValNode.active = true\n        if (data.isBoss()) { //boss加载mini头像\n            headValNode.y = -32\n            resHelper.loadPawnHeadMiniIcon(data.getViewId(), headValNode, this.key)\n        } else {\n            headValNode.y = -36\n            resHelper.loadPawnHeadIcon(data.getViewId(), headValNode, this.key)\n        }\n        editNode.active = this.isCanEditSkin = this.headNode_.Component(cc.Button).interactable = !isHero && (isCanEdit || isConfPawn || fromTo === 'book')\n        this.headNode_.Child('name/val').setLocaleKey(data.name)\n        this.headNode_.Child('name/type').setLocaleKey(data.type ? data.typeName : '')\n        // 同步皮肤设置\n        this.settingSyncSkinNode_.active = false\n        this.syncSkinMaskNode_.active = false\n        if (this.syncSkinNode_.active = !isConfPawn && isCanEditEquip) {\n            const val = gameHpr.user.getLocalPreferenceData(PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0\n            this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val)\n        }\n        // 等级\n        this.updateLv()\n        // 出手速度\n        const attackSpeed = this.headNode_.Child('attack_speed')\n        if (attackSpeed.active = fromTo !== 'book') {\n            const isCanEditAs = !drillInfo && fromTo !== 'ceri' && (isCanEdit || isConfPawn)\n            attackSpeed.Child('edit').active = isCanEditAs\n            attackSpeed.Child('val').active = !isCanEditAs\n            if (isCanEditAs) {\n                this.attackSpeedEditLbl_.string = data.attackSpeed + ''\n            } else {\n                attackSpeed.Child('val', cc.Label).string = data.attackSpeed + ''\n            }\n        }\n        // 属性\n        viewHelper.updatePawnAttrs(this.attrNode_, this.data)\n        // 技能\n        this.updateSkills()\n        // 装备 和 背包\n        const isYyj = this.data.portrayal?.skill?.id === HeroType.YANG_YOUJI\n        const isEquip = !isMachine && (hasOwner || isConfPawn || !!drillInfo || isYyj)\n        if (this.equipNode_.active = isEquip || !!this.preEquipUid) {\n            const info = this.equipNode_.Child('info')\n            // 装备\n            info.Child('edit_equip_be').active = isCanEditEquip || isConfPawn\n            this.updateEquipInfo(info.Child('equip_show_be'))\n            // 宠物\n            info.Child('edit_pet_be').active = isCanEditEquip && isYyj\n            const petNode = info.Child('pet_show_be')\n            if (petNode.active = isYyj) {\n                this.updatePetInfo(petNode)\n            }\n            // 背包\n            const bagNode = info.Child('bag')\n            if (isEquip) {\n                this.updateBag(bagNode, data.treasures)\n            } else {\n                bagNode.Swih('')\n            }\n        }\n        // 同步装备设置\n        this.settingSyncEquipNode_.active = false\n        this.syncEquipMaskNode_.active = false\n        if (this.syncEquipNode_.active = !isConfPawn && isCanEditEquip) {\n            const val = gameHpr.user.getLocalPreferenceData(PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0\n            this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val)\n        }\n        // 归属\n        const from = this.fromNode_\n        const showMarchSpeed = data.marchSpeed > 0 && !data.uid, showArmy = !!data.armyName, showPlayer = hasOwner && !isOwner\n        if (from.active = showMarchSpeed || showArmy || showPlayer) {\n            // 行军速度\n            if (from.Child('march_speed').active = showMarchSpeed) {\n                from.Child('march_speed/val', cc.Label).setLocaleKey('ui.march_speed_desc', data.marchSpeed)\n            }\n            // 所属军队\n            const army = from.Child('army')\n            if (army.active = showArmy) {\n                army.Child('val/edit').active = isCanEdit && isInArea && !isFromDrillground\n                this.updateArmyInfo(army, data.armyName)\n            }\n            const playerInfo = gameHpr.getPlayerInfo(data.owner)\n            // 所属玩家\n            if (from.Child('player').active = showPlayer) {\n                from.Child('player/val', cc.Label).string = ut.nameFormator(playerInfo?.nickname || '???', 8)\n            }\n            // 所属联盟\n            const alliName = playerInfo?.allianceName\n            if (from.Child('alli').active = showPlayer && !!alliName) {\n                from.Child('alli/val', cc.Label).string = alliName\n            }\n        }\n        // 按钮\n        do {\n            const buttonRoot = this.buttonNode_.Child('root')\n            const isEditPos = isCanEdit && isInArea && !isFromDrillground\n            if (isEditPos) {\n                buttonRoot.Swih('edit_pos_be')\n            } else if (drillInfo) {\n                const node = buttonRoot.Swih('cancel_' + drillInfo.type + '_be')[0]\n                if (drillInfo.type === 'drill') {\n                    node.Child('val').setLocaleKey(data.isMachine() ? 'ui.button_cancel_sc' : 'ui.button_cancel_drill')\n                } else if (drillInfo.type === 'cure') {\n                    node.Child('val').setLocaleKey('ui.button_cancel_cure')\n                }\n            } else {\n                this.buttonNode_.active = false\n                break\n            }\n            const player = gameHpr.player\n            const isLving = player.isInPawnLvingQueue(data.uid)\n            this.buttonNode_.active = true\n            this.buttonNode_.Child('uplv_be').active = isEditPos && !isMachine && !data.isMaxLv() && !isLving\n            const avatarNode = this.buttonNode_.Child('avatar_be')\n            const isCanAvatar = isOwner && !isBattleing && !isHero && !drillInfo\n            if (avatarNode.active = isCanAvatar) {\n                const army = gameHpr.areaCenter.getArmy(data.aIndex, data.armyUid)\n                const isNotAvatar = !army || army.pawns.some(m => m.isHero()) || player.getBuildLv(BUILD_NID.HERO_HALL) <= 0 || player.getHeroSlots().every(m => !m.hero) || !player.checkCanAvatarPawn(data.id)\n                avatarNode.opacity = isNotAvatar ? 100 : 255\n            }\n        } while (false)\n        // 刷新buff\n        this.updateBuffs()\n        this.buffNode_.stopAllActions()\n        this.buffNode_.opacity = 0\n        cc.tween(this.buffNode_).delay(0.2).to(0.5, { opacity: 255 }).start()\n    }\n\n    public onRemove() {\n        gameHpr.uiShowPawnData = null\n        this.selectEquipBoxNode_.active = false\n        this.closeArmyList()\n        this.syncInfoToServer()\n        this.data.recordCurrHp(false)\n        this.data = null\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/head_be_n/attack_speed/edit/0/attack_speed_be\n    onClickAttackSpeed(event: cc.Event.EventTouch, data: string) {\n        const type = event.target.parent.name\n        this.data.changeAttackSpeed(type === '0' ? -1 : 1)\n        this.attackSpeedEditLbl_.string = this.data.attackSpeed + ''\n    }\n\n    // path://root/button_n/root/edit_pos_be\n    onClickEditPos(event: cc.Event.EventTouch, data: string) {\n        this.emit(EventType.EDIT_PAWN_POS, this.data.aIndex, this.data.uid)\n        viewHelper.hidePnl('area/AreaArmy')\n        this.hide()\n    }\n\n    // path://root/button_n/root/cancel_drill_be\n    onClickCancelDrill(event: cc.Event.EventTouch, _: string) {\n        if (!this.drillInfo) {\n            return\n        } else if (this.drillInfo.surplusTime > 0) {\n            return viewHelper.showMessageBox(this.data.isMachine() ? 'ui.cancel_sc_no_back_cost_tip' : 'ui.cancel_drill_no_back_cost_tip', {\n                ok: () => this.isValid && this.cancelDrill(this.drillInfo),\n                cancel: () => { },\n            })\n        }\n        this.cancelDrill(this.drillInfo)\n    }\n\n    // path://root/button_n/root/cancel_lving_be\n    onClickCancelLving(event: cc.Event.EventTouch, _: string) {\n        if (!this.drillInfo) {\n            return\n        } else if (this.drillInfo.surplusTime > 0) {\n            return viewHelper.showMessageBox('ui.cancel_lving_no_back_cost_tip', {\n                ok: () => {\n                    this.cancelLving(this.drillInfo)\n                },\n                cancel: () => { },\n            })\n        }\n        this.cancelLving(this.drillInfo)\n    }\n\n    // path://root/equip_n/info/edit_equip_be\n    onClickEditEquip(event: cc.Event.EventTouch, data: string) {\n        this.showEquipList()\n    }\n\n    // path://select_equip_box_be_n\n    onClickSelectEquipBox(event: cc.Event.EventTouch, data: string) {\n        this.closeEquipList()\n    }\n\n    // path://select_equip_box_be_n/root/list/view/content/equip_item_be\n    onClickEquipItem(event: cc.Event.EventTouch, _: string) {\n        const data: EquipInfo = event.target.Data\n        this.data.changeEquip(data)\n        viewHelper.updatePawnAttrs(this.attrNode_, this.data)\n        if (this.data.id === 3104) { //陌刀兵还需要刷新技能信息\n            this.skillNode_.Child('root/skills').children.forEach(m => {\n                if (!m.Data) {\n                } else if (m.Data.type === PawnSkillType.INSTABILITY_ATTACK) {\n                    m.Data.desc_params = [this.data.getAttackText()]\n                } else if (m.Data.type === PawnSkillType.PEOPLE_BROKEN) {\n                    m.Data.desc_params = [this.data.getAttackTextByIndex(2)]\n                }\n            })\n        }\n        if (gameHpr.guide.isGuideById(3)) {\n            this.closeEquipList()\n        } else {\n            this.updateEquipListSelect(data)\n        }\n    }\n\n    // path://root/from_n/army/val/edit/edit_army_be\n    onClickEditArmy(event: cc.Event.EventTouch, data: string) {\n        this.showArmyList()\n    }\n\n    // path://select_army_box_be_n\n    onClickSelectArmyBox(event: cc.Event.EventTouch, data: string) {\n        this.closeArmyList()\n    }\n\n    // path://select_army_box_be_n/root/list/army_item_be\n    onClickArmyItem(event: cc.Event.EventTouch, _: string) {\n        const data: ArmyObj = event.target.Data\n        if (data) {\n            if (this.data.isHero() && data.isHasHero()) {\n                return viewHelper.showAlert(ecode.ARMY_ONLY_AVATAR_ONE)\n            }\n            return this.changeArmy(data.uid, false)\n        } else if (gameHpr.player.isArmyCountFull()) {\n            return viewHelper.showAlert(ecode.PLAYER_FULL_ARMY)\n        }\n        viewHelper.showPnl('common/CreateArmy', (name: string) => {\n            if (this.isValid) {\n                this.changeArmy(name, true)\n            }\n        })\n    }\n\n    // path://root/skill_n/root/skills/skill_be\n    onClickSkill(event: cc.Event.EventTouch, data: string) {\n        audioMgr.playSFX('click')\n        viewHelper.showPnl('common/SkillInfoBox', event.target.Data)\n    }\n\n    // path://root/equip_n/info/equip_show_be\n    onClickEquipShow(event: cc.Event.EventTouch, _: string) {\n        audioMgr.playSFX('click')\n        const data: EquipInfo = event.target.Data\n        if (data?.id) {\n            viewHelper.showPnl('common/EquipInfoBox', data)\n        } else {\n            this.showEquipList()\n        }\n    }\n\n    // path://root/equip_n/info/bag/bag_be\n    onClickBag(event: cc.Event.EventTouch, data: string) {\n        if (this.data.owner !== gameHpr.getUid()) {\n            return viewHelper.showAlert(ecode.NOT_OPEN_OTHER_TREASURE)\n        } else {\n            viewHelper.showPnl('common/TreasureList', [event.target.Data])\n        }\n    }\n\n    // path://root/attr_n_be\n    onClickAttr(event: cc.Event.EventTouch, data: string) {\n        viewHelper.showPnl('common/PawnAttrBox', this.data)\n    }\n\n    // path://select_skin_box_be_n\n    onClickSelectSkinBox(event: cc.Event.EventTouch, data: string) {\n        this.closeSkinList(this.selectSkinBoxNode_.Child('skin_show').Data || 0)\n    }\n\n    // path://select_skin_box_be_n/root/list/view/content/skin_item_be\n    onClickSkinItem(event: cc.Event.EventTouch, _: string) {\n        const data = event.target.Data\n        data && this.updateSkinListSelect(data)\n    }\n\n    // path://root/head_be_n\n    onClickHead(event: cc.Event.EventTouch, data: string) {\n        audioMgr.playSFX('click')\n        this.showSkinList()\n    }\n\n    // path://select_skin_box_be_n/root/button/buy_skin_be\n    onClickBuySkin(event: cc.Event.EventTouch, _: string) {\n        const data: PawnSkinInfo = event.target.Data\n        if (data && data.gold > 0) {\n            if (gameHpr.user.getGold() < data.gold) {\n                return viewHelper.showGoldNotEnough()\n            }\n            gameHpr.user.buyPawnSkin(data.id).then(err => {\n                if (err) {\n                    return viewHelper.showAlert(err)\n                } else if (this.isValid) {\n                    this.closeSkinList(data.id)\n                }\n            })\n        }\n    }\n\n    // path://select_equip_box_be_n/sync_equip_be_n\n    onClickSyncEquip(event: cc.Event.EventTouch, data: string) {\n        this.settingSyncEquipNode_.active = true\n        this.syncEquipMaskNode_.active = true\n        const val = (gameHpr.user.getLocalPreferenceData(PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0) + ''\n        this.settingSyncEquipNode_.Child('lay').children.forEach(m => {\n            m.Component(cc.Toggle).isChecked = m.name === val\n        })\n    }\n\n    // path://select_equip_box_be_n/sync_equip_mask_be_n\n    onClickSyncEquipMask(event: cc.Event.EventTouch, data: string) {\n        const it = this.settingSyncEquipNode_.Child('lay').children.find(m => m.Component(cc.Toggle).isChecked)\n        const val = it ? Number(it.name) : 0\n        gameHpr.user.setLocalPreferenceData(PreferenceKey.SYNC_PAWN_EQUIP_CONF, val)\n        this.settingSyncEquipNode_.active = false\n        this.syncEquipMaskNode_.active = false\n        this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val)\n    }\n\n    // path://root/button_n/uplv_be\n    onClickUplv(event: cc.Event.EventTouch, data: string) {\n        if (!this.data || this.data.isMaxLv() || this.data.isMachine()) {\n            return\n        }\n        viewHelper.showPnl('area/UpPawnLv', this.data, async (ok: boolean) => {\n            if (!ok || !this.isValid) {\n                return true\n            } else if (gameHpr.player.getUpScroll() < 1) {\n                viewHelper.showAlert('toast.res_deficiency', { params: [CTYPE_NAME[CType.UP_SCROLL]] })\n                return false\n            }\n            const cond = gameHpr.checkCondsByString(this.data?.attrJson?.lv_cond)\n            if (cond?.type === CType.BUILD_LV) {\n                viewHelper.showAlert('toast.build_cond_unmet', { params: ['buildText.name_' + cond.id, cond.count] })\n                return false\n            }\n            return this.upLvByUseScroll()\n        })\n    }\n\n    // path://root/buff/buff_n/buff_icon_be\n    onClickBuffIcon(event: cc.Event.EventTouch, _: string) {\n        const data = event.target.Data\n        if (!data) {\n        } else if (data.iconType === 3) { //韬略\n            viewHelper.showPnl('area/PawnStrategyInfo', this.data)\n        } else if (data.iconType === 4) { //政策\n            viewHelper.showPnl('area/PolicyBuffInfo', data.buffs)\n        } else {\n            viewHelper.showPnl('common/BuffInfoBox', data)\n        }\n    }\n\n    // path://root/head_be_n/lv/edit/0/edit_lv_be\n    onClickEditLv(event: cc.Event.EventTouch, data: string) {\n        const type = event.target.parent.name\n        const val = type === '0' ? -1 : 1\n        let lv = this.data.lv + val\n        if (lv > 6) {\n            lv = 1\n        } else if (lv < 1) {\n            lv = 6\n        }\n        this.localUplv(this.data, lv)\n    }\n\n    // path://root/button_n/avatar_be\n    onClickAvatar(event: cc.Event.EventTouch, _: string) {\n        const pawn = this.data\n        const army = gameHpr.areaCenter.getArmy(pawn.aIndex, pawn.armyUid)\n        if (!army) {\n            return\n        } else if (army.pawns.some(m => m.isHero())) {\n            return viewHelper.showAlert(ecode.ARMY_ONLY_AVATAR_ONE)\n        } else if (gameHpr.player.getBuildLv(BUILD_NID.HERO_HALL) <= 0) {\n            return viewHelper.showAlert('toast.please_build_first', { params: ['buildText.name_' + BUILD_NID.HERO_HALL] })\n        } else if (gameHpr.player.getHeroSlots().every(m => !m.hero)) {\n            return viewHelper.showAlert('toast.please_worship_hero') //请先在英雄殿供奉一个英雄\n        }\n        viewHelper.showPnl('area/SelectAvatarHero', pawn.id, (portrayalId: number) => {\n            if (!this.isValid || !portrayalId) {\n                return\n            }\n            gameHpr.player.changePawnPortrayal(pawn.aIndex, pawn.armyUid, pawn.uid, portrayalId).then(err => {\n                if (err) {\n                    return viewHelper.showAlert(err)\n                } else if (this.isValid) {\n                    this.hide()\n                    viewHelper.hidePnl('area/AreaArmy')\n                    // 聚焦士兵\n                    this.emit(EventType.FOCUS_PAWN, { index: pawn.aIndex, uid: pawn.uid, point: pawn.point })\n                }\n            })\n        })\n    }\n\n    // path://root/skill_n/root/portrayal_skill_be\n    onClickPortrayalSkill(event: cc.Event.EventTouch, data: string) {\n        audioMgr.playSFX('click')\n        if (this.data.portrayal) {\n            viewHelper.showPnl('common/PortrayalInfoBox', this.data.portrayal, 'pawn', this.data.owner)\n        }\n    }\n\n    // path://root/equip_n/info/pet_show_be\n    onClickPetShow(event: cc.Event.EventTouch, _: string) {\n        audioMgr.playSFX('click')\n        const id = event.target.Data\n        if (id) {\n            viewHelper.showPnl('common/PetInfoBox', id, SUMMON_LV[this.data.lv])\n        } else {\n            this.showPetList()\n        }\n    }\n\n    // path://root/equip_n/info/edit_pet_be\n    onClickEditPet(event: cc.Event.EventTouch, data: string) {\n        this.showPetList()\n    }\n\n    // path://select_pet_box_be_n\n    onClickSelectPetBox(event: cc.Event.EventTouch, data: string) {\n        this.closePetList()\n    }\n\n    // path://select_pet_box_be_n/root/list/view/content/pet_item_be\n    onClickPetItem(event: cc.Event.EventTouch, _: string) {\n        const id = event.target.Data\n        if (id === DEFAULT_PET_ID || gameHpr.player.getKillRecordMap()[id]) {\n            this.data.setPetId(id)\n        }\n        this.updatePetListSelect(id)\n    }\n\n    // path://select_skin_box_be_n/sync_skin_be_n\n    onClickSyncSkin(event: cc.Event.EventTouch, data: string) {\n        this.settingSyncSkinNode_.active = true\n        this.syncSkinMaskNode_.active = true\n        const val = (gameHpr.user.getLocalPreferenceData(PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0) + ''\n        this.settingSyncSkinNode_.Child('lay').children.forEach(m => {\n            m.Component(cc.Toggle).isChecked = m.name === val\n        })\n    }\n\n    // path://select_skin_box_be_n/sync_skin_mask_be_n\n    onClickSyncSkinMask(event: cc.Event.EventTouch, data: string) {\n        const it = this.settingSyncSkinNode_.Child('lay').children.find(m => m.Component(cc.Toggle).isChecked)\n        const val = it ? Number(it.name) : 0\n        gameHpr.user.setLocalPreferenceData(PreferenceKey.SYNC_PAWN_SKIN_CONF, val)\n        this.settingSyncSkinNode_.active = false\n        this.syncSkinMaskNode_.active = false\n        this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val)\n    }\n\n    // path://root/button_n/root/cancel_cure_be\n    onClickCancelCure(event: cc.Event.EventTouch, data: string) {\n        if (!this.drillInfo) {\n            return\n        } else if (this.drillInfo.surplusTime > 0) {\n            return viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {\n                ok: () => this.isValid && this.cancelCure(this.drillInfo),\n                cancel: () => { },\n            })\n        }\n        this.cancelCure(this.drillInfo)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    private onUpdatePawnTreasure(pawn: PawnObj) {\n        if (pawn.uid === this.data.uid) {\n            this.updateBag(this.equipNode_.Child('info/bag'), pawn.treasures)\n        }\n    }\n\n    private onUpdateArmy(army: ArmyObj) {\n        if (this.data.armyUid === army.uid) {\n            const uid = this.data.uid\n            this.data = army.pawns.find(m => m.uid === uid) || this.data\n        }\n    }\n\n    // 刷新buff\n    private onUpdateBuff() {\n        this.updateBuffs()\n    }\n\n    // 刷新士兵信息\n    private onUpdatePawnInfo() {\n        this.updateLv()\n        viewHelper.updatePawnAttrs(this.attrNode_, this.data)\n        this.updateSkills()\n        const uplvButton = this.buttonNode_.Child('uplv_be')\n        uplvButton.active = uplvButton.active && !this.data.isMaxLv()\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private updateLv() {\n        const data = this.data\n        const lvNode = this.headNode_.Child('lv'), isNoLv = data.isMachine() || data.isBuilding()\n        if (lvNode.Child('edit').active = !isNoLv && this.fromTo === 'book') {\n            lvNode.Child('name').Color('#756963').setLocaleKey('ui.level')\n            lvNode.Child('val').active = false\n            this.lvEditLbl_.string = data.lv + ''\n        } else if (lvNode.Child('val').active = !isNoLv) {\n            const hasOwner = !!data.owner, isOwner = data.isOwner(), isLving = gameHpr.player.isInPawnLvingQueue(data.uid)\n            const lvColor = hasOwner && data.isMaxLv() ? '#B6A591' : '#756963'\n            lvNode.Child('name').Color(lvColor).setLocaleKey('ui.level')\n            lvNode.Child('val/0', cc.Label).Color(lvColor).string = data.lv + ''\n            lvNode.Child('val/up').active = isOwner && isLving && !this.drillInfo\n        } else {\n            lvNode.Child('name').Color('#B6A591').setLocaleKey('ui.not_lv')\n        }\n    }\n\n    private updateSkills() {\n        this.skillNode_.active = viewHelper.updatePawnSkills(this.skillNode_.Child('root'), this.data, this.key)\n    }\n\n    private updateListPosition() {\n        if (this.preRootHeight !== this.root.height) {\n            this.preRootHeight = this.root.height\n            // 皮肤列表\n            let node = this.selectSkinBoxNode_.Child('root')\n            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('skin_list'), this.selectSkinBoxNode_))\n            node.scale = this.root.scale\n            this.settingSyncSkinNode_.setPosition(node.getPosition())\n            this.settingSyncSkinNode_.scale = this.root.scale\n            node = this.selectSkinBoxNode_.Child('skin_show')\n            node.setPosition(ut.convertToNodeAR(this.headNode_, this.selectSkinBoxNode_))\n            node.scale = this.root.scale\n            node = this.syncSkinNode_\n            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('sync_pos'), this.selectSkinBoxNode_))\n            node.scale = this.root.scale\n            // 装备列表\n            node = this.selectEquipBoxNode_.Child('root')\n            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_list'), this.selectEquipBoxNode_))\n            node.scale = this.root.scale\n            this.settingSyncEquipNode_.setPosition(node.getPosition())\n            this.settingSyncEquipNode_.scale = this.root.scale\n            node = this.selectEquipBoxNode_.Child('equip_show')\n            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_show_be'), this.selectEquipBoxNode_))\n            node.scale = this.root.scale\n            node = this.syncEquipNode_\n            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/sync_pos'), this.selectEquipBoxNode_))\n            node.scale = this.root.scale\n            // 宠物列表\n            node = this.selectPetBoxNode_.Child('root')\n            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_list'), this.selectPetBoxNode_))\n            node.scale = this.root.scale\n            node = this.selectPetBoxNode_.Child('pet_show')\n            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_show_be'), this.selectPetBoxNode_))\n            node.scale = this.root.scale\n            // 军队列表\n            node = this.selectArmyBoxNode_.Child('root')\n            node.setPosition(ut.convertToNodeAR(this.fromNode_.Child('army/val/edit/list'), this.selectArmyBoxNode_))\n            node.scale = this.root.scale\n        }\n    }\n\n    // 显示皮肤列表 ----------------------------------------------------------------------------\n    private showSkinList() {\n        this.headNode_.Child('edit').active = this.headNode_.Child('val').active = false\n        this.selectSkinBoxNode_.active = true\n        this.updateListPosition()\n        const root = this.selectSkinBoxNode_.Child('root')\n        const skins = gameHpr.user.getPawnSkins(this.data.id), len = skins.length\n        const sv = root.Child('list', cc.ScrollView)\n        sv.Child('empty').active = len === 0\n        sv.Items(skins, (it, data) => {\n            it.Data = data\n            const valNode = it.Child('val')\n            valNode.opacity = data.unlock ? 255 : 120\n            resHelper.loadPawnHeadIcon(data?.id || this.data.id, valNode, this.key)\n        })\n        let skinId = this.data.skinId\n        let index = skins.findIndex(m => m.id === skinId)\n        if (index === -1) {\n            index = 0\n            skinId = this.data.skinId = skins[index]?.id ?? 0\n        }\n        const lay = sv.content.Component(cc.Layout), item = sv.GetItemNode()\n        const w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width\n        const minx = Math.max(w - pw, 0)\n        sv.stopAutoScroll()\n        sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx)\n        this.updateSkinListSelect(skins[index])\n    }\n\n    private closeSkinList(skinId: number) {\n        this.headNode_.Child('val').active = true\n        this.headNode_.Child('edit').active = this.isCanEditSkin\n        this.selectSkinBoxNode_.active = false\n        if (this.data.skinId !== skinId) {\n            this.data.skinId = skinId\n            resHelper.loadPawnHeadIcon(this.data.getViewId(), this.headNode_.Child('val'), this.key)\n            if (this.fromTo !== 'book') {\n                if (!this.data.uid && !this.drillInfo) {\n                    gameHpr.player.changeConfigPawnInfoByData(this.data)\n                }\n                eventCenter.emit(EventType.CHANGE_PAWN_SKIN, this.data)\n            }\n        }\n    }\n\n    private updateSkinListSelect(skin: PawnSkinInfo) {\n        const root = this.selectSkinBoxNode_.Child('root')\n        // 刷新选择\n        root.Child('list', cc.ScrollView).content.children.forEach(it => {\n            const id = it.Data?.id || 0\n            it.Child('select1').active = it.Child('select2').active = skin.id === id\n            it.Component(cc.Button).interactable = skin.id !== id\n        })\n        // 显示选择的\n        if (skin.unlock || !skin.id) {\n            const node = this.selectSkinBoxNode_.Child('skin_show')\n            node.Data = skin.id\n            resHelper.loadPawnHeadIcon(skin.id || this.data.id, node.Child('val'), this.key)\n        }\n        // 显示信息\n        const desc = skin.id ? assetsMgr.getJsonData('pawnSkin', skin.id)?.desc || '' : 'ui.default_pawn_skin'\n        root.Child('desc').setLocaleKey(desc)\n        const stateNode = root.Child('state')\n        if (stateNode.active = !!skin.id && !skin.gold) {\n            stateNode.Color(skin.unlock ? '#4AB32E' : '#A18876').setLocaleKey(skin.unlock ? 'ui.yet_owned' : 'ui.not_owned')\n        }\n        const buttonNode = root.Child('button')\n        if (buttonNode.active = !!skin.gold) {\n            const buyNode = buttonNode.Child('buy_skin_be')\n            buyNode.Data = skin\n            buyNode.Child('lay/gold/val', cc.Label).string = skin.gold + ''\n        }\n    }\n\n    // 刷新装备信息 ----------------------------------------------------------------------------\n    private updateEquipInfo(node: cc.Node, equip?: EquipInfo) {\n        equip = node.Data = equip || this.data.equip\n        const isOwner = this.data.isOwner() && this.fromTo !== 'drillground'\n        const isCanEditEquip = isOwner && !this.isBattleing && this.data.getState() === PawnState.NONE && (!this.fromTo || this.fromTo === 'area_army')\n        node.Component(cc.Button).interactable = !!equip?.id || this.isConfPawn || isCanEditEquip\n        if (equip?.id) {\n            resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount())\n        } else if (isOwner || this.isCanEdit || this.isConfPawn) {\n            node.Swih('add')[0].Child('dot').active = gameHpr.player.getEquips().length > 0\n        } else {\n            node.Swih('')\n        }\n    }\n\n    // 显示装备列表\n    private showEquipList() {\n        if (this.data.isOwner() && this.isBattleing) {\n            return viewHelper.showAlert(ecode.BATTLEING)\n        }\n        this.selectEquipBoxNode_.active = true\n        this.updateListPosition()\n        const root = this.selectEquipBoxNode_.Child('root')\n        const equips = gameHpr.player.getPawnEquips(this.data.id), len = equips.length\n        const randomArr = ut.stringToNumbers(assetsMgr.getJson('equipBase').get('exclusive_pawn', this.data.id)?.[0]?.random, ',')\n        const isHp = randomArr[0] > randomArr[1]\n        equips.sort((a, b) => {\n            let aw = a.isExclusive() ? 1 : 0, bw = b.isExclusive() ? 1 : 0\n            if (isHp) {\n                aw = aw * 10 + (a.hp ? 1 : 0)\n                bw = bw * 10 + (b.hp ? 1 : 0)\n            } else {\n                aw = aw * 10 + (a.attack ? 1 : 0)\n                bw = bw * 10 + (b.attack ? 1 : 0)\n            }\n            return bw - aw\n        })\n        const equip = this.equipNode_.Child('info/equip_show_be').Data\n        const sv = root.Child('list', cc.ScrollView)\n        sv.Child('empty').active = len === 0\n        sv.Items(equips, (it, data) => {\n            it.Data = data\n            resHelper.loadEquipIcon(data.id, it.Child('val'), this.key, data.getSmeltCount())\n            it.Child('recommend').active = false\n        })\n        if (equip?.uid) {\n            const index = equips.findIndex(m => m.uid === equip.uid)\n            const lay = sv.content.Component(cc.Layout), item = sv.GetItemNode()\n            const w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width\n            const minx = Math.max(w - pw, 0)\n            sv.stopAutoScroll()\n            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx)\n        } else {\n            sv.scrollToLeft()\n        }\n        this.updateEquipListSelect(equip)\n    }\n\n    // 刷新选择信息\n    private updateEquipListSelect(equip: EquipInfo) {\n        const root = this.selectEquipBoxNode_.Child('root')\n        // 刷新选择\n        root.Child('list', cc.ScrollView).content.children.forEach(it => {\n            const data = it.Data\n            const select = it.Child('select').active = equip?.uid === data?.uid\n            it.Component(cc.Button).interactable = !select\n        })\n        // 显示选择的\n        const node = this.selectEquipBoxNode_.Child('equip_show')\n        if (equip?.id) {\n            resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount())\n        } else {\n            node.Swih('add')\n        }\n        // 显示信息\n        root.Child('empty').active = !equip?.id\n        const sv = root.Child('info', cc.ScrollView), info = sv.content\n        sv.stopAutoScroll()\n        info.y = 0\n        if (sv.setActive(!!equip?.id)) {\n            viewHelper.updateEquipView(info, equip, this.key)\n            ut.waitNextFrame(2).then(() => {\n                if (this.isValid) {\n                    sv.node.height = Math.min(320, info.height + 4)\n                    sv.node.Child('view', cc.Widget).updateAlignment()\n                }\n            })\n        }\n    }\n\n    private closeEquipList() {\n        this.selectEquipBoxNode_.active = false\n        this.updateEquipInfo(this.equipNode_.Child('info/equip_show_be'))\n    }\n\n    // 刷新宠物信息 ----------------------------------------------------------------------------\n    private updatePetInfo(node: cc.Node, id?: number) {\n        id = node.Data = id || this.data.petId || DEFAULT_PET_ID\n        resHelper.loadPawnHeadMiniIcon(id, node.Child('val'), this.key)\n    }\n\n    // 显示宠物列表\n    private showPetList() {\n        if (this.data.isOwner() && this.isBattleing) {\n            return viewHelper.showAlert(ecode.BATTLEING)\n        }\n        this.selectPetBoxNode_.active = true\n        this.updateListPosition()\n        const root = this.selectPetBoxNode_.Child('root')\n        const pets = assetsMgr.getJson('pawnBase').datas.filter(m => m.type === PawnType.BEAST && !!m.velocity).sort((a, b) => a.drill_time - b.drill_time), len = pets.length\n        const id = this.equipNode_.Child('info/pet_show_be').Data\n        const sv = root.Child('list', cc.ScrollView)\n        const killRecordMap = gameHpr.player.getKillRecordMap()\n        sv.Items(pets, (it, json) => {\n            it.Data = json.id\n            const icon = it.Child('val')\n            resHelper.loadPawnHeadMiniIcon(json.id, icon, this.key)\n            icon.opacity = json.id === DEFAULT_PET_ID || killRecordMap[json.id] ? 255 : 120\n        })\n        if (id) {\n            const index = pets.findIndex(m => m.id === id)\n            const lay = sv.content.Component(cc.Layout), item = sv.GetItemNode()\n            const w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width\n            const minx = Math.max(w - pw, 0)\n            sv.stopAutoScroll()\n            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx)\n        } else {\n            sv.scrollToLeft()\n        }\n        this.updatePetListSelect(id)\n    }\n\n    // 刷新选择信息\n    private updatePetListSelect(id: number) {\n        const root = this.selectPetBoxNode_.Child('root')\n        // 刷新选择\n        root.Child('list', cc.ScrollView).content.children.forEach(it => {\n            it.Child('select').active = id === it.Data\n            it.Component(cc.Button).interactable = id !== it.Data\n        })\n        // 显示选择的\n        const node = this.selectPetBoxNode_.Child('pet_show')\n        if (id) {\n            resHelper.loadPawnHeadMiniIcon(id, node.Swih('val')[0], this.key)\n        } else {\n            node.Swih('add')\n        }\n        // 显示信息\n        const lv = id === DEFAULT_PET_ID || gameHpr.player.getKillRecordMap()[id] ? SUMMON_LV[this.data.lv] : 0\n        viewHelper.updatePetView(root.Child('info'), id, lv)\n    }\n\n    private closePetList() {\n        this.selectPetBoxNode_.active = false\n        this.updatePetInfo(this.equipNode_.Child('info/pet_show_be'))\n    }\n\n    // 刷新军队信息 ----------------------------------------------------------------------------\n    private updateArmyInfo(node?: cc.Node, armyName?: string) {\n        node = node || this.fromNode_.Child('army')\n        armyName = armyName || this.data.armyName\n        node.Child('val/name/val', cc.Label).string = armyName\n    }\n\n    // 显示军队列表\n    private showArmyList() {\n        this.selectArmyBoxNode_.active = true\n        this.updateListPosition()\n        const root = this.selectArmyBoxNode_.Child('root')\n        const uid = this.data.armyUid\n        const arr = gameHpr.areaCenter.getArea(this.data.aIndex)?.armys || []\n        const armys: ArmyObj[] = []\n        arr.forEach(m => {\n            if (m.uid == uid) {\n                armys.unshift(m)\n            } else if (m.isCanDrillPawn() && m.getActPawnCount() < ARMY_PAWN_MAX_COUNT) {\n                armys.push(m)\n            }\n        })\n        if (armys.length < gameHpr.player.getArmyMaxCount()) {\n            armys.push(null)\n        }\n        const len = armys.length\n        root.Swih('list')[0].Items(armys, (it, data, i) => {\n            it.Data = data\n            const select = uid === data?.uid\n            it.Child('name', cc.Label).Color(select ? '#B6A591' : '#756963').string = data?.name || ''\n            it.Child('line').active = i < (len - 1)\n            it.Child('select').active = select\n            it.Child('add').active = !data\n            it.Component(cc.Button).interactable = !select\n        })\n    }\n\n    private closeArmyList() {\n        this.selectArmyBoxNode_.active = false\n    }\n\n    // 改变军队\n    private async changeArmy(newArmyUid: string, isNewCreate: boolean) {\n        const pawn = this.data\n        const index = pawn.aIndex\n        const armyUid = pawn.armyUid\n        const uid = pawn.uid\n        const attackSpeed = pawn.attackSpeed\n        const equipUid = pawn.equip.uid\n        const skinId = pawn.skinId\n        const petId = pawn.petId\n        if (!isNewCreate) {\n            pawn.armyUid = newArmyUid\n        }\n        const { err, data } = await netHelper.reqChangePawnArmy({ index, armyUid, uid, newArmyUid, isNewCreate, attackSpeed, equipUid, skinId, petId })\n        if (err) {\n            pawn.armyUid = armyUid\n            return viewHelper.showAlert(err)\n        }\n        pawn.changeArmy(data)\n        if (this.isValid) {\n            this.preAttackSpeed = attackSpeed\n            this.preEquipUid = equipUid\n            this.preSkinId = skinId\n            this.updateArmyInfo()\n            this.closeArmyList()\n        }\n        if (this.fromTo === 'area_army') {\n            this.emit(EventType.UPDATE_AREA_ARMY_LIST, index)\n            this.hide()\n        }\n    }\n\n    // 背包\n    private updateBag(node: cc.Node, treasures: TreasureInfo[]) {\n        if (this.equipNode_.active && this.data) {\n            const data = this.data\n            const cap = Math.max(treasures?.length || 0, data.baseJson?.bag_cap || 0)\n            node.Items(cap, (it, _, i) => {\n                const treasure = it.Data = treasures[i]\n                it.Swih(treasure ? 'val' : 'empty')\n                if (it.Component(cc.Button).interactable = !!treasure) {\n                    const state = treasure.rewards.length > 0 ? 1 : 0\n                    resHelper.loadIcon('icon/treasure_' + (treasure.json?.lv || 1) + '_' + state, it.Child('val', cc.Sprite), this.key)\n                }\n            })\n        } else {\n            node.Swih('')\n        }\n    }\n\n    // 刷新buff\n    private updateBuffs() {\n        const buffs: BuffObj[] = [], policyBuffs: BuffObj[] = []\n        const heroSkill = this.data.portrayal?.skill\n        this.data.buffs.forEach(m => {\n            if (m.iconType === 4) {\n                return policyBuffs.push(m)\n            } else if (m.type === BuffType.LOW_HP_ADD_ATTACK || m.type === BuffType.LOW_HP_ADD_SUCKBLOOD) {\n                let buff = buffs.find(b => !!b && (b.type === BuffType.LOW_HP_ADD_ATTACK || b.type === BuffType.LOW_HP_ADD_SUCKBLOOD))\n                if (buff) {\n                    buff.tempParam = m.value\n                } else {\n                    buffs.push(m)\n                }\n                return\n            } else if (m.type === BuffType.DELAY_DEDUCT_HP) {\n                m.tempParam = this.data.getEquipEffectByType(EquipEffectType.FIXED_DAMAGE)?.value ?? 0\n            } else if (m.type === BuffType.THOUSAND_UMBRELLA) { //千机伞\n                const effect = this.data.getEquipEffectByType(EquipEffectType.THOUSAND_UMBRELLA)\n                m.tempParam = [effect?.value || 1, effect?.odds || 1]\n                const v = m.tempParam[m.value]\n                if (v) {\n                    m.tempParam[m.value] = v * 2\n                }\n            } else if (m.type === BuffType.TOUGH) { //曹仁 判断是否叠满\n                m.tempParam = heroSkill?.id === HeroType.CAO_REN ? heroSkill.value : 50\n            } else if (m.type === BuffType.TIGER_MANIA) { //许褚 虎痴\n                m.tempParam = heroSkill?.id === HeroType.XU_CHU ? heroSkill.value : 200\n            } else if (m.type === BuffType.CHECK_ABNEGATION) { //吕蒙 检测克己\n                m.tempParam = heroSkill?.id === HeroType.LV_MENG ? heroSkill.value : 0\n            } else if (m.type === BuffType.CHECK_LITTLE_GIRL) { //孙尚香 枭姬\n                m.tempParam = this.data.isMaxLv() ? 2 : 1\n            } else if (m.type === BuffType.COURAGEOUSLY) { //典韦 奋勇是否满层\n                m.tempParam = heroSkill?.id === HeroType.DIAN_WEI ? heroSkill.value : 50\n            } else if (m.type === BuffType.RECURRENCE) { //孟获 再起\n                m.tempParam = heroSkill?.id === HeroType.MENG_HUO ? heroSkill.value : 15\n            }\n            if (m.icon) {\n                buffs.push(m)\n            }\n        })\n        buffs.sort((a, b) => {\n            const aw = a ? a.effectType : 100, bw = b ? b.effectType : 100\n            return bw - aw\n        })\n        // 是否有韬略\n        if (this.data.isHasStrategy()) {\n            buffs.unshift({ iconType: 3, icon: 0 } as any)\n        }\n        // 是否有政策\n        if (policyBuffs.length > 0) {\n            buffs.unshift({ iconType: 4, icon: 1000, buffs: policyBuffs } as any)\n        }\n        this.buffNode_.Items(buffs, (it, data) => {\n            it.Data = data\n            it.Component(cc.MultiFrame).setFrame(data.iconType)\n            resHelper.loadBuffIcon(data.icon, it.Child('val'), this.key, false)\n        })\n    }\n\n    // 使用卷轴升级士兵\n    private async upLvByUseScroll() {\n        await this.syncInfoToServer(true) //先同步一下属性\n        const pawn = this.data\n        const { err, data } = await netHelper.reqUseUpScrollUpPawnLv({ index: pawn.aIndex, armyUid: pawn.armyUid, uid: pawn.uid })\n        if (err) {\n            viewHelper.showAlert(err)\n            return false\n        }\n        gameHpr.player.updateRewardItemsByFlags(data.cost)\n        viewHelper.showAlert('toast.up_pawn_lv_succeed')\n        this.localUplv(pawn, data.lv)\n        if (this.fromTo === 'area_army') {\n            this.emit(EventType.UPDATE_AREA_ARMY_LIST, pawn.index)\n        }\n        return true\n    }\n\n    private localUplv(pawn: PawnObj, lv: number) {\n        pawn.lv = lv\n        pawn.updateAttrJson()\n        pawn.curHp = pawn.getMaxHp()\n        pawn.recordCurrHp(true)\n        if (this.isValid) {\n            this.onUpdatePawnInfo()\n        }\n    }\n\n    // 同步信息到服务器\n    private async syncInfoToServer(wait?: boolean) {\n        if (this.fromTo === 'book') {\n            return\n        }\n        const data = this.data\n        const isEquip = this.preEquipUid !== data.equip.uid\n        const isBattleing = data.isBattleing() || gameHpr.isBattleingByIndex(data.aIndex)\n        const isChange = this.preAttackSpeed !== data.attackSpeed || isEquip || this.preSkinId !== data.skinId || this.prePetId !== data.petId\n        // 同步训练士兵装备信息\n        if (!data.uid && !this.drillInfo && isChange) {\n            const id = data.id, equipUid = data.equip.uid, skinId = data.skinId, attackSpeed = data.attackSpeed\n            const res = await netHelper.reqChangeConfigPawnEquip({ id, equipUid, skinId, attackSpeed }, wait)\n            if (!res.err) {\n                gameHpr.player.changeConfigPawnInfo(id, equipUid, skinId, attackSpeed)\n            }\n            this.emit(EventType.UPDATE_AREA_ARMY_LIST, data.index)\n        } else if (data.isOwner() && !isBattleing && isChange) {\n            const syncEquip = gameHpr.user.getLocalPreferenceData(PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0\n            const syncSkin = gameHpr.user.getLocalPreferenceData(PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0\n            const res = await netHelper.reqChangePawnAttr({ //同步士兵的属性信息\n                index: data.aIndex,\n                armyUid: data.armyUid,\n                uid: data.uid,\n                attackSpeed: data.attackSpeed,\n                equipUid: data.equip.uid,\n                syncEquip: syncEquip,\n                skinId: data.skinId,\n                syncSkin: syncSkin,\n                petId: data.petId,\n            }, wait)\n            if (!res.err && !!syncEquip && isEquip) {\n                viewHelper.showAlert('toast.replace_pawn_equp_suc_' + syncEquip, { params: ['pawnText.name_' + data.id], showTime: 2 })\n            }\n            this.emit(EventType.UPDATE_AREA_ARMY_LIST, data.index)\n        }\n    }\n\n    // 取消招募\n    private cancelDrill(info: IPawnDrillInfo) {\n        if (!this.data) {\n            return\n        }\n        const index = info.index\n        const uid = info.uid\n        const json = info.json\n        const isMachine = this.data.isMachine()\n        netHelper.reqCancelDrillPawn({ index, buildUid: info.buid, uid }).then(res => {\n            if (res.err) {\n                return viewHelper.showAlert(res.err)\n            } else {\n                const data = res.data\n                gameHpr.player.updateOutputByFlags(data.output)\n                gameHpr.areaCenter.getArea(index)?.updateArmyDrillPawns(data.army)\n                gameHpr.player.updatePawnDrillQueue(data.queues)\n                gameHpr.delMessageByTag(uid)\n                if (data.needCost?.length) {\n                    viewHelper.showPnl('common/CancelDrill', {\n                        text: isMachine ? 'ui.cancel_sc_tip' : 'ui.cancel_drill_tip',\n                        id: json.id,\n                        cost: data.needCost,\n                    })\n                }\n            }\n            if (this.isValid) {\n                this.hide()\n            }\n        })\n    }\n\n    // 取消训练\n    private cancelLving(info: IPawnDrillInfo) {\n        const index = info.index\n        const uid = info.uid\n        const id = info.id\n        const lv = info.lv\n        netHelper.reqCancelPawnLving({ index, uid }).then(res => {\n            if (res.err) {\n                return viewHelper.showAlert(res.err)\n            } else {\n                const data = res.data\n                gameHpr.player.updateRewardItemsByFlags(data.cost)\n                gameHpr.player.updatePawnLevelingQueue(data.queues)\n                gameHpr.delMessageByTag(uid)\n                if (data.needCost?.length) {\n                    viewHelper.showPnl('common/CancelDrill', { text: 'ui.cancel_lving_tip', id, cost: data.needCost })\n                }\n            }\n            if (this.isValid) {\n                this.hide()\n            }\n        })\n    }\n\n    // 取消治疗\n    private cancelCure(info: IPawnDrillInfo) {\n        if (!this.data) {\n            return\n        }\n        const index = info.index\n        const uid = info.uid\n        const json = info.json\n        netHelper.reqCancelCurePawn({ index, uid }).then(res => {\n            if (res.err) {\n                return viewHelper.showAlert(res.err)\n            } else {\n                const data = res.data\n                gameHpr.player.updateOutputByFlags(data.output)\n                gameHpr.areaCenter.getArea(index)?.updateArmyCurePawns(data.army)\n                gameHpr.player.updatePawnCuringQueue(data.queues)\n                gameHpr.delMessageByTag(uid)\n                this.emit(EventType.UPDATE_PAWN_INJURY_QUEUE)\n                if (data.needCost?.length) {\n                    viewHelper.showPnl('common/CancelDrill', {\n                        text: 'ui.cancel_cure_tip',\n                        id: json.id,\n                        cost: data.needCost,\n                    })\n                }\n            }\n            if (this.isValid) {\n                this.hide()\n            }\n        })\n    }\n}\n"]}