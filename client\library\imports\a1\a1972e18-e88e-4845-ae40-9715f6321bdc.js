"use strict";
cc._RF.push(module, 'a19724Y6I5IRa5AlxX2Mhvc', 'ResHelper');
// app/script/common/helper/ResHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resHelper = void 0;
var Constant_1 = require("../constant/Constant");
var OutlineShaderCtrl_1 = require("../shader/OutlineShaderCtrl");
var GameHelper_1 = require("./GameHelper");
/**
 * 游戏中的资源相关帮助方法
 */
var ResHelper = /** @class */ (function () {
    function ResHelper() {
        this.lands = {};
        this.landMap = {};
        this.seawavePrefabs = {};
        this.cityPrefabs = {};
        this.mapFlagNumbers = {};
        this.spriteDefaultMaterial = null;
        this.sprite2dGrayMaterial = null;
    }
    ResHelper.prototype.init = function (progessCallback) {
        return __awaiter(this, void 0, void 0, function () {
            var sfs, pfbs;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.lands = {};
                        assetsMgr.debug = false;
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('land', cc.SpriteFrame, '_land_res_', function (done, total) { return progessCallback(done / total); })];
                    case 1:
                        sfs = _a.sent();
                        sfs.forEach(function (m) { return _this.lands[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('seawave', cc.Prefab, '_seawave_prefab_', function (done, total) { return progessCallback(done / total); })];
                    case 2:
                        pfbs = _a.sent();
                        pfbs.forEach(function (m) { return _this.seawavePrefabs[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('city', cc.Prefab, '_city_prefab_', function (done, total) { return progessCallback(done / total); })];
                    case 3:
                        pfbs = _a.sent();
                        pfbs.forEach(function (m) { return _this.cityPrefabs[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('map_flag_num', cc.SpriteFrame, '_land_res_', function (done, total) { return progessCallback(done / total); })];
                    case 4:
                        // 地图标记数字
                        sfs = _a.sent();
                        sfs.forEach(function (m) { return _this.mapFlagNumbers[m.name] = m; });
                        assetsMgr.debug = true;
                        // 材质
                        this.spriteDefaultMaterial = cc.Material.getBuiltinMaterial('2d-sprite');
                        this.sprite2dGrayMaterial = cc.Material.getBuiltinMaterial('2d-gray-sprite');
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.initNovice = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('other/CITY_10010', cc.Prefab, key)];
                    case 1:
                        pfb = _a.sent();
                        this.cityPrefabs[pfb.name] = pfb;
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.cleanNovice = function () {
        delete this.cityPrefabs['CITY_10010'];
    };
    ResHelper.prototype.getPawnName = function (id) { return 'pawnText.name_' + id; };
    ResHelper.prototype.checkLandSkin = function (type) {
        return !!this.landMap[type];
    };
    // 初始化地块皮肤
    ResHelper.prototype.initLandSkin = function (type, progessCallback) {
        return __awaiter(this, void 0, void 0, function () {
            var obj, sfs;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        obj = this.landMap[type];
                        if (obj) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('land_' + type, cc.SpriteFrame, '_land_res_' + type, function (done, total) { return progessCallback && progessCallback(done / total); })];
                    case 1:
                        sfs = _a.sent();
                        obj = this.landMap[type] = {};
                        sfs.forEach(function (m) { return obj[m.name] = m; });
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.cleanLandSkin = function () {
        var type = GameHelper_1.gameHpr.world.getSeason().type;
        for (var k in this.landMap) {
            if (Number(k) !== type) {
                delete this.landMap[k];
                assetsMgr.releaseTempResByTag('_land_res_' + k);
            }
        }
    };
    // 根据下标获取节点
    ResHelper.prototype.getNodeByIndex = function (node, i, position) {
        var it = node.children[i] || cc.instantiate2(node.children[0], node);
        it.active = true;
        it.Data = null;
        it.setPosition(position);
        return it;
    };
    ResHelper.prototype.getNodeByIndex2 = function (node, i, position) {
        var it = node.children[i] || cc.instantiate2(node.children[0], node);
        it.active = true;
        it.Data = null;
        it.setPosition(position.x, position.y - Constant_1.TILE_SIZE_HALF.y);
        return it;
    };
    // 隐藏多于的
    ResHelper.prototype.hideNodeByIndex = function (node, idx) {
        for (var i = idx, l = node.childrenCount; i < l; i++) {
            var it = node.children[i];
            it.active = false;
            it.Data = null;
        }
    };
    // 清理子节点只剩1个
    ResHelper.prototype.cleanNodeChildren = function (node) {
        var _a;
        for (var i = node.childrenCount - 1; i >= 1; i--) {
            node.children[i].destroy();
        }
        (_a = node.children[0]) === null || _a === void 0 ? void 0 : _a.setActive(false);
    };
    // 获取sprite材质
    ResHelper.prototype.get2dSpriteMaterial = function (unlock) {
        return unlock ? this.spriteDefaultMaterial : this.sprite2dGrayMaterial;
    };
    // 纯色灰材质
    ResHelper.prototype.getSpriteColorGrayMaterial = function (unlock) {
        return unlock ? this.spriteDefaultMaterial : assetsMgr.getMaterial('SpriteColorGrey');
    };
    // 地面icon
    ResHelper.prototype.getLandIcon = function (icon) {
        if (icon.startsWith('land_')) {
            return this.getLandItemIcon(icon, GameHelper_1.gameHpr.world.getSeasonType());
        }
        return this.lands[icon];
    };
    // 地面资源icon
    ResHelper.prototype.getLandItemIcon = function (icon, type) {
        var _a;
        return ((_a = this.landMap[type]) === null || _a === void 0 ? void 0 : _a[icon]) || this.lands[icon] || null;
    };
    // 获取海浪预制体
    ResHelper.prototype.getSeawavePrefab = function (id) {
        return this.seawavePrefabs['SEAWAVE_' + id];
    };
    // 获取城市预制体
    ResHelper.prototype.getCityPrefab = function (id) {
        return this.cityPrefabs['CITY_' + id];
    };
    // 获取资源icon
    ResHelper.prototype.getResIcon = function (type) {
        return assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
    };
    // 获取地图标记数字
    ResHelper.prototype.getMapFlagNumber = function (flag) {
        var key = flag <= 0 ? 'x' : (flag - 1);
        return this.mapFlagNumbers['map_flag_' + key];
    };
    // 加载icon
    ResHelper.prototype.loadIcon = function (url, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            var spr, sf;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!url || !icon || !icon.isValid) {
                            return [2 /*return*/, null];
                        }
                        spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite);
                        if (!spr) {
                            return [2 /*return*/, null];
                        }
                        else if (setEmpty) {
                            spr.spriteFrame = null;
                        }
                        spr['__load_icon_url'] = url;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.SpriteFrame, key)];
                    case 1:
                        sf = _a.sent();
                        if (spr.isValid && spr['__load_icon_url'] === url) {
                            spr.spriteFrame = sf || null;
                        }
                        return [2 /*return*/, spr];
                }
            });
        });
    };
    // 加载设施icon
    ResHelper.prototype.loadBuildIcon = function (url, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('build/' + url, icon, key)];
            });
        });
    };
    // 加载城市icon
    ResHelper.prototype.loadCityIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('city/city_' + id, icon, key)];
            });
        });
    };
    // 加载士兵头像icon
    ResHelper.prototype.loadPawnHeadIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (id === 3405104) { //牛仔隐藏款 特殊处理
                    return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id + "_00", icon, key)];
                }
                return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id + "_01", icon, key)];
            });
        });
    };
    // 加载士兵小头像icon
    ResHelper.prototype.loadPawnHeadMiniIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载技能图标
    ResHelper.prototype.loadSkillIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('skill/skill_' + id, icon, key)];
            });
        });
    };
    // 加载装备icon
    ResHelper.prototype.loadEquipIcon = function (id, icon, key, smeltLv) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var spr, outline;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.loadIcon('equip/equip_' + id, icon, key)];
                    case 1:
                        spr = _b.sent();
                        if (!spr || !spr.isValid) {
                        }
                        else if (smeltLv) {
                            outline = spr.getComponent(OutlineShaderCtrl_1.default) || spr.addComponent(OutlineShaderCtrl_1.default);
                            outline.setTarget(spr);
                            outline.setOutlineSize(2);
                            outline.setColor(ut.colorFromHEX(smeltLv === 1 ? '#58F1FF' : '#E488FF'));
                            outline.setVisible(true);
                            // spr.node.adaptScale(size, cc.size(size.width + 8, size.height + 8))
                        }
                        else {
                            (_a = spr.getComponent(OutlineShaderCtrl_1.default)) === null || _a === void 0 ? void 0 : _a.setVisible(false);
                            // spr.node.scale = 1
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载政策icon
    ResHelper.prototype.loadPolicyIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('policy/policy_' + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载buff icon
    ResHelper.prototype.loadBuffIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('buff_icon/buff_' + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载联盟图标
    ResHelper.prototype.loadAlliIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('alli_icon/alli_icon_' + id, icon, key)];
            });
        });
    };
    // 加载评分图标
    ResHelper.prototype.loadRankScoreIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('rank_icon/rank_icon_' + (id >= 0 ? id : 'none'), icon, key)];
            });
        });
    };
    // 加载礼物图标
    ResHelper.prototype.loadGiftIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('gift/gift_' + id, icon, key)];
            });
        });
    };
    // 加载表情
    ResHelper.prototype.loadEmojiIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('emoji/emoji_' + id, icon, key)];
            });
        });
    };
    // 加载画像
    ResHelper.prototype.loadPortrayalImage = function (id, icon, key) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_b) {
                if ((_a = assetsMgr.getJsonData('portrayalBase', id)) === null || _a === void 0 ? void 0 : _a.has_anim) {
                    return [2 /*return*/, this.loadIcon("portrayal_anim/" + id + "/portrayal_" + id + "_01", icon, key)];
                }
                return [2 /*return*/, this.loadIcon('portrayal/portrayal_' + id, icon, key)];
            });
        });
    };
    // 加载残卷遮挡
    ResHelper.prototype.loadPortrayalDebrisMask = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('portrayal/pd_mask_' + id, icon, key)];
            });
        });
    };
    // 加载英雄技能图标
    ResHelper.prototype.loadHeroSkillIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('hero_skill_icon/hero_skill_' + id, icon, key)];
            });
        });
    };
    // 加载英雄预制体
    ResHelper.prototype.loadHeroMarchPrefab = function (id, key) {
        return __awaiter(this, void 0, void 0, function () {
            var pfbName, pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        pfbName = 'ROLE_' + id;
                        return [4 /*yield*/, assetsMgr.loadTempRes('march/' + pfbName, cc.Prefab, key)];
                    case 1:
                        pfb = _a.sent();
                        if ((pfb === null || pfb === void 0 ? void 0 : pfb.name) !== pfbName) {
                            return [2 /*return*/, null];
                        }
                        return [2 /*return*/, pfb];
                }
            });
        });
    };
    // 加载植物种子图标
    ResHelper.prototype.loadBotanySeedIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('botany_seed/botany_seed_' + id, icon, key)];
            });
        });
    };
    // 加载表情节点
    ResHelper.prototype.loadEmojiNode = function (id, root, scale, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            var node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!root || !id) {
                            return [2 /*return*/];
                        }
                        else if (setEmpty) {
                            root.removeAllChildren();
                        }
                        return [4 /*yield*/, nodePoolMgr.get('emoji/EMOJI_' + id, key)];
                    case 1:
                        node = _a.sent();
                        if (node && root.isValid) {
                            node.parent = root;
                            node.active = true;
                            node.setPosition(0, 0);
                            node.scaleX = scale || 1;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载玩家头像
    ResHelper.prototype.loadPlayerHead = function (icon, url, key, setEmpty) {
        return __awaiter(this, void 0, void 0, function () {
            var spr, val, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(icon === null || icon === void 0 ? void 0 : icon.isValid)) {
                            return [2 /*return*/];
                        }
                        spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite);
                        if (!spr) {
                            return [2 /*return*/];
                        }
                        else if (setEmpty) {
                            spr.spriteFrame = null;
                            spr.node.removeAllChildren();
                        }
                        url = spr['_player_head_icon_'] = url || 'head_icon_free_001';
                        val = null;
                        if (!url.startsWith('head_icon_anim_')) return [3 /*break*/, 2];
                        return [4 /*yield*/, assetsMgr.loadTempRes('headicon/' + url, cc.Prefab, key)];
                    case 1:
                        val = _a.sent();
                        return [3 /*break*/, 6];
                    case 2:
                        if (!url.startsWith('head_icon_')) return [3 /*break*/, 4];
                        return [4 /*yield*/, assetsMgr.loadTempRes('headicon/' + url, cc.SpriteFrame, key)];
                    case 3:
                        val = _a.sent();
                        return [3 /*break*/, 6];
                    case 4: return [4 /*yield*/, assetsMgr.loadRemote(url, '.jpg', key || '_player_head_')];
                    case 5:
                        val = _a.sent();
                        _a.label = 6;
                    case 6:
                        if (spr.isValid && spr['_player_head_icon_'] === url) {
                            spr.node.removeAllChildren();
                            if (!val) {
                                spr.spriteFrame = null;
                            }
                            else if (val instanceof cc.Prefab) {
                                spr.spriteFrame = null;
                                node = cc.instantiate2(val, spr.node);
                                node.setContentSize(spr.node.width * spr.node.scaleX, spr.node.height * spr.node.scaleY);
                            }
                            else if (val instanceof cc.SpriteFrame) {
                                spr.spriteFrame = val;
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    return ResHelper;
}());
exports.resHelper = new ResHelper();
if (cc.sys.isBrowser) {
    window['resHelper'] = exports.resHelper;
}

cc._RF.pop();