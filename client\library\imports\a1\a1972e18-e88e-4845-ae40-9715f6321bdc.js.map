{"version": 3, "sources": ["assets\\app\\script\\common\\helper\\ResHelper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAiE;AAEjE,iEAA2D;AAC3D,2CAAsC;AAEtC;;GAEG;AACH;IAAA;QAEY,UAAK,GAAsC,EAAE,CAAA;QAC7C,YAAO,GAAyD,EAAE,CAAA;QAClE,mBAAc,GAAiC,EAAE,CAAA;QACjD,gBAAW,GAAiC,EAAE,CAAA;QAC9C,mBAAc,GAAsC,EAAE,CAAA;QAEtD,0BAAqB,GAAgB,IAAI,CAAA;QACzC,yBAAoB,GAAgB,IAAI,CAAA;IAuTpD,CAAC;IArTgB,wBAAI,GAAjB,UAAkB,eAA0C;;;;;;;wBACxD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;wBACf,SAAS,CAAC,KAAK,GAAG,KAAK,CAAA;wBACb,qBAAM,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,eAAe,CAAC,IAAI,GAAG,KAAK,CAAC,EAA7B,CAA6B,CAAC,EAAA;;wBAA1H,GAAG,GAAG,SAAoH;wBAC9H,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAtB,CAAsB,CAAC,CAAA;wBAC7B,qBAAM,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,eAAe,CAAC,IAAI,GAAG,KAAK,CAAC,EAA7B,CAA6B,CAAC,EAAA;;wBAA/H,IAAI,GAAG,SAAwH;wBACnI,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAA/B,CAA+B,CAAC,CAAA;wBAC3C,qBAAM,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,eAAe,CAAC,IAAI,GAAG,KAAK,CAAC,EAA7B,CAA6B,CAAC,EAAA;;wBAAzH,IAAI,GAAG,SAAkH,CAAA;wBACzH,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAA5B,CAA4B,CAAC,CAAA;wBAEzC,qBAAM,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,eAAe,CAAC,IAAI,GAAG,KAAK,CAAC,EAA7B,CAA6B,CAAC,EAAA;;wBADlI,SAAS;wBACT,GAAG,GAAG,SAA4H,CAAA;wBAClI,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAA/B,CAA+B,CAAC,CAAA;wBACjD,SAAS,CAAC,KAAK,GAAG,IAAI,CAAA;wBACtB,KAAK;wBACL,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAA;wBACxE,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAA;;;;;KAC/E;IAEY,8BAAU,GAAvB,UAAwB,GAAW;;;;;4BACnB,qBAAM,SAAS,CAAC,WAAW,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,EAAA;;wBAArE,GAAG,GAAG,SAA+D;wBAC3E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;;;;;KACnC;IAEM,+BAAW,GAAlB;QACI,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;IACzC,CAAC;IAEM,+BAAW,GAAlB,UAAmB,EAAU,IAAI,OAAO,gBAAgB,GAAG,EAAE,CAAA,CAAC,CAAC;IAExD,iCAAa,GAApB,UAAqB,IAAY;QAC7B,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,UAAU;IACG,gCAAY,GAAzB,UAA0B,IAAY,EAAE,eAA2C;;;;;;wBAC3E,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;wBAC5B,IAAI,GAAG,EAAE;4BACL,sBAAM;yBACT;wBACW,qBAAM,SAAS,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,EAAE,EAAE,CAAC,WAAW,EAAE,YAAY,GAAG,IAAI,EAAE,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,eAAe,IAAI,eAAe,CAAC,IAAI,GAAG,KAAK,CAAC,EAAhD,CAAgD,CAAC,EAAA;;wBAA5J,GAAG,GAAG,SAAsJ;wBAClK,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;wBAC7B,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAf,CAAe,CAAC,CAAA;;;;;KACpC;IAEM,iCAAa,GAApB;QACI,IAAM,IAAI,GAAG,oBAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,CAAA;QAC3C,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;YACxB,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;gBACtB,SAAS,CAAC,mBAAmB,CAAC,YAAY,GAAG,CAAC,CAAC,CAAA;aAClD;SACJ;IACL,CAAC;IAED,WAAW;IACJ,kCAAc,GAArB,UAAsB,IAAa,EAAE,CAAS,EAAE,QAAiB;QAC7D,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QACtE,EAAE,CAAC,MAAM,GAAG,IAAI,CAAA;QAChB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;QACd,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QACxB,OAAO,EAAE,CAAA;IACb,CAAC;IACM,mCAAe,GAAtB,UAAuB,IAAa,EAAE,CAAS,EAAE,QAAiB;QAC9D,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QACtE,EAAE,CAAC,MAAM,GAAG,IAAI,CAAA;QAChB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;QACd,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,yBAAc,CAAC,CAAC,CAAC,CAAA;QACzD,OAAO,EAAE,CAAA;IACb,CAAC;IAED,QAAQ;IACD,mCAAe,GAAtB,UAAuB,IAAa,EAAE,GAAW;QAC7C,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClD,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;YAC3B,EAAE,CAAC,MAAM,GAAG,KAAK,CAAA;YACjB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;SACjB;IACL,CAAC;IAED,YAAY;IACL,qCAAiB,GAAxB,UAAyB,IAAa;;QAClC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;SAC7B;QACD,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;IACtC,CAAC;IAED,aAAa;IACN,uCAAmB,GAA1B,UAA2B,MAAe;QACtC,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAA;IAC1E,CAAC;IAED,QAAQ;IACD,8CAA0B,GAAjC,UAAkC,MAAe;QAC7C,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;IACzF,CAAC;IAED,SAAS;IACF,+BAAW,GAAlB,UAAmB,IAAY;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,oBAAO,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;SACnE;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAC3B,CAAC;IAED,WAAW;IACJ,mCAAe,GAAtB,UAAuB,IAAY,EAAE,IAAY;;QAC7C,OAAO,OAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0CAAG,IAAI,MAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA;IACjE,CAAC;IAED,UAAU;IACH,oCAAgB,GAAvB,UAAwB,EAAU;QAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED,UAAU;IACH,iCAAa,GAApB,UAAqB,EAAU;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,CAAC,CAAA;IACzC,CAAC;IAED,WAAW;IACJ,8BAAU,GAAjB,UAAkB,IAAW;QACzB,OAAO,SAAS,CAAC,QAAQ,CAAC,qBAAU,CAAC,IAAI,CAAC,CAAC,CAAA;IAC/C,CAAC;IAED,WAAW;IACJ,oCAAgB,GAAvB,UAAwB,IAAY;QAChC,IAAM,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;QACxC,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,GAAG,CAAC,CAAA;IACjD,CAAC;IAED,SAAS;IACI,4BAAQ,GAArB,UAAsB,GAAW,EAAE,IAAyB,EAAE,GAAW,EAAE,QAAwB;QAAxB,yBAAA,EAAA,eAAwB;;;;;;wBAC/F,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;4BAChC,sBAAO,IAAI,EAAA;yBACd;wBACK,GAAG,GAAG,IAAI,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;wBACxE,IAAI,CAAC,GAAG,EAAE;4BACN,sBAAO,IAAI,EAAA;yBACd;6BAAM,IAAI,QAAQ,EAAE;4BACjB,GAAG,CAAC,WAAW,GAAG,IAAI,CAAA;yBACzB;wBACD,GAAG,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAA;wBACjB,qBAAM,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,EAAA;;wBAA1D,EAAE,GAAG,SAAqD;wBAChE,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,iBAAiB,CAAC,KAAK,GAAG,EAAE;4BAC/C,GAAG,CAAC,WAAW,GAAG,EAAE,IAAI,IAAI,CAAA;yBAC/B;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,WAAW;IACE,iCAAa,GAA1B,UAA2B,GAAW,EAAE,IAAyB,EAAE,GAAW;;;gBAC1E,sBAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KAClD;IAED,WAAW;IACE,gCAAY,GAAzB,UAA0B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBACxE,sBAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KACrD;IAED,aAAa;IACA,oCAAgB,GAA7B,UAA8B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBAC5E,IAAI,EAAE,KAAK,OAAO,EAAE,EAAE,YAAY;oBAC9B,sBAAO,IAAI,CAAC,QAAQ,CAAC,UAAQ,EAAE,cAAS,EAAE,QAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;iBAC9D;gBACD,sBAAO,IAAI,CAAC,QAAQ,CAAC,UAAQ,EAAE,cAAS,EAAE,QAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KAC9D;IAED,cAAc;IACD,wCAAoB,GAAjC,UAAkC,EAAU,EAAE,IAAyB,EAAE,GAAW,EAAE,QAAwB;QAAxB,yBAAA,EAAA,eAAwB;;;gBAC1G,sBAAO,IAAI,CAAC,QAAQ,CAAC,UAAQ,EAAE,cAAS,EAAI,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,EAAA;;;KACrE;IAED,SAAS;IACI,iCAAa,GAA1B,UAA2B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBACzE,sBAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KACvD;IAED,WAAW;IACE,iCAAa,GAA1B,UAA2B,EAAU,EAAE,IAAyB,EAAE,GAAW,EAAE,OAAgB;;;;;;4BAC/E,qBAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;wBAAzD,GAAG,GAAG,SAAmD;wBAC/D,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;yBACzB;6BAAM,IAAI,OAAO,EAAE;4BAEV,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,2BAAiB,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,2BAAiB,CAAC,CAAA;4BAC1F,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;4BACtB,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;4BACzB,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;4BACxE,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;4BACxB,sEAAsE;yBACzE;6BAAM;4BACH,MAAA,GAAG,CAAC,YAAY,CAAC,2BAAiB,CAAC,0CAAE,UAAU,CAAC,KAAK,EAAC;4BACtD,qBAAqB;yBACxB;;;;;KACJ;IAED,WAAW;IACE,kCAAc,GAA3B,UAA4B,EAAU,EAAE,IAAyB,EAAE,GAAW,EAAE,QAAwB;QAAxB,yBAAA,EAAA,eAAwB;;;gBACpG,sBAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,EAAA;;;KACnE;IAED,cAAc;IACD,gCAAY,GAAzB,UAA0B,EAAU,EAAE,IAAyB,EAAE,GAAW,EAAE,QAAwB;QAAxB,yBAAA,EAAA,eAAwB;;;gBAClG,sBAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,EAAA;;;KACpE;IAED,SAAS;IACI,gCAAY,GAAzB,UAA0B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBACxE,sBAAO,IAAI,CAAC,QAAQ,CAAC,sBAAsB,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KAC/D;IAED,SAAS;IACI,qCAAiB,GAA9B,UAA+B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBAC7E,sBAAO,IAAI,CAAC,QAAQ,CAAC,sBAAsB,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KACpF;IAED,SAAS;IACI,gCAAY,GAAzB,UAA0B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBACxE,sBAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KACrD;IAED,OAAO;IACM,iCAAa,GAA1B,UAA2B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBACzE,sBAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KACvD;IAED,OAAO;IACM,sCAAkB,GAA/B,UAAgC,EAAU,EAAE,IAAyB,EAAE,GAAW;;;;gBAC9E,UAAI,SAAS,CAAC,WAAW,CAAC,eAAe,EAAE,EAAE,CAAC,0CAAE,QAAQ,EAAE;oBACtD,sBAAO,IAAI,CAAC,QAAQ,CAAC,oBAAkB,EAAE,mBAAc,EAAE,QAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;iBAC7E;gBACD,sBAAO,IAAI,CAAC,QAAQ,CAAC,sBAAsB,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KAC/D;IAED,SAAS;IACI,2CAAuB,GAApC,UAAqC,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBACnF,sBAAO,IAAI,CAAC,QAAQ,CAAC,oBAAoB,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KAC7D;IAED,WAAW;IACE,qCAAiB,GAA9B,UAA+B,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBAC7E,sBAAO,IAAI,CAAC,QAAQ,CAAC,6BAA6B,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KACtE;IAED,UAAU;IACG,uCAAmB,GAAhC,UAAiC,EAAU,EAAE,GAAW;;;;;;wBAC9C,OAAO,GAAG,OAAO,GAAG,EAAE,CAAA;wBAChB,qBAAM,SAAS,CAAC,WAAW,CAAC,QAAQ,GAAG,OAAO,EAAE,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,EAAA;;wBAArE,GAAG,GAAG,SAA+D;wBAC3E,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,MAAK,OAAO,EAAE;4BACvB,sBAAO,IAAI,EAAA;yBACd;wBACD,sBAAO,GAAG,EAAA;;;;KACb;IAED,WAAW;IACE,sCAAkB,GAA/B,UAAgC,EAAU,EAAE,IAAyB,EAAE,GAAW;;;gBAC9E,sBAAO,IAAI,CAAC,QAAQ,CAAC,0BAA0B,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAA;;;KACnE;IAED,SAAS;IACI,iCAAa,GAA1B,UAA2B,EAAU,EAAE,IAAa,EAAE,KAAa,EAAE,GAAW,EAAE,QAAwB;QAAxB,yBAAA,EAAA,eAAwB;;;;;;wBACtG,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;4BACd,sBAAM;yBACT;6BAAM,IAAI,QAAQ,EAAE;4BACjB,IAAI,CAAC,iBAAiB,EAAE,CAAA;yBAC3B;wBACY,qBAAM,WAAW,CAAC,GAAG,CAAC,cAAc,GAAG,EAAE,EAAE,GAAG,CAAC,EAAA;;wBAAtD,IAAI,GAAG,SAA+C;wBAC5D,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;4BACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;4BAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;4BAClB,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;4BACtB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,CAAA;yBAC3B;;;;;KACJ;IAED,SAAS;IACI,kCAAc,GAA3B,UAA4B,IAAyB,EAAE,GAAW,EAAE,GAAW,EAAE,QAAkB;;;;;;wBAC/F,IAAI,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,CAAA,EAAE;4BAChB,sBAAM;yBACT;wBACK,GAAG,GAAG,IAAI,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;wBACxE,IAAI,CAAC,GAAG,EAAE;4BACN,sBAAM;yBACT;6BAAM,IAAI,QAAQ,EAAE;4BACjB,GAAG,CAAC,WAAW,GAAG,IAAI,CAAA;4BACtB,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAA;yBAC/B;wBACD,GAAG,GAAG,GAAG,CAAC,oBAAoB,CAAC,GAAG,GAAG,IAAI,oBAAoB,CAAA;wBACzD,GAAG,GAAQ,IAAI,CAAA;6BACf,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAjC,wBAAiC;wBAC3B,qBAAM,SAAS,CAAC,WAAW,CAAC,WAAW,GAAG,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,EAAA;;wBAApE,GAAG,GAAG,SAA8D,CAAA;;;6BAC7D,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAA5B,wBAA4B;wBAC7B,qBAAM,SAAS,CAAC,WAAW,CAAC,WAAW,GAAG,GAAG,EAAE,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,EAAA;;wBAAzE,GAAG,GAAG,SAAmE,CAAA;;4BAEnE,qBAAM,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,EAAA;;wBAArE,GAAG,GAAG,SAA+D,CAAA;;;wBAEzE,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,oBAAoB,CAAC,KAAK,GAAG,EAAE;4BAClD,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAA;4BAC5B,IAAI,CAAC,GAAG,EAAE;gCACN,GAAG,CAAC,WAAW,GAAG,IAAI,CAAA;6BACzB;iCAAM,IAAI,GAAG,YAAY,EAAE,CAAC,MAAM,EAAE;gCACjC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAA;gCAChB,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;gCAC3C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;6BAC3F;iCAAM,IAAI,GAAG,YAAY,EAAE,CAAC,WAAW,EAAE;gCACtC,GAAG,CAAC,WAAW,GAAG,GAAG,CAAA;6BACxB;yBACJ;;;;;KACJ;IACL,gBAAC;AAAD,CAhUA,AAgUC,IAAA;AAEY,QAAA,SAAS,GAAG,IAAI,SAAS,EAAE,CAAA;AACxC,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE;IAClB,MAAM,CAAC,WAAW,CAAC,GAAG,iBAAS,CAAA;CAClC", "file": "", "sourceRoot": "/", "sourcesContent": ["import { CTYPE_ICON, TILE_SIZE_HALF } from \"../constant/Constant\"\nimport { CType } from \"../constant/Enums\"\nimport OutlineShaderCtrl from \"../shader/OutlineShaderCtrl\"\nimport { gameHpr } from \"./GameHelper\"\n\n/**\n * 游戏中的资源相关帮助方法\n */\nclass ResHelper {\n\n    private lands: { [key: string]: cc.SpriteFrame } = {}\n    private landMap: { [key: number]: { [key: string]: cc.SpriteFrame } } = {}\n    private seawavePrefabs: { [key: string]: cc.Prefab } = {}\n    private cityPrefabs: { [key: string]: cc.Prefab } = {}\n    private mapFlagNumbers: { [key: string]: cc.SpriteFrame } = {}\n\n    private spriteDefaultMaterial: cc.Material = null\n    private sprite2dGrayMaterial: cc.Material = null\n\n    public async init(progessCallback: (percent: number) => void) {\n        this.lands = {}\n        assetsMgr.debug = false\n        let sfs = await assetsMgr.loadTempRseDir('land', cc.SpriteFrame, '_land_res_', (done, total) => progessCallback(done / total))\n        sfs.forEach(m => this.lands[m.name] = m)\n        let pfbs = await assetsMgr.loadTempRseDir('seawave', cc.Prefab, '_seawave_prefab_', (done, total) => progessCallback(done / total))\n        pfbs.forEach(m => this.seawavePrefabs[m.name] = m)\n        pfbs = await assetsMgr.loadTempRseDir('city', cc.Prefab, '_city_prefab_', (done, total) => progessCallback(done / total))\n        pfbs.forEach(m => this.cityPrefabs[m.name] = m)\n        // 地图标记数字\n        sfs = await assetsMgr.loadTempRseDir('map_flag_num', cc.SpriteFrame, '_land_res_', (done, total) => progessCallback(done / total))\n        sfs.forEach(m => this.mapFlagNumbers[m.name] = m)\n        assetsMgr.debug = true\n        // 材质\n        this.spriteDefaultMaterial = cc.Material.getBuiltinMaterial('2d-sprite')\n        this.sprite2dGrayMaterial = cc.Material.getBuiltinMaterial('2d-gray-sprite')\n    }\n\n    public async initNovice(key: string) {\n        const pfb = await assetsMgr.loadTempRes('other/CITY_10010', cc.Prefab, key)\n        this.cityPrefabs[pfb.name] = pfb\n    }\n\n    public cleanNovice() {\n        delete this.cityPrefabs['CITY_10010']\n    }\n\n    public getPawnName(id: number) { return 'pawnText.name_' + id }\n\n    public checkLandSkin(type: number) {\n        return !!this.landMap[type]\n    }\n\n    // 初始化地块皮肤\n    public async initLandSkin(type: number, progessCallback?: (percent: number) => void) {\n        let obj = this.landMap[type]\n        if (obj) {\n            return\n        }\n        const sfs = await assetsMgr.loadTempRseDir('land_' + type, cc.SpriteFrame, '_land_res_' + type, (done, total) => progessCallback && progessCallback(done / total))\n        obj = this.landMap[type] = {}\n        sfs.forEach(m => obj[m.name] = m)\n    }\n\n    public cleanLandSkin() {\n        const type = gameHpr.world.getSeason().type\n        for (let k in this.landMap) {\n            if (Number(k) !== type) {\n                delete this.landMap[k]\n                assetsMgr.releaseTempResByTag('_land_res_' + k)\n            }\n        }\n    }\n\n    // 根据下标获取节点\n    public getNodeByIndex(node: cc.Node, i: number, position: cc.Vec2) {\n        const it = node.children[i] || cc.instantiate2(node.children[0], node)\n        it.active = true\n        it.Data = null\n        it.setPosition(position)\n        return it\n    }\n    public getNodeByIndex2(node: cc.Node, i: number, position: cc.Vec2) {\n        const it = node.children[i] || cc.instantiate2(node.children[0], node)\n        it.active = true\n        it.Data = null\n        it.setPosition(position.x, position.y - TILE_SIZE_HALF.y)\n        return it\n    }\n\n    // 隐藏多于的\n    public hideNodeByIndex(node: cc.Node, idx: number) {\n        for (let i = idx, l = node.childrenCount; i < l; i++) {\n            const it = node.children[i]\n            it.active = false\n            it.Data = null\n        }\n    }\n\n    // 清理子节点只剩1个\n    public cleanNodeChildren(node: cc.Node) {\n        for (let i = node.childrenCount - 1; i >= 1; i--) {\n            node.children[i].destroy()\n        }\n        node.children[0]?.setActive(false)\n    }\n\n    // 获取sprite材质\n    public get2dSpriteMaterial(unlock: boolean) {\n        return unlock ? this.spriteDefaultMaterial : this.sprite2dGrayMaterial\n    }\n\n    // 纯色灰材质\n    public getSpriteColorGrayMaterial(unlock: boolean) {\n        return unlock ? this.spriteDefaultMaterial : assetsMgr.getMaterial('SpriteColorGrey')\n    }\n\n    // 地面icon\n    public getLandIcon(icon: string) {\n        if (icon.startsWith('land_')) {\n            return this.getLandItemIcon(icon, gameHpr.world.getSeasonType())\n        }\n        return this.lands[icon]\n    }\n\n    // 地面资源icon\n    public getLandItemIcon(icon: string, type: number) {\n        return this.landMap[type]?.[icon] || this.lands[icon] || null\n    }\n\n    // 获取海浪预制体\n    public getSeawavePrefab(id: number) {\n        return this.seawavePrefabs['SEAWAVE_' + id]\n    }\n\n    // 获取城市预制体\n    public getCityPrefab(id: number) {\n        return this.cityPrefabs['CITY_' + id]\n    }\n\n    // 获取资源icon\n    public getResIcon(type: CType) {\n        return assetsMgr.getImage(CTYPE_ICON[type])\n    }\n\n    // 获取地图标记数字\n    public getMapFlagNumber(flag: number) {\n        const key = flag <= 0 ? 'x' : (flag - 1)\n        return this.mapFlagNumbers['map_flag_' + key]\n    }\n\n    // 加载icon\n    public async loadIcon(url: string, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {\n        if (!url || !icon || !icon.isValid) {\n            return null\n        }\n        const spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite)\n        if (!spr) {\n            return null\n        } else if (setEmpty) {\n            spr.spriteFrame = null\n        }\n        spr['__load_icon_url'] = url\n        const sf = await assetsMgr.loadTempRes(url, cc.SpriteFrame, key)\n        if (spr.isValid && spr['__load_icon_url'] === url) {\n            spr.spriteFrame = sf || null\n        }\n        return spr\n    }\n\n    // 加载设施icon\n    public async loadBuildIcon(url: string, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('build/' + url, icon, key)\n    }\n\n    // 加载城市icon\n    public async loadCityIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('city/city_' + id, icon, key)\n    }\n\n    // 加载士兵头像icon\n    public async loadPawnHeadIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        if (id === 3405104) { //牛仔隐藏款 特殊处理\n            return this.loadIcon(`role/${id}/role_${id}_00`, icon, key)\n        }\n        return this.loadIcon(`role/${id}/role_${id}_01`, icon, key)\n    }\n\n    // 加载士兵小头像icon\n    public async loadPawnHeadMiniIcon(id: number, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {\n        return this.loadIcon(`role/${id}/role_${id}`, icon, key, setEmpty)\n    }\n\n    // 加载技能图标\n    public async loadSkillIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('skill/skill_' + id, icon, key)\n    }\n\n    // 加载装备icon\n    public async loadEquipIcon(id: number, icon: cc.Sprite | cc.Node, key: string, smeltLv?: number) {\n        const spr = await this.loadIcon('equip/equip_' + id, icon, key)\n        if (!spr || !spr.isValid) {\n        } else if (smeltLv) {\n            // const size = spr.node.getContentSize()\n            const outline = spr.getComponent(OutlineShaderCtrl) || spr.addComponent(OutlineShaderCtrl)\n            outline.setTarget(spr)\n            outline.setOutlineSize(2)\n            outline.setColor(ut.colorFromHEX(smeltLv === 1 ? '#58F1FF' : '#E488FF'))\n            outline.setVisible(true)\n            // spr.node.adaptScale(size, cc.size(size.width + 8, size.height + 8))\n        } else {\n            spr.getComponent(OutlineShaderCtrl)?.setVisible(false)\n            // spr.node.scale = 1\n        }\n    }\n\n    // 加载政策icon\n    public async loadPolicyIcon(id: number, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {\n        return this.loadIcon('policy/policy_' + id, icon, key, setEmpty)\n    }\n\n    // 加载buff icon\n    public async loadBuffIcon(id: number, icon: cc.Sprite | cc.Node, key: string, setEmpty: boolean = true) {\n        return this.loadIcon('buff_icon/buff_' + id, icon, key, setEmpty)\n    }\n\n    // 加载联盟图标\n    public async loadAlliIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('alli_icon/alli_icon_' + id, icon, key)\n    }\n\n    // 加载评分图标\n    public async loadRankScoreIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('rank_icon/rank_icon_' + (id >= 0 ? id : 'none'), icon, key)\n    }\n\n    // 加载礼物图标\n    public async loadGiftIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('gift/gift_' + id, icon, key)\n    }\n\n    // 加载表情\n    public async loadEmojiIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('emoji/emoji_' + id, icon, key)\n    }\n\n    // 加载画像\n    public async loadPortrayalImage(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        if (assetsMgr.getJsonData('portrayalBase', id)?.has_anim) {\n            return this.loadIcon(`portrayal_anim/${id}/portrayal_${id}_01`, icon, key)\n        }\n        return this.loadIcon('portrayal/portrayal_' + id, icon, key)\n    }\n\n    // 加载残卷遮挡\n    public async loadPortrayalDebrisMask(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('portrayal/pd_mask_' + id, icon, key)\n    }\n\n    // 加载英雄技能图标\n    public async loadHeroSkillIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('hero_skill_icon/hero_skill_' + id, icon, key)\n    }\n\n    // 加载英雄预制体\n    public async loadHeroMarchPrefab(id: number, key: string) {\n        const pfbName = 'ROLE_' + id\n        const pfb = await assetsMgr.loadTempRes('march/' + pfbName, cc.Prefab, key)\n        if (pfb?.name !== pfbName) {\n            return null\n        }\n        return pfb\n    }\n\n    // 加载植物种子图标\n    public async loadBotanySeedIcon(id: number, icon: cc.Sprite | cc.Node, key: string) {\n        return this.loadIcon('botany_seed/botany_seed_' + id, icon, key)\n    }\n\n    // 加载表情节点\n    public async loadEmojiNode(id: number, root: cc.Node, scale: number, key: string, setEmpty: boolean = true) {\n        if (!root || !id) {\n            return\n        } else if (setEmpty) {\n            root.removeAllChildren()\n        }\n        const node = await nodePoolMgr.get('emoji/EMOJI_' + id, key)\n        if (node && root.isValid) {\n            node.parent = root\n            node.active = true\n            node.setPosition(0, 0)\n            node.scaleX = scale || 1\n        }\n    }\n\n    // 加载玩家头像\n    public async loadPlayerHead(icon: cc.Sprite | cc.Node, url: string, key: string, setEmpty?: boolean) {\n        if (!icon?.isValid) {\n            return\n        }\n        const spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite)\n        if (!spr) {\n            return\n        } else if (setEmpty) {\n            spr.spriteFrame = null\n            spr.node.removeAllChildren()\n        }\n        url = spr['_player_head_icon_'] = url || 'head_icon_free_001'\n        let val: any = null\n        if (url.startsWith('head_icon_anim_')) {\n            val = await assetsMgr.loadTempRes('headicon/' + url, cc.Prefab, key)\n        } else if (url.startsWith('head_icon_')) {\n            val = await assetsMgr.loadTempRes('headicon/' + url, cc.SpriteFrame, key)\n        } else {\n            val = await assetsMgr.loadRemote(url, '.jpg', key || '_player_head_')\n        }\n        if (spr.isValid && spr['_player_head_icon_'] === url) {\n            spr.node.removeAllChildren()\n            if (!val) {\n                spr.spriteFrame = null\n            } else if (val instanceof cc.Prefab) {\n                spr.spriteFrame = null\n                const node = cc.instantiate2(val, spr.node)\n                node.setContentSize(spr.node.width * spr.node.scaleX, spr.node.height * spr.node.scaleY)\n            } else if (val instanceof cc.SpriteFrame) {\n                spr.spriteFrame = val\n            }\n        }\n    }\n}\n\nexport const resHelper = new ResHelper()\nif (cc.sys.isBrowser) {\n    window['resHelper'] = resHelper\n}"]}