{"version": 3, "sources": ["assets\\app\\script\\common\\helper\\ViewHelper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,wDAAkD;AAElD,+DAAuE;AACvE,iDAA6Q;AAE7Q,2CAAyC;AACzC,2CAAyG;AAEzG,8CAAwC;AACxC,8CAAwC;AACxC,2CAAsC;AACtC,yCAAuC;AACvC,yCAAuC;AACvC,kEAA4D;AAC5D,8DAAwD;AACxD,yEAAmE;AAEnE,0EAAoE;AACpE,oDAA8C;AAG9C;;GAEG;AACH;IAAA;IA6yCA,CAAC;IA3yCG,OAAO;IACM,6BAAQ,GAArB,UAAsB,GAAW;QAAE,gBAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,+BAAc;;;;gBAC7C,sBAAO,IAAI,OAAO,CAAO,UAAA,OAAO,IAAI,OAAA,WAAW,CAAC,IAAI,OAAhB,WAAW,YAAM,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,GAAK,MAAM,IAA5D,CAA6D,CAAC,EAAA;;;KACrG;IAED,QAAQ;IACK,gCAAW,GAAxB,UAAyB,GAAW,EAAE,QAAoC;;;gBACtE,sBAAO,IAAI,OAAO,CAAO,UAAA,OAAO,IAAI,OAAA,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,EAA/D,CAA+D,CAAC,EAAA;;;KACvG;IAED,QAAQ;IACK,+BAAU,GAAvB,UAAwB,GAAW,EAAE,QAAgD;;;gBACjF,sBAAO,IAAI,OAAO,CAAiB,UAAA,OAAO,IAAI,OAAA,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,EAA9D,CAA8D,CAAC,EAAA;;;KAChH;IAED,OAAO;IACM,4BAAO,GAApB,UAAqB,GAA4B;QAAE,gBAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,+BAAc;;;;gBAC7D,sBAAO,IAAI,OAAO,CAAiB,UAAA,OAAO,IAAI,OAAA,WAAW,CAAC,IAAI,OAAhB,WAAW,YAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,GAAK,MAAM,IAA3D,CAA4D,CAAC,EAAA;;;KAC9G;IAED,OAAO;IACA,4BAAO,GAAd,UAAe,GAA4B;QACvC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;IAC5C,CAAC;IAED,QAAQ;IACD,8BAAS,GAAhB,UAAiB,GAAW,EAAE,IAAgB;QAC1C,IAAI,GAAG,KAAK,aAAK,CAAC,YAAY,EAAE;YAC5B,OAAO,WAAW,CAAC,IAAI,CAAC,kBAAQ,CAAC,cAAc,CAAC,CAAA;SACnD;QACD,WAAW,CAAC,IAAI,CAAC,kBAAQ,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IACpD,CAAC;IAED,QAAQ;IACD,mCAAc,GAArB,UAAsB,GAAW,EAAE,IAAqB;QACpD,WAAW,CAAC,IAAI,CAAC,kBAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IAC1D,CAAC;IAED,UAAU;IACH,mCAAc,GAArB;QACI,WAAW,CAAC,IAAI,CAAC,kBAAQ,CAAC,gBAAgB,CAAC,CAAA;IAC/C,CAAC;IAED,OAAO;IACA,6BAAQ,GAAf,UAAgB,IAAY,EAAE,MAAc;QACxC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,IAAI,MAAA,EAAE,MAAM,QAAA,EAAE,CAAC,CAAA;IACjD,CAAC;IAED,SAAS;IACF,iCAAY,GAAnB,UAAoB,IAAuC,EAAE,KAA4B;QAA5B,sBAAA,EAAA,oBAA4B;QACrF,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAA;IACpD,CAAC;IAED,SAAS;IACF,gCAAW,GAAlB,UAAmB,GAAY,EAAE,KAAc;QAC3C,IAAI,GAAG,EAAE;YACL,WAAW,CAAC,IAAI,CAAC,kBAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;SAClD;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,kBAAQ,CAAC,WAAW,CAAC,CAAA;SACzC;IACL,CAAC;IAED,WAAW;IACJ,oCAAe,GAAtB,UAAuB,GAAY;QAC/B,IAAI,GAAG,EAAE;YACL,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;SAChD;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;SAC9C;IACL,CAAC;IAED,cAAc;IACP,oCAAe,GAAtB,UAAuB,GAAY;QAC/B,IAAI,GAAG,EAAE;YACL,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;SAC7C;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;SAC3C;IACL,CAAC;IAED,SAAS;IACI,oCAAe,GAA5B;;;;gBACI,sBAAO,IAAI,OAAO,CAAU,UAAA,OAAO;wBAC/B,IAAM,IAAI,GAAG,oBAAO,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAA;wBACxE,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE;4BACtB,SAAS,EAAE,IAAI;4BACf,MAAM,EAAE,oBAAoB;4BAC5B,EAAE,EAAE,cAAM,OAAA,OAAO,CAAC,IAAI,CAAC,EAAb,CAAa;4BACvB,MAAM,EAAE,cAAM,OAAA,OAAO,CAAC,KAAK,CAAC,EAAd,CAAc;yBAC/B,CAAC,CAAA;oBACN,CAAC,CAAC,EAAA;;;KACL;IAED,WAAW;IACJ,0CAAqB,GAA5B,UAA6B,IAAa,EAAE,KAAe,EAAE,KAAa;QACtE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YACvB,IAAM,IAAI,GAAG,2BAAgB,CAAC,IAAI,CAAC,CAAA;YACnC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5B,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACnB,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACA,6BAAQ,GAAf,UAAgB,CAAc,EAAE,IAAa,EAAE,KAAc;QACzD,CAAC,CAAC,KAAK,EAAE,CAAA;QACT,IAAM,GAAG,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC5B,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,oBAAS,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,oBAAS,CAAA;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;YACrB,qBAAS,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,yBAAc,CAAC,CAAA;YAC3D,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;YACtB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;SAC7B;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;YACrB,qBAAS,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,yBAAc,CAAC,CAAA;YAC3D,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;YACtB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;SAC7B;QACD,CAAC,CAAC,MAAM,EAAE,CAAA;IACd,CAAC;IAED,YAAY;IACL,sCAAiB,GAAxB,UAAyB,IAAa,EAAE,IAAc,EAAE,GAAW;QAC/D,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;QACtD,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACxC,CAAC;IAED,QAAQ;IACD,wCAAmB,GAA1B,UAA2B,IAAa,EAAE,IAAc,EAAE,GAAW;QACjE,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAA;QAC1E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QAC1D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IACM,yCAAoB,GAA3B,UAA4B,IAAa,EAAE,IAAc,EAAE,GAAW;QAClE,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAA;QAC1E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QAC5C,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/B,OAAO,CAAC,sBAAsB,EAAE,CAAA;QAChC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA;IAC1D,CAAC;IAEM,yCAAoB,GAA3B,UAA4B,IAAc,EAAE,IAAa,EAAE,MAAe,EAAE,KAAsB,EAAE,GAAW;;QAC3G,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC7B,cAAc;QACd,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QAC9B,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QAChD,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,WAAI,IAAI,CAAC,UAAU,0CAAE,EAAE,CAAA,CAAA;QACrD,IAAI,MAAM,EAAE;YACR,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;SACnE;aAAM;YACH,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;SAC/D;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;YACjC,IAAA,IAAI,GAAc,IAAI,KAAlB,EAAE,OAAO,GAAK,IAAI,QAAT,CAAS;YAC9B,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YACpC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACxD,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,EAAE;gBACrC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;gBACpC,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,OAAO,CAAA;aAClD;YACD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QAClD,CAAC,CAAC,CAAA;QACF,UAAU;QACV,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE;YAC1B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;SAC3C;IACL,CAAC;IAEM,0CAAqB,GAA5B,UAA6B,IAAc,EAAE,MAAe;QACxD,IAAM,UAAU,GAAG,IAAI,CAAC,EAAE,KAAK,yBAAc,IAAI,IAAI,CAAC,EAAE,IAAI,oBAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAA,CAAC,mBAAmB;QAC/G,IAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAC9C,IAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK,yBAAc,CAAC,CAAC,CAAC,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,oBAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAClM,IAAI,CAAC,QAAQ,EAAE;YACX,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAA;YACpD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YACnC,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;YAClB,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;SACxE;aAAM;YACH,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAA;YACpD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YACnC,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;YAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;SACnD;QACD,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;IACpE,CAAC;IAED,YAAY;IACL,wCAAmB,GAA1B,UAA2B,IAAa,EAAE,IAAc;;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QACrC,IAAM,KAAK,GAAG,IAAI,CAAC,EAAE,KAAK,0BAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAC,IAAI,CAAC,MAAM,0CAAE,WAAW,EAAE,CAAA;QAC5E,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,4BAAiB,IAAI,oBAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAA;QAC3G,cAAc;QACd,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAA;QAC1D,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACxD,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,CAAA;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,UAAC,EAAE,EAAE,KAAK;;YACnC,IAAI,QAAQ,EAAE;gBACV,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,oBAAoB,GAAG,KAAK,EAAE,OAAA,IAAI,CAAC,MAAM,0CAAE,YAAY,OAAM,EAAE,CAAC,CAAA;aACpG;YACD,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;YAC1C,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE;gBACxD,OAAO,CAAC,YAAY,CAAC,OAAA,QAAQ,CAAC,MAAM,0CAAE,YAAY,OAAM,EAAE,CAAC,CAAA;aAC9D;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,OAAO,EAAE;YACV,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;YAC/C,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;YAC3D,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;SACxE;QACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAA;IACxD,CAAC;IAED,OAAO;IACA,uCAAkB,GAAzB,UAA0B,WAAoB,EAAE,IAAc,EAAE,QAAiB;QAC7E,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAChB,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SAC9B;QACD,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QAC5C,IAAM,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAA;QAC7B,IAAM,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1C,IAAI,EAAE,EAAE,EAAE,QAAQ;YACd,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;SACrG;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SAC5B;IACL,CAAC;IAEM,2CAAsB,GAA7B,UAA8B,IAAa,EAAE,MAAkB,EAAE,IAAa,EAAE,EAAW;QACvF,yCAAyC;QACzC,IAAI,oBAAO,CAAC,YAAY,EAAE;YACtB,IAAI,IAAI,qCAAsB,CAAA;YAC9B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACtD,EAAE,GAAG,CAAC,CAAA;SACT;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;QAC3C,yBAAyB;QACzB,wDAAwD;QACxD,IAAI;IACR,CAAC;IAED,OAAO;IACA,mCAAc,GAArB,UAAsB,IAAa,EAAE,MAAkB,EAAE,IAAa,EAAE,EAAW;QAAnF,iBAaC;;QAZG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE,UAAC,EAAE,EAAE,IAAI,IAAK,OAAA,KAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,EAAtC,CAAsC,CAAC,CAAA;QAC5F,UAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG;YACvC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;YACZ,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;YAC1C,IAAI,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG;gBACrB,EAAE,CAAC,MAAM,GAAG,OAAK,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,OAAI,CAAA;aAC5C;YACD,IAAI,EAAE,EAAE;gBACJ,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;aAClD;YACD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;SAC/G;IACL,CAAC;IAED,OAAO;IACA,uCAAkB,GAAzB,UAA0B,IAAa,EAAE,MAAkB,EAAE,IAAY,EAAE,EAAU,EAAE,MAAe,EAAE,eAAuB;QAA/H,iBAsBC;;QArBG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE,UAAC,EAAE,EAAE,IAAI,IAAK,OAAA,KAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,IAAI,eAAe,GAAG,CAAC,CAAC,CAAC,EAAtE,CAAsE,CAAC,CAAA;QAC5H,UAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG;YACvC,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YAChC,IAAI,MAAM,IAAI,eAAe,GAAG,CAAC,EAAE;gBAC/B,EAAE,GAAG,CAAC,CAAA;gBACN,IAAM,MAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,eAAe,GAAG,CAAC,CAAA;gBACzE,MAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAK,eAAiB,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,MAAI,eAAiB,CAAC,CAAC,CAAC,EAAE,CAAA;gBACzH,MAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;gBACzE,MAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,EAAE,EAAE;gBACb,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,OAAK,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,OAAI,CAAA;aAC/E;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACd;YACD,IAAI,EAAE,EAAE;gBACJ,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;aAClD;YACD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;YAC5G,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,OAAO,GAAG,CAAC,MAAM,IAAI,eAAe,GAAG,CAAC,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,MAAK,aAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;YACnI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,GAAG,CAAC,MAAM,IAAI,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;SACrJ;IACL,CAAC;IAED,SAAS;IACF,sCAAiB,GAAxB,UAAyB,EAAW,EAAE,IAAc,EAAE,OAAiB;QACnE,IAAI,EAAE,IAAI,IAAI,EAAE;YACZ,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACzE,IAAI,CAAC,OAAO,EAAE;gBACV,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;aACrD;iBAAM;gBACH,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,oBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;aAC7G;SACJ;IACL,CAAC;IAED,SAAS;IACF,0CAAqB,GAA5B,UAA6B,EAAW,EAAE,IAAc,EAAE,OAAiB;QACvE,IAAI,EAAE,IAAI,IAAI,EAAE;YACZ,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACzE,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,oBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;SACzH;IACL,CAAC;IAED,OAAO;IACA,mCAAc,GAArB,UAAsB,EAAW,EAAE,IAAS;QACxC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YACf,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,aAAK,CAAC,IAAI,CAAC,CAAA;YAC/E,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;SACzD;aAAM,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YACvB,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,aAAK,CAAC,KAAK,CAAC,CAAA;YAChF,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;SAC1D;aAAM;YACH,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;SAClC;IACL,CAAC;IAED,OAAO;IACA,uCAAkB,GAAzB,UAA0B,EAAW,EAAE,KAAa,EAAE,OAAuB;QAAvB,wBAAA,EAAA,cAAuB;QACzE,EAAE,CAAC,IAAI,GAAG,KAAK,CAAA;QACf,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,OAAO,EAAE;YACT,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,oBAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,qBAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;SAClH;aAAM;YACH,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,qBAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAA;SACnE;QACD,MAAM,CAAC,sBAAsB,EAAE,CAAA;QAC/B,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAA;IAC9C,CAAC;IAED,YAAY;IACL,uCAAkB,GAAzB,UAA0B,IAAa,EAAE,KAAiB,EAAE,GAAY;QAAxE,iBAEC;QADG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,IAAK,OAAA,KAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAxC,CAAwC,EAAC;IAC9E,CAAC;IACM,yCAAoB,GAA3B,UAA4B,EAAW,EAAE,IAAc,EAAE,GAAW,EAAE,SAAmB;QAAzF,iBA6FC;;QA5FG,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;YACd,OAAM;SACT;QACD,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QACnF,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAA;SACnC;QACD,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE,EAAE,MAAM;YACtC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;YACvF,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAC;SACzF;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,IAAI;YAC5C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,cAAc,CAAC,OAAO,EAAE,OAAA,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,0CAAE,IAAI,KAAI,EAAE,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,CAAC,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAApE,CAAoE,CAAC,CAAA;YAC7M,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,mBAAmB,EAAC;SACtD;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,UAAU,EAAE,EAAE,MAAM;YAC/C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,CAAC,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAApE,CAAoE,CAAC,CAAA;YAC7J,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,qBAAqB,EAAC;SACxD;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE,EAAE,IAAI;YAC3C,IAAM,EAAE,GAAG,OAAA,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,0CAAE,EAAE,KAAI,CAAC,CAAA;YAC9D,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,yBAAyB,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,CAAC,KAAK,EAAC;SACpG;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,KAAK,EAAE,EAAE,IAAI;YACxC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,QAAQ,CAAC,yBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;YACzF,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,EAAC;SACzD;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,IAAI;YAC5C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,QAAQ,CAAC,yBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;YACzF,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,EAAC;SACpE;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,EAAE,EAAE,IAAI;YACvC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,CAAC,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAApE,CAAoE,CAAC,CAAA;YACpK,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAC;SAC7D;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,KAAK,EAAE,EAAE,IAAI;YACxC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;YAC5E,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAC;SAC9D;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,MAAM,EAAE,EAAE,IAAI;YACzC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;YAC7E,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE,EAAC;SAC/D;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,IAAI;YAC5C,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;YACzE,IAAI,QAAQ,EAAE,EAAE,YAAY;gBACxB,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAA;gBACpE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBACrB,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAApC,CAAoC,CAAC,CAAA;aACnE;iBAAM,IAAI,QAAQ,EAAE;gBACjB,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBACpB,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;aACxE;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;gBACpD,OAAO,IAAI,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,CAAC,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAApE,CAAoE,CAAC,CAAA;gBACpK,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAC;aAChF;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAAE,EAAE,MAAM;YAChD,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACxB,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,qBAAqB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAC;aACnH;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;gBACpD,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;gBACxE,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,qBAAqB,GAAG,IAAI,CAAC,EAAE,EAAC;aAClE;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE,EAAE,OAAO;YAC9C,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACxB,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAC;aACjF;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;gBACpD,qBAAS,CAAC,QAAQ,CAAC,yBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;gBAC9F,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,yBAAyB,GAAG,IAAI,CAAC,EAAE,EAAC;aACtE;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,cAAc,EAAE,EAAE,QAAQ;YACrD,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,+BAA+B,EAAC;SACpE;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,MAAM;YAC9C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,OAAO,IAAI,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,CAAC,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAApE,CAAoE,CAAC,CAAA;YAC5J,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,yBAAyB,EAAC;SAC5D;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAvC,CAAuC,CAAC,CAAA;YACrD,IAAI,OAAO,EAAE;gBACT,IAAM,EAAE,GAAG,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC1C,IAAI,EAAE,EAAE;oBACJ,OAAO,CAAC,WAAW,GAAG,EAAE,CAAA;iBAC3B;qBAAM;oBACH,qBAAS,CAAC,QAAQ,CAAC,yBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,YAAY,CAAC,CAAA;iBACjF;aACJ;YACD,IAAI,QAAQ,EAAE;gBACV,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAA;aACpC;SACJ;IACL,CAAC;IAEM,6CAAwB,GAA/B,UAAgC,EAAW,EAAE,IAAc,EAAE,QAAyB;QAAtF,iBAyIC;;QAzI4D,yBAAA,EAAA,gBAAyB;QAClF,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;YACd,OAAM;SACT;QACD,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QAC5C,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE,EAAE,MAAM;YACtC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAC;aACzF;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAC;aAC7H;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,IAAI;YAC5C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC,EAAC;aAC1F;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,mBAAmB,EAAC;aACtD;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,UAAU,EAAE,EAAE,MAAM;YAC/C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,EAAC;aAC5F;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,qBAAqB,EAAC;aACxD;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE,EAAE,IAAI;YAC3C,IAAM,EAAE,GAAG,OAAA,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,0CAAE,EAAE,KAAI,CAAC,CAAA;YAC9D,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,yBAAyB,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAC;aACxI;iBAAM;gBACH,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,yBAAyB,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,CAAC,KAAK,EAAC;aACpG;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,KAAK,EAAE,EAAE,IAAI;YACxC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;aAC7F;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,EAAC;aACzD;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,IAAI;YAC5C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAC;aACxG;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,EAAC;aACpE;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,EAAE,EAAE,IAAI;YACvC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;aACjG;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAC;aAC7D;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,KAAK,EAAE,EAAE,IAAI;YACxC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;aAClG;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAC;aAC9D;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,MAAM,EAAE,EAAE,IAAI;YACzC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;aACnG;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE,EAAC;aAC/D;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,IAAI;YAC5C,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;YACzE,IAAI,QAAQ,EAAE,EAAE,YAAY;gBACxB,IAAI,QAAQ,EAAE;oBACV,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;iBAC3G;qBAAM;oBACH,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAA;iBACvE;gBACD,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBACrB,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAApC,CAAoC,CAAC,CAAA;aACnE;iBAAM,IAAI,QAAQ,EAAE;gBACjB,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;aACvB;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;gBACpD,IAAI,QAAQ,EAAE;oBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;iBACpH;qBAAM;oBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAC;iBAChF;aACJ;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAAE,EAAE,MAAM;YAChD,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACxB,IAAI,QAAQ,EAAE;oBACV,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,qBAAqB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;iBACvI;qBAAM;oBACH,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,qBAAqB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAC;iBACnH;aACJ;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;gBACpD,IAAI,QAAQ,EAAE;oBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;iBACtG;qBAAM;oBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,qBAAqB,GAAG,IAAI,CAAC,EAAE,EAAC;iBAClE;aACJ;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE,EAAE,OAAO;YAC9C,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACxB,IAAI,QAAQ,EAAE;oBACV,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;iBACrH;qBAAM;oBACH,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,aAAa,EAAE,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAC;iBACjF;aACJ;iBAAM;gBACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;gBACpD,IAAI,QAAQ,EAAE;oBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC;iBAC1G;qBAAM;oBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,yBAAyB,GAAG,IAAI,CAAC,EAAE,EAAC;iBACtE;aACJ;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,cAAc,EAAE,EAAE,QAAQ;YACrD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC,EAAC;aACxG;iBAAM;gBACH,MAAA,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAE,YAAY,CAAC,+BAA+B,EAAC;aACpE;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE,EAAE,MAAM;YAC9C,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAAtC,CAAsC,CAAC,CAAA;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,EAAC;aAChG;iBAAM;gBACH,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,YAAY,CAAC,yBAAyB,EAAC;aAC5D;SACJ;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAvC,CAAuC,CAAC,CAAA;YACrD,IAAI,QAAQ,EAAE;gBACV,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAA;aACpC;SACJ;IACL,CAAC;IAED,SAAS;IACF,oCAAe,GAAtB,UAAuB,IAAa,EAAE,KAAgB,EAAE,GAAW,EAAE,UAAmB,EAAE,YAAoB,EAAE,SAAmB;QAC/H,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QAC9D,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QAC9D,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACxB,IAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;gBAC1D,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE;oBACpB,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAA;iBAC5C;aACJ;SACJ;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAC,KAAK,EAAC;QACzB,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QACzC,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,GAAG;YAC9C,SAAS,CAAC,YAAY,CAAC,wBAAwB,EAAE,gBAAgB,GAAG,KAAK,CAAC,cAAc,CAAC,CAAA;SAC5F;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC,CAAA;IAC7F,CAAC;IACM,wCAAmB,GAA1B,UAA2B,IAAa,EAAE,KAAgB,EAAE,UAAmB,EAAE,YAAoB,EAAE,SAAmB;;QACtH,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAC3H,MAAA,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;QACzC,MAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;QAC5C,KAAK;QACL,IAAM,YAAY,GAAG,oBAAO,CAAC,eAAe,EAAE,CAAA;QAC9C,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,UAAC,EAAE,EAAE,IAAI;;YACrC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;YACvD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YACxE,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACjB,KAAK,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAA;aACxC;YACD,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAA;YAC9C,QAAQ;YACR,IAAM,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;YAClC,UAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,0CAAE,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;gBACnC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAA;gBACzD,IAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;gBAC1C,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,EAAE;oBACjC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAA;iBAC5B;aACJ;YACD,OAAO;YACP,UAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,SAAS,CAAC,YAAY,GAAG;gBAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,EAAE,EAA/C,CAA+C,CAAC,CAAA;aAC/E;QACL,CAAC,CAAC,CAAA;QACF,KAAK;QACL,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAA;QACxC,IAAI,UAAU,CAAC,MAAM,GAAG,WAAW,GAAG,CAAC,EAAE;YACrC,IAAM,MAAI,GAAG,WAAW,GAAG,CAAC,CAAA;YAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE,gBAAc,GAA8B,IAAI,EAAE,aAAW,GAA+B,IAAI,CAAA;YAC7H,eAAe;YACf,IAAI,UAAU,IAAI,YAAY,EAAE;gBAC5B,gBAAc,GAAG,EAAE,CAAA;gBACnB,YAAY,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,gBAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAA7B,CAA6B,CAAC,CAAA;gBACxD,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;oBAChC,IAAI,EAAE,GAAG,gBAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,gBAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC5E,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAA;oBAC5C,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAA;oBAC5C,OAAO,EAAE,GAAG,EAAE,CAAA;gBAClB,CAAC,CAAC,CAAA;aACL;YACD,IAAI,YAAY,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;gBACrC,aAAW,GAAG,EAAE,CAAA;gBAChB,oBAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,aAAW,CAAC,CAAC,CAAC,GAAG,IAAI,EAArB,CAAqB,CAAC,CAAA;aACvF;YACD,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,IAAI;gBAC/B,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,OAAO,GAAG,gBAAc,aAAd,gBAAc,uBAAd,gBAAc,CAAG,IAAI,CAAC,IAAI,CAAC,CAAA;gBACvF,IAAI,OAAO,EAAE,EAAE,OAAO;oBAClB,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBACnB,EAAE,CAAC,YAAY,CAAC,qBAAqB,GAAG,MAAM,CAAC,CAAC,CAAC,aAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,OAAd,SAAS,YAAM,IAAI,CAAC,IAAI,GAAK,UAAU,GAAE,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,CAAA;iBAClL;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,EAAE,OAAO;oBAC1C,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBACnB,EAAE,CAAC,YAAY,CAAC,mBAAmB,EAAE,SAAS,CAAC,IAAI,OAAd,SAAS,YAAM,IAAI,CAAC,IAAI,GAAK,UAAU,GAAE,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAA;iBAChH;qBAAM,IAAI,MAAI,EAAE,EAAE,OAAO;oBACtB,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBACnB,EAAE,CAAC,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC,IAAI,OAAd,SAAS,YAAM,IAAI,CAAC,IAAI,GAAK,UAAU,GAAE,CAAA;iBACvF;qBAAM;oBACH,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBACnB,EAAE,CAAC,YAAY,OAAf,EAAE,YAAc,IAAI,CAAC,IAAI,GAAK,UAAU,GAAC;iBAC5C;YACL,CAAC,CAAC,CAAA;SACL;QACD,SAAS;QACT,IAAI,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,GAAG;YAC1F,kBAAkB,CAAC,YAAY,CAAC,kCAAgC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,SAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAG,CAAC,CAAA;SACxH;IACL,CAAC;IAED,aAAa;IACN,wCAAmB,GAA1B,UAA2B,IAAa,EAAE,IAAS;QAAnD,iBAyCC;;QAxCG,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QAC9D,QAAQ,CAAC,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;QAClD,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;QACzE,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG;YAC7C,SAAS,CAAC,YAAY,CAAC,wBAAwB,EAAE,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAA;SAC3F;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAC,IAAI,EAAC;QACxB,KAAK;QACL,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QAC3E,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;;YAC3B,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;YACvD,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC9D,MAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;YACjC,MAAA,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;QACtC,CAAC,CAAC,CAAA;QACF,KAAK;QACL,IAAI,SAAS,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QACpD,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE;YACvB,SAAS,GAAG,oBAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SAC9D;QACD,IAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,EAAvC,CAAuC,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAf,CAAe,CAAC,CAAA;QAC3G,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAA;QAC3M,QAAQ;QACR,YAAY,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA;QACjE,IAAI,eAAe,CAAC,MAAM,EAAE;YACxB,YAAY,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;YACzE,eAAe,CAAC,KAAK,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,CAAC,IAAK,OAAA,KAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,EAAvC,CAAuC,CAAC,CAAA;SACrF;QACD,QAAQ;QACR,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YAClE,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,CAAC,IAAK,OAAA,KAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,EAAxC,CAAwC,CAAC,CAAA;SAClF;QACD,SAAS;QACT,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9C,IAAA,KAAA,OAAS,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,IAAA,EAArD,CAAC,QAAA,EAAE,CAAC,QAAiD,CAAA;YAC5D,kBAAkB,CAAC,YAAY,CAAC,kCAAgC,CAAC,SAAI,CAAG,CAAC,CAAA;SAC5E;QACD,MAAM;QACN,MAAA,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,0CAAE,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,EAAC;IAChE,CAAC;IAEM,iCAAY,GAAnB,UAAoB,GAAa;QAC7B,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAG,CAAA;IACnE,CAAC;IAEM,kCAAa,GAApB,UAAqB,IAAS;QAC1B,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAA;SAC/D;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAA;SACnE;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAEM,0CAAqB,GAA5B,UAA6B,EAAW,EAAE,IAAS,EAAE,MAAgB;QAC3D,IAAA,MAAM,GAAG,EAAE,EAAE,KAAA,OAAS,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAA7B,CAAC,QAAA,EAAE,CAAC,QAAyB,CAAA;QACjD,IAAI,IAAI,CAAC,EAAE,KAAK,uBAAe,CAAC,eAAe,IAAI,IAAI,CAAC,EAAE,KAAK,uBAAe,CAAC,WAAW,EAAE;YACxF,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SACnB;aAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SAClB;aAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC,KAAG,CAAC,GAAG,IAAI,CAAC,MAAQ,CAAC,CAAA;SACpC;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,MAAI,CAAC,SAAI,CAAC,SAAI,IAAI,CAAC,MAAQ,CAAC,CAAA;SAC3C;QACD,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,MAAM,CAAC,IAAI,CAAC,MAAI,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAI,CAAC,CAAA;SACpE;QACD,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACnB,IAAI,MAAM,EAAE;YACR,EAAE,CAAC,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAA;SACpG;aAAM;YACH,EAAE,CAAC,YAAY,CAAC,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;SACzD;IACL,CAAC;IAED,SAAS;IACF,kCAAa,GAApB,UAAqB,EAAW,EAAE,EAAU,EAAE,EAAU;QACpD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAA;QACpD,IAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QAClC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;YACtB,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;SACnC;QACD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAA;QAC7B,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;IACrD,CAAC;IACM,sCAAiB,GAAxB,UAAyB,IAAa,EAAE,EAAU,EAAE,EAAU;QAC1D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;YACpB,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC,CAAA;YAClE,IAAM,SAAS,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;YACxF,KAAK;YACL,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,UAAC,EAAE,EAAE,IAAI;gBACzC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;gBACvD,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAA;YACtD,CAAC,CAAC,CAAA;YACF,KAAK;YACL,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YACrC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACrC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,UAAC,IAAI,EAAE,IAAI;oBAC3D,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;oBACrD,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;oBACnE,IAAI,CAAC,YAAY,CAAC,yBAAyB,EAAE,oBAAkB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAM,EAAE,IAAI,CAAC,CAAA;gBACzG,CAAC,CAAC,CAAA;aACL;SACJ;IACL,CAAC;IAED,SAAS;IACF,6CAAwB,GAA/B,UAAgC,IAAa,EAAE,GAAW,EAAE,GAAyB;QACjF,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QAChD,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;QAC/C,oBAAO,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC1C,IAAI,eAAe,CAAC,OAAO,EAAE;gBACzB,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;aAC9G;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACF,0CAAqB,GAA5B,UAA6B,IAAa,EAAE,GAAW,EAAE,GAAkB;QACvE,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;QACD,oBAAO,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,KAAK;YACrC,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;gBAClD,IAAI,CAAC,KAAK,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,KAAI,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;aACjG;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACF,2CAAsB,GAA7B,UAA8B,IAAa,EAAE,UAAmB,EAAE,GAAW,EAAE,GAAW,EAAE,GAAuB;;QAC/G,IAAM,MAAM,eAAG,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,oBAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,mBAAmB,CAAC,0CAAG,CAAC,2CAAG,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;QAClI,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;YACzB,MAAM,CAAC,YAAY,GAAG,KAAK,CAAA;SAC9B;QACD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,UAAC,EAAE,EAAE,CAAC;YAChB,qBAAS,CAAC,YAAY,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAA;YAClD,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YAChC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;QACrC,CAAC,CAAC,CAAA;QACF,oBAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;;YACzC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,OAAM;aACT;iBAAM,IAAI,QAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,MAAM,CAAA,EAAE;gBAC5B,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAS,CAAA;aACrC;YACD,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE;gBACjB,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;gBACzB,MAAM,CAAC,YAAY,GAAG,IAAI,CAAA;aAC7B;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,EAAW;oBAAX,KAAA,aAAW,EAAV,EAAE,QAAA,EAAE,KAAK,QAAA;gBACjC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC/B,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;gBAClC,qBAAS,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAA;gBACjD,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,GAAG,EAAE,CAAA;YACnD,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACA,yCAAoB,GAA3B,UAA4B,IAAa,EAAE,GAAW,EAAE,GAAW,EAAE,GAAsB;QACvF,oBAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;YACxC,IAAI,IAAI,CAAC,OAAO,EAAE;gBACR,IAAA,KAAmB,oBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAApE,EAAE,QAAA,EAAE,QAAQ,cAAwD,CAAA;gBAC5E,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAC/B,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;gBACnC,EAAE,IAAI,CAAC,IAAI,qBAAS,CAAC,iBAAiB,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;gBACrD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAA;gBACrF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAA;gBAClE,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;aAC9D;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,QAAQ;IACD,yCAAoB,GAA3B,UAA4B,IAAa,EAAE,GAAW,EAAE,GAA2B;QAC/E,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QAC5E,MAAM,CAAC,MAAM,GAAG,GAAG,CAAA;QACnB,OAAO,CAAC,MAAM,GAAG,KAAK,CAAA;QACtB,oBAAO,CAAC,qBAAqB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;YAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,wBAAwB;gBAClB,IAAA,KAAA,OAAe,IAAI,IAAA,EAAlB,GAAG,QAAA,EAAE,KAAK,QAAQ,CAAA;gBACzB,MAAM,CAAC,MAAM,GAAG,EAAE,GAAG,KAAK,CAAA;gBAC1B,iFAAiF;aACpF;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,aAAa;IACA,wCAAmB,GAAhC,UAAiC,EAAU,EAAE,MAAY;;;;;;wBAC/C,EAAE,GAAG,+BAAoB,CAAC,EAAE,CAAC,CAAA;wBACnC,IAAI,CAAC,EAAE,EAAE;4BACL,sBAAO,KAAK,EAAA;yBACf;wBACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;wBACT,qBAAM,oBAAO,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,EAAA;;wBAAjF,IAAI,GAAG,SAA0E;wBACvF,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;wBACjB,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;6BACnC,IAAI,EAAJ,wBAAI;wBACJ,qBAAM,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,EAAA;;wBAApC,SAAoC,CAAA;wBACpC,sBAAO,IAAI,EAAA;4BAEf,sBAAO,KAAK,EAAA;;;;KACf;IAED,SAAS;IACF,sCAAiB,GAAxB;QAAA,iBAOC;QANG,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE;YAC1C,MAAM,EAAE,oBAAoB;YAC5B,UAAU,EAAE,cAAc;YAC1B,EAAE,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAArC,CAAqC;YAC/C,MAAM,EAAE,cAAQ,CAAC;SACpB,CAAC,CAAA;IACN,CAAC;IAED,UAAU;IACH,kCAAa,GAApB,UAAqB,EAAW,EAAE,IAAS,EAAE,SAAiB;QAC1D,IAAI,KAAK,GAAe,EAAE,CAAA;QAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACjB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,MAAM,GAAG,oBAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAA;YAClD,IAAI,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;gBACjE,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;aAClK;SACJ;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;YAC3B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,KAAK,GAAG,oBAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAA;SAC9C;aAAM;YACH,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,KAAK,GAAG,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SAC7C;QACD,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAA;QACjD,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;QACvD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;IACxF,CAAC;IAED,UAAU;IACH,uCAAkB,GAAzB,UAA0B,IAAa,EAAE,GAAY,EAAE,MAAgB;QACnE,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,GAAG,CAAA;QACzC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAC1D,IAAI,GAAG,EAAE;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;YAClB,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;YAC5C,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACzB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA;SAC3E;aAAM;YACH,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACxB,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;YAC7C,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,GAAG,KAAK,EAAnB,CAAmB,CAAC,CAAC,KAAK,EAAE,CAAA;SAC3E;QACD,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA;IAC5E,CAAC;IACM,sCAAiB,GAAxB,UAAyB,IAAa;QAClC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;QAC3C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;QACjC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,CAAC,CAAA;IAChC,CAAC;IAED,SAAS;IACF,oCAAe,GAAtB,UAAuB,GAAW,EAAE,IAAkB;QAClD,IAAI,CAAC,oBAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,iBAAiB,CAAA;YAC9C,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAA;YACxC,OAAO,IAAI,CAAA;SACd;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,SAAS;IACF,2CAAsB,GAA7B,UAA8B,GAAW,EAAE,KAAiB,EAAE,IAAkB;QAC5E,IAAI,CAAC,oBAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE;YAClC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,iBAAiB,CAAA;YAC9C,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YACtD,OAAO,IAAI,CAAA;SACd;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,OAAO;IACA,wCAAmB,GAA1B,UAA2B,IAAuB,EAAE,EAAY;QAC5D,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAA;QAChC,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,EAAE;YACd,OAAM;SACT;aAAM,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACxC,IAAI,UAAQ,GAAG,CAAC,EAAE,GAAC,GAAG,IAAI,EAAE,KAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;YAClD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,CAAC;gBAClB,IAAI,CAAC,CAAC,IAAI,GAAG,UAAQ,EAAE;oBACnB,UAAQ,GAAG,CAAC,CAAC,IAAI,CAAA;iBACpB;gBACD,IAAI,CAAC,CAAC,GAAG,KAAK,KAAG,EAAE;oBACf,GAAC,GAAG,CAAC,CAAA;iBACR;YACL,CAAC,CAAC,CAAA;YACF,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACtB,IAAI,GAAC,IAAI,GAAG,GAAG,GAAC,CAAC,IAAI,GAAG,8CAAmC,EAAE;gBACzD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,8CAAmC,GAAG,CAAC,GAAG,GAAG,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;gBACzG,OAAO,IAAI,CAAC,cAAc,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAA;aAC1G;iBAAM,IAAI,UAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,oBAAO,CAAC,gBAAgB,EAAE,CAAC,EAAE;gBAC/D,OAAO,IAAI,CAAC,cAAc,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAA;aAC3F;SACJ;QACD,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;IACrD,CAAC;IAED,QAAQ;IACD,mCAAc,GAArB,UAAsB,IAAa,EAAE,IAAY;QAC7C,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,CAAA;QACpB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;YACvB,IAAI,GAAG,IAAI,CAAC,EAAE;gBACV,GAAG,IAAI,CAAC,CAAA;gBACR,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;aACzC;iBAAM,IAAI,GAAG,IAAI,GAAG,EAAE;gBACnB,GAAG,IAAI,GAAG,CAAA;gBACV,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;aACzC;iBAAM;gBACH,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;aACzC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACF,sCAAiB,GAAxB,UAAyB,IAAa,EAAE,IAAY,EAAE,IAAY;QAC9D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;QACpC,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE;YAC1B,QAAQ,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;SAC5C;IACL,CAAC;IAED,OAAO;IACA,kCAAa,GAApB,UAAqB,EAAU,EAAE,QAAiB,EAAE,QAAiB,EAAE,MAAe,EAAE,OAAgB,EAAE,GAAW;QACjH,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACrC,IAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,4BAAkB,CAAC,CAAA;QAClD,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,GAAE;QACb,qBAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA;QAC9C,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAC5B,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACtC,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAA;SACnD;aAAM;YACH,qBAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;SAClD;QACD,IAAI,IAAI,IAAI,QAAQ,IAAI,OAAO,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnC,IAAI,OAAO,CAAC,OAAO,EAAE;oBACjB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;iBACvB;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,WAAW;IACJ,+CAA0B,GAAjC,UAAkC,EAAW,EAAE,MAAc;QACzD,IAAM,SAAS,GAAG,MAAM,IAAI,oCAAyB,CAAA;QACrD,EAAE,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,EAAE,CAAA;QAC9G,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACzE,CAAC;IAED,SAAS;IACF,4CAAuB,GAA9B,UAA+B,EAAU,EAAE,KAAa;QACpD,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,EAAE,KAAK,CAAC,CAAA;IAClD,CAAC;IAED,SAAS;IACF,wCAAmB,GAA1B,UAA2B,IAAa,EAAE,IAAmB,EAAE,MAAgB;QAA/E,iBAmBC;QAlBG,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAChC,EAAE;QACF,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC1D,KAAK;QACL,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAC,EAAE,EAAE,CAAC;YACrE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;YACpD,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAA;QAC9C,CAAC,CAAC,CAAA;QACF,KAAK;QACL,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,yBAAyB,EAAE,oBAAkB,SAAS,CAAC,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC,SAAM,EAAE,SAAS,CAAC,IAAI,CAAC,0BAA0B,GAAG,EAAE,EAAE,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;QACrO,KAAK;QACL,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QAC7C,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;QAC/G,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAI,IAAI,CAAC,SAAS,CAAC,MAAM,MAAG,CAAA;YACjF,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,UAAC,EAAE,EAAE,QAAQ,IAAK,OAAA,KAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,EAAxD,CAAwD,CAAC,CAAA;SAClH;IACL,CAAC;IAED,SAAS;IACF,qCAAgB,GAAvB,UAAwB,EAAW,EAAE,QAAqB,EAAE,cAAsB;QAC9E,EAAE,CAAC,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;IAC9J,CAAC;IAEM,0CAAqB,GAA5B,UAA6B,IAAmB,EAAE,MAAgB;QAC9D,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAA,CAAC,CAAC,CAAC,CAAA;SACpF;QACD,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAO,OAAO,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,CAAA,CAAC,CAAC,CAAC,CAAA;IACxH,CAAC;IAED,SAAS;IACF,gDAA2B,GAAlC,UAAmC,IAAmB,EAAE,MAAgB;QACpE,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACtB,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAA;SACpC;QACD,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACrE,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;SACnE;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACxB;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,WAAW;IACJ,6CAAwB,GAA/B,UAAgC,IAAa,EAAE,KAAsB,EAAE,cAAsB;QACzF,IAAI,SAAS,GAAsC,EAAE,EAAE,KAAK,GAAsB,IAAI,EAAE,SAAS,GAAkB,EAAE,CAAA;QACrH,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YACL,IAAA,KAAA,OAA2B,CAAC,CAAC,IAAI,IAAA,EAAhC,SAAS,QAAA,EAAE,IAAI,QAAA,EAAE,KAAK,QAAU,CAAA;YACvC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE,IAAI;gBACvB,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,CAAC,CAAA;aAClC;iBAAM,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE,IAAI;gBAC9B,KAAK,GAAG,IAAI,2BAAiB,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;aACpD;iBAAM,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE,IAAI;gBAC9B,SAAS,CAAC,IAAI,CAAC,IAAI,qBAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;aAC/C;QACL,CAAC,CAAC,CAAA;QACF,KAAK;QACL,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,UAAC,EAAE,EAAE,CAAC;YACtC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;YACpD,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC,CAAC,CAAA;QACF,KAAK;QACL,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACrC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE;YAC5B,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;SAC5D;QACD,KAAK;QACL,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAI,SAAS,CAAC,MAAM,MAAG,CAAA;QAC5E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,UAAC,EAAE,EAAE,QAAQ,IAAK,OAAA,kBAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,EAAzD,CAAyD,CAAC,CAAA;IACzH,CAAC;IAED,OAAO;IACA,8BAAS,GAAhB;QAAA,iBAEC;QADG,oBAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAtB,CAAsB,CAAC,CAAA;IACvE,CAAC;IAED,SAAS;IACI,sCAAiB,GAA9B,UAA+B,EAAU;;;;gBACrC,sBAAO,IAAI,OAAO,CAAS,UAAA,OAAO;wBAC9B,IAAM,GAAG,GAAG,wBAAa,CAAC,EAAE,CAAC,CAAA;wBAC7B,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAA;wBACnJ,KAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,2BAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,UAAC,GAAoB,gBAAK,OAAA,OAAO,aAAC,GAAG,CAAC,CAAC,CAAC,0CAAE,EAAE,mCAAI,CAAC,CAAC,CAAA,EAAA,EAAE,EAAE,CAAC,CAAA;oBAClI,CAAC,CAAC,EAAA;;;KACL;IAED,WAAW;IACJ,wCAAmB,GAA1B,UAA2B,KAAa;QACpC,IAAI,KAAK,KAAK,CAAC,EAAE;YACb,OAAO,SAAS,CAAA;SACnB;aAAM,IAAI,KAAK,KAAK,CAAC,EAAE;YACpB,OAAO,SAAS,CAAA;SACnB;aAAM,IAAI,KAAK,KAAK,CAAC,EAAE;YACpB,OAAO,SAAS,CAAA;SACnB;QACD,OAAO,SAAS,CAAA;IACpB,CAAC;IAED,SAAS;IACF,oCAAe,GAAtB,UAAuB,IAAS;QAAhC,iBAWC;QAVG,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;YACvC,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC3C,EAAE,EAAE,cAAM,OAAA,oBAAO,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;gBACnD,IAAI,GAAG,EAAE;oBACL,OAAO,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;iBAC7B;gBACD,KAAI,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;YAChD,CAAC,CAAC,EALQ,CAKR;YACF,MAAM,EAAE,cAAQ,CAAC;SACpB,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACA,kCAAa,GAApB,UAAqB,IAAS,EAAE,KAA0B,EAAE,WAAoB;QAAhF,iBAeC;QAdG,IAAM,KAAK,GAAG,oBAAO,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACpD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,uBAAuB,EAAE;YAC9E,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC3C,EAAE,EAAE,cAAM,OAAA,oBAAO,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;gBAC1D,IAAI,GAAG,EAAE;oBACL,OAAO,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;iBAC7B;qBAAM,IAAI,WAAW,CAAC,OAAO,EAAE;oBAC5B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAA;oBACzD,WAAW,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,oBAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;iBAC1F;gBACD,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAA;YAC5F,CAAC,CAAC,EARQ,CAQR;YACF,MAAM,EAAE,cAAQ,CAAC;SACpB,CAAC,CAAA;IACN,CAAC;IAEY,iCAAY,GAAzB;;;gBACI,IAAI,oBAAO,CAAC,QAAQ,EAAE,EAAE;oBACpB,sBAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAA;iBAC1C;qBAAM;oBACH,sBAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAA;iBACxC;;;;KACJ;IAED,SAAS;IACF,wCAAmB,GAA1B,UAA2B,MAAgB;QACvC,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;SACzC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAAE;YAC1C,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;SACzD;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE;YACvC,IAAM,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,GAAG,GAAG,wBAAa,CAAC,EAAE,CAAC,CAAA;YAC7C,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAA;YACnJ,IAAI,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;SAC1D;IACL,CAAC;IAED,qBAAqB;IACd,qCAAgB,GAAvB;QACI,IAAM,IAAI,GAAG,oBAAO,CAAC,aAAa,EAAE,CAAA;QACpC,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,EAAE;YACd,IAAM,GAAG,GAAW,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,GAAa,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC5F,IAAI,CAAC,OAAO,OAAZ,IAAI,YAAS,GAAG,GAAK,MAAM,GAAC;SAC/B;IACL,CAAC;IAED,SAAS;IACF,oCAAe,GAAtB,UAAuB,IAAa,EAAE,IAAa;QAC/C,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QACxD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QAC9D,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAChE,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAA;QAC7F,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAA;QACzF,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC7D,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAA;SAC1E;IACL,CAAC;IAED,SAAS;IACF,qCAAgB,GAAvB,UAAwB,IAAa,EAAE,IAAa,EAAE,GAAW;;QAC7D,KAAK;QACL,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QACzF,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3C,IAAM,QAAM,GAAG,EAAE,CAAA;YACjB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;gBACjB,IAAI,CAAC,CAAC,IAAI,GAAG,qBAAa,CAAC,QAAQ,EAAE;oBACjC,IAAI,CAAC,GAAG,QAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAa,CAAC,QAAQ,EAAjC,CAAiC,CAAC,CAAA;oBAC3D,IAAI,CAAC,CAAC,EAAE;wBACJ,CAAC,GAAG,QAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,qBAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,CAAA;qBAChI;oBACD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACzB,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;iBAC5D;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAa,CAAC,kBAAkB,EAAE;oBACpD,QAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAA;iBACvK;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAa,CAAC,aAAa,EAAE;oBAC/C,QAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;iBACzJ;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAa,CAAC,SAAS,EAAE;oBAC3C,QAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;iBACtK;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAa,CAAC,KAAK,EAAE;oBACvC,QAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAA;iBAC7L;qBAAM;oBACH,QAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAA;iBAClK;YACL,CAAC,CAAC,CAAA;YACF,SAAS,CAAC,KAAK,CAAC,QAAM,EAAE,UAAC,EAAE,EAAE,KAAK;gBAC9B,EAAE,CAAC,IAAI,GAAG,KAAK,CAAA;gBACf,qBAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAA;YAC3D,CAAC,CAAC,CAAA;SACL;QACD,OAAO;QACP,IAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,SAAS,CAAC,CAAC,cAAC,IAAI,CAAC,SAAS,0CAAE,KAAK,0CAAE,EAAE,CAAA,GAAG;YACxD,qBAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAA;SACzF;QACD,OAAO,SAAS,CAAC,MAAM,IAAI,CAAC,EAAC,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,MAAM,CAAA,CAAA;IACvD,CAAC;IAED,SAAS;IACF,oCAAe,GAAtB,UAAuB,IAAa,EAAE,IAA6B,EAAE,KAAe,EAAE,UAAmB,EAAE,IAAa;QAAxH,iBA+DC;QA9DG,IAAM,MAAM,GAAgB,EAAE,EAAE,MAAM,GAAG,IAAI,YAAY,iBAAO,CAAC,CAAC,CAAC,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;QACtI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,MAAM,CAAC,IAAI,CAAC,iBAAS,CAAC,KAAK,CAAC,CAAA;SAC/B;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,CAAC,IAAI,CAAC,iBAAS,CAAC,MAAM,CAAC,CAAA;SAChC;QACD,IAAI,UAAU,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,iBAAS,CAAC,KAAK,CAAC,CAAA;SAC/B;QACD,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,iBAAS,CAAC,MAAM,CAAC,CAAA;SAChC;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACtD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SAC7B;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAA;QACjE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,KAAK;YAC1C,IAAM,KAAK,GAAG,2BAAgB,CAAC,KAAK,CAAC,CAAA;YACrC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAA;YACnE,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;YAC/C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACpB,IAAI,CAAC,IAAI,EAAE;gBACP,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;aAC3B;iBAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,KAAK,IAAI,KAAK,EAAE;gBAC3C,IAAM,aAAW,GAAG,KAAK,CAAC,WAAW,CAAA;gBACrC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,EAAE,GAAG,KAAK,EAAE;oBACxC,IAAI,IAAI,CAAC,OAAO,EAAE;wBACd,IAAI,CAAC,KAAK,GAAG,aAAW,CAAA;wBACxB,IAAI,CAAC,KAAK,GAAG,iBAAS,CAAC,IAAI,CAAA;wBAC3B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAApB,CAAoB,CAAC,CAAA;wBACjD,KAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;qBAC5D;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;aAC7B;iBAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,KAAK,IAAI,KAAK,KAAK,iBAAS,CAAC,KAAK,IAAI,KAAK,KAAK,iBAAS,CAAC,MAAM,EAAE,EAAE,IAAI;gBACnG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBACjB,IAAA,KAAuB,KAAK,KAAK,iBAAS,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,iBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAvM,IAAI,UAAA,EAAE,KAAK,WAA4L,CAAA;gBAC/M,IAAI,KAAK,EAAE;oBACP,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;iBACzD;qBAAM;oBACH,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;iBAC5B;gBACD,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;aAC3C;iBAAM,IAAI,KAAK,KAAK,iBAAS,CAAC,MAAM,IAAI,MAAM,EAAE,EAAE,IAAI;gBACnD,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBACvB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,KAAK,EAAE;oBACzC,IAAI,IAAI,CAAC,OAAO,EAAE;wBACd,IAAI,IAAI,YAAY,iBAAO,EAAE;yBAC5B;6BAAM;4BACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;yBACrB;wBACD,IAAI,CAAC,KAAK,GAAG,iBAAS,CAAC,IAAI,CAAA;wBAC3B,KAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;qBAC5D;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;aAC7B;iBAAM;gBACH,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;aAC3B;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IACL,iBAAC;AAAD,CA7yCA,AA6yCC,IAAA;AAEY,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAA;AAC1C,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE;IAClB,MAAM,CAAC,YAAY,CAAC,GAAG,kBAAU,CAAA;CACpC", "file": "", "sourceRoot": "/", "sourcesContent": ["import BuildObj from \"../../model/area/BuildObj\"\nimport CTypeObj from \"../../model/common/CTypeObj\"\nimport EquipInfo from \"../../model/main/EquipInfo\"\nimport { NOVICE_BUILD_SPEED_MUL } from \"../../model/guide/NoviceConfig\"\nimport { ARMY_STATE_COLOR, BORDER_LINE_CONF, BUILD_EMBASSY_NID, BUILD_MAIN_NID, BUILD_PLANT_NID, CTYPE_ICON_URL, FIXATION_MENU_CONFIG, HERO_OPT_GIFT, ONE_USER_POPULARITY_CHANGE_INTERVAL, POR<PERSON>AY<PERSON>_COMP_NEED_COUNT, TILE_SIZE, TILE_SIZE_HALF } from \"../constant/Constant\"\nimport { AlertOpts, ArmyShortInfo, AttrArrayInfo, BuildViewInfo, MessageBoxOpts, NoLongerOpts } from \"../constant/DataType\"\nimport { ecode } from \"../constant/ECode\"\nimport { ArmyState, CType, EquipEffectType, PawnSkillType, SelectPortrayalType } from \"../constant/Enums\"\nimport { IPersonalDescPlayer, IPopularityPlayer, IRankScorePlayer, ITitlePlayer, ITotalGameCountPlayer } from \"../constant/Interface\"\nimport NetEvent from \"../event/NetEvent\"\nimport NotEvent from \"../event/NotEvent\"\nimport { gameHpr } from \"./GameHelper\"\nimport { mapHelper } from \"./MapHelper\"\nimport { resHelper } from \"./ResHelper\"\nimport PortrayalInfo from \"../../model/common/PortrayalInfo\"\nimport StrategyObj from \"../../model/common/StrategyObj\"\nimport FrameAnimationCmpt from \"../../view/cmpt/FrameAnimationCmpt\"\nimport PawnObj from \"../../model/area/PawnObj\"\nimport PortrayalSkillObj from \"../../model/common/PortrayalSkillObj\"\nimport ArmyObj from \"../../model/area/ArmyObj\"\nimport MarchObj from \"../../model/main/MarchObj\"\n\n/**\n * 视图帮助方法\n */\nclass ViewHelper {\n\n    // 跳转场景\n    public async gotoWind(val: string, ...params: any) {\n        return new Promise<void>(resolve => eventCenter.emit(mc.Event.GOTO_WIND, val, resolve, ...params))\n    }\n\n    // 预加载场景\n    public async preloadWind(key: string, progress?: (percent: number) => void) {\n        return new Promise<void>(resolve => eventCenter.emit(mc.Event.PRELOAD_WIND, key, resolve, progress))\n    }\n\n    // 预加载UI\n    public async preloadPnl(key: string, progress?: (done: number, total: number) => void) {\n        return new Promise<mc.BasePnlCtrl>(resolve => eventCenter.emit(mc.Event.PRELOAD_PNL, key, resolve, progress))\n    }\n\n    // 显示UI\n    public async showPnl(key: string | mc.BasePnlCtrl, ...params: any) {\n        return new Promise<mc.BasePnlCtrl>(resolve => eventCenter.emit(mc.Event.OPEN_PNL, key, resolve, ...params))\n    }\n\n    // 隐藏UI\n    public hidePnl(key: string | mc.BasePnlCtrl) {\n        eventCenter.emit(mc.Event.HIDE_PNL, key)\n    }\n\n    // 显示提示框\n    public showAlert(msg: string, opts?: AlertOpts) {\n        if (msg === ecode.NOT_BIND_UID) {\n            return eventCenter.emit(NetEvent.NET_DISCONNECT)\n        }\n        eventCenter.emit(NotEvent.OPEN_ALERT, msg, opts)\n    }\n\n    // 显示对话框\n    public showMessageBox(msg: string, opts?: MessageBoxOpts) {\n        eventCenter.emit(NotEvent.OPEN_MESSAGE_BOX, msg, opts)\n    }\n\n    // 主动关闭对话框\n    public hideMessageBox() {\n        eventCenter.emit(NotEvent.HIDE_MESSAGE_BOX)\n    }\n\n    // 显示说明\n    public showDesc(text: string, params?: any[]) {\n        this.showPnl('common/Desc', { text, params })\n    }\n\n    // 显示说明信息\n    public showDescInfo(list: { key: string, params?: any[] }[], title: string = 'ui.explain') {\n        this.showPnl('common/DescInfo', { title, list })\n    }\n\n    // 显示网络等待\n    public showNetWait(val: boolean, delay?: number) {\n        if (val) {\n            eventCenter.emit(NetEvent.NET_REQ_BEGIN, delay)\n        } else {\n            eventCenter.emit(NetEvent.NET_REQ_END)\n        }\n    }\n\n    // 显示通用加载动画\n    public showLoadingWait(val: boolean) {\n        if (val) {\n            eventCenter.emit(mc.Event.LOADING_WAIT_BEGIN)\n        } else {\n            eventCenter.emit(mc.Event.LOADING_WAIT_END)\n        }\n    }\n\n    // 显示加载wind的动画\n    public showWindLoading(val: boolean) {\n        if (val) {\n            eventCenter.emit(mc.Event.LOAD_BEGIN_WIND)\n        } else {\n            eventCenter.emit(mc.Event.LOAD_END_WIND)\n        }\n    }\n\n    // 显示连接失败\n    public async showConnectFail() {\n        return new Promise<boolean>(resolve => {\n            const text = gameHpr.getTextByNetworkStatus('login.connect_server_fail')\n            this.showMessageBox(text, {\n                lockClose: true,\n                okText: 'login.button_retry',\n                ok: () => resolve(true),\n                cancel: () => resolve(false),\n            })\n        })\n    }\n\n    // 刷新地块的边框线\n    public updateCellBorderLines(node: cc.Node, lines: number[], color: string) {\n        node.Items(lines, (it, line) => {\n            const conf = BORDER_LINE_CONF[line]\n            it.setContentSize(conf.size)\n            it.setPosition(conf.pos)\n            it.Color(color)\n        })\n    }\n\n    // 绘制网格\n    public drawGrid(g: cc.Graphics, size: cc.Vec2, start: cc.Vec2) {\n        g.clear()\n        const pos: cc.Vec2 = cc.v2()\n        const w = size.x * TILE_SIZE, h = size.y * TILE_SIZE\n        for (let i = 0; i <= size.x; i++) {\n            pos.set(start).x += i\n            mapHelper.getPixelByPoint(pos, pos).subSelf(TILE_SIZE_HALF)\n            g.moveTo(pos.x, pos.y)\n            g.lineTo(pos.x, pos.y + h)\n        }\n        for (let i = 0; i <= size.y; i++) {\n            pos.set(start).y += i\n            mapHelper.getPixelByPoint(pos, pos).subSelf(TILE_SIZE_HALF)\n            g.moveTo(pos.x, pos.y)\n            g.lineTo(pos.x + w, pos.y)\n        }\n        g.stroke()\n    }\n\n    // 显示建筑的基础信息\n    public updateBuildBaseUI(node: cc.Node, data: BuildObj, key: string) {\n        this.updateBuildBaseInfo(node.Child('top'), data, key)\n        this.updateBuildAttrInfo(node, data)\n    }\n\n    // 基础上信息\n    public updateBuildBaseInfo(node: cc.Node, data: BuildObj, key: string) {\n        resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key)\n        node.Child('icon/name').setLocaleKey(data.name)\n        node.Child('icon/lv').setLocaleKey('ui.lv', data.lv)\n        const desc = node.Child('desc') || node.Child('info/desc')\n        desc.setLocaleKey(data.desc)\n    }\n    public _updateBuildBaseInfo(node: cc.Node, data: BuildObj, key: string) {\n        resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key)\n        node.Child('icon/name').setLocaleKey(data.name)\n        node.Child('icon/lv/val').setLocaleKey('ui.lv', data.lv)\n        const descLbl = node.Child('desc', cc.Label)\n        descLbl.setLocaleKey(data.desc)\n        descLbl._forceUpdateRenderData()\n        node.height = Math.max(220, descLbl.node.height + 168)\n    }\n\n    public _updateBuildAttrInfo(data: BuildObj, attr: cc.Node, bottom: cc.Node, attrs: BuildViewInfo[], key: string) {\n        const top = attr.Child('top')\n        // 显示下级信息和升级费用\n        const isMaxLv = data.isMaxLv()\n        top.Child('curr').setLocaleKey('ui.lv', data.lv)\n        const nextLv = data.tempNextLv || data.nextLvInfo?.lv\n        if (nextLv) {\n            top.Child('next').Color('#625450').setLocaleKey('ui.lv', nextLv)\n        } else {\n            top.Child('next').Color('#B6A591').setLocaleKey('ui.maxlv1')\n        }\n        attr.Child('items').Items(attrs, (it, data, i) => {\n            const { curr, nextVal } = data\n            it.Child('curr/icon').active = false\n            it.Child('curr/val').setLocaleKey(curr.key, curr.params)\n            if (it.Child('next').active = !!nextVal) {\n                it.Child('next/icon').active = false\n                it.Child('next/val', cc.Label).string = nextVal\n            }\n            it.Child('line').active = i < attrs.length - 1\n        })\n        // 刷新费用和按钮\n        if (bottom.active = !isMaxLv) {\n            this.updateBuildBottomInfo(data, bottom)\n        }\n    }\n\n    public updateBuildBottomInfo(data: BuildObj, bottom: cc.Node) {\n        const needMainLv = data.id !== BUILD_MAIN_NID && data.lv >= gameHpr.player.getMainBuildLv() // 只要不是主城 就不能比主城等级高\n        const params = needMainLv ? [data.lv + 1] : []\n        const condText = data.id === BUILD_MAIN_NID ? gameHpr.checkCellCountCond(data.attrJson.prep_cond) : gameHpr.checkUnlcokBuildCond(data.attrJson.prep_cond) || (needMainLv ? 'ui.need_main_lv' : '')\n        if (!condText) {\n            bottom.Child('title/val').setLocaleKey('ui.up_cost')\n            bottom.Child('cond').active = false\n            const need = bottom.Child('need')\n            need.active = true\n            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time)\n        } else {\n            bottom.Child('title/val').setLocaleKey('ui.up_cond')\n            bottom.Child('need').active = false\n            const cond = bottom.Child('cond')\n            cond.active = true\n            cond.Child('val').setLocaleKey(condText, params)\n        }\n        this.updateBuildButtons(bottom.Child('buttons'), data, condText)\n    }\n\n    // 显示建筑的属性信息\n    public updateBuildAttrInfo(node: cc.Node, data: BuildObj) {\n        const attr = node.Child('attrs/attr')\n        const dtype = data.id === BUILD_PLANT_NID ? 501 : data.effect?.getDescType()\n        const showAttr = attr.active = !!dtype && (data.id !== BUILD_EMBASSY_NID || gameHpr.alliance.isMeCreater())\n        // 显示下级信息和升级费用\n        const isMaxLv = data.isMaxLv(), nextInfo = data.nextLvInfo\n        const top = attr.Child('top'), need = node.Child('need')\n        top.active = need.active = !isMaxLv\n        attr.Child('items').Items(1, (it, _data) => {\n            if (showAttr) {\n                it.Child('cur/val').setLocaleKey('ui.build_eff_desc_' + dtype, data.effect?.getValueText() || '')\n            }\n            const nextLbl = it.Child('next', cc.Label)\n            if (it.Child('arrow').active = nextLbl.setActive(!isMaxLv)) {\n                nextLbl.setLocaleKey(nextInfo.effect?.getValueText() || '')\n            }\n        })\n        if (!isMaxLv) {\n            top.Child('cur').setLocaleKey('ui.lv', data.lv)\n            top.Child('next').setLocaleKey('ui.lv', data.nextLvInfo.lv)\n            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time)\n        }\n        this.updateBuildButtons(need.Child('buttons'), data)\n    }\n\n    // 刷新按钮\n    public updateBuildButtons(buttonsNode: cc.Node, data: BuildObj, condText?: string) {\n        if (data.isMaxLv()) {\n            return buttonsNode.Swih('')\n        }\n        buttonsNode.opacity = !!condText ? 120 : 255\n        const player = gameHpr.player\n        const bt = player.getBuildBtInfo(data.uid)\n        if (bt) { //是否在队列中\n            buttonsNode.Swih('uping')[0].Child('val').setLocaleKey(bt.isRuning() ? 'ui.uping' : 'ui.queueing')\n        } else {\n            buttonsNode.Swih('up_be')\n        }\n    }\n\n    public updateCostViewForBuild(node: cc.Node, ctypes: CTypeObj[], time?: number, cd?: number) {\n        // const up = node.Child('time/guide_up')\n        if (gameHpr.isNoviceMode) {\n            time /= NOVICE_BUILD_SPEED_MUL\n            time = Math.max(3, Math.floor(time * (1 - (cd || 0))))\n            cd = 0\n        }\n        this.updateCostView(node, ctypes, time, cd)\n        // if (up?.getActive()) {\n        //     node.Child('time/val', cc.Label).Color('#49983C')\n        // }\n    }\n\n    // 刷新费用\n    public updateCostView(node: cc.Node, ctypes: CTypeObj[], time?: number, cd?: number) {\n        node.Child('cost').Items(ctypes || [], (it, cost) => this.updateCostViewOne(it, cost, true))\n        if (node.Child('time')?.setActive(!!time)) {\n            cd = cd || 0\n            const up = node.Child('time/up', cc.Label)\n            if (up?.setActive(!!cd)) {\n                up.string = `(-${Math.floor(cd * 100)}%)`\n            }\n            if (cd) {\n                time = Math.max(3, Math.floor(time * (1 - cd)))\n            }\n            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss')\n        }\n    }\n\n    // 刷新费用\n    public updateFreeCostView(node: cc.Node, ctypes: CTypeObj[], time: number, cd: number, isFree: boolean, policyFreeCount: number) {\n        node.Child('cost').Items(ctypes || [], (it, cost) => this.updateFreeCostViewOne(it, cost, !(isFree || policyFreeCount > 0)))\n        if (node.Child('time')?.setActive(!!time)) {\n            const up = node.Child('time/up')\n            if (isFree || policyFreeCount > 0) {\n                cd = 1\n                const node = up.Swih('free')[0], bothFree = isFree && policyFreeCount > 0\n                node.Child('val', cc.Label).string = bothFree ? `x(${policyFreeCount}` : policyFreeCount > 0 ? `x${policyFreeCount}` : ''\n                node.Child('add', cc.Label).string = bothFree ? '+1' : isFree ? 'x1' : ''\n                node.Child('xx').active = bothFree\n            } else if (!!cd) {\n                up.Swih('val')[0].Component(cc.Label).string = `(-${Math.floor(cd * 100)}%)`\n            } else {\n                up.Swih('')\n            }\n            if (cd) {\n                time = Math.max(3, Math.floor(time * (1 - cd)))\n            }\n            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss')\n            node.Child('cost').children.forEach(m => m.opacity = (isFree || policyFreeCount > 0) && m.Data?.type !== CType.FIXATOR ? 100 : 255)\n            node.Child('time/up/val').opacity = node.Child('time/val').opacity = node.Child('time/icon').opacity = (isFree || policyFreeCount > 0) ? 100 : 255\n        }\n    }\n\n    // 刷新单个费用\n    public updateCostViewOne(it: cc.Node, cost: CTypeObj, isCheck?: boolean) {\n        if (it && cost) {\n            it.Data = cost\n            it.Child('icon', cc.Sprite).spriteFrame = resHelper.getResIcon(cost.type)\n            if (!isCheck) {\n                it.Child('val', cc.Label).string = cost.count + ''\n            } else {\n                it.Child('val', cc.Label).Color(gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + ''\n            }\n        }\n    }\n\n    // 刷新单个费用\n    public updateFreeCostViewOne(it: cc.Node, cost: CTypeObj, isCheck?: boolean) {\n        if (it && cost) {\n            it.Data = cost\n            it.Child('icon', cc.Sprite).spriteFrame = resHelper.getResIcon(cost.type)\n            it.Child('val', cc.Label).Color(!isCheck || gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + ''\n        }\n    }\n\n    // 更新费用\n    public updateCostText(it: cc.Node, json: any) {\n        if (json.gold > 0) {\n            it.Child('gold/icon', cc.Sprite).spriteFrame = resHelper.getResIcon(CType.GOLD)\n            it.Child('gold/val', cc.Label).string = json.gold + ''\n        } else if (json.ingot > 0) {\n            it.Child('gold/icon', cc.Sprite).spriteFrame = resHelper.getResIcon(CType.INGOT)\n            it.Child('gold/val', cc.Label).string = json.ingot + ''\n        } else {\n            it.Child('gold').active = false\n        }\n    }\n\n    // 显示位置\n    public updatePositionView(it: cc.Node, index: number, hasName: boolean = true) {\n        it.Data = index\n        const posLbl = it.Component(cc.Label)\n        if (hasName) {\n            posLbl.setLocaleKey('ui.position', gameHpr.getCellBaseNameByIndex(index), mapHelper.indexToPoint(index).Join())\n        } else {\n            posLbl.string = '(' + mapHelper.indexToPoint(index).Join() + ')'\n        }\n        posLbl._forceUpdateRenderData()\n        it.Child('line').width = posLbl.node.width\n    }\n\n    // 刷新道具 根据类型\n    public updateItemByCTypes(node: cc.Node, items: CTypeObj[], key?: string) {\n        node?.Items(items, (it, data) => this.updateItemByCTypeOne(it, data, key))\n    }\n    public updateItemByCTypeOne(it: cc.Node, item: CTypeObj, key: string, adaptSize?: cc.Size) {\n        if (!item || !it) {\n            return\n        }\n        const iconSpr = it.Child('icon', cc.Sprite), countLbl = it.Child('count', cc.Label)\n        if (iconSpr) {\n            iconSpr.node.removeAllChildren()\n        }\n        if (item.type === CType.BUILD_LV) { //建筑等级\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadBuildIcon('build_' + item.id, iconSpr, key || mc.currWindName)\n            it.Child('text')?.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count)\n        } else if (item.type === CType.HEAD_ICON) { //头像\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadPlayerHead(iconSpr, assetsMgr.getJsonData('headIcon', item.id)?.icon || '', key || mc.currWindName).then(() => (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize))\n            it.Child('text')?.setLocaleKey('ui.headicon_title')\n        } else if (item.type === CType.CHAT_EMOJI) { //聊天表情\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadEmojiIcon(item.id, iconSpr, key || mc.currWindName).then(() => (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize))\n            it.Child('text')?.setLocaleKey('ui.chat_emoji_title')\n        } else if (item.type === CType.TREASURE) { //宝箱\n            const lv = assetsMgr.getJsonData('treasure', item.id)?.lv || 1\n            it.Swih('text')[0]?.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count)\n        } else if (item.type === CType.TITLE) { //称号\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadIcon(CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName)\n            it.Child('text')?.setLocaleKey('titleText.' + item.id)\n        } else if (item.type === CType.WIN_POINT) { //胜点\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadIcon(CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName)\n            it.Child('text')?.setLocaleKey('ui.rank_score_num_2', item.count)\n        } else if (item.type === CType.PAWN) { //士兵\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(() => (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize))\n            it.Child('text')?.setLocaleKey('pawnText.name_' + item.id)\n        } else if (item.type === CType.EQUIP) { //装备\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadEquipIcon(item.id, iconSpr, key || mc.currWindName)\n            it.Child('text')?.setLocaleKey('equipText.name_' + item.id)\n        } else if (item.type === CType.POLICY) { //政策\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadPolicyIcon(item.id, iconSpr, key || mc.currWindName)\n            it.Child('text')?.setLocaleKey('policyText.name_' + item.id)\n        } else if (item.type === CType.PAWN_SKIN) { //皮肤\n            const textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin')\n            if (textNode) { //纯文本 '限定皮肤'\n                it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc')\n                textNode.off('click')\n                textNode.on('click', () => this.showPnl('common/ItemBox', item))\n            } else if (skinNode) {\n                it.Swih('pawn_skin')\n                resHelper.loadPawnHeadIcon(item.id, skinNode, key || mc.currWindName)\n            } else {\n                it.Swih(m => m.name === 'icon' || m.name === 'text')\n                iconSpr && resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(() => (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize))\n                it.Child('text')?.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000))\n            }\n        } else if (item.type === CType.HERO_DEBRIS) { //英雄残卷\n            if (it.Child('text_click')) {\n                it.Swih('text')[0]?.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id))\n            } else {\n                it.Swih(m => m.name === 'icon' || m.name === 'text')\n                resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName)\n                it.Child('text')?.setLocaleKey('portrayalText.name_' + item.id)\n            }\n        } else if (item.type === CType.HERO_OPT) { //自选英雄包\n            if (it.Child('text_click')) {\n                it.Swih('text')[0]?.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id)\n            } else {\n                it.Swih(m => m.name === 'icon' || m.name === 'text')\n                resHelper.loadIcon(CTYPE_ICON_URL[item.type] + '_' + item.id, iconSpr, key || mc.currWindName)\n                it.Child('text')?.setLocaleKey('ui.hero_opt_gift_short_' + item.id)\n            }\n        } else if (item.type === CType.COMPLETE_GUIDE) { //完成新手引导\n            it.Swih('text')[0]?.setLocaleKey('guideText.guide_task_complete')\n        } else if (item.type === CType.CITY_SKIN) { //城市皮肤\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            iconSpr && resHelper.loadCityIcon(item.id, iconSpr, key || mc.currWindName).then(() => (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize))\n            it.Child('text')?.setLocaleKey('ui.title_main_city_skin')\n        } else {\n            it.Swih(m => m.name === 'icon' || m.name === 'count')\n            if (iconSpr) {\n                const sf = resHelper.getResIcon(item.type)\n                if (sf) {\n                    iconSpr.spriteFrame = sf\n                } else {\n                    resHelper.loadIcon(CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName)\n                }\n            }\n            if (countLbl) {\n                countLbl.string = '' + item.count\n            }\n        }\n    }\n\n    public updateItemNameByCTypeOne(it: cc.Node, item: CTypeObj, isFormat: boolean = false) {\n        if (!item || !it) {\n            return\n        }\n        const countLbl = it.Child('count', cc.Label)\n        if (item.type === CType.BUILD_LV) { //建筑等级\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count)\n            } else {\n                it.Child('text')?.setLocaleKey('ui.build_lv', ut.nameFormator(assetsMgr.lang('buildText.name_' + item.id), 5), item.count)\n            }\n        } else if (item.type === CType.HEAD_ICON) { //头像\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.headicon_title'), 5))\n            } else {\n                it.Child('text')?.setLocaleKey('ui.headicon_title')\n            }\n        } else if (item.type === CType.CHAT_EMOJI) { //聊天表情\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.chat_emoji_title'), 5))\n            } else {\n                it.Child('text')?.setLocaleKey('ui.chat_emoji_title')\n            }\n        } else if (item.type === CType.TREASURE) { //宝箱\n            const lv = assetsMgr.getJsonData('treasure', item.id)?.lv || 1\n            if (isFormat) {\n                it.Swih('text')[0]?.setLocaleKey('ui.treasure_reward_desc', ut.nameFormator(assetsMgr.lang('ui.treasure_name_' + lv), 5), item.count)\n            } else {\n                it.Swih('text')[0]?.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count)\n            }\n        } else if (item.type === CType.TITLE) { //称号\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('titleText.' + item.id), 5))\n            } else {\n                it.Child('text')?.setLocaleKey('titleText.' + item.id)\n            }\n        } else if (item.type === CType.WIN_POINT) { //胜点\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.rank_score_num_2'), 5), item.count)\n            } else {\n                it.Child('text')?.setLocaleKey('ui.rank_score_num_2', item.count)\n            }\n        } else if (item.type === CType.PAWN) { //士兵\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + item.id), 5))\n            } else {\n                it.Child('text')?.setLocaleKey('pawnText.name_' + item.id)\n            }\n        } else if (item.type === CType.EQUIP) { //装备\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('equipText.name_' + item.id), 5))\n            } else {\n                it.Child('text')?.setLocaleKey('equipText.name_' + item.id)\n            }\n        } else if (item.type === CType.POLICY) { //政策\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('policyText.name_' + item.id), 5))\n            } else {\n                it.Child('text')?.setLocaleKey('policyText.name_' + item.id)\n            }\n        } else if (item.type === CType.PAWN_SKIN) { //皮肤\n            const textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin')\n            if (textNode) { //纯文本 '限定皮肤'\n                if (isFormat) {\n                    it.Swih('text_click')[0].setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.limited_skin_reward_desc'), 5))\n                } else {\n                    it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc')\n                }\n                textNode.off('click')\n                textNode.on('click', () => this.showPnl('common/ItemBox', item))\n            } else if (skinNode) {\n                it.Swih('pawn_skin')\n            } else {\n                it.Swih(m => m.name === 'icon' || m.name === 'text')\n                if (isFormat) {\n                    it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + Math.floor(item.id / 1000)), 5))\n                } else {\n                    it.Child('text')?.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000))\n                }\n            }\n        } else if (item.type === CType.HERO_DEBRIS) { //英雄残卷\n            if (it.Child('text_click')) {\n                if (isFormat) {\n                    it.Swih('text')[0]?.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id), 5))\n                } else {\n                    it.Swih('text')[0]?.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id))\n                }\n            } else {\n                it.Swih(m => m.name === 'icon' || m.name === 'text')\n                if (isFormat) {\n                    it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('portrayalText.name_' + item.id), 5))\n                } else {\n                    it.Child('text')?.setLocaleKey('portrayalText.name_' + item.id)\n                }\n            }\n        } else if (item.type === CType.HERO_OPT) { //自选英雄包\n            if (it.Child('text_click')) {\n                if (isFormat) {\n                    it.Swih('text')[0]?.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_' + item.id), 5))\n                } else {\n                    it.Swih('text')[0]?.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id)\n                }\n            } else {\n                it.Swih(m => m.name === 'icon' || m.name === 'text')\n                if (isFormat) {\n                    it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_short_' + item.id), 5))\n                } else {\n                    it.Child('text')?.setLocaleKey('ui.hero_opt_gift_short_' + item.id)\n                }\n            }\n        } else if (item.type === CType.COMPLETE_GUIDE) { //完成新手引导\n            if (isFormat) {\n                it.Swih('text')[0]?.setLocaleKey(ut.nameFormator(assetsMgr.lang('guideText.guide_task_complete'), 5))\n            } else {\n                it.Swih('text')[0]?.setLocaleKey('guideText.guide_task_complete')\n            }\n        } else if (item.type === CType.CITY_SKIN) { //城市皮肤\n            it.Swih(m => m.name === 'icon' || m.name === 'text')\n            if (isFormat) {\n                it.Child('text')?.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.title_main_city_skin'), 5))\n            } else {\n                it.Child('text')?.setLocaleKey('ui.title_main_city_skin')\n            }\n        } else {\n            it.Swih(m => m.name === 'icon' || m.name === 'count')\n            if (countLbl) {\n                countLbl.string = '' + item.count\n            }\n        }\n    }\n\n    // 刷新装备显示\n    public updateEquipView(node: cc.Node, equip: EquipInfo, key: string, lockEffect?: number, smeltEffects?: any[], showRange?: boolean) {\n        const nameNode = node.Child('name') || node.Child('icon/name')\n        nameNode.setLocaleKey(equip.name)\n        const meltNode = node.Child('melt') || node.Child('icon/melt')\n        if (meltNode?.setActive(equip.isSmelt())) {\n            for (let i = 0; i < 2; i++) {\n                const it = meltNode.Child(i), data = equip.smeltEffects[i]\n                if (it.active = !!data) {\n                    resHelper.loadEquipIcon(data.id, it, key)\n                }\n            }\n        }\n        const noForge = node.Child('no_forge') || node.Child('icon/no_forge')\n        noForge?.setActive(false)\n        const exclusive = node.Child('exclusive')\n        if (exclusive?.setActive(!!equip.exclusive_pawn)) {\n            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + equip.exclusive_pawn)\n        }\n        this.updateEquipAttrView(node.Child('attrs'), equip, lockEffect, smeltEffects, showRange)\n    }\n    public updateEquipAttrView(node: cc.Node, equip: EquipInfo, lockEffect?: number, smeltEffects?: any[], showRange?: boolean) {\n        const attrNode = node.Child('attr'), effectNode = node.Child('effects'), skillIntensifyNode = node.Child('skill_intensify')\n        node.Child('more_desc')?.setActive(false)\n        node.Child('more_effects')?.setActive(false)\n        // 属性\n        const serverRunDay = gameHpr.getServerRunDay()\n        attrNode.Items(equip.mainAttrs, (it, data) => {\n            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1)\n            let value = data.value, canShowRange = showRange && data.base.length > 0\n            if (!!data.todayAdd) {\n                value += serverRunDay * data.todayAdd\n            }\n            it.Child('val', cc.Label).string = '+' + value\n            // 额外添加的\n            const add = value - data.initValue\n            if (it.Child('add')?.setActive(!!add)) {\n                it.Child('add/0', cc.Label).string = '(' + data.initValue\n                const addLbl = it.Child('add/1', cc.Label)\n                if (addLbl.setActive(!canShowRange)) {\n                    addLbl.string = '+' + add\n                }\n            }\n            // 随机范围\n            if (it.Child('base')?.setActive(canShowRange)) {\n                data.base.forEach((v, i) => it.Child('base/' + i, cc.Label).string = v + '')\n            }\n        })\n        // 效果\n        const effectCount = equip.effects.length\n        if (effectNode.active = effectCount > 0) {\n            const mult = effectCount > 1\n            let effects = equip.effects, smeltEffectMap: { [key: number]: number } = null, effectIdMap: { [key: number]: boolean } = null\n            // 如果有锁定和融炼 排个序\n            if (lockEffect || smeltEffects) {\n                smeltEffectMap = {}\n                smeltEffects.forEach(m => smeltEffectMap[m.type] = m.id)\n                effects = effects.slice().sort((a, b) => {\n                    let aw = smeltEffectMap[a.type] ? 1 : 0, bw = smeltEffectMap[b.type] ? 1 : 0\n                    aw = aw * 10 + Number(a.type === lockEffect)\n                    bw = bw * 10 + Number(b.type === lockEffect)\n                    return aw - bw\n                })\n            }\n            if (smeltEffects && equip.isExclusive()) {\n                effectIdMap = {}\n                gameHpr.world.getExclusiveEquipEffects(equip.id).forEach(m => effectIdMap[m] = true)\n            }\n            effectNode.Items(effects, (it, data) => {\n                const descParams = data.getDescParams(showRange), smeltId = smeltEffectMap?.[data.type]\n                if (smeltId) { // 融炼词条\n                    it.Color('#C2B3A1')\n                    it.setLocaleKey('ui.yet_smelt_equip_' + Number(!!effectIdMap[data.type]), assetsMgr.lang(data.name, ...descParams).replace(/#000001/g, '#C2B3A1'), 'equipText.name_' + smeltId)\n                } else if (data.type === lockEffect) { // 锁定词条\n                    it.Color('#C2B3A1')\n                    it.setLocaleKey('ui.yet_lock_equip', assetsMgr.lang(data.name, ...descParams).replace(/#000001/g, '#C2B3A1'))\n                } else if (mult) { // 正常词条\n                    it.Color('#756963')\n                    it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang(data.name, ...descParams))\n                } else {\n                    it.Color('#756963')\n                    it.setLocaleKey(data.name, ...descParams)\n                }\n            })\n        }\n        // 技能强化效果\n        if (skillIntensifyNode?.setActive(!!equip.skillIntensify && equip.skillIntensify.length > 0)) {\n            skillIntensifyNode.setLocaleKey(`pawnSkillText.intensify_desc_${equip.skillIntensify[0]}_${equip.skillIntensify[1]}`)\n        }\n    }\n\n    // 刷新装备基础信息显示\n    public updateEquipBaseView(node: cc.Node, json: any) {\n        const nameNode = node.Child('name') || node.Child('icon/name')\n        nameNode.setLocaleKey('equipText.name_' + json.id)\n        const exclusive = node.Child('exclusive') || node.Child('icon/exclusive')\n        if (exclusive?.setActive(!!json.exclusive_pawn)) {\n            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + json.exclusive_pawn)\n        }\n        const noForge = node.Child('no_forge') || node.Child('icon/no_forge')\n        noForge?.setActive(true)\n        // 属性\n        const attrNode = node.Child('attrs/attr'), attrs = this.getEquipAttrs(json)\n        attrNode.Items(attrs, (it, data) => {\n            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1)\n            it.Child('val', cc.Label).string = this.wrapEquipVal(data.val)\n            it.Child('add')?.setActive(false)\n            it.Child('base')?.setActive(false)\n        })\n        // 效果\n        let effectIds = ut.stringToNumbers(json.effect, '|')\n        if (!!json.exclusive_pawn) {\n            effectIds = gameHpr.world.getExclusiveEquipEffects(json.id)\n        }\n        const effects = effectIds.map(m => assetsMgr.getJsonData('equipEffect', m)).sort((a, b) => a.sort - b.sort)\n        const effectsNode = node.Child('attrs/effects'), moreDescNode = node.Child('attrs/more_desc'), moreEffectsNode = node.Child('attrs/more_effects'), skillIntensifyNode = node.Child('attrs/skill_intensify')\n        // 多个的处理\n        moreDescNode.active = moreEffectsNode.active = effects.length > 1\n        if (moreEffectsNode.active) {\n            moreDescNode.setLocaleKey('ui.equip_more_effect_desc', json.effect_count)\n            moreEffectsNode.Items(effects, (it, m) => this.updateEquipBaseEffect(it, m, true))\n        }\n        // 单个的处理\n        if (effectsNode.active = !!effects.length && !moreEffectsNode.active) {\n            effectsNode.Items(effects, (it, m) => this.updateEquipBaseEffect(it, m, false))\n        }\n        // 技能强化效果\n        if (skillIntensifyNode.active = !!json.skill_intensify) {\n            const [a, b] = ut.stringToNumbers(json.skill_intensify, ',')\n            skillIntensifyNode.setLocaleKey(`pawnSkillText.intensify_desc_${a}_${b}`)\n        }\n        // 空属性\n        node.Child('attrs/empty_effect')?.setActive(!effects.length)\n    }\n\n    public wrapEquipVal(arr: number[]) {\n        return arr[0] === arr[1] ? (arr[0] + '') : `[${arr.join('-')}]`\n    }\n\n    public getEquipAttrs(data: any) {\n        const arr = []\n        if (data.hp) {\n            arr.push({ type: 1, val: ut.stringToNumbers(data.hp, ',') })\n        }\n        if (data.attack) {\n            arr.push({ type: 2, val: ut.stringToNumbers(data.attack, ',') })\n        }\n        return arr\n    }\n\n    public updateEquipBaseEffect(it: cc.Node, json: any, isMore?: boolean) {\n        const params = [], [a, b] = json.value.split(',')\n        if (json.id === EquipEffectType.MINGGUANG_ARMOR || json.id === EquipEffectType.BAIBI_SWORD) {\n            params.push('0')\n        } else if (!json.value) {\n            params.push('')\n        } else if (a === b) {\n            params.push(`${a}${json.suffix}`)\n        } else {\n            params.push(`[${a}-${b}]${json.suffix}`)\n        }\n        if (json.odds) {\n            params.push(`[${ut.stringToNumbers(json.odds, ',').join('-')}]%`)\n        }\n        it.Color('#756963')\n        if (isMore) {\n            it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang('equipText.effect_' + json.id, params))\n        } else {\n            it.setLocaleKey('equipText.effect_' + json.id, params)\n        }\n    }\n\n    // 刷新宠物信息\n    public updatePetView(it: cc.Node, id: number, lv: number) {\n        it.Child('name').setLocaleKey('pawnText.name_' + id)\n        const lvNode = it.Child('name/lv')\n        if (lvNode.active = !!lv) {\n            lvNode.setLocaleKey('ui.lv', lv)\n        }\n        it.Child('none').active = !lv\n        this.updatePetAttrView(it.Child('attrs'), id, lv)\n    }\n    public updatePetAttrView(node: cc.Node, id: number, lv: number) {\n        if (node.active = !!lv) {\n            const attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + lv)\n            const mainAttrs = [{ type: 1, value: attrJson.hp }, { type: 2, value: attrJson.attack }]\n            // 属性\n            node.Child('attr').Items(mainAttrs, (it, data) => {\n                it.Child('icon', cc.MultiFrame).setFrame(data.type - 1)\n                it.Child('val', cc.Label).string = '' + data.value\n            })\n            // 技能\n            const skillNode = node.Child('skill')\n            if (skillNode.active = !!attrJson.skill) {\n                skillNode.Items(ut.stringToNumbers(attrJson.skill), (node, data) => {\n                    const json = assetsMgr.getJsonData('pawnSkill', data)\n                    const text = assetsMgr.lang(json.desc, json.desc_params.split('|'))\n                    node.setLocaleKey('ui.res_transit_cap_desc', `<color=#333333>${assetsMgr.lang(json.name)}</c>`, text)\n                })\n            }\n        }\n    }\n\n    // 刷新玩家简介\n    public updatePlayerPersonalDesc(node: cc.Node, uid: string, plr?: IPersonalDescPlayer) {\n        const personalDescLbl = node.Component(cc.Label)\n        personalDescLbl.Color('#B6A591').string = '...'\n        gameHpr.getUserPersonalDesc(uid, plr).then(val => {\n            if (personalDescLbl.isValid) {\n                personalDescLbl.Color(val ? '#756963' : '#B6A591').string = val || assetsMgr.lang('ui.empty_personal_desc')\n            }\n        })\n    }\n\n    // 刷新称号显示\n    public updatePlayerTitleText(node: cc.Node, uid: string, plr?: ITitlePlayer) {\n        if (!node) {\n            return\n        }\n        gameHpr.getUserTitle(uid, plr).then(title => {\n            if (node.isValid) {\n                const json = assetsMgr.getJsonData('title', title)\n                node.Color(json?.color || '#756963').setLocaleKey(json ? 'titleText.' + json.id : 'ui.nought')\n            }\n        })\n    }\n\n    // 刷新人气显示\n    public updatePlayerPopularity(root: cc.Node, buttonNode: cc.Node, uid: string, key: string, plr?: IPopularityPlayer) {\n        const button = buttonNode.Swih(uid === gameHpr.getUid() ? 'popularity_record_be' : 'add_popularity_be')?.[0]?.Component(cc.Button)\n        if (button) {\n            button.node.opacity = 120\n            button.interactable = false\n        }\n        root.Items(1, (it, _) => {\n            resHelper.loadGiftIcon(101, it.Child('icon'), key)\n            it.Child('count').active = false\n            it.Child('loading').active = true\n        })\n        gameHpr.getUserPopularity(uid, plr).then(info => {\n            if (!root.isValid) {\n                return\n            } else if (!info?.list?.length) {\n                info = { list: [[101, 0]] } as any\n            }\n            if (button?.isValid) {\n                button.node.opacity = 255\n                button.interactable = true\n            }\n            root.Items(info.list, (it, [id, count]) => {\n                it.Child('count').active = true\n                it.Child('loading').active = false\n                resHelper.loadGiftIcon(id, it.Child('icon'), key)\n                it.Child('count', cc.Label).string = count + ''\n            })\n        })\n    }\n\n    // 刷新段位\n    public updatePlayerRankInfo(node: cc.Node, uid: string, key: string, plr?: IRankScorePlayer) {\n        gameHpr.getUserRankScore(uid, plr).then(data => {\n            if (node.isValid) {\n                const { id, winPoint } = gameHpr.resolutionRankScore(data.score, data.count)\n                const icon = node.Child('icon')\n                icon.Swih(id >= 0 ? 'val' : 'none')\n                id >= 0 && resHelper.loadRankScoreIcon(id, icon, key)\n                node.Child('rank').setLocaleKey(id >= 0 ? 'ui.rank_name_' + id : 'ui.rank_name_none')\n                node.Child('rank_val').setLocaleKey('ui.rank_score_num', winPoint)\n                node.Child('ranked_val', cc.Label).string = data.count + ''\n            }\n        })\n    }\n\n    // 刷新总局数\n    public updateTotalGameCount(node: cc.Node, uid: string, plr?: ITotalGameCountPlayer) {\n        const valLbl = node.Child('val', cc.Label), winRate = node.Child('win_rate')\n        valLbl.string = '-'\n        winRate.active = false\n        gameHpr.getUserTotalGameCount(uid, plr).then(info => {\n            if (node.isValid) {\n                // winRate.active = true\n                const [win, total] = info\n                valLbl.string = '' + total\n                // winRate.setLocaleKey('ui.win_rate', total ? Math.floor(win / total * 100) : 0)\n            }\n        })\n    }\n\n    // 在主场景显示建筑信息\n    public async showBuildInfoByMain(id: number, params?: any) {\n        const ui = FIXATION_MENU_CONFIG[id]\n        if (!ui) {\n            return false\n        }\n        this.showNetWait(true)\n        const area = await gameHpr.areaCenter.reqAreaByIndex(gameHpr.player.getMainCityIndex())\n        this.showNetWait(false)\n        const data = area?.getBuildsById(id)[0]\n        if (data) {\n            await this.showPnl(ui, data, params)\n            return true\n        }\n        return false\n    }\n\n    // 显示金币不足\n    public showGoldNotEnough() {\n        this.showMessageBox('ui.gold_not_enough_tip', {\n            okText: 'ui.button_exchange',\n            cancelText: 'ui.button_no',\n            ok: () => this.showPnl('common/ShopBuyGoldTip'),\n            cancel: () => { }\n        })\n    }\n\n    // 初始化转盘界面\n    public initWheelItem(it: cc.Node, data: any, descColor: string) {\n        let items: CTypeObj[] = []\n        if (data.factor > 0) {\n            it.Data = data\n            const runDay = gameHpr.user.getWheelInRoomRunDay()\n            if (runDay > 0) {\n                const count = Math.floor(30 * Math.min(runDay, 10) * data.factor)\n                items = count > 0 ? [new CTypeObj().init(CType.CEREAL, 0, count), new CTypeObj().init(CType.TIMBER, 0, count), new CTypeObj().init(CType.STONE, 0, count)] : []\n            }\n        } else if (data.factor === -1) {\n            it.Data = data\n            items = gameHpr.user.getWheelRandomAwards()\n        } else {\n            it.Data = null\n            items = gameHpr.stringToCTypes(data.award)\n        }\n        this.updateItemByCTypes(it.Child('award'), items)\n        it.Child('loading').active = !!it.Data && !items.length\n        it.Child('desc').Color(descColor).setLocaleKey(items.length === 0 ? 'ui.empty' : '')\n    }\n\n    // 打开关闭弹出框\n    public changePopupBoxList(node: cc.Node, val: boolean, isDown?: boolean) {\n        node.Child('select_mask_be').active = val\n        const mask = node.Child('mask'), root = mask.Child('root')\n        if (val) {\n            mask.active = true\n            root.y = isDown ? mask.height : -mask.height\n            const y = isDown ? -4 : 4\n            cc.tween(root).to(0.15, { y: y }, { easing: cc.easing.sineOut }).start()\n        } else {\n            root.y = isDown ? -4 : 4\n            const y = isDown ? mask.height : -mask.height\n            cc.tween(root).to(0.1, { y: y }).call(() => mask.active = false).start()\n        }\n        cc.tween(node.Child('icon')).to(0.15, { angle: val ? -180 : 0 }).start()\n    }\n    public closePopupBoxList(node: cc.Node) {\n        node.Child('select_mask_be').active = false\n        node.Child('mask').active = false\n        node.Child('icon').angle = 0\n    }\n\n    // 显示不再提示\n    public showNoLongerTip(key: string, data: NoLongerOpts) {\n        if (!gameHpr.isNoLongerTip(key)) {\n            data.noKey = key\n            data.okText = data.okText || 'ui.button_gotit'\n            this.showPnl('common/NoLongerTip', data)\n            return true\n        }\n        return false\n    }\n\n    // 显示不再提示\n    public showResFullNoLongerTip(key: string, items: CTypeObj[], data: NoLongerOpts) {\n        if (!gameHpr.isNoLongerTipBySid(key)) {\n            data.noKey = key\n            data.okText = data.okText || 'ui.button_gotit'\n            this.showPnl('common/ResFullNoLongerTip', items, data)\n            return true\n        }\n        return false\n    }\n\n    // 添加人气\n    public addPlayerPopularity(data: IPopularityPlayer, cb: Function) {\n        const info = data.popularityInfo\n        if (info?.reqing) {\n            return\n        } else if (info && info.records.length > 0) {\n            let lastTime = 0, d = null, uid = gameHpr.getUid()\n            info.records.forEach(m => {\n                if (m.time > lastTime) {\n                    lastTime = m.time\n                }\n                if (m.uid === uid) {\n                    d = m\n                }\n            })\n            const now = Date.now()\n            if (d && now - d.time < ONE_USER_POPULARITY_CHANGE_INTERVAL) {\n                const day = Math.max(1, Math.floor((ONE_USER_POPULARITY_CHANGE_INTERVAL - (now - d.time)) / ut.Time.Day))\n                return this.showMessageBox('ui.month_add_popularity_tip', { params: [day], okText: 'ui.button_gotit' })\n            } else if (lastTime > ut.dateZeroTime(gameHpr.getServerNowTime())) {\n                return this.showMessageBox('ui.today_add_popularity_tip', { okText: 'ui.button_gotit' })\n            }\n        }\n        this.showPnl('common/AddPopularityTip', data, cb)\n    }\n\n    // 刷新图鉴心\n    public updateBookStar(node: cc.Node, star: number) {\n        let val = star * 0.5\n        node.children.forEach((m, i) => {\n            if (val >= 1) {\n                val -= 1\n                m.Component(cc.MultiFrame).setFrame(2)\n            } else if (val >= 0.5) {\n                val -= 0.5\n                m.Component(cc.MultiFrame).setFrame(1)\n            } else {\n                m.Component(cc.MultiFrame).setFrame(0)\n            }\n        })\n    }\n\n    // 显示画像名字\n    public showPortrayalName(node: cc.Node, name: string, vice: string) {\n        node.Child('val').setLocaleKey(name)\n        const viceNode = node.Child('vice')\n        if (viceNode.active = !!vice) {\n            viceNode.setLocaleKey('ui.bracket', vice)\n        }\n    }\n\n    // 显示立绘\n    public updatePicture(id: number, isUnlock: boolean, iconNode: cc.Node, offset: cc.Vec2, hasAnim: boolean, key: string) {\n        const valNode = iconNode.Child('val')\n        const anim = valNode.Component(FrameAnimationCmpt)\n        anim?.clean()\n        resHelper.loadPortrayalImage(id, valNode, key)\n        iconNode.setPosition(offset)\n        valNode.opacity = isUnlock ? 255 : 100\n        if (isUnlock) {\n            iconNode.Component(cc.Sprite).spriteFrame = null\n        } else {\n            resHelper.loadPortrayalImage(id, iconNode, key)\n        }\n        if (anim && isUnlock && hasAnim) {\n            anim.init('portrayal_' + id, key).then(() => {\n                if (valNode.isValid) {\n                    anim.play('standby')\n                }\n            })\n        }\n    }\n\n    // 刷新画像碎片数量\n    public updatePortrayalDebrisCount(it: cc.Node, debris: number) {\n        const isCanComp = debris >= PORTRAYAL_COMP_NEED_COUNT\n        it.Child('debris_count/val', cc.Label).Color(isCanComp ? cc.Color.GREEN : cc.Color.WHITE).string = debris + ''\n        it.Child('debris_count').Color(isCanComp ? '#FFA647' : cc.Color.GRAY)\n    }\n\n    // 显示获得画像\n    public showGainPortrayalDebris(id: number, count: number) {\n        this.showPnl('common/GetPortrayal', id, count)\n    }\n\n    // 刷新英雄属性\n    public updatePortrayalAttr(node: cc.Node, data: PortrayalInfo, isHero?: boolean) {\n        const root = node.Child('attrs')\n        //\n        root.Child('avatar/val').setLocaleKey(data.avatarPawnName)\n        // 属性\n        root.Child('attr').Items(this.getPortrayalMainAttrs(data, isHero), (it, d) => {\n            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1)\n            it.Child('val', cc.Label).string = d.value\n        })\n        // 技能\n        const id = data.json.skill\n        root.Child('skill').setLocaleKey('ui.res_transit_cap_desc', `<color=#333333>${assetsMgr.lang('portrayalSkillText.name_' + id)}</c>`, assetsMgr.lang('portrayalSkillText.desc_' + id, this.getPortrayalSkillDescParams(data, isHero)))\n        // 韬略\n        const strategysNode = root.Child('strategys')\n        const showStrategy = root.Child('strategy').active = strategysNode.active = isHero && data.strategys.length > 0\n        if (showStrategy) {\n            root.Child('strategy/name/count', cc.Label).string = `(${data.strategys.length})`\n            strategysNode.Items(data.strategys, (it, strategy) => this.showStrategyText(it, strategy, data.avatarPawnName))\n        }\n    }\n\n    // 显示韬略文本\n    public showStrategyText(it: cc.Node, strategy: StrategyObj, avatarPawnName: string) {\n        it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang(strategy.desc, strategy.getDescParamsRange(avatarPawnName, 1, '#' + it.color.toHEX('#rrggbb'))))\n    }\n\n    public getPortrayalMainAttrs(data: PortrayalInfo, isHero?: boolean) {\n        if (isHero && data.mainAttrs.length > 0) {\n            return data.mainAttrs.map(m => { return { type: m.type, value: '+' + m.value } })\n        }\n        return ['hp', 'attack'].map((k, i) => { return { type: i + 1, value: '[' + data.json[k].replace(',', '-') + ']' } })\n    }\n\n    // 获取说明参数\n    public getPortrayalSkillDescParams(data: PortrayalInfo, isHero?: boolean) {\n        if (isHero && data.skill) {\n            return data.skill.getDescParams()\n        }\n        const json = assetsMgr.getJsonData('portrayalSkill', data.json.skill)\n        const arr = []\n        if (json.value) {\n            arr.push('[' + json.value.replace(',', '-') + ']' + json.suffix)\n        }\n        if (json.target) {\n            arr.push(json.target)\n        }\n        return arr\n    }\n\n    // 刷新英雄简短属性\n    public updatePortrayalShortAttr(node: cc.Node, attrs: AttrArrayInfo[], avatarPawnName: string) {\n        let mainAttrs: { type: number, value: number }[] = [], skill: PortrayalSkillObj = null, strategys: StrategyObj[] = []\n        attrs.forEach(m => {\n            const [fieldType, type, value] = m.attr\n            if (fieldType === 0) { //属性\n                mainAttrs.push({ type, value })\n            } else if (fieldType === 1) { //技能\n                skill = new PortrayalSkillObj().init(type, value)\n            } else if (fieldType === 2) { //韬略\n                strategys.push(new StrategyObj().init(type))\n            }\n        })\n        // 属性\n        node.Child('attr').Items(mainAttrs, (it, d) => {\n            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1)\n            it.Child('val', cc.Label).string = '+' + d.value\n        })\n        // 技能\n        const skillNode = node.Child('skill')\n        if (skillNode.active = !!skill) {\n            skillNode.setLocaleKey(skill.desc, skill.getDescParams())\n        }\n        // 韬略\n        node.Child('strategy/name/count', cc.Label).string = `(${strategys.length})`\n        node.Child('strategys').Items(strategys, (it, strategy) => viewHelper.showStrategyText(it, strategy, avatarPawnName))\n    }\n\n    // 返回大厅\n    public backLobby() {\n        gameHpr.resetSelectServer(true).then(res => this.gotoWind('lobby'))\n    }\n\n    // 显示英雄自选\n    public async showHeroOptSelect(lv: number) {\n        return new Promise<number>(resolve => {\n            const arr = HERO_OPT_GIFT[lv]\n            const list = arr ? arr.map(m => new PortrayalInfo().init(m)) : assetsMgr.getJson('portrayalBase').datas.map(m => new PortrayalInfo().init(m.id, m))\n            this.showPnl('common/SelectPortrayal', SelectPortrayalType.GIFT, list, (arr: PortrayalInfo[]) => resolve(arr[0]?.id ?? 0), lv)\n        })\n    }\n\n    // 获取状态背景颜色\n    public getHeroStateBgColor(state: number) {\n        if (state === 0) {\n            return '#FFFFFF'\n        } else if (state === 1) {\n            return '#21DE29'\n        } else if (state === 2) {\n            return '#FFFFFF'\n        }\n        return '#F45757'\n    }\n\n    // 显示申请好友\n    public showApplyFriend(data: any) {\n        this.showMessageBox('ui.apply_friend_tip', {\n            params: [ut.nameFormator(data.nickname, 8)],\n            ok: () => gameHpr.friend.applyFriend(data.uid).then(err => {\n                if (err) {\n                    return this.showAlert(err)\n                }\n                this.showAlert('toast.apply_friend_succeed')\n            }),\n            cancel: () => { }\n        })\n    }\n\n    // 显示拉黑\n    public showBlacklist(data: any, event: cc.Event.EventTouch, buttonsNode: cc.Node) {\n        const state = gameHpr.friend.isInBlacklist(data.uid)\n        this.showMessageBox(state ? 'ui.cancel_blacklist_desc' : 'ui.add_blacklist_desc', {\n            params: [ut.nameFormator(data.nickname, 8)],\n            ok: () => gameHpr.friend.doBlacklist(data.uid, state).then(err => {\n                if (err) {\n                    return this.showAlert(err)\n                } else if (buttonsNode.isValid) {\n                    event.target.Child('val', cc.MultiFrame).setFrame(!state)\n                    buttonsNode.Child('add_friend_be').active = state && !gameHpr.friend.isFriend(data.uid)\n                }\n                this.showAlert(state ? 'toast.cancel_blacklist_succeed' : 'toast.add_blacklist_succeed')\n            }),\n            cancel: () => { }\n        })\n    }\n\n    public async showFeedback() {\n        if (gameHpr.isGLobal()) {\n            return this.showPnl('login/HDFeedback')\n        } else {\n            return this.showPnl('login/Feedback')\n        }\n    }\n\n    // 查看奖励详情\n    public previewRewardDetail(reward: CTypeObj) {\n        if (reward.type === CType.PAWN_SKIN) {\n            this.showPnl('common/ItemBox', reward)\n        } else if (reward.type === CType.HERO_DEBRIS) {\n            const json = assetsMgr.getJsonData('portrayalBase', reward.id)\n            this.showPnl('common/PortrayalBaseInfo', json, 'shop')\n        } else if (reward.type === CType.HERO_OPT) {\n            const id = reward.id, arr = HERO_OPT_GIFT[id]\n            const list = arr ? arr.map(m => new PortrayalInfo().init(m)) : assetsMgr.getJson('portrayalBase').datas.map(m => new PortrayalInfo().init(m.id, m))\n            this.showPnl('common/SelectPortrayalPreview', id, list)\n        }\n    }\n\n    // 通过deeplink打开相应游戏界面\n    public openUIByDeepLink() {\n        const data = gameHpr.getEnterQuery()\n        if (data?.openUI) {\n            const url: string = data.openUI.replace('_', '/'), params: string[] = data.params.split('_')\n            this.showPnl(url, ...params)\n        }\n    }\n\n    // 刷新士兵属性\n    public updatePawnAttrs(node: cc.Node, pawn: PawnObj) {\n        node.Child('hp/val', cc.Label).string = pawn.getHpText()\n        node.Child('anger/val', cc.Label).string = pawn.getAngerText()\n        node.Child('attack/val', cc.Label).string = pawn.getAttackText()\n        node.Child('attack_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getAttackRange())\n        node.Child('move_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getMoveRange())\n        if (node.Child('cereal_c').active = !!pawn.baseJson.cereal_cost) {\n            node.Child('cereal_c/val', cc.Label).string = pawn.baseJson.cereal_cost\n        }\n    }\n\n    // 刷新士兵技能\n    public updatePawnSkills(node: cc.Node, pawn: PawnObj, key: string) {\n        // 技能\n        const skillNode = node.Child('skills'), portrayalSkill = node.Child('portrayal_skill_be')\n        if (skillNode.active = pawn.skills.length > 0) {\n            const skills = []\n            pawn.skills.forEach(m => { //这里将2个被动技能放到一起\n                if (m.type < PawnSkillType.RESTRAIN) {\n                    let d = skills.find(s => s.type === PawnSkillType.RESTRAIN)\n                    if (!d) {\n                        d = skills.add({ id: 101, useType: m.use_type, type: PawnSkillType.RESTRAIN, name: m.json.name, descs: [], desc_params: [] })\n                    }\n                    d.descs.push(m.json.desc)\n                    d.desc_params.push(String(m.json.desc_params).split('|'))\n                } else if (m.type === PawnSkillType.INSTABILITY_ATTACK) {\n                    skills.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getAttackText(), m.json.desc_params]] })\n                } else if (m.type === PawnSkillType.PEOPLE_BROKEN) {\n                    skills.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [pawn.getAttackTextByIndex(2)] })\n                } else if (m.type === PawnSkillType.SKILL_217) {\n                    skills.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[m.json.desc_params, pawn.getMoveRange()]] })\n                } else if (m.type === PawnSkillType.CADET) {\n                    skills.add({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getCadetLvText(), m.json.desc_params, 'pawnText.name_4205']] })\n                } else {\n                    skills.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [String(m.json.desc_params).split('|')] })\n                }\n            })\n            skillNode.Items(skills, (it, skill) => {\n                it.Data = skill\n                resHelper.loadSkillIcon(skill.id, it.Child('val'), key)\n            })\n        }\n        // 英雄技能\n        if (portrayalSkill?.setActive(!!pawn.portrayal?.skill?.id)) {\n            resHelper.loadHeroSkillIcon(pawn.portrayal.skill.id, portrayalSkill.Child('val'), key)\n        }\n        return skillNode.active || !!portrayalSkill?.active\n    }\n\n    // 刷新军队状态\n    public updateArmyState(node: cc.Node, data: ArmyShortInfo | ArmyObj, march: MarchObj, isHasLving: boolean, isMe: boolean) {\n        const states: ArmyState[] = [], tonden = data instanceof ArmyObj ? gameHpr.world.getArmyTondenInfo(data.index, data.uid) : data.tonden\n        if (data.drillPawns.length > 0) {\n            states.push(ArmyState.DRILL)\n        }\n        if (data.curingPawns.length > 0) {\n            states.push(ArmyState.CURING)\n        }\n        if (isHasLving) {\n            states.push(ArmyState.LVING)\n        }\n        if (tonden) {\n            states.push(ArmyState.TONDEN)\n        }\n        if (data.state !== ArmyState.NONE || states.length === 0) {\n            states.unshift(data.state)\n        }\n        const scroll = node.Child('stateScroll'), player = gameHpr.player\n        scroll.Child('state').Items(states, (it, state) => {\n            const color = ARMY_STATE_COLOR[state]\n            it.Child('val').Color(color).setLocaleKey('ui.army_state_' + state)\n            const timeLbl = it.Child('time', cc.LabelTimer)\n            timeLbl.Color(color)\n            if (!isMe) {\n                timeLbl.setActive(false)\n            } else if (state === ArmyState.MARCH && march) {\n                const targetIndex = march.targetIndex\n                timeLbl.setActive(true)\n                timeLbl.run(march.getSurplusTime() * 0.001, () => {\n                    if (node.isValid) {\n                        data.index = targetIndex\n                        data.state = ArmyState.NONE\n                        data.treasures.forEach(m => m.index = data.index)\n                        this.updateArmyState(node, data, march, isHasLving, isMe)\n                    }\n                })\n                timeLbl.node.opacity = 255\n            } else if (state === ArmyState.DRILL || state === ArmyState.LVING || state === ArmyState.CURING) { //训练\n                timeLbl.setActive(true)\n                const { time, pause }: any = state === ArmyState.DRILL ? player.getSumDrillTimeByArmy(data.uid) : state === ArmyState.CURING ? player.getSumCuringTimeByArmy(data.uid) : player.getSumLvingTimeByArmy(data.uid)\n                if (pause) {\n                    timeLbl.string = ut.millisecondFormat(time, 'h:mm:ss')\n                } else {\n                    timeLbl.run(time * 0.001)\n                }\n                timeLbl.node.opacity = pause ? 128 : 255\n            } else if (state === ArmyState.TONDEN && tonden) { //屯田\n                timeLbl.setActive(true)\n                timeLbl.run(tonden.getSurplusTime() * 0.001, () => {\n                    if (node.isValid) {\n                        if (data instanceof ArmyObj) {\n                        } else {\n                            data.tonden = null\n                        }\n                        data.state = ArmyState.NONE\n                        this.updateArmyState(node, data, march, isHasLving, isMe)\n                    }\n                })\n                timeLbl.node.opacity = 255\n            } else {\n                timeLbl.setActive(false)\n            }\n        })\n    }\n}\n\nexport const viewHelper = new ViewHelper()\nif (cc.sys.isBrowser) {\n    window['viewHelper'] = viewHelper\n}"]}