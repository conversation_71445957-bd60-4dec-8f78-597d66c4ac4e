"use strict";
cc._RF.push(module, 'de2ef8h2fZIPpcnDYYevUvO', 'AncientBuildCmpt');
// app/script/view/area/AncientBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AncientObj_1 = require("../../model/main/AncientObj");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var AncientBTAnimRoleConf_1 = require("./AncientBTAnimRoleConf");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 遗迹建筑
var AncientBuildCmpt = /** @class */ (function (_super) {
    __extends(AncientBuildCmpt, _super);
    function AncientBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.lvNode = null; //等级节点
        _this.upLvAnimNode = null; //升级动画
        _this.touchCmpt = null;
        _this.ancientInfo = null;
        return _this;
    }
    AncientBuildCmpt.prototype.init = function (data, origin, originY, owner) {
        _super.prototype.init.call(this, data, origin, originY, owner);
        this.initAncientInfo(data);
        this.touchCmpt = this.body.addComponent(ClickTouchCmpt_1.default).on(this.onClick, this);
        this.syncPoint();
        this.syncZindex();
        // 初始化等级
        this.initLv();
        this.updateLv(this.ancientInfo.lv);
        // 显示是否在升级中
        this.updateUpLvAnim();
        return this;
    };
    // 重新同步
    AncientBuildCmpt.prototype.resync = function (data) {
        this.data = data;
        this.initAncientInfo(data);
        this.syncPoint();
        this.syncZindex();
        this.setCanClick(true);
        this.updateLv(this.ancientInfo.lv);
        this.updateUpLvAnim();
        return this;
    };
    // 初始化遗迹信息
    AncientBuildCmpt.prototype.initAncientInfo = function (data) {
        if (data.aIndex < 0) {
            this.ancientInfo = new AncientObj_1.default().init(GameHelper_1.gameHpr.world.getMapCellByIndex(data.aIndex));
            this.ancientInfo.updateInfo({ lv: data.lv });
        }
        else {
            this.ancientInfo = GameHelper_1.gameHpr.world.getAncientInfo(data.aIndex);
        }
    };
    AncientBuildCmpt.prototype.initLv = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.lvNode = cc.instantiate2(pfb, this.node);
                        this.lvNode.zIndex = 1;
                        this.lvNode.setPosition(Math.floor(this.data.size.x * 0.5) * Constant_1.TILE_SIZE, 36);
                        this.lvNode.Child('val', cc.Label).string = '' + this.ancientInfo.lv;
                        this.lvNode.active = true;
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载升级动画
    AncientBuildCmpt.prototype.loadUpLvAnim = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_ANCIENT_BT', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.upLvAnimNode = cc.instantiate2(pfb, this.node);
                        this.upLvAnimNode.zIndex = 1;
                        this.upLvAnimNode.setPosition(0, 0);
                        this.upLvAnimNode.active = false;
                        this.updateUpLvAnim();
                        return [2 /*return*/];
                }
            });
        });
    };
    AncientBuildCmpt.prototype.clean = function () {
        var _a;
        this.unscheduleAllCallbacks();
        this.node.stopAllActions();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.clean();
        this.node.destroy();
        this.data = null;
    };
    AncientBuildCmpt.prototype.onClick = function () {
        if (!this.data) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.ancientInfo && this.data.aIndex >= 0 && GameHelper_1.gameHpr.checkIsOneAlliance(this.ancientInfo.owner)) {
            ViewHelper_1.viewHelper.showPnl(this.data.getUIUrl(), this.data);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', this.data);
        }
    };
    // 设置是否可以点击
    AncientBuildCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 刷新等级
    AncientBuildCmpt.prototype.updateLv = function (lv) {
        var _a;
        if (this.lvNode) {
            this.lvNode.Child('val', cc.Label).string = '' + lv;
        }
        if (this.data.isMaxLv()) {
            this.Child('body/val', cc.MultiFrame).setFrame(5);
        }
        else if ((_a = this.ancientInfo) === null || _a === void 0 ? void 0 : _a.owner) {
            this.Child('body/val', cc.MultiFrame).setFrame(Math.floor(lv / 5) + 1);
        }
        else {
            this.Child('body/val', cc.MultiFrame).setFrame(0);
        }
    };
    // 刷新升级动画
    AncientBuildCmpt.prototype.updateUpLvAnim = function () {
        var _a, _b;
        if (!this.ancientInfo.owner || this.data.isMaxLv() || this.ancientInfo.state !== 1 || !!this.ancientInfo.pauseState) {
            (_a = this.upLvAnimNode) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else if (this.upLvAnimNode) {
            this.upLvAnimNode.active = true;
            this.upLvAnimNode.Child('time', cc.LabelTimer).run(this.ancientInfo.getSurplusTime() * 0.001);
            var roles = ((_b = AncientBTAnimRoleConf_1.ANCIENT_BTANIM_ROLE_POSITION[this.data.id]) === null || _b === void 0 ? void 0 : _b[Math.floor(this.ancientInfo.lv / 5)]) || [];
            this.upLvAnimNode.Child('root').Items(roles, function (it, data) {
                it.setPosition(data.x, data.y);
                it.scaleX = data.scaleX;
                it.Component(cc.Animation).play('ancient_bt', ut.random(1, 5) * 0.1);
            });
        }
        else {
            this.loadUpLvAnim();
        }
    };
    AncientBuildCmpt = __decorate([
        ccclass
    ], AncientBuildCmpt);
    return AncientBuildCmpt;
}(BaseBuildCmpt_1.default));
exports.default = AncientBuildCmpt;

cc._RF.pop();