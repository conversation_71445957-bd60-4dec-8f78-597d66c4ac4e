{"version": 3, "sources": ["assets\\app\\script\\view\\area\\AncientBuildCmpt.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA2D;AAC3D,6DAAyD;AACzD,6DAA4D;AAE5D,0DAAqD;AACrD,yDAAoD;AACpD,iEAAuE;AACvE,iDAA4C;AAEtC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,OAAO;AAEP;IAA8C,oCAAa;IAA3D;QAAA,qEA+HC;QA7HW,YAAM,GAAY,IAAI,CAAA,CAAC,MAAM;QAC7B,kBAAY,GAAY,IAAI,CAAA,CAAC,MAAM;QACnC,eAAS,GAAmB,IAAI,CAAA;QAEhC,iBAAW,GAAe,IAAI,CAAA;;IAyH1C,CAAC;IAvHU,+BAAI,GAAX,UAAY,IAAc,EAAE,MAAe,EAAE,OAAe,EAAE,KAAa;QACvE,iBAAM,IAAI,YAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;QACxC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAc,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAC9E,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,QAAQ;QACR,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;QAClC,WAAW;QACX,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,OAAO,IAAI,CAAA;IACf,CAAC;IAED,OAAO;IACA,iCAAM,GAAb,UAAc,IAAc;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;QAClC,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,OAAO,IAAI,CAAA;IACf,CAAC;IAED,UAAU;IACF,0CAAe,GAAvB,UAAwB,IAAc;QAClC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACjB,IAAI,CAAC,WAAW,GAAG,IAAI,oBAAU,EAAE,CAAC,IAAI,CAAC,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;YACtF,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;SAC/C;aAAM;YACH,IAAI,CAAC,WAAW,GAAG,oBAAO,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAC/D;IACL,CAAC;IAEa,iCAAM,GAApB;;;;;4BACgB,qBAAM,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAA;;wBAAtE,GAAG,GAAG,SAAgE;wBAC5E,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE;4BAC1C,sBAAM;yBACT;wBACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;wBAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;wBACtB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAS,EAAE,EAAE,CAAC,CAAA;wBAC3E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAA;wBACpE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAA;;;;;KAC5B;IAED,SAAS;IACI,uCAAY,GAAzB;;;;;4BACgB,qBAAM,SAAS,CAAC,WAAW,CAAC,wBAAwB,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAA;;wBAA9E,GAAG,GAAG,SAAwE;wBACpF,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE;4BAC1C,sBAAM;yBACT;wBACD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;wBACnD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAA;wBAC5B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;wBACnC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;wBAChC,IAAI,CAAC,cAAc,EAAE,CAAA;;;;;KACxB;IAEM,gCAAK,GAAZ;;QACI,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAA;QAC1B,MAAA,IAAI,CAAC,SAAS,0CAAE,KAAK,GAAE;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAEO,kCAAO,GAAf;QACI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAM;SACT;QACD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;YACjG,uBAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;SACtD;aAAM;YACH,uBAAU,CAAC,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;SAC1D;IACL,CAAC;IAED,WAAW;IACJ,sCAAW,GAAlB,UAAmB,GAAY;QAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,GAAG,CAAA;SACpC;IACL,CAAC;IAED,OAAO;IACA,mCAAQ,GAAf,UAAgB,EAAU;;QACtB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,CAAA;SACtD;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;SACpD;aAAM,UAAI,IAAI,CAAC,WAAW,0CAAE,KAAK,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;SACzE;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;SACpD;IACL,CAAC;IAED,SAAS;IACF,yCAAc,GAArB;;QACI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;YACjH,MAAA,IAAI,CAAC,YAAY,0CAAE,SAAS,CAAC,KAAK,EAAC;SACtC;aAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC,CAAA;YAC7F,IAAM,KAAK,GAAU,OAAA,oDAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,0CAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC,MAAK,EAAE,CAAA;YAC5G,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;gBAClD,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;gBAC9B,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;gBACvB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA;YACxE,CAAC,CAAC,CAAA;SACL;aAAM;YACH,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;IACL,CAAC;IA9HgB,gBAAgB;QADpC,OAAO;OACa,gBAAgB,CA+HpC;IAAD,uBAAC;CA/HD,AA+HC,CA/H6C,uBAAa,GA+H1D;kBA/HoB,gBAAgB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { TILE_SIZE } from \"../../common/constant/Constant\";\r\nimport { gameHpr } from \"../../common/helper/GameHelper\";\r\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\r\nimport BuildObj from \"../../model/area/BuildObj\";\r\nimport AncientObj from \"../../model/main/AncientObj\";\r\nimport ClickTouchCmpt from \"../cmpt/ClickTouchCmpt\";\r\nimport { ANCIENT_BTANIM_ROLE_POSITION } from \"./AncientBTAnimRoleConf\";\r\nimport BaseBuildCmpt from \"./BaseBuildCmpt\";\r\n\r\nconst { ccclass, property } = cc._decorator;\r\n\r\n// 遗迹建筑\r\n@ccclass\r\nexport default class AncientBuildCmpt extends BaseBuildCmpt {\r\n\r\n    private lvNode: cc.Node = null //等级节点\r\n    private upLvAnimNode: cc.Node = null //升级动画\r\n    private touchCmpt: ClickTouchCmpt = null\r\n\r\n    private ancientInfo: AncientObj = null\r\n\r\n    public init(data: BuildObj, origin: cc.Vec2, originY: number, owner: string) {\r\n        super.init(data, origin, originY, owner)\r\n        this.initAncientInfo(data)\r\n        this.touchCmpt = this.body.addComponent(ClickTouchCmpt).on(this.onClick, this)\r\n        this.syncPoint()\r\n        this.syncZindex()\r\n        // 初始化等级\r\n        this.initLv()\r\n        this.updateLv(this.ancientInfo.lv)\r\n        // 显示是否在升级中\r\n        this.updateUpLvAnim()\r\n        return this\r\n    }\r\n\r\n    // 重新同步\r\n    public resync(data: BuildObj) {\r\n        this.data = data\r\n        this.initAncientInfo(data)\r\n        this.syncPoint()\r\n        this.syncZindex()\r\n        this.setCanClick(true)\r\n        this.updateLv(this.ancientInfo.lv)\r\n        this.updateUpLvAnim()\r\n        return this\r\n    }\r\n\r\n    // 初始化遗迹信息\r\n    private initAncientInfo(data: BuildObj) {\r\n        if (data.aIndex < 0) {\r\n            this.ancientInfo = new AncientObj().init(gameHpr.world.getMapCellByIndex(data.aIndex))\r\n            this.ancientInfo.updateInfo({ lv: data.lv })\r\n        } else {\r\n            this.ancientInfo = gameHpr.world.getAncientInfo(data.aIndex)\r\n        }\r\n    }\r\n\r\n    private async initLv() {\r\n        const pfb = await assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, 'area')\r\n        if (!this.node || !this.node.isValid || !pfb) {\r\n            return\r\n        }\r\n        this.lvNode = cc.instantiate2(pfb, this.node)\r\n        this.lvNode.zIndex = 1\r\n        this.lvNode.setPosition(Math.floor(this.data.size.x * 0.5) * TILE_SIZE, 36)\r\n        this.lvNode.Child('val', cc.Label).string = '' + this.ancientInfo.lv\r\n        this.lvNode.active = true\r\n    }\r\n\r\n    // 加载升级动画\r\n    public async loadUpLvAnim() {\r\n        const pfb = await assetsMgr.loadTempRes('build/BUILD_ANCIENT_BT', cc.Prefab, 'area')\r\n        if (!this.node || !this.node.isValid || !pfb) {\r\n            return\r\n        }\r\n        this.upLvAnimNode = cc.instantiate2(pfb, this.node)\r\n        this.upLvAnimNode.zIndex = 1\r\n        this.upLvAnimNode.setPosition(0, 0)\r\n        this.upLvAnimNode.active = false\r\n        this.updateUpLvAnim()\r\n    }\r\n\r\n    public clean() {\r\n        this.unscheduleAllCallbacks()\r\n        this.node.stopAllActions()\r\n        this.touchCmpt?.clean()\r\n        this.node.destroy()\r\n        this.data = null\r\n    }\r\n\r\n    private onClick() {\r\n        if (!this.data) {\r\n            return\r\n        }\r\n        audioMgr.playSFX('click')\r\n        if (this.ancientInfo && this.data.aIndex >= 0 && gameHpr.checkIsOneAlliance(this.ancientInfo.owner)) {\r\n            viewHelper.showPnl(this.data.getUIUrl(), this.data)\r\n        } else {\r\n            viewHelper.showPnl('build/BuildAncientBase', this.data)\r\n        }\r\n    }\r\n\r\n    // 设置是否可以点击\r\n    public setCanClick(val: boolean) {\r\n        if (this.touchCmpt) {\r\n            this.touchCmpt.interactable = val\r\n        }\r\n    }\r\n\r\n    // 刷新等级\r\n    public updateLv(lv: number) {\r\n        if (this.lvNode) {\r\n            this.lvNode.Child('val', cc.Label).string = '' + lv\r\n        }\r\n        if (this.data.isMaxLv()) {\r\n            this.Child('body/val', cc.MultiFrame).setFrame(5)\r\n        } else if (this.ancientInfo?.owner) {\r\n            this.Child('body/val', cc.MultiFrame).setFrame(Math.floor(lv / 5) + 1)\r\n        } else {\r\n            this.Child('body/val', cc.MultiFrame).setFrame(0)\r\n        }\r\n    }\r\n\r\n    // 刷新升级动画\r\n    public updateUpLvAnim() {\r\n        if (!this.ancientInfo.owner || this.data.isMaxLv() || this.ancientInfo.state !== 1 || !!this.ancientInfo.pauseState) {\r\n            this.upLvAnimNode?.setActive(false)\r\n        } else if (this.upLvAnimNode) {\r\n            this.upLvAnimNode.active = true\r\n            this.upLvAnimNode.Child('time', cc.LabelTimer).run(this.ancientInfo.getSurplusTime() * 0.001)\r\n            const roles: any[] = ANCIENT_BTANIM_ROLE_POSITION[this.data.id]?.[Math.floor(this.ancientInfo.lv / 5)] || []\r\n            this.upLvAnimNode.Child('root').Items(roles, (it, data) => {\r\n                it.setPosition(data.x, data.y)\r\n                it.scaleX = data.scaleX\r\n                it.Component(cc.Animation).play('ancient_bt', ut.random(1, 5) * 0.1)\r\n            })\r\n        } else {\r\n            this.loadUpLvAnim()\r\n        }\r\n    }\r\n}"]}