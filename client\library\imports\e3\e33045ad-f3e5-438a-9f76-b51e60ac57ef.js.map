{"version": 3, "sources": ["assets\\app\\script\\model\\area\\PawnObj.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,qDAAqH;AAErH,0DAAoD;AACpD,2EAAyE;AACzE,6DAAwD;AAExD,yDAAmD;AAEnD,+CAAyC;AACzC,qCAA+B;AAC/B,+CAAyC;AACzC,+CAAyC;AAEzC,OAAO;AACP;IA0CI;QAxCO,WAAM,GAAW,CAAC,CAAA,CAAC,QAAQ;QAC3B,aAAQ,GAAW,CAAC,CAAC,CAAA;QACrB,QAAG,GAAW,EAAE,CAAA;QAChB,SAAI,GAAW,EAAE,CAAA,CAAC,QAAQ;QAC1B,YAAO,GAAW,EAAE,CAAA,CAAC,SAAS;QAC9B,aAAQ,GAAW,EAAE,CAAA,CAAC,MAAM;QAC5B,UAAK,GAAW,EAAE,CAAA;QAClB,kBAAa,GAA8B,EAAE,CAAA,CAAC,IAAI;QAClD,cAAS,GAAmB,EAAE,CAAA,CAAC,QAAQ;QAEvC,OAAE,GAAW,CAAC,CAAA;QACd,UAAK,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QACxB,OAAE,GAAW,CAAC,CAAA,CAAC,IAAI;QACnB,WAAM,GAAW,CAAC,CAAA,CAAC,MAAM;QACzB,UAAK,GAAW,CAAC,CAAA,CAAC,MAAM;QACxB,UAAK,GAAW,CAAC,CAAA;QACjB,aAAQ,GAAW,CAAC,CAAA,CAAC,IAAI;QACzB,aAAQ,GAAW,CAAC,CAAA;QACpB,WAAM,GAAW,CAAC,CAAA;QAClB,gBAAW,GAAW,CAAC,CAAA,CAAC,MAAM;QAC9B,cAAS,GAAW,CAAC,CAAA,CAAC,MAAM;QAC5B,gBAAW,GAAW,CAAC,CAAA,CAAC,QAAQ;QAChC,UAAK,GAAc,IAAI,CAAA,CAAC,SAAS;QACjC,cAAS,GAAkB,IAAI,CAAA,CAAE,OAAO;QACxC,oBAAe,GAAW,CAAC,CAAA,CAAC,YAAY;QACxC,UAAK,GAAW,CAAC,CAAA,CAAC,OAAO;QACzB,UAAK,GAAiB,IAAI,CAAA,CAAC,MAAM;QACjC,WAAM,GAAmB,EAAE,CAAA,CAAC,MAAM;QAClC,cAAS,GAAY,KAAK,CAAA,CAAC,OAAO;QAClC,UAAK,GAAc,EAAE,CAAA,CAAC,WAAW;QACjC,oBAAe,GAAmC,EAAE,CAAA,CAAC,OAAO;QAE5D,WAAM,GAAW,CAAC,CAAA;QAClB,aAAQ,GAAQ,IAAI,CAAA;QACpB,aAAQ,GAAkB,IAAI,CAAA;QAC9B,WAAM,GAAe,EAAE,CAAA,CAAC,MAAM;QAE7B,eAAU,GAAW,CAAC,CAAC,CAAA,CAAC,MAAM;QAC9B,wBAAmB,GAAe,IAAI,CAAA,CAAC,QAAQ;QAGnD,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE,CAAA;IACxB,CAAC;IAEM,0BAAQ,GAAf;QACI,OAAO,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,gBAAgB,GAAG,IAAI,CAAC,WAAW,GAAG,cAAc,GAAG,IAAI,CAAC,SAAS,CAAA;IACnN,CAAC;IAEM,sBAAI,GAAX,UAAY,EAAU,EAAE,KAAW,EAAE,EAAW,EAAE,MAAe,EAAE,eAAwB;;QACvF,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,CAAA;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,mBAAS,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAA;QACnE,IAAI,CAAC,eAAe,GAAG,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,OAAA,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,oBAAO,CAAC,MAAM,EAAE,CAAC,0CAAE,eAAe,KAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAChJ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACvB,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,yBAAO,GAAd,UAAe,IAAS,EAAE,IAAY,EAAE,KAAa,EAAE,KAAc;;QACjE,eAAe;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACnB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QACjB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA;QAC9B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,GAAG,IAAI,mBAAS,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,uBAAa,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACpF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAA;QAC5B,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,EAAE,CAAA;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAA;QAC7C,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;QAC/B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC3B,IAAI,CAAC,KAAK,eAAG,IAAI,CAAC,EAAE,0CAAG,CAAC,oCAAK,IAAI,CAAC,KAAK,CAAA;QACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvC,IAAI,CAAC,SAAS,EAAE,CAAA;SACnB;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,uBAAK,GAAZ;;QACI,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAC1B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YACzB,SAAS,QAAE,IAAI,CAAC,SAAS,0CAAE,KAAK,EAAE;YAClC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;YAC5B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;YACrC,aAAa,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC;SAClD,CAAA;IACL,CAAC;IAED,sBAAW,0BAAK;aAAhB,cAAqB,OAAO,IAAI,CAAC,MAAM,CAAA,CAAC,CAAC;;;OAAA;IAEjC,0BAAQ,GAAhB,UAAiB,OAAiB;;QAC9B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QAC1D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,OAAA,IAAI,CAAC,QAAQ,0CAAE,YAAY,KAAI,CAAC,CAAC,CAAA;QACzE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QACxE,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,WAAW;IACJ,gCAAc,GAArB;;QACI,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAA;SACd;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,CAAA;QACtC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,qCAAiB,CAAC,WAAW,CAAC,wBAAwB,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;SAC/F;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,WAAW,GAAG,OAAA,IAAI,CAAC,QAAQ,0CAAE,YAAY,KAAI,CAAC,CAAA;QACnD,IAAI,CAAC,SAAS,GAAG,OAAA,IAAI,CAAC,QAAQ,0CAAE,UAAU,KAAI,CAAC,CAAA;QAC/C,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,sBAAY,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAA1B,CAA0B,CAAC,CAAA;QAC1F,IAAI,CAAC,MAAM,GAAG,oBAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACzB,CAAC;IAED,WAAW;IACJ,oCAAkB,GAAzB;;QACI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,iBAAiB,GAAG,OAAA,IAAI,CAAC,QAAQ,0CAAE,gBAAgB,KAAI,EAAE,CAAA;YAC7D,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBACf,iBAAiB,GAAG,OAAA,SAAS,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,0CAAE,gBAAgB,KAAI,iBAAiB,CAAA;aACvH;iBAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,iBAAiB,GAAG,OAAA,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,0CAAE,gBAAgB,KAAI,iBAAiB,CAAA;aAC5G;YACD,IAAI,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAA;SAC/F;QACD,OAAO,IAAI,CAAC,mBAAmB,CAAA;IACnC,CAAC;IAED,sCAAsC;IAC/B,8BAAY,GAAnB,UAAoB,GAAY;QAC5B,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3C,CAAC;IAEM,2BAAS,GAAhB,sBAAqB,OAAO,OAAA,IAAI,CAAC,SAAS,0CAAE,EAAE,KAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,CAAA,CAAC,CAAC;IACnE,8BAAY,GAAnB,cAAwB,OAAO,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA,CAAC,CAAC;IACzD,wBAAM,GAAb,cAAkB,OAAO,IAAI,CAAC,GAAG,CAAA,CAAC,CAAC;IAC5B,2BAAS,GAAhB,cAAqB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA,CAAC,CAAC;IAClD,0BAAQ,GAAf,cAAoB,OAAO,IAAI,CAAC,KAAK,CAAA,CAAC,CAAC;IAChC,0BAAQ,GAAf,UAAgB,KAAU,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA,CAAC,CAAC;IAC9C,6BAAW,GAAlB,cAAiC,OAAO,IAAI,CAAC,IAAI,CAAA,CAAC,CAAC;IAC5C,mCAAiB,GAAxB,sBAA6B,aAAO,IAAI,CAAC,SAAS,0CAAE,KAAK,CAAA,CAAC,CAAC;IACpD,gCAAc,GAArB,sBAA0B,OAAO,OAAA,IAAI,CAAC,SAAS,0CAAE,EAAE,KAAI,CAAC,CAAA,CAAC,CAAC;IAE1D,sBAAW,yBAAI;aAAf,sBAAoB,OAAO,OAAA,IAAI,CAAC,SAAS,0CAAE,WAAW,OAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAA,CAAC,CAAC;;;OAAA;IACxF,sBAAW,yBAAI;aAAf,sBAAoB,OAAO,OAAA,IAAI,CAAC,QAAQ,0CAAE,IAAI,KAAI,CAAC,CAAA,CAAC,CAAC;;;OAAA;IACrD,sBAAW,6BAAQ;aAAnB,cAAwB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA,CAAC,CAAC;;;OAAA;IAC7E,sBAAW,+BAAU;aAArB,sBAA0B,OAAO,OAAA,IAAI,CAAC,QAAQ,0CAAE,WAAW,KAAI,CAAC,CAAA,CAAC,CAAC;;;OAAA;IAClE,sBAAW,+BAAU;aAArB,sBAA0B,OAAO,OAAA,IAAI,CAAC,QAAQ,0CAAE,WAAW,KAAI,CAAC,CAAA,CAAC,CAAC,CAAC,IAAI;;;;OAAL;IAClE,sBAAW,+BAAU;aAArB,sBAA0B,aAAO,IAAI,CAAC,QAAQ,0CAAE,WAAW,CAAA,CAAC,CAAC,CAAC,SAAS;;;;OAAV;IAEtD,gCAAc,GAArB;QACI,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;IACxH,CAAC;IAEM,8BAAY,GAAnB;QACI,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;IACxD,CAAC;IAED,gBAAgB;IACT,iCAAe,GAAtB;;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAA;SACrC;QACD,OAAO,OAAA,IAAI,CAAC,QAAQ,0CAAE,QAAQ,KAAI,GAAG,CAAA;IACzC,CAAC;IAEM,+BAAa,GAApB;QACI,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAA;QACjD,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAa,CAAC,kBAAkB,CAAC,CAAA;QACnE,IAAI,KAAK,EAAE;YACP,IAAM,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAC/E,OAAO,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;SACpD;QACD,OAAO,MAAM,CAAA;IACjB,CAAC;IAEM,sCAAoB,GAA3B,UAA4B,KAAa;QACrC,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAa,CAAC,kBAAkB,CAAC,CAAA;QACnE,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,SAAS,GAAG,EAAE,CAAA;SACxB;QACD,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC7E,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;QACvC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAA;QAClE,OAAO,SAAS,GAAG,GAAG,GAAG,SAAS,CAAA;IACtC,CAAC;IAED,kBAAkB;IACX,yCAAuB,GAA9B;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAa,CAAC,kBAAkB,CAAC,CAAA;QACnE,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC,MAAM,CAAA;SACrB;QACD,IAAM,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC5E,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IACnC,CAAC;IAEM,6BAAW,GAAlB,UAAmB,MAAc,EAAE,cAAyB;QAA5D,iBA8DC;QA7DG,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE;gBAC3B,OAAM;aACT;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,qBAAqB,EAAE;gBACvH,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA,CAAC,UAAU;aAChD;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,gBAAgB,EAAE;gBACtF,cAAc,IAAI,CAAC,CAAC,KAAK,CAAA,CAAC,UAAU;aACvC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,kBAAkB,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,eAAe,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,eAAe,EAAE;gBACnQ,MAAM,IAAI,CAAC,CAAC,KAAK,CAAA,CAAC,OAAO;aAC5B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,EAAE,EAAE,OAAO;gBACpD,IAAI,aAAA,KAAI,CAAC,SAAS,0CAAE,KAAK,0CAAE,EAAE,MAAK,gBAAQ,CAAC,SAAS,EAAE;oBAClD,MAAM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;iBACpD;aACJ;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,EAAE,EAAE,QAAQ;gBAC5C,IAAI,aAAA,KAAI,CAAC,SAAS,0CAAE,KAAK,0CAAE,EAAE,MAAK,gBAAQ,CAAC,OAAO,EAAE;oBAChD,MAAM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;iBACpD;aACJ;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,MAAM,EAAE,EAAE,OAAO;gBAC5C,IAAM,CAAC,GAAG,OAAA,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,gBAAQ,CAAC,OAAO,CAAC,0CAAE,MAAM,KAAI,CAAC,CAAA;gBAChF,MAAM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;aAC1B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,WAAW,EAAE,EAAE,OAAO;gBACjD,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,eAAe,EAAE,EAAE,YAAY;gBAC1D,cAAc,IAAI,CAAC,CAAC,KAAK,CAAA;aAC5B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,mBAAmB,EAAE,EAAE,SAAS;gBAC3D,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,EAAE,EAAE,KAAK;gBACrD,IAAM,MAAM,GAAG,KAAI,CAAC,oBAAoB,CAAC,uBAAe,CAAC,iBAAiB,CAAC,CAAA;gBAC3E,IAAI,MAAM,EAAE;oBACR,IAAM,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAA;oBAC3D,cAAc,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAA;iBACjC;aACJ;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,EAAE,EAAE,OAAO;gBACvD,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,EAAE,EAAE,UAAU;gBACvD,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,YAAY,EAAE,EAAE,OAAO;gBAClD,MAAM,IAAI,CAAC,CAAC,KAAK,CAAA;aACpB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,MAAM,EAAE,EAAE,QAAQ;gBAC7C,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACrC;QACL,CAAC,CAAC,CAAA;QACF,KAAK;QACL,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YAClC,IAAM,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;YACnC,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;gBAC1D,MAAM,IAAI,CAAC,CAAC,KAAK,CAAA;aACpB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;gBAC7C,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACrC;SACJ;QACD,OAAO;QACP,IAAI,cAAc,GAAG,CAAC,EAAE;YACpB,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAA;SAChD;QACD,OAAO;QACP,IAAI,cAAc,GAAG,CAAC,EAAE;YACpB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAA;SACrE;QACD,OAAO,MAAM,CAAA;IACjB,CAAC;IAEM,8BAAY,GAAnB;QACI,OAAO,IAAI,CAAC,KAAK,CAAA;IACrB,CAAC;IAEM,0BAAQ,GAAf;QACI,OAAO,IAAI,CAAC,UAAU,EAAE,CAAA;IAC5B,CAAC;IAEM,4BAAU,GAAjB,UAAkB,cAAwC;QAA1D,iBA6CC;QA7CiB,+BAAA,EAAA,iBAA2B,gBAAQ,CAAC,IAAI;QACtD,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAA;QACnB,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,CAAA;QAClC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE;gBAC3B,OAAM;aACT;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,UAAU,EAAE;gBAC1E,UAAU,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA,CAAC,UAAU;aAC5C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,UAAU,EAAE;gBACvC,EAAE,IAAI,CAAC,CAAC,KAAK,CAAA;aAChB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,MAAM,EAAE,EAAE,OAAO;gBAC5C,IAAM,CAAC,GAAG,OAAA,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,gBAAQ,CAAC,OAAO,CAAC,0CAAE,MAAM,KAAI,CAAC,CAAA;gBAChF,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;aACtB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,EAAE,EAAE,OAAO;gBAC3C,IAAI,aAAA,KAAI,CAAC,SAAS,0CAAE,KAAK,0CAAE,EAAE,MAAK,gBAAQ,CAAC,OAAO,EAAE;oBAChD,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;iBAChD;aACJ;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,EAAE,EAAE,OAAO;gBACvD,UAAU,IAAI,GAAG,CAAA;aACpB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,EAAE,EAAE,UAAU;gBACvD,UAAU,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACjC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAQ,CAAC,UAAU,EAAE,EAAE,OAAO;gBAChD,UAAU,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACjC;QACL,CAAC,CAAC,CAAA;QACF,KAAK;QACL,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YAClC,IAAM,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;YACnC,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;gBAClB,EAAE,IAAI,CAAC,CAAC,KAAK,CAAA;aAChB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;gBACzB,EAAE,IAAI,CAAC,CAAC,MAAM,CAAA;aACjB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;gBACzB,UAAU,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACjC;SACJ;QACD,OAAO;QACP,IAAI,UAAU,GAAG,CAAC,EAAE;YAChB,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,UAAU,CAAC,CAAA;SACpC;QACD,OAAO;QACP,IAAI,UAAU,GAAG,CAAC,EAAE;YAChB,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,UAAU,CAAC,CAAC,CAAA;SACrD;QACD,OAAO,EAAE,CAAA;IACb,CAAC;IAEM,gCAAc,GAArB;;QACI,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE;YAC1B,OAAO,GAAG,CAAA;SACb;QACD,IAAM,KAAK,GAAG,OAAA,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,oBAAO,CAAC,MAAM,EAAE,CAAC,0CAAE,eAAe,KAAI,CAAC,CAAA;QACzF,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,OAAO,KAAK,CAAA;SACf;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAA;QAClC,IAAI,KAAK,GAAG,KAAK,EAAE;YACf,OAAU,KAAK,WAAK,KAAK,GAAG,KAAK,OAAG,CAAA;SACvC;QACD,OAAO,KAAK,GAAG,EAAE,CAAA;IACrB,CAAC;IAEM,0BAAQ,GAAf;QACI,OAAO,IAAI,CAAC,KAAK,CAAA;IACrB,CAAC;IACM,0BAAQ,GAAf,UAAgB,EAAU;QACtB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;IACnB,CAAC;IAED,OAAO;IACA,4BAAU,GAAjB,UAAkB,IAAS;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAA;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAA;IAC7B,CAAC;IAED,OAAO;IACA,wBAAM,GAAb,cAAkB,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC;IAC3C,SAAS;IACF,wBAAM,GAAb,cAAkB,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,CAAC,CAAA,CAAC,CAAC;IAC7H,OAAO;IACA,2BAAS,GAAhB,cAAqB,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,OAAO,CAAA,CAAC,CAAC;IAC5D,OAAO;IACA,4BAAU,GAAjB,cAAsB,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,CAAA,CAAC,CAAC;IAC3D,UAAU;IACH,6BAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,SAAS,CAAA,CAAC,CAAC;IAEhE,aAAa;IACN,mCAAiB,GAAxB;QACI,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACf,OAAO,kBAAkB,CAAA;SAC5B;aAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACtB,OAAO,kBAAkB,CAAA;SAC5B;QACD,OAAO,aAAa,CAAA;IACxB,CAAC;IAED,UAAU;IACH,gCAAc,GAArB;;QACI,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,gBAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAC,IAAI,CAAC,KAAK,0CAAE,EAAE,CAAA,CAAC,CAAA;IAChG,CAAC;IAED,OAAO;IACA,yBAAO,GAAd;;QACI,OAAO,QAAC,IAAI,CAAC,QAAQ,0CAAE,OAAO,CAAA,CAAA;IAClC,CAAC;IAED,UAAU;IACH,yBAAO,GAAd;QACI,OAAO,IAAI,CAAC,KAAK,KAAK,oBAAO,CAAC,MAAM,EAAE,CAAA;IAC1C,CAAC;IAED,QAAQ;IACD,6BAAW,GAAlB;;QACI,OAAO,OAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,KAAI,iBAAS,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;IACjE,CAAC;IAED,SAAS;IACF,+BAAa,GAApB;;QACI,IAAM,IAAI,eAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,0CAAE,WAAW,4CAAI,mBAAmB,EAAE,CAAA;QAC1F,aAAO,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,mBAAmB,CAAC,IAAI,CAAC,GAAG,oCAAK,CAAC,CAAC,CAAA;IACpD,CAAC;IAEM,2BAAS,GAAhB;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;QAC3C,OAAO,KAAK,GAAG,GAAG,GAAG,KAAK,CAAA;IAC9B,CAAC;IAEM,4BAAU,GAAjB;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC7B,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAC7C,CAAC;IAEM,6BAAW,GAAlB;;QACI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;YACrB,OAAO,CAAC,CAAA;SACX;QACD,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,YAAY;QACZ,IAAI,OAAA,IAAI,CAAC,iBAAiB,EAAE,0CAAE,EAAE,MAAK,gBAAQ,CAAC,OAAO,EAAE;YACnD,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAA;SAC7C;QACD,UAAU;QACV,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE;YACjD,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAA;SACvC;QACD,OAAO,QAAQ,CAAA;IACnB,CAAC;IAEM,6BAAW,GAAlB;QACI,OAAO,IAAI,CAAC,QAAQ,CAAA;IACxB,CAAC;IAEM,8BAAY,GAAnB;QACI,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;IAC/E,CAAC;IAEM,+BAAa,GAApB;QACI,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IACrE,CAAC;IAEM,uBAAK,GAAZ;QACI,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;IAC5C,CAAC;IAEM,0BAAQ,GAAf;;QACI,OAAO,OAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,KAAI,iBAAS,CAAC,IAAI,CAAA;IAC7C,CAAC;IAED,OAAO;IACA,6BAAW,GAAlB,UAAmB,KAAgB,EAAE,IAAU,EAAE,IAAc;QAC3D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,IAAI,CAAC,KAAK,GAAG,IAAI,sBAAY,EAAE,CAAA;SAClC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAC5B,2EAA2E;QAC3E,QAAQ;QACR,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YAC9B,IAAI,CAAC,SAAS,EAAE,CAAA;YAChB,IAAI,CAAC,aAAa,EAAE,CAAA;YACpB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;SAClC;IACL,CAAC;IAED,OAAO;IACA,8BAAY,GAAnB,UAAoB,IAAS;;QACzB,IAAI,OAAA,IAAI,CAAC,SAAS,0CAAE,EAAE,MAAK,IAAI,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA,CAAC,YAAY;YAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,uBAAa,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAClD,IAAI,CAAC,UAAU,EAAE,CAAA;YACjB,IAAI,CAAC,SAAS,EAAE,CAAA;YAChB,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;SAC1D;IACL,CAAC;IAED,SAAS;IACF,mCAAiB,GAAxB,UAAyB,GAAW;QAChC,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,CAAA;QAChC,IAAI,GAAG,GAAG,CAAC,EAAE;YACT,GAAG,GAAG,CAAC,CAAA;SACV;aAAM,IAAI,GAAG,GAAG,CAAC,EAAE;YAChB,GAAG,GAAG,CAAC,CAAA;SACV;QACD,IAAI,CAAC,WAAW,GAAG,GAAG,CAAA;IAC1B,CAAC;IACM,gCAAc,GAArB,UAAsB,GAAW;QAC7B,IAAI,CAAC,WAAW,GAAG,GAAG,CAAA;IAC1B,CAAC;IAEM,4BAAU,GAAjB,UAAkB,MAAc;QAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;YACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;YACpB,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;SACrD;IACL,CAAC;IAED,OAAO;IACA,6BAAW,GAAlB,UAAmB,IAAS,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAChD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;YACnC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAChD,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;SAChE;IACL,CAAC;IAED,SAAS;IACF,iCAAe,GAAtB,UAAuB,GAAW,EAAE,KAAiB;QACjD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;SACzB;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;YAC/B,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBACrE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;aAC5B;iBAAM;gBACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;aACrB;SACJ;QACD,IAAI,CAAC,UAAU,EAAE,CAAA;IACrB,CAAC;IAED,SAAS;IACF,gCAAc,GAArB,UAAsB,EAAU,EAAE,KAAY;;QAC1C,IAAI,OAAA,IAAI,CAAC,SAAS,0CAAE,EAAE,MAAK,EAAE,IAAI,CAAC,KAAK,EAAE;YACrC,OAAM;SACT;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAC7B,IAAI,CAAC,UAAU,EAAE,CAAA;IACrB,CAAC;IAED,6BAA6B;IAC7B,8BAA8B;IACvB,4BAAU,GAAjB,UAAkB,IAAc;QAAhC,iBAwEC;;QAvEG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC7B,OAAM;SACT;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAA;QACjK,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAM,MAAM,GAAG,oBAAO,CAAC,eAAe,EAAE,CAAA;QACxC,IAAI,UAAU,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,CAAA;QACtC,QAAQ;QACR,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;YAC5B,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,eAAe,EAAE,EAAE,MAAM;gBACpD,KAAI,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE,CAAA;aAC7B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,WAAW,EAAE,EAAE,MAAM;gBACvD,KAAI,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,CAAA;aACzB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,YAAY,EAAE,EAAE,cAAc;gBAChE,KAAI,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG,MAAM,CAAA;aACjC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,gBAAgB,EAAE,EAAE,cAAc;gBACpE,KAAI,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,GAAG,MAAM,CAAA;aAClC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,gBAAgB,EAAE,EAAE,QAAQ;gBAC9D,UAAU,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACjC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,cAAc,EAAE,EAAE,QAAQ;gBAC5D,cAAc,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,SAAS,EAAE,EAAE,SAAS;gBACxD,KAAI,CAAC,WAAW,IAAI,CAAC,CAAA;aACxB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,uBAAe,CAAC,cAAc,EAAE,EAAE,SAAS;gBAC7D,KAAI,CAAC,SAAS,IAAI,CAAC,CAAA;aACtB;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QAC3B,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;QAChC,OAAO;QACP,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAA;YAC/B,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAA;YACpC,YAAY;YACZ,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,KAAK,0CAAE,EAAE,MAAK,gBAAQ,CAAC,WAAW,EAAE;gBACnD,cAAc,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAA;aACtD;SACJ;QACD,OAAO;QACP,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE;YAC1B,IAAM,GAAG,GAAG,OAAA,IAAI,CAAC,cAAc,CAAC,qBAAa,CAAC,KAAK,CAAC,0CAAE,KAAK,KAAI,CAAC,CAAA;YAChE,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAA;YACnD,IAAI,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,CAAA;SAC3C;QACD,UAAU;QACV,IAAI,UAAU,GAAG,CAAC,EAAE;YAChB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,CAAA;SACpD;QACD,UAAU;QACV,IAAI,cAAc,GAAG,CAAC,EAAE;YACpB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,CAAA;SAC1D;QACD,UAAU;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;YACrB,IAAA,KAAA,OAAa,IAAI,CAAC,KAAK,CAAC,cAAc,IAAA,EAArC,IAAE,QAAA,EAAE,IAAI,QAA6B,CAAA;YAC5C,MAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,IAAE,EAAf,CAAe,CAAC,0CAAE,gBAAgB,CAAC,IAAI,EAAC;SACjE;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAArB,CAAqB,CAAC,CAAA;SAClD;QACD,iBAAiB;QACjB,UAAI,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,0CAAE,eAAe,IAAI;YACjE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;SAC/B;aAAM,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;SAC1D;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;SACrD;IACL,CAAC;IAED,SAAS;IACF,gCAAc,GAArB,UAAsB,IAAmB;QACrC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC,CAAA;IACjD,CAAC;IAED,SAAS;IACF,gCAAc,GAArB;QACI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAa,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAa,CAAC,kBAAkB,EAAvG,CAAuG,CAAC,CAAA,CAAC,aAAa;IACvJ,CAAC;IAEM,0BAAQ,GAAf,UAAgB,KAAY;QACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,iBAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAxB,CAAwB,CAAC,CAAA;IACzD,CAAC;IAEM,0BAAQ,GAAf,UAAgB,KAAY;QAA5B,iBAaC;QAZG,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE;YACf,IAAM,SAAO,GAA+B,EAAE,CAAA;YAC9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,SAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAnB,CAAmB,CAAC,CAAA;YAC5C,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBACX,IAAM,IAAI,GAAG,SAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;gBAC5B,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;iBAClB;qBAAM;oBACH,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,iBAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;iBAC5C;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAEM,2BAAS,GAAhB,UAAiB,IAAc;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC,CAAA;IAChD,CAAC;IAEM,8BAAY,GAAnB,UAAoB,IAAc;;QAC9B,OAAO,OAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC,0CAAE,KAAK,KAAI,CAAC,CAAA;IAC5D,CAAC;IAEM,+BAAa,GAApB;QACI,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC7B,CAAC;IAEM,4BAAU,GAAjB;QACI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;IACzB,CAAC;IAED,QAAQ;IACD,gCAAc,GAArB;QACI,IAAI,GAAG,GAAG,CAAC,CAAA;QACX,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACjB,GAAG,IAAI,CAAC,CAAC,KAAK,CAAA;aACjB;QACL,CAAC,CAAC,CAAA;QACF,OAAO,GAAG,CAAA;IACd,CAAC;IAEM,oCAAkB,GAAzB;QACI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;IAC7B,CAAC;IAEM,iCAAe,GAAtB,UAAuB,QAAqB;QACxC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAA;IAClD,CAAC;IAEM,iCAAe,GAAtB,UAAuB,EAAU;QAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAA;IACnC,CAAC;IAED,SAAS;IACF,kCAAgB,GAAvB,UAAwB,EAAU;;QAC9B,OAAO,OAAA,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,0CAAE,KAAK,KAAI,CAAC,CAAA;IAC/C,CAAC;IAED,UAAU;IACH,gCAAc,GAArB;QAAA,iBAEC;QAFqB,aAAgB;aAAhB,UAAgB,EAAhB,qBAAgB,EAAhB,IAAgB;YAAhB,wBAAgB;;QAClC,OAAO,GAAG,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAzB,CAAyB,CAAC,CAAA;IACnD,CAAC;IAEM,+BAAa,GAApB;QACI,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;IAClD,CAAC;IAEM,2BAAS,GAAhB;;QACI,IAAI,CAAC,QAAQ,GAAG,OAAA,IAAI,CAAC,QAAQ,0CAAE,UAAU,KAAI,CAAC,CAAA;QAC9C,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;YACnB,KAAK;YACL,IAAI,OAAA,IAAI,CAAC,iBAAiB,EAAE,0CAAE,EAAE,MAAK,gBAAQ,CAAC,OAAO,EAAE;gBACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAA;aAClD;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEM,4BAAU,GAAjB;QACI,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;IAC5B,CAAC;IAED,WAAW;IACJ,iCAAe,GAAtB;;QACI,aAAO,IAAI,CAAC,KAAK,0CAAE,OAAO,CAAA;IAC9B,CAAC;IAED,WAAW;IACJ,sCAAoB,GAA3B,UAA4B,IAAqB;;QAC7C,aAAO,IAAI,CAAC,KAAK,0CAAE,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,EAAC;IACzD,CAAC;IAED,OAAO;IACA,iCAAe,GAAtB,UAAuB,SAAgB;QAAvC,iBAEC;QADG,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,oBAAO,CAAC,mBAAmB,CAAC,CAAC,EAAE,KAAI,CAAC,MAAM,EAAE,KAAI,CAAC,OAAO,EAAE,KAAI,CAAC,GAAG,CAAC,EAAnE,CAAmE,CAAC,CAAA;IACpH,CAAC;IACL,cAAC;AAAD,CAhuBA,AAguBC,IAAA", "file": "", "sourceRoot": "/", "sourcesContent": ["import { TreasureInfo } from \"../../common/constant/DataType\"\nimport { BuffType, EquipEffectType, HeroType, PawnSkillType, PawnState, PawnType } from \"../../common/constant/Enums\"\nimport { PawnAttrJItem } from \"../../common/constant/JsonType\"\nimport EventType from \"../../common/event/EventType\"\nimport { errorReportHelper } from \"../../common/helper/ErrorReportHelper\"\nimport { gameHpr } from \"../../common/helper/GameHelper\"\nimport CTypeObj from \"../common/CTypeObj\"\nimport PortrayalInfo from \"../common/PortrayalInfo\"\nimport StrategyObj from \"../common/StrategyObj\"\nimport EquipInfo from \"../main/EquipInfo\"\nimport BuffObj from \"./BuffObj\"\nimport PawnSkillObj from \"./PawnSkillObj\"\nimport PawnStateObj from \"./PawnStateObj\"\n\n// 一个士兵\nexport default class PawnObj {\n\n    public aIndex: number = 0 //所属哪个区域\n    public enterDir: number = -1\n    public uid: string = ''\n    public cuid: string = '' //客户端uid\n    public armyUid: string = '' //所属队伍uid\n    public armyName: string = '' //军队名字\n    public owner: string = ''\n    public recordDataMap: { [key: string]: number } = {} //记录\n    public treasures: TreasureInfo[] = [] //当前宝箱个数\n\n    public id: number = 0\n    public point: cc.Vec2 = cc.v2()\n    public lv: number = 0 //等级\n    public skinId: number = 0 //皮肤id\n    public curHp: number = 0 //当前血量\n    public maxHp: number = 0\n    public curAnger: number = 0 //怒气\n    public maxAnger: number = 0\n    public attack: number = 0\n    public attackRange: number = 0 //攻击范围\n    public moveRange: number = 0 //移动范围\n    public attackSpeed: number = 0 //当前出手速度\n    public equip: EquipInfo = null //当前携带的装备\n    public portrayal: PortrayalInfo = null  //携带的画像\n    public rodeleroCadetLv: number = 0 //当前 见习勇者 层数\n    public petId: number = 0 //携带的宠物\n    public state: PawnStateObj = null //当前状态\n    public skills: PawnSkillObj[] = [] //技能列表\n    public actioning: boolean = false //是否行动中\n    public buffs: BuffObj[] = [] //当前的buff列表\n    public strategyBuffMap: { [key: number]: StrategyObj } = {} //韬略map\n\n    public attrId: number = 0\n    public baseJson: any = null\n    public attrJson: PawnAttrJItem = null\n    public upCost: CTypeObj[] = [] //升级费用\n\n    private tempCurrHp: number = -1 //用于记录\n    private tempAttackAnimTimes: number[][] = null //攻击动画时间\n\n    constructor() {\n        this.cuid = ut.UID()\n    }\n\n    public toString() {\n        return \"uid:\" + this.uid + \", point:\" + this.point.Join(\",\") + \", hp:\" + this.curHp + \"/\" + this.getMaxHp() + \", attack:\" + this.attack + \", attackRange:\" + this.attackRange + \", moveRange:\" + this.moveRange\n    }\n\n    public init(id: number, equip?: any, lv?: number, skinId?: number, rodeleroCadetLv?: number) {\n        this.id = id\n        this.lv = lv || 1\n        this.skinId = skinId || 0\n        this.equip = new EquipInfo().fromSvr(equip || { id: 0, attrs: [] })\n        this.rodeleroCadetLv = rodeleroCadetLv ?? (this.id === 3205 ? (gameHpr.getPlayerInfo(this.owner || gameHpr.getUid())?.rodeleroCadetLv || 0) : 0)\n        this.tempCurrHp = -1\n        this.initJson()\n        this.curHp = this.maxHp\n        return this\n    }\n\n    public fromSvr(data: any, auid: string, owner: string, aname?: string) {\n        // cc.log(data)\n        this.aIndex = data.index\n        this.uid = data.uid\n        this.id = data.id\n        this.lv = data.lv || 1\n        this.skinId = data.skinId || 0\n        this.point.set(data.point)\n        this.armyUid = auid\n        this.attackSpeed = data.attackSpeed || 0\n        this.equip = new EquipInfo().fromSvr(data.equip || { id: 0, attrs: [] })\n        this.portrayal = data.portrayal ? new PortrayalInfo().fromSvr(data.portrayal) : null\n        this.rodeleroCadetLv = data.rodeleroCadetLv || 0\n        this.petId = data.petId || 0\n        this.armyName = aname || ''\n        this.owner = owner\n        this.recordDataMap = data.recordDataMap || {}\n        this.tempCurrHp = -1\n        this.updateTreasures(data.treasures)\n        this.setBuffs(data.buffs || [])\n        this.strategyBuffMap = {}\n        this.initJson(data.isFight)\n        this.curHp = data.hp?.[0] ?? this.maxHp\n        this.curAnger = data.curAnger\n        if (!this.curAnger && !this.isBattleing()) {\n            this.initAnger()\n        }\n        return this\n    }\n\n    public strip() {\n        return {\n            index: this.aIndex,\n            armyUid: this.armyUid,\n            uid: this.uid,\n            skinId: this.skinId,\n            point: this.point.toJson(),\n            id: this.id,\n            lv: this.lv,\n            curAnger: this.curAnger,\n            attackSpeed: this.attackSpeed,\n            equip: this.equip.strip(),\n            portrayal: this.portrayal?.strip(),\n            rodeleroCadetLv: this.rodeleroCadetLv,\n            petId: this.petId,\n            treasures: ut.deepClone(this.treasures),\n            hp: [this.curHp, this.maxHp],\n            buffs: this.buffs.map(m => m.strip()),\n            recordDataMap: ut.deepClone(this.recordDataMap)\n        }\n    }\n\n    public get index() { return this.aIndex }\n\n    private initJson(isFight?: boolean) {\n        this.baseJson = assetsMgr.getJsonData('pawnBase', this.id)\n        this.attackSpeed = this.attackSpeed || (this.baseJson?.attack_speed || 9)\n        this.changeState(isFight ? PawnState.STAND : PawnState.NONE, null, true)\n        this.updateAttrJson()\n    }\n\n    // 刷新属性json\n    public updateAttrJson() {\n        if (this.isMachine()) {\n            this.lv = 1\n        }\n        this.attrId = this.id * 1000 + this.lv\n        this.attrJson = assetsMgr.getJsonData('pawnAttr', this.attrId)\n        if (!this.attrJson) {\n            return errorReportHelper.reportError('PawnObj.updateAttrJson', { id: this.id, lv: this.lv })\n        }\n        this.maxHp = this.attrJson.hp || 0\n        this.maxAnger = this.attrJson.anger || 0\n        this.attack = this.attrJson.attack || 0\n        this.attackRange = this.attrJson?.attack_range || 0\n        this.moveRange = this.attrJson?.move_range || 0\n        this.skills = ut.stringToNumbers(this.attrJson.skill).map(m => new PawnSkillObj().init(m))\n        this.upCost = gameHpr.getPawnCost(this.id, this.lv, this.attrJson)\n        this.updateAttr(true)\n    }\n\n    // 获取攻击动画时间\n    public getAttackAnimTimes() {\n        if (!this.tempAttackAnimTimes) {\n            let attackAnimTimeStr = this.attrJson?.attack_anim_time || ''\n            if (this.isHero()) {\n                attackAnimTimeStr = assetsMgr.getJsonData('portrayalBase', this.portrayal.id)?.attack_anim_time || attackAnimTimeStr\n            } else if (this.skinId > 0) {\n                attackAnimTimeStr = assetsMgr.getJsonData('pawnSkin', this.skinId)?.attack_anim_time || attackAnimTimeStr\n            }\n            this.tempAttackAnimTimes = attackAnimTimeStr.split('|').map(m => ut.stringToNumbers(m, ','))\n        }\n        return this.tempAttackAnimTimes\n    }\n\n    // 记录当前的血量 用于打开士兵面板时 切换装备的时候可以记录一开始的血量\n    public recordCurrHp(val: boolean) {\n        this.tempCurrHp = val ? this.curHp : -1\n    }\n\n    public getViewId() { return this.portrayal?.id || this.skinId || this.id }\n    public getPrefabUrl() { return 'pawn/PAWN_' + this.getViewId() }\n    public getUid() { return this.uid }\n    public getAbsUid() { return this.uid + this.getViewId() }\n    public getPoint() { return this.point }\n    public setPoint(point: any) { this.point.set(point) }\n    public getPawnType(): PawnType { return this.type }\n    public getPortrayalSkill() { return this.portrayal?.skill }\n    public getPortrayalId() { return this.portrayal?.id || 0 }\n\n    public get name() { return this.portrayal?.getChatName() || 'pawnText.name_' + this.id }\n    public get type() { return this.baseJson?.type || 0 }\n    public get typeName() { return this.type ? 'ui.pawn_type_' + this.type : '' }\n    public get marchSpeed() { return this.baseJson?.march_speed || 0 }\n    public get cerealCost() { return this.baseJson?.cereal_cost || 1 } //粮耗\n    public get behaviorId() { return this.attrJson?.behavior_id } //行为树配置id\n\n    public getAttackRange() {\n        return this.attackRange + this.getStrategyValue(40101) + this.getStrategyValue(40302) + this.getStrategyValue(50013)\n    }\n\n    public getMoveRange() {\n        return this.moveRange + this.getStrategyValue(31901)\n    }\n\n    // 获取移动速度 每秒移动距离\n    public getMoveVelocity() {\n        if (this.portrayal) {\n            return this.portrayal.moveVelocity\n        }\n        return this.baseJson?.velocity || 100\n    }\n\n    public getAttackText() {\n        const attack = this.amendAttack(this.attack) + ''\n        const skill = this.getSkillByType(PawnSkillType.INSTABILITY_ATTACK)\n        if (skill) {\n            const maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack)\n            return attack + '-' + this.amendAttack(maxAttack)\n        }\n        return attack\n    }\n\n    public getAttackTextByIndex(index: number) {\n        let minAttack = this.amendAttack(this.attack)\n        const skill = this.getSkillByType(PawnSkillType.INSTABILITY_ATTACK)\n        if (!skill) {\n            return minAttack + ''\n        }\n        let maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack)\n        maxAttack = this.amendAttack(maxAttack)\n        minAttack += (Math.round((maxAttack - minAttack) / 3) * index + 1)\n        return minAttack + '-' + maxAttack\n    }\n\n    // 获取最大攻击力 目前只用于陌刀\n    public getInstabilityMaxAttack() {\n        const skill = this.getSkillByType(PawnSkillType.INSTABILITY_ATTACK)\n        if (!skill) {\n            return this.attack\n        }\n        const attack = skill.value + Math.max(0, this.attack - this.attrJson.attack)\n        return this.amendAttack(attack)\n    }\n\n    public amendAttack(attack: number, ignoreBuffType?: BuffType) {\n        let addAttackRatio = 0, lowAttackRatio = 0\n        this.buffs.forEach(m => {\n            if (m.type === ignoreBuffType) {\n                return\n            } else if (m.type === BuffType.ATTACK_HALO || m.type === BuffType.LV_1_POWER || m.type === BuffType.LONGYUAN_SWORD_ATTACK) {\n                addAttackRatio += (m.value * 0.01) //提升攻击力百分比\n            } else if (m.type === BuffType.LOW_HP_ADD_ATTACK || m.type === BuffType.KUROU_ADD_ATTACK) {\n                addAttackRatio += m.value //提升攻击力百分比\n            } else if (m.type === BuffType.ADD_ATTACK || m.type === BuffType.INSPIRE || m.type === BuffType.WORTHY_MONARCH || m.type === BuffType.ADD_EXECUTE_ATTACK || m.type === BuffType.KILL_ADD_ATTACK || m.type === BuffType.GOD_WAR || m.type === BuffType.DDIE_ADD_ATTACK) {\n                attack += m.value //增加攻击力\n            } else if (m.type === BuffType.WISDOM_COURAGE) { //姜维 智勇\n                if (this.portrayal?.skill?.id === HeroType.JIANG_WEI) {\n                    attack += (m.value * this.portrayal.skill.target)\n                }\n            } else if (m.type === BuffType.VALOR) { //李嗣业 勇猛\n                if (this.portrayal?.skill?.id === HeroType.LI_SIYE) {\n                    attack += (m.value * this.portrayal.skill.target)\n                }\n            } else if (m.type === BuffType.MORALE) { //曹操 士气\n                const v = assetsMgr.getJsonData('portrayalSkill', HeroType.CAO_CAO)?.target || 0\n                attack += (m.value * v)\n            } else if (m.type === BuffType.TIGER_MANIA) { //许褚 虎痴\n                addAttackRatio += (m.value * 0.01)\n            } else if (m.type === BuffType.DESTROY_WEAPONS) { //摧毁武器 降低攻击力\n                lowAttackRatio += m.value\n            } else if (m.type === BuffType.LOW_HP_ADD_ATTACK_S) { //韬略 加攻击力\n                addAttackRatio += (m.value * 0.01)\n            } else if (m.type === BuffType.THOUSAND_UMBRELLA) { //千机伞\n                const effect = this.getEquipEffectByType(EquipEffectType.THOUSAND_UMBRELLA)\n                if (effect) {\n                    const val = m.value === 0 ? effect.value * 2 : effect.value\n                    addAttackRatio += (val * 0.01)\n                }\n            } else if (m.type === BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵\n                addAttackRatio += (m.value * 0.01)\n            } else if (m.type === BuffType.FEED_INTENSIFY) { //养由基 投喂强化\n                addAttackRatio += (m.value * 0.06)\n            } else if (m.type === BuffType.COURAGEOUSLY) { //典韦 奋勇\n                attack += m.value\n            } else if (m.type === BuffType.KERIAN) { //辛弃疾 金戈\n                addAttackRatio += (m.value * 0.01)\n            }\n        })\n        // 韬略\n        for (let key in this.strategyBuffMap) {\n            const s = this.strategyBuffMap[key]\n            if (s.type === 20001 || s.type === 40301 || s.type === 50010) {\n                attack += s.value\n            } else if (s.type === 20003 || s.type === 50029) {\n                addAttackRatio += (s.value * 0.01)\n            }\n        }\n        // 提升比列\n        if (addAttackRatio > 0) {\n            attack += Math.round(attack * addAttackRatio)\n        }\n        // 降低比列\n        if (lowAttackRatio > 0) {\n            attack = Math.max(0, attack - Math.round(attack * lowAttackRatio))\n        }\n        return attack\n    }\n\n    public getInitMaxHp() {\n        return this.maxHp\n    }\n\n    public getMaxHp() {\n        return this.amendMaxHp()\n    }\n\n    public amendMaxHp(ignoreBuffType: BuffType = BuffType.NONE) {\n        let hp = this.maxHp\n        let addHpRatio = 0, lowHpRatio = 0\n        this.buffs.forEach(m => {\n            if (m.type === ignoreBuffType) {\n                return\n            } else if (m.type === BuffType.DEFEND_HALO || m.type === BuffType.LV_1_POWER) {\n                addHpRatio += (m.value * 0.01) //提升攻击力百分比\n            } else if (m.type === BuffType.ADD_MAX_HP) {\n                hp += m.value\n            } else if (m.type === BuffType.MORALE) { //曹操 士气\n                const v = assetsMgr.getJsonData('portrayalSkill', HeroType.CAO_CAO)?.params || 0\n                hp += (m.value * v)\n            } else if (m.type === BuffType.TOUGH) { //曹仁 坚韧\n                if (this.portrayal?.skill?.id === HeroType.CAO_REN) {\n                    hp += (m.value * this.portrayal.skill.target)\n                }\n            } else if (m.type === BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵\n                addHpRatio += 0.1\n            } else if (m.type === BuffType.FEED_INTENSIFY) { //养由基 投喂强化\n                addHpRatio += (m.value * 0.04)\n            } else if (m.type === BuffType.TYRANNICAL) { //董卓 暴虐\n                lowHpRatio += (m.value * 0.04)\n            }\n        })\n        // 韬略\n        for (let key in this.strategyBuffMap) {\n            const s = this.strategyBuffMap[key]\n            if (s.type === 20002) {\n                hp += s.value\n            } else if (s.type === 50010) {\n                hp += s.params\n            } else if (s.type === 20004) {\n                addHpRatio += (s.value * 0.01)\n            }\n        }\n        // 提升比列\n        if (addHpRatio > 0) {\n            hp += Math.round(hp * addHpRatio)\n        }\n        // 降低比列\n        if (lowHpRatio > 0) {\n            hp = Math.max(1, hp - Math.round(hp * lowHpRatio))\n        }\n        return hp\n    }\n\n    public getCadetLvText() {\n        if (this.rodeleroCadetLv < 0) {\n            return '0'\n        }\n        const actLv = gameHpr.getPlayerInfo(this.owner || gameHpr.getUid())?.rodeleroCadetLv || 0\n        if (!this.uid) {\n            return actLv\n        }\n        const curLv = this.rodeleroCadetLv\n        if (actLv > curLv) {\n            return `${curLv}(+${actLv - curLv})`\n        }\n        return actLv + ''\n    }\n\n    public getPetId() {\n        return this.petId\n    }\n    public setPetId(id: number) {\n        this.petId = id\n    }\n\n    // 改变军队\n    public changeArmy(data: any) {\n        this.armyUid = data.uid\n        this.armyName = data.name\n    }\n\n    // 是否英雄\n    public isHero() { return !!this.portrayal }\n    // 是否boss\n    public isBoss() { return (this.type === PawnType.BEAST || this.type === PawnType.CATERAN) && this.attrJson.move_range === 0 }\n    // 是否器械\n    public isMachine() { return this.type === PawnType.MACHINE }\n    // 是否建筑\n    public isBuilding() { return this.type === PawnType.BUILD }\n    // 是否非战斗单位\n    public isNoncombat() { return this.type === PawnType.NONCOMBAT }\n\n    // 获取血条预制体url\n    public getHPBarPrefabUrl() {\n        if (this.isHero()) {\n            return 'pawn/HERO_HP_BAR'\n        } else if (this.isBoss()) {\n            return 'pawn/BOSS_HP_BAR'\n        }\n        return 'pawn/HP_BAR'\n    }\n\n    // 是否可以穿装备\n    public isCanWearEquip() {\n        return (this.type < PawnType.MACHINE || this.isBoss()) && (!!this.owner || !!this.equip?.id)\n    }\n\n    // 是否满级\n    public isMaxLv() {\n        return !this.attrJson?.lv_cost\n    }\n\n    // 是否自己的士兵\n    public isOwner() {\n        return this.owner === gameHpr.getUid()\n    }\n\n    // 是否战斗中\n    public isBattleing() {\n        return this.state?.type >= PawnState.STAND || this.aIndex < 0\n    }\n\n    // 获取战斗阵营\n    public getBattleCamp() {\n        const ctrl = gameHpr.areaCenter.getArea(this.aIndex)?.getFspModel()?.getBattleController()\n        return ctrl?.getFighterCampIndex(this.uid) ?? -1\n    }\n\n    public getHpText() {\n        const maxHp = this.getMaxHp()\n        const curHp = this.uid ? this.curHp : maxHp\n        return curHp + '/' + maxHp\n    }\n\n    public getHpRatio() {\n        const maxHp = this.getMaxHp()\n        return maxHp > 0 ? this.curHp / maxHp : 0\n    }\n\n    public getMaxAnger() {\n        if (this.maxAnger === 0) {\n            return 0\n        }\n        let maxAnger = this.maxAnger\n        // 吕蒙 -50%怒气\n        if (this.getPortrayalSkill()?.id === HeroType.LV_MENG) {\n            maxAnger = Math.round(this.maxAnger * 0.5)\n        }\n        // 韬略 怒气-1\n        if (this.isHasStrategys(40102, 40201, 40303, 40401)) {\n            maxAnger = Math.max(1, maxAnger - 1)\n        }\n        return maxAnger\n    }\n\n    public getCurAnger() {\n        return this.curAnger\n    }\n\n    public getAngerText() {\n        return this.maxAnger > 0 ? this.curAnger + '/' + this.getMaxAnger() : '0/0'\n    }\n\n    public getAngerRatio() {\n        return this.maxAnger > 0 ? this.curAnger / this.getMaxAnger() : 0\n    }\n\n    public isDie() {\n        return this.curHp <= 0 && this.maxHp > 0\n    }\n\n    public getState() {\n        return this.state?.type || PawnState.NONE\n    }\n\n    // 改变状态\n    public changeState(state: PawnState, data?: any, init?: boolean) {\n        if (!this.state) {\n            this.state = new PawnStateObj()\n        }\n        this.state.init(state, data)\n        // cc.log('changeState', this.uid, this.point.ID(), PawnState[state], data)\n        // 非战斗状态\n        if (!init && !this.isBattleing()) {\n            this.initAnger()\n            this.cleanAllBuffs()\n            this.tempAttackAnimTimes = null\n        }\n    }\n\n    // 设置画像\n    public setPortrayal(data: any) {\n        if (this.portrayal?.id !== data.id) {\n            this.skinId = 0 //有画像就一定没有皮肤\n            this.portrayal = new PortrayalInfo().fromSvr(data)\n            this.updateAttr()\n            this.initAnger()\n            eventCenter.emit(EventType.CHANGE_PAWN_PORTRAYAL, this)\n        }\n    }\n\n    // 改变攻击速度\n    public changeAttackSpeed(val: number) {\n        let num = this.attackSpeed + val\n        if (num < 1) {\n            num = 9\n        } else if (num > 9) {\n            num = 1\n        }\n        this.attackSpeed = num\n    }\n    public setAttackSpeed(val: number) {\n        this.attackSpeed = val\n    }\n\n    public changeSkin(skinId: number) {\n        if (this.skinId !== skinId) {\n            this.skinId = skinId\n            eventCenter.emit(EventType.CHANGE_PAWN_SKIN, this)\n        }\n    }\n\n    // 切换装备\n    public changeEquip(data: any, isEmit: boolean = true) {\n        if (this.equip.uid !== data.uid) {\n            this.equip.setId(data.uid, data.id)\n            this.updateEquipAttr(this.equip.uid, data.attrs)\n            isEmit && eventCenter.emit(EventType.CHANGE_PAWN_EQUIP, this)\n        }\n    }\n\n    // 刷新装备信息\n    public updateEquipAttr(uid: string, attrs: number[][]) {\n        if (!this.equip.uid || !attrs) {\n            this.equip.setAttr([])\n        } else if (this.equip.uid === uid) {\n            // 如果是专属 检测自己是否可以携带\n            if (!this.equip.isExclusive() || this.equip.checkExclusivePawn(this.id)) {\n                this.equip.setAttr(attrs)\n            } else {\n                this.equip.clean()\n            }\n        }\n        this.updateAttr()\n    }\n\n    // 刷新英雄信息\n    public updateHeroAttr(id: number, attrs: any[]) {\n        if (this.portrayal?.id !== id || !attrs) {\n            return\n        }\n        this.portrayal.setAttr(attrs)\n        this.updateAttr()\n    }\n\n    // 刷新属性 这个只是前端模拟 还是需要以服务器数据为准\n    // 一般如果在场景没有战斗并改变属性的时候可以前端自行计算\n    public updateAttr(init?: boolean) {\n        if (!init && this.isBattleing()) {\n            return\n        }\n        const maxHp = this.attrJson.hp || 0, attack = this.attrJson.attack || 0, attackRange = this.attrJson.attack_range || 0, moveRange = this.attrJson.move_range || 0\n        this.maxHp = maxHp\n        this.attack = attack\n        this.attackRange = attackRange\n        this.moveRange = moveRange\n        const runDay = gameHpr.getServerRunDay()\n        let addHpRatio = 0, addAttackRatio = 0\n        // 加上装备的\n        this.getEquipEffects().forEach(m => {\n            if (m.type === EquipEffectType.MINGGUANG_ARMOR) { //提高生命\n                this.maxHp += m.value * 10\n            } else if (m.type === EquipEffectType.BAIBI_SWORD) { //提高攻击\n                this.attack += m.value\n            } else if (m.type === EquipEffectType.TODAY_ADD_HP) { //根据服务器运行时间加生命\n                this.maxHp += m.value * runDay\n            } else if (m.type === EquipEffectType.TODAY_ADD_ATTACK) { //根据服务器运行时间加攻击\n                this.attack += m.value * runDay\n            } else if (m.type === EquipEffectType.CENTERING_HELMET) { //提高生命比例\n                addHpRatio += (m.value * 0.01)\n            } else if (m.type === EquipEffectType.LONGYUAN_SWORD) { //提高攻击比例\n                addAttackRatio += (m.value * 0.01)\n            } else if (m.type === EquipEffectType.NOT_DODGE) { //攻击范围 +1\n                this.attackRange += 1\n            } else if (m.type === EquipEffectType.ADD_MOVE_RANGE) { //移动范围 +1\n                this.moveRange += 1\n            }\n        })\n        this.maxHp += this.equip.hp\n        this.attack += this.equip.attack\n        // 画像属性\n        if (this.portrayal) {\n            this.maxHp += this.portrayal.hp\n            this.attack += this.portrayal.attack\n            // 裴行俨 自带攻击力\n            if (this.portrayal.skill?.id === HeroType.PEI_XINGYAN) {\n                addAttackRatio += this.portrayal.skill.value * 0.01\n            }\n        }\n        // 见习勇者\n        if (this.rodeleroCadetLv > 0) {\n            const max = this.getSkillByType(PawnSkillType.CADET)?.value || 0\n            const cadetLv = Math.min(this.rodeleroCadetLv, max)\n            this.maxHp += cadetLv * 5\n            this.attack += Math.round(cadetLv * 0.5)\n        }\n        // 生命 提升比列\n        if (addHpRatio > 0) {\n            this.maxHp += Math.round(this.maxHp * addHpRatio)\n        }\n        // 攻击 提升比例\n        if (addAttackRatio > 0) {\n            this.attack += Math.round(this.attack * addAttackRatio)\n        }\n        // 是否有技能强化\n        if (this.equip.skillIntensify) {\n            const [id, type] = this.equip.skillIntensify\n            this.skills.find(m => m.baseId === id)?.setIntensifyType(type)\n        } else {\n            this.skills.forEach(m => m.setIntensifyType(0))\n        }\n        // 如果是主城或配置士兵直接满血\n        if (gameHpr.world.getMapCellByIndex(this.aIndex)?.isRecoverPawnHP()) {\n            this.curHp = this.getMaxHp()\n        } else if (this.tempCurrHp >= 0) {\n            this.curHp = Math.min(this.tempCurrHp, this.getMaxHp())\n        } else {\n            this.curHp = Math.min(this.curHp, this.getMaxHp())\n        }\n    }\n\n    // 获取技能信息\n    public getSkillByType(type: PawnSkillType) {\n        return this.skills.find(m => m.type === type)\n    }\n\n    // 获取主动技能\n    public getActiveSkill() {\n        return this.skills.find(m => m.use_type === 1 || m.type === PawnSkillType.FULL_STRING || m.type === PawnSkillType.LONGITUDINAL_CLEFT) //目前满弦、顺劈也算主动\n    }\n\n    public setBuffs(buffs: any[]) {\n        this.buffs = buffs.map(m => new BuffObj().fromSvr(m))\n    }\n\n    public addBuffs(buffs: any[]) {\n        if (buffs?.length) {\n            const buffMap: { [key: number]: BuffObj } = {}\n            this.buffs.forEach(m => buffMap[m.type] = m)\n            buffs.forEach(m => {\n                const buff = buffMap[m.type]\n                if (buff) {\n                    buff.fromSvr(m)\n                } else {\n                    this.buffs.push(new BuffObj().fromSvr(m))\n                }\n            })\n        }\n    }\n\n    public isHasBuff(type: BuffType) {\n        return this.buffs.some(m => m.type === type)\n    }\n\n    public getBuffValue(type: BuffType) {\n        return this.buffs.find(m => m.type === type)?.value || 0\n    }\n\n    public cleanAllBuffs() {\n        this.cleanBuffs()\n        this.cleanStrategyBuffs()\n    }\n\n    public cleanBuffs() {\n        this.buffs.length = 0\n    }\n\n    // 获取护盾值\n    public getShieldValue() {\n        let val = 0\n        this.buffs.forEach(m => {\n            if (m.isHasShield()) {\n                val += m.value\n            }\n        })\n        return val\n    }\n\n    public cleanStrategyBuffs() {\n        this.strategyBuffMap = {}\n    }\n\n    public addStrategyBuff(strategy: StrategyObj) {\n        this.strategyBuffMap[strategy.type] = strategy\n    }\n\n    public getStrategyBuff(tp: number) {\n        return this.strategyBuffMap[tp]\n    }\n\n    // 获取韬略数值\n    public getStrategyValue(tp: number) {\n        return this.getStrategyBuff(tp)?.value || 0\n    }\n\n    // 是否有某个韬略\n    public isHasStrategys(...tps: number[]) {\n        return tps.some(m => !!this.strategyBuffMap[m])\n    }\n\n    public isHasStrategy() {\n        return !ut.isEmptyObject(this.strategyBuffMap)\n    }\n\n    public initAnger() {\n        this.curAnger = this.attrJson?.init_anger || 0\n        if (this.curAnger > 0) {\n            // 吕蒙\n            if (this.getPortrayalSkill()?.id === HeroType.LV_MENG) {\n                this.curAnger = Math.round(this.curAnger * 0.5)\n            }\n        }\n        return this\n    }\n\n    public isHasAnger() {\n        return this.maxAnger > 0\n    }\n\n    // 获取装备效果列表\n    public getEquipEffects() {\n        return this.equip?.effects\n    }\n\n    // 获取某个装备效果\n    public getEquipEffectByType(type: EquipEffectType) {\n        return this.equip?.effects.find(m => m.type === type)\n    }\n\n    // 刷新宝箱\n    public updateTreasures(treasures: any[]) {\n        this.treasures = (treasures || []).map(m => gameHpr.fromSvrTreasureInfo(m, this.aIndex, this.armyUid, this.uid))\n    }\n}"]}