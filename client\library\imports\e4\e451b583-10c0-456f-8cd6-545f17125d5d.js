"use strict";
cc._RF.push(module, 'e451bWDEMBFb4zWVF8XEl1d', 'NoviceServerModel');
// app/script/model/guide/NoviceServerModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var AreaObj_1 = require("../area/AreaObj");
var ArmyObj_1 = require("../area/ArmyObj");
var BuildObj_1 = require("../area/BuildObj");
var PawnObj_1 = require("../area/PawnObj");
var CTypeObj_1 = require("../common/CTypeObj");
var EquipInfo_1 = require("../main/EquipInfo");
var GuideConfig_1 = require("./GuideConfig");
var NoviceAreaObj_1 = require("./NoviceAreaObj");
var NoviceBTCityObj_1 = require("./NoviceBTCityObj");
var NoviceBTObj_1 = require("./NoviceBTObj");
var NovicePawnSlotObj_1 = require("./NovicePawnSlotObj");
var NoviceConfig_1 = require("./NoviceConfig");
var NoviceDrillPawnObj_1 = require("./NoviceDrillPawnObj");
var NoviceMarchObj_1 = require("./NoviceMarchObj");
var NoviceOutputObj_1 = require("./NoviceOutputObj");
var NovicePawnLevelingObj_1 = require("./NovicePawnLevelingObj");
var NovicePolicyObj_1 = require("./NovicePolicyObj");
var NoviceEquipSlotObj_1 = require("./NoviceEquipSlotObj");
var PortrayalInfo_1 = require("../common/PortrayalInfo");
var NoviceHeroSlotObj_1 = require("./NoviceHeroSlotObj");
var NoviceEnemyObj_1 = require("./NoviceEnemyObj");
var NoviceRecordObj_1 = require("./NoviceRecordObj");
/**
 * 新手村模块
 */
var NoviceServerModel = /** @class */ (function (_super) {
    __extends(NoviceServerModel, _super);
    function NoviceServerModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.isRunning = false;
        // 玩家数据
        _this.puid = '';
        _this.lastOutputTime = 0;
        _this.outputInterval = 0;
        _this.cereal = new NoviceOutputObj_1.default();
        _this.timber = new NoviceOutputObj_1.default();
        _this.stone = new NoviceOutputObj_1.default();
        _this.expBook = 0;
        _this.iron = 0;
        _this.upScroll = 0;
        _this.fixator = 0;
        _this.cerealConsume = 0;
        _this.stamina = 0;
        _this.gold = 0;
        _this.ownCells = {};
        _this.btQueues = [];
        _this.resetPolicyNeedGold = 0;
        _this.effects = {};
        _this.merchants = [];
        _this.towerLvMap = {};
        _this.configPawnMap = {};
        _this.equips = [];
        _this.currForgeEquip = null;
        _this.guideTasks = [];
        _this.pawnSlots = {}; //当前士兵槽位列表
        _this.policySlots = {}; //当前政策槽位列表
        _this.equipSlots = {}; //当前装备槽位列表
        _this.heroSlots = []; //当前英雄槽位列表
        _this.portrayals = []; //拥有的画像列表
        _this.freeRecruitPawnCount = 0; //免费招募士兵数量
        _this.drillHistory = {}; //历史招募士兵数量
        // 世界
        _this.areas = [];
        _this.armysMap = {};
        _this.marchs = []; //当前行军列表
        _this.avoidWarAreas = {};
        _this.battleDist = {};
        _this.armyAutoBackIndexMap = {};
        _this.pawnLvingQueues = { map: {}, pawnUIDMap: {} };
        _this.drillPawnQueues = {};
        _this.btCityQueues = [];
        _this.enemyEquipMap = {};
        _this.noviceEnemyObj = new NoviceEnemyObj_1.default();
        _this.battleRecordList = []; //战斗记录
        _this.battleRecordMaxCount = 10; //战斗记录最大数量
        _this.saveElapsed = 0;
        _this.firstBattleAreaInfo = null;
        _this.enemyUid = '';
        _this.enemyName = '';
        _this.enemyCells = {};
        _this.defenseBattleIdx = -1;
        _this.castleBattleIdx = -1;
        _this.castleBattleStart = false;
        _this.lastOccupyPawnRes = {}; //最后一次攻占要塞的士兵资源总和
        _this.manulBattles = {};
        _this.hasTreasure = false; //是否有宝箱
        _this.inited = false;
        _this.noviceCanUsePolicyIds = [1001, 1042, 1044, 1009, 1014, 1028]; //新手引导固定这6个政策可选
        _this._cd = 0;
        return _this;
    }
    NoviceServerModel.prototype.onCreate = function () {
    };
    NoviceServerModel.prototype.init = function (uid, mapJson) {
        var _this = this;
        var _a, _b, _c, _d, _e;
        this.puid = uid;
        var now = Date.now();
        var data = this.getSaveData(uid) || {};
        var areas = data.areas || {};
        // 初始化世界信息
        this.towerLvMap = {};
        this.configPawnMap = data.configPawnMap || {};
        this.currForgeEquip = data.currForgeEquip;
        this.guideTasks = data.guideTasks || [];
        this.equips = (data.equips || []).map(function (m) { return new EquipInfo_1.default().fromSvr(m); });
        this.areas = [];
        this.ownCells = {};
        this.enemyCells = {};
        this.enemyUid = data.enemyUid || this.noviceEnemyObj.getEnemyUID();
        this.enemyName = data.enemyName || assetsMgr.lang('guideText.enemy_name');
        this.armysMap = {};
        this.marchs = (data.marchs || []).map(function (m) { return new NoviceMarchObj_1.default().fromDB(m); });
        this.avoidWarAreas = data.avoidWarAreas || {};
        this.battleDist = {};
        this.armyAutoBackIndexMap = data.armyAutoBackIndexMap || {};
        this.btQueues = (data.btQueues || []).map(function (m) { return new NoviceBTObj_1.default().fromDB(m); });
        this.btCityQueues = (data.btCityQueues || []).map(function (m) { return new NoviceBTCityObj_1.default().fromDB(m); });
        this.enemyEquipMap = data.enemyEquipMap || {};
        this.firstBattleAreaInfo = data.firstBattleAreaInfo;
        this.initPawnLvingQueues(data.pawnLvingQueues || {});
        this.initDrillPawnQueues(data.drillPawnQueues || {});
        this.defenseBattleIdx = data.defenseBattleIdx || -1;
        this.castleBattleIdx = data.castleBattleIdx;
        this.lastOccupyPawnRes = data.lastOccupyPawnRes || {};
        this.manulBattles = data.manulBattles || {};
        this.castleBattleStart = data.castleBattleStart || false;
        this.pawnSlots = this.getPawnSlots(data.pawnSlots);
        this.policySlots = this.getPolicySlots(data.policySlots);
        this.equipSlots = this.getEquipSlots(data.equipSlots);
        this.portrayals = (data.portrayals || []).map(function (m) { return new PortrayalInfo_1.default().fromSvr(m); });
        this.heroSlots = this.getHeroSlots(data.heroSlots);
        this.freeRecruitPawnCount = data.freeRecruitPawnCount || 0;
        this.drillHistory = data.drillHistory || {};
        this.battleRecordList = (data.battleRecordList || []).map(function (m) { return new NoviceRecordObj_1.default().fromDB(m); });
        this.gold = data.gold || 0;
        var cells = [], size = NoviceConfig_1.NOVICE_MAP_SIZE.x * NoviceConfig_1.NOVICE_MAP_SIZE.y;
        var areaCenter = GameHelper_1.gameHpr.areaCenter;
        for (var index = 0; index < size; index++) {
            var info = areas[index] || { index: index, landId: mapJson.json[index] };
            var area = this.areas.add(new NoviceAreaObj_1.default().init(info));
            cells.push({
                landId: 1,
                owner: area.owner,
                cityId: 1,
            });
            this.updatePlayerArmyDist(area);
            // 战斗
            if (info.battle) {
                info.battle.mul = 1;
                var a = areaCenter.addArea(new AreaObj_1.default().init(info));
                area.proxyAO = a;
                a.battleBegin(info.battle, false);
                this.addBattleDist(area);
            }
            else if (this.getAreaAttacker(area)) {
                this.addBattleDist(area);
            }
            if (CC_PREVIEW) {
                // for (let i = 0; i < area.armys.length; i++) {
                //     let pawns = area.armys[i].pawns
                //     for (let j = 0; j < pawns.length; j++) {
                //         pawns[j].portrayal = null
                //     }
                // }
                // this.heroSlots[0].hero = null
            }
        }
        // 初始化主城
        var mainArea = this.areas[NoviceConfig_1.NOVICE_MAINCITY_INDEX];
        if (!mainArea.owner) {
            mainArea.owner = uid;
            mainArea.cityId = Constant_1.CITY_MAIN_NID;
            mainArea.builds = [];
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_MAIN_NID, point: cc.v2(2, 5), lv: 1 }, false);
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_WALL_NID, point: cc.v2(-1, -1), lv: 1 }, false);
            mainArea.armys = [];
            mainArea.addArmy({
                index: mainArea.index,
                uid: ut.UID(),
                name: assetsMgr.lang('ui.default_army_name') + 1,
                owner: uid,
                pawns: [
                    { index: mainArea.index, uid: ut.UID(), id: 3101, lv: 1, point: cc.v2(7, 4) },
                    { index: mainArea.index, uid: ut.UID(), id: 3101, lv: 1, point: cc.v2(9, 4) },
                ]
            });
            mainArea.updateSize();
            NoviceConfig_1.NOVICE_MAINCITY_OTHER_INDEXS.forEach(function (i) {
                var area = _this.areas[i];
                area.owner = uid;
                area.cityId = -Constant_1.CITY_MAIN_NID;
            });
            this.updatePlayerArmyDist(mainArea);
        }
        this.areas.forEach(function (m) {
            if (!m.owner) {
            }
            else if (m.owner === _this.puid) {
                _this.ownCells[m.index] = m;
            }
            else if (m.owner === _this.enemyUid) {
                _this.enemyCells[m.index] = m;
            }
            else {
                m.owner = '';
                m.updateCity(0, true);
                m.armys = [];
            }
        });
        // 初始化玩家信息
        this.cereal.value = (_a = data.cereal) !== null && _a !== void 0 ? _a : Constant_1.INIT_RES_COUNT;
        this.timber.value = (_b = data.timber) !== null && _b !== void 0 ? _b : Constant_1.INIT_RES_COUNT;
        this.stone.value = (_c = data.stone) !== null && _c !== void 0 ? _c : Constant_1.INIT_RES_COUNT;
        this.expBook = data.expBook || 0;
        this.iron = data.iron || 0;
        this.upScroll = data.upScroll || 0;
        this.fixator = data.fixator || 0;
        this.lastOutputTime = (_d = data.lastOutputTime) !== null && _d !== void 0 ? _d : now;
        this.stamina = (_e = data.stamina) !== null && _e !== void 0 ? _e : 100;
        this.updateBuildEffect();
        this.updateOpSec();
        this.checkUpdateAreaAvoidWar(now, true);
        this.inited = true;
        this.setPolicyUnLockLv(true);
        this.noviceEnemyObj.load(data.noviceEnemyData || {});
        if (CC_PREVIEW) {
            // this.cereal.value = 999
            // this.timber.value = 999
            // this.stone.value = 999
        }
        return {
            player: {
                cereal: this.cereal.strip(),
                timber: this.timber.strip(),
                stone: this.stone.strip(),
                expBook: this.expBook,
                iron: this.iron,
                upScroll: this.upScroll,
                fixator: this.fixator,
                granaryCap: this.getGranaryCap(),
                warehouseCap: this.getWarehouseCap(),
                cerealConsume: this.cerealConsume,
                stamina: this.stamina,
                mainCityIndex: NoviceConfig_1.NOVICE_MAINCITY_INDEX,
                builds: mainArea.builds.map(function (m) {
                    return {
                        index: m.aIndex,
                        uid: m.uid,
                        id: m.id,
                        lv: m.lv,
                        point: m.point,
                    };
                }),
                armyDists: this.getPlayerArmyDistArray(this.puid),
                resetPolicyNeedGold: this.resetPolicyNeedGold,
                merchants: this.merchants,
                towerLvMap: this.towerLvMap,
                hasNewTreasure: this.checkPlayerHasTreasure(this.puid),
                btQueues: this.btQueues.map(function (m) { return m.strip(); }),
                pawnDrillQueues: this.toDrillPawnQueue(NoviceConfig_1.NOVICE_MAINCITY_INDEX),
                pawnLevelingQueues: this.toPawnLvingQueue(NoviceConfig_1.NOVICE_MAINCITY_INDEX),
                configPawnMap: this.configPawnMap,
                currForgeEquip: this.toForgeEquipData(),
                equips: this.equips.map(function (m) { return m.toDB(); }),
                guideTasks: this.guideTasks.map(function (m) { return { id: m.id, progress: m.progress }; }),
                freeRecruitPawnCount: this.freeRecruitPawnCount,
                pawnSlots: this.pawnSlots,
                policySlots: this.policySlots,
                equipSlots: this.equipSlots,
                heroSlots: this.heroSlots,
            },
            cells: this.areas.map(function (m) { return m.toBaseData(); }),
            enemy: {
                id: this.enemyUid,
                name: this.enemyName,
            },
            user: {
                gold: this.gold,
                portrayals: this.portrayals,
            }
        };
    };
    //记录战斗详情数据
    NoviceServerModel.prototype.recordFighterData = function (data) {
        if (GameHelper_1.gameHpr.isNoviceMode) {
            this.setBattleRecord(3, null, data);
        }
    };
    //士兵槽位
    NoviceServerModel.prototype.getPawnSlots = function (pawnSlots) {
        if (pawnSlots === void 0) { pawnSlots = {}; }
        var slots = {};
        var tempPawnBase = {};
        var pawnBase = assetsMgr.getJson('pawnBase').dataIdMap;
        for (var key in pawnBase) {
            var info = pawnBase[key];
            if (info.need_build_lv > 0 && info.type < 5) {
                var list = tempPawnBase[info.need_build_lv] || [];
                list.push(info.id);
                tempPawnBase[info.need_build_lv] = list;
            }
        }
        for (var i = 0; i < Constant_1.PAWN_SLOT_CONF.length; i++) {
            var lv = Constant_1.PAWN_SLOT_CONF[i];
            slots[lv] = new NovicePawnSlotObj_1.default().fromSvr(pawnSlots[lv] || { lv: lv, id: 0, selectIds: [] }).init();
            slots[lv].allSelectIds = tempPawnBase[lv];
        }
        slots[1].selectIds = [3201, 3204, 3206]; //新手引导固定这3个
        return slots;
    };
    //政策槽位
    NoviceServerModel.prototype.getPolicySlots = function (policySlots) {
        if (policySlots === void 0) { policySlots = {}; }
        var slots = {};
        for (var i = 0; i < Constant_1.POLICY_SLOT_CONF.length; i++) {
            var lv = Constant_1.POLICY_SLOT_CONF[i];
            slots[lv] = new NovicePolicyObj_1.default().fromSvr(policySlots[lv] || { lv: lv, id: 0, selectIds: [] }).init();
            slots[lv].allSelectIds = this.noviceCanUsePolicyIds;
        }
        return slots;
    };
    NoviceServerModel.prototype.randListCount = function (slots, count) {
        if (count === void 0) { count = 3; }
        return ut.shuffleArray(slots).slice(0, count);
    };
    //设置政策解锁等级，新手引导只有固定几个可以解锁
    NoviceServerModel.prototype.setPolicyUnLockLv = function (isNovice) {
        var jsonData = assetsMgr.getJson('policy').dataIdMap;
        for (var key in jsonData) {
            var item = jsonData[key];
            if (this.noviceCanUsePolicyIds.indexOf(item.id) < 0) {
                item.need_build_lv = isNovice ? '100' : '';
            }
        }
        var buildBase = assetsMgr.getJson('buildBase').dataIdMap;
        buildBase[2015].prep_cond = isNovice ? '4,2001,4|4,2004,1' : '4,2001,10|4,2004,1';
        buildBase[2016].prep_cond = isNovice ? '4,2001,2|4,2004,1' : '4,2001,5|4,2004,1';
    };
    //装备槽位
    NoviceServerModel.prototype.getEquipSlots = function (equipSlots) {
        if (equipSlots === void 0) { equipSlots = {}; }
        var equipMap = {};
        var jsonData = assetsMgr.getJson('equipBase').dataIdMap;
        for (var key in jsonData) {
            var info = jsonData[key];
            if (info.random) {
                var list = info.need_build_lv.split(',');
                for (var i = 0; i < list.length; i++) {
                    var lv = list[i];
                    var idList = equipMap[lv] || [];
                    idList.push(info.id);
                    equipMap[lv] = idList;
                }
            }
        }
        var slots = {};
        for (var i = 0; i < Constant_1.EQUIP_SLOT_CONF.length; i++) {
            var lv = Constant_1.EQUIP_SLOT_CONF[i];
            slots[lv] = new NoviceEquipSlotObj_1.default().fromSvr(equipSlots[lv] || { lv: lv, id: 0, selectIds: [] }).init();
            slots[lv].allSelectIds = equipMap[lv];
        }
        return slots;
    };
    //英雄槽位
    NoviceServerModel.prototype.getHeroSlots = function (heroSlots) {
        if (heroSlots) {
            return heroSlots.map(function (m) { return new NoviceHeroSlotObj_1.default().fromSvr(m); });
        }
        var slot = new NoviceHeroSlotObj_1.default();
        slot.lv = 1;
        var slots = [slot];
        return slots;
    };
    // 刷新野地怪物，根据攻击对象刷新不同的野怪
    NoviceServerModel.prototype.updateMonster = function (area, attacker) {
        if (!area.owner && !area.isBattleing() && attacker != GameHelper_1.gameHpr.getUid()) {
            //已经有军队了就不刷新
            for (var i = 0; i < area.armys.length; i++) {
                var army = area.armys[i];
                if (army.owner) {
                    return;
                }
            }
            area.updateArmys(area.getEnemyArmys(attacker));
        }
    };
    NoviceServerModel.prototype.setEnemyUid = function (uid) {
        this.enemyUid = uid;
    };
    NoviceServerModel.prototype.setEnemyArea = function (area) {
        this.enemyCells[area.index] = area;
    };
    NoviceServerModel.prototype.getAreas = function () {
        return this.areas;
    };
    // 获取野怪装备
    NoviceServerModel.prototype.getEnemyEquipById = function (id) {
        return this.enemyEquipMap[id];
    };
    NoviceServerModel.prototype.setEnemyEquip = function (id, equip) {
        this.enemyEquipMap[id] = equip;
    };
    NoviceServerModel.prototype.getSaveData = function (uid) {
        return storageMgr.loadJson('novice_data_' + uid);
    };
    NoviceServerModel.prototype.cleanSaveData = function () {
        storageMgr.remove('novice_data_' + GameHelper_1.gameHpr.getUid());
    };
    NoviceServerModel.prototype.run = function () {
        this.isRunning = true;
    };
    NoviceServerModel.prototype.update = function (dt) {
        if (!this.isRunning) {
            return;
        }
        var now = Date.now();
        this.checkUpdateOutput(now);
        this.checkUpdateMarch(now);
        this.checkUpdateStudy(now);
        this.checkUpdateBTQueue(now);
        this.checkUpdateBTCityQueue(now);
        this.checkUpdateDrillPawnQueue(now);
        this.checkUpdatePawnLvingQueue(now);
        this.checkUpdateAreaAvoidWar(now);
        this.checkUpdateForgeEquip(now);
        if (this._cd >= 0) {
            this._cd -= dt;
            if (this._cd <= 0) {
                this._cd = 1;
                this.checkTreasure();
                this.checkTask();
                this.noviceEnemyObj.trigger();
            }
        }
        this.checkSave(dt);
    };
    NoviceServerModel.prototype.forceSave = function () {
        this.checkSave(10);
    };
    NoviceServerModel.prototype.strip = function () {
        var areas = {};
        for (var key in this.ownCells) {
            var area = this.ownCells[key];
            areas[area.index] = area.toDB();
        }
        var dist = this.getPlayerArmyDist(this.puid);
        for (var key in dist) {
            var index = Number(key);
            if (!areas[key]) {
                areas[index] = this.getArea(index).toDB();
            }
        }
        if (this.enemyUid) {
            for (var key in this.enemyCells) {
                var area = this.enemyCells[key];
                areas[area.index] = area.toDB();
            }
            var enemyDist = this.getPlayerArmyDist(this.enemyUid);
            for (var key in enemyDist) {
                var index = Number(key);
                if (!areas[key]) {
                    areas[index] = this.getArea(index).toDB();
                }
            }
        }
        return {
            areas: areas,
            cereal: this.cereal.value,
            timber: this.timber.value,
            stone: this.stone.value,
            expBook: this.expBook,
            iron: this.iron,
            upScroll: this.upScroll,
            fixator: this.fixator,
            stamina: this.stamina,
            lastOutputTime: this.lastOutputTime,
            btQueues: this.btQueues,
            pawnLvingQueues: this.pawnLvingQueues,
            drillPawnQueues: this.drillPawnQueues,
            avoidWarAreas: this.avoidWarAreas,
            armyAutoBackIndexMap: this.armyAutoBackIndexMap,
            marchs: this.marchs,
            configPawnMap: this.configPawnMap,
            currForgeEquip: this.currForgeEquip,
            equips: this.equips.map(function (m) { return m.toDB(); }),
            btCityQueues: this.btCityQueues,
            enemyEquipMap: this.enemyEquipMap,
            guideTasks: this.guideTasks,
            firstBattleAreaInfo: this.firstBattleAreaInfo,
            freeRecruitPawnCount: this.freeRecruitPawnCount,
            enemyUid: this.enemyUid,
            castleBattleIdx: this.castleBattleIdx,
            lastOccupyPawnRes: this.lastOccupyPawnRes,
            defenseBattleIdx: this.defenseBattleIdx,
            manulBattles: this.manulBattles,
            castleBattleStart: this.castleBattleStart,
            pawnSlots: this.objectToDB(this.pawnSlots),
            policySlots: this.objectToDB(this.policySlots),
            equipSlots: this.objectToDB(this.equipSlots),
            portrayals: this.portrayals.map(function (m) { return m.strip(); }),
            heroSlots: this.heroSlots.map(function (m) { return m.strip(); }),
            noviceEnemyData: this.noviceEnemyObj.strip(),
            drillHistory: this.drillHistory,
            gold: this.gold,
            battleRecordList: this.battleRecordList.map(function (m) { return m.strip(); }),
        };
    };
    NoviceServerModel.prototype.getGold = function () { return this.gold; };
    NoviceServerModel.prototype.setGold = function (val) {
        this.gold = val;
    };
    NoviceServerModel.prototype.objectToDB = function (obj) {
        var retObj = {};
        for (var key in obj) {
            retObj[key] = obj[key].toDB();
        }
        return retObj;
    };
    NoviceServerModel.prototype.checkSave = function (dt) {
        // 避免数据还未写进内存就用初始数据覆盖存档
        if (!this.inited)
            return;
        this.saveElapsed += dt;
        if (this.saveElapsed < 1) {
            return;
        }
        this.saveElapsed = 0;
        storageMgr.saveJson('novice_data_' + this.puid, this.strip());
        // cc.log('save...')
    };
    NoviceServerModel.prototype.getArea = function (index) {
        return this.areas[index];
    };
    NoviceServerModel.prototype.getGuideTasks = function () {
        return this.guideTasks;
    };
    NoviceServerModel.prototype.appendGuideTask = function (filter) {
        var _a, _b;
        var db = (_a = assetsMgr.getJson('guideTask')) === null || _a === void 0 ? void 0 : _a.datas;
        var curTasks = new Set(this.guideTasks.map(function (m) { return m.id; }));
        var tasks = (_b = db.filter(function (m) { return filter(m) && !curTasks.has(m.id); })) !== null && _b !== void 0 ? _b : [];
        this.guideTasks = __spread(this.guideTasks, tasks.map(function (m) { return { id: m.id, progress: 0 }; }));
        this.updateGuideTasksProgress();
    };
    NoviceServerModel.prototype.updateGuideTasksProgress = function () {
        var tasks = [];
        this.guideTasks.forEach(function (m) {
            tasks.push({ id: m.id, progress: m.progress });
        });
        if (tasks.length > 0) {
            GameHelper_1.gameHpr.player.updateGuideTasksProgress(tasks);
        }
    };
    // 刷新免战信息
    NoviceServerModel.prototype.checkUpdateAreaAvoidWar = function (now, init) {
        for (var key in this.avoidWarAreas) {
            var time = this.avoidWarAreas[key];
            // 新手村直接无免战
            if (true || now - time >= 0) {
                delete this.avoidWarAreas[key];
                !init && this.notifyWorld(Enums_1.NotifyType.AREA_AVOID_WAR, Number(key));
            }
        }
    };
    NoviceServerModel.prototype.initPawnLvingQueues = function (data) {
        this.pawnLvingQueues = { map: {}, pawnUIDMap: data.pawnUIDMap || {} };
        for (var key in data.map) {
            var arr = data.map[key];
            this.pawnLvingQueues.map[key] = arr.map(function (m) { return new NovicePawnLevelingObj_1.default().fromDB(m); });
        }
    };
    NoviceServerModel.prototype.initDrillPawnQueues = function (data) {
        this.drillPawnQueues = {};
        for (var key in data) {
            var obj = data[key];
            for (var k in obj) {
                var arr = obj[k];
                obj[k] = arr.map(function (m) { return new NoviceDrillPawnObj_1.default().fromDB(m); });
            }
            this.drillPawnQueues[key] = obj;
        }
    };
    NoviceServerModel.prototype.getPlayerCellOutput = function () {
        var cereal = Constant_1.INIT_RES_OUTPUT, timber = Constant_1.INIT_RES_OUTPUT, stone = Constant_1.INIT_RES_OUTPUT;
        for (var key in this.ownCells) {
            var area = this.ownCells[key], landId = area.landId;
            if (area.cityId > 0) { //从城市资源获取
                var id = area.cityId * 1000 + landId;
                var json = assetsMgr.getJsonData('cityResAttr', id);
                if (json) {
                    cereal += json.cereal;
                    timber += json.timber;
                    stone += json.stone;
                }
            }
            else if (area.cityId === 0) {
                var json = assetsMgr.getJsonData('land', landId);
                cereal += json.cereal;
                timber += json.timber;
                stone += json.stone;
            }
        }
        return { cereal: cereal, timber: timber, stone: stone };
    };
    // 更新玩家的军队分布信息
    NoviceServerModel.prototype.updatePlayerArmyDist = function (area) {
        var _this = this;
        area.armys.forEach(function (m) {
            _this.changeArmyDistIndex(m.owner, m.uid, area.index);
        });
    };
    // 改变军队位置
    NoviceServerModel.prototype.changeArmyDistIndex = function (owner, uid, index) {
        var dist = this.armysMap[owner];
        if (!dist) {
            dist = this.armysMap[owner] = {};
        }
        if (index == -1) {
            delete dist[uid];
        }
        else {
            dist[uid] = index;
        }
    };
    NoviceServerModel.prototype.getPlayerArmyDist = function (uid) {
        var dist = this.armysMap[uid] || {}, dists = {};
        for (var key in dist) {
            var index = dist[key];
            dists[index] = true;
        }
        return dists;
    };
    // 获取玩家军队分布情况
    NoviceServerModel.prototype.getPlayerArmyDistArray = function (uid) {
        var dist = this.getPlayerArmyDist(uid);
        var arr = [];
        var _loop_1 = function (key) {
            var index = Number(key);
            var area = this_1.getArea(index);
            if (!area) {
                return "continue";
            }
            var armys = [];
            area.armys.forEach(function (m) {
                if (m.owner === uid) {
                    armys.push(m.strip());
                }
            });
            if (armys.length > 0) {
                arr.push({ index: index, armys: armys });
            }
        };
        var this_1 = this;
        for (var key in dist) {
            _loop_1(key);
        }
        return arr;
    };
    // 获取我的军队列表
    NoviceServerModel.prototype.getMyPlayerArmys = function () {
        var uid = GameHelper_1.gameHpr.getUid();
        var dist = this.getPlayerArmyDist(uid);
        var armys = [];
        for (var key in dist) {
            var index = Number(key);
            var area = this.getArea(index);
            if (!area) {
                continue;
            }
            area.armys.forEach(function (m) {
                if (m.owner === uid) {
                    armys.push(m.strip());
                }
            });
        }
        return armys;
    };
    NoviceServerModel.prototype.getPlayerCerealConsume = function () {
        var count = 0;
        var uid = this.puid;
        var dist = this.getPlayerArmyDist(uid);
        for (var key in dist) {
            var area = this.areas[Number(key)];
            if (!area) {
                continue;
            }
            area.armys.forEach(function (m) {
                if (m.owner === uid) {
                    m.pawns.forEach(function (p) { return count += p.cerealCost; });
                }
            });
        }
        return count;
    };
    NoviceServerModel.prototype.getPolicyEffect = function (type) {
        var count = 0;
        for (var key in this.policySlots) {
            var policySlot = this.policySlots[key];
            if (policySlot.type === type) {
                count += policySlot.getValue(0);
            }
        }
        return count;
    };
    // 获取粮食容量
    NoviceServerModel.prototype.getGranaryCap = function () {
        return Constant_1.INIT_RES_CAP + (this.effects[Enums_1.CEffect.GRANARY_CAP] || 0) + this.getExtraCap(2002);
    };
    // 获取仓库容量
    NoviceServerModel.prototype.getWarehouseCap = function () {
        return Constant_1.INIT_RES_CAP + (this.effects[Enums_1.CEffect.WAREHOUSE_CAP] || 0) + this.getExtraCap(2003);
    };
    // 获取额外容量
    NoviceServerModel.prototype.getExtraCap = function (id) {
        var val = this.getPolicyEffect(Enums_1.CEffect.GW_CAP) || 0;
        if (val <= 0) {
            return 0;
        }
        var lv = 0;
        var area = this.areas[NoviceConfig_1.NOVICE_MAINCITY_INDEX];
        area.builds.forEach(function (m) {
            if (m.id === id) {
                lv += m.lv;
            }
        });
        return lv * val;
    };
    NoviceServerModel.prototype.checkPolicyWithType = function (type) {
        switch (type) {
            case Enums_1.CEffect.RES_OUTPUT:
                this.updateOpSec(true);
                break;
        }
    };
    NoviceServerModel.prototype.updateOpSec = function (isNotify) {
        // 计算占领的资源田
        var _a = this.getPlayerCellOutput(), cereal = _a.cereal, timber = _a.timber, stone = _a.stone;
        // 加上内政增加 固定值
        var val = this.getPolicyEffect(Enums_1.CEffect.RES_OUTPUT);
        cereal += val;
        timber += val;
        stone += val;
        // 获取粮耗
        this.cerealConsume = this.getPlayerCerealConsume();
        // 取出最大的产出时间
        this.outputInterval = this.cereal.initOutput(cereal - this.cerealConsume);
        var t = this.timber.initOutput(timber);
        if (t > this.outputInterval) {
            this.outputInterval = t;
        }
        t = this.stone.initOutput(stone);
        if (t > this.outputInterval) {
            this.outputInterval = t;
        }
        if (isNotify) {
            this.notifyPlayer(Enums_1.NotifyType.OUTPUT, {
                cereal: this.cereal.strip(),
                timber: this.timber.strip(),
                stone: this.stone.strip(),
                cerealConsume: this.cerealConsume,
            });
        }
    };
    NoviceServerModel.prototype.checkUpdateOutput = function (now) {
        var dt = now - this.lastOutputTime;
        if (dt < this.outputInterval) {
            return;
        }
        this.lastOutputTime = now;
        // 限制产出
        var capScale = .7;
        if (this.cereal.do(dt, this.getGranaryCap() * capScale)) { //粮食
            this.notifyPlayer(Enums_1.NotifyType.OUTPUT, { cereal: this.cereal.strip() });
        }
        var cap = this.getWarehouseCap() * capScale;
        if (this.timber.do(dt, cap)) { //木头
            this.notifyPlayer(Enums_1.NotifyType.OUTPUT, { timber: this.timber.strip() });
        }
        if (this.stone.do(dt, cap)) { //石头
            this.notifyPlayer(Enums_1.NotifyType.OUTPUT, { stone: this.stone.strip() });
        }
    };
    NoviceServerModel.prototype.notifyPlayer = function (type, data) {
        var _a;
        this.emit('game/OnUpdatePlayerInfo', { list: [(_a = { type: type }, _a['data_' + type] = data, _a)] });
    };
    NoviceServerModel.prototype.notifyWorld = function (type, data) {
        var _a;
        this.emit('game/OnUpdateWorldInfo', { list: [(_a = { type: type }, _a['data_' + type] = data, _a)] });
    };
    NoviceServerModel.prototype.notifyArea = function (index, type, data) {
        var _a;
        this.emit('game/OnUpdateAreaInfo', (_a = { index: index, type: type }, _a['data_' + type] = data, _a));
    };
    NoviceServerModel.prototype.notifyPlayerArmyDistInfo = function (owner) {
        this.notifyPlayer(Enums_1.NotifyType.ARMY_DIST, this.getPlayerArmyDistArray(owner || this.puid));
    };
    // 获取地块于地块的距离
    NoviceServerModel.prototype.getToMapCellDis = function (sindex, tindex) {
        var _a = __read(MapHelper_1.mapHelper.getMinDisPoint(GameHelper_1.gameHpr.world.getMapCellByIndex(sindex).getOwnPoints(), GameHelper_1.gameHpr.world.getMapCellByIndex(tindex).getOwnPoints()), 2), sp = _a[0], tp = _a[1];
        return MapHelper_1.mapHelper.getPointToPointDis(sp, tp);
    };
    // 获取同时到达行军时间
    NoviceServerModel.prototype.getArmysMarchSameSpeed = function (armys, target, speedAcc) {
        var sameTime = 0;
        for (var i = 0; i < armys.length; i++) {
            var area = this.getArea(armys[i].index);
            var army = area === null || area === void 0 ? void 0 : area.getArmyByUid(armys[i].uid);
            if (army) {
                var pawn = army.getPawnByMinMarchSpeed();
                var dis = this.getToMapCellDis(armys[i].index, target);
                var time = dis * (ut.Time.Hour / pawn.marchSpeed);
                sameTime = Math.max(sameTime, time);
            }
        }
        sameTime = Math.max(sameTime / Math.abs(speedAcc), 200);
        return sameTime;
    };
    // 添加行军
    NoviceServerModel.prototype.addMarchArmy = function (index, army, target, timeState, auto) {
        if (index === target) {
            return;
        }
        // 行军速度最慢的士兵
        var pawn = army.getPawnByMinMarchSpeed();
        // 获取时间
        var time = 0;
        if (timeState > 0) {
            time = timeState;
        }
        else {
            var dis = this.getToMapCellDis(index, target);
            time = dis * (ut.Time.Hour / pawn.marchSpeed);
            if (timeState < 0) { //是否加速
                time = Math.max(time / Math.abs(timeState), 200);
            }
        }
        // cc.log('addMarchArmy timeState: ' + timeState + ', time: ' + time)
        var march = new NoviceMarchObj_1.default().init(army, index, target, Math.floor(time), pawn.id);
        march.autoRevoke = auto;
        this.marchs.push(march);
        army.state = Enums_1.ArmyState.MARCH;
        // 通知行军
        this.notifyWorld(Enums_1.NotifyType.ADD_MARCH, march.strip());
        // 通知删除战场中的军队 因为进入行军中就需要从战场中删除掉
        this.notifyArea(index, Enums_1.NotifyType.REMOVE_ARMY, army.uid);
        // 通知军队分布情况
        this.notifyPlayerArmyDistInfo();
    };
    // 检测更新行军信息
    NoviceServerModel.prototype.checkUpdateMarch = function (now) {
        for (var i = this.marchs.length - 1; i >= 0; i--) {
            var m = this.marchs[i];
            if (now - m.startTime < m.needTime) {
                continue;
            }
            // 到达目的地 先删除
            this.marchs.splice(i, 1);
            this.notifyWorld(Enums_1.NotifyType.REMOVE_MARCH, { uid: m.uid });
            var area = this.getArea(m.armyIndex), tArea = this.getArea(m.targetIndex);
            if (!area || !tArea) {
                continue;
            }
            var army = area.getArmyByUid(m.armyUid);
            if (!army) {
                continue;
            }
            army.state = Enums_1.ArmyState.NONE;
            var isRevoke = area.index === tArea.index;
            var isCanAddAreaArmy = this.checkCanAddAreaArmy(tArea, army.owner, isRevoke);
            // 如果是撤回 或者可以进入
            if (isRevoke || isCanAddAreaArmy) {
                if (!isRevoke) {
                    area.removeArmy(army.uid); // 只要不是撤回 就从当前区域删除
                }
                // 如果是撤回 但是不能进入当前区域 就找一个可以进入的继续行军
                if (isRevoke && !isCanAddAreaArmy) {
                    // 添加记录
                    // this.AddArmyMarchRecord(m.AutoRevoke, isRevoke, army, m.StartIndex, m.TargetIndex)
                    // if ta := this.GetPlayerNotFullArmyArea(army.Owner, tArea.index); ta != nil {
                    //     this.AddMarchArmy(tArea.index, army, ta.index, 0, true)
                    // } else {
                    //     tArea.RemoveArmy(army.Uid) //直接删除士兵
                    //     this.NotifyPlayerArmyDistInfo(army.Owner)
                    //     // 记录解散
                    //     this.room.GetRecord().AddArmyMarchRecord(4, army.Owner, army.Uid, army.Name, army.AIndex, 0)
                    // }
                }
                else {
                    this.updateMonster(tArea, army.owner);
                    // 添加到目标区域
                    this.addAreaArmy(tArea, army, MapHelper_1.mapHelper.getAddArmyDir(area.index, tArea.index));
                    // 通知军队分布情况
                    this.notifyPlayerArmyDistInfo();
                    // 添加记录
                    // this.AddArmyMarchRecord(m.AutoRevoke, isRevoke, army, m.StartIndex, m.TargetIndex)
                }
            }
            else { //否则 强行遣返
                //     this.room.GetRecord().AddArmyMarchRecord(5, army.Owner, army.Uid, army.Name, m.StartIndex, m.TargetIndex)
                //     army.AutoBackIndex = -1
                //     this.CancelMarchArmy(m, true)
            }
        }
    };
    NoviceServerModel.prototype.addAreaArmy = function (area, army, dir) {
        this.setArmyPawnPosition(area, army, dir); //设置士兵的位置
        army.enterDir = dir;
        army.aIndex = area.index;
        army.pawns.forEach(function (m) { return m.aIndex = area.index; });
        area.addArmy(army.strip());
        army = area.getArmyByUid(army.uid);
        this.changeArmyDistIndex(army.owner, army.uid, area.index);
        // 战斗中
        if (area.isBattleing()) {
            this.addArmyToBattle(area, army);
            return army;
        }
        var attacker = this.getAreaAttacker(area);
        if (attacker) { //有攻击者 直接触发战斗
            this.triggerAreaBattle(area, attacker, area.passPoints[dir]);
        }
        else if (army.getActPawnCount() > 0) { //有士兵才通知
            if (area.isRecoverPawnHP()) {
                army.recoverAllPawn(); //恢复士兵血量
            }
            this.notifyArea(area.index, Enums_1.NotifyType.ADD_ARMY, army.strip());
        }
        return army;
    };
    NoviceServerModel.prototype.triggerAreaBattle = function (area, attacker, enterPoint) {
        var _this = this;
        // 由教程主动触发战斗
        if (GameHelper_1.gameHpr.guide.isCurrProgressTag(GuideConfig_1.GuideTagType.FIRST_TRIGGER_BATTLE)) {
            // 调整士兵的位置
            var army_1 = area.armys.find(function (m) { return m.owner === _this.puid; });
            if (army_1 && army_1.pawns.length > 0) {
                NoviceConfig_1.NOVICE_FIRST_BATTLE_PAWN_POINT.forEach(function (m, i) { var _a; return (_a = army_1.pawns[i]) === null || _a === void 0 ? void 0 : _a.setPoint(m); });
                army_1.pawns[0].curHp = 45;
            }
            // 调整野怪血量 
            area.armys.filter(function (m) { return m.owner !== _this.puid; }).forEach(function (army) { return army.pawns.forEach(function (m) {
                var _a;
                m.curHp = (_a = NoviceConfig_1.NOVICE_FIRST_BATTLE_ENEMY_HP[m.id]) !== null && _a !== void 0 ? _a : m.maxHp;
            }); });
            this.firstBattleAreaInfo = { index: area.index, attacker: attacker };
        }
        else if (this.manulBattles[area.index]) { //手动战斗
            this.manulBattles[area.index] = { index: area.index, attacker: attacker };
            // 先设置状态
            area.armys.forEach(function (m) {
                if (m.state === Enums_1.ArmyState.NONE) {
                    m.state = Enums_1.ArmyState.FIGHT;
                    m.pawns.forEach(function (p) { return p.changeState(Enums_1.PawnState.STAND); });
                }
            });
        }
        else {
            this.battleBegin(area, attacker);
        }
        this.addBattleDist(area);
    };
    NoviceServerModel.prototype.battleBegin = function (area, attacker, mul) {
        var roles = [];
        var fighters = [];
        var pawnResObj = this.lastOccupyPawnRes[area.index] = {};
        area.armys.forEach(function (m) {
            if (m.state === Enums_1.ArmyState.MARCH) {
                return;
            }
            m.state = Enums_1.ArmyState.FIGHT;
            var isAttacker = m.owner === attacker;
            var isMyArmy = m.isOwner();
            m.pawns.forEach(function (p) {
                var _a;
                p.changeState(Enums_1.PawnState.STAND);
                fighters.push({
                    uid: p.uid,
                    camp: isAttacker ? 1 : 0,
                    attackIndex: isAttacker ? p.attackSpeed + 100000 : p.attackSpeed,
                });
                if (isMyArmy) {
                    GameHelper_1.gameHpr.stringToCTypes((_a = p.baseJson) === null || _a === void 0 ? void 0 : _a.drill_cost).forEach(function (m) {
                        pawnResObj[m.type] = (pawnResObj[m.type] || 0) + m.count;
                    });
                }
                roles.push({ role_id: p.id, role_count: 1 });
            });
        });
        var a = GameHelper_1.gameHpr.areaCenter.getArea(area.index);
        if (a) {
            a.init(area.strip());
        }
        else {
            a = GameHelper_1.gameHpr.areaCenter.addArea(new AreaObj_1.default().init(area.strip()));
        }
        area.proxyAO = a;
        var attackIndex = 0;
        fighters.sort(function (a, b) { return b.attackIndex - a.attackIndex; });
        fighters.forEach(function (m) { return m.attackIndex = ++attackIndex; });
        this.addTowerToBattle(area, attackIndex, fighters);
        // 开始战斗
        a.battleBegin({
            camp: 0,
            fighters: fighters,
            mul: mul || 1,
            attackerArmyAcc: 1,
            defenderArmyAcc: 1,
        });
        // 上报
        TaHelper_1.taHelper.track('ta_attackFieldStart_rookie', {
            roles: roles,
            field: {
                field_lv: area.landLv,
                field_dis: MapHelper_1.mapHelper.getIndexToIndexDis(NoviceConfig_1.NOVICE_MAINCITY_INDEX, area.index),
                field_type: area.owner ? 0 : area.getLandType(),
                field_index: area.index,
            },
            uid: GameHelper_1.gameHpr.getUid()
        });
        //记录战斗信息
        if (attacker === GameHelper_1.gameHpr.getUid()) {
            var record = new NoviceRecordObj_1.default();
            record.init(area, { attacker: attacker, fighters: fighters });
            this.battleRecordList.unshift(record);
        }
    };
    NoviceServerModel.prototype.addTowerToBattle = function (area, attackIndex, fighters) {
        //处理攻击建筑
        if (area.isOwner()) { //自己领地被进攻
            // 加入箭塔
            fighters.push({
                towerId: 7001,
                towerLv: 1,
                uid: ut.UID(),
                camp: 0,
                attackIndex: ++attackIndex,
                point: { x: 5, y: 5 },
                waitRound: 0,
            });
        }
        else if (area.cityId === Constant_1.CITY_FORT_NID) { //敌方要塞
            // 加入箭塔
            fighters.push({
                towerId: 7002,
                towerLv: 1,
                uid: ut.UID(),
                camp: 0,
                attackIndex: ++attackIndex,
                point: { x: 5, y: 5 },
                waitRound: 0,
            });
        }
        else if (area.cityId === Constant_1.CITY_MAIN_NID) { //敌方主城
            // 加入城墙
            fighters.push({
                towerId: 7003,
                towerLv: 1,
                uid: ut.UID(),
                camp: 0,
                attackIndex: ++attackIndex,
                point: { x: 5, y: 5 },
                waitRound: 0,
            });
            fighters.push({
                towerId: 7003,
                towerLv: 1,
                uid: ut.UID(),
                camp: 1,
                attackIndex: ++attackIndex,
                point: { x: 11, y: 5 },
                waitRound: 0,
            });
            fighters.push({
                towerId: 7003,
                towerLv: 1,
                uid: ut.UID(),
                camp: 0,
                attackIndex: ++attackIndex,
                point: { x: 5, y: 11 },
                waitRound: 0,
            });
            fighters.push({
                towerId: 7003,
                towerLv: 1,
                uid: ut.UID(),
                camp: 0,
                attackIndex: ++attackIndex,
                point: { x: 11, y: 11 },
                waitRound: 0,
            });
        }
    };
    NoviceServerModel.prototype.resumeBattle = function () {
        var lookCell = GameHelper_1.gameHpr.world.getLookCell();
        if (this.manulBattles[lookCell.index]) {
            var _a = this.manulBattles[lookCell.index], index = _a.index, attacker = _a.attacker;
            var area = this.getArea(lookCell.index);
            this.battleBegin(area, attacker, 1);
            delete this.manulBattles[lookCell.index];
            return true;
        }
        return false;
    };
    NoviceServerModel.prototype.beginFirstBattle = function () {
        var _this = this;
        if (this.firstBattleAreaInfo) {
            var _a = this.firstBattleAreaInfo, index = _a.index, attacker = _a.attacker;
            this.firstBattleAreaInfo = undefined;
            var area = this.getArea(index);
            if (area === null || area === void 0 ? void 0 : area.armys.some(function (m) { return m.owner === _this.puid; })) {
                this.battleBegin(area, attacker, 1);
            }
        }
    };
    // 触发战斗结束
    NoviceServerModel.prototype.triggerBattleEnd = function (index, attacker, battleEndInfo) {
        var _this = this;
        var _a;
        var area = this.areas[index];
        if (!area) {
            return;
        }
        var owner = area.owner;
        var isMainCity = area.isMainCity();
        // 还原军队状态
        var a = (_a = area.proxyAO) !== null && _a !== void 0 ? _a : GameHelper_1.gameHpr.areaCenter.getArea(index);
        if (!a) {
            return;
        }
        area.proxyAO = null;
        var equipAttrMap = {}, armyMap = {};
        GameHelper_1.gameHpr.player.getEquips().forEach(function (m) { return equipAttrMap[m.uid] = m.attrs; });
        // 先删除没有的
        var armyUidMap = {}, isRemoveArmy = false;
        a.armys.forEach(function (m) { return armyUidMap[m.uid] = true; });
        for (var i = area.armys.length - 1; i >= 0; i--) {
            var uid = area.armys[i].uid;
            if (!armyUidMap[uid]) {
                area.removeArmy(uid);
                isRemoveArmy = true;
            }
        }
        if (isRemoveArmy) {
            this.notifyPlayerArmyDistInfo();
        }
        // 如果占领了 就删除
        if (area.isOwner() || attacker) {
            delete this.lastOccupyPawnRes[index];
        }
        if (attacker || owner) {
            area.updateArmys(a.armys.map(function (m) { return m.strip(); }));
        }
        else { //没有占领 这里重新刷新野怪
            area.updateArmys(area.getEnemyArmys());
        }
        area.armys.forEach(function (army) {
            army.state = Enums_1.ArmyState.NONE;
            // 只有是胜利者才有宝箱
            if (GameHelper_1.gameHpr.checkIsOneAlliance(attacker, army.owner)) {
                var arr = armyMap[army.owner];
                if (!arr) {
                    arr = armyMap[army.owner] = [];
                }
                arr.push(army);
            }
            army.pawns.forEach(function (m) {
                m.changeState(Enums_1.PawnState.NONE);
                m.updateEquipAttr(m.equip.uid, equipAttrMap[m.equip.uid]); //刷新装备信息
                if (m.isHero()) {
                    _this.setGuideTaskProgress(100028, 1);
                }
            });
        });
        var recordTreasures = []; //记录宝箱
        // 分配宝箱
        var _b = this.getBattleTreasureCount(area, attacker), treasureIds = _b.treasureIds, treasureCounts = _b.treasureCounts, specialTreasureId = _b.specialTreasureId;
        if (!ut.isEmptyObject(armyMap) && (treasureIds.length > 0 || specialTreasureId > 0)) {
            var uids = {};
            if (treasureCounts.length < 2 || treasureIds.length === 0) {
                treasureCounts = [0, 0];
            }
            var attackTreasureCount = ut.random(treasureCounts[0], treasureCounts[1]); //先随机攻占者的数量
            for (var uid in armyMap) {
                var rcount = 0, armys = armyMap[uid];
                var treasureCount = 0, specialTreasureCount = 0;
                if (uid === attacker) {
                    treasureCount = attackTreasureCount;
                    specialTreasureCount = 1; //暂时只有攻击者有
                }
                // 添加特殊资源
                if (specialTreasureId > 0 && specialTreasureCount > 0) {
                    for (var i = 0, l = armys.length; i < l; i++) {
                        var army = armys[i];
                        for (var ii = 0, ll = army.pawns.length; ii < ll; ii++) {
                            var pawn = army.pawns[ii];
                            var sc = this.addPawnTreasure(pawn, specialTreasureCount, specialTreasureId);
                            rcount += sc;
                            specialTreasureCount -= sc;
                            if (specialTreasureCount <= 0) {
                                break;
                            }
                        }
                        if (specialTreasureCount <= 0) {
                            break;
                        }
                    }
                }
                if (treasureCount > 0) {
                    // 随机分配
                    var cnt = armys.length, min = 0;
                    var length = cnt;
                    var sum = treasureCount - min * length;
                    var counts = [];
                    var i = 0;
                    for (var l = length - 1; i < l; i++) {
                        var val = ut.random(0, sum / length * 2);
                        sum -= val;
                        length -= 1;
                        counts[i] = val + min;
                    }
                    counts[i] = sum + min;
                    var count = 0;
                    for (var i_1 = 0, l = armys.length; i_1 < l; i_1++) {
                        var army = armys[i_1];
                        count += counts[i_1];
                        if (count <= 0 || army.pawns.length === 0) {
                            continue;
                        }
                        for (var ii = 0, ll = army.pawns.length; ii < ll; ii++) {
                            var pawn = army.pawns[ii];
                            var data = treasureIds[ut.randomIndexByWeight(treasureIds, 'weight')];
                            var sc = this.addPawnTreasure(pawn, count, data.id);
                            recordTreasures.push(pawn.treasures[pawn.treasures.length - 1]);
                            rcount += sc;
                            count -= sc;
                            if (count <= 0) {
                                break;
                            }
                        }
                    }
                }
                if (rcount > 0) {
                    uids[uid] = true;
                }
            }
            // 发送有新宝箱通知
            for (var uid in uids) {
                this.sendPlayerHasTreasure(uid);
            }
        }
        // 占领
        if (attacker && attacker !== owner) {
            area.owner = attacker;
            area.updateCity(0, true);
            if (attacker === this.puid) {
                this.ownCells[area.index] = area;
                delete this.enemyCells[area.index];
            }
            else {
                this.enemyCells[area.index] = area;
                delete this.ownCells[area.index];
            }
            this.updateOpSec(true);
            // 新手村不免战
            // this.addAvoidWarAreaEndTime(area.index, ut.Time.Hour * 7, false) //添加免战时间
            this.notifyWorld(Enums_1.NotifyType.ADD_CELL, { index: index, owner: attacker }); //通知
            // 占领敌人主城
            if (isMainCity) {
                for (var key in this.enemyCells) {
                    var cell = this.enemyCells[key];
                    if (cell.owner !== attacker) {
                        cell.owner = attacker;
                        cell.updateCity(0, true);
                        cell.armys.length = 0;
                        this.notifyWorld(Enums_1.NotifyType.ADD_CELL, { index: cell.index, owner: attacker }); //通知
                    }
                }
                this.enemyCells = {};
            }
        }
        this.removeBattleDist(index);
        // 更新血量
        area.updateMaxHP();
        // 是否有军队要自动返回的
        this.checkArmyAutoBack(area);
        // 调整位置
        this.adjustmentPawnPos(area);
        // 如果是可以恢复血量的
        if (area.isRecoverPawnHP()) {
            area.recoverAllPawnHP();
        }
        this.notifyArea(area.index, Enums_1.NotifyType.AREA_BATTLE_END, {
            attacker: attacker,
            data: area.strip(),
            treasures: area.armys.reduce(function (arr, cur) { return cur.getAllPawnTreasures().concat(arr); }, []),
        });
        // 上报
        TaHelper_1.taHelper.track('ta_attackFieldEnd_rookie', {
            isWin: !!attacker,
            battle_costTime: area.getBattleElapsedTime(),
            field: {
                field_lv: area.landLv,
                field_dis: MapHelper_1.mapHelper.getIndexToIndexDis(NoviceConfig_1.NOVICE_MAINCITY_INDEX, area.index),
                field_type: owner ? 0 : area.getLandType(),
                field_index: area.index,
            },
            uid: GameHelper_1.gameHpr.getUid()
        });
        this.checkHero();
        this.setBattleRecord(2, area, { battleEndInfo: battleEndInfo, recordTreasures: recordTreasures });
    };
    NoviceServerModel.prototype.getBattleTreasureCount = function (area, fighter) {
        var treasureIds = [], treasureCounts = [], specialTreasureId = 0;
        if (!fighter) {
            return { treasureIds: treasureIds, treasureCounts: treasureCounts, specialTreasureId: specialTreasureId };
        }
        else if (area.owner === fighter) {
            return { treasureIds: treasureIds, treasureCounts: treasureCounts, specialTreasureId: specialTreasureId };
        }
        else if (area.owner) {
            return { treasureIds: treasureIds, treasureCounts: treasureCounts, specialTreasureId: specialTreasureId };
        }
        var index = NoviceConfig_1.NOVICE_MAINCITY_INDEX;
        var dis = Math.min(16, GameHelper_1.gameHpr.getToMapCellDis(area.index, index));
        var landLv = area.landLv, landType = area.landType;
        var json = assetsMgr.getJsonData('landAttr', landLv * 1000 + dis);
        if (!json) {
            return { treasureIds: treasureIds, treasureCounts: treasureCounts, specialTreasureId: specialTreasureId };
        }
        else if (this.changeStamina(-json.need_stamina) === -1) {
            return { treasureIds: treasureIds, treasureCounts: treasureCounts, specialTreasureId: specialTreasureId };
        }
        else if (GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.CHECK_FIRST_BATTLE_END)) { //是否第一次战斗
            return { treasureIds: [{ id: 1002, weight: 100 }], treasureCounts: [1, 1], specialTreasureId: specialTreasureId };
        }
        var treasureIdPrefix = landType * 100;
        var weights = ut.stringToNumbers(json.treasures_lv, ',');
        weights.forEach(function (m, i) {
            if (m > 0) {
                treasureIds.push({ id: treasureIdPrefix + (i + 1), weight: m });
            }
        });
        treasureCounts = ut.stringToNumbers(json.treasures_count, ',');
        // 特殊资源
        if (json.other_rewards_odds) {
            var odds = ut.stringToNumbers(json.other_rewards_odds, ',');
            if (ut.chance(odds[0])) {
                specialTreasureId = treasureIdPrefix + 13;
            }
        }
        return { treasureIds: treasureIds, treasureCounts: treasureCounts, specialTreasureId: specialTreasureId };
    };
    NoviceServerModel.prototype.addPawnTreasure = function (pawn, cnt, id) {
        var _a;
        if (cnt <= 0 || id <= 0) {
            return 0;
        }
        var bagCap = ((_a = pawn.baseJson) === null || _a === void 0 ? void 0 : _a.bag_cap) || 0;
        var count = bagCap - pawn.treasures.length;
        count = Math.min(count, cnt);
        for (var i = 0; i < count; i++) {
            pawn.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo({
                uid: ut.UID(),
                id: id,
                rewards: [],
            }, pawn.aIndex, pawn.armyUid, pawn.uid));
        }
        return count;
    };
    // 检测玩家是否有新的宝箱
    NoviceServerModel.prototype.checkPlayerHasTreasure = function (uid) {
        var dist = this.getPlayerArmyDist(uid);
        for (var key in dist) {
            var area = this.areas[Number(key)];
            if (!area) {
                continue;
            }
            else if (area.armys.some(function (army) { return army.owner === uid && army.pawns.some(function (m) { return m.treasures.length > 0; }); })) {
                return true;
            }
        }
        return false;
    };
    // 通知玩家有新宝箱
    NoviceServerModel.prototype.sendPlayerHasTreasure = function (uid) {
        var has = this.checkPlayerHasTreasure(uid);
        if (this.hasTreasure !== has) {
            this.hasTreasure = has;
            this.notifyPlayer(Enums_1.NotifyType.NEW_TREASURE, has);
        }
    };
    NoviceServerModel.prototype.checkArmyAutoBack = function (area) {
        var _this = this;
        if (GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.CHECK_FIRST_BATTLE_END)) {
            return;
        }
        area.armys.forEach(function (army) {
            var _a;
            var autoBackIndex = (_a = _this.armyAutoBackIndexMap[army.uid]) !== null && _a !== void 0 ? _a : -1;
            if (army.state === Enums_1.ArmyState.MARCH || autoBackIndex === -1) {
                return;
            }
            var target = null;
            if (autoBackIndex === -2) { //返回到最近的
                target = _this.getPlayerNotFullArmyMinFortAndMain(army.owner, army.aIndex);
            }
            else {
                var a = _this.areas[autoBackIndex];
                if (a && !a.isBattleing() && GameHelper_1.gameHpr.checkIsOneAlliance(army.owner, a.owner) && !_this.isAreaFullArmy(a, army.owner)) {
                    target = a;
                }
            }
            if (!target || target.index === area.index) {
                return;
            }
            var speed = 0;
            // 是否可以加速 城边3格内加速
            if (area.index === NoviceConfig_1.NOVICE_MAINCITY_INDEX || target.index === NoviceConfig_1.NOVICE_MAINCITY_INDEX) {
                var dis = GameHelper_1.gameHpr.getToMapCellDis(area.index, target.index);
                if (dis <= 5) {
                    speed -= Constant_1.MAIN_CITY_MARCH_SPEED[dis];
                }
            }
            // 添加行军
            _this.armyAutoBackIndexMap[army.uid] = -1;
            _this.addMarchArmy(area.index, army, target.index, speed, false);
        });
    };
    NoviceServerModel.prototype.getPlayerNotFullArmyMinFortAndMain = function (uid, startIndex) {
        if (!uid) {
            return null;
        }
        var minDis = 100000, ret = null;
        var cells = uid === this.puid ? this.ownCells : this.enemyCells;
        for (var key in cells) {
            var area = cells[key];
            if (!area.isBattleing() && area.isRecoverPawnHP() && !this.isAreaFullArmy(area, uid)) {
                var dis = GameHelper_1.gameHpr.getToMapCellDis(startIndex, area.index);
                if (dis < minDis) {
                    minDis = dis;
                    ret = area;
                }
            }
        }
        return ret;
    };
    NoviceServerModel.prototype.addArmyToBattle = function (area, army) {
        if (army.pawns.length === 0) {
            return;
        }
        var a = GameHelper_1.gameHpr.areaCenter.getArea(area.index), fsp = a === null || a === void 0 ? void 0 : a.getFspModel();
        if (!fsp) {
            return;
        }
        var currentFrameIndex = fsp.getCurrentFrameIndex();
        army.state = Enums_1.ArmyState.FIGHT;
        var fighters = [], isAttacker = army.owner !== area.owner;
        var pawnResObj = this.lastOccupyPawnRes[area.index];
        if (!pawnResObj) {
            pawnResObj = this.lastOccupyPawnRes[area.index] = {};
        }
        var isMyArmy = army.isOwner();
        army.pawns.forEach(function (p) {
            var _a;
            p.changeState(Enums_1.PawnState.STAND);
            fighters.push({
                uid: p.uid,
                camp: isAttacker ? 1 : 0,
                attackIndex: p.attackSpeed,
                waitRound: 1,
            });
            if (isMyArmy) {
                GameHelper_1.gameHpr.stringToCTypes((_a = p.baseJson) === null || _a === void 0 ? void 0 : _a.drill_cost).forEach(function (m) {
                    pawnResObj[m.type] = (pawnResObj[m.type] || 0) + m.count;
                });
            }
        });
        var attackIndex = 0;
        fsp.getBattleController().getFighters().forEach(function (m) {
            if (m.attackIndex > attackIndex) {
                attackIndex = m.attackIndex;
            }
        });
        fighters.sort(function (a, b) { return b.attackIndex - a.attackIndex; });
        fighters.forEach(function (m) { return m.attackIndex = ++attackIndex; });
        var frame = {
            type: 1,
            currentFrameIndex: currentFrameIndex,
            army: army.strip(),
            fighters: fighters,
        };
        fsp.onFSPCheckFrame(frame);
        this.setBattleRecord(1, area, frame);
    };
    //设置战斗记录
    NoviceServerModel.prototype.setBattleRecord = function (type, area, data) {
        for (var i = 0; i < this.battleRecordList.length; i++) {
            var record = this.battleRecordList[i];
            if (record.endTime > 0)
                continue;
            if (1 === type) {
                record.addFrame(area, data);
            }
            else if (2 === type) {
                record.battleEnd(area, data);
            }
            else if (3 === type) {
                record.setFighterData(data);
            }
        }
        if (2 === type && this.battleRecordList.length > this.battleRecordMaxCount) {
            this.battleRecordList.length = this.battleRecordMaxCount;
        }
    };
    NoviceServerModel.prototype.setArmyPawnPosition = function (area, army, dir) {
        var pawnCount = army.pawns.length;
        if (pawnCount == 0 || dir < 0 || dir >= 4) {
            return;
        }
        else if (GameHelper_1.gameHpr.checkIsOneAlliance(area.owner, army.owner)) { //在自己区域或者联盟区域的时候 直接设置在战斗区域
            army.setPawnPoints(area.getPawnPointsByDoor(army.uid, dir, pawnCount));
        }
        else {
            army.setPawnPoints([area.passPoints[dir]]); //设置到关口位置
        }
    };
    NoviceServerModel.prototype.adjustmentPawnPos = function (area) {
        area.armys.forEach(function (army) {
            if (army.state === Enums_1.ArmyState.MARCH) {
                return;
            }
            army.pawns.forEach(function (pawn) {
                var point = pawn.point;
                if (area.checkIsBattleArea(point.x, point.y) && !area.banPlacePawnPointMap[point.ID()]) {
                    return;
                }
                var dir = MapHelper_1.mapHelper.getMinDisIndex(point, area.doorPoints);
                var points = area.getPawnPointsByDoor('', dir, 1);
                if (points.length > 0) {
                    pawn.setPoint(points[0]);
                }
            });
        });
    };
    NoviceServerModel.prototype.addAvoidWarAreaEndTime = function (index, time, isAcc) {
        var now = Date.now();
        var endTime = this.avoidWarAreas[index];
        if (!endTime || !isAcc) {
            endTime = now + time;
        }
        else {
            endTime += time;
        }
        this.avoidWarAreas[index] = endTime;
        this.notifyWorld(Enums_1.NotifyType.AREA_AVOID_WAR, {
            index: index,
            time: endTime - now,
        });
    };
    NoviceServerModel.prototype.addBattleDist = function (area, init) {
        var uids = this.battleDist[area.index] = area.getHasArmyPlayers();
        !init && this.notifyWorld(Enums_1.NotifyType.BATTLE_DIST, { index: area.index, uids: uids });
    };
    NoviceServerModel.prototype.removeBattleDist = function (index) {
        delete this.battleDist[index];
        this.notifyWorld(Enums_1.NotifyType.BATTLE_DIST, { index: index });
    };
    NoviceServerModel.prototype.getBattleDistMap = function () { return this.battleDist; };
    NoviceServerModel.prototype.getAvoidWarDistMap = function () { return this.avoidWarAreas; };
    NoviceServerModel.prototype.getMarchs = function () { return this.marchs.map(function (m) { return m.strip(); }); };
    NoviceServerModel.prototype.getBTCityQueues = function () { return this.btCityQueues; };
    // 是否有到某个地方的行军
    NoviceServerModel.prototype.isCheckHasMarchByTarget = function (index) {
        return this.marchs.some(function (m) { return m.targetIndex === index; });
    };
    NoviceServerModel.prototype.getAreaAttacker = function (area) {
        var _a;
        var uid = area.owner;
        return ((_a = area.armys.find(function (m) { return m.state !== Enums_1.ArmyState.MARCH && !GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, uid); })) === null || _a === void 0 ? void 0 : _a.owner) || '';
    };
    NoviceServerModel.prototype.checkCanAddAreaArmy = function (area, owner, isRevoke) {
        if (!isRevoke && this.isAreaFullArmy(area, owner)) {
            return false; //如果当前区域没有这个军队且满了 就不能进
        }
        else if (GameHelper_1.gameHpr.checkIsOneAlliance(area.owner, owner)) {
            return true; //如果是联盟或者自己的土地就可以进入
        }
        // 如果在攻击范围内且不是免战的 就可以进入
        return !this.isAvoidWarArea(area.index) && this.checkCanOccupyCell(area.index, owner);
    };
    // 是否可以攻击
    NoviceServerModel.prototype.checkCanOccupyCell = function (index, uid) {
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(index);
        if (cell && GameHelper_1.gameHpr.checkIsOneAlliance(cell.owner, uid)) {
            return false;
        }
        var indexs = MapHelper_1.mapHelper.getOnePointOuter(cell.actPoint, cell.getSize());
        return indexs.some(function (m) { var _a; return !!GameHelper_1.gameHpr.checkIsOneAlliance((_a = GameHelper_1.gameHpr.world.getMapCellByPoint(m)) === null || _a === void 0 ? void 0 : _a.owner, uid); });
    };
    NoviceServerModel.prototype.isAvoidWarArea = function (index) {
        return !!this.avoidWarAreas[index];
    };
    NoviceServerModel.prototype.isAreaFullArmy = function (area, owner) {
        return this.getAreaFullArmyCount(area, owner) >= 0;
    };
    NoviceServerModel.prototype.isAreaFullArmyByIndex = function (index) {
        var area = this.getArea(index);
        return !area || this.isAreaFullArmy(area, this.puid);
    };
    NoviceServerModel.prototype.getAreaFullArmyCount = function (area, owner) {
        var isFriend = GameHelper_1.gameHpr.checkIsOneAlliance(area.owner, owner);
        var a = 0, b = 0;
        area.armys.forEach(function (m) {
            if (GameHelper_1.gameHpr.checkIsOneAlliance(area.owner, m.owner)) {
                a += 1;
            }
            else {
                b += 1;
            }
        });
        if (isFriend) {
            return a - area.maxArmyCount;
        }
        return b - area.maxArmyCount;
    };
    // 获取区域信息
    NoviceServerModel.prototype.HD_GetAreaInfo = function (data) {
        var index = data.index;
        var area = this.areas[index];
        return {
            err: '',
            data: { data: area.strip() }
        };
    };
    // 获取玩家军队列表
    NoviceServerModel.prototype.HD_GetPlayerArmys = function (type, ignoreIndex) {
        var _a, _b, _c;
        var uid = this.puid;
        var dist = this.getPlayerArmyDist(uid);
        var arr = [];
        for (var key in dist) {
            var index = Number(key);
            var area = (_b = (_a = this.areas[index]) === null || _a === void 0 ? void 0 : _a.proxyAO) !== null && _b !== void 0 ? _b : this.areas[index];
            if (!area || ignoreIndex === area.index) {
                continue;
            }
            area.armys.forEach(function (m) {
                if (m.owner !== uid || (type === 1 && !m.isCanBattle())) {
                    return;
                }
                arr.push(m.strip());
            });
        }
        return {
            err: '',
            data: { list: arr, canGotoCount: ((_c = this.getArea(ignoreIndex)) === null || _c === void 0 ? void 0 : _c.maxArmyCount) || 0 }
        };
    };
    NoviceServerModel.prototype.HD_MoveAreaPawns = function (data) {
        var area = this.areas[data.index];
        if (area) {
            var army_2 = area.getArmyByUid(data.armyUid);
            if (army_2) {
                data.pawns.forEach(function (m) { var _a; return (_a = army_2.pawns.find(function (p) { return p.uid === m.uid; })) === null || _a === void 0 ? void 0 : _a.setPoint(m.point); });
            }
        }
        return { err: '', data: null };
    };
    NoviceServerModel.prototype.HD_OpenTreasure = function (data) {
        var _a = this.getPawnTreasure(data.auid, data.puid, data.uid), area = _a.area, pawn = _a.pawn, treasure = _a.treasure;
        if (!area || !treasure || !pawn) {
            return { err: 'ecode.500076', data: null };
        }
        else if (treasure.rewards.length > 0) {
            return { err: 'ecode.500076', data: null };
        }
        var mul = 1 + this.getPolicyEffect(Enums_1.CEffect.TREASURE_AWARD) * 0.01;
        this.randomTreasureRewards(treasure, mul);
        var res = {
            armyUid: data.auid,
            uid: data.puid,
            treasures: pawn.treasures.map(function (m) {
                return {
                    uid: m.uid,
                    id: m.id,
                    rewards: m.rewards.map(function (r) { return r instanceof CTypeObj_1.default ? r.toJson() : r; })
                };
            }),
        };
        this.notifyArea(area.index, Enums_1.NotifyType.UPDATE_PAWN_TREASURE, res);
        res.rewards = treasure.rewards.map(function (m) { return m instanceof CTypeObj_1.default ? m.toJson() : m; });
        return { err: '', data: res };
    };
    NoviceServerModel.prototype.HD_ClaimTreasure = function (data) {
        var _a = this.getPawnTreasure(data.auid, data.puid, data.uid), area = _a.area, pawn = _a.pawn, treasure = _a.treasure;
        if (!area || !treasure || !pawn) {
            return { err: 'ecode.500076', data: null };
        }
        // 发放奖励
        this.changeCostByTypeObjs(treasure.rewards, 1);
        // 删除宝箱
        pawn.treasures.remove('uid', treasure.uid);
        // 通知是否有新的宝箱
        this.sendPlayerHasTreasure(this.puid);
        // 通知
        var res = {
            armyUid: data.auid,
            uid: data.puid,
            treasures: pawn.treasures.map(function (m) {
                return {
                    uid: m.uid,
                    id: m.id,
                    rewards: m.rewards.map(function (r) { return r instanceof CTypeObj_1.default ? r.toJson() : r; })
                };
            }),
        };
        this.notifyArea(area.index, Enums_1.NotifyType.UPDATE_PAWN_TREASURE, res);
        res.rewards = this.toItemByTypeObjs(treasure.rewards);
        this.setGuideTaskProgress(100012, 1);
        return { err: '', data: res };
    };
    NoviceServerModel.prototype.HD_AddAreaBuild = function (data) {
        if (this.btQueues.length >= this.getBTQueueMaxCount()) {
            return { err: 'ecode.500014', data: null };
        }
        var area = this.areas[data.index];
        if (!area || data.index !== NoviceConfig_1.NOVICE_MAINCITY_INDEX) {
            return { err: 'ecode.500044', data: null };
        }
        // 创建一个0级的
        var build = new BuildObj_1.default().init(data.index, ut.UID(), cc.v2(0, 0), data.id, 0);
        // 获取位置
        var point = area.getBuildCanPlacePos(build);
        if (!point) {
            return { err: 'ecode.500016', data: null }; //没有可摆放的位置了
        }
        else if (!this.checkAndDeductCostByTypeObjs(build.upCost)) {
            return { err: 'ecode.500012', data: null }; //扣除资源
        }
        // 设置位置
        build.point.set(point);
        // 添加到场景里面
        area.addBuild(build.strip(), false);
        // 添加到修建队列
        var costTime = Math.max(3, Math.floor(build.attrJson.bt_time * NoviceConfig_1.NOVICE_BUILD_CREATE_MUL));
        if (GameHelper_1.gameHpr.guide.buildAcc) {
            costTime = 3;
            GameHelper_1.gameHpr.guide.buildAcc = false;
        }
        this.putBTQueue(data.index, build.uid, build.id, 1, costTime);
        // 通知
        this.notifyArea(data.index, Enums_1.NotifyType.ADD_BUILD, build.strip());
        // 返回
        return {
            err: '',
            data: {
                build: build.strip(),
                output: this.toOutputInfo(),
                queues: this.btQueues.map(function (m) { return m.strip(); }),
            }
        };
    };
    NoviceServerModel.prototype.HD_UpAreaBuild = function (data) {
        if (this.btQueues.length >= this.getBTQueueMaxCount()) {
            return { err: 'ecode.500014', data: null };
        }
        var area = this.areas[data.index];
        if (!area) {
            return { err: 'ecode.500044', data: null };
        }
        var build = area.getBuildByUid(data.uid);
        if (!build) {
            return { err: 'ecode.500009', data: null };
        }
        else if ((build.id !== Constant_1.BUILD_MAIN_NID && build.id !== Constant_1.BUILD_BARRACKS_NID && build.id !== Constant_1.BUILD_SMITHY_NID && build.id !== Constant_1.BUILD_GRANARY_NID
            && build.id !== Constant_1.BUILD_WAREHOUSE_NID) || build.lv >= NoviceConfig_1.NOVICE_MAIN_MAX_LEVEL) { //新引导可以升级主城
            return { err: 'toast.novice_not_opt', data: null };
        }
        else if (this.btQueues.has('uid', build.uid)) { //修建中
            return { err: 'ecode.500013', data: null };
        }
        else if (build.isMaxLv()) { //是否已经满级
            return { err: 'ecode.500015', data: null };
        }
        else if (build.id !== Constant_1.BUILD_MAIN_NID) {
            var mb = area.getBuildById(Constant_1.BUILD_MAIN_NID);
            if (!mb || build.lv >= mb.lv) {
                return { err: 'ecode.500072', data: null }; //不是主城的话要检测是否超过主城等级
            }
        }
        // 扣除资源
        if (!this.checkAndDeductCostByTypeObjs(build.upCost)) {
            return { err: 'ecode.500012', data: null };
        }
        var time = build.attrJson.bt_time;
        // 添加到修建队列
        this.putBTQueue(data.index, build.uid, build.id, build.lv + 1, time);
        // 返回
        return {
            err: '',
            data: {
                output: this.toOutputInfo(),
                queues: this.btQueues.map(function (m) { return m.strip(); }),
            }
        };
    };
    NoviceServerModel.prototype.HD_CancelBT = function (data) {
        var area = this.areas[data.index];
        if (!area) {
            return { err: 'ecode.500044', data: null };
        }
        var build = area.getBuildByUid(data.uid);
        if (!build) {
            return { err: 'ecode.500009', data: null };
        }
        // 取消队列
        this.cancelBT(data.uid);
        var res = { queues: this.btQueues.map(function (m) { return m.strip(); }) };
        // 返回升到当前等级的资源
        if (build.lv > 0) {
            var attrId = build.id * 1000 + (build.lv - 1);
            var json = assetsMgr.getJsonData('buildAttr', attrId);
            if (json === null || json === void 0 ? void 0 : json.up_cost) {
                this.changeCostByTypeObjs(GameHelper_1.gameHpr.stringToCTypes(json.up_cost), 1); //退还资源
                res.output = this.toOutputInfo();
            }
        }
        else { //如果是0级就不返回资源 并删除掉
            area.removeBuild(data.uid, false);
            this.notifyArea(data.index, Enums_1.NotifyType.REMOVE_BUILD, data.uid);
        }
        return { err: '', data: res };
    };
    NoviceServerModel.prototype.HD_InDoneBt = function () {
        if (this.btQueues.length === 0) {
            return { err: '', data: { queues: [] } };
        }
        var gold = this.changePlayerGold(-Constant_1.IN_DONE_BT_GOLD);
        if (gold === -1) {
            return { err: 'ecode.500053', data: null }; //金币不足
        }
        // 完成所有
        this.completeAllBt();
        // 返回
        return {
            err: '',
            data: {
                queues: this.btQueues.map(function (m) { m.strip(); }),
                gold: gold,
            }
        };
    };
    NoviceServerModel.prototype.HD_UseUpScrollUpPawnLv = function (_a) {
        var index = _a.index, armyUid = _a.armyUid, uid = _a.uid;
        var area = this.areas[index];
        var army = area === null || area === void 0 ? void 0 : area.getArmyByUid(armyUid);
        if (!army || army.owner !== this.puid) {
            return { err: 'ecode.500017', data: null };
        }
        var pawn = army.pawns.find(function (m) { return m.uid === uid; });
        if (!pawn) {
            return { err: 'ecode.500017', data: null };
        }
        else if (area.isBattleing()) {
            return { err: 'ecode.500036', data: null }; //当前战斗中
        }
        else if (pawn.getPawnType() >= Enums_1.PawnType.MACHINE) {
            return { err: 'ecode.500017', data: null }; //器械不可升级
        }
        else if (pawn.isMaxLv()) {
            return { err: 'ecode.500015', data: null }; //已经满级
        }
        else if (this.checkPawnLving(pawn.uid)) {
            return { err: 'ecode.500079', data: null }; //已经在练级了
        }
        var json = pawn.attrJson;
        if (!json) {
            return { err: 'ecode.500017', data: null };
        }
        else if (!this.checkCTypes(GameHelper_1.gameHpr.stringToCTypes(json.lv_cond))) {
            return { err: 'ecode.500033', data: null };
        }
        var cost = [new CTypeObj_1.default().init(Enums_1.CType.UP_SCROLL, 0, 1)];
        if (!this.checkAndDeductCostByTypeObjs(cost)) { //扣除费用
            return { err: 'ecode.500012', data: null };
        }
        // 直接升级
        pawn.lv += 1;
        pawn.updateAttrJson();
        // 通知
        this.notifyArea(index, Enums_1.NotifyType.UPDATE_ARMY, army.strip());
        return {
            err: '', data: {
                cost: this.toItemByTypeObjs(cost),
                lv: pawn.lv,
            }
        };
    };
    // 获取免费招募剩余次数
    NoviceServerModel.prototype.getFreeRecruitPawnSurplusCount = function () {
        return Math.max(0, this.getPolicyEffect(Enums_1.CEffect.FREE_DRILL_COUNT) + 9 - this.freeRecruitPawnCount);
    };
    NoviceServerModel.prototype.HD_DrillPawn = function (_a) {
        var index = _a.index, buildUid = _a.buildUid, id = _a.id, armyUid = _a.armyUid, armyName = _a.armyName;
        var json = assetsMgr.getJsonData('pawnBase', id);
        if (!json) {
            return { err: 'ecode.500017', data: null };
        }
        var area = this.areas[index];
        if (!area || area.owner !== this.puid) {
            return { err: 'ecode.500017', data: null };
        }
        var build = area.getBuildByUid(buildUid);
        if (!build) {
            return { err: 'ecode.500009', data: null };
        }
        else if (json.spawn_build_id !== build.id) {
            return { err: 'ecode.500009', data: null };
        }
        else if (json.need_unlock === 1 && !this.isUnlockPawn(id)) { //是否需要解锁
            return { err: 'ecode.500017', data: null };
        }
        else if (this.isDrillPawnQueueFull(index, buildUid)) {
            return { err: 'ecode.500018', data: null };
        }
        var army = area.getArmyByUid(armyUid);
        var isFreeDrill = this.getFreeRecruitPawnSurplusCount() > 0;
        if (!army) { //这里如果没有 就说明要创建一个
            armyName = armyName || '???';
            if (armyUid) {
                return { err: 'ecode.500011', data: null };
            }
            else if (this.getPlayerArmyCount() >= this.getArmyMaxCount()) {
                return { err: 'ecode.500054', data: null }; //军队上限
            }
            else if (this.isAreaFullArmy(area, this.puid)) {
                return { err: 'ecode.500037', data: null }; //军队已满
            }
            else if (ut.getStringLen(armyName) > 12) {
                return { err: 'ecode.500049', data: null };
            }
            else if (!isFreeDrill && !this.checkCerealConsume(json.cereal_cost)) { //检测粮耗
                return { err: 'ecode.500082', data: null };
            }
            else if (!isFreeDrill && !this.checkAndDeductCostByTypeObjs(GameHelper_1.gameHpr.stringToCTypes(json.drill_cost))) { //扣除资源
                return { err: 'ecode.500012', data: null };
            }
            army = new ArmyObj_1.default().fromSvr({
                index: index,
                uid: ut.UID(),
                name: armyName,
                owner: this.puid,
            });
            army = this.addAreaArmy(area, army, -1);
        }
        else if (army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT) {
            return { err: 'ecode.500019', data: null };
        }
        else if (army.state === Enums_1.ArmyState.MARCH || army.aIndex !== index) {
            return { err: 'ecode.500011', data: null };
        }
        else if (!isFreeDrill && !this.checkCerealConsume(json.cereal_cost)) { //检测粮耗
            return { err: 'ecode.500082', data: null };
        }
        else if (!isFreeDrill && !this.checkAndDeductCostByTypeObjs(GameHelper_1.gameHpr.stringToCTypes(json.drill_cost))) { //扣除资源
            return { err: 'ecode.500012', data: null };
        }
        // 添加到军队 表示这个军队有训练的士兵
        army.drillPawns.push(id);
        // 添加到训练队列
        var time = json.drill_time;
        if (this.getFreeRecruitPawnSurplusCount() > 0) { //是否有加速
            this.freeRecruitPawnCount += 1;
            time = 3;
        }
        else {
            time = Math.max(3, Math.floor(time * NoviceConfig_1.NOVICE_RECRUIT_SPEED_MUL));
        }
        // 临时加速
        if (GameHelper_1.gameHpr.guide.drillAcc) {
            time = 3;
            GameHelper_1.gameHpr.guide.drillAcc = false;
        }
        // time = 3 //测试
        this.putDrillPawnQueue(index, build.uid, army.uid, id, 1, time, this.getPolicyEffect(Enums_1.CEffect.XL_CD));
        this.emit('DRILL_PAWN_START'); //给引导用的
        // 返回
        return {
            err: '', data: {
                output: this.toOutputInfo(),
                queues: this.toDrillPawnQueue(index),
                army: army.strip(),
                freeRecruitPawnCount: this.freeRecruitPawnCount,
            }
        };
    };
    NoviceServerModel.prototype.HD_CancelDrillPawn = function (_a) {
        var index = _a.index, buildUid = _a.buildUid, uid = _a.uid;
        var area = this.areas[index];
        if (!area) {
            return { err: 'ecode.500017', data: null };
        }
        else if (area.isBattleing()) {
            return { err: 'ecode.500036', data: null };
        }
        var info = this.getDrillPawnQueueInfo(index, buildUid, uid);
        if (!info) {
            return { err: 'ecode.500017', data: null };
        }
        var json = assetsMgr.getJsonData('pawnBase', info.id);
        if (!json) {
            return { err: 'ecode.500017', data: null };
        }
        var army = area.getArmyByUid(info.aUid);
        if (!army || army.owner !== this.puid) {
            return { err: 'ecode.500017', data: null };
        }
        var dInfo = this.cancelDrillPawnQueue(index, buildUid, uid);
        if (!dInfo) { // 取消
            return { err: 'ecode.500017', data: null };
        }
        // 返还资源
        if (dInfo.startTime === 0) {
            this.changeCostByTypeObjs(GameHelper_1.gameHpr.stringToCTypes(json.drill_cost), 1);
        }
        // 删除军队里面的
        army.drillPawns.remove(info.id);
        // 如果没有了 就删除
        if (army.getActPawnCount() === 0) {
            area.removeArmy(army.uid);
            this.notifyPlayerArmyDistInfo();
        }
        // 返回
        return {
            err: '', data: {
                output: this.toOutputInfo(),
                queues: this.toDrillPawnQueue(index),
                army: army.strip(),
            }
        };
    };
    NoviceServerModel.prototype.HD_MoveAreaBuild = function (_a) {
        var index = _a.index, uid = _a.uid, point = _a.point;
        var area = this.getArea(index);
        if (!area || area.owner !== this.puid) {
            return;
        }
        var build = area.getBuildByUid(uid);
        if (!build) {
            return;
        }
        else if (!area.checkCanPlace(build, point)) {
            return; //位置被占用
        }
        build.point.set(point); //直接改变位置
        this.notifyArea(index, Enums_1.NotifyType.MOVE_BUILD, { uid: uid, point: point }); //通知
    };
    NoviceServerModel.prototype.HD_ChangePawnArmy = function (_a) {
        var index = _a.index, armyUid = _a.armyUid, uid = _a.uid, newArmyUid = _a.newArmyUid, isNewCreate = _a.isNewCreate, attackSpeed = _a.attackSpeed, equipId = _a.equipId, skinId = _a.skinId;
        return __awaiter(this, void 0, Promise, function () {
            var area, preArmy, pawn, newArmy, equip;
            return __generator(this, function (_b) {
                area = this.getArea(index);
                preArmy = area === null || area === void 0 ? void 0 : area.getArmyByUid(armyUid);
                if (!preArmy || preArmy.owner != this.puid) {
                    return [2 /*return*/, { err: 'ecode.500017' }];
                }
                else if (area.isBattleing()) {
                    return [2 /*return*/, { err: 'ecode.500036' }]; //当前战斗中
                }
                pawn = preArmy.pawns.find(function (m) { return m.uid === uid; });
                if (!pawn) {
                    return [2 /*return*/, { err: 'ecode.500017' }];
                }
                newArmy = area.getArmyByUid(newArmyUid);
                if (isNewCreate) { //新建
                    if (!newArmyUid) {
                        return [2 /*return*/, { err: 'ecode.500011', data: null }];
                    }
                    else if (this.getPlayerArmyCount() >= this.getArmyMaxCount()) {
                        return [2 /*return*/, { err: 'ecode.500054', data: null }]; //军队上限
                    }
                    else if (this.isAreaFullArmy(area, this.puid)) {
                        return [2 /*return*/, { err: 'ecode.500037', data: null }]; //军队已满
                    }
                    else if (ut.getStringLen(newArmyUid) > 12) {
                        return [2 /*return*/, { err: 'ecode.500049', data: null }];
                    }
                    newArmy = new ArmyObj_1.default().init(area.index, this.puid, ut.UID());
                    newArmy.name = newArmyUid;
                    newArmy = this.addAreaArmy(area, newArmy, -1);
                }
                if (!newArmy || newArmy.owner !== this.puid) {
                    return [2 /*return*/, { err: 'ecode.500011' }];
                }
                else if (newArmy.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT) {
                    return [2 /*return*/, { err: 'ecode.500019' }]; //军队士兵已满
                }
                // 先从前一个军队中删除
                preArmy.removePawn(uid);
                if (preArmy.getActPawnCount() === 0) { //如果一个士兵都没有了
                    area.removeArmy(preArmy.uid);
                }
                else {
                    this.notifyArea(index, Enums_1.NotifyType.UPDATE_ARMY, preArmy.strip());
                }
                // 出手速度
                pawn.attackSpeed = attackSpeed;
                equip = this.getPlayerEquip(equipId);
                if (equip === null || equip === void 0 ? void 0 : equip.checkExclusivePawn(pawn.id)) {
                    pawn.changeEquip(equip.strip(), false);
                }
                // 添加到新的军队中
                newArmy.addPawn(pawn.strip());
                // 改变练级队列里面的士兵
                this.changePawnLvingInfo(pawn);
                this.notifyArea(index, Enums_1.NotifyType.UPDATE_ARMY, newArmy.strip());
                // 通知军队分布信息
                this.notifyPlayerArmyDistInfo();
                return [2 /*return*/, { data: { uid: newArmy.uid, name: newArmy.name, } }];
            });
        });
    };
    NoviceServerModel.prototype.HD_ChangeConfigPawnEquip = function (_a) {
        var id = _a.id, equipUid = _a.equipUid, skinId = _a.skinId;
        return __awaiter(this, void 0, void 0, function () {
            var equip;
            return __generator(this, function (_b) {
                // 装备
                if (equipUid) {
                    equip = this.getPlayerEquip(equipUid);
                    if (!equip || !equip.checkExclusivePawn(id)) {
                        return [2 /*return*/, { err: 'ecode.500059' }];
                    }
                }
                this.configPawnMap[id] = { equipUid: equipUid, skinId: skinId };
                return [2 /*return*/, {}];
            });
        });
    };
    NoviceServerModel.prototype.HD_ChangePawnAttr = function (_a) {
        var _b;
        var index = _a.index, armyUid = _a.armyUid, uid = _a.uid, attackSpeed = _a.attackSpeed, equipUid = _a.equipUid, skinId = _a.skinId, syncEquip = _a.syncEquip;
        return __awaiter(this, void 0, void 0, function () {
            var area, army, pawn, datas, equip, arr;
            return __generator(this, function (_c) {
                area = this.getArea(index), army = area === null || area === void 0 ? void 0 : area.getArmyByUid(armyUid);
                pawn = army === null || army === void 0 ? void 0 : army.pawns.find(function (m) { return m.uid === uid; });
                if (!pawn || pawn.owner !== this.puid) {
                    return [2 /*return*/, { err: 'ecode.500017' }];
                }
                else if (area.isBattleing()) {
                    return [2 /*return*/, { err: 'ecode.500036' }]; //当前战斗中
                }
                // 出手速度
                pawn.attackSpeed = attackSpeed;
                datas = [pawn.strip()];
                // 装备
                if (((_b = pawn.equip) === null || _b === void 0 ? void 0 : _b.uid) !== equipUid) {
                    equip = this.getPlayerEquip(equipUid);
                    if (equip && equip.checkExclusivePawn(pawn.id)) {
                        arr = area.changePawnEquip(pawn, equip, syncEquip);
                        if (arr.length > 0) {
                            datas = arr;
                        }
                    }
                    if (pawn.type === 2 && equip.id == this.equipSlots[Constant_1.EQUIP_SLOT_CONF[1]].id) {
                        this.setGuideTaskProgress(100023, 1);
                    }
                }
                // 通知
                this.notifyArea(index, Enums_1.NotifyType.CHANGE_PAWN_ATTR, { index: index, pawns: datas });
                return [2 /*return*/, {}];
            });
        });
    };
    NoviceServerModel.prototype.HD_PawnLving = function (_a) {
        var index = _a.index, auid = _a.auid, puid = _a.puid;
        return __awaiter(this, void 0, void 0, function () {
            var area, army, pawn, json;
            return __generator(this, function (_b) {
                area = this.getArea(index), army = area === null || area === void 0 ? void 0 : area.getArmyByUid(auid);
                if (!army || army.owner !== this.puid) {
                    return [2 /*return*/, { err: 'ecode.500017' }];
                }
                else if (area.isBattleing()) {
                    return [2 /*return*/, { err: 'ecode.500036' }]; //当前战斗中
                }
                else if (army.state === Enums_1.ArmyState.MARCH || army.aIndex !== index) {
                    return [2 /*return*/, { err: 'ecode.500011' }];
                }
                pawn = army.pawns.find(function (m) { return m.uid === puid; });
                if (!pawn) {
                    return [2 /*return*/, { err: 'ecode.500017' }];
                }
                else if (pawn.getPawnType() >= Enums_1.PawnType.MACHINE) { //器械不能训练
                    return [2 /*return*/, { err: 'ecode.500017' }];
                }
                else if (pawn.isMaxLv()) {
                    return [2 /*return*/, { err: 'ecode.500015' }];
                }
                else if (this.isPawnLvingQueueFull(index)) {
                    return [2 /*return*/, { err: 'ecode.500101' }];
                }
                else if (this.isInLvingQueue(index, puid)) {
                    return [2 /*return*/, { err: 'ecode.500079' }];
                }
                json = pawn.attrJson;
                if (!this.checkCTypesNeedArea(GameHelper_1.gameHpr.stringToCTypes(json.lv_cond), area.index)) {
                    return [2 /*return*/, { err: 'ecode.500033' }];
                }
                else if (!this.checkAndDeductCostByTypeObjs(pawn.upCost)) { //扣除资源
                    return [2 /*return*/, { err: 'ecode.500012' }];
                }
                // 添加到训练队列
                this.putPawnLvingQueue(index, army.uid, pawn.uid, pawn.id, pawn.lv + 1, json.lv_time, this.getPolicyEffect(Enums_1.CEffect.UPLVING_CD));
                // 返回
                return [2 /*return*/, {
                        data: {
                            cost: this.toItemByTypeObjs(pawn.upCost),
                            queues: this.toPawnLvingQueue(index),
                        }
                    }];
            });
        });
    };
    NoviceServerModel.prototype.HD_CancelPawnLving = function (_a) {
        var index = _a.index, uid = _a.uid;
        var area = this.getArea(index);
        if (!area) {
            return { err: 'ecode.500017' };
        }
        else if (area.isBattleing()) {
            return { err: 'ecode.500036' }; //当前战斗中
        }
        var info = this.getPawnLvingQueueInfo(index, uid);
        if (!info) {
            return { err: 'ecode.500017' };
        }
        var army = area.getArmyByUid(info.auid);
        if (!army || army.owner !== this.puid) {
            return { err: 'ecode.500017' };
        }
        var pawn = army.pawns.find(function (m) { return m.uid === info.puid; });
        if (!pawn) {
            return { err: 'ecode.500017' };
        }
        var lInfo = this.cancelPawnLvingQueue(index, uid);
        if (!lInfo) { //取消
            return { err: 'ecode.500017' };
        }
        // 返还资源
        if (lInfo.startTime === 0) {
            this.changeCostByTypeObjs(pawn.upCost, 1);
        }
        // 返回
        return {
            data: {
                cost: this.toItemByTypeObjs(pawn.upCost),
                queues: this.toPawnLvingQueue(index),
            }
        };
    };
    NoviceServerModel.prototype.HD_ForgeEquip = function (_a) {
        var uid = _a.uid, lockEffect = _a.lockEffect;
        if (this.currForgeEquip) {
            return { err: 'ecode.500058' };
        }
        var recastCount = 0; //重铸次数
        var isFreeForge = false;
        var cost = [];
        var equip = this.getPlayerEquip(uid);
        var json = null;
        var id = 0;
        if (equip) { //表示重铸
            json = equip.json;
            id = equip.id;
            recastCount = equip.recastCount;
            isFreeForge = equip.nextForgeFree;
            if (lockEffect > 0 && equip.exclusive_pawn > 0 && equip.getEffectByType(lockEffect)) {
                cost.push(new CTypeObj_1.default().init(Enums_1.CType.FIXATOR, 0, 1));
            }
            else {
                lockEffect = 0;
            }
            this.setGuideTaskProgress(100029, 1);
        }
        else {
            id = Number(uid.split('_')[0]);
            json = assetsMgr.getJsonData('equipBase', id);
        }
        if (!json) {
            return { err: 'ecode.500059' };
        }
        //  else if (!this.isUnlockEquip(id)) { //是否需要解锁
        //     return { err: 'ecode.500059' }
        // }
        var time = json.forge_time;
        time = Math.max(3, Math.floor(time * NoviceConfig_1.NOVICE_FORGE_SPEED_MUL));
        // 临时加速
        if (GameHelper_1.gameHpr.guide.forgeAcc) {
            time = 3;
            GameHelper_1.gameHpr.guide.forgeAcc = false;
        }
        var s2c = {};
        if (!isFreeForge) {
            cost.pushArr(GameHelper_1.gameHpr.stringToCTypes(json.forge_cost));
        }
        else {
            time = 3; //这里将时间直接缩短
        }
        if (cost.length === 0) {
        }
        else if (!this.checkAndDeductCostByTypeObjs(cost)) { //扣除费用
            return { err: 'ecode.500012' };
        }
        else {
            s2c.cost = this.toItemByTypeObjs(cost);
        }
        // 开始打造
        this.forgeEquip(uid, time, lockEffect);
        s2c.currForgeEquip = this.toForgeEquipData();
        // 下次是否免费
        if (equip) {
            equip.nextForgeFree = ut.chance(this.getPolicyEffect(Enums_1.CEffect.FREE_RECAST));
            s2c.nextForgeFree = equip.nextForgeFree;
        }
        this.emit('FORGE_EQUIP_START');
        return { data: s2c };
    };
    NoviceServerModel.prototype.HD_RestoreForge = function (data) {
        if (this.currForgeEquip) {
            return { err: 'ecode.500058' };
        }
        var equip = this.getPlayerEquip(data.uid);
        if (!equip) {
            return { err: 'ecode.500059' };
        }
        else if (equip.lastAttrs.length === 0) {
            return { err: 'ecode.500091' };
        }
        var json = equip.json;
        if (!json) {
            return { err: 'ecode.500059' };
        }
        else if (!this.checkAndDeductCostByTypeObjs([new CTypeObj_1.default().init(Enums_1.CType.IRON, 0, json.restore_cost)])) { //扣除费用
            return { err: 'ecode.500012' };
        }
        // 还原
        this.restoreEquipAttr(equip);
        // 更新玩家装备到临时记录列表
        this.updatePlayerPawnEquipInfo(this.puid, equip.strip());
        // 返回
        return {
            data: {
                equip: equip.toDB(),
                iron: this.iron,
            }
        };
    };
    NoviceServerModel.prototype.HD_CreateCity = function (_a) {
        var index = _a.index, id = _a.id;
        var cell = this.getArea(index);
        if (!cell || cell.owner !== this.puid || cell.cityId !== 0) {
            return { err: 'ecode.500000' };
        }
        else if (this.checkCellBTCitying(index)) { //正在修建中
            return { err: 'ecode.500041' };
        }
        var json = assetsMgr.getJsonData('city', id);
        if (!json) {
            return { err: 'ecode.500009' };
        }
        else if (json.need_land > 0 && json.need_land !== cell.getLandType()) {
            return { err: 'ecode.500009' };
        }
        else if (!this.checkCTypes(GameHelper_1.gameHpr.stringToCTypes(json.bt_cond))) {
            return { err: 'ecode.500033' };
        }
        var count = this.getPlayerCityCountByID(id);
        if (count >= json.bt_count) {
            return { err: 'ecode.500043' }; //超出修建数量
        }
        else if (!this.checkAndDeductCostByTypeObjs(GameHelper_1.gameHpr.stringToCTypes(json.bt_cost))) { // 扣除资源
            return { err: 'ecode.500012' };
        }
        // 如果是要塞再看是否有加速修建要塞的政策
        var cd = 0;
        if (id === Constant_1.CITY_FORT_NID) {
            cd += this.getPolicyEffect(Enums_1.CEffect.CITY_BUILD_CD);
        }
        var time = Math.floor(json.bt_time * 1000 * (1 - cd * 0.01));
        // 加入队列
        this.putBTCityQueue(index, id, Math.max(1000, time));
        // 返回
        return { data: { output: this.toOutputInfo() } };
    };
    NoviceServerModel.prototype.HD_DismantleCity = function (data) {
        var cell = this.getArea(data.index);
        if (!cell || cell.owner !== this.puid || cell.cityId === 0) {
            return { err: 'ecode.500000' };
        }
        else if (this.checkCellBTCitying(data.index)) { //正在拆除中
            return { err: 'ecode.500042' };
        }
        var json = assetsMgr.getJsonData('city', cell.cityId);
        if (!json) {
            return { err: 'ecode.500009' };
        }
        // 加入队列
        this.putBTCityQueue(data.index, 0, json.dt_time * 1000);
        return { err: '', data: null };
    };
    NoviceServerModel.prototype.HD_StudySelect = function (_a) {
        var lv = _a.lv, id = _a.id, tp = _a.tp;
        var data = {};
        switch (tp) {
            case Enums_1.StudyType.POLICY:
                this.policySlots[lv].id = id;
                this.policySlots[lv].selectIds = [];
                this.policySlots[lv].init();
                data.slots = this.policySlots;
                this.checkPolicyWithType(this.policySlots[lv].type);
                break;
            case Enums_1.StudyType.PAWN:
                this.pawnSlots[lv].id = id;
                this.pawnSlots[lv].selectIds = [];
                this.pawnSlots[lv].init();
                data.slots = this.pawnSlots;
                break;
            case Enums_1.StudyType.EQUIP:
            case Enums_1.StudyType.EXCLUSIVE:
                this.equipSlots[lv].id = id;
                this.equipSlots[lv].selectIds = [];
                this.equipSlots[lv].init();
                data.slots = this.equipSlots;
                break;
        }
        this.resetNextSelect(tp, lv);
        this.emit('STUDY_SELECT_FINISH');
        return { err: null, data: data };
    };
    //重置下一级可以解锁的研究内容
    NoviceServerModel.prototype.resetNextSelect = function (type, curLv) {
        var cfgList = [];
        var slots = {};
        switch (type) {
            case Enums_1.StudyType.POLICY:
                cfgList = Constant_1.POLICY_SLOT_CONF;
                slots = this.policySlots;
                break;
            case Enums_1.StudyType.EQUIP:
                cfgList = Constant_1.EQUIP_SLOT_CONF;
                slots = this.equipSlots;
                break;
            case Enums_1.StudyType.PAWN:
                cfgList = Constant_1.PAWN_SLOT_CONF;
                slots = this.pawnSlots;
                break;
        }
        var unLockIdList = [];
        for (var i = 0; i < cfgList.length; i++) {
            var lv = cfgList[i];
            var slot = slots[lv];
            if (slot.id || lv < curLv) {
                unLockIdList.push(slot.id);
            }
            else if (curLv === lv) {
                if (0 === slot.selectIds.length) {
                    for (var j = 0; j < unLockIdList.length; j++) {
                        var index = slot.allSelectIds.indexOf(unLockIdList[j]);
                        if (index > -1) {
                            slot.allSelectIds.splice(index, 1);
                        }
                    }
                    slot.selectIds = this.randListCount(slot.allSelectIds);
                }
                break;
            }
        }
        return slots;
    };
    NoviceServerModel.prototype.HD_CeriStartStudy = function (_a) {
        var lv = _a.lv, id = _a.id;
        // const slot = this.getCeriSlotByLv(lv)
        // if (!slot || !slot.isCanStudy()) {
        //     return { err: 'ecode.500087' }
        // } else if (!slot.selectIds.some(m => m === id)) {
        //     return { err: 'ecode.500087' }
        // } else if (!this.checkPerCeriSlotIsStudy(lv)) {
        //     return { err: 'ecode.500088' } //需要研究上一个槽位
        // }
        // const json = assetsMgr.getJsonData('ceri', id)
        // if (!json) {
        //     return { err: 'ecode.500087' }
        // }
        // // 开始研究
        // slot.startStudy(id)
        // const s2c: any = { slot: slot.strip() }
        // // 检测是否有下一个槽位 给下一个槽位随机选择
        // const next = this.getCeriSlotByLv(lv + 1)
        // if (next && next.id === 0 && next.selectIds.length === 0) {
        //     next.selectIds = this.ceriRandomSelect(next.type, next.lv)
        //     s2c.next = next.strip()
        // }
        return { err: null, data: {} };
    };
    NoviceServerModel.prototype.HD_CeriResetSelect = function (data) {
        var slot = null;
        var slots = {};
        switch (data.tp) {
            case Enums_1.StudyType.POLICY:
                slots = this.policySlots;
                break;
            case Enums_1.StudyType.EQUIP:
                slots = this.equipSlots;
                break;
        }
        slot = slots[data.lv];
        if (!slot || !slot.isCanStudy()) {
            return { err: 'ecode.500087' };
        }
        else if (!this.checkPerSlotIsStudy(slot.lv, slots)) {
            return { err: 'ecode.500088' }; //需要研究上一个槽位
        }
        var selectIds = this.randListCount(slot.allSelectIds);
        return { err: null, data: { selectIds: selectIds } };
    };
    NoviceServerModel.prototype.HD_ClaimTaskReward = function (data) {
        if (!this.guideTasks.has('id', data.id)) {
            return { err: 'ecode.500057' };
        }
        var json = assetsMgr.getJsonData('guideTask', data.id);
        if (!json) {
            return { err: 'ecode.500057' };
        }
        var items = GameHelper_1.gameHpr.stringToCTypes(json.reward);
        // 发放道具
        this.changeCostByTypeObjs(items, 1);
        // 放入已完成列表 并获取下一个任务
        var index = this.guideTasks.findIndex(function (m) { return m.id === data.id; });
        if (json.next_id) {
            this.guideTasks[index] = { id: json.next_id, progress: 0 };
        }
        else {
            this.guideTasks.splice(index, 1);
        }
        // 通知任务奖励领取
        this.emit(EventType_1.default.GUIDE_TASK_REWARD_CLAIM, { id: data.id });
        // 上报
        TaHelper_1.taHelper.trackNovice('ta_rookieTask', { rookie_id: data.id, uid: GameHelper_1.gameHpr.getUid() });
        // 返回
        return {
            data: {
                rewards: this.toItemByTypeObjs(items),
                tasks: this.guideTasks.map(function (m) { return { id: m.id, progress: m.progress }; }),
            }
        };
    };
    // 检测更新研究
    NoviceServerModel.prototype.checkUpdateStudy = function (now) {
        // this.ceriSlots.forEach(slot => {
        //     if (slot.startTime == 0 || slot.needTime == 0) {
        //         return
        //     } else if (now - slot.startTime >= slot.needTime) {
        //         slot.startTime = 0
        //         slot.selectIds = []
        //         this.notifyPlayer(NotifyType.CERI_STUDY_DONE, slot.strip())
        //         // 检测是否有下一个槽位 给下一个槽位随机选择 但是目前其实在开始研究的时候就给下一个随机了 这里只是再次判断一下
        //         const next = this.getCeriSlotByLv(slot.lv + 1)
        //         if (next && next.id === 0 && next.selectIds.length === 0) {
        //             next.selectIds = this.ceriRandomSelect(next.type, next.lv)
        //             this.notifyPlayer(NotifyType.CERI_STUDY_DONE, next.strip())
        //         }
        //     }
        // })
    };
    NoviceServerModel.prototype.checkPerSlotIsStudy = function (lv, slots) {
        if (lv === 1) {
            return true;
        }
        for (var key in slots) {
            if (Number(key) < lv) {
                var slot = slots[key];
                if (slot.id === 0) {
                    return false;
                }
            }
        }
        return true;
    };
    NoviceServerModel.prototype.putBTCityQueue = function (index, id, time) {
        var info = new NoviceBTCityObj_1.default().init(index, id, time);
        this.btCityQueues.push(info);
        this.notifyWorld(Enums_1.NotifyType.ADD_BTCITY, info.strip()); //通知
    };
    // 检测更新建造队列
    NoviceServerModel.prototype.checkUpdateBTCityQueue = function (now) {
        for (var i = this.btCityQueues.length - 1; i >= 0; i--) {
            var m = this.btCityQueues[i];
            if (now - m.startTime < m.needTime) {
                continue;
            }
            // 删除
            this.btCityQueues.splice(i, 1);
            // 设置地块城市信息
            var area = this.getArea(m.aIndex);
            if (!area) {
                continue;
            }
            area.updateCity(m.cityId);
            area.updateSize();
            area.updateMaxHP(); //刷新最大血量
            // 如果是要塞恢复所有士兵血量
            if (area.isRecoverPawnHP() && !area.isBattleing()) {
                area.recoverAllPawnHP();
                this.notifyArea(area.index, Enums_1.NotifyType.UPDATE_ALL_PAWN_HP, area.armys.map(function (m) { return m.strip(); }));
            }
            // 刷新玩家产量
            this.updateOpSec(true);
            // 通知
            this.notifyWorld(Enums_1.NotifyType.REMOVE_BTCITY, {
                index: m.aIndex,
                cell: area.toBaseData(),
                builds: area.builds.map(function (m) { return m.strip(); }),
            });
        }
    };
    NoviceServerModel.prototype.getPlayerCityCountByID = function (id) {
        var count = 0;
        for (var key in this.ownCells) {
            var area = this.ownCells[key];
            if (area.cityId === id || this.getBTCityIdByIndex(area.index) === id) {
                count += 1;
            }
        }
        return count;
    };
    NoviceServerModel.prototype.checkCellBTCitying = function (index) {
        return this.getBTCityIdByIndex(index) !== -1;
    };
    NoviceServerModel.prototype.getBTCityIdByIndex = function (index) {
        var _a, _b;
        return (_b = (_a = this.btCityQueues.find(function (m) { return m.aIndex === index; })) === null || _a === void 0 ? void 0 : _a.cityId) !== null && _b !== void 0 ? _b : -1;
    };
    NoviceServerModel.prototype.forgeEquip = function (uid, time, lockEffect) {
        var cd = this.effects[Enums_1.CEffect.FORGE_CD] || 0; //这里享受 减少速度
        time = Math.max(Math.floor(time * 1000 * (1 - cd * 0.01)), 3000);
        this.currForgeEquip = {
            uid: uid,
            startTime: Date.now(),
            needTime: time,
            lockEffect: lockEffect,
        };
    };
    NoviceServerModel.prototype.toForgeEquipData = function () {
        var m = this.currForgeEquip;
        if (!m) {
            return null;
        }
        return {
            uid: m.uid,
            needTime: m.needTime,
            surplusTime: Math.max(m.needTime - (Date.now() - m.startTime), 0),
            lockEffect: m.lockEffect,
        };
    };
    // 刷新打造装备
    NoviceServerModel.prototype.checkUpdateForgeEquip = function (now) {
        if (!this.currForgeEquip) {
            return;
        }
        else if (now - this.currForgeEquip.startTime < this.currForgeEquip.needTime) {
            return;
        }
        var lockEffect = this.currForgeEquip.lockEffect;
        var uid = this.currForgeEquip.uid;
        this.currForgeEquip = null; //标记为nil
        var equip = this.forgePlayerEquip(uid, lockEffect);
        // 通知
        this.notifyPlayer(Enums_1.NotifyType.FORGE_EQUIP_RET, equip.toDB());
    };
    NoviceServerModel.prototype.forgePlayerEquip = function (uid, lockEffect) {
        var id = Number(uid.split('_')[0]);
        var equip = this.equips.find(function (m) { return m.id === id; });
        // 随机属性
        if (!equip) { //如果没有就表示是打造
            equip = new EquipInfo_1.default().init(uid, id, []);
            this.randomEquipAttr(equip);
            this.equips.push(equip);
        }
        else {
            this.randomEquipAttr(equip, lockEffect);
            equip.recastCount += 1;
            // 更新玩家装备到临时记录列表
            this.updatePlayerPawnEquipInfo(this.puid, equip.strip());
        }
        return equip;
    };
    NoviceServerModel.prototype.randomEquipAttr = function (equip, lockEffect) {
        var _a;
        // 先记录上一次的属性
        equip.lastAttrs = this.cloneEquipAttrs(equip.attrs);
        equip.attrs = [];
        //
        var lockAttr = [];
        if (lockEffect > 0 && equip.exclusive_pawn > 0) {
            lockAttr = ((_a = equip.lastAttrs.find(function (m) { return m.attr.length >= 2 && m.attr[0] === 2 && m.attr[1] === lockEffect; })) === null || _a === void 0 ? void 0 : _a.attr) || [];
        }
        // 主属性 0
        if (equip.json.hp) { // 1
            var arr = ut.stringToNumbers(equip.json.hp, ',');
            equip.attrs.push({ attr: [0, 1, ut.random(arr[0], arr[1])] });
        }
        if (equip.json.attack) { // 2
            var arr = ut.stringToNumbers(equip.json.attack, ',');
            equip.attrs.push({ attr: [0, 2, ut.random(arr[0], arr[1])] });
        }
        // 效果
        var effectIds = ut.stringToNumbers(equip.json.effect, '|');
        var effectIndexMap = {};
        effectIds.forEach(function (id, i) { return effectIndexMap[id] = i + 1; });
        var cnt = equip.json.effect_count;
        if (lockAttr && effectIndexMap[lockAttr[1]] > 0) {
            cnt = Math.max(cnt - 1, 1); //如果有锁定效果 这里就要少一个
            effectIds.remove(lockAttr[1]); //删除
        }
        else {
            lockAttr = null;
        }
        var ids = [];
        for (var i = 0; i < cnt && i < effectIds.length; i++) {
            var index = ut.random(0, effectIds.length - 1);
            ids.push(effectIds[index]);
            effectIds.splice(index, 1);
        }
        var effects = [];
        ids.forEach(function (id) {
            var json = assetsMgr.getJsonData('equipEffect', id);
            if (json) {
                var v = [2, id];
                var arr = ut.stringToNumbers(json.value, ',');
                if (arr.length === 2) {
                    v.push(ut.random(arr[0], arr[1]));
                }
                else {
                    v.push(0); //这里就算没有 也要push一个0因为 后面可能有概率
                }
                arr = ut.stringToNumbers(json.odds, ',');
                if (arr.length === 2) {
                    v.push(ut.random(arr[0], arr[1]));
                }
                effects.push(v);
            }
        });
        if (lockAttr) {
            effects.push(lockAttr);
        }
        effects.sort(function (a, b) { return effectIndexMap[a[1]] - effectIndexMap[b[1]]; }); //这里要排个序 必须根据配置顺序来
        effects.forEach(function (m) { return equip.attrs.push({ attr: m }); });
        equip.updateAttr();
    };
    NoviceServerModel.prototype.restoreEquipAttr = function (equip) {
        if (equip.lastAttrs.length > 0) {
            equip.attrs = this.cloneEquipAttrs(equip.lastAttrs);
            equip.lastAttrs = [];
            equip.updateAttr();
        }
    };
    NoviceServerModel.prototype.cloneEquipAttrs = function (attrs) {
        var ret = [];
        for (var i = 0, l = attrs.length; i < l; i++) {
            ret.push({ attr: attrs[i].attr.slice() });
        }
        return ret;
    };
    NoviceServerModel.prototype.updatePlayerPawnEquipInfo = function (uid, equip) {
        var dist = this.getPlayerArmyDist(uid);
        for (var key in dist) {
            var area = this.getArea(Number(key));
            if (!area || area.isBattleing()) { //这里在战斗不更新装备
                continue;
            }
            area.armys.forEach(function (army) {
                if (army.owner === uid) {
                    army.updatePawnEquipAttr(equip);
                }
            });
        }
    };
    // 从队列中删除
    NoviceServerModel.prototype.cancelPawnLvingQueue = function (index, uid) {
        var queue = this.getPawnLvingQueue(index);
        if (!queue) {
            return null;
        }
        for (var i = queue.length - 1; i >= 0; i--) {
            var d = queue[i];
            if (d.uid === uid) {
                delete this.pawnLvingQueues.pawnUIDMap[d.puid];
                queue.splice(i, 1);
                if (queue.length === 0) {
                    delete this.pawnLvingQueues.map[index];
                }
                else if (d.startTime > 0) {
                    queue[0].startTime = Date.now();
                }
                return d;
            }
        }
        return null;
    };
    NoviceServerModel.prototype.isPawnLvingQueueFull = function (index) {
        var queue = this.getPawnLvingQueue(index);
        return !!queue && queue.length >= 6;
    };
    NoviceServerModel.prototype.isInLvingQueue = function (index, uid) {
        var _a;
        return !!((_a = this.getPawnLvingQueue(index)) === null || _a === void 0 ? void 0 : _a.some(function (m) { return m.puid === uid; }));
    };
    NoviceServerModel.prototype.putPawnLvingQueue = function (index, auid, puid, id, lv, time, cd) {
        var queue = this.pawnLvingQueues.map[index];
        if (!queue) {
            this.pawnLvingQueues.map[index] = queue = [];
        }
        time = Math.floor(time * 1000 * (1 - cd * 0.01));
        var info = new NovicePawnLevelingObj_1.default().init(index, auid, puid, id, lv, time);
        queue.push(info);
        this.pawnLvingQueues.pawnUIDMap[info.puid] = info.uid;
        // 设置第一个的开始时间
        if (queue.length === 1) {
            queue[0].startTime = Date.now();
        }
    };
    // 检测更新士兵训练队列
    NoviceServerModel.prototype.checkUpdatePawnLvingQueue = function (now) {
        for (var key in this.pawnLvingQueues.map) {
            var queue = this.pawnLvingQueues.map[key];
            var m = queue[0]; //取出第一个开始
            if (now - m.startTime < m.needTime) {
                continue;
            }
            delete this.pawnLvingQueues.pawnUIDMap[m.puid];
            queue.shift();
            if (queue.length > 0) {
                queue[0].startTime = m.startTime + m.needTime;
            }
            else {
                delete this.pawnLvingQueues.map[key];
            }
            // 通知练级完成
            this.areaPawnLvingComplete(m.index, m.auid, m.puid, m.lv);
            // 通知队列信息
            this.notifyPlayer(Enums_1.NotifyType.PAWN_LEVELING_QUEUE, this.toPawnLvingQueue(Number(key)));
        }
    };
    NoviceServerModel.prototype.areaPawnLvingComplete = function (index, auid, puid, lv) {
        var area = this.getArea(index), army = area === null || area === void 0 ? void 0 : area.getArmyByUid(auid);
        if (!army) {
            return;
        }
        var pawn = army.pawns.find(function (m) { return m.uid === puid; });
        if (pawn) {
            pawn.lv = lv;
            pawn.updateAttrJson();
            this.notifyArea(area.index, Enums_1.NotifyType.UPDATE_ARMY, army.strip());
        }
    };
    NoviceServerModel.prototype.getPlayerEquip = function (uid) {
        return this.equips.find(function (m) { return m.uid === uid; });
    };
    NoviceServerModel.prototype.changePawnLvingInfo = function (pawn) {
        var uid = this.pawnLvingQueues.pawnUIDMap[pawn.uid];
        var info = this.getPawnLvingQueueInfo(pawn.aIndex, uid);
        if (info) {
            info.auid = pawn.armyUid;
            info.puid = pawn.uid;
            this.notifyPlayer(Enums_1.NotifyType.PAWN_LEVELING_QUEUE, this.toPawnLvingQueue(info.index));
        }
    };
    NoviceServerModel.prototype.getPawnLvingQueueInfo = function (index, uid) {
        var _a;
        if (!uid) {
            return null;
        }
        return (_a = this.getPawnLvingQueue(index)) === null || _a === void 0 ? void 0 : _a.find(function (m) { return m.uid === uid; });
    };
    NoviceServerModel.prototype.getPawnLvingQueue = function (index) {
        var _a;
        return (_a = this.pawnLvingQueues.map) === null || _a === void 0 ? void 0 : _a[index];
    };
    NoviceServerModel.prototype.toPawnLvingQueue = function (index) {
        var _a;
        return ((_a = this.getPawnLvingQueue(index)) === null || _a === void 0 ? void 0 : _a.map(function (m) { return m.strip(); })) || [];
    };
    NoviceServerModel.prototype.putDrillPawnQueue = function (index, buid, armyUid, id, lv, time, cd) {
        var _a, _b;
        var m = this.drillPawnQueues[index];
        if (!m) {
            this.drillPawnQueues[index] = m = {};
        }
        var queue = m[buid];
        if (!queue) {
            m[buid] = queue = [];
        }
        cd += (((_b = (_a = this.getAreaBuildEffect(index, buid)) === null || _a === void 0 ? void 0 : _a[Enums_1.CEffect.XL_CD]) === null || _b === void 0 ? void 0 : _b.value) || 0);
        time = time * 1000 * (1 - cd * 0.01);
        queue.push(new NoviceDrillPawnObj_1.default().init(index, buid, armyUid, id, lv, time));
        // 设置第一个的开始时间
        if (queue.length === 1) {
            queue[0].startTime = Date.now();
        }
    };
    // 从队列中删除
    NoviceServerModel.prototype.cancelDrillPawnQueue = function (index, buid, uid) {
        var queue = this.getDrillPawnQueue(index, buid);
        if (!queue) {
            return null;
        }
        for (var i = queue.length - 1; i >= 0; i--) {
            var m = queue[i];
            if (m.uid === uid) {
                queue.splice(i, 1);
                if (queue.length === 0) {
                    delete this.drillPawnQueues[index];
                }
                else if (m.startTime > 0) {
                    queue[0].startTime = Date.now();
                }
                return m;
            }
        }
        return null;
    };
    // 检测更新士兵训练队列
    NoviceServerModel.prototype.checkUpdateDrillPawnQueue = function (now) {
        for (var key in this.drillPawnQueues) {
            var index = Number(key), obj = this.drillPawnQueues[key];
            var update = false;
            for (var k in obj) {
                var queue = obj[k];
                var m = queue[0]; //取出第一个开始
                if (!m || now - m.startTime < m.needTime) {
                    continue;
                }
                update = true;
                queue.shift();
                if (queue.length > 0) {
                    queue[0].startTime = m.startTime + m.needTime;
                }
                else {
                    delete obj[k];
                }
                // 通知训练完成
                this.areaPawnDrillComplete(m.index, m.aUid, m.id, m.lv);
                //招募完成
                this.notifyPlayerArmyDistInfo();
            }
            if (!update) {
                continue;
            }
            // 通知队列信息
            var area = this.areas[index];
            if (area) {
                this.notifyPlayer(Enums_1.NotifyType.PAWN_DRILL_QUEUE, this.toDrillPawnQueue(index));
            }
            // 如果没有就删除
            if (ut.isEmptyObject(this.drillPawnQueues[index])) {
                delete this.drillPawnQueues[index];
            }
        }
    };
    NoviceServerModel.prototype.areaPawnDrillComplete = function (index, auid, id, lv) {
        var _a, _b, _c;
        var area = this.areas[index], army = area === null || area === void 0 ? void 0 : area.getArmyByUid(auid);
        if (!army) {
            return;
        }
        else if (ut.chance(this.getPolicyEffect(Enums_1.CEffect.XL_2LV))) {
            lv = 2; //有一定几率直接2级
        }
        // 删除训练中的
        army.drillPawns.remove(id);
        // 创建士兵
        var pawn = new PawnObj_1.default().init(id);
        pawn.uid = ut.UID();
        pawn.owner = army.owner;
        pawn.armyUid = army.uid;
        pawn.enterDir = army.enterDir;
        pawn.aIndex = army.index;
        pawn.setPoint(area.getDrillPawnPoint());
        // 添加装备
        var _d = this.getConfigPawnInfo(pawn.id), equipUid = _d.equipUid, equipAttrs = _d.equipAttrs, skinId = _d.skinId;
        pawn.changeEquip({ uid: equipUid, attrs: [{ attr: equipAttrs }] }, false);
        pawn.skinId = skinId;
        // 添加士兵
        army.addPawn(pawn);
        // 主动刷新玩家粮耗
        this.updateOpSec(true);
        // 招募历史数量
        var hisCount = this.drillHistory[id] || 0;
        this.drillHistory[id] = hisCount + 1;
        // 战斗中的话 就不通知 用帧同步通知
        if (!area.isBattleing()) {
            this.notifyArea(area.index, Enums_1.NotifyType.UPDATE_ARMY, army.strip());
        }
        this.emit(EventType_1.default.GUIDE_PAWN_DRILLED);
        // 上报
        TaHelper_1.taHelper.trackNovice('ta_recruitPawn_rookie', { role_id: id, uid: GameHelper_1.gameHpr.getUid() });
        //招募任务
        this.setGuideTaskProgress(100001, 1);
        //招募远程士兵任务
        var range = ((_a = assetsMgr.getJsonData('pawnAttr', id * 1000 + 1)) === null || _a === void 0 ? void 0 : _a.attack_range) || 0;
        if (range > 1) {
            this.setGuideTaskProgress(100005, 1);
            this.setGuideTaskProgress(100020, 1);
        }
        //招募英雄化身士兵
        if (id === ((_c = (_b = this.heroSlots[0]) === null || _b === void 0 ? void 0 : _b.hero) === null || _c === void 0 ? void 0 : _c.avatarPawn)) {
            this.setGuideTaskProgress(100999, 1);
        }
    };
    NoviceServerModel.prototype.getConfigPawnInfo = function (pawnId) {
        var _a;
        var equipUid = '', skinId = 0, equipAttrs = [];
        var info = this.configPawnMap[pawnId];
        if (info) {
            equipUid = info.equipUid;
            skinId = info.skinId;
        }
        if (equipUid) {
            equipAttrs = ((_a = this.equips.find(function (m) { return m.uid === equipUid; })) === null || _a === void 0 ? void 0 : _a.mainAttrs) || [];
        }
        return { equipUid: equipUid, skinId: skinId, equipAttrs: equipAttrs };
    };
    NoviceServerModel.prototype.checkCerealConsume = function (count) {
        return this.cereal.opHour >= count;
    };
    NoviceServerModel.prototype.getPlayerArmyCount = function () {
        var dist = this.armysMap[this.puid] || {};
        return Object.keys(dist).length;
    };
    NoviceServerModel.prototype.getArmyMaxCount = function () {
        return (this.effects[Enums_1.CEffect.ARMY_COUNT] || 0) + this.getPolicyEffect(Enums_1.CEffect.ARMY_COUNT);
    };
    NoviceServerModel.prototype.isDrillPawnQueueFull = function (index, uid) {
        var queue = this.getDrillPawnQueue(index, uid);
        return !!queue && queue.length >= 6;
    };
    NoviceServerModel.prototype.getDrillPawnQueue = function (index, uid) {
        var _a;
        return (_a = this.drillPawnQueues[index]) === null || _a === void 0 ? void 0 : _a[uid];
    };
    NoviceServerModel.prototype.getDrillPawnQueueInfo = function (index, buid, uid) {
        var _a;
        return (_a = this.getDrillPawnQueue(index, buid)) === null || _a === void 0 ? void 0 : _a.find(function (m) { return m.uid === uid; });
    };
    NoviceServerModel.prototype.toDrillPawnQueue = function (index) {
        var ret = {};
        var obj = this.drillPawnQueues[index];
        if (!obj) {
            return ret;
        }
        for (var key in obj) {
            ret[key] = { list: obj[key].map(function (m) { return m.strip(); }) };
        }
        return ret;
    };
    NoviceServerModel.prototype.updatePolicyEffect = function (effectMap) {
        var RES_OUTPUT = false, GW_CAP = false;
        for (var key in effectMap) {
            var type = Number(key);
            if (type == Enums_1.CEffect.RES_OUTPUT ||
                type == Enums_1.CEffect.RARE_RES_OUTPUT ||
                type == Enums_1.CEffect.MORE_RARE_RES ||
                type == Enums_1.CEffect.LV_UP_QUEUE) { //增加资源产量
                RES_OUTPUT = true;
            }
            else if (type == Enums_1.CEffect.GW_CAP) { //粮食和仓库容量
                GW_CAP = true;
            }
        }
        if (RES_OUTPUT) {
            this.updateOpSec(true);
        }
        if (GW_CAP) {
            this.notifyPlayer(Enums_1.NotifyType.OUTPUT, {
                granaryCap: this.getGranaryCap(),
                warehouseCap: this.getWarehouseCap(),
            });
        }
    };
    // 是否解锁某个政策
    NoviceServerModel.prototype.isUnlockPolicy = function (id) {
        // return this.ceriSlots.some(m => m.getActValue() === id)
    };
    // 是否解锁某个士兵
    NoviceServerModel.prototype.isUnlockPawn = function (id) {
        var _a;
        for (var key in this.pawnSlots) {
            if (this.pawnSlots[key].id === id) {
                return true;
            }
        }
        for (var key in this.heroSlots) {
            if (((_a = this.heroSlots[key].hero) === null || _a === void 0 ? void 0 : _a.json.avatar_pawn) === id) {
                return true;
            }
        }
        return false;
    };
    // 是否解锁某个装备
    NoviceServerModel.prototype.isUnlockEquip = function (id) {
        // return this.ceriSlots.some(m => m.getActValue() === id)
    };
    NoviceServerModel.prototype.getPolicyEffectMapBool = function () {
        var effectMap = {};
        for (var key in this.policySlots) {
            var slot = this.policySlots[key];
            if (slot.id > 0) {
                effectMap[slot.type] = true;
            }
        }
        return effectMap;
    };
    NoviceServerModel.prototype.checkPawnLving = function (uid) {
        var _a;
        return !!((_a = this.pawnLvingQueues.pawnUIDMap) === null || _a === void 0 ? void 0 : _a[uid]);
    };
    // 添加到建造队列
    NoviceServerModel.prototype.putBTQueue = function (index, uid, id, lv, time) {
        var cd = this.getPolicyEffect(Enums_1.CEffect.BUILD_CD) * 0.01;
        var mul = lv === 1 ? 1 : NoviceConfig_1.NOVICE_BUILD_SPEED_MUL;
        time = Math.floor((time / mul) * 1000.0 * (1.0 - cd));
        // time = 2000 //测试
        this.btQueues.push(new NoviceBTObj_1.default().init(index, uid, id, lv, time));
        // 设置第一个的开始时间
        if (this.btQueues.length === 1) {
            this.btQueues[0].startTime = Date.now();
        }
        // // 上报
        // taHelper.trackNovice('ta_building_rookie', { build: { build_id: id, build_lv: lv }, uid: gameHpr.getUid() })
    };
    NoviceServerModel.prototype.cancelBT = function (uid) {
        // 从队列中删除
        this.btQueues.remove('bUid', uid);
        // 如果取消的第一个 还有的话就设置开始时间
        if (this.btQueues.length > 0 && this.btQueues[0].startTime == 0) {
            this.btQueues[0].startTime = Date.now();
        }
    };
    NoviceServerModel.prototype.completeAllBt = function () {
        var _this = this;
        this.btQueues.forEach(function (m) { return _this.updateBTComplete(m); });
        this.btQueues = []; //置空
    };
    NoviceServerModel.prototype.toOutputInfo = function () {
        return {
            granaryCap: this.getGranaryCap(),
            warehouseCap: this.getWarehouseCap(),
            cereal: this.cereal.strip(),
            timber: this.timber.strip(),
            stone: this.stone.strip(),
        };
    };
    // 获取修建队列数量
    NoviceServerModel.prototype.getBTQueueMaxCount = function () {
        return this.getPolicyEffect(Enums_1.CEffect.BT_QUEUE) + Constant_1.DEFAULT_BT_QUEUE_COUNT;
    };
    NoviceServerModel.prototype.toItemByTypeObjs = function (tos) {
        var _this = this;
        var items = {};
        tos.forEach(function (m) {
            if (m.type === Enums_1.CType.CEREAL) {
                items.cereal = _this.cereal.strip();
            }
            else if (m.type === Enums_1.CType.TIMBER) {
                items.timber = _this.timber.strip();
            }
            else if (m.type === Enums_1.CType.STONE) {
                items.stone = _this.stone.strip();
            }
            else if (m.type === Enums_1.CType.EXP_BOOK) {
                items.expBook = _this.expBook;
            }
            else if (m.type === Enums_1.CType.IRON) {
                items.iron = _this.iron;
            }
            else if (m.type === Enums_1.CType.UP_SCROLL) {
                items.upScroll = _this.upScroll;
            }
            else if (m.type === Enums_1.CType.FIXATOR) {
                items.fixator = _this.fixator;
            }
        });
        return items;
    };
    // 是否满资源
    NoviceServerModel.prototype.isFullRes = function () {
        var granaryCap = this.getGranaryCap(), warehouseCap = this.getWarehouseCap();
        return this.cereal.value >= granaryCap && this.timber.value >= warehouseCap && this.stone.value >= warehouseCap;
    };
    // 扣除资源
    NoviceServerModel.prototype.changeCostByTypeObjs = function (tos, change) {
        var _this = this;
        tos.forEach(function (m) { return _this.changeCostByTypeObjOne(m, change); });
    };
    NoviceServerModel.prototype.changeCostByTypeObjOne = function (m, change) {
        var count = m.count * change, add = 0, val = 0;
        if (m.type === Enums_1.CType.CEREAL) {
            add = this.cereal.change(count, this.getGranaryCap());
            val = this.cereal.value;
        }
        else if (m.type === Enums_1.CType.TIMBER) {
            add = this.timber.change(count, this.getWarehouseCap());
            val = this.timber.value;
        }
        else if (m.type === Enums_1.CType.STONE) {
            add = this.stone.change(count, this.getWarehouseCap());
            val = this.stone.value;
        }
        else if (m.type === Enums_1.CType.EXP_BOOK) {
            add = this.changeExpBook(count);
            val = this.expBook;
        }
        else if (m.type === Enums_1.CType.IRON) {
            add = this.changeIron(count);
            val = this.iron;
        }
        else if (m.type === Enums_1.CType.UP_SCROLL) {
            add = this.changeUpScroll(count);
            val = this.upScroll;
        }
        else if (m.type === Enums_1.CType.FIXATOR) {
            add = this.changeFixator(count);
            val = this.fixator;
        }
        else if (m.type === Enums_1.CType.BUILD_LV) {
            // this.SetBuildLv(m.id, m.count)
            val = m.count;
        }
        return { add: add, val: val };
    };
    // 检测资源是否满足
    NoviceServerModel.prototype.checkCostByTypeObjs = function (tos) {
        for (var i = 0, l = tos.length; i < l; i++) {
            if (!this.checkCostByTypeObjOne(tos[i])) {
                return false;
            }
        }
        return true;
    };
    NoviceServerModel.prototype.checkCostByTypeObjOne = function (m) {
        if (m.type == Enums_1.CType.CEREAL) {
            return this.cereal.value >= m.count;
        }
        else if (m.type == Enums_1.CType.TIMBER) {
            return this.timber.value >= m.count;
        }
        else if (m.type == Enums_1.CType.STONE) {
            return this.stone.value >= m.count;
        }
        else if (m.type == Enums_1.CType.EXP_BOOK) { //经验书
            return this.expBook >= m.count;
        }
        else if (m.type == Enums_1.CType.CEREAL_C) { //粮耗
            return this.cereal.opHour >= m.count;
        }
        else if (m.type == Enums_1.CType.IRON) { //铁
            return this.iron >= m.count;
        }
        else if (m.type == Enums_1.CType.UP_SCROLL) { //卷轴
            return this.upScroll >= m.count;
        }
        else if (m.type == Enums_1.CType.FIXATOR) { //固定器
            return this.fixator >= m.count;
        }
        return false;
    };
    // 检测通用条件 需要区域
    NoviceServerModel.prototype.checkCTypesNeedArea = function (cts, index) {
        for (var i = 0, l = cts.length; i < l; i++) {
            if (!this.checkCTypeOneNeedArea(cts[i], index)) {
                return false;
            }
        }
        return true;
    };
    NoviceServerModel.prototype.checkCTypes = function (cts) {
        return this.checkCTypesNeedArea(cts, NoviceConfig_1.NOVICE_MAINCITY_INDEX);
    };
    // 检测单个
    NoviceServerModel.prototype.checkCTypeOneNeedArea = function (ct, index) {
        var _a;
        if (ct.type == Enums_1.CType.CEREAL ||
            ct.type == Enums_1.CType.TIMBER ||
            ct.type == Enums_1.CType.STONE ||
            ct.type == Enums_1.CType.GOLD ||
            ct.type == Enums_1.CType.EXP_BOOK ||
            ct.type == Enums_1.CType.IRON ||
            ct.type == Enums_1.CType.UP_SCROLL ||
            ct.type == Enums_1.CType.FIXATOR) {
            return this.checkCostByTypeObjOne(ct);
        }
        else if (ct.type == Enums_1.CType.BUILD_LV) {
            return !!((_a = this.areas[index]) === null || _a === void 0 ? void 0 : _a.getBuildsById(ct.id).some(function (m) { return m.lv >= ct.count; }));
        }
        else if (ct.type == Enums_1.CType.CELL_COUNT) { //领地数
            var count = Object.keys(this.ownCells).length;
            if (ct.id === 1) {
                count = Math.max(0, count - 4);
            }
            return ct.count <= count;
        }
        return false;
    };
    NoviceServerModel.prototype.checkCTypeOne = function (ct) {
        return this.checkCTypeOneNeedArea(ct, NoviceConfig_1.NOVICE_MAINCITY_INDEX);
    };
    NoviceServerModel.prototype.checkAndDeductCostByTypeObjs = function (tos) {
        var b = this.checkCostByTypeObjs(tos);
        if (b) {
            this.changeCostByTypeObjs(tos, -1);
        }
        return b;
    };
    NoviceServerModel.prototype.changeExpBook = function (val) {
        var count = this.expBook + val;
        if (count < 0) {
            val = -this.expBook;
        }
        this.expBook += val;
        return val;
    };
    NoviceServerModel.prototype.changeIron = function (val) {
        var count = this.iron + val;
        if (count < 0) {
            val = -this.iron;
        }
        this.iron += val;
        return val;
    };
    NoviceServerModel.prototype.changeUpScroll = function (val) {
        var count = this.upScroll + val;
        if (count < 0) {
            val = -this.upScroll;
        }
        this.upScroll += val;
        return val;
    };
    NoviceServerModel.prototype.changeFixator = function (val) {
        var count = this.fixator + val;
        if (count < 0) {
            val = -this.fixator;
        }
        this.fixator += val;
        return val;
    };
    NoviceServerModel.prototype.changePlayerGold = function (val) {
        if (this.gold + val < 0) {
            return -1;
        }
        this.gold += val;
        return this.gold;
    };
    NoviceServerModel.prototype.randomTreasureRewards = function (treasure, mul) {
        var _this = this;
        var json = assetsMgr.getJsonData('treasure', treasure.id);
        if (!json) {
            return treasure;
        }
        var arr = ut.stringToNumbers(json.count, ',');
        var count = ut.random(arr[0], arr[1]);
        var items = json.rewards.split('|');
        if (items.length === count) {
            treasure.rewards = items.map(function (m) { return _this.stringToTypeObjRandCount(m, mul); });
            return treasure;
        }
        var weights = ut.stringToNumbers(json.weight, ',');
        var totalWeight = 0, cnt = weights.length;
        weights.forEach(function (m) { return totalWeight += m; });
        for (var i = 0; i < count; i++) {
            var offset = ut.random(0, totalWeight - 1);
            for (var ii = 0; ii < cnt; ii++) {
                var val = weights[ii];
                if (!val) {
                    continue;
                }
                else if (offset < val) {
                    totalWeight -= val;
                    weights[ii] = 0;
                    treasure.rewards.push(this.stringToTypeObjRandCount(items[ii], mul));
                    break;
                }
                else {
                    offset -= val;
                }
            }
        }
        if (treasure.rewards.length === 0) {
            treasure.rewards = [new CTypeObj_1.default().init(Enums_1.CType.CEREAL, 0, 50)];
        }
    };
    // 字符串转类型对象 随机个数
    NoviceServerModel.prototype.stringToTypeObjRandCount = function (str, mul) {
        var arr = ut.stringToNumbers(str, ',');
        if (arr.length !== 4) {
            return null;
        }
        return new CTypeObj_1.default().init(arr[0], arr[1], Math.floor(ut.random(arr[2], arr[3]) * mul));
    };
    NoviceServerModel.prototype.getPawnTreasure = function (auid, puid, uid) {
        var dist = this.getPlayerArmyDist(this.puid);
        for (var key in dist) {
            var area = this.areas[Number(key)];
            if (!area) {
                continue;
            }
            var army = area.getArmyByUid(auid);
            if (army) {
                var pawn = army.pawns.find(function (m) { return m.uid === puid; });
                if (pawn) {
                    return { area: area, pawn: pawn, treasure: pawn.treasures.find(function (m) { return m.uid === uid; }) };
                }
            }
        }
        return { area: null, pawn: null, treasure: null };
    };
    NoviceServerModel.prototype.cancelMarch = function (uid) {
        var march = this.marchs.find(function (m) { return m.uid === uid; });
        if (march) {
            var area = this.areas[march.armyIndex];
            var army = area === null || area === void 0 ? void 0 : area.getArmyByUid(march.armyUid);
            if (army) {
                this.armyAutoBackIndexMap[army.uid] = -1;
                this.cancelMarchArmy(march, false);
            }
        }
    };
    NoviceServerModel.prototype.cancelMarchArmy = function (march, auto) {
        var now = Date.now();
        march.autoRevoke = false;
        // 将目标变成起始位置
        march.startIndex = march.targetIndex;
        march.targetIndex = march.armyIndex;
        // 如果是自动遣返
        if (auto) {
            // 加下速
            march.needTime = Math.max(march.needTime / 2, 1000);
            march.startTime = now;
            this.marchs.push(march);
        }
        else {
            // 已经走的时间
            var elapsed = now - march.startTime;
            // 开始时间等于 当前时间减去剩余时间
            march.startTime = now - Math.max(march.needTime - elapsed, 0);
        }
        // 通知行军
        this.notifyWorld(Enums_1.NotifyType.ADD_MARCH, march.strip());
    };
    // 检测更新建造队列
    NoviceServerModel.prototype.checkUpdateBTQueue = function (now) {
        if (this.btQueues.length === 0) {
            return;
        }
        // 取出第一个开始
        var m = this.btQueues[0];
        if (now - m.startTime < m.needTime) {
            return;
        }
        this.btQueues.shift(); //删除
        if (this.btQueues.length > 0) {
            this.btQueues[0].startTime = m.startTime + m.needTime;
        }
        // 先通知队列
        this.notifyPlayer(Enums_1.NotifyType.BT_QUEUE, this.btQueues.map(function (m) { return m.strip(); }));
        // 刷新完成
        this.updateBTComplete(m);
    };
    // 通知建造升级
    NoviceServerModel.prototype.updateBTComplete = function (m) {
        var build = this.areaBuildBTComplete(m.aIndex, m.bUid);
        if (build) {
            this.notifyPlayer(Enums_1.NotifyType.BUILD_UP, build.strip()); //通知玩家
            this.updateBuildUpLvInfo(m.bid, m.bLv);
            // 上报
            TaHelper_1.taHelper.trackNovice('ta_building_rookie', { build: { build_id: build.id, build_lv: build.lv }, uid: GameHelper_1.gameHpr.getUid() });
            //通知研究更新
            var studyType = Enums_1.StudyType.NONE;
            var notifyType = Enums_1.NotifyType.NONE;
            if (build.id === Constant_1.BUILD_MAIN_NID) {
                studyType = Enums_1.StudyType.POLICY;
                notifyType = Enums_1.NotifyType.UPDATE_POLICY_SLOT;
            }
            else if (build.id === Constant_1.BUILD_BARRACKS_NID) {
                studyType = Enums_1.StudyType.PAWN;
                notifyType = Enums_1.NotifyType.UPDATE_PAWN_SLOT;
            }
            else if (build.id === Constant_1.BUILD_SMITHY_NID) {
                studyType = Enums_1.StudyType.EQUIP;
                notifyType = Enums_1.NotifyType.UPDATE_EQUIP_SLOT;
            }
            if (studyType !== Enums_1.StudyType.NONE) {
                var slots = this.resetNextSelect(studyType, build.lv);
                this.notifyPlayer(notifyType, slots); //通知玩家
            }
        }
    };
    NoviceServerModel.prototype.updateBuildUpLvInfo = function (id, lv) {
        // 刷新一下效果
        this.updateBuildEffect();
        // 如果是主城建筑 刷新需要重置的金币
        if (id === Constant_1.BUILD_MAIN_NID && Constant_1.POLICY_SLOT_CONF.has(lv)) {
            this.resetPolicyNeedGold = 0;
            this.notifyPlayer(Enums_1.NotifyType.UPDATE_RP_GOLD, this.resetPolicyNeedGold);
            // } else if (id === BUILD_CERI_NID) { //如果是研究所 刷新槽位信息
            //     this.updateCeriSlotInfo(lv, false)
        }
    };
    // 刷新建筑效果
    NoviceServerModel.prototype.updateBuildEffect = function () {
        var _this = this;
        var eos = this.getAreaAllBuildEffect(NoviceConfig_1.NOVICE_MAINCITY_INDEX);
        var merchantCount = 0;
        var granaryCap = this.getGranaryCap(), warehouseCap = this.getWarehouseCap();
        this.effects[Enums_1.CEffect.FORGE_CD] = 0; //减少打造时间
        this.effects[Enums_1.CEffect.ARMY_COUNT] = 0; //军队最大数量
        this.effects[Enums_1.CEffect.GRANARY_CAP] = 0; //粮食容量
        this.effects[Enums_1.CEffect.WAREHOUSE_CAP] = 0; //仓库容量
        eos.forEach(function (m) {
            if (m.type === Enums_1.CEffect.ALLIANCE_PERS) { //联盟人数
            }
            else if (m.type === Enums_1.CEffect.MERCHANT_COUNT) { //商人数量
                merchantCount += m.value;
            }
            else {
                _this.effects[m.type] += m.value;
            }
        });
        // 容量
        var newGranaryCap = this.getGranaryCap(), newWarehouseCap = this.getWarehouseCap();
        if (granaryCap != newGranaryCap || warehouseCap != newWarehouseCap) {
            this.notifyPlayer(Enums_1.NotifyType.OUTPUT, { granaryCap: newGranaryCap, warehouseCap: newWarehouseCap });
        }
        // 刷新商人数量
        if (merchantCount !== this.merchants.length) {
            this.updateMerchantCount(merchantCount);
        }
    };
    NoviceServerModel.prototype.getAreaAllBuildEffect = function (index) {
        var area = this.areas[index];
        var effects = [];
        area.builds.forEach(function (m) {
            if (m.lv !== 0 && m.effect) {
                effects.push(m.effect);
            }
        });
        return effects;
    };
    NoviceServerModel.prototype.getAreaBuildEffect = function (index, buid) {
        var effects = {};
        var area = this.areas[index];
        var build = area === null || area === void 0 ? void 0 : area.getBuildByUid(buid);
        if (build) {
            effects[build.effect.type] = build.effect;
        }
        return effects;
    };
    NoviceServerModel.prototype.updateMerchantCount = function (count) {
        while (this.merchants.length < count) {
            this.merchants.push({ state: 0 });
        }
        this.notifyPlayer(Enums_1.NotifyType.UPDATE_MERCHANT, this.merchants);
    };
    NoviceServerModel.prototype.areaBuildBTComplete = function (index, uid) {
        var area = this.areas[index];
        if (!area) {
            return null;
        }
        var build = area.getBuildByUid(uid);
        if (!build || build.isMaxLv()) {
            return null;
        }
        build.lv += 1;
        build.updateAttrJson();
        this.areaBuildUpComplete(area, build);
        return build;
    };
    NoviceServerModel.prototype.areaBuildUpComplete = function (area, build) {
        if (build.id == Constant_1.BUILD_WALL_NID) {
            this.towerLvMap[build.getBuildPawnId()] = build.lv;
            area.updateMaxHP();
            this.notifyWorld(Enums_1.NotifyType.PLAYER_TOWER_LV, {
                uid: this.puid,
                towerLvMap: this.towerLvMap,
            }); //通知
        }
        else if (build.id == 2012 || build.id == 2013) {
            this.towerLvMap[build.getBuildPawnId()] = build.lv;
            this.updateAreaMaxHP(build.id === 2013 ? 2102 : 0);
            this.notifyWorld(Enums_1.NotifyType.PLAYER_TOWER_LV, {
                uid: this.puid,
                towerLvMap: this.towerLvMap,
            }); //通知
        }
        this.notifyArea(area.index, Enums_1.NotifyType.BUILD_UP, build.strip());
    };
    // 刷新战斗中的血量
    NoviceServerModel.prototype.updateAreaMaxHP = function (id) {
        for (var key in this.ownCells) {
            var area = this.ownCells[key];
            var cityId = Math.abs(area.cityId);
            if (cityId === Constant_1.CITY_MAIN_NID) {
                continue;
            }
            else if (cityId !== Constant_1.CITY_FORT_NID) {
                cityId = 0; //只要不是要塞其他全都是哨站
            }
            if (cityId === id) {
                area.updateMaxHP();
            }
        }
    };
    NoviceServerModel.prototype.updateCeriSlotInfo = function (blv, init) {
        // let ok = false
        // for (let i = 0, l = CERI_SLOT_CONF.length; i < l; i++) {
        //     const { lv, type } = CERI_SLOT_CONF[i]
        //     const preLv = lv - 1
        //     const slot = this.ceriSlots.find(m => m.lv === lv)
        //     if (slot) {
        //         if (lv > blv) {
        //             slot.selectIds = []
        //         } else {
        //             const pre = this.getCeriSlotByLv(preLv)
        //             if ((pre && pre.id > 0) || lv == 1) {
        //                 if (slot.id > 0) {
        //                     slot.selectIds = []
        //                 } else {
        //                     slot.selectIds = this.ceriRandomSelect(slot.type, slot.lv)
        //                 }
        //             } else {
        //                 slot.selectIds = []
        //             }
        //         }
        //         ok = true
        //     } else if (lv <= blv) {
        //         const slot = new NoviceCeriSlotObj().init(lv, type)
        //         const pre = this.getCeriSlotByLv(preLv)
        //         if ((pre && pre.id > 0) || lv == 1) {
        //             slot.selectIds = this.ceriRandomSelect(slot.type, slot.lv)
        //         }
        //         this.ceriSlots.push(slot)
        //         ok = true
        //     }
        // }
        // if (ok && !init) {
        //     this.notifyPlayer(NotifyType.UPDATE_CERI_SLOT, this.ceriSlots.map(m => m.strip()))
        // }
    };
    // 获取槽位信息
    NoviceServerModel.prototype.getCeriSlotByLv = function (lv) {
        // if (lv < 1) {
        //     return null
        // }
        // return this.ceriSlots.find(m => m.lv === lv)
    };
    NoviceServerModel.prototype.ceriRandomSelect = function (type, lv) {
        // if (type == 4) {
        //     return this.ceriRandomExclusiveSelect(lv)
        // }
        // const idMap = {}
        // const preIdMap = {}
        // this.ceriSlots.forEach(m => { //当前研究槽位信息
        //     if (m.type !== type) {
        //         return
        //     }
        //     idMap[m.id] = true
        //     if (m.lv === lv) {
        //         m.selectIds.forEach(id => preIdMap[id] = true)
        //     } else if (m.id === 0) {
        //         m.selectIds.forEach(id => idMap[id] = true)
        //     }
        // })
        // // 删除已经解锁 或者已经随机出来的
        // let datas = assetsMgr.getJson('ceri').get('type', type)
        // datas.delete(m => idMap[m.id] || m.need_lv > lv)
        // return this.randomCeriIds(preIdMap, datas)
    };
    NoviceServerModel.prototype.ceriRandomExclusiveSelect = function (lv) {
        // const idMap = {}
        // const preIdMap = {}
        // // 初始士兵
        // const pawns = assetsMgr.getJson('pawnBase').datas.filter(m => m.spawn_build_id > 0 && !m.need_unlock)
        // pawns.forEach(m => idMap[m.id] = true)
        // // 获取已经有的士兵列表
        // this.ceriSlots.forEach(m => { //当前研究槽位信息
        //     if (m.lv === lv) {
        //         m.selectIds.forEach(id => preIdMap[id] = true)
        //     } else if (m.type === 2 && m.value > 0) {
        //         idMap[m.value] = true
        //     } else if (m.type === 4 && m.value > 0) { //已经获取的专属装备 要忽略
        //         const json = assetsMgr.getJsonData('equipBase', m.value)
        //         if (json) {
        //             idMap[json.exclusive_pawn] = false
        //         }
        //     }
        // })
        // // 删除已经解锁 或者已经随机出来的
        // const datas = assetsMgr.getJson('ceri').get('type', 4)
        // datas.delete(m => {
        //     const json = assetsMgr.getJsonData('equipBase', m.value)
        //     return !json || !idMap[json.exclusive_pawn]
        // })
        // return this.randomCeriIds(preIdMap, datas)
    };
    NoviceServerModel.prototype.randomCeriIds = function (preIdMap, datas) {
        if (datas.length <= 3) {
            return datas.map(function (m) { return m.id; });
        }
        var ids = [], cnt = 0;
        while (cnt < 50) {
            cnt += 1;
            var arr = datas.map(function (m) { return m; });
            var ok = false;
            for (var i = 0; i < 3 && arr.length > 0; i++) {
                var index = ut.randomIndexByWeight(arr, 'weight');
                var id = arr[index].id;
                ids.push(id);
                arr.splice(index, 1);
                if (!preIdMap[id]) {
                    ok = true;
                }
            }
            if (ok) {
                break;
            }
            ids = [];
        }
        return ids;
    };
    // 检测是否有相同的军队名字
    NoviceServerModel.prototype.checkArmyNameEqual = function (name) {
        var uid = this.puid;
        var dist = this.getPlayerArmyDist(uid);
        for (var key in dist) {
            var area = this.getArea(Number(key));
            if (!area) {
                continue;
            }
            for (var i = 0, l = area.armys.length; i < l; i++) {
                var army = area.armys[i];
                if (army.owner === uid && army.name === name) {
                    return true;
                }
            }
        }
        return false;
    };
    // 修复主城扣除资源
    NoviceServerModel.prototype.restoreMainCity = function () {
        var cost = [
            new CTypeObj_1.default().init(Enums_1.CType.CEREAL, 0, 60),
            new CTypeObj_1.default().init(Enums_1.CType.TIMBER, 0, 150),
            new CTypeObj_1.default().init(Enums_1.CType.STONE, 0, 150),
        ];
        this.checkAndDeductCostByTypeObjs(cost);
        this.notifyPlayer(Enums_1.NotifyType.OUTPUT, {
            cereal: this.cereal.strip(),
            timber: this.timber.strip(),
            stone: this.stone.strip(),
        });
    };
    // 改变体力
    NoviceServerModel.prototype.changeStamina = function (val) {
        if (this.stamina + val < 0) {
            return -1;
        }
        this.stamina += val;
        this.notifyPlayer(Enums_1.NotifyType.UPDATE_ITEMS, { stamina: this.stamina });
        return this.stamina;
    };
    NoviceServerModel.prototype.syncGuideTaskProgress = function () {
        var e_1, _a;
        var tasks = GameHelper_1.gameHpr.player.getGuideTasks();
        var _loop_2 = function (m) {
            var task = this_2.guideTasks.find(function (n) { return n.id === m.id; });
            if (task) {
                task.progress = m.cond.progress;
            }
        };
        var this_2 = this;
        try {
            for (var tasks_1 = __values(tasks), tasks_1_1 = tasks_1.next(); !tasks_1_1.done; tasks_1_1 = tasks_1.next()) {
                var m = tasks_1_1.value;
                _loop_2(m);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (tasks_1_1 && !tasks_1_1.done && (_a = tasks_1.return)) _a.call(tasks_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    // 设置引导任务进度
    NoviceServerModel.prototype.setGuideTaskProgress = function (taskId, val, isAdd) {
        if (isAdd === void 0) { isAdd = true; }
        // this.syncGuideTaskProgress()
        var task = this.guideTasks.find(function (m) { return m.id === taskId; });
        if (task) {
            if (isAdd) {
                task.progress += val;
            }
            else {
                task.progress = val;
            }
            // 刷新
            this.updateGuideTasksProgress();
        }
    };
    NoviceServerModel.prototype.getDistanceToMainCity = function (cell) {
        var t = MapHelper_1.mapHelper.indexToPoint(cell.index).clone();
        var s = MapHelper_1.mapHelper.getMinDis(t, NoviceConfig_1.NOVICE_MAINCITY_POINTS);
        return MapHelper_1.mapHelper.getPointToPointDis(s, t);
    };
    NoviceServerModel.prototype.getEnemyUID = function () {
        return this.enemyUid;
    };
    NoviceServerModel.prototype.addManulBattle = function (areaIndex) {
        if (!this.manulBattles[areaIndex]) {
            this.manulBattles[areaIndex] = { index: areaIndex, attacker: '' };
        }
    };
    // 获取最后攻占时的士兵资源字符串
    NoviceServerModel.prototype.getLastOccupyPawnResToStr = function (index) {
        var resObj = this.lastOccupyPawnRes[index];
        delete this.lastOccupyPawnRes[index];
        var str = '';
        for (var key in resObj) {
            if (str) {
                str += '|';
            }
            str += key + ',0,' + resObj[key];
        }
        return str;
    };
    NoviceServerModel.prototype.sendResources = function (rewardStr) {
        if (rewardStr === void 0) { rewardStr = '1,0,600|2,0,600|3,0,600'; }
        var ctypes = GameHelper_1.gameHpr.stringToCTypes(rewardStr);
        this.changeCostByTypeObjs(ctypes, 1);
        GameHelper_1.gameHpr.addGainMassage(ctypes);
        GameHelper_1.gameHpr.player.updateRewardItemsByFlags(this.toItemByTypeObjs(ctypes));
    };
    // 士兵能否训练
    NoviceServerModel.prototype.canDrillPawn = function (id, res) {
        var json = assetsMgr.getJsonData('pawnBase', id);
        var m = GameHelper_1.gameHpr.stringToCTypes(json.drill_cost);
        if (res) {
            for (var i = 0; i < m.length; i++) {
                var need = m[i];
                if (need.count > res[need.type - 1]) {
                    return false;
                }
            }
            return true;
        }
        else {
            return this.checkCostByTypeObjs(m);
        }
    };
    NoviceServerModel.prototype.isUserPawnDrilling = function () {
        var drillPawns = this.drillPawnQueues[NoviceConfig_1.NOVICE_MAINCITY_INDEX];
        return !!drillPawns;
    };
    // 是否正在修建
    NoviceServerModel.prototype.isBuilding = function (id) {
        return this.btQueues.some(function (m) { return m.bid === id; });
    };
    NoviceServerModel.prototype.debugCostRes = function (ctypeStr) {
        var cts = GameHelper_1.gameHpr.stringToCTypes(ctypeStr);
        this.changeCostByTypeObjs(cts, -1);
        GameHelper_1.gameHpr.player.updateRewardItemsByFlags(this.toItemByTypeObjs(cts));
    };
    NoviceServerModel.prototype.checkTreasure = function () {
        this.sendPlayerHasTreasure(this.puid);
    };
    NoviceServerModel.prototype.checkTask = function () {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var taskProgress, tempPawnState, isAllWearEquip, allArmys, i, j, pawn, key, key, key;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        taskProgress = { 100006: 0, 100017: 0, 100019: 0, 100022: 0, 100025: 0 };
                        (_b = (_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.forEach(function (cell) {
                            if (cell.index !== NoviceConfig_1.NOVICE_MAINCITY_INDEX && !NoviceConfig_1.NOVICE_MAINCITY_OTHER_INDEXS.includes(cell.index)) {
                                var dis = _this.getDistanceToMainCity(cell);
                                if (1 === cell.landLv && dis > 1) {
                                    //攻占更远的1级地
                                    taskProgress[100006] += 1;
                                }
                            }
                        });
                        tempPawnState = {};
                        isAllWearEquip = true;
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        allArmys = _c.sent();
                        for (i = 0; i < allArmys.length; i++) {
                            taskProgress[100025] += allArmys[i].pawns.length; //军队数量
                            for (j = 0; j < allArmys[i].pawns.length; j++) {
                                pawn = allArmys[i].pawns[j];
                                if (!pawn.equip || !pawn.equip.uid) {
                                    isAllWearEquip = false;
                                }
                                tempPawnState[pawn.id] = allArmys[i].state;
                            }
                        }
                        if (isAllWearEquip) {
                            taskProgress[100017] += 1;
                        }
                        for (key in this.equipSlots) {
                            if (this.equipSlots[key].id) {
                                taskProgress[100022] += 1; //研究装备
                            }
                        }
                        for (key in this.pawnSlots) {
                            if (this.configPawnMap[this.pawnSlots[key].id]) {
                                taskProgress[100019] += 1; //兵营士兵添加默认装备
                            }
                        }
                        // for (let key in this.drillHistory) {
                        //     let pawnId = Number(key)
                        //     let drillCount = this.drillHistory[key]
                        //     //招募远程士兵任务
                        //     const range = assetsMgr.getJsonData('pawnAttr', pawnId * 1000 + 1)?.attack_range || 0
                        //     if (range > 1) {
                        //         taskProgress[100020] += drillCount
                        //     }
                        //     //招募英雄化身士兵
                        //     if (tempPawnState[pawnId] === ArmyState.NONE && pawnId === this.heroSlots[0]?.hero?.avatarPawn) {
                        //         this.setGuideTaskProgress(100999, 1)
                        //     }
                        // }
                        for (key in taskProgress) {
                            this.setGuideTaskProgress(Number(key), taskProgress[key], false);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    //检查化身的英雄是否阵亡
    NoviceServerModel.prototype.checkHero = function () {
        for (var i = 0; i < this.heroSlots.length; i++) {
            var heroSlot = this.heroSlots[i];
            if (heroSlot.avatarArmyUID) {
                var armys = GameHelper_1.gameHpr.player.getArmyDistMap();
                for (var key in armys) {
                    var armyBaseList = armys[key];
                    for (var j = 0; j < armyBaseList.length; j++) {
                        var armyBase = armyBaseList[j];
                        for (var k = 0; k < armyBase.pawns.length; k++) {
                            var pawn = armyBase.pawns[k];
                            if (pawn.armyUid === heroSlot.avatarArmyUID) {
                                return;
                            }
                        }
                    }
                }
                // 英雄死亡
                heroSlot.avatarArmyUID = '';
                GameHelper_1.gameHpr.player.updateHeroSlotOne(heroSlot);
            }
        }
    };
    // 获取玩家士兵
    NoviceServerModel.prototype.getPawns = function () {
        var _a, _b;
        var uid = this.puid;
        var dist = this.getPlayerArmyDist(uid);
        var arr = [];
        for (var key in dist) {
            var index = Number(key);
            var area = (_b = (_a = this.areas[index]) === null || _a === void 0 ? void 0 : _a.proxyAO) !== null && _b !== void 0 ? _b : this.areas[index];
            area === null || area === void 0 ? void 0 : area.armys.forEach(function (m) {
                if (m.owner === uid) {
                    arr.pushArr(m.pawns);
                }
            });
        }
        return arr;
    };
    // 英雄自选礼包
    NoviceServerModel.prototype.genHeroReward = function (heroId, count) {
        var isFind = false;
        for (var i = 0; i < this.portrayals.length; i++) {
            if (this.portrayals[i].id === heroId) {
                this.portrayals[i].debris += count;
                isFind = true;
                break;
            }
        }
        if (!isFind) {
            var info = new PortrayalInfo_1.default().init(heroId);
            info.debris += count;
            this.portrayals.push(info);
        }
        GameHelper_1.gameHpr.user.setPortrayals(this.portrayals);
        //自动合成完整英雄
        var _a = this.HD_PortrayalComp({ id: heroId }), err = _a.err, data = _a.data;
        if (!err) {
            var portrayal = GameHelper_1.gameHpr.user.getPortrayals()[0];
            portrayal.updateInfo(data.info);
            GameHelper_1.gameHpr.user.checkHasCanCompPortrayal();
            GameHelper_1.gameHpr.player.updatePawnHeroAttr(portrayal.id, portrayal.attrs);
            eventCenter.emit(EventType_1.default.UPDATE_PORTRAYAL_INFO);
        }
    };
    //合成英雄
    NoviceServerModel.prototype.HD_PortrayalComp = function (data) {
        var info = null;
        for (var i = 0; i < this.portrayals.length; i++) {
            if (this.portrayals[i].id === data.id) {
                info = this.portrayals[i];
                break;
            }
        }
        var needDebris = 3;
        if (info && info.debris >= needDebris) {
            // info.lastAttrs = info.attrs
            info.attrs = [];
            var portrayalBase = assetsMgr.getJsonData('portrayalBase', data.id);
            var hpList = portrayalBase.hp.split(',');
            var hp = ut.random(Number(hpList[0]), Number(hpList[1]));
            info.attrs.push({ attr: [0, 1, hp] });
            var attackList = portrayalBase.attack.split(',');
            var attack = ut.random(Number(attackList[0]), Number(attackList[1]));
            info.attrs.push({ attr: [0, 2, attack] });
            var skillBase = assetsMgr.getJsonData('portrayalSkill', portrayalBase.skill);
            var skillList = skillBase.value.split(',');
            var skill = ut.random(Number(skillList[0]), Number(skillList[1]));
            info.attrs.push({ attr: [1, portrayalBase.skill, skill] });
            var strategyList = portrayalBase.strategy.split('|');
            var strategyRandList = this.randListCount(strategyList, 2);
            for (var i = 0; i < strategyRandList.length; i++) {
                info.attrs.push({ attr: [2, Number(strategyRandList[i])] });
            }
            info.recompCount++;
            info.debris -= needDebris;
            return { data: { info: info } };
        }
        return { err: null };
    };
    //供奉英雄
    NoviceServerModel.prototype.HD_WorshipHero = function (data) {
        var index = data.index, id = data.id;
        var curPortrayals = null;
        for (var i = 0; i < this.portrayals.length; i++) {
            if (this.portrayals[i].id === id) {
                curPortrayals = this.portrayals[i];
                break;
            }
        }
        this.heroSlots[index].hero = curPortrayals;
        this.emit('WORSHIP_HERO');
        return { err: null, data: { slot: this.heroSlots[index], pawnSlots: this.pawnSlots } };
    };
    //英雄化身
    NoviceServerModel.prototype.HD_ChangePawnPortrayal = function (data) {
        var index = data.index, armyUid = data.armyUid, uid = data.uid, portrayalId = data.portrayalId;
        var heroSlot = null;
        for (var i = 0; i < this.heroSlots.length; i++) {
            var slot = this.heroSlots[i];
            if (slot.hero.id === portrayalId) {
                heroSlot = slot;
                heroSlot.deadTime = 0;
                heroSlot.avatarArmyUID = armyUid;
            }
        }
        var armys = this.areas[index].armys;
        for (var i = 0; i < armys.length; i++) {
            var pawns = armys[i].pawns;
            for (var j = 0; j < pawns.length; j++) {
                var pawn = pawns[j];
                if (pawn.armyUid === armyUid && pawn.uid === uid) {
                    pawn.skinId = 0;
                    pawn.portrayal = heroSlot.hero;
                    pawn.updateAttr();
                    pawn.curHp = pawn.maxHp;
                    break;
                }
            }
        }
        this.notifyArea(index, Enums_1.NotifyType.CHANGE_PAWN_PORTRAYAL, { armyUid: armyUid, uid: uid, portrayal: heroSlot.hero, skinId: 0 });
        var pawnLocal = GameHelper_1.gameHpr.areaCenter.getArea(index).getPawnByPrecise(data.armyUid, data.uid);
        pawnLocal.updateAttr();
        pawnLocal.curHp = pawnLocal.maxHp;
        return { err: null, data: { slot: heroSlot } };
    };
    //战斗记录
    NoviceServerModel.prototype.HD_GetBattleRecordsList = function () {
        var list = [];
        for (var i = 0; i < this.battleRecordList.length; i++) {
            var record = this.battleRecordList[i];
            if (record.endTime > 0) {
                list.push(record);
            }
        }
        return { err: null, data: { list: list } };
    };
    //获取战斗记录
    NoviceServerModel.prototype.HD_GetBattleRecord = function (data) {
        for (var i = 0; i < this.battleRecordList.length; i++) {
            if (this.battleRecordList[i].uid === data.uid) {
                return { err: null, data: { record: this.battleRecordList[i].strip() } };
            }
        }
        return { err: 'login.net_error' };
    };
    //战斗详情
    NoviceServerModel.prototype.HD_GetArmyRecordsByUids = function (data) {
        for (var i = 0; i < this.battleRecordList.length; i++) {
            if (this.battleRecordList[i].uid === data.battleUid) {
                return { err: null, data: { list: this.battleRecordList[i].getBattleStatistics() } };
            }
        }
        return { err: 'login.net_error' };
    };
    NoviceServerModel = __decorate([
        mc.addmodel('novice_server')
    ], NoviceServerModel);
    return NoviceServerModel;
}(mc.BaseModel));
exports.default = NoviceServerModel;
// 新手村回档调试用
var NoviceDebug = /** @class */ (function () {
    function NoviceDebug() {
        this._history = [];
        this.index = 0;
        this._history = JSON.parse(cc.sys.localStorage.getItem('noviceDebug_history') || '[]');
        this.index = Number(cc.sys.localStorage.getItem('noviceDebug_index') || 0);
    }
    NoviceDebug.prototype.apply = function (index) {
        var data = this._history[index];
        if (!data)
            return;
        var guide = data.guide, server = data.server;
        for (var key in guide) {
            if (guide[key] === null) {
                storageMgr.remove(key);
                continue;
            }
            storageMgr.saveString(key, guide[key]);
        }
        storageMgr.saveJson('novice_data_' + GameHelper_1.gameHpr.user.getUid(), server);
        this.index = index;
        cc.sys.localStorage.setItem('noviceDebug_index', this.index);
        location.reload();
    };
    // 存一次档
    NoviceDebug.prototype.save = function () {
        var guide = GameHelper_1.gameHpr.guide.strip();
        var server = GameHelper_1.gameHpr.noviceServer.strip();
        var data = { guide: guide, server: server };
        this._history.push(data);
        cc.sys.localStorage.setItem('noviceDebug_history', JSON.stringify(this._history));
    };
    // 回到上一个存档
    NoviceDebug.prototype.back = function () {
        var index = this._history.length - 1;
        if (index < 0)
            index = 0;
        this.apply(index);
    };
    // 前进到下一个存档
    NoviceDebug.prototype.forward = function () {
        var index = this.index + 1;
        if (index >= this._history.length)
            index = this._history.length - 1;
        this.apply(index);
    };
    // 清空存档
    NoviceDebug.prototype.clear = function () {
        this._history = [];
        this.index = 0;
        cc.sys.localStorage.setItem('noviceDebug_history', JSON.stringify(this._history));
        cc.sys.localStorage.setItem('noviceDebug_index', this.index);
    };
    NoviceDebug.prototype.go = function (index) {
        index = Math.max(0, Math.min(index, this._history.length - 1));
        this.apply(index);
    };
    return NoviceDebug;
}());
if (CC_PREVIEW) {
    window['noviceDebug'] = new NoviceDebug();
}

cc._RF.pop();