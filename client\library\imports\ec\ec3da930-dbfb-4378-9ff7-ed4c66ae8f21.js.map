{"version": 3, "sources": ["assets\\app\\script\\view\\area\\AreaArmyPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAoD;AACpD,qDAA4D;AAC5D,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAK5D,yDAAoD;AAI5C,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA6C,mCAAc;IAA3D;QAAA,qEAyWC;QAvWG,0BAA0B;QAClB,aAAO,GAAa,IAAI,CAAA,CAAC,4BAA4B;QACrD,uBAAiB,GAAc,IAAI,CAAA,CAAC,wCAAwC;QAC5E,aAAO,GAAkB,IAAI,CAAA,CAAC,sBAAsB;QACpD,aAAO,GAAuB,IAAI,CAAA,CAAC,0BAA0B;QACrE,MAAM;QAEW,cAAQ,GAAW,eAAe,CAAA;QAE3C,SAAG,GAAW,CAAC,CAAA;QACf,UAAI,GAAY,IAAI,CAAA;QACpB,YAAM,GAAgB,IAAI,CAAA;QAE1B,sBAAgB,GAAY,KAAK,CAAA;QAEjC,eAAS,GAAwC,EAAE,CAAA;;IAwV/D,CAAC;IAtVU,yCAAe,GAAtB;;QACI,OAAO;sBACD,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACxE,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,qBAAqB,EAAE,QAAK,GAAE,IAAI;sBAC1E,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACzE,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBAC/D,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBAC3E,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBAC3E,GAAC,mBAAS,CAAC,wBAAwB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;SACjF,CAAA;IACL,CAAC;IAEY,kCAAQ,GAArB;;;gBACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;;;;KACxC;IAEM,iCAAO,GAAd,UAAe,IAAS;;QACpB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,oBAAO,CAAC,UAAU,CAAC,WAAW,EAAE,CAAA;QACzD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,IAAI,CAAC,IAAI,EAAE,CAAA;SACrB;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,SAAG,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,yBAAyB,CAAC,mCAAI,KAAK,CAAA;QAC1I,IAAA,KAAA,OAAuB,IAAI,CAAC,eAAe,EAAE,IAAA,EAA5C,QAAQ,QAAA,EAAE,QAAQ,QAA0B,CAAA;QACnD,IAAI,MAAM,GAAW,oBAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;QACvE,IAAA,KAAA,OAAgB,EAAE,CAAC,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,IAAA,EAA9C,KAAK,QAAA,EAAE,IAAI,QAAmC,CAAA;QACnD,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;YAC3B,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACxC,IAAI,GAAG,QAAQ,GAAG,CAAC,IAAI,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aAChD;iBAAM;gBACH,IAAI,GAAG,QAAQ,GAAG,CAAC,IAAI,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aAChD;SACJ;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,CAAC,CAAC,CAAA;IAChC,CAAC;IAEM,kCAAQ,GAAf;QACI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;YAC5D,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;SACjH;IACL,CAAC;IAEM,iCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,oDAAoD;IACpD,yCAAe,GAAf,UAAgB,KAA0B,EAAE,CAAS;QACjD,IAAM,IAAI,GAAY,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACrD,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAO,CAAC,MAAM,EAAE,EAAE;YACjC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,uBAAuB,CAAC,CAAA;SAC7D;QACD,IAAM,SAAS,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,mBAAmB,EAAE,CAAA;QAC7C,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACnC,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAA;SACvD;IACL,CAAC;IAED,+DAA+D;IAC/D,yCAAe,GAAf,UAAgB,KAA0B,EAAE,CAAS;QACjD,IAAM,IAAI,GAAY,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACnE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;SAC7B;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YAChC,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,SAAS,CAAC,CAAA;SACxC;aAAM;YACH,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;SAChD;IACL,CAAC;IAED,sDAAsD;IACtD,qCAAW,GAAX,UAAY,KAA0B,EAAE,CAAS;QAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC9B,IAAI,CAAC,IAAI,EAAE;SACV;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YACvB,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,oBAAO,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;SAC7H;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YACxB,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,oBAAO,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;SAC9H;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YACvB,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,oBAAO,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;SACxI;aAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YAClB,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAA;SACpE;IACL,CAAC;IAED,wCAAwC;IACxC,8CAAoB,GAApB,UAAqB,KAAgB,EAAE,IAAY;QAC/C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,uBAAU,CAAC,eAAe,CAAC,sBAAsB,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC,CAAA;IAClG,CAAC;IAED,uDAAuD;IACvD,2CAAiB,GAAjB,UAAkB,KAA0B,EAAE,CAAS;QAAvD,iBAgBC;QAfG,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAA;QACrC,IAAM,IAAI,GAAY,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,IAAI,CAAA;QAC9B,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YAChC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,SAAS,CAAC,CAAA;SAC/C;QACD,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,EAAE,UAAC,KAAa;YAC5D,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;gBACvB,IAAI,KAAI,CAAC,OAAO,EAAE;oBACd,KAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAA;iBACnD;aACJ;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,wDAAwD;IACxD,4CAAkB,GAAlB,UAAmB,KAA0B,EAAE,CAAS;QAAxD,iBAkBC;QAjBG,IAAM,IAAI,GAAY,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACrD,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAO,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC5G,OAAM;SACT;QACD,uBAAU,CAAC,cAAc,CAAC,qBAAqB,EAAE;YAC7C,MAAM,EAAE,CAAC,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;YACtD,EAAE,EAAE;gBACA,oBAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;oBACxC,IAAI,GAAG,EAAE;wBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;qBACnC;yBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;wBACrB,cAAc;qBACjB;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YACD,MAAM,EAAE,cAAQ,CAAC;SACpB,CAAC,CAAA;IACN,CAAC;IAED,0BAA0B;IAC1B,qCAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACtC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,oBAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAA;QAC9E,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,SAAS;IACD,8CAAoB,GAA5B,UAA6B,IAAa;QACtC,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,GAAG,MAAK,IAAI,CAAC,OAAO,CAAA,EAAA,CAAC,CAAA;QAChF,IAAI,EAAE,EAAE;YACJ,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;SACvD;IACL,CAAC;IAED,SAAS;IACD,+CAAqB,GAA7B,UAA8B,IAAa;QACvC,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,GAAG,MAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;QAC5E,IAAI,EAAE,EAAE;YACJ,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;SACvD;IACL,CAAC;IAED,SAAS;IACD,0CAAgB,GAAxB,UAAyB,GAAW,EAAE,IAAY;QAC9C,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,GAAG,MAAK,GAAG,CAAA,EAAA,CAAC,CAAA;QACvE,IAAI,EAAE,EAAE;YACJ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;SAC5D;IACL,CAAC;IAEO,8CAAoB,GAA5B,UAA6B,KAAa;QACtC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YAC3B,IAAI,CAAC,eAAe,EAAE,CAAA;YACtB,IAAI,CAAC,cAAc,EAAE,CAAA;SACxB;IACL,CAAC;IACD,iHAAiH;IAEjH,SAAS;IACD,yCAAe,GAAvB;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAA;QACpE,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAA;QAC9B,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YACrB,IAAI,CAAC,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE;gBAC3B,OAAM;aACT;iBAAM,IAAI,oBAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;gBACnD,QAAQ,IAAI,CAAC,CAAA;aAChB;iBAAM;gBACH,QAAQ,IAAI,CAAC,CAAA;aAChB;QACL,CAAC,CAAC,CAAA;QACF,iBAAiB;QACjB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC7B,oBAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,KAAK,KAAK,EAArB,CAAqB,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;YAClE,IAAI,CAAC,CAAC,UAAU,EAAE;aACjB;iBAAM,IAAI,oBAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;gBACnD,QAAQ,IAAI,CAAC,CAAA;aAChB;iBAAM;gBACH,QAAQ,IAAI,CAAC,CAAA;aAChB;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAI,QAAQ,SAAI,YAAY,MAAG,CAAA;QAC/F,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAI,QAAQ,SAAI,YAAY,MAAG,CAAA;QAC9F,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAI,QAAQ,SAAI,YAAY,MAAG,CAAA;QAC/F,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAI,QAAQ,SAAI,YAAY,MAAG,CAAA;QAC9F,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED,SAAS;IACD,wCAAc,GAAtB;QAAA,iBAmGC;QAlGG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;QACrD,IAAM,MAAM,GAAG,EAAE,CAAA;QACjB,oBAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,EAAxB,CAAwB,CAAC,CAAA;QAChE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;QACrD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC;YAChC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;gBACzC,OAAO,KAAK,CAAA;aACf;YACD,OAAO,CAAC,KAAI,CAAC,GAAG,KAAK,oBAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QACnE,CAAC,CAAC,CAAA;QACF,IAAM,YAAY,GAAG,EAAE,EAAE,cAAc,GAAG,EAAE,EAAE,eAAe,GAAG,EAAE,CAAA;QAClE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;YACvC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;YAC5B,IAAI,CAAC,CAAC,EAAE;gBACJ,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;aAChC;YACD,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACb,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAA;QAC5E,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAA;QAC1E,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,CAAA;QACvD,IAAM,YAAY,GAAG,oBAAO,CAAC,YAAY,EAAE,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAA;QACvF,IAAM,aAAa,GAAG,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,EAAE,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA;QAC1F,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QACnB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAA;YACjC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;YAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;SAC7B;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,IAAI;;YAC7B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YACxD,IAAI,KAAK,GAAU,IAAI,CAAC,KAAK,EAAE,UAAU,GAAG,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,aAAa,GAAG,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YACxI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;YACrE,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAA;YACpL,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAA;YACpH,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,YAAY,CAAA;YACxD,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC/D,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE;gBACzC,IAAM,GAAG,GAAG,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC7C,IAAI,GAAG,EAAE;oBACL,qBAAS,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;oBACrE,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;oBACvE,KAAK;oBACL,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,aAAa,EAAE;wBACnD,qBAAS,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;wBACtE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,YAAY,CAAA;qBACzD;iBACJ;qBAAM;oBACH,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;iBACrC;aACJ;iBAAM;gBACH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;aACtB;YACD,IAAM,MAAM,GAAuB,OAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,0CAAE,KAAK,OAAM,EAAE,CAAA;YACxE,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;YACrF,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,IAAI,EAAE,IAAS;;gBAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAA;gBAC9F,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAA;iBACjE;qBAAM,IAAI,QAAQ,EAAE;oBACjB,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;iBAClE;qBAAM;oBACH,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,MAAA,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;iBACzE;gBACD,IAAM,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAA;gBAChE,IAAM,EAAE,GAAG,OAAO,CAAC,CAAC,OAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,0CAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBACxE,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;gBACxD,qBAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAA,IAAI,CAAC,SAAS,0CAAE,EAAE,KAAI,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;gBACpG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAA;gBAC3G,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAChD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,CAAA;oBAC3C,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;oBACjC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,MAAA,EAAE,CAAC,CAAA;iBAC1C;gBACD,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBACnC,IAAI,QAAQ,CAAC,MAAM,GAAG,SAAS,EAAE;oBAC7B,OAAO;oBACP,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAA;oBAC1F,KAAK;oBACL,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,cAAc,GAAE,EAAE;wBACjF,IAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,GAAc,IAAI,CAAC,KAAK,CAAA;wBACjF,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,EAAE;4BACX,qBAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,KAAI,CAAC,GAAG,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;yBAC1E;6BAAM;4BACH,GAAG,CAAC,WAAW,GAAG,IAAI,CAAA;yBACzB;qBACJ;iBACJ;gBACD,IAAI,OAAO,EAAE;oBACT,UAAU,GAAG,IAAI,CAAA;iBACpB;YACL,CAAC,CAAC,CAAA;YACF,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAA;YACzG,uBAAU,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;YACtF,KAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YACpC,KAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACC,8CAAoB,GAA5B,UAA6B,EAAW,EAAE,IAAa;QACnD,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,aAAa,GAAG,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/F,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,IAAI,CAAC,oBAAO,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,EAAE;YAC7G,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;YACnG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAA,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;YACpJ,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,OAAO,CAAA;YAChD,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE;gBACvB,aAAa,CAAC,sBAAsB,EAAE,CAAA;gBACtC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAA;aACxC;SACJ;IACL,CAAC;IAED,SAAS;IACD,4CAAkB,GAA1B,UAA2B,EAAW,EAAE,IAAa;QACjD,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACpF,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YACnD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,wBAAc,CAAC,CAAC,MAAM,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAA;SACxF;IACL,CAAC;IAED,gCAAM,GAAN,UAAO,EAAU;QACb,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,CAAC;YACpB,IAAI,CAAC,CAAC,IAAI,EAAE;gBACR,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAA;aACxC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAxWgB,eAAe;QADnC,OAAO;OACa,eAAe,CAyWnC;IAAD,sBAAC;CAzWD,AAyWC,CAzW4C,EAAE,CAAC,WAAW,GAyW1D;kBAzWoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ecode } from \"../../common/constant/ECode\";\nimport { PreferenceKey } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport AreaObj from \"../../model/area/AreaObj\";\nimport ArmyObj from \"../../model/area/ArmyObj\";\nimport PawnObj from \"../../model/area/PawnObj\";\nimport PawnDrillInfoObj from \"../../model/main/PawnDrillInfoObj\";\nimport TextButtonCmpt from \"../cmpt/TextButtonCmpt\";\nimport PlayerModel from \"../../model/main/PlayerModel\";\nimport EquipInfo from \"../../model/main/EquipInfo\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class AreaArmyPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private tabLbl_: cc.Label = null // path://root/info/bg/tab_l\n    private showPawnEquipTge_: cc.Toggle = null // path://root/info/show_pawn_equip_te_t\n    private listSv_: cc.ScrollView = null // path://root/list_sv\n    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce\n    //@end\n\n    private readonly PKEY_TAB: string = 'AREA_ARMY_TAB'\n\n    private tab: number = 0\n    private area: AreaObj = null\n    private player: PlayerModel = null\n\n    private preShowPawnEquip: boolean = false\n\n    private hpBarList: { bar: cc.Sprite, pawn: PawnObj }[] = []\n\n    public listenEventMaps() {\n        return [\n            { [EventType.UPDATE_PAWN_TREASURE]: this.onUpdatePawnTreasure, enter: true },\n            { [EventType.UPDATE_ARMY_TREASURES]: this.onUpdateArmyTreasures, enter: true },\n            { [EventType.UPDATE_ARMY_NAME]: this.onUpdateArmyName, enter: true },\n            { [EventType.UPDATE_AREA_ARMY_LIST]: this.onUpdateAreaArmyList, enter: true },\n            { [EventType.REMOVE_ARMY]: this.onUpdateAreaArmyList, enter: true },\n            { [EventType.UPDATE_PAWN_DRILL_QUEUE]: this.onUpdateAreaArmyList, enter: true },\n            { [EventType.UPDATE_PAWN_LVING_QUEUE]: this.onUpdateAreaArmyList, enter: true },\n            { [EventType.UPDATE_PAWN_CURING_QUEUE]: this.onUpdateAreaArmyList, enter: true },\n        ]\n    }\n\n    public async onCreate() {\n        this.player = this.getModel('player')\n    }\n\n    public onEnter(data: any) {\n        const area = this.area = gameHpr.areaCenter.getLookArea()\n        if (!area) {\n            return this.hide()\n        }\n        this.preShowPawnEquip = this.showPawnEquipTge_.isChecked = gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED) ?? false\n        const [defCount, atkCount] = this.updateArmyCount()\n        let tabStr: string = gameHpr.user.getTempPreferenceMap(this.PKEY_TAB) || ''\n        let [index, type] = ut.stringToNumbers(tabStr, '_')\n        if (index !== area.index) {\n            this.listSv_.node.Data = -1\n            if (gameHpr.checkIsOneAlliance(area.owner)) {\n                type = defCount > 0 || atkCount === 0 ? 0 : 1\n            } else {\n                type = atkCount > 0 || defCount === 0 ? 1 : 0\n            }\n        }\n        this.tabsTc_.Tabs(type ?? 0)\n    }\n\n    public onRemove() {\n        this.hpBarList = []\n        this.area = null\n        if (this.preShowPawnEquip !== this.showPawnEquipTge_.isChecked) {\n            gameHpr.user.setLocalPreferenceData(PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED, this.showPawnEquipTge_.isChecked)\n        }\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/list_sv/view/content/item/treasure_be\n    onClickTreasure(event: cc.Event.EventTouch, _: string) {\n        const data: ArmyObj = event.target.parent.parent.Data\n        if (data.owner !== gameHpr.getUid()) {\n            return viewHelper.showAlert(ecode.NOT_OPEN_OTHER_TREASURE)\n        }\n        const treasures = data?.getAllPawnTreasures()\n        if (treasures && treasures.length > 0) {\n            viewHelper.showPnl('common/TreasureList', treasures)\n        }\n    }\n\n    // path://root/list_sv/view/content/item/name/edit/edit_name_be\n    onClickEditName(event: cc.Event.EventTouch, _: string) {\n        const data: ArmyObj = event.target.parent.parent.parent.parent.Data\n        if (!data || !data.isOwner()) {\n        } else if (this.area.isBattleing()) {\n            viewHelper.showAlert(ecode.BATTLEING)\n        } else {\n            viewHelper.showPnl('area/EditArmyName', data)\n        }\n    }\n\n    // path://root/list_sv/view/content/item/pawns/pawn_be\n    onClickPawn(event: cc.Event.EventTouch, _: string) {\n        audioMgr.playSFX('click')\n        const data = event.target.Data\n        if (!data) {\n        } else if (data.drillInfo) {\n            viewHelper.showPnl('area/PawnInfo', gameHpr.areaCenter.createPawnByDrillInfo(data.drillInfo), data.drillInfo, 'area_army')\n        } else if (data.curingInfo) {\n            viewHelper.showPnl('area/PawnInfo', gameHpr.areaCenter.createPawnByCureInfo(data.curingInfo), data.curingInfo, 'area_army')\n        } else if (data.lvingInfo) {\n            viewHelper.showPnl('area/PawnInfo', gameHpr.areaCenter.createPawnByLvingInfo(data.pawn, data.lvingInfo), data.lvingInfo, 'area_army')\n        } else if (data.pawn) {\n            viewHelper.showPnl('area/PawnInfo', data.pawn, null, 'area_army')\n        }\n    }\n\n    // path://root/info/show_pawn_equip_te_t\n    onClickShowPawnEquip(event: cc.Toggle, data: string) {\n        audioMgr.playSFX('click')\n        this.updateArmyList()\n        viewHelper.showNoLongerTip('area_army_show_equip', { content: 'ui.area_army_show_equip_tip' })\n    }\n\n    // path://root/list_sv/view/content/item/march_speed_be\n    onClickMarchSpeed(event: cc.Event.EventTouch, _: string) {\n        const it = event.target.parent.parent\n        const data: ArmyObj = it?.Data\n        if (!data) {\n            return\n        } else if (this.area.isBattleing()) {\n            return viewHelper.showAlert(ecode.BATTLEING)\n        }\n        viewHelper.showPnl('main/ModifyMarchSpeed', data, (speed: number) => {\n            if (speed) {\n                data.marchSpeed = speed\n                if (this.isValid) {\n                    this.updateArmyMarchSpeed(it.Child('top'), data)\n                }\n            }\n        })\n    }\n\n    // path://root/list_sv/view/content/item/force_revoke_be\n    onClickForceRevoke(event: cc.Event.EventTouch, _: string) {\n        const data: ArmyObj = event.target.parent.parent.Data\n        if (data.owner === gameHpr.getUid() || data.aIndex !== gameHpr.player.getMainCityIndex() || data.isBattleing()) {\n            return\n        }\n        viewHelper.showMessageBox('ui.force_revoke_tip', {\n            params: [gameHpr.getPlayerName(data.owner), data.name],\n            ok: () => {\n                gameHpr.world.forceRevoke(data.uid).then(err => {\n                    if (err) {\n                        return viewHelper.showAlert(err)\n                    } else if (this.isValid) {\n                        // this.hide()\n                    }\n                })\n            },\n            cancel: () => { }\n        })\n    }\n\n    // path://root/tabs_tc_tce\n    onClickTabs(event: cc.Toggle, data: string) {\n        !data && audioMgr.playSFX('click')\n        const tab = this.tab = Number(event.node.name)\n        gameHpr.user.setTempPreferenceData(this.PKEY_TAB, this.area.index + '_' + tab)\n        this.updateArmyList()\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // 刷新士兵宝箱\n    private onUpdatePawnTreasure(pawn: PawnObj) {\n        const it = this.listSv_.content.children.find(m => m.Data?.uid === pawn.armyUid)\n        if (it) {\n            this.updateArmyTreasure(it.Child('bottom'), it.Data)\n        }\n    }\n\n    // 刷新军队宝箱\n    private onUpdateArmyTreasures(army: ArmyObj) {\n        const it = this.listSv_.content.children.find(m => m.Data?.uid === army.uid)\n        if (it) {\n            this.updateArmyTreasure(it.Child('bottom'), it.Data)\n        }\n    }\n\n    // 刷新军队名字\n    private onUpdateArmyName(uid: string, name: string) {\n        const it = this.listSv_.content.children.find(m => m.Data?.uid === uid)\n        if (it) {\n            it.Child('top').Child('name/val', cc.Label).string = name\n        }\n    }\n\n    private onUpdateAreaArmyList(index: number) {\n        if (this.area.index === index) {\n            this.updateArmyCount()\n            this.updateArmyList()\n        }\n    }\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    // 刷新军队数量\n    private updateArmyCount() {\n        const owner = this.area.owner, maxArmyCount = this.area.maxArmyCount\n        let defCount = 0, atkCount = 0\n        // 计算各个军队数量\n        this.area.armys.forEach(m => {\n            if (m.getPawnActCount() === 0) {\n                return\n            } else if (gameHpr.checkIsOneAlliance(m.owner, owner)) {\n                defCount += 1\n            } else {\n                atkCount += 1\n            }\n        })\n        // 从这个区域开始行军的军队数量\n        const index = this.area.index\n        gameHpr.world.getMarchs().filter(m => m.armyIndex === index).forEach(m => {\n            if (m.autoRevoke) {\n            } else if (gameHpr.checkIsOneAlliance(m.owner, owner)) {\n                defCount += 1\n            } else {\n                atkCount += 1\n            }\n        })\n        this.tabsTc_.Child('0/Background/lay/count', cc.Label).string = `(${defCount}/${maxArmyCount})`\n        this.tabsTc_.Child('0/checkmark/lay/count', cc.Label).string = `(${defCount}/${maxArmyCount})`\n        this.tabsTc_.Child('1/Background/lay/count', cc.Label).string = `(${atkCount}/${maxArmyCount})`\n        this.tabsTc_.Child('1/checkmark/lay/count', cc.Label).string = `(${atkCount}/${maxArmyCount})`\n        return [defCount, atkCount]\n    }\n\n    // 刷新军队列表\n    private updateArmyList() {\n        this.tabLbl_.setLocaleKey('ui.area_army_' + this.tab)\n        const marchs = {}\n        gameHpr.world.getMarchs().forEach(m => marchs[m.armyUid] = true)\n        const owner = this.area.owner, uid = gameHpr.getUid()\n        const arr = this.area.armys.filter(m => {\n            if (m.pawns.length === 0 && m.owner !== uid) {\n                return false\n            }\n            return !this.tab === gameHpr.checkIsOneAlliance(m.owner, owner)\n        })\n        const pawnDrillMap = {}, lvingPawnLvMap = {}, curingPawnLvMap = {}\n        this.player.getAllPawnDrillList().forEach(m => {\n            let p = pawnDrillMap[m.auid]\n            if (!p) {\n                p = pawnDrillMap[m.auid] = []\n            }\n            p.push(m)\n        })\n        this.player.getPawnLevelingQueues().forEach(m => lvingPawnLvMap[m.puid] = m)\n        this.player.getCuringPawnsQueue().forEach(m => curingPawnLvMap[m.uid] = m)\n        this.listSv_.Child('empty').setActive(arr.length === 0)\n        const isNoviceMode = gameHpr.isNoviceMode, showEquip = this.showPawnEquipTge_.isChecked\n        const mainCityIndex = gameHpr.player.getMainCityIndex(), isAncient = this.area.isAncient()\n        this.hpBarList = []\n        if (this.listSv_.node.Data !== this.tab) {\n            this.listSv_.node.Data = this.tab\n            this.listSv_.stopAutoScroll()\n            this.listSv_.content.y = 0\n        }\n        this.listSv_.Items(arr, (it, data) => {\n            it.Data = data\n            const top = it.Child('top'), bottom = it.Child('bottom')\n            let pawns: any[] = data.pawns, isHasLving = false, isOwner = data.isOwner(), isOneAlliance = gameHpr.checkIsOneAlliance(data.owner, uid)\n            it.Color(isOwner ? '#E9DDC7' : isOneAlliance ? '#DAEBDD' : '#F6D6CD')\n            const armyName = data.owner ? data.name : isAncient ? assetsMgr.lang('ui.ancient_army_name') : assetsMgr.lang(pawns[0] ? 'ui.pawn_type_' + (pawns[0].type || 6) : 'ui.neutral_pawn')\n            top.Child('name/val', cc.Label).Color(isOwner ? '#564C49' : isOneAlliance ? '#4A85D5' : '#D54A4A').string = armyName\n            top.Child('name/edit').active = isOwner && !isNoviceMode\n            const other = top.Child('name/other'), alli = top.Child('alli')\n            if (other.active = !isOwner && !!data.owner) {\n                const plr = gameHpr.getPlayerInfo(data.owner)\n                if (plr) {\n                    resHelper.loadPlayerHead(other.Child('head'), plr.headIcon, this.key)\n                    other.Child('name', cc.Label).string = ut.nameFormator(plr.nickname, 7)\n                    // 联盟\n                    if (alli.active = !!plr.allianceUid && !isOneAlliance) {\n                        resHelper.loadAlliIcon(plr.allianceIcon, alli.Child('icon'), this.key)\n                        alli.Child('name', cc.Label).string = plr.allianceName\n                    }\n                } else {\n                    other.active = alli.active = false\n                }\n            } else {\n                alli.active = false\n            }\n            const drills: PawnDrillInfoObj[] = pawnDrillMap[data.uid]?.slice() || []\n            const list = isOwner ? pawns.concat(data.drillPawns).concat(data.curingPawns) : pawns\n            it.Child('pawns').Items(list, (node, pawn: any) => {\n                const icon = node.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime\n                if (isId) {\n                    node.Data = { id: pawn, drillInfo: drills.remove('id', pawn) }\n                } else if (isCuring) {\n                    node.Data = { id: pawn, curingInfo: curingPawnLvMap[pawn.uid] }\n                } else {\n                    node.Data = { pawn, id: pawn.id, lvingInfo: lvingPawnLvMap[pawn.uid] }\n                }\n                const isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring\n                const lv = isLving ? lvingPawnLvMap[pawn.uid]?.lv : (isId ? 1 : pawn.lv)\n                icon.opacity = (isId || isLving || isCuring) ? 120 : 255\n                resHelper.loadPawnHeadMiniIcon(isId ? pawn : (pawn.portrayal?.id || pawn.id), icon, this.key, false)\n                node.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv\n                if (node.Child('hp').active = (!isId && !isCuring)) {\n                    const spr = node.Child('hp/bar', cc.Sprite)\n                    spr.fillRange = pawn.getHpRatio()\n                    this.hpBarList.push({ bar: spr, pawn })\n                }\n                const showNode = node.Child('show')\n                if (showNode.active = showEquip) {\n                    // 出手速度\n                    showNode.Child('speed', cc.Label).string = (isId || isCuring) ? '' : '' + pawn.attackSpeed\n                    // 装备\n                    if (showNode.Child('equip').active = !isId && !isCuring && !!pawn?.isCanWearEquip()) {\n                        const spr = showNode.Child('equip/val', cc.Sprite), equip: EquipInfo = pawn.equip\n                        if (equip?.id) {\n                            resHelper.loadEquipIcon(equip.id, spr, this.key, equip.getSmeltCount())\n                        } else {\n                            spr.spriteFrame = null\n                        }\n                    }\n                }\n                if (isLving) {\n                    isHasLving = true\n                }\n            })\n            bottom.Child('force_revoke_be').active = data.aIndex === mainCityIndex && !isOwner && !data.isBattleing()\n            viewHelper.updateArmyState(bottom, data, marchs[data.uid], isHasLving, data.isOwner())\n            this.updateArmyMarchSpeed(top, data)\n            this.updateArmyTreasure(bottom, data)\n        })\n    }\n\n    // 行军速度\n    private updateArmyMarchSpeed(it: cc.Node, data: ArmyObj) {\n        const node = it.Child('march_speed_be'), isOneAlliance = gameHpr.checkIsOneAlliance(data.owner)\n        if (node.active = isOneAlliance && !gameHpr.isNoviceMode && data.pawns.length > 0 && data.defaultMarchSpeed > 0) {\n            const marchSpeedLbl = node.Component(cc.Label), line = node.Child('line'), isOwner = data.isOwner()\n            node.Color(/* isOwner &&  */data.marchSpeed === data.defaultMarchSpeed ? '#936E5A' : '#B6A591').setLocaleKey('ui.march_speed_desc', data.marchSpeed)\n            node.Component(cc.Button).interactable = isOwner\n            if (line.active = isOwner) {\n                marchSpeedLbl._forceUpdateRenderData()\n                line.width = marchSpeedLbl.node.width\n            }\n        }\n    }\n\n    // 刷新宝箱信息\n    private updateArmyTreasure(it: cc.Node, data: ArmyObj) {\n        const node = it.Child('treasure_be'), treasureCount = data.getAllPawnTreasureCount()\n        if (node.active = treasureCount > 0 && data.isOwner()) {\n            node.Child('treasure', TextButtonCmpt).setKey('ui.get_treasure_count', treasureCount)\n        }\n    }\n\n    update(dt: number) {\n        this.hpBarList.forEach(m => {\n            if (m.pawn) {\n                m.bar.fillRange = m.pawn.getHpRatio()\n            }\n        })\n    }\n}\n"]}