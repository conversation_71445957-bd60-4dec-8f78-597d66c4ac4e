
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/Constant.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '479ff5A9XFA/Zmr6hM7rR5U', 'Constant');
// app/script/common/constant/Constant.ts

"use strict";
/////////////// 所有常量（全大写单词间用下划线隔开）///////////////
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_BUILD_SIZE = exports.DEFAULT_AREA_SIZE = exports.DEFAULT_CITY_SIZE = exports.CREATE_ALLI_MAX_LV = exports.ARMY_PAWN_MAX_COUNT = exports.MODIFY_NICKNAME_GOLD = exports.IN_DONE_FORGE_GOLD = exports.IN_DONE_BT_GOLD = exports.DEFAULT_BT_QUEUE_COUNT = exports.INIT_RES_OUTPUT = exports.INIT_RES_COUNT = exports.INIT_RES_CAP = exports.AX_CAVALRY_ID = exports.PAWN_CROSSBOW_ID = exports.BUILD_TOWER_NID = exports.BUILD_FORT_NID = exports.BUILD_HOSPITAL_NID = exports.BUILD_HEROHALL_NID = exports.BUILD_ALLI_BAZAAR_NID = exports.BUILD_PLANT_NID = exports.BUILD_DRILLGROUND_NID = exports.BUILD_SMITHY_NID = exports.BUILD_BAZAAR_NID = exports.BUILD_EMBASSY_NID = exports.BUILD_BARRACKS_NID = exports.BUILD_WAREHOUSE_NID = exports.BUILD_GRANARY_NID = exports.BUILD_MAIN_NID = exports.BUILD_WALL_NID = exports.BUILD_QUARRY_ID = exports.BUILD_TIMBER_ID = exports.BUILD_FARM_ID = exports.CITY_LUOYANG_ID = exports.CITY_YANJING_ID = exports.CITY_JINLING_ID = exports.CITY_CHANGAN_ID = exports.ANCIENT_WALL_ID = exports.CITY_FORT_NID = exports.BUILD_FLAG_NID = exports.CITY_MAIN_NID = exports.DELAY_CLOSE_PNL_TIME = exports.LONG_PRESS_TIME = exports.MAX_ZINDEX = exports.AREA_MAX_ZINDEX = exports.BUILD_DRAG_OFFSETY = exports.MAP_EXTRA_SIZE = exports.TILE_SIZE_HALF = exports.TILE_SIZE = exports.MAP_SHOW_OFFSET = exports.CLICK_SPACE = void 0;
exports.REPLACEMENT_MIN_RES_COUNT = exports.REPLACEMENT_SERVICE_CHARGE = exports.CHAT_BARRAGE_COLOR = exports.NEED_MUTUAL_BUFF = exports.NEED_SHOW_BUFF = exports.BUFF_NODE_ZINDEX = exports.BATTLE_EFFECT_TYPE = exports.SHIELD_BUFF = exports.BUFF_SHOW_TYPE_TRAN = exports.ONE_USER_POPULARITY_CHANGE_INTERVAL = exports.CREATE_ALLI_COND = exports.CREATE_ALLI_COST = exports.CAN_EXIT_ALLI_TIME = exports.RESET_STUDY_SLOT_GOLD = exports.PAWN_SLOT_CONF = exports.EQUIP_SMELT_NEED_LV = exports.EQUIP_SLOT_EXCLUSIVE_LV = exports.EQUIP_SLOT_CONF = exports.POLICY_SLOT_CONF = exports.ADD_OUTPUT_TIME = exports.ADD_OUTPUT_RATIO = exports.ADD_OUTPUT_GOLD = exports.FREE_HEAD_ICONS = exports.FIXATION_MENU_MAX_COUNT = exports.FIXATION_MENU_CONFIG = exports.PLAYBACK_MULS = exports.ARMY_RECORD_DESC_CONF = exports.MAIL_STATE_COLOR = exports.ARMY_STATE_COLOR = exports.MARCH_ARMY_TIME_COLOR = exports.MARCH_ARMY_NAME_COLOR = exports.BUILD_EFFECT_TYPE_CONF = exports.CTYPE_NAME = exports.CTYPE_ICON = exports.CTYPE_ICON_URL = exports.RES_FIELDS_CTYPE = exports.PAWN_BUBBLE_OFFSETY = exports.CELL_RES_FIELDS = exports.SELECT_CELL_INFO_BOX = exports.RIVER_LINE_CONF = exports.BORDER_LINE_CONF = exports.DECORATION_MUD_CONF = exports.LAND_DI_CONF = exports.BATTLE_MAX_TIME = exports.TRANSIT_TIME = exports.MAIN_CITY_MARCH_SPEED = exports.UP_MARCH_SPEED_MUL = exports.DEFAULT_MAX_ADD_PAWN_TIMES = exports.DEFAULT_MAX_ARMY_COUNT = exports.BOSS_BUILD_SIZE = void 0;
exports.LOBBY_MODE_BUTTOM_NAME = exports.ALLI_APPLY_MAX_COUNT = exports.MAX_MAP_MARK_COUNT = exports.RES_MAP = exports.FIRE_PAWN_ID = exports.SPEAR_PAWN_ID = exports.DEFAULT_PET_ID = exports.SUMMON_LV = exports.HERO_SLOT_LV_COND = exports.HERO_REVIVES_TIME = exports.HERO_OPT_GIFT = exports.BUY_OPT_HERO_COST = exports.RESTORE_PORTRAYAL_GOLD_COST = exports.RESTORE_PORTRAYAL_WAR_TOKEN_COST = exports.PORTRAYAL_COMP_NEED_COUNT = exports.POINTSETS_ONE_GOLD_COST = exports.POINTSETS_ONE_COST = exports.NEXT_APPLY_CD = exports.SERVER_APPLY_CANCEL_CD = exports.SEND_TRUMPET_ACC_COST = exports.SEND_TRUMPET_COST = exports.CHAT_BANNED_REST_MAX_TIME = exports.CHAT_REST_MAX_TIME = exports.CHAT_TOLERATE_MAX_COUNT = exports.CHAT_SEND_INTERVAL = exports.CHAT_MAX_COUNT = exports.COLOR_NORMAL = exports.ANCIENT_SUP_TIME = exports.ANCIENT_SUP_COST_MUL = exports.SEASON_DURATION_TIME = exports.CAN_MIN_MARCH_SPEED = exports.OPEN_ALL_TREASURE_MIN_LAND_COUNT = exports.BATTLE_FORECAST_FREE_COUNT = exports.BATTLE_FORECAST_COST = exports.FRIENDS_MIN_LAND_COUNT = exports.SHOW_TIME_MAX_INTERVAL = exports.NOLIMIT_PCHAT_MAX_LAND = exports.OCCUPY_PLAYER_CELL_MIN_DIS = exports.REMOVE_PCHAT_TIME = exports.LAND_SCORE_CONF = exports.CONCURRENT_GAME_LIMIT = exports.NOT_OCCUPY_BY_MAX_LAND_COUNT = exports.NOT_OCCUPY_BY_SERVER_RUNTIME = exports.ALLI_JOB_COUNT = exports.ALLI_JOB_DESC = exports.LOGOUT_MAX_DAY = exports.LANGUAGE_TEXT_LIST = exports.UP_RECRUIT_PAWN_MUL = exports.RES_TRANSIT_CAP = exports.REPLACEMENT_TODAY_COUNT_MAP = void 0;
exports.DIFFICULTY_BG_COLOR = exports.AREA_DI_COLOR_CONF = exports.MAP_MASK_ITEM_COLOR = exports.CAMERA_BG_COLOR = exports.FACTORY_SLOT_CONF = exports.PAWN_COST_LV_LIST = exports.ALLI_LEADER_VOTE_MAX_COUNT = exports.STUDY_TO_BOOKTYPE = exports.PORTRAYAL_CHOSENONE_ODDS = exports.GO_HOSPITAL_CHANCE = exports.HOSPITAL_PAWN_LIMIT = exports.TONDEN_STAMINA_MUL = exports.TONDEN_GET_RES_RATIO = exports.TODAY_TONDEN_MAX_COUNT = exports.NOTICE_PERMISSION_CD = exports.PRIZE_QUESTION_TIME = exports.PRIZE_QUESTION_ID = exports.RECHARGE_BATTLE_PASS_EXP = exports.RECHARGE_BATTLE_PASS = exports.BATTLE_FIRE_COLOR = exports.BATTLE_HPBAR_COLOR = exports.RANK_SHOP_WAR_TOKEN_CONFIG = exports.SKEW_SIZE_HALF = exports.SKEW_SIZE = exports.SKEW_ANGLE = void 0;
var Enums_1 = require("./Enums");
// 点击间隔
var CLICK_SPACE = 10;
exports.CLICK_SPACE = CLICK_SPACE;
// 一格的大小
var TILE_SIZE = 80;
exports.TILE_SIZE = TILE_SIZE;
// 地图边界额外宽度
var MAP_EXTRA_SIZE = 2;
exports.MAP_EXTRA_SIZE = MAP_EXTRA_SIZE;
// 地图显示偏移
var MAP_SHOW_OFFSET = cc.v2(TILE_SIZE * 8, TILE_SIZE * 8);
exports.MAP_SHOW_OFFSET = MAP_SHOW_OFFSET;
//
var TILE_SIZE_HALF = cc.v2(TILE_SIZE * 0.5, TILE_SIZE * 0.5);
exports.TILE_SIZE_HALF = TILE_SIZE_HALF;
// 设施拖拽时候的高
var BUILD_DRAG_OFFSETY = TILE_SIZE_HALF.y - 24;
exports.BUILD_DRAG_OFFSETY = BUILD_DRAG_OFFSETY;
// 区域最高y坐标
var AREA_MAX_ZINDEX = 21 * TILE_SIZE;
exports.AREA_MAX_ZINDEX = AREA_MAX_ZINDEX;
// 层级最大值
var MAX_ZINDEX = 10000;
exports.MAX_ZINDEX = MAX_ZINDEX;
// 长按时间
var LONG_PRESS_TIME = 0.4;
exports.LONG_PRESS_TIME = LONG_PRESS_TIME;
// 延迟关闭pnl时间
var DELAY_CLOSE_PNL_TIME = 0.4;
exports.DELAY_CLOSE_PNL_TIME = DELAY_CLOSE_PNL_TIME;
// 主城id
var CITY_MAIN_NID = 1001;
exports.CITY_MAIN_NID = CITY_MAIN_NID;
// 要塞id
var CITY_FORT_NID = 2102;
exports.CITY_FORT_NID = CITY_FORT_NID;
// 4个遗迹
var ANCIENT_WALL_ID = 3000; //城墙
exports.ANCIENT_WALL_ID = ANCIENT_WALL_ID;
var CITY_CHANGAN_ID = 3001; //长安
exports.CITY_CHANGAN_ID = CITY_CHANGAN_ID;
var CITY_JINLING_ID = 3002; //金陵
exports.CITY_JINLING_ID = CITY_JINLING_ID;
var CITY_YANJING_ID = 3003; //燕京
exports.CITY_YANJING_ID = CITY_YANJING_ID;
var CITY_LUOYANG_ID = 3004; //洛阳
exports.CITY_LUOYANG_ID = CITY_LUOYANG_ID;
// 农场
var BUILD_FARM_ID = 2201;
exports.BUILD_FARM_ID = BUILD_FARM_ID;
// 伐木场
var BUILD_TIMBER_ID = 2202;
exports.BUILD_TIMBER_ID = BUILD_TIMBER_ID;
// 采石场
var BUILD_QUARRY_ID = 2203;
exports.BUILD_QUARRY_ID = BUILD_QUARRY_ID;
// 城墙建筑id
var BUILD_WALL_NID = 2000;
exports.BUILD_WALL_NID = BUILD_WALL_NID;
// 主城id
var BUILD_MAIN_NID = 2001;
exports.BUILD_MAIN_NID = BUILD_MAIN_NID;
// 仓库建筑id
var BUILD_GRANARY_NID = 2002;
exports.BUILD_GRANARY_NID = BUILD_GRANARY_NID;
// 粮仓建筑id
var BUILD_WAREHOUSE_NID = 2003;
exports.BUILD_WAREHOUSE_NID = BUILD_WAREHOUSE_NID;
// 兵营建筑id
var BUILD_BARRACKS_NID = 2004;
exports.BUILD_BARRACKS_NID = BUILD_BARRACKS_NID;
// 大使馆建筑id
var BUILD_EMBASSY_NID = 2005;
exports.BUILD_EMBASSY_NID = BUILD_EMBASSY_NID;
// 市场建筑id
var BUILD_BAZAAR_NID = 2006;
exports.BUILD_BAZAAR_NID = BUILD_BAZAAR_NID;
// 铁匠铺所建筑id
var BUILD_SMITHY_NID = 2008;
exports.BUILD_SMITHY_NID = BUILD_SMITHY_NID;
// 校场建筑id
var BUILD_DRILLGROUND_NID = 2011;
exports.BUILD_DRILLGROUND_NID = BUILD_DRILLGROUND_NID;
// 工厂建筑id
var BUILD_PLANT_NID = 2010;
exports.BUILD_PLANT_NID = BUILD_PLANT_NID;
// 联盟市场建筑id
var BUILD_ALLI_BAZAAR_NID = 2014;
exports.BUILD_ALLI_BAZAAR_NID = BUILD_ALLI_BAZAAR_NID;
// 英雄殿建筑id
var BUILD_HEROHALL_NID = 2015;
exports.BUILD_HEROHALL_NID = BUILD_HEROHALL_NID;
// 医馆建筑id
var BUILD_HOSPITAL_NID = 2016;
exports.BUILD_HOSPITAL_NID = BUILD_HOSPITAL_NID;
// 旗子id
var BUILD_FLAG_NID = 2101;
exports.BUILD_FLAG_NID = BUILD_FLAG_NID;
// 要塞建筑id
var BUILD_FORT_NID = 2102;
exports.BUILD_FORT_NID = BUILD_FORT_NID;
// 箭塔建筑id
var BUILD_TOWER_NID = 2103;
exports.BUILD_TOWER_NID = BUILD_TOWER_NID;
// 强弩兵ID
var PAWN_CROSSBOW_ID = 3305;
exports.PAWN_CROSSBOW_ID = PAWN_CROSSBOW_ID;
// 斧骑兵ID
var AX_CAVALRY_ID = 3406;
exports.AX_CAVALRY_ID = AX_CAVALRY_ID;
// 初始资源产量
var INIT_RES_OUTPUT = 120;
exports.INIT_RES_OUTPUT = INIT_RES_OUTPUT;
// 初始容量
var INIT_RES_CAP = 1000;
exports.INIT_RES_CAP = INIT_RES_CAP;
// 初始资源
var INIT_RES_COUNT = 700;
exports.INIT_RES_COUNT = INIT_RES_COUNT;
// 默认修建队列
var DEFAULT_BT_QUEUE_COUNT = 2;
exports.DEFAULT_BT_QUEUE_COUNT = DEFAULT_BT_QUEUE_COUNT;
// 立即完成修建需要的金币数
var IN_DONE_BT_GOLD = 30;
exports.IN_DONE_BT_GOLD = IN_DONE_BT_GOLD;
// 立即完成打造需要的金币数
var IN_DONE_FORGE_GOLD = 30;
exports.IN_DONE_FORGE_GOLD = IN_DONE_FORGE_GOLD;
// 修改昵称需要的金币数
var MODIFY_NICKNAME_GOLD = 500;
exports.MODIFY_NICKNAME_GOLD = MODIFY_NICKNAME_GOLD;
// 军队最大士兵个数
var ARMY_PAWN_MAX_COUNT = 9;
exports.ARMY_PAWN_MAX_COUNT = ARMY_PAWN_MAX_COUNT;
// 大使馆多少级可以创建联盟
var CREATE_ALLI_MAX_LV = 3;
exports.CREATE_ALLI_MAX_LV = CREATE_ALLI_MAX_LV;
// 
var DEFAULT_CITY_SIZE = cc.v2(1, 1); //默认的城市地块大小
exports.DEFAULT_CITY_SIZE = DEFAULT_CITY_SIZE;
var DEFAULT_AREA_SIZE = cc.v2(11, 11); //默认的区域大小
exports.DEFAULT_AREA_SIZE = DEFAULT_AREA_SIZE;
var DEFAULT_BUILD_SIZE = cc.v2(1, 1); //默认的建筑面积大小
exports.DEFAULT_BUILD_SIZE = DEFAULT_BUILD_SIZE;
var BOSS_BUILD_SIZE = cc.v2(3, 3); //boss
exports.BOSS_BUILD_SIZE = BOSS_BUILD_SIZE;
var DEFAULT_MAX_ARMY_COUNT = 5; //默认区域的最大容纳军队数量
exports.DEFAULT_MAX_ARMY_COUNT = DEFAULT_MAX_ARMY_COUNT;
var DEFAULT_MAX_ADD_PAWN_TIMES = 20; //默认最大补兵次数
exports.DEFAULT_MAX_ADD_PAWN_TIMES = DEFAULT_MAX_ADD_PAWN_TIMES;
// 加速行军倍数
var UP_MARCH_SPEED_MUL = 3;
exports.UP_MARCH_SPEED_MUL = UP_MARCH_SPEED_MUL;
// 城边加速倍数
var MAIN_CITY_MARCH_SPEED = {
    1: 3.5,
    2: 3,
    3: 2.5,
    4: 2,
    5: 1.5,
};
exports.MAIN_CITY_MARCH_SPEED = MAIN_CITY_MARCH_SPEED;
// 运送时间 格/小时
var TRANSIT_TIME = 300;
exports.TRANSIT_TIME = TRANSIT_TIME;
// 一场战斗最多持续时间 秒
var BATTLE_MAX_TIME = 3600 * 3;
exports.BATTLE_MAX_TIME = BATTLE_MAX_TIME;
// 地块底配置
var LAND_DI_CONF = [
    { list: [0, 0, 0, 0], no: '01' },
    { list: [0, 1, 1, 0], no: '02' },
    { list: [0, 1, 1, 1], no: '03' },
    { list: [0, 0, 1, 1], no: '04' },
    { list: [1, 1, 1, 0], no: '05' },
    { list: [1, 1, 1, 1], no: '06' },
    { list: [1, 0, 1, 1], no: '07' },
    { list: [1, 1, 0, 0], no: '08' },
    { list: [1, 1, 0, 1], no: '09' },
    { list: [1, 0, 0, 1], no: '10' },
    { list: [0, 0, 1, 0], no: '11' },
    { list: [1, 0, 1, 0], no: '12' },
    { list: [1, 0, 0, 0], no: '13' },
    { list: [0, 1, 0, 0], no: '14' },
    { list: [0, 1, 0, 1], no: '15' },
    { list: [0, 0, 0, 1], no: '16' },
];
exports.LAND_DI_CONF = LAND_DI_CONF;
// 地块底配置，方向：左上右下
var DECORATION_MUD_CONF = {
    '0011': '101',
    '1011': '102',
    '1001': '103',
    '0111': '104',
    '1101': '107',
    '0110': '108',
    '1110': '109',
    '1100': '110',
    '0010': '111',
    '1010': '112',
    '1000': '113',
    '0001': '114',
    '0101': '115',
    '0100': '116',
    '0000': '117',
    '1111': ['105', '106'],
};
exports.DECORATION_MUD_CONF = DECORATION_MUD_CONF;
// 边框线配置
var BORDER_LINE_CONF = [
    { size: cc.size(80, 4), pos: cc.v2(0, 38) },
    { size: cc.size(4, 80), pos: cc.v2(38, 0) },
    { size: cc.size(80, 4), pos: cc.v2(0, -38) },
    { size: cc.size(4, 80), pos: cc.v2(-38, 0) },
    { size: cc.size(4, 4), pos: cc.v2(-38, 38) },
    { size: cc.size(4, 4), pos: cc.v2(38, 38) },
    { size: cc.size(4, 4), pos: cc.v2(38, -38) },
    { size: cc.size(4, 4), pos: cc.v2(-38, -38) },
];
exports.BORDER_LINE_CONF = BORDER_LINE_CONF;
// 河流边框线配置
var RIVER_LINE_CONF = {
    0: { size: cc.size(80, 4), pos: cc.v2(0, 30) },
    111: { size: cc.size(4, 68), pos: cc.v2(38, -6) },
    112: { size: cc.size(4, 80), pos: cc.v2(38, 0) },
    121: { size: cc.size(4, 80), pos: cc.v2(38, -12) },
    122: { size: cc.size(4, 92), pos: cc.v2(38, -6) },
    311: { size: cc.size(4, 68), pos: cc.v2(-38, -6) },
    312: { size: cc.size(4, 80), pos: cc.v2(-38, 0) },
    321: { size: cc.size(4, 80), pos: cc.v2(-38, -12) },
    322: { size: cc.size(4, 92), pos: cc.v2(-38, -6) },
};
exports.RIVER_LINE_CONF = RIVER_LINE_CONF;
// 选择地块信息框大小
var SELECT_CELL_INFO_BOX = cc.rect(320, 320, 272, 320);
exports.SELECT_CELL_INFO_BOX = SELECT_CELL_INFO_BOX;
// 士兵气泡高度
var PAWN_BUBBLE_OFFSETY = 100;
exports.PAWN_BUBBLE_OFFSETY = PAWN_BUBBLE_OFFSETY;
// 地块资源配置列表字段
var CELL_RES_FIELDS = ['cereal', 'timber', 'stone'];
exports.CELL_RES_FIELDS = CELL_RES_FIELDS;
// 资源字段反向映射
var RES_FIELDS_CTYPE = {
    'cereal': Enums_1.CType.CEREAL,
    'timber': Enums_1.CType.TIMBER,
    'stone': Enums_1.CType.STONE,
};
exports.RES_FIELDS_CTYPE = RES_FIELDS_CTYPE;
// 通用类型对应图标url
var CTYPE_ICON_URL = (_a = {},
    _a[Enums_1.CType.TITLE] = 'icon/title_empty',
    _a[Enums_1.CType.WIN_POINT] = 'icon/win_point',
    _a[Enums_1.CType.HERO_OPT] = 'icon/hero_opt',
    _a[Enums_1.CType.UP_RECRUIT] = 'icon/up_recruit',
    _a);
exports.CTYPE_ICON_URL = CTYPE_ICON_URL;
// 通用类型对应图标url
var CTYPE_ICON = (_b = {},
    _b[Enums_1.CType.CEREAL] = 'cereal',
    _b[Enums_1.CType.TIMBER] = 'timber',
    _b[Enums_1.CType.STONE] = 'stone',
    _b[Enums_1.CType.GOLD] = 'gold',
    _b[Enums_1.CType.INGOT] = 'ingot',
    _b[Enums_1.CType.WAR_TOKEN] = 'war_token',
    _b[Enums_1.CType.EXP_BOOK] = 'exp_book',
    _b[Enums_1.CType.CEREAL_C] = 'cereal_c',
    _b[Enums_1.CType.IRON] = 'iron',
    _b[Enums_1.CType.UP_SCROLL] = 'up_scroll',
    _b[Enums_1.CType.FIXATOR] = 'fixator',
    _b[Enums_1.CType.BASE_RES] = 'base_res',
    _b[Enums_1.CType.BASE_RES_2] = 'base_res_2',
    _b[Enums_1.CType.STAMINA] = 'stamina',
    _b[Enums_1.CType.RANK_COIN] = 'rank_coin',
    _b);
exports.CTYPE_ICON = CTYPE_ICON;
// 通用类型对应的名字
var CTYPE_NAME = (_c = {},
    _c[Enums_1.CType.CEREAL] = 'ui.cereal',
    _c[Enums_1.CType.TIMBER] = 'ui.timber',
    _c[Enums_1.CType.STONE] = 'ui.stone',
    _c[Enums_1.CType.GOLD] = 'ui.gold',
    _c[Enums_1.CType.INGOT] = 'ui.ingot',
    _c[Enums_1.CType.WAR_TOKEN] = 'ui.war_token',
    _c[Enums_1.CType.EXP_BOOK] = 'ui.exp_book',
    _c[Enums_1.CType.CEREAL_C] = 'ui.cereal_c',
    _c[Enums_1.CType.IRON] = 'ui.iron',
    _c[Enums_1.CType.UP_SCROLL] = 'ui.up_scroll',
    _c[Enums_1.CType.FIXATOR] = 'ui.fixator',
    _c[Enums_1.CType.BASE_RES] = 'ui.base_res',
    _c);
exports.CTYPE_NAME = CTYPE_NAME;
// 建筑效果配置
var BUILD_EFFECT_TYPE_CONF = (_d = {},
    _d[Enums_1.CEffect.BT_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.BUILD_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.GRANARY_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.WAREHOUSE_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.XL_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.ALLIANCE_PERS] = { vtype: 'number' },
    _d[Enums_1.CEffect.MERCHANT_COUNT] = { vtype: 'number' },
    _d[Enums_1.CEffect.DRILL_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.WALL_HP] = { vtype: 'number' },
    _d[Enums_1.CEffect.FORGE_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.ARMY_COUNT] = { vtype: 'number' },
    _d[Enums_1.CEffect.RES_OUTPUT] = { vtype: 'number' },
    _d[Enums_1.CEffect.MARCH_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.UPLVING_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.GW_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.XL_2LV] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.TRANSIT_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MAIN_MARCH_MUL] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CITY_BUILD_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.TREASURE_AWARD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.FREE_RECAST] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.RARE_RES_OUTPUT] = { vtype: 'number' },
    _d[Enums_1.CEffect.MORE_RARE_RES] = { vtype: 'number' },
    _d[Enums_1.CEffect.LV_UP_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.TOWER_LV] = { vtype: 'number' },
    _d[Enums_1.CEffect.FARM_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.QUARRY_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MILL_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CURE_QUEUE] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MARKET_SERVICE_CHARGE] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CITY_COUNT_LIMIT] = { vtype: 'number' },
    _d[Enums_1.CEffect.TOWER_HP] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.OTHER_RES_ODDS] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CURE_CD] = { vtype: 'number', suffix: '%' },
    _d);
exports.BUILD_EFFECT_TYPE_CONF = BUILD_EFFECT_TYPE_CONF;
// 行军军队名字颜色
var MARCH_ARMY_NAME_COLOR = (_e = {},
    _e[Enums_1.MarchLineType.SELF_ARMY] = '#59A733',
    _e[Enums_1.MarchLineType.OTHER_ARMY] = '#C34B3F',
    _e[Enums_1.MarchLineType.ALLI_ARMY] = '#4F8FBA',
    _e);
exports.MARCH_ARMY_NAME_COLOR = MARCH_ARMY_NAME_COLOR;
// 行军军队时间颜色
var MARCH_ARMY_TIME_COLOR = (_f = {},
    _f[Enums_1.MarchLineType.SELF_ARMY] = '#FFFFFF',
    _f[Enums_1.MarchLineType.OTHER_ARMY] = '#FF9162',
    _f[Enums_1.MarchLineType.ALLI_ARMY] = '#7FD6FF',
    _f);
exports.MARCH_ARMY_TIME_COLOR = MARCH_ARMY_TIME_COLOR;
// 军队状态颜色
var ARMY_STATE_COLOR = (_g = {},
    _g[Enums_1.ArmyState.NONE] = '#936E5A',
    _g[Enums_1.ArmyState.MARCH] = '#936E5A',
    _g[Enums_1.ArmyState.FIGHT] = '#C34B3F',
    _g[Enums_1.ArmyState.DRILL] = '#59A733',
    _g[Enums_1.ArmyState.LVING] = '#59A733',
    _g[Enums_1.ArmyState.CURING] = '#59A733',
    _g[Enums_1.ArmyState.TONDEN] = '#59A733',
    _g);
exports.ARMY_STATE_COLOR = ARMY_STATE_COLOR;
// 邮件状态颜色
var MAIL_STATE_COLOR = (_h = {},
    _h[Enums_1.MailStateType.NONE] = '#C34B3F',
    _h[Enums_1.MailStateType.NOT_CLAIM] = '#C34B3F',
    _h[Enums_1.MailStateType.READ] = '#A18876',
    _h);
exports.MAIL_STATE_COLOR = MAIL_STATE_COLOR;
var COLOR_NORMAL = {
    DONE: '#21DC2D'
};
exports.COLOR_NORMAL = COLOR_NORMAL;
// 军队记录说明的配置
var ARMY_RECORD_DESC_CONF = {
    0: ['index'],
    1: ['index', 'target'],
    2: ['index', 'target'],
    3: ['index', 'target'],
    4: ['index'],
    5: ['index', 'target'],
    6: ['index'],
    7: ['index', 'target'],
};
exports.ARMY_RECORD_DESC_CONF = ARMY_RECORD_DESC_CONF;
// 回放倍数
var PLAYBACK_MULS = [
    { val: 4, text: '0.25x' },
    { val: 2, text: '0.5x' },
    { val: 1, text: '1x' },
    { val: 0.5, text: '2x' },
    { val: 0.25, text: '4x' },
];
exports.PLAYBACK_MULS = PLAYBACK_MULS;
// 固定到菜单配置
var FIXATION_MENU_CONFIG = {
    2001: 'build/BuildMainInfo',
    2004: 'build/BuildBarracks',
    2005: 'build/BuildEmbassy',
    2006: 'build/BuildBazaar',
    2008: 'build/BuildSmithy',
    2010: 'build/BuildFactory',
    2011: 'build/BuildDrillground',
    2012: 'build/BuildTower',
    2013: 'build/BuildTower',
    2014: 'build/BuildBazaar',
    2015: 'build/BuildHerohall',
    2016: 'build/BuildHospital',
};
exports.FIXATION_MENU_CONFIG = FIXATION_MENU_CONFIG;
var FIXATION_MENU_MAX_COUNT = 3; //固定到菜单最多个数
exports.FIXATION_MENU_MAX_COUNT = FIXATION_MENU_MAX_COUNT;
// 免费头像列表
var FREE_HEAD_ICONS = [
    'head_icon_free_001',
    'head_icon_free_002',
    'head_icon_free_003',
    'head_icon_free_004',
    'head_icon_free_005',
    'head_icon_free_006',
    'head_icon_free_007',
    'head_icon_free_008',
];
exports.FREE_HEAD_ICONS = FREE_HEAD_ICONS;
// 商城购买添加产量需要的金币
var ADD_OUTPUT_GOLD = 50;
exports.ADD_OUTPUT_GOLD = ADD_OUTPUT_GOLD;
var ADD_OUTPUT_RATIO = 20; //商城购买添加产量比例
exports.ADD_OUTPUT_RATIO = ADD_OUTPUT_RATIO;
var ADD_OUTPUT_TIME = 1 * 86400 * 1000; //商城购买添加产量持续时间
exports.ADD_OUTPUT_TIME = ADD_OUTPUT_TIME;
// 内政政策槽位配置
var POLICY_SLOT_CONF = [3, 5, 10, 15, 20];
exports.POLICY_SLOT_CONF = POLICY_SLOT_CONF;
// 装备槽位配置
var EQUIP_SLOT_CONF = [1, 3, 5, 7, 10, 12, 14, 16, 18, 20];
exports.EQUIP_SLOT_CONF = EQUIP_SLOT_CONF;
var EQUIP_SLOT_EXCLUSIVE_LV = { 10: true, 18: true };
exports.EQUIP_SLOT_EXCLUSIVE_LV = EQUIP_SLOT_EXCLUSIVE_LV;
// 装备融炼解锁等级
var EQUIP_SMELT_NEED_LV = [14, 20];
exports.EQUIP_SMELT_NEED_LV = EQUIP_SMELT_NEED_LV;
// 士兵槽位配置
var PAWN_SLOT_CONF = [1, 2, 4, 7];
exports.PAWN_SLOT_CONF = PAWN_SLOT_CONF;
// 研究每重置一次需要的金币
var RESET_STUDY_SLOT_GOLD = 50;
exports.RESET_STUDY_SLOT_GOLD = RESET_STUDY_SLOT_GOLD;
// 多长时间可以退出联盟
var CAN_EXIT_ALLI_TIME = 3600000 * 12;
exports.CAN_EXIT_ALLI_TIME = CAN_EXIT_ALLI_TIME;
// 创建联盟费用
var CREATE_ALLI_COST = '1,0,3000|2,0,2000|3,0,2000';
exports.CREATE_ALLI_COST = CREATE_ALLI_COST;
var CREATE_ALLI_COND = 100;
exports.CREATE_ALLI_COND = CREATE_ALLI_COND;
// 单个玩家给其他玩家改变人气间隔
var ONE_USER_POPULARITY_CHANGE_INTERVAL = 86400000 * 30;
exports.ONE_USER_POPULARITY_CHANGE_INTERVAL = ONE_USER_POPULARITY_CHANGE_INTERVAL;
// 外显buff 同时显示 层级
var BUFF_NODE_ZINDEX = (_j = {},
    _j[Enums_1.BuffType.SHIELD] = 1,
    _j[Enums_1.BuffType.PROTECTION_SHIELD] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD_001] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD_102] = 1,
    _j[Enums_1.BuffType.ABNEGATION_SHIELD] = 1,
    _j[Enums_1.BuffType.PARRY] = 2,
    _j[Enums_1.BuffType.PARRY_001] = 2,
    _j[Enums_1.BuffType.PARRY_102] = 2,
    _j[Enums_1.BuffType.WITHSTAND] = 2,
    _j[Enums_1.BuffType.JUMPSLASH_DAMAGE] = 2,
    _j[Enums_1.BuffType.BEHEADED_GENERAL] = 2,
    _j[Enums_1.BuffType.ANTICIPATION_DEFENSE] = 2,
    _j[Enums_1.BuffType.ANTICIPATION_ATTACK] = 2,
    _j[Enums_1.BuffType.DIZZINESS] = 3,
    _j[Enums_1.BuffType.PARALYSIS] = 3,
    _j[Enums_1.BuffType.PARALYSIS_UP] = 3,
    _j[Enums_1.BuffType.WIRE_CHAIN] = 3,
    _j[Enums_1.BuffType.SILENCE] = 4,
    _j[Enums_1.BuffType.CHAOS] = 4,
    _j[Enums_1.BuffType.POISONED_WINE] = 4,
    _j[Enums_1.BuffType.LIAN_PO_ATTACK] = 5,
    _j[Enums_1.BuffType.LIAN_PO_DEFEND] = 5,
    _j);
exports.BUFF_NODE_ZINDEX = BUFF_NODE_ZINDEX;
// 外显buff 同时显示
var NEED_SHOW_BUFF = (_k = {},
    _k[Enums_1.BuffType.SHIELD] = true,
    _k[Enums_1.BuffType.PROTECTION_SHIELD] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD_001] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD_102] = true,
    _k[Enums_1.BuffType.ABNEGATION_SHIELD] = true,
    _k[Enums_1.BuffType.PARRY] = true,
    _k[Enums_1.BuffType.PARRY_001] = true,
    _k[Enums_1.BuffType.PARRY_102] = true,
    _k[Enums_1.BuffType.WITHSTAND] = true,
    _k[Enums_1.BuffType.JUMPSLASH_DAMAGE] = true,
    _k[Enums_1.BuffType.BEHEADED_GENERAL] = true,
    _k[Enums_1.BuffType.ANTICIPATION_DEFENSE] = true,
    _k[Enums_1.BuffType.ANTICIPATION_ATTACK] = true,
    _k[Enums_1.BuffType.DIZZINESS] = true,
    _k[Enums_1.BuffType.PARALYSIS] = true,
    _k[Enums_1.BuffType.PARALYSIS_UP] = true,
    _k[Enums_1.BuffType.WIRE_CHAIN] = true,
    _k[Enums_1.BuffType.SILENCE] = true,
    _k[Enums_1.BuffType.CHAOS] = true,
    _k[Enums_1.BuffType.POISONED_WINE] = true,
    _k[Enums_1.BuffType.LIAN_PO_ATTACK] = true,
    _k[Enums_1.BuffType.LIAN_PO_DEFEND] = true,
    _k);
exports.NEED_SHOW_BUFF = NEED_SHOW_BUFF;
// 外显buff 互斥显示
var NEED_MUTUAL_BUFF = (_l = {},
    _l[Enums_1.BuffType.ARMOR_PENETRATION] = true,
    _l[Enums_1.BuffType.INSPIRE] = true,
    _l[Enums_1.BuffType.WORTHY_MONARCH] = true,
    _l[Enums_1.BuffType.DESTROY_WEAPONS] = true,
    _l[Enums_1.BuffType.POISONING_MAX_HP] = true,
    _l[Enums_1.BuffType.POISONING_CUR_HP] = true,
    _l[Enums_1.BuffType.INFECTION_PLAGUE] = true,
    _l[Enums_1.BuffType.BLEED] = true,
    _l[Enums_1.BuffType.DAMAGE_INCREASE] = true,
    _l[Enums_1.BuffType.DAMAGE_REDUCE] = true,
    _l[Enums_1.BuffType.GOD_WAR] = true,
    _l[Enums_1.BuffType.FEAR] = true,
    _l[Enums_1.BuffType.TIMIDITY] = true,
    _l[Enums_1.BuffType.TIGER_MANIA] = true,
    _l[Enums_1.BuffType.IRREMOVABILITY] = true,
    _l[Enums_1.BuffType.OVERLORD] = true,
    _l[Enums_1.BuffType.IGNITION] = true,
    _l[Enums_1.BuffType.RAGE] = true,
    _l);
exports.NEED_MUTUAL_BUFF = NEED_MUTUAL_BUFF;
// 外显buff 类型转换
var BUFF_SHOW_TYPE_TRAN = (_m = {},
    _m[Enums_1.BuffType.POISONING_CUR_HP] = Enums_1.BuffType.POISONING_MAX_HP,
    _m[Enums_1.BuffType.INFECTION_PLAGUE] = Enums_1.BuffType.POISONING_MAX_HP,
    _m[Enums_1.BuffType.DAMAGE_REDUCE] = Enums_1.BuffType.DESTROY_WEAPONS,
    _m[Enums_1.BuffType.WORTHY_MONARCH] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.GOD_WAR] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.TIGER_MANIA] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.IRREMOVABILITY] = Enums_1.BuffType.TIMIDITY,
    _m[Enums_1.BuffType.OVERLORD] = Enums_1.BuffType.TIMIDITY,
    _m);
exports.BUFF_SHOW_TYPE_TRAN = BUFF_SHOW_TYPE_TRAN;
// 护盾buff
var SHIELD_BUFF = (_o = {},
    _o[Enums_1.BuffType.SHIELD] = true,
    _o[Enums_1.BuffType.PROTECTION_SHIELD] = true,
    _o[Enums_1.BuffType.LOW_HP_SHIELD] = true,
    _o[Enums_1.BuffType.ATTACK_SHIELD] = true,
    _o[Enums_1.BuffType.SUCKBLOOD_SHIELD] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD_001] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD_102] = true,
    _o[Enums_1.BuffType.BATTLE_BEGIN_SHIELD] = true,
    _o[Enums_1.BuffType.KUROU_SHIELD] = true,
    _o[Enums_1.BuffType.SUCK_SHIELD] = true,
    _o[Enums_1.BuffType.ABNEGATION_SHIELD] = true,
    _o[Enums_1.BuffType.LONGITUDINAL_CLEFT_SHIELD] = true,
    _o[Enums_1.BuffType.CRIMSONGOLD_SHIELD] = true,
    _o[Enums_1.BuffType.BLACK_IRON_STAFF_SHIELD] = true,
    _o);
exports.SHIELD_BUFF = SHIELD_BUFF;
// 战斗特效类型
// 10000002.攻击 10000003.闪避 10000004.减伤 10000005.护盾
var BATTLE_EFFECT_TYPE = {
    ATTACK: [10000002],
    DAMAGE_REDUCTION: [10000004],
    SHIELD: [10000005],
    VALOR: [10000002, 10000003],
    WISDOM_COURAGE: [10000002, 10000004],
    KUROU: [10000002, 10000005],
    SAND_CLOCK: [10000006],
    TONDEN: [114001],
};
exports.BATTLE_EFFECT_TYPE = BATTLE_EFFECT_TYPE;
// 聊天弹幕颜色
var CHAT_BARRAGE_COLOR = {
    0: '#FFFFFF',
    1: '#5BB8FF',
    2: '#FF81F7',
};
exports.CHAT_BARRAGE_COLOR = CHAT_BARRAGE_COLOR;
// 和大自然交换资源手续费
var REPLACEMENT_SERVICE_CHARGE = 60;
exports.REPLACEMENT_SERVICE_CHARGE = REPLACEMENT_SERVICE_CHARGE;
// 和大自然交换最少资源
var REPLACEMENT_MIN_RES_COUNT = 100;
exports.REPLACEMENT_MIN_RES_COUNT = REPLACEMENT_MIN_RES_COUNT;
// 每日置换次数
var REPLACEMENT_TODAY_COUNT_MAP = {
    0: 3,
    1: 3,
    2: 3,
};
exports.REPLACEMENT_TODAY_COUNT_MAP = REPLACEMENT_TODAY_COUNT_MAP;
// 各个资源占用运送的容量
var RES_TRANSIT_CAP = (_p = {},
    _p[Enums_1.CType.CEREAL] = 1,
    _p[Enums_1.CType.TIMBER] = 1,
    _p[Enums_1.CType.STONE] = 1,
    _p[Enums_1.CType.EXP_BOOK] = 100,
    _p[Enums_1.CType.IRON] = 100,
    _p[Enums_1.CType.UP_SCROLL] = 500,
    _p[Enums_1.CType.FIXATOR] = 500,
    _p);
exports.RES_TRANSIT_CAP = RES_TRANSIT_CAP;
// 加速招募倍数
var UP_RECRUIT_PAWN_MUL = 0.125;
exports.UP_RECRUIT_PAWN_MUL = UP_RECRUIT_PAWN_MUL;
// 多语言名字
var LANGUAGE_TEXT_LIST = [
    { lang: 'en', text: 'ENGLISH' },
    { lang: 'cn', text: '简体中文' },
    { lang: 'hk', text: '繁體(港澳)' },
    { lang: 'tw', text: '繁體(臺灣)' },
    { lang: 'jp', text: '日本語' },
    { lang: 'kr', text: '한국어' },
    { lang: 'idl', text: 'Bahasa Indonesia' },
    { lang: 'th', text: 'ภาษาไทย' },
    { lang: 'vi', text: 'Tiếng Việt' },
];
exports.LANGUAGE_TEXT_LIST = LANGUAGE_TEXT_LIST;
// 注销账号等待时间
var LOGOUT_MAX_DAY = 86400000 * 7;
exports.LOGOUT_MAX_DAY = LOGOUT_MAX_DAY;
// 联盟职位说明
var ALLI_JOB_DESC = {
    0: [1, 2, 3, 4, 5, 7, 8],
    1: [3, 4, 5, 7],
    2: [6, 8],
    10: [0],
};
exports.ALLI_JOB_DESC = ALLI_JOB_DESC;
// 职位数量
var ALLI_JOB_COUNT = {
    0: 1,
    1: 1,
    2: 2,
    10: 40,
};
exports.ALLI_JOB_COUNT = ALLI_JOB_COUNT;
// 开服多久内不可攻占
var NOT_OCCUPY_BY_SERVER_RUNTIME = 86400000 * 3;
exports.NOT_OCCUPY_BY_SERVER_RUNTIME = NOT_OCCUPY_BY_SERVER_RUNTIME;
var NOT_OCCUPY_BY_MAX_LAND_COUNT = 100;
exports.NOT_OCCUPY_BY_MAX_LAND_COUNT = NOT_OCCUPY_BY_MAX_LAND_COUNT;
// 不同类型的区最多可玩几个区
var CONCURRENT_GAME_LIMIT = 1;
exports.CONCURRENT_GAME_LIMIT = CONCURRENT_GAME_LIMIT;
// 领地积分配置
var LAND_SCORE_CONF = {
    1: [[50, 2], [100, 1]],
    2: [[40, 4], [80, 2]],
    3: [[30, 6], [60, 3]],
    4: [[20, 8], [40, 4]],
    5: [[10, 10], [20, 5]],
};
exports.LAND_SCORE_CONF = LAND_SCORE_CONF;
// 多久才可以删除私聊
var REMOVE_PCHAT_TIME = 3600000 * 12;
exports.REMOVE_PCHAT_TIME = REMOVE_PCHAT_TIME;
// 攻占玩家领地要求最低距离
var OCCUPY_PLAYER_CELL_MIN_DIS = 5;
exports.OCCUPY_PLAYER_CELL_MIN_DIS = OCCUPY_PLAYER_CELL_MIN_DIS;
// 多少地可以无限制私聊
var NOLIMIT_PCHAT_MAX_LAND = 150;
exports.NOLIMIT_PCHAT_MAX_LAND = NOLIMIT_PCHAT_MAX_LAND;
// 聊天 显示时间的最大间隔
var SHOW_TIME_MAX_INTERVAL = 60000 * 1;
exports.SHOW_TIME_MAX_INTERVAL = SHOW_TIME_MAX_INTERVAL;
// 可申请添加好友最小地块数
var FRIENDS_MIN_LAND_COUNT = 100;
exports.FRIENDS_MIN_LAND_COUNT = FRIENDS_MIN_LAND_COUNT;
// 战斗预测费用
var BATTLE_FORECAST_COST = 30;
exports.BATTLE_FORECAST_COST = BATTLE_FORECAST_COST;
// 战斗预测免费次数
var BATTLE_FORECAST_FREE_COUNT = 5;
exports.BATTLE_FORECAST_FREE_COUNT = BATTLE_FORECAST_FREE_COUNT;
// 一键打开宝箱要求
var OPEN_ALL_TREASURE_MIN_LAND_COUNT = 100;
exports.OPEN_ALL_TREASURE_MIN_LAND_COUNT = OPEN_ALL_TREASURE_MIN_LAND_COUNT;
// 最小可修改的行军速度
var CAN_MIN_MARCH_SPEED = 30;
exports.CAN_MIN_MARCH_SPEED = CAN_MIN_MARCH_SPEED;
// 一个季节持续的时间
var SEASON_DURATION_TIME = 3 * 86400000;
exports.SEASON_DURATION_TIME = SEASON_DURATION_TIME;
// 遗迹加速资源倍数
var ANCIENT_SUP_COST_MUL = 40;
exports.ANCIENT_SUP_COST_MUL = ANCIENT_SUP_COST_MUL;
// 遗迹加速时间
var ANCIENT_SUP_TIME = 6 * 60000;
exports.ANCIENT_SUP_TIME = ANCIENT_SUP_TIME;
// 聊天相关
var CHAT_MAX_COUNT = 50;
exports.CHAT_MAX_COUNT = CHAT_MAX_COUNT;
var CHAT_SEND_INTERVAL = 6000; //发送聊天的预期间隔 (毫秒)
exports.CHAT_SEND_INTERVAL = CHAT_SEND_INTERVAL;
var CHAT_TOLERATE_MAX_COUNT = 3; //最多容忍多少次在间隔内发送
exports.CHAT_TOLERATE_MAX_COUNT = CHAT_TOLERATE_MAX_COUNT;
var CHAT_REST_MAX_TIME = 30000; //如果太频繁就休息一下
exports.CHAT_REST_MAX_TIME = CHAT_REST_MAX_TIME;
var CHAT_BANNED_REST_MAX_TIME = 60000 * 10; //禁言休息时间
exports.CHAT_BANNED_REST_MAX_TIME = CHAT_BANNED_REST_MAX_TIME;
// 发送喇叭费用
var SEND_TRUMPET_COST = 50;
exports.SEND_TRUMPET_COST = SEND_TRUMPET_COST;
var SEND_TRUMPET_ACC_COST = 25;
exports.SEND_TRUMPET_ACC_COST = SEND_TRUMPET_ACC_COST;
// 多长时间可以取消报名
var SERVER_APPLY_CANCEL_CD = 1 * 60 * 1000;
exports.SERVER_APPLY_CANCEL_CD = SERVER_APPLY_CANCEL_CD;
// 下次报名的等待时间
var NEXT_APPLY_CD = 6 * 1000;
exports.NEXT_APPLY_CD = NEXT_APPLY_CD;
// 点将一次的费用
var POINTSETS_ONE_COST = 10;
exports.POINTSETS_ONE_COST = POINTSETS_ONE_COST;
// 点击5次 金币费用
var POINTSETS_ONE_GOLD_COST = 598;
exports.POINTSETS_ONE_GOLD_COST = POINTSETS_ONE_GOLD_COST;
// 残卷合成画像 需要数量
var PORTRAYAL_COMP_NEED_COUNT = 3;
exports.PORTRAYAL_COMP_NEED_COUNT = PORTRAYAL_COMP_NEED_COUNT;
// 还原画像费用
var RESTORE_PORTRAYAL_WAR_TOKEN_COST = 50;
exports.RESTORE_PORTRAYAL_WAR_TOKEN_COST = RESTORE_PORTRAYAL_WAR_TOKEN_COST;
var RESTORE_PORTRAYAL_GOLD_COST = 598;
exports.RESTORE_PORTRAYAL_GOLD_COST = RESTORE_PORTRAYAL_GOLD_COST;
// 购买自选英雄费用 元宝
var BUY_OPT_HERO_COST = 999;
exports.BUY_OPT_HERO_COST = BUY_OPT_HERO_COST;
// 英雄自选礼包
var HERO_OPT_GIFT = {
    // 陈到, 徐盛, 张辽
    1: [310101, 320101, 340101],
    // 陈到, 李嗣业, 徐盛, 黄盖, 王异, 张辽, 徐晃
    2: [310101, 310401, 320101, 320401, 330301, 340101, 340601],
    // 3: 全自选
    // 陈到, 张郃, 李嗣业, 文鸯, 徐盛, 曹仁, 张飞, 黄盖, 刘宠, 王异, 曹休, 张辽, 许诸, 夏侯渊, 徐晃
    4: [310101, 310201, 310401, 310601, 320101, 320201, 320301, 320401, 330202, 330301, 330501, 340101, 340401, 340501, 340601],
};
exports.HERO_OPT_GIFT = HERO_OPT_GIFT;
// 英雄复活时间
var HERO_REVIVES_TIME = 3600000 * 5;
exports.HERO_REVIVES_TIME = HERO_REVIVES_TIME;
// 英雄槽位等级开启条件
var HERO_SLOT_LV_COND = [1, 10, 20];
exports.HERO_SLOT_LV_COND = HERO_SLOT_LV_COND;
// 养由基召唤时的对应等级
var SUMMON_LV = {
    1: 1,
    2: 2,
    3: 4,
    4: 6,
    5: 8,
    6: 10,
};
exports.SUMMON_LV = SUMMON_LV;
// 默认的宠物id
var DEFAULT_PET_ID = 4101;
exports.DEFAULT_PET_ID = DEFAULT_PET_ID;
// 矛
var SPEAR_PAWN_ID = 3701;
exports.SPEAR_PAWN_ID = SPEAR_PAWN_ID;
// 火
var FIRE_PAWN_ID = 3702;
exports.FIRE_PAWN_ID = FIRE_PAWN_ID;
// 资源
var RES_MAP = (_q = {},
    _q[Enums_1.CType.CEREAL] = true,
    _q[Enums_1.CType.TIMBER] = true,
    _q[Enums_1.CType.STONE] = true,
    _q[Enums_1.CType.BASE_RES] = true,
    _q[Enums_1.CType.EXP_BOOK] = true,
    _q[Enums_1.CType.IRON] = true,
    _q[Enums_1.CType.UP_SCROLL] = true,
    _q[Enums_1.CType.FIXATOR] = true,
    _q);
exports.RES_MAP = RES_MAP;
// 最多可标记多少个
var MAX_MAP_MARK_COUNT = 10;
exports.MAX_MAP_MARK_COUNT = MAX_MAP_MARK_COUNT;
// 申请联盟个数限制
var ALLI_APPLY_MAX_COUNT = 3;
exports.ALLI_APPLY_MAX_COUNT = ALLI_APPLY_MAX_COUNT;
// 大厅模式对应的底部
var LOBBY_MODE_BUTTOM_NAME = (_r = {},
    _r[Enums_1.LobbyModeType.FREE] = 'team',
    _r[Enums_1.LobbyModeType.NEWBIE] = 'team',
    _r[Enums_1.LobbyModeType.RANKED] = 'team',
    _r[Enums_1.LobbyModeType.SNAIL_ISLE] = 'twomiles',
    _r);
exports.LOBBY_MODE_BUTTOM_NAME = LOBBY_MODE_BUTTOM_NAME;
// 斜度
var SKEW_ANGLE = 45;
exports.SKEW_ANGLE = SKEW_ANGLE;
// 斜着的外宽高
var SKEW_SIZE = cc.size(16, 8);
exports.SKEW_SIZE = SKEW_SIZE;
// 斜着的内宽高 32 16
var SKEW_SIZE_HALF = cc.size(SKEW_SIZE.width * 0.5, SKEW_SIZE.height * 0.5);
exports.SKEW_SIZE_HALF = SKEW_SIZE_HALF;
// 段位商城的兵符配置
var RANK_SHOP_WAR_TOKEN_CONFIG = [
    { warToken: 10, coin: 10 },
    { warToken: 100, coin: 100 },
    { warToken: 1000, coin: 1000 },
    { warToken: 10000, coin: 10000 },
];
exports.RANK_SHOP_WAR_TOKEN_CONFIG = RANK_SHOP_WAR_TOKEN_CONFIG;
// 战斗的血条颜色
var BATTLE_HPBAR_COLOR = {
    m: { bar: '#8BE273', bg: '#162D20' },
    f: { bar: '#6DB5E2', bg: '#121D3A' },
    0: { bar: '#EE2A4A', bg: '#3B1316' },
    1: { bar: '#FF64B8', bg: '#41142C' },
    2: { bar: '#AD64FF', bg: '#281240' },
    3: { bar: '#FF9648', bg: '#4A2B14' },
};
exports.BATTLE_HPBAR_COLOR = BATTLE_HPBAR_COLOR;
// 战斗的火焰颜色
var BATTLE_FIRE_COLOR = {
    m: '#53B977',
    f: '#40A4E9',
    0: '#B90900',
    1: '#FF76F7',
    2: '#AD64FF',
    3: '#FFA836',
};
exports.BATTLE_FIRE_COLOR = BATTLE_FIRE_COLOR;
// 战令价格配置
var RECHARGE_BATTLE_PASS = 'jwm_up_book'; // $8.99
exports.RECHARGE_BATTLE_PASS = RECHARGE_BATTLE_PASS;
// 战令经验购买配置
var RECHARGE_BATTLE_PASS_EXP = [50, 100]; // 50元宝购买100经验
exports.RECHARGE_BATTLE_PASS_EXP = RECHARGE_BATTLE_PASS_EXP;
// 有奖问卷调查id
var PRIZE_QUESTION_ID = 99900001;
exports.PRIZE_QUESTION_ID = PRIZE_QUESTION_ID;
// 有奖问卷调查期限
var PRIZE_QUESTION_TIME = ['2024-12-26-06-00', '2024-12-30-06-00'];
exports.PRIZE_QUESTION_TIME = PRIZE_QUESTION_TIME;
// 打开允许通知弹窗的公共CD
var NOTICE_PERMISSION_CD = 24 * 60 * 60 * 1000; // 24小时
exports.NOTICE_PERMISSION_CD = NOTICE_PERMISSION_CD;
// 每日屯田次数
var TODAY_TONDEN_MAX_COUNT = 10;
exports.TODAY_TONDEN_MAX_COUNT = TODAY_TONDEN_MAX_COUNT;
// 屯田资源获取比例
var TONDEN_GET_RES_RATIO = 1;
exports.TONDEN_GET_RES_RATIO = TONDEN_GET_RES_RATIO;
// 屯田奖励点消耗倍数
var TONDEN_STAMINA_MUL = 3;
exports.TONDEN_STAMINA_MUL = TONDEN_STAMINA_MUL;
// 医馆伤兵上限
var HOSPITAL_PAWN_LIMIT = 200;
exports.HOSPITAL_PAWN_LIMIT = HOSPITAL_PAWN_LIMIT;
// 各等级士兵战败后回馆概率
var GO_HOSPITAL_CHANCE = {
    1: '0%',
    2: '100%',
    3: '100%',
    4: '100%',
    5: '100%',
    6: '100%',
};
exports.GO_HOSPITAL_CHANCE = GO_HOSPITAL_CHANCE;
// 画像的天选几率
var PORTRAYAL_CHOSENONE_ODDS = 0.005;
exports.PORTRAYAL_CHOSENONE_ODDS = PORTRAYAL_CHOSENONE_ODDS;
// 研究类型转评论类型
var STUDY_TO_BOOKTYPE = (_s = {},
    _s[Enums_1.StudyType.POLICY] = Enums_1.BookCommentType.POLICY,
    _s[Enums_1.StudyType.PAWN] = Enums_1.BookCommentType.PAWN,
    _s[Enums_1.StudyType.EQUIP] = Enums_1.BookCommentType.EQUIP,
    _s[Enums_1.StudyType.EXCLUSIVE] = Enums_1.BookCommentType.EQUIP,
    _s);
exports.STUDY_TO_BOOKTYPE = STUDY_TO_BOOKTYPE;
// 盟主投票最大次数
var ALLI_LEADER_VOTE_MAX_COUNT = 4;
exports.ALLI_LEADER_VOTE_MAX_COUNT = ALLI_LEADER_VOTE_MAX_COUNT;
// 招募动态资源每级系数
var PAWN_COST_LV_LIST = [1, 2, 4, 6, 8, 10];
exports.PAWN_COST_LV_LIST = PAWN_COST_LV_LIST;
// 工厂解锁配置
var FACTORY_SLOT_CONF = [5];
exports.FACTORY_SLOT_CONF = FACTORY_SLOT_CONF;
// 摄像机背景颜色
var CAMERA_BG_COLOR = ['#ACC961', '#88CA6E', '#E4B765', '#A7E2E3'];
exports.CAMERA_BG_COLOR = CAMERA_BG_COLOR;
//
var MAP_MASK_ITEM_COLOR = ['#2B8A85', '#1755AC', '#832E4F', '#8378C2'];
exports.MAP_MASK_ITEM_COLOR = MAP_MASK_ITEM_COLOR;
// 区域内的地面颜色
var AREA_DI_COLOR_CONF = [
    // 春
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#AFC864', battle: ['#CED974', '#BCD16A'], build: '#EFE28C' },
    },
    // 夏
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' },
    },
    // 秋
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' },
    },
    // 冬
    {
        0: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        3: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#D8C069' },
        4: { bg: '#AFC864', battle: ['#EFE28C', '#EBD784'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#D8C069', battle: ['#EFE28C', '#EBD784'], build: '#EFE28C' },
    },
];
exports.AREA_DI_COLOR_CONF = AREA_DI_COLOR_CONF;
// 难度底板颜色
var DIFFICULTY_BG_COLOR = {
    1: '#81A514',
    2: '#6683AB',
    3: '#9C58BF',
    4: '#CE59A0',
    5: '#C34B3F',
};
exports.DIFFICULTY_BG_COLOR = DIFFICULTY_BG_COLOR;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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