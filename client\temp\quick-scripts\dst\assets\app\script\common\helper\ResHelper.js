
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/ResHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a19724Y6I5IRa5AlxX2Mhvc', 'ResHelper');
// app/script/common/helper/ResHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resHelper = void 0;
var Constant_1 = require("../constant/Constant");
var OutlineShaderCtrl_1 = require("../shader/OutlineShaderCtrl");
var GameHelper_1 = require("./GameHelper");
/**
 * 游戏中的资源相关帮助方法
 */
var ResHelper = /** @class */ (function () {
    function ResHelper() {
        this.lands = {};
        this.landMap = {};
        this.seawavePrefabs = {};
        this.cityPrefabs = {};
        this.mapFlagNumbers = {};
        this.spriteDefaultMaterial = null;
        this.sprite2dGrayMaterial = null;
    }
    ResHelper.prototype.init = function (progessCallback) {
        return __awaiter(this, void 0, void 0, function () {
            var sfs, pfbs;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.lands = {};
                        assetsMgr.debug = false;
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('land', cc.SpriteFrame, '_land_res_', function (done, total) { return progessCallback(done / total); })];
                    case 1:
                        sfs = _a.sent();
                        sfs.forEach(function (m) { return _this.lands[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('seawave', cc.Prefab, '_seawave_prefab_', function (done, total) { return progessCallback(done / total); })];
                    case 2:
                        pfbs = _a.sent();
                        pfbs.forEach(function (m) { return _this.seawavePrefabs[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('city', cc.Prefab, '_city_prefab_', function (done, total) { return progessCallback(done / total); })];
                    case 3:
                        pfbs = _a.sent();
                        pfbs.forEach(function (m) { return _this.cityPrefabs[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('map_flag_num', cc.SpriteFrame, '_land_res_', function (done, total) { return progessCallback(done / total); })];
                    case 4:
                        // 地图标记数字
                        sfs = _a.sent();
                        sfs.forEach(function (m) { return _this.mapFlagNumbers[m.name] = m; });
                        assetsMgr.debug = true;
                        // 材质
                        this.spriteDefaultMaterial = cc.Material.getBuiltinMaterial('2d-sprite');
                        this.sprite2dGrayMaterial = cc.Material.getBuiltinMaterial('2d-gray-sprite');
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.initNovice = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('other/CITY_10010', cc.Prefab, key)];
                    case 1:
                        pfb = _a.sent();
                        this.cityPrefabs[pfb.name] = pfb;
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.cleanNovice = function () {
        delete this.cityPrefabs['CITY_10010'];
    };
    ResHelper.prototype.getPawnName = function (id) { return 'pawnText.name_' + id; };
    ResHelper.prototype.checkLandSkin = function (type) {
        return !!this.landMap[type];
    };
    // 初始化地块皮肤
    ResHelper.prototype.initLandSkin = function (type, progessCallback) {
        return __awaiter(this, void 0, void 0, function () {
            var obj, sfs;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        obj = this.landMap[type];
                        if (obj) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('land_' + type, cc.SpriteFrame, '_land_res_' + type, function (done, total) { return progessCallback && progessCallback(done / total); })];
                    case 1:
                        sfs = _a.sent();
                        obj = this.landMap[type] = {};
                        sfs.forEach(function (m) { return obj[m.name] = m; });
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.cleanLandSkin = function () {
        var type = GameHelper_1.gameHpr.world.getSeason().type;
        for (var k in this.landMap) {
            if (Number(k) !== type) {
                delete this.landMap[k];
                assetsMgr.releaseTempResByTag('_land_res_' + k);
            }
        }
    };
    // 根据下标获取节点
    ResHelper.prototype.getNodeByIndex = function (node, i, position) {
        var it = node.children[i] || cc.instantiate2(node.children[0], node);
        it.active = true;
        it.Data = null;
        it.setPosition(position);
        return it;
    };
    ResHelper.prototype.getNodeByIndex2 = function (node, i, position) {
        var it = node.children[i] || cc.instantiate2(node.children[0], node);
        it.active = true;
        it.Data = null;
        it.setPosition(position.x, position.y - Constant_1.TILE_SIZE_HALF.y);
        return it;
    };
    // 隐藏多于的
    ResHelper.prototype.hideNodeByIndex = function (node, idx) {
        for (var i = idx, l = node.childrenCount; i < l; i++) {
            var it = node.children[i];
            it.active = false;
            it.Data = null;
        }
    };
    // 清理子节点只剩1个
    ResHelper.prototype.cleanNodeChildren = function (node) {
        var _a;
        for (var i = node.childrenCount - 1; i >= 1; i--) {
            node.children[i].destroy();
        }
        (_a = node.children[0]) === null || _a === void 0 ? void 0 : _a.setActive(false);
    };
    // 获取sprite材质
    ResHelper.prototype.get2dSpriteMaterial = function (unlock) {
        return unlock ? this.spriteDefaultMaterial : this.sprite2dGrayMaterial;
    };
    // 纯色灰材质
    ResHelper.prototype.getSpriteColorGrayMaterial = function (unlock) {
        return unlock ? this.spriteDefaultMaterial : assetsMgr.getMaterial('SpriteColorGrey');
    };
    // 地面icon
    ResHelper.prototype.getLandIcon = function (icon) {
        if (icon.startsWith('land_')) {
            return this.getLandItemIcon(icon, GameHelper_1.gameHpr.world.getSeasonType());
        }
        return this.lands[icon];
    };
    // 地面资源icon
    ResHelper.prototype.getLandItemIcon = function (icon, type) {
        var _a;
        return ((_a = this.landMap[type]) === null || _a === void 0 ? void 0 : _a[icon]) || this.lands[icon] || null;
    };
    // 获取海浪预制体
    ResHelper.prototype.getSeawavePrefab = function (id) {
        return this.seawavePrefabs['SEAWAVE_' + id];
    };
    // 获取城市预制体
    ResHelper.prototype.getCityPrefab = function (id) {
        return this.cityPrefabs['CITY_' + id];
    };
    // 获取资源icon
    ResHelper.prototype.getResIcon = function (type) {
        return assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
    };
    // 获取地图标记数字
    ResHelper.prototype.getMapFlagNumber = function (flag) {
        var key = flag <= 0 ? 'x' : (flag - 1);
        return this.mapFlagNumbers['map_flag_' + key];
    };
    // 加载icon
    ResHelper.prototype.loadIcon = function (url, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            var spr, sf;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!url || !icon || !icon.isValid) {
                            return [2 /*return*/, null];
                        }
                        spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite);
                        if (!spr) {
                            return [2 /*return*/, null];
                        }
                        else if (setEmpty) {
                            spr.spriteFrame = null;
                        }
                        spr['__load_icon_url'] = url;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.SpriteFrame, key)];
                    case 1:
                        sf = _a.sent();
                        if (spr.isValid && spr['__load_icon_url'] === url) {
                            spr.spriteFrame = sf || null;
                        }
                        return [2 /*return*/, spr];
                }
            });
        });
    };
    // 加载设施icon
    ResHelper.prototype.loadBuildIcon = function (url, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('build/' + url, icon, key)];
            });
        });
    };
    // 加载城市icon
    ResHelper.prototype.loadCityIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('city/city_' + id, icon, key)];
            });
        });
    };
    // 加载士兵头像icon
    ResHelper.prototype.loadPawnHeadIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (id === 3405104) { //牛仔隐藏款 特殊处理
                    return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id + "_00", icon, key)];
                }
                return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id + "_01", icon, key)];
            });
        });
    };
    // 加载士兵小头像icon
    ResHelper.prototype.loadPawnHeadMiniIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载技能图标
    ResHelper.prototype.loadSkillIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('skill/skill_' + id, icon, key)];
            });
        });
    };
    // 加载装备icon
    ResHelper.prototype.loadEquipIcon = function (id, icon, key, smeltLv) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var spr, outline;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.loadIcon('equip/equip_' + id, icon, key)];
                    case 1:
                        spr = _b.sent();
                        if (!spr || !spr.isValid) {
                        }
                        else if (smeltLv) {
                            outline = spr.getComponent(OutlineShaderCtrl_1.default) || spr.addComponent(OutlineShaderCtrl_1.default);
                            outline.setTarget(spr);
                            outline.setOutlineSize(2);
                            outline.setColor(ut.colorFromHEX(smeltLv === 1 ? '#58F1FF' : '#E488FF'));
                            outline.setVisible(true);
                            // spr.node.adaptScale(size, cc.size(size.width + 8, size.height + 8))
                        }
                        else {
                            (_a = spr.getComponent(OutlineShaderCtrl_1.default)) === null || _a === void 0 ? void 0 : _a.setVisible(false);
                            // spr.node.scale = 1
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载政策icon
    ResHelper.prototype.loadPolicyIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('policy/policy_' + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载buff icon
    ResHelper.prototype.loadBuffIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('buff_icon/buff_' + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载联盟图标
    ResHelper.prototype.loadAlliIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('alli_icon/alli_icon_' + id, icon, key)];
            });
        });
    };
    // 加载评分图标
    ResHelper.prototype.loadRankScoreIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('rank_icon/rank_icon_' + (id >= 0 ? id : 'none'), icon, key)];
            });
        });
    };
    // 加载礼物图标
    ResHelper.prototype.loadGiftIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('gift/gift_' + id, icon, key)];
            });
        });
    };
    // 加载表情
    ResHelper.prototype.loadEmojiIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('emoji/emoji_' + id, icon, key)];
            });
        });
    };
    // 加载画像
    ResHelper.prototype.loadPortrayalImage = function (id, icon, key) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_b) {
                if ((_a = assetsMgr.getJsonData('portrayalBase', id)) === null || _a === void 0 ? void 0 : _a.has_anim) {
                    return [2 /*return*/, this.loadIcon("portrayal_anim/" + id + "/portrayal_" + id + "_01", icon, key)];
                }
                return [2 /*return*/, this.loadIcon('portrayal/portrayal_' + id, icon, key)];
            });
        });
    };
    // 加载残卷遮挡
    ResHelper.prototype.loadPortrayalDebrisMask = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('portrayal/pd_mask_' + id, icon, key)];
            });
        });
    };
    // 加载英雄技能图标
    ResHelper.prototype.loadHeroSkillIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('hero_skill_icon/hero_skill_' + id, icon, key)];
            });
        });
    };
    // 加载英雄预制体
    ResHelper.prototype.loadHeroMarchPrefab = function (id, key) {
        return __awaiter(this, void 0, void 0, function () {
            var pfbName, pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        pfbName = 'ROLE_' + id;
                        return [4 /*yield*/, assetsMgr.loadTempRes('march/' + pfbName, cc.Prefab, key)];
                    case 1:
                        pfb = _a.sent();
                        if ((pfb === null || pfb === void 0 ? void 0 : pfb.name) !== pfbName) {
                            return [2 /*return*/, null];
                        }
                        return [2 /*return*/, pfb];
                }
            });
        });
    };
    // 加载植物种子图标
    ResHelper.prototype.loadBotanySeedIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('botany_seed/botany_seed_' + id, icon, key)];
            });
        });
    };
    // 加载表情节点
    ResHelper.prototype.loadEmojiNode = function (id, root, scale, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            var node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!root || !id) {
                            return [2 /*return*/];
                        }
                        else if (setEmpty) {
                            root.removeAllChildren();
                        }
                        return [4 /*yield*/, nodePoolMgr.get('emoji/EMOJI_' + id, key)];
                    case 1:
                        node = _a.sent();
                        if (node && root.isValid) {
                            node.parent = root;
                            node.active = true;
                            node.setPosition(0, 0);
                            node.scaleX = scale || 1;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载玩家头像
    ResHelper.prototype.loadPlayerHead = function (icon, url, key, setEmpty) {
        return __awaiter(this, void 0, void 0, function () {
            var spr, val, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(icon === null || icon === void 0 ? void 0 : icon.isValid)) {
                            return [2 /*return*/];
                        }
                        spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite);
                        if (!spr) {
                            return [2 /*return*/];
                        }
                        else if (setEmpty) {
                            spr.spriteFrame = null;
                            spr.node.removeAllChildren();
                        }
                        url = spr['_player_head_icon_'] = url || 'head_icon_free_001';
                        val = null;
                        if (!url.startsWith('head_icon_anim_')) return [3 /*break*/, 2];
                        return [4 /*yield*/, assetsMgr.loadTempRes('headicon/' + url, cc.Prefab, key)];
                    case 1:
                        val = _a.sent();
                        return [3 /*break*/, 6];
                    case 2:
                        if (!url.startsWith('head_icon_')) return [3 /*break*/, 4];
                        return [4 /*yield*/, assetsMgr.loadTempRes('headicon/' + url, cc.SpriteFrame, key)];
                    case 3:
                        val = _a.sent();
                        return [3 /*break*/, 6];
                    case 4: return [4 /*yield*/, assetsMgr.loadRemote(url, '.jpg', key || '_player_head_')];
                    case 5:
                        val = _a.sent();
                        _a.label = 6;
                    case 6:
                        if (spr.isValid && spr['_player_head_icon_'] === url) {
                            spr.node.removeAllChildren();
                            if (!val) {
                                spr.spriteFrame = null;
                            }
                            else if (val instanceof cc.Prefab) {
                                spr.spriteFrame = null;
                                node = cc.instantiate2(val, spr.node);
                                node.setContentSize(spr.node.width * spr.node.scaleX, spr.node.height * spr.node.scaleY);
                            }
                            else if (val instanceof cc.SpriteFrame) {
                                spr.spriteFrame = val;
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    return ResHelper;
}());
exports.resHelper = new ResHelper();
if (cc.sys.isBrowser) {
    window['resHelper'] = exports.resHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcaGVscGVyXFxSZXNIZWxwZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsaURBQWlFO0FBRWpFLGlFQUEyRDtBQUMzRCwyQ0FBc0M7QUFFdEM7O0dBRUc7QUFDSDtJQUFBO1FBRVksVUFBSyxHQUFzQyxFQUFFLENBQUE7UUFDN0MsWUFBTyxHQUF5RCxFQUFFLENBQUE7UUFDbEUsbUJBQWMsR0FBaUMsRUFBRSxDQUFBO1FBQ2pELGdCQUFXLEdBQWlDLEVBQUUsQ0FBQTtRQUM5QyxtQkFBYyxHQUFzQyxFQUFFLENBQUE7UUFFdEQsMEJBQXFCLEdBQWdCLElBQUksQ0FBQTtRQUN6Qyx5QkFBb0IsR0FBZ0IsSUFBSSxDQUFBO0lBdVRwRCxDQUFDO0lBclRnQix3QkFBSSxHQUFqQixVQUFrQixlQUEwQzs7Ozs7Ozt3QkFDeEQsSUFBSSxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUE7d0JBQ2YsU0FBUyxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUE7d0JBQ2IscUJBQU0sU0FBUyxDQUFDLGNBQWMsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLFdBQVcsRUFBRSxZQUFZLEVBQUUsVUFBQyxJQUFJLEVBQUUsS0FBSyxJQUFLLE9BQUEsZUFBZSxDQUFDLElBQUksR0FBRyxLQUFLLENBQUMsRUFBN0IsQ0FBNkIsQ0FBQyxFQUFBOzt3QkFBMUgsR0FBRyxHQUFHLFNBQW9IO3dCQUM5SCxHQUFHLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsS0FBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUF0QixDQUFzQixDQUFDLENBQUE7d0JBQzdCLHFCQUFNLFNBQVMsQ0FBQyxjQUFjLENBQUMsU0FBUyxFQUFFLEVBQUUsQ0FBQyxNQUFNLEVBQUUsa0JBQWtCLEVBQUUsVUFBQyxJQUFJLEVBQUUsS0FBSyxJQUFLLE9BQUEsZUFBZSxDQUFDLElBQUksR0FBRyxLQUFLLENBQUMsRUFBN0IsQ0FBNkIsQ0FBQyxFQUFBOzt3QkFBL0gsSUFBSSxHQUFHLFNBQXdIO3dCQUNuSSxJQUFJLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsS0FBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUEvQixDQUErQixDQUFDLENBQUE7d0JBQzNDLHFCQUFNLFNBQVMsQ0FBQyxjQUFjLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxNQUFNLEVBQUUsZUFBZSxFQUFFLFVBQUMsSUFBSSxFQUFFLEtBQUssSUFBSyxPQUFBLGVBQWUsQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFDLEVBQTdCLENBQTZCLENBQUMsRUFBQTs7d0JBQXpILElBQUksR0FBRyxTQUFrSCxDQUFBO3dCQUN6SCxJQUFJLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsS0FBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUE1QixDQUE0QixDQUFDLENBQUE7d0JBRXpDLHFCQUFNLFNBQVMsQ0FBQyxjQUFjLENBQUMsY0FBYyxFQUFFLEVBQUUsQ0FBQyxXQUFXLEVBQUUsWUFBWSxFQUFFLFVBQUMsSUFBSSxFQUFFLEtBQUssSUFBSyxPQUFBLGVBQWUsQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFDLEVBQTdCLENBQTZCLENBQUMsRUFBQTs7d0JBRGxJLFNBQVM7d0JBQ1QsR0FBRyxHQUFHLFNBQTRILENBQUE7d0JBQ2xJLEdBQUcsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxLQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQS9CLENBQStCLENBQUMsQ0FBQTt3QkFDakQsU0FBUyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUE7d0JBQ3RCLEtBQUs7d0JBQ0wsSUFBSSxDQUFDLHFCQUFxQixHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsa0JBQWtCLENBQUMsV0FBVyxDQUFDLENBQUE7d0JBQ3hFLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLGtCQUFrQixDQUFDLGdCQUFnQixDQUFDLENBQUE7Ozs7O0tBQy9FO0lBRVksOEJBQVUsR0FBdkIsVUFBd0IsR0FBVzs7Ozs7NEJBQ25CLHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsa0JBQWtCLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUMsRUFBQTs7d0JBQXJFLEdBQUcsR0FBRyxTQUErRDt3QkFDM0UsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEdBQUcsR0FBRyxDQUFBOzs7OztLQUNuQztJQUVNLCtCQUFXLEdBQWxCO1FBQ0ksT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxDQUFBO0lBQ3pDLENBQUM7SUFFTSwrQkFBVyxHQUFsQixVQUFtQixFQUFVLElBQUksT0FBTyxnQkFBZ0IsR0FBRyxFQUFFLENBQUEsQ0FBQyxDQUFDO0lBRXhELGlDQUFhLEdBQXBCLFVBQXFCLElBQVk7UUFDN0IsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQTtJQUMvQixDQUFDO0lBRUQsVUFBVTtJQUNHLGdDQUFZLEdBQXpCLFVBQTBCLElBQVksRUFBRSxlQUEyQzs7Ozs7O3dCQUMzRSxHQUFHLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQTt3QkFDNUIsSUFBSSxHQUFHLEVBQUU7NEJBQ0wsc0JBQU07eUJBQ1Q7d0JBQ1cscUJBQU0sU0FBUyxDQUFDLGNBQWMsQ0FBQyxPQUFPLEdBQUcsSUFBSSxFQUFFLEVBQUUsQ0FBQyxXQUFXLEVBQUUsWUFBWSxHQUFHLElBQUksRUFBRSxVQUFDLElBQUksRUFBRSxLQUFLLElBQUssT0FBQSxlQUFlLElBQUksZUFBZSxDQUFDLElBQUksR0FBRyxLQUFLLENBQUMsRUFBaEQsQ0FBZ0QsQ0FBQyxFQUFBOzt3QkFBNUosR0FBRyxHQUFHLFNBQXNKO3dCQUNsSyxHQUFHLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLENBQUE7d0JBQzdCLEdBQUcsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxHQUFHLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBZixDQUFlLENBQUMsQ0FBQTs7Ozs7S0FDcEM7SUFFTSxpQ0FBYSxHQUFwQjtRQUNJLElBQU0sSUFBSSxHQUFHLG9CQUFPLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQUksQ0FBQTtRQUMzQyxLQUFLLElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7WUFDeEIsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssSUFBSSxFQUFFO2dCQUNwQixPQUFPLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUE7Z0JBQ3RCLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxZQUFZLEdBQUcsQ0FBQyxDQUFDLENBQUE7YUFDbEQ7U0FDSjtJQUNMLENBQUM7SUFFRCxXQUFXO0lBQ0osa0NBQWMsR0FBckIsVUFBc0IsSUFBYSxFQUFFLENBQVMsRUFBRSxRQUFpQjtRQUM3RCxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUN0RSxFQUFFLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtRQUNoQixFQUFFLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNkLEVBQUUsQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLENBQUE7UUFDeEIsT0FBTyxFQUFFLENBQUE7SUFDYixDQUFDO0lBQ00sbUNBQWUsR0FBdEIsVUFBdUIsSUFBYSxFQUFFLENBQVMsRUFBRSxRQUFpQjtRQUM5RCxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUN0RSxFQUFFLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtRQUNoQixFQUFFLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNkLEVBQUUsQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLENBQUMsRUFBRSxRQUFRLENBQUMsQ0FBQyxHQUFHLHlCQUFjLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDekQsT0FBTyxFQUFFLENBQUE7SUFDYixDQUFDO0lBRUQsUUFBUTtJQUNELG1DQUFlLEdBQXRCLFVBQXVCLElBQWEsRUFBRSxHQUFXO1FBQzdDLEtBQUssSUFBSSxDQUFDLEdBQUcsR0FBRyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDbEQsSUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUMzQixFQUFFLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtZQUNqQixFQUFFLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtTQUNqQjtJQUNMLENBQUM7SUFFRCxZQUFZO0lBQ0wscUNBQWlCLEdBQXhCLFVBQXlCLElBQWE7O1FBQ2xDLEtBQUssSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLGFBQWEsR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUM5QyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFBO1NBQzdCO1FBQ0QsTUFBQSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQywwQ0FBRSxTQUFTLENBQUMsS0FBSyxFQUFDO0lBQ3RDLENBQUM7SUFFRCxhQUFhO0lBQ04sdUNBQW1CLEdBQTFCLFVBQTJCLE1BQWU7UUFDdEMsT0FBTyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFBO0lBQzFFLENBQUM7SUFFRCxRQUFRO0lBQ0QsOENBQTBCLEdBQWpDLFVBQWtDLE1BQWU7UUFDN0MsT0FBTyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBO0lBQ3pGLENBQUM7SUFFRCxTQUFTO0lBQ0YsK0JBQVcsR0FBbEIsVUFBbUIsSUFBWTtRQUMzQixJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLEVBQUU7WUFDMUIsT0FBTyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksRUFBRSxvQkFBTyxDQUFDLEtBQUssQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFBO1NBQ25FO1FBQ0QsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQzNCLENBQUM7SUFFRCxXQUFXO0lBQ0osbUNBQWUsR0FBdEIsVUFBdUIsSUFBWSxFQUFFLElBQVk7O1FBQzdDLE9BQU8sT0FBQSxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQywwQ0FBRyxJQUFJLE1BQUssSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxJQUFJLENBQUE7SUFDakUsQ0FBQztJQUVELFVBQVU7SUFDSCxvQ0FBZ0IsR0FBdkIsVUFBd0IsRUFBVTtRQUM5QixPQUFPLElBQUksQ0FBQyxjQUFjLENBQUMsVUFBVSxHQUFHLEVBQUUsQ0FBQyxDQUFBO0lBQy9DLENBQUM7SUFFRCxVQUFVO0lBQ0gsaUNBQWEsR0FBcEIsVUFBcUIsRUFBVTtRQUMzQixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsT0FBTyxHQUFHLEVBQUUsQ0FBQyxDQUFBO0lBQ3pDLENBQUM7SUFFRCxXQUFXO0lBQ0osOEJBQVUsR0FBakIsVUFBa0IsSUFBVztRQUN6QixPQUFPLFNBQVMsQ0FBQyxRQUFRLENBQUMscUJBQVUsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFBO0lBQy9DLENBQUM7SUFFRCxXQUFXO0lBQ0osb0NBQWdCLEdBQXZCLFVBQXdCLElBQVk7UUFDaEMsSUFBTSxHQUFHLEdBQUcsSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksR0FBRyxDQUFDLENBQUMsQ0FBQTtRQUN4QyxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUMsV0FBVyxHQUFHLEdBQUcsQ0FBQyxDQUFBO0lBQ2pELENBQUM7SUFFRCxTQUFTO0lBQ0ksNEJBQVEsR0FBckIsVUFBc0IsR0FBVyxFQUFFLElBQXlCLEVBQUUsR0FBVyxFQUFFLFFBQXdCO1FBQXhCLHlCQUFBLEVBQUEsZUFBd0I7Ozs7Ozt3QkFDL0YsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUU7NEJBQ2hDLHNCQUFPLElBQUksRUFBQTt5QkFDZDt3QkFDSyxHQUFHLEdBQUcsSUFBSSxZQUFZLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUE7d0JBQ3hFLElBQUksQ0FBQyxHQUFHLEVBQUU7NEJBQ04sc0JBQU8sSUFBSSxFQUFBO3lCQUNkOzZCQUFNLElBQUksUUFBUSxFQUFFOzRCQUNqQixHQUFHLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQTt5QkFDekI7d0JBQ0QsR0FBRyxDQUFDLGlCQUFpQixDQUFDLEdBQUcsR0FBRyxDQUFBO3dCQUNqQixxQkFBTSxTQUFTLENBQUMsV0FBVyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQUMsV0FBVyxFQUFFLEdBQUcsQ0FBQyxFQUFBOzt3QkFBMUQsRUFBRSxHQUFHLFNBQXFEO3dCQUNoRSxJQUFJLEdBQUcsQ0FBQyxPQUFPLElBQUksR0FBRyxDQUFDLGlCQUFpQixDQUFDLEtBQUssR0FBRyxFQUFFOzRCQUMvQyxHQUFHLENBQUMsV0FBVyxHQUFHLEVBQUUsSUFBSSxJQUFJLENBQUE7eUJBQy9CO3dCQUNELHNCQUFPLEdBQUcsRUFBQTs7OztLQUNiO0lBRUQsV0FBVztJQUNFLGlDQUFhLEdBQTFCLFVBQTJCLEdBQVcsRUFBRSxJQUF5QixFQUFFLEdBQVc7OztnQkFDMUUsc0JBQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLEdBQUcsR0FBRyxFQUFFLElBQUksRUFBRSxHQUFHLENBQUMsRUFBQTs7O0tBQ2xEO0lBRUQsV0FBVztJQUNFLGdDQUFZLEdBQXpCLFVBQTBCLEVBQVUsRUFBRSxJQUF5QixFQUFFLEdBQVc7OztnQkFDeEUsc0JBQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxZQUFZLEdBQUcsRUFBRSxFQUFFLElBQUksRUFBRSxHQUFHLENBQUMsRUFBQTs7O0tBQ3JEO0lBRUQsYUFBYTtJQUNBLG9DQUFnQixHQUE3QixVQUE4QixFQUFVLEVBQUUsSUFBeUIsRUFBRSxHQUFXOzs7Z0JBQzVFLElBQUksRUFBRSxLQUFLLE9BQU8sRUFBRSxFQUFFLFlBQVk7b0JBQzlCLHNCQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsVUFBUSxFQUFFLGNBQVMsRUFBRSxRQUFLLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFBO2lCQUM5RDtnQkFDRCxzQkFBTyxJQUFJLENBQUMsUUFBUSxDQUFDLFVBQVEsRUFBRSxjQUFTLEVBQUUsUUFBSyxFQUFFLElBQUksRUFBRSxHQUFHLENBQUMsRUFBQTs7O0tBQzlEO0lBRUQsY0FBYztJQUNELHdDQUFvQixHQUFqQyxVQUFrQyxFQUFVLEVBQUUsSUFBeUIsRUFBRSxHQUFXLEVBQUUsUUFBd0I7UUFBeEIseUJBQUEsRUFBQSxlQUF3Qjs7O2dCQUMxRyxzQkFBTyxJQUFJLENBQUMsUUFBUSxDQUFDLFVBQVEsRUFBRSxjQUFTLEVBQUksRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLFFBQVEsQ0FBQyxFQUFBOzs7S0FDckU7SUFFRCxTQUFTO0lBQ0ksaUNBQWEsR0FBMUIsVUFBMkIsRUFBVSxFQUFFLElBQXlCLEVBQUUsR0FBVzs7O2dCQUN6RSxzQkFBTyxJQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsR0FBRyxFQUFFLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFBOzs7S0FDdkQ7SUFFRCxXQUFXO0lBQ0UsaUNBQWEsR0FBMUIsVUFBMkIsRUFBVSxFQUFFLElBQXlCLEVBQUUsR0FBVyxFQUFFLE9BQWdCOzs7Ozs7NEJBQy9FLHFCQUFNLElBQUksQ0FBQyxRQUFRLENBQUMsY0FBYyxHQUFHLEVBQUUsRUFBRSxJQUFJLEVBQUUsR0FBRyxDQUFDLEVBQUE7O3dCQUF6RCxHQUFHLEdBQUcsU0FBbUQ7d0JBQy9ELElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFO3lCQUN6Qjs2QkFBTSxJQUFJLE9BQU8sRUFBRTs0QkFFVixPQUFPLEdBQUcsR0FBRyxDQUFDLFlBQVksQ0FBQywyQkFBaUIsQ0FBQyxJQUFJLEdBQUcsQ0FBQyxZQUFZLENBQUMsMkJBQWlCLENBQUMsQ0FBQTs0QkFDMUYsT0FBTyxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsQ0FBQTs0QkFDdEIsT0FBTyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsQ0FBQTs0QkFDekIsT0FBTyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsWUFBWSxDQUFDLE9BQU8sS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQTs0QkFDeEUsT0FBTyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsQ0FBQTs0QkFDeEIsc0VBQXNFO3lCQUN6RTs2QkFBTTs0QkFDSCxNQUFBLEdBQUcsQ0FBQyxZQUFZLENBQUMsMkJBQWlCLENBQUMsMENBQUUsVUFBVSxDQUFDLEtBQUssRUFBQzs0QkFDdEQscUJBQXFCO3lCQUN4Qjs7Ozs7S0FDSjtJQUVELFdBQVc7SUFDRSxrQ0FBYyxHQUEzQixVQUE0QixFQUFVLEVBQUUsSUFBeUIsRUFBRSxHQUFXLEVBQUUsUUFBd0I7UUFBeEIseUJBQUEsRUFBQSxlQUF3Qjs7O2dCQUNwRyxzQkFBTyxJQUFJLENBQUMsUUFBUSxDQUFDLGdCQUFnQixHQUFHLEVBQUUsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLFFBQVEsQ0FBQyxFQUFBOzs7S0FDbkU7SUFFRCxjQUFjO0lBQ0QsZ0NBQVksR0FBekIsVUFBMEIsRUFBVSxFQUFFLElBQXlCLEVBQUUsR0FBVyxFQUFFLFFBQXdCO1FBQXhCLHlCQUFBLEVBQUEsZUFBd0I7OztnQkFDbEcsc0JBQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxpQkFBaUIsR0FBRyxFQUFFLEVBQUUsSUFBSSxFQUFFLEdBQUcsRUFBRSxRQUFRLENBQUMsRUFBQTs7O0tBQ3BFO0lBRUQsU0FBUztJQUNJLGdDQUFZLEdBQXpCLFVBQTBCLEVBQVUsRUFBRSxJQUF5QixFQUFFLEdBQVc7OztnQkFDeEUsc0JBQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxzQkFBc0IsR0FBRyxFQUFFLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFBOzs7S0FDL0Q7SUFFRCxTQUFTO0lBQ0kscUNBQWlCLEdBQTlCLFVBQStCLEVBQVUsRUFBRSxJQUF5QixFQUFFLEdBQVc7OztnQkFDN0Usc0JBQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxzQkFBc0IsR0FBRyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFBOzs7S0FDcEY7SUFFRCxTQUFTO0lBQ0ksZ0NBQVksR0FBekIsVUFBMEIsRUFBVSxFQUFFLElBQXlCLEVBQUUsR0FBVzs7O2dCQUN4RSxzQkFBTyxJQUFJLENBQUMsUUFBUSxDQUFDLFlBQVksR0FBRyxFQUFFLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFBOzs7S0FDckQ7SUFFRCxPQUFPO0lBQ00saUNBQWEsR0FBMUIsVUFBMkIsRUFBVSxFQUFFLElBQXlCLEVBQUUsR0FBVzs7O2dCQUN6RSxzQkFBTyxJQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsR0FBRyxFQUFFLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFBOzs7S0FDdkQ7SUFFRCxPQUFPO0lBQ00sc0NBQWtCLEdBQS9CLFVBQWdDLEVBQVUsRUFBRSxJQUF5QixFQUFFLEdBQVc7Ozs7Z0JBQzlFLFVBQUksU0FBUyxDQUFDLFdBQVcsQ0FBQyxlQUFlLEVBQUUsRUFBRSxDQUFDLDBDQUFFLFFBQVEsRUFBRTtvQkFDdEQsc0JBQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxvQkFBa0IsRUFBRSxtQkFBYyxFQUFFLFFBQUssRUFBRSxJQUFJLEVBQUUsR0FBRyxDQUFDLEVBQUE7aUJBQzdFO2dCQUNELHNCQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsc0JBQXNCLEdBQUcsRUFBRSxFQUFFLElBQUksRUFBRSxHQUFHLENBQUMsRUFBQTs7O0tBQy9EO0lBRUQsU0FBUztJQUNJLDJDQUF1QixHQUFwQyxVQUFxQyxFQUFVLEVBQUUsSUFBeUIsRUFBRSxHQUFXOzs7Z0JBQ25GLHNCQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsb0JBQW9CLEdBQUcsRUFBRSxFQUFFLElBQUksRUFBRSxHQUFHLENBQUMsRUFBQTs7O0tBQzdEO0lBRUQsV0FBVztJQUNFLHFDQUFpQixHQUE5QixVQUErQixFQUFVLEVBQUUsSUFBeUIsRUFBRSxHQUFXOzs7Z0JBQzdFLHNCQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsNkJBQTZCLEdBQUcsRUFBRSxFQUFFLElBQUksRUFBRSxHQUFHLENBQUMsRUFBQTs7O0tBQ3RFO0lBRUQsVUFBVTtJQUNHLHVDQUFtQixHQUFoQyxVQUFpQyxFQUFVLEVBQUUsR0FBVzs7Ozs7O3dCQUM5QyxPQUFPLEdBQUcsT0FBTyxHQUFHLEVBQUUsQ0FBQTt3QkFDaEIscUJBQU0sU0FBUyxDQUFDLFdBQVcsQ0FBQyxRQUFRLEdBQUcsT0FBTyxFQUFFLEVBQUUsQ0FBQyxNQUFNLEVBQUUsR0FBRyxDQUFDLEVBQUE7O3dCQUFyRSxHQUFHLEdBQUcsU0FBK0Q7d0JBQzNFLElBQUksQ0FBQSxHQUFHLGFBQUgsR0FBRyx1QkFBSCxHQUFHLENBQUUsSUFBSSxNQUFLLE9BQU8sRUFBRTs0QkFDdkIsc0JBQU8sSUFBSSxFQUFBO3lCQUNkO3dCQUNELHNCQUFPLEdBQUcsRUFBQTs7OztLQUNiO0lBRUQsV0FBVztJQUNFLHNDQUFrQixHQUEvQixVQUFnQyxFQUFVLEVBQUUsSUFBeUIsRUFBRSxHQUFXOzs7Z0JBQzlFLHNCQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsMEJBQTBCLEdBQUcsRUFBRSxFQUFFLElBQUksRUFBRSxHQUFHLENBQUMsRUFBQTs7O0tBQ25FO0lBRUQsU0FBUztJQUNJLGlDQUFhLEdBQTFCLFVBQTJCLEVBQVUsRUFBRSxJQUFhLEVBQUUsS0FBYSxFQUFFLEdBQVcsRUFBRSxRQUF3QjtRQUF4Qix5QkFBQSxFQUFBLGVBQXdCOzs7Ozs7d0JBQ3RHLElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxFQUFFLEVBQUU7NEJBQ2Qsc0JBQU07eUJBQ1Q7NkJBQU0sSUFBSSxRQUFRLEVBQUU7NEJBQ2pCLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO3lCQUMzQjt3QkFDWSxxQkFBTSxXQUFXLENBQUMsR0FBRyxDQUFDLGNBQWMsR0FBRyxFQUFFLEVBQUUsR0FBRyxDQUFDLEVBQUE7O3dCQUF0RCxJQUFJLEdBQUcsU0FBK0M7d0JBQzVELElBQUksSUFBSSxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7NEJBQ3RCLElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFBOzRCQUNsQixJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTs0QkFDbEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUE7NEJBQ3RCLElBQUksQ0FBQyxNQUFNLEdBQUcsS0FBSyxJQUFJLENBQUMsQ0FBQTt5QkFDM0I7Ozs7O0tBQ0o7SUFFRCxTQUFTO0lBQ0ksa0NBQWMsR0FBM0IsVUFBNEIsSUFBeUIsRUFBRSxHQUFXLEVBQUUsR0FBVyxFQUFFLFFBQWtCOzs7Ozs7d0JBQy9GLElBQUksRUFBQyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsT0FBTyxDQUFBLEVBQUU7NEJBQ2hCLHNCQUFNO3lCQUNUO3dCQUNLLEdBQUcsR0FBRyxJQUFJLFlBQVksRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQTt3QkFDeEUsSUFBSSxDQUFDLEdBQUcsRUFBRTs0QkFDTixzQkFBTTt5QkFDVDs2QkFBTSxJQUFJLFFBQVEsRUFBRTs0QkFDakIsR0FBRyxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUE7NEJBQ3RCLEdBQUcsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQTt5QkFDL0I7d0JBQ0QsR0FBRyxHQUFHLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQyxHQUFHLEdBQUcsSUFBSSxvQkFBb0IsQ0FBQTt3QkFDekQsR0FBRyxHQUFRLElBQUksQ0FBQTs2QkFDZixHQUFHLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLEVBQWpDLHdCQUFpQzt3QkFDM0IscUJBQU0sU0FBUyxDQUFDLFdBQVcsQ0FBQyxXQUFXLEdBQUcsR0FBRyxFQUFFLEVBQUUsQ0FBQyxNQUFNLEVBQUUsR0FBRyxDQUFDLEVBQUE7O3dCQUFwRSxHQUFHLEdBQUcsU0FBOEQsQ0FBQTs7OzZCQUM3RCxHQUFHLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxFQUE1Qix3QkFBNEI7d0JBQzdCLHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsV0FBVyxHQUFHLEdBQUcsRUFBRSxFQUFFLENBQUMsV0FBVyxFQUFFLEdBQUcsQ0FBQyxFQUFBOzt3QkFBekUsR0FBRyxHQUFHLFNBQW1FLENBQUE7OzRCQUVuRSxxQkFBTSxTQUFTLENBQUMsVUFBVSxDQUFDLEdBQUcsRUFBRSxNQUFNLEVBQUUsR0FBRyxJQUFJLGVBQWUsQ0FBQyxFQUFBOzt3QkFBckUsR0FBRyxHQUFHLFNBQStELENBQUE7Ozt3QkFFekUsSUFBSSxHQUFHLENBQUMsT0FBTyxJQUFJLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQyxLQUFLLEdBQUcsRUFBRTs0QkFDbEQsR0FBRyxDQUFDLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFBOzRCQUM1QixJQUFJLENBQUMsR0FBRyxFQUFFO2dDQUNOLEdBQUcsQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFBOzZCQUN6QjtpQ0FBTSxJQUFJLEdBQUcsWUFBWSxFQUFFLENBQUMsTUFBTSxFQUFFO2dDQUNqQyxHQUFHLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQTtnQ0FDaEIsSUFBSSxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsR0FBRyxFQUFFLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQTtnQ0FDM0MsSUFBSSxDQUFDLGNBQWMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssR0FBRyxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sR0FBRyxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBOzZCQUMzRjtpQ0FBTSxJQUFJLEdBQUcsWUFBWSxFQUFFLENBQUMsV0FBVyxFQUFFO2dDQUN0QyxHQUFHLENBQUMsV0FBVyxHQUFHLEdBQUcsQ0FBQTs2QkFDeEI7eUJBQ0o7Ozs7O0tBQ0o7SUFDTCxnQkFBQztBQUFELENBaFVBLEFBZ1VDLElBQUE7QUFFWSxRQUFBLFNBQVMsR0FBRyxJQUFJLFNBQVMsRUFBRSxDQUFBO0FBQ3hDLElBQUksRUFBRSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUU7SUFDbEIsTUFBTSxDQUFDLFdBQVcsQ0FBQyxHQUFHLGlCQUFTLENBQUE7Q0FDbEMiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDVFlQRV9JQ09OLCBUSUxFX1NJWkVfSEFMRiB9IGZyb20gXCIuLi9jb25zdGFudC9Db25zdGFudFwiXG5pbXBvcnQgeyBDVHlwZSB9IGZyb20gXCIuLi9jb25zdGFudC9FbnVtc1wiXG5pbXBvcnQgT3V0bGluZVNoYWRlckN0cmwgZnJvbSBcIi4uL3NoYWRlci9PdXRsaW5lU2hhZGVyQ3RybFwiXG5pbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4vR2FtZUhlbHBlclwiXG5cbi8qKlxuICog5ri45oiP5Lit55qE6LWE5rqQ55u45YWz5biu5Yqp5pa55rOVXG4gKi9cbmNsYXNzIFJlc0hlbHBlciB7XG5cbiAgICBwcml2YXRlIGxhbmRzOiB7IFtrZXk6IHN0cmluZ106IGNjLlNwcml0ZUZyYW1lIH0gPSB7fVxuICAgIHByaXZhdGUgbGFuZE1hcDogeyBba2V5OiBudW1iZXJdOiB7IFtrZXk6IHN0cmluZ106IGNjLlNwcml0ZUZyYW1lIH0gfSA9IHt9XG4gICAgcHJpdmF0ZSBzZWF3YXZlUHJlZmFiczogeyBba2V5OiBzdHJpbmddOiBjYy5QcmVmYWIgfSA9IHt9XG4gICAgcHJpdmF0ZSBjaXR5UHJlZmFiczogeyBba2V5OiBzdHJpbmddOiBjYy5QcmVmYWIgfSA9IHt9XG4gICAgcHJpdmF0ZSBtYXBGbGFnTnVtYmVyczogeyBba2V5OiBzdHJpbmddOiBjYy5TcHJpdGVGcmFtZSB9ID0ge31cblxuICAgIHByaXZhdGUgc3ByaXRlRGVmYXVsdE1hdGVyaWFsOiBjYy5NYXRlcmlhbCA9IG51bGxcbiAgICBwcml2YXRlIHNwcml0ZTJkR3JheU1hdGVyaWFsOiBjYy5NYXRlcmlhbCA9IG51bGxcblxuICAgIHB1YmxpYyBhc3luYyBpbml0KHByb2dlc3NDYWxsYmFjazogKHBlcmNlbnQ6IG51bWJlcikgPT4gdm9pZCkge1xuICAgICAgICB0aGlzLmxhbmRzID0ge31cbiAgICAgICAgYXNzZXRzTWdyLmRlYnVnID0gZmFsc2VcbiAgICAgICAgbGV0IHNmcyA9IGF3YWl0IGFzc2V0c01nci5sb2FkVGVtcFJzZURpcignbGFuZCcsIGNjLlNwcml0ZUZyYW1lLCAnX2xhbmRfcmVzXycsIChkb25lLCB0b3RhbCkgPT4gcHJvZ2Vzc0NhbGxiYWNrKGRvbmUgLyB0b3RhbCkpXG4gICAgICAgIHNmcy5mb3JFYWNoKG0gPT4gdGhpcy5sYW5kc1ttLm5hbWVdID0gbSlcbiAgICAgICAgbGV0IHBmYnMgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSc2VEaXIoJ3NlYXdhdmUnLCBjYy5QcmVmYWIsICdfc2Vhd2F2ZV9wcmVmYWJfJywgKGRvbmUsIHRvdGFsKSA9PiBwcm9nZXNzQ2FsbGJhY2soZG9uZSAvIHRvdGFsKSlcbiAgICAgICAgcGZicy5mb3JFYWNoKG0gPT4gdGhpcy5zZWF3YXZlUHJlZmFic1ttLm5hbWVdID0gbSlcbiAgICAgICAgcGZicyA9IGF3YWl0IGFzc2V0c01nci5sb2FkVGVtcFJzZURpcignY2l0eScsIGNjLlByZWZhYiwgJ19jaXR5X3ByZWZhYl8nLCAoZG9uZSwgdG90YWwpID0+IHByb2dlc3NDYWxsYmFjayhkb25lIC8gdG90YWwpKVxuICAgICAgICBwZmJzLmZvckVhY2gobSA9PiB0aGlzLmNpdHlQcmVmYWJzW20ubmFtZV0gPSBtKVxuICAgICAgICAvLyDlnLDlm77moIforrDmlbDlrZdcbiAgICAgICAgc2ZzID0gYXdhaXQgYXNzZXRzTWdyLmxvYWRUZW1wUnNlRGlyKCdtYXBfZmxhZ19udW0nLCBjYy5TcHJpdGVGcmFtZSwgJ19sYW5kX3Jlc18nLCAoZG9uZSwgdG90YWwpID0+IHByb2dlc3NDYWxsYmFjayhkb25lIC8gdG90YWwpKVxuICAgICAgICBzZnMuZm9yRWFjaChtID0+IHRoaXMubWFwRmxhZ051bWJlcnNbbS5uYW1lXSA9IG0pXG4gICAgICAgIGFzc2V0c01nci5kZWJ1ZyA9IHRydWVcbiAgICAgICAgLy8g5p2Q6LSoXG4gICAgICAgIHRoaXMuc3ByaXRlRGVmYXVsdE1hdGVyaWFsID0gY2MuTWF0ZXJpYWwuZ2V0QnVpbHRpbk1hdGVyaWFsKCcyZC1zcHJpdGUnKVxuICAgICAgICB0aGlzLnNwcml0ZTJkR3JheU1hdGVyaWFsID0gY2MuTWF0ZXJpYWwuZ2V0QnVpbHRpbk1hdGVyaWFsKCcyZC1ncmF5LXNwcml0ZScpXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIGluaXROb3ZpY2Uoa2V5OiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgcGZiID0gYXdhaXQgYXNzZXRzTWdyLmxvYWRUZW1wUmVzKCdvdGhlci9DSVRZXzEwMDEwJywgY2MuUHJlZmFiLCBrZXkpXG4gICAgICAgIHRoaXMuY2l0eVByZWZhYnNbcGZiLm5hbWVdID0gcGZiXG4gICAgfVxuXG4gICAgcHVibGljIGNsZWFuTm92aWNlKCkge1xuICAgICAgICBkZWxldGUgdGhpcy5jaXR5UHJlZmFic1snQ0lUWV8xMDAxMCddXG4gICAgfVxuXG4gICAgcHVibGljIGdldFBhd25OYW1lKGlkOiBudW1iZXIpIHsgcmV0dXJuICdwYXduVGV4dC5uYW1lXycgKyBpZCB9XG5cbiAgICBwdWJsaWMgY2hlY2tMYW5kU2tpbih0eXBlOiBudW1iZXIpIHtcbiAgICAgICAgcmV0dXJuICEhdGhpcy5sYW5kTWFwW3R5cGVdXG4gICAgfVxuXG4gICAgLy8g5Yid5aeL5YyW5Zyw5Z2X55qu6IKkXG4gICAgcHVibGljIGFzeW5jIGluaXRMYW5kU2tpbih0eXBlOiBudW1iZXIsIHByb2dlc3NDYWxsYmFjaz86IChwZXJjZW50OiBudW1iZXIpID0+IHZvaWQpIHtcbiAgICAgICAgbGV0IG9iaiA9IHRoaXMubGFuZE1hcFt0eXBlXVxuICAgICAgICBpZiAob2JqKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzZnMgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSc2VEaXIoJ2xhbmRfJyArIHR5cGUsIGNjLlNwcml0ZUZyYW1lLCAnX2xhbmRfcmVzXycgKyB0eXBlLCAoZG9uZSwgdG90YWwpID0+IHByb2dlc3NDYWxsYmFjayAmJiBwcm9nZXNzQ2FsbGJhY2soZG9uZSAvIHRvdGFsKSlcbiAgICAgICAgb2JqID0gdGhpcy5sYW5kTWFwW3R5cGVdID0ge31cbiAgICAgICAgc2ZzLmZvckVhY2gobSA9PiBvYmpbbS5uYW1lXSA9IG0pXG4gICAgfVxuXG4gICAgcHVibGljIGNsZWFuTGFuZFNraW4oKSB7XG4gICAgICAgIGNvbnN0IHR5cGUgPSBnYW1lSHByLndvcmxkLmdldFNlYXNvbigpLnR5cGVcbiAgICAgICAgZm9yIChsZXQgayBpbiB0aGlzLmxhbmRNYXApIHtcbiAgICAgICAgICAgIGlmIChOdW1iZXIoaykgIT09IHR5cGUpIHtcbiAgICAgICAgICAgICAgICBkZWxldGUgdGhpcy5sYW5kTWFwW2tdXG4gICAgICAgICAgICAgICAgYXNzZXRzTWdyLnJlbGVhc2VUZW1wUmVzQnlUYWcoJ19sYW5kX3Jlc18nICsgaylcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOagueaNruS4i+agh+iOt+WPluiKgueCuVxuICAgIHB1YmxpYyBnZXROb2RlQnlJbmRleChub2RlOiBjYy5Ob2RlLCBpOiBudW1iZXIsIHBvc2l0aW9uOiBjYy5WZWMyKSB7XG4gICAgICAgIGNvbnN0IGl0ID0gbm9kZS5jaGlsZHJlbltpXSB8fCBjYy5pbnN0YW50aWF0ZTIobm9kZS5jaGlsZHJlblswXSwgbm9kZSlcbiAgICAgICAgaXQuYWN0aXZlID0gdHJ1ZVxuICAgICAgICBpdC5EYXRhID0gbnVsbFxuICAgICAgICBpdC5zZXRQb3NpdGlvbihwb3NpdGlvbilcbiAgICAgICAgcmV0dXJuIGl0XG4gICAgfVxuICAgIHB1YmxpYyBnZXROb2RlQnlJbmRleDIobm9kZTogY2MuTm9kZSwgaTogbnVtYmVyLCBwb3NpdGlvbjogY2MuVmVjMikge1xuICAgICAgICBjb25zdCBpdCA9IG5vZGUuY2hpbGRyZW5baV0gfHwgY2MuaW5zdGFudGlhdGUyKG5vZGUuY2hpbGRyZW5bMF0sIG5vZGUpXG4gICAgICAgIGl0LmFjdGl2ZSA9IHRydWVcbiAgICAgICAgaXQuRGF0YSA9IG51bGxcbiAgICAgICAgaXQuc2V0UG9zaXRpb24ocG9zaXRpb24ueCwgcG9zaXRpb24ueSAtIFRJTEVfU0laRV9IQUxGLnkpXG4gICAgICAgIHJldHVybiBpdFxuICAgIH1cblxuICAgIC8vIOmakOiXj+WkmuS6jueahFxuICAgIHB1YmxpYyBoaWRlTm9kZUJ5SW5kZXgobm9kZTogY2MuTm9kZSwgaWR4OiBudW1iZXIpIHtcbiAgICAgICAgZm9yIChsZXQgaSA9IGlkeCwgbCA9IG5vZGUuY2hpbGRyZW5Db3VudDsgaSA8IGw7IGkrKykge1xuICAgICAgICAgICAgY29uc3QgaXQgPSBub2RlLmNoaWxkcmVuW2ldXG4gICAgICAgICAgICBpdC5hY3RpdmUgPSBmYWxzZVxuICAgICAgICAgICAgaXQuRGF0YSA9IG51bGxcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOa4heeQhuWtkOiKgueCueWPquWJqTHkuKpcbiAgICBwdWJsaWMgY2xlYW5Ob2RlQ2hpbGRyZW4obm9kZTogY2MuTm9kZSkge1xuICAgICAgICBmb3IgKGxldCBpID0gbm9kZS5jaGlsZHJlbkNvdW50IC0gMTsgaSA+PSAxOyBpLS0pIHtcbiAgICAgICAgICAgIG5vZGUuY2hpbGRyZW5baV0uZGVzdHJveSgpXG4gICAgICAgIH1cbiAgICAgICAgbm9kZS5jaGlsZHJlblswXT8uc2V0QWN0aXZlKGZhbHNlKVxuICAgIH1cblxuICAgIC8vIOiOt+WPlnNwcml0ZeadkOi0qFxuICAgIHB1YmxpYyBnZXQyZFNwcml0ZU1hdGVyaWFsKHVubG9jazogYm9vbGVhbikge1xuICAgICAgICByZXR1cm4gdW5sb2NrID8gdGhpcy5zcHJpdGVEZWZhdWx0TWF0ZXJpYWwgOiB0aGlzLnNwcml0ZTJkR3JheU1hdGVyaWFsXG4gICAgfVxuXG4gICAgLy8g57qv6Imy54Gw5p2Q6LSoXG4gICAgcHVibGljIGdldFNwcml0ZUNvbG9yR3JheU1hdGVyaWFsKHVubG9jazogYm9vbGVhbikge1xuICAgICAgICByZXR1cm4gdW5sb2NrID8gdGhpcy5zcHJpdGVEZWZhdWx0TWF0ZXJpYWwgOiBhc3NldHNNZ3IuZ2V0TWF0ZXJpYWwoJ1Nwcml0ZUNvbG9yR3JleScpXG4gICAgfVxuXG4gICAgLy8g5Zyw6Z2iaWNvblxuICAgIHB1YmxpYyBnZXRMYW5kSWNvbihpY29uOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKGljb24uc3RhcnRzV2l0aCgnbGFuZF8nKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuZ2V0TGFuZEl0ZW1JY29uKGljb24sIGdhbWVIcHIud29ybGQuZ2V0U2Vhc29uVHlwZSgpKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLmxhbmRzW2ljb25dXG4gICAgfVxuXG4gICAgLy8g5Zyw6Z2i6LWE5rqQaWNvblxuICAgIHB1YmxpYyBnZXRMYW5kSXRlbUljb24oaWNvbjogc3RyaW5nLCB0eXBlOiBudW1iZXIpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubGFuZE1hcFt0eXBlXT8uW2ljb25dIHx8IHRoaXMubGFuZHNbaWNvbl0gfHwgbnVsbFxuICAgIH1cblxuICAgIC8vIOiOt+WPlua1t+a1qumihOWItuS9k1xuICAgIHB1YmxpYyBnZXRTZWF3YXZlUHJlZmFiKGlkOiBudW1iZXIpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc2Vhd2F2ZVByZWZhYnNbJ1NFQVdBVkVfJyArIGlkXVxuICAgIH1cblxuICAgIC8vIOiOt+WPluWfjuW4gumihOWItuS9k1xuICAgIHB1YmxpYyBnZXRDaXR5UHJlZmFiKGlkOiBudW1iZXIpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2l0eVByZWZhYnNbJ0NJVFlfJyArIGlkXVxuICAgIH1cblxuICAgIC8vIOiOt+WPlui1hOa6kGljb25cbiAgICBwdWJsaWMgZ2V0UmVzSWNvbih0eXBlOiBDVHlwZSkge1xuICAgICAgICByZXR1cm4gYXNzZXRzTWdyLmdldEltYWdlKENUWVBFX0lDT05bdHlwZV0pXG4gICAgfVxuXG4gICAgLy8g6I635Y+W5Zyw5Zu+5qCH6K6w5pWw5a2XXG4gICAgcHVibGljIGdldE1hcEZsYWdOdW1iZXIoZmxhZzogbnVtYmVyKSB7XG4gICAgICAgIGNvbnN0IGtleSA9IGZsYWcgPD0gMCA/ICd4JyA6IChmbGFnIC0gMSlcbiAgICAgICAgcmV0dXJuIHRoaXMubWFwRmxhZ051bWJlcnNbJ21hcF9mbGFnXycgKyBrZXldXG4gICAgfVxuXG4gICAgLy8g5Yqg6L29aWNvblxuICAgIHB1YmxpYyBhc3luYyBsb2FkSWNvbih1cmw6IHN0cmluZywgaWNvbjogY2MuU3ByaXRlIHwgY2MuTm9kZSwga2V5OiBzdHJpbmcsIHNldEVtcHR5OiBib29sZWFuID0gdHJ1ZSkge1xuICAgICAgICBpZiAoIXVybCB8fCAhaWNvbiB8fCAhaWNvbi5pc1ZhbGlkKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNwciA9IGljb24gaW5zdGFuY2VvZiBjYy5TcHJpdGUgPyBpY29uIDogaWNvbi5Db21wb25lbnQoY2MuU3ByaXRlKVxuICAgICAgICBpZiAoIXNwcikge1xuICAgICAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgICAgfSBlbHNlIGlmIChzZXRFbXB0eSkge1xuICAgICAgICAgICAgc3ByLnNwcml0ZUZyYW1lID0gbnVsbFxuICAgICAgICB9XG4gICAgICAgIHNwclsnX19sb2FkX2ljb25fdXJsJ10gPSB1cmxcbiAgICAgICAgY29uc3Qgc2YgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXModXJsLCBjYy5TcHJpdGVGcmFtZSwga2V5KVxuICAgICAgICBpZiAoc3ByLmlzVmFsaWQgJiYgc3ByWydfX2xvYWRfaWNvbl91cmwnXSA9PT0gdXJsKSB7XG4gICAgICAgICAgICBzcHIuc3ByaXRlRnJhbWUgPSBzZiB8fCBudWxsXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNwclxuICAgIH1cblxuICAgIC8vIOWKoOi9veiuvuaWvWljb25cbiAgICBwdWJsaWMgYXN5bmMgbG9hZEJ1aWxkSWNvbih1cmw6IHN0cmluZywgaWNvbjogY2MuU3ByaXRlIHwgY2MuTm9kZSwga2V5OiBzdHJpbmcpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubG9hZEljb24oJ2J1aWxkLycgKyB1cmwsIGljb24sIGtleSlcbiAgICB9XG5cbiAgICAvLyDliqDovb3ln47luIJpY29uXG4gICAgcHVibGljIGFzeW5jIGxvYWRDaXR5SWNvbihpZDogbnVtYmVyLCBpY29uOiBjYy5TcHJpdGUgfCBjYy5Ob2RlLCBrZXk6IHN0cmluZykge1xuICAgICAgICByZXR1cm4gdGhpcy5sb2FkSWNvbignY2l0eS9jaXR5XycgKyBpZCwgaWNvbiwga2V5KVxuICAgIH1cblxuICAgIC8vIOWKoOi9veWjq+WFteWktOWDj2ljb25cbiAgICBwdWJsaWMgYXN5bmMgbG9hZFBhd25IZWFkSWNvbihpZDogbnVtYmVyLCBpY29uOiBjYy5TcHJpdGUgfCBjYy5Ob2RlLCBrZXk6IHN0cmluZykge1xuICAgICAgICBpZiAoaWQgPT09IDM0MDUxMDQpIHsgLy/niZvku5TpmpDol4/mrL4g54m55q6K5aSE55CGXG4gICAgICAgICAgICByZXR1cm4gdGhpcy5sb2FkSWNvbihgcm9sZS8ke2lkfS9yb2xlXyR7aWR9XzAwYCwgaWNvbiwga2V5KVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKGByb2xlLyR7aWR9L3JvbGVfJHtpZH1fMDFgLCBpY29uLCBrZXkpXG4gICAgfVxuXG4gICAgLy8g5Yqg6L295aOr5YW15bCP5aS05YOPaWNvblxuICAgIHB1YmxpYyBhc3luYyBsb2FkUGF3bkhlYWRNaW5pSWNvbihpZDogbnVtYmVyLCBpY29uOiBjYy5TcHJpdGUgfCBjYy5Ob2RlLCBrZXk6IHN0cmluZywgc2V0RW1wdHk6IGJvb2xlYW4gPSB0cnVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKGByb2xlLyR7aWR9L3JvbGVfJHtpZH1gLCBpY29uLCBrZXksIHNldEVtcHR5KVxuICAgIH1cblxuICAgIC8vIOWKoOi9veaKgOiDveWbvuagh1xuICAgIHB1YmxpYyBhc3luYyBsb2FkU2tpbGxJY29uKGlkOiBudW1iZXIsIGljb246IGNjLlNwcml0ZSB8IGNjLk5vZGUsIGtleTogc3RyaW5nKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKCdza2lsbC9za2lsbF8nICsgaWQsIGljb24sIGtleSlcbiAgICB9XG5cbiAgICAvLyDliqDovb3oo4XlpIdpY29uXG4gICAgcHVibGljIGFzeW5jIGxvYWRFcXVpcEljb24oaWQ6IG51bWJlciwgaWNvbjogY2MuU3ByaXRlIHwgY2MuTm9kZSwga2V5OiBzdHJpbmcsIHNtZWx0THY/OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3Qgc3ByID0gYXdhaXQgdGhpcy5sb2FkSWNvbignZXF1aXAvZXF1aXBfJyArIGlkLCBpY29uLCBrZXkpXG4gICAgICAgIGlmICghc3ByIHx8ICFzcHIuaXNWYWxpZCkge1xuICAgICAgICB9IGVsc2UgaWYgKHNtZWx0THYpIHtcbiAgICAgICAgICAgIC8vIGNvbnN0IHNpemUgPSBzcHIubm9kZS5nZXRDb250ZW50U2l6ZSgpXG4gICAgICAgICAgICBjb25zdCBvdXRsaW5lID0gc3ByLmdldENvbXBvbmVudChPdXRsaW5lU2hhZGVyQ3RybCkgfHwgc3ByLmFkZENvbXBvbmVudChPdXRsaW5lU2hhZGVyQ3RybClcbiAgICAgICAgICAgIG91dGxpbmUuc2V0VGFyZ2V0KHNwcilcbiAgICAgICAgICAgIG91dGxpbmUuc2V0T3V0bGluZVNpemUoMilcbiAgICAgICAgICAgIG91dGxpbmUuc2V0Q29sb3IodXQuY29sb3JGcm9tSEVYKHNtZWx0THYgPT09IDEgPyAnIzU4RjFGRicgOiAnI0U0ODhGRicpKVxuICAgICAgICAgICAgb3V0bGluZS5zZXRWaXNpYmxlKHRydWUpXG4gICAgICAgICAgICAvLyBzcHIubm9kZS5hZGFwdFNjYWxlKHNpemUsIGNjLnNpemUoc2l6ZS53aWR0aCArIDgsIHNpemUuaGVpZ2h0ICsgOCkpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzcHIuZ2V0Q29tcG9uZW50KE91dGxpbmVTaGFkZXJDdHJsKT8uc2V0VmlzaWJsZShmYWxzZSlcbiAgICAgICAgICAgIC8vIHNwci5ub2RlLnNjYWxlID0gMVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yqg6L295pS/562WaWNvblxuICAgIHB1YmxpYyBhc3luYyBsb2FkUG9saWN5SWNvbihpZDogbnVtYmVyLCBpY29uOiBjYy5TcHJpdGUgfCBjYy5Ob2RlLCBrZXk6IHN0cmluZywgc2V0RW1wdHk6IGJvb2xlYW4gPSB0cnVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKCdwb2xpY3kvcG9saWN5XycgKyBpZCwgaWNvbiwga2V5LCBzZXRFbXB0eSlcbiAgICB9XG5cbiAgICAvLyDliqDovb1idWZmIGljb25cbiAgICBwdWJsaWMgYXN5bmMgbG9hZEJ1ZmZJY29uKGlkOiBudW1iZXIsIGljb246IGNjLlNwcml0ZSB8IGNjLk5vZGUsIGtleTogc3RyaW5nLCBzZXRFbXB0eTogYm9vbGVhbiA9IHRydWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubG9hZEljb24oJ2J1ZmZfaWNvbi9idWZmXycgKyBpZCwgaWNvbiwga2V5LCBzZXRFbXB0eSlcbiAgICB9XG5cbiAgICAvLyDliqDovb3ogZTnm5/lm77moIdcbiAgICBwdWJsaWMgYXN5bmMgbG9hZEFsbGlJY29uKGlkOiBudW1iZXIsIGljb246IGNjLlNwcml0ZSB8IGNjLk5vZGUsIGtleTogc3RyaW5nKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKCdhbGxpX2ljb24vYWxsaV9pY29uXycgKyBpZCwgaWNvbiwga2V5KVxuICAgIH1cblxuICAgIC8vIOWKoOi9veivhOWIhuWbvuagh1xuICAgIHB1YmxpYyBhc3luYyBsb2FkUmFua1Njb3JlSWNvbihpZDogbnVtYmVyLCBpY29uOiBjYy5TcHJpdGUgfCBjYy5Ob2RlLCBrZXk6IHN0cmluZykge1xuICAgICAgICByZXR1cm4gdGhpcy5sb2FkSWNvbigncmFua19pY29uL3JhbmtfaWNvbl8nICsgKGlkID49IDAgPyBpZCA6ICdub25lJyksIGljb24sIGtleSlcbiAgICB9XG5cbiAgICAvLyDliqDovb3npLznianlm77moIdcbiAgICBwdWJsaWMgYXN5bmMgbG9hZEdpZnRJY29uKGlkOiBudW1iZXIsIGljb246IGNjLlNwcml0ZSB8IGNjLk5vZGUsIGtleTogc3RyaW5nKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKCdnaWZ0L2dpZnRfJyArIGlkLCBpY29uLCBrZXkpXG4gICAgfVxuXG4gICAgLy8g5Yqg6L296KGo5oOFXG4gICAgcHVibGljIGFzeW5jIGxvYWRFbW9qaUljb24oaWQ6IG51bWJlciwgaWNvbjogY2MuU3ByaXRlIHwgY2MuTm9kZSwga2V5OiBzdHJpbmcpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubG9hZEljb24oJ2Vtb2ppL2Vtb2ppXycgKyBpZCwgaWNvbiwga2V5KVxuICAgIH1cblxuICAgIC8vIOWKoOi9veeUu+WDj1xuICAgIHB1YmxpYyBhc3luYyBsb2FkUG9ydHJheWFsSW1hZ2UoaWQ6IG51bWJlciwgaWNvbjogY2MuU3ByaXRlIHwgY2MuTm9kZSwga2V5OiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKGFzc2V0c01nci5nZXRKc29uRGF0YSgncG9ydHJheWFsQmFzZScsIGlkKT8uaGFzX2FuaW0pIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKGBwb3J0cmF5YWxfYW5pbS8ke2lkfS9wb3J0cmF5YWxfJHtpZH1fMDFgLCBpY29uLCBrZXkpXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMubG9hZEljb24oJ3BvcnRyYXlhbC9wb3J0cmF5YWxfJyArIGlkLCBpY29uLCBrZXkpXG4gICAgfVxuXG4gICAgLy8g5Yqg6L295q6L5Y236YGu5oyhXG4gICAgcHVibGljIGFzeW5jIGxvYWRQb3J0cmF5YWxEZWJyaXNNYXNrKGlkOiBudW1iZXIsIGljb246IGNjLlNwcml0ZSB8IGNjLk5vZGUsIGtleTogc3RyaW5nKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxvYWRJY29uKCdwb3J0cmF5YWwvcGRfbWFza18nICsgaWQsIGljb24sIGtleSlcbiAgICB9XG5cbiAgICAvLyDliqDovb3oi7Hpm4TmioDog73lm77moIdcbiAgICBwdWJsaWMgYXN5bmMgbG9hZEhlcm9Ta2lsbEljb24oaWQ6IG51bWJlciwgaWNvbjogY2MuU3ByaXRlIHwgY2MuTm9kZSwga2V5OiBzdHJpbmcpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubG9hZEljb24oJ2hlcm9fc2tpbGxfaWNvbi9oZXJvX3NraWxsXycgKyBpZCwgaWNvbiwga2V5KVxuICAgIH1cblxuICAgIC8vIOWKoOi9veiLsembhOmihOWItuS9k1xuICAgIHB1YmxpYyBhc3luYyBsb2FkSGVyb01hcmNoUHJlZmFiKGlkOiBudW1iZXIsIGtleTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IHBmYk5hbWUgPSAnUk9MRV8nICsgaWRcbiAgICAgICAgY29uc3QgcGZiID0gYXdhaXQgYXNzZXRzTWdyLmxvYWRUZW1wUmVzKCdtYXJjaC8nICsgcGZiTmFtZSwgY2MuUHJlZmFiLCBrZXkpXG4gICAgICAgIGlmIChwZmI/Lm5hbWUgIT09IHBmYk5hbWUpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHBmYlxuICAgIH1cblxuICAgIC8vIOWKoOi9veakjeeJqeenjeWtkOWbvuagh1xuICAgIHB1YmxpYyBhc3luYyBsb2FkQm90YW55U2VlZEljb24oaWQ6IG51bWJlciwgaWNvbjogY2MuU3ByaXRlIHwgY2MuTm9kZSwga2V5OiBzdHJpbmcpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubG9hZEljb24oJ2JvdGFueV9zZWVkL2JvdGFueV9zZWVkXycgKyBpZCwgaWNvbiwga2V5KVxuICAgIH1cblxuICAgIC8vIOWKoOi9veihqOaDheiKgueCuVxuICAgIHB1YmxpYyBhc3luYyBsb2FkRW1vamlOb2RlKGlkOiBudW1iZXIsIHJvb3Q6IGNjLk5vZGUsIHNjYWxlOiBudW1iZXIsIGtleTogc3RyaW5nLCBzZXRFbXB0eTogYm9vbGVhbiA9IHRydWUpIHtcbiAgICAgICAgaWYgKCFyb290IHx8ICFpZCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAoc2V0RW1wdHkpIHtcbiAgICAgICAgICAgIHJvb3QucmVtb3ZlQWxsQ2hpbGRyZW4oKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG5vZGUgPSBhd2FpdCBub2RlUG9vbE1nci5nZXQoJ2Vtb2ppL0VNT0pJXycgKyBpZCwga2V5KVxuICAgICAgICBpZiAobm9kZSAmJiByb290LmlzVmFsaWQpIHtcbiAgICAgICAgICAgIG5vZGUucGFyZW50ID0gcm9vdFxuICAgICAgICAgICAgbm9kZS5hY3RpdmUgPSB0cnVlXG4gICAgICAgICAgICBub2RlLnNldFBvc2l0aW9uKDAsIDApXG4gICAgICAgICAgICBub2RlLnNjYWxlWCA9IHNjYWxlIHx8IDFcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWKoOi9veeOqeWutuWktOWDj1xuICAgIHB1YmxpYyBhc3luYyBsb2FkUGxheWVySGVhZChpY29uOiBjYy5TcHJpdGUgfCBjYy5Ob2RlLCB1cmw6IHN0cmluZywga2V5OiBzdHJpbmcsIHNldEVtcHR5PzogYm9vbGVhbikge1xuICAgICAgICBpZiAoIWljb24/LmlzVmFsaWQpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNwciA9IGljb24gaW5zdGFuY2VvZiBjYy5TcHJpdGUgPyBpY29uIDogaWNvbi5Db21wb25lbnQoY2MuU3ByaXRlKVxuICAgICAgICBpZiAoIXNwcikge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAoc2V0RW1wdHkpIHtcbiAgICAgICAgICAgIHNwci5zcHJpdGVGcmFtZSA9IG51bGxcbiAgICAgICAgICAgIHNwci5ub2RlLnJlbW92ZUFsbENoaWxkcmVuKClcbiAgICAgICAgfVxuICAgICAgICB1cmwgPSBzcHJbJ19wbGF5ZXJfaGVhZF9pY29uXyddID0gdXJsIHx8ICdoZWFkX2ljb25fZnJlZV8wMDEnXG4gICAgICAgIGxldCB2YWw6IGFueSA9IG51bGxcbiAgICAgICAgaWYgKHVybC5zdGFydHNXaXRoKCdoZWFkX2ljb25fYW5pbV8nKSkge1xuICAgICAgICAgICAgdmFsID0gYXdhaXQgYXNzZXRzTWdyLmxvYWRUZW1wUmVzKCdoZWFkaWNvbi8nICsgdXJsLCBjYy5QcmVmYWIsIGtleSlcbiAgICAgICAgfSBlbHNlIGlmICh1cmwuc3RhcnRzV2l0aCgnaGVhZF9pY29uXycpKSB7XG4gICAgICAgICAgICB2YWwgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXMoJ2hlYWRpY29uLycgKyB1cmwsIGNjLlNwcml0ZUZyYW1lLCBrZXkpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB2YWwgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFJlbW90ZSh1cmwsICcuanBnJywga2V5IHx8ICdfcGxheWVyX2hlYWRfJylcbiAgICAgICAgfVxuICAgICAgICBpZiAoc3ByLmlzVmFsaWQgJiYgc3ByWydfcGxheWVyX2hlYWRfaWNvbl8nXSA9PT0gdXJsKSB7XG4gICAgICAgICAgICBzcHIubm9kZS5yZW1vdmVBbGxDaGlsZHJlbigpXG4gICAgICAgICAgICBpZiAoIXZhbCkge1xuICAgICAgICAgICAgICAgIHNwci5zcHJpdGVGcmFtZSA9IG51bGxcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsIGluc3RhbmNlb2YgY2MuUHJlZmFiKSB7XG4gICAgICAgICAgICAgICAgc3ByLnNwcml0ZUZyYW1lID0gbnVsbFxuICAgICAgICAgICAgICAgIGNvbnN0IG5vZGUgPSBjYy5pbnN0YW50aWF0ZTIodmFsLCBzcHIubm9kZSlcbiAgICAgICAgICAgICAgICBub2RlLnNldENvbnRlbnRTaXplKHNwci5ub2RlLndpZHRoICogc3ByLm5vZGUuc2NhbGVYLCBzcHIubm9kZS5oZWlnaHQgKiBzcHIubm9kZS5zY2FsZVkpXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHZhbCBpbnN0YW5jZW9mIGNjLlNwcml0ZUZyYW1lKSB7XG4gICAgICAgICAgICAgICAgc3ByLnNwcml0ZUZyYW1lID0gdmFsXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59XG5cbmV4cG9ydCBjb25zdCByZXNIZWxwZXIgPSBuZXcgUmVzSGVscGVyKClcbmlmIChjYy5zeXMuaXNCcm93c2VyKSB7XG4gICAgd2luZG93WydyZXNIZWxwZXInXSA9IHJlc0hlbHBlclxufSJdfQ==