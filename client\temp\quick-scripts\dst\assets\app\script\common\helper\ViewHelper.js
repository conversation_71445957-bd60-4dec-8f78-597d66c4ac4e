
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/ViewHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd7769eBkKxF1YXHywafMCu6', 'ViewHelper');
// app/script/common/helper/ViewHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewHelper = void 0;
var CTypeObj_1 = require("../../model/common/CTypeObj");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var Constant_1 = require("../constant/Constant");
var ECode_1 = require("../constant/ECode");
var Enums_1 = require("../constant/Enums");
var NetEvent_1 = require("../event/NetEvent");
var NotEvent_1 = require("../event/NotEvent");
var GameHelper_1 = require("./GameHelper");
var MapHelper_1 = require("./MapHelper");
var ResHelper_1 = require("./ResHelper");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var StrategyObj_1 = require("../../model/common/StrategyObj");
var FrameAnimationCmpt_1 = require("../../view/cmpt/FrameAnimationCmpt");
var PortrayalSkillObj_1 = require("../../model/common/PortrayalSkillObj");
var ArmyObj_1 = require("../../model/area/ArmyObj");
/**
 * 视图帮助方法
 */
var ViewHelper = /** @class */ (function () {
    function ViewHelper() {
    }
    // 跳转场景
    ViewHelper.prototype.gotoWind = function (val) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit.apply(eventCenter, __spread([mc.Event.GOTO_WIND, val, resolve], params)); })];
            });
        });
    };
    // 预加载场景
    ViewHelper.prototype.preloadWind = function (key, progress) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit(mc.Event.PRELOAD_WIND, key, resolve, progress); })];
            });
        });
    };
    // 预加载UI
    ViewHelper.prototype.preloadPnl = function (key, progress) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit(mc.Event.PRELOAD_PNL, key, resolve, progress); })];
            });
        });
    };
    // 显示UI
    ViewHelper.prototype.showPnl = function (key) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit.apply(eventCenter, __spread([mc.Event.OPEN_PNL, key, resolve], params)); })];
            });
        });
    };
    // 隐藏UI
    ViewHelper.prototype.hidePnl = function (key) {
        eventCenter.emit(mc.Event.HIDE_PNL, key);
    };
    // 显示提示框
    ViewHelper.prototype.showAlert = function (msg, opts) {
        if (msg === ECode_1.ecode.NOT_BIND_UID) {
            return eventCenter.emit(NetEvent_1.default.NET_DISCONNECT);
        }
        eventCenter.emit(NotEvent_1.default.OPEN_ALERT, msg, opts);
    };
    // 显示对话框
    ViewHelper.prototype.showMessageBox = function (msg, opts) {
        eventCenter.emit(NotEvent_1.default.OPEN_MESSAGE_BOX, msg, opts);
    };
    // 主动关闭对话框
    ViewHelper.prototype.hideMessageBox = function () {
        eventCenter.emit(NotEvent_1.default.HIDE_MESSAGE_BOX);
    };
    // 显示说明
    ViewHelper.prototype.showDesc = function (text, params) {
        this.showPnl('common/Desc', { text: text, params: params });
    };
    // 显示说明信息
    ViewHelper.prototype.showDescInfo = function (list, title) {
        if (title === void 0) { title = 'ui.explain'; }
        this.showPnl('common/DescInfo', { title: title, list: list });
    };
    // 显示网络等待
    ViewHelper.prototype.showNetWait = function (val, delay) {
        if (val) {
            eventCenter.emit(NetEvent_1.default.NET_REQ_BEGIN, delay);
        }
        else {
            eventCenter.emit(NetEvent_1.default.NET_REQ_END);
        }
    };
    // 显示通用加载动画
    ViewHelper.prototype.showLoadingWait = function (val) {
        if (val) {
            eventCenter.emit(mc.Event.LOADING_WAIT_BEGIN);
        }
        else {
            eventCenter.emit(mc.Event.LOADING_WAIT_END);
        }
    };
    // 显示加载wind的动画
    ViewHelper.prototype.showWindLoading = function (val) {
        if (val) {
            eventCenter.emit(mc.Event.LOAD_BEGIN_WIND);
        }
        else {
            eventCenter.emit(mc.Event.LOAD_END_WIND);
        }
    };
    // 显示连接失败
    ViewHelper.prototype.showConnectFail = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.connect_server_fail');
                        _this.showMessageBox(text, {
                            lockClose: true,
                            okText: 'login.button_retry',
                            ok: function () { return resolve(true); },
                            cancel: function () { return resolve(false); },
                        });
                    })];
            });
        });
    };
    // 刷新地块的边框线
    ViewHelper.prototype.updateCellBorderLines = function (node, lines, color) {
        node.Items(lines, function (it, line) {
            var conf = Constant_1.BORDER_LINE_CONF[line];
            it.setContentSize(conf.size);
            it.setPosition(conf.pos);
            it.Color(color);
        });
    };
    // 绘制网格
    ViewHelper.prototype.drawGrid = function (g, size, start) {
        g.clear();
        var pos = cc.v2();
        var w = size.x * Constant_1.TILE_SIZE, h = size.y * Constant_1.TILE_SIZE;
        for (var i = 0; i <= size.x; i++) {
            pos.set(start).x += i;
            MapHelper_1.mapHelper.getPixelByPoint(pos, pos).subSelf(Constant_1.TILE_SIZE_HALF);
            g.moveTo(pos.x, pos.y);
            g.lineTo(pos.x, pos.y + h);
        }
        for (var i = 0; i <= size.y; i++) {
            pos.set(start).y += i;
            MapHelper_1.mapHelper.getPixelByPoint(pos, pos).subSelf(Constant_1.TILE_SIZE_HALF);
            g.moveTo(pos.x, pos.y);
            g.lineTo(pos.x + w, pos.y);
        }
        g.stroke();
    };
    // 显示建筑的基础信息
    ViewHelper.prototype.updateBuildBaseUI = function (node, data, key) {
        this.updateBuildBaseInfo(node.Child('top'), data, key);
        this.updateBuildAttrInfo(node, data);
    };
    // 基础上信息
    ViewHelper.prototype.updateBuildBaseInfo = function (node, data, key) {
        ResHelper_1.resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key);
        node.Child('icon/name').setLocaleKey(data.name);
        node.Child('icon/lv').setLocaleKey('ui.lv', data.lv);
        var desc = node.Child('desc') || node.Child('info/desc');
        desc.setLocaleKey(data.desc);
    };
    ViewHelper.prototype._updateBuildBaseInfo = function (node, data, key) {
        ResHelper_1.resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key);
        node.Child('icon/name').setLocaleKey(data.name);
        node.Child('icon/lv/val').setLocaleKey('ui.lv', data.lv);
        var descLbl = node.Child('desc', cc.Label);
        descLbl.setLocaleKey(data.desc);
        descLbl._forceUpdateRenderData();
        node.height = Math.max(220, descLbl.node.height + 168);
    };
    ViewHelper.prototype._updateBuildAttrInfo = function (data, attr, bottom, attrs, key) {
        var _a;
        var top = attr.Child('top');
        // 显示下级信息和升级费用
        var isMaxLv = data.isMaxLv();
        top.Child('curr').setLocaleKey('ui.lv', data.lv);
        var nextLv = data.tempNextLv || ((_a = data.nextLvInfo) === null || _a === void 0 ? void 0 : _a.lv);
        if (nextLv) {
            top.Child('next').Color('#625450').setLocaleKey('ui.lv', nextLv);
        }
        else {
            top.Child('next').Color('#B6A591').setLocaleKey('ui.maxlv1');
        }
        attr.Child('items').Items(attrs, function (it, data, i) {
            var curr = data.curr, nextVal = data.nextVal;
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey(curr.key, curr.params);
            if (it.Child('next').active = !!nextVal) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = nextVal;
            }
            it.Child('line').active = i < attrs.length - 1;
        });
        // 刷新费用和按钮
        if (bottom.active = !isMaxLv) {
            this.updateBuildBottomInfo(data, bottom);
        }
    };
    ViewHelper.prototype.updateBuildBottomInfo = function (data, bottom) {
        var needMainLv = data.id !== Constant_1.BUILD_MAIN_NID && data.lv >= GameHelper_1.gameHpr.player.getMainBuildLv(); // 只要不是主城 就不能比主城等级高
        var params = needMainLv ? [data.lv + 1] : [];
        var condText = data.id === Constant_1.BUILD_MAIN_NID ? GameHelper_1.gameHpr.checkCellCountCond(data.attrJson.prep_cond) : GameHelper_1.gameHpr.checkUnlcokBuildCond(data.attrJson.prep_cond) || (needMainLv ? 'ui.need_main_lv' : '');
        if (!condText) {
            bottom.Child('title/val').setLocaleKey('ui.up_cost');
            bottom.Child('cond').active = false;
            var need = bottom.Child('need');
            need.active = true;
            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time);
        }
        else {
            bottom.Child('title/val').setLocaleKey('ui.up_cond');
            bottom.Child('need').active = false;
            var cond = bottom.Child('cond');
            cond.active = true;
            cond.Child('val').setLocaleKey(condText, params);
        }
        this.updateBuildButtons(bottom.Child('buttons'), data, condText);
    };
    // 显示建筑的属性信息
    ViewHelper.prototype.updateBuildAttrInfo = function (node, data) {
        var _a;
        var attr = node.Child('attrs/attr');
        var dtype = data.id === Constant_1.BUILD_PLANT_NID ? 501 : (_a = data.effect) === null || _a === void 0 ? void 0 : _a.getDescType();
        var showAttr = attr.active = !!dtype && (data.id !== Constant_1.BUILD_EMBASSY_NID || GameHelper_1.gameHpr.alliance.isMeCreater());
        // 显示下级信息和升级费用
        var isMaxLv = data.isMaxLv(), nextInfo = data.nextLvInfo;
        var top = attr.Child('top'), need = node.Child('need');
        top.active = need.active = !isMaxLv;
        attr.Child('items').Items(1, function (it, _data) {
            var _a, _b;
            if (showAttr) {
                it.Child('cur/val').setLocaleKey('ui.build_eff_desc_' + dtype, ((_a = data.effect) === null || _a === void 0 ? void 0 : _a.getValueText()) || '');
            }
            var nextLbl = it.Child('next', cc.Label);
            if (it.Child('arrow').active = nextLbl.setActive(!isMaxLv)) {
                nextLbl.setLocaleKey(((_b = nextInfo.effect) === null || _b === void 0 ? void 0 : _b.getValueText()) || '');
            }
        });
        if (!isMaxLv) {
            top.Child('cur').setLocaleKey('ui.lv', data.lv);
            top.Child('next').setLocaleKey('ui.lv', data.nextLvInfo.lv);
            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time);
        }
        this.updateBuildButtons(need.Child('buttons'), data);
    };
    // 刷新按钮
    ViewHelper.prototype.updateBuildButtons = function (buttonsNode, data, condText) {
        if (data.isMaxLv()) {
            return buttonsNode.Swih('');
        }
        buttonsNode.opacity = !!condText ? 120 : 255;
        var player = GameHelper_1.gameHpr.player;
        var bt = player.getBuildBtInfo(data.uid);
        if (bt) { //是否在队列中
            buttonsNode.Swih('uping')[0].Child('val').setLocaleKey(bt.isRuning() ? 'ui.uping' : 'ui.queueing');
        }
        else {
            buttonsNode.Swih('up_be');
        }
    };
    ViewHelper.prototype.updateCostViewForBuild = function (node, ctypes, time, cd) {
        // const up = node.Child('time/guide_up')
        if (GameHelper_1.gameHpr.isNoviceMode) {
            time /= NoviceConfig_1.NOVICE_BUILD_SPEED_MUL;
            time = Math.max(3, Math.floor(time * (1 - (cd || 0))));
            cd = 0;
        }
        this.updateCostView(node, ctypes, time, cd);
        // if (up?.getActive()) {
        //     node.Child('time/val', cc.Label).Color('#49983C')
        // }
    };
    // 刷新费用
    ViewHelper.prototype.updateCostView = function (node, ctypes, time, cd) {
        var _this = this;
        var _a;
        node.Child('cost').Items(ctypes || [], function (it, cost) { return _this.updateCostViewOne(it, cost, true); });
        if ((_a = node.Child('time')) === null || _a === void 0 ? void 0 : _a.setActive(!!time)) {
            cd = cd || 0;
            var up = node.Child('time/up', cc.Label);
            if (up === null || up === void 0 ? void 0 : up.setActive(!!cd)) {
                up.string = "(-" + Math.floor(cd * 100) + "%)";
            }
            if (cd) {
                time = Math.max(3, Math.floor(time * (1 - cd)));
            }
            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss');
        }
    };
    // 刷新费用
    ViewHelper.prototype.updateFreeCostView = function (node, ctypes, time, cd, isFree, policyFreeCount) {
        var _this = this;
        var _a;
        node.Child('cost').Items(ctypes || [], function (it, cost) { return _this.updateFreeCostViewOne(it, cost, !(isFree || policyFreeCount > 0)); });
        if ((_a = node.Child('time')) === null || _a === void 0 ? void 0 : _a.setActive(!!time)) {
            var up = node.Child('time/up');
            if (isFree || policyFreeCount > 0) {
                cd = 1;
                var node_1 = up.Swih('free')[0], bothFree = isFree && policyFreeCount > 0;
                node_1.Child('val', cc.Label).string = bothFree ? "x(" + policyFreeCount : policyFreeCount > 0 ? "x" + policyFreeCount : '';
                node_1.Child('add', cc.Label).string = bothFree ? '+1' : isFree ? 'x1' : '';
                node_1.Child('xx').active = bothFree;
            }
            else if (!!cd) {
                up.Swih('val')[0].Component(cc.Label).string = "(-" + Math.floor(cd * 100) + "%)";
            }
            else {
                up.Swih('');
            }
            if (cd) {
                time = Math.max(3, Math.floor(time * (1 - cd)));
            }
            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss');
            node.Child('cost').children.forEach(function (m) { var _a; return m.opacity = (isFree || policyFreeCount > 0) && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.type) !== Enums_1.CType.FIXATOR ? 100 : 255; });
            node.Child('time/up/val').opacity = node.Child('time/val').opacity = node.Child('time/icon').opacity = (isFree || policyFreeCount > 0) ? 100 : 255;
        }
    };
    // 刷新单个费用
    ViewHelper.prototype.updateCostViewOne = function (it, cost, isCheck) {
        if (it && cost) {
            it.Data = cost;
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost.type);
            if (!isCheck) {
                it.Child('val', cc.Label).string = cost.count + '';
            }
            else {
                it.Child('val', cc.Label).Color(GameHelper_1.gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + '';
            }
        }
    };
    // 刷新单个费用
    ViewHelper.prototype.updateFreeCostViewOne = function (it, cost, isCheck) {
        if (it && cost) {
            it.Data = cost;
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost.type);
            it.Child('val', cc.Label).Color(!isCheck || GameHelper_1.gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + '';
        }
    };
    // 更新费用
    ViewHelper.prototype.updateCostText = function (it, json) {
        if (json.gold > 0) {
            it.Child('gold/icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.GOLD);
            it.Child('gold/val', cc.Label).string = json.gold + '';
        }
        else if (json.ingot > 0) {
            it.Child('gold/icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.INGOT);
            it.Child('gold/val', cc.Label).string = json.ingot + '';
        }
        else {
            it.Child('gold').active = false;
        }
    };
    // 显示位置
    ViewHelper.prototype.updatePositionView = function (it, index, hasName) {
        if (hasName === void 0) { hasName = true; }
        it.Data = index;
        var posLbl = it.Component(cc.Label);
        if (hasName) {
            posLbl.setLocaleKey('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(index), MapHelper_1.mapHelper.indexToPoint(index).Join());
        }
        else {
            posLbl.string = '(' + MapHelper_1.mapHelper.indexToPoint(index).Join() + ')';
        }
        posLbl._forceUpdateRenderData();
        it.Child('line').width = posLbl.node.width;
    };
    // 刷新道具 根据类型
    ViewHelper.prototype.updateItemByCTypes = function (node, items, key) {
        var _this = this;
        node === null || node === void 0 ? void 0 : node.Items(items, function (it, data) { return _this.updateItemByCTypeOne(it, data, key); });
    };
    ViewHelper.prototype.updateItemByCTypeOne = function (it, item, key, adaptSize) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
        if (!item || !it) {
            return;
        }
        var iconSpr = it.Child('icon', cc.Sprite), countLbl = it.Child('count', cc.Label);
        if (iconSpr) {
            iconSpr.node.removeAllChildren();
        }
        if (item.type === Enums_1.CType.BUILD_LV) { //建筑等级
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadBuildIcon('build_' + item.id, iconSpr, key || mc.currWindName);
            (_a = it.Child('text')) === null || _a === void 0 ? void 0 : _a.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count);
        }
        else if (item.type === Enums_1.CType.HEAD_ICON) { //头像
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPlayerHead(iconSpr, ((_b = assetsMgr.getJsonData('headIcon', item.id)) === null || _b === void 0 ? void 0 : _b.icon) || '', key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_c = it.Child('text')) === null || _c === void 0 ? void 0 : _c.setLocaleKey('ui.headicon_title');
        }
        else if (item.type === Enums_1.CType.CHAT_EMOJI) { //聊天表情
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadEmojiIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_d = it.Child('text')) === null || _d === void 0 ? void 0 : _d.setLocaleKey('ui.chat_emoji_title');
        }
        else if (item.type === Enums_1.CType.TREASURE) { //宝箱
            var lv = ((_e = assetsMgr.getJsonData('treasure', item.id)) === null || _e === void 0 ? void 0 : _e.lv) || 1;
            (_f = it.Swih('text')[0]) === null || _f === void 0 ? void 0 : _f.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count);
        }
        else if (item.type === Enums_1.CType.TITLE) { //称号
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
            (_g = it.Child('text')) === null || _g === void 0 ? void 0 : _g.setLocaleKey('titleText.' + item.id);
        }
        else if (item.type === Enums_1.CType.WIN_POINT) { //胜点
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
            (_h = it.Child('text')) === null || _h === void 0 ? void 0 : _h.setLocaleKey('ui.rank_score_num_2', item.count);
        }
        else if (item.type === Enums_1.CType.PAWN) { //士兵
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_j = it.Child('text')) === null || _j === void 0 ? void 0 : _j.setLocaleKey('pawnText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.EQUIP) { //装备
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadEquipIcon(item.id, iconSpr, key || mc.currWindName);
            (_k = it.Child('text')) === null || _k === void 0 ? void 0 : _k.setLocaleKey('equipText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.POLICY) { //政策
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPolicyIcon(item.id, iconSpr, key || mc.currWindName);
            (_l = it.Child('text')) === null || _l === void 0 ? void 0 : _l.setLocaleKey('policyText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.PAWN_SKIN) { //皮肤
            var textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin');
            if (textNode) { //纯文本 '限定皮肤'
                it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc');
                textNode.off('click');
                textNode.on('click', function () { return _this.showPnl('common/ItemBox', item); });
            }
            else if (skinNode) {
                it.Swih('pawn_skin');
                ResHelper_1.resHelper.loadPawnHeadIcon(item.id, skinNode, key || mc.currWindName);
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                iconSpr && ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
                (_m = it.Child('text')) === null || _m === void 0 ? void 0 : _m.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000));
            }
        }
        else if (item.type === Enums_1.CType.HERO_DEBRIS) { //英雄残卷
            if (it.Child('text_click')) {
                (_o = it.Swih('text')[0]) === null || _o === void 0 ? void 0 : _o.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id));
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName);
                (_p = it.Child('text')) === null || _p === void 0 ? void 0 : _p.setLocaleKey('portrayalText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.HERO_OPT) { //自选英雄包
            if (it.Child('text_click')) {
                (_q = it.Swih('text')[0]) === null || _q === void 0 ? void 0 : _q.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id);
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type] + '_' + item.id, iconSpr, key || mc.currWindName);
                (_r = it.Child('text')) === null || _r === void 0 ? void 0 : _r.setLocaleKey('ui.hero_opt_gift_short_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.COMPLETE_GUIDE) { //完成新手引导
            (_s = it.Swih('text')[0]) === null || _s === void 0 ? void 0 : _s.setLocaleKey('guideText.guide_task_complete');
        }
        else if (item.type === Enums_1.CType.CITY_SKIN) { //城市皮肤
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadCityIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_t = it.Child('text')) === null || _t === void 0 ? void 0 : _t.setLocaleKey('ui.title_main_city_skin');
        }
        else {
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'count'; });
            if (iconSpr) {
                var sf = ResHelper_1.resHelper.getResIcon(item.type);
                if (sf) {
                    iconSpr.spriteFrame = sf;
                }
                else {
                    ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
                }
            }
            if (countLbl) {
                countLbl.string = '' + item.count;
            }
        }
    };
    ViewHelper.prototype.updateItemNameByCTypeOne = function (it, item, isFormat) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8;
        if (isFormat === void 0) { isFormat = false; }
        if (!item || !it) {
            return;
        }
        var countLbl = it.Child('count', cc.Label);
        if (item.type === Enums_1.CType.BUILD_LV) { //建筑等级
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_a = it.Child('text')) === null || _a === void 0 ? void 0 : _a.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count);
            }
            else {
                (_b = it.Child('text')) === null || _b === void 0 ? void 0 : _b.setLocaleKey('ui.build_lv', ut.nameFormator(assetsMgr.lang('buildText.name_' + item.id), 5), item.count);
            }
        }
        else if (item.type === Enums_1.CType.HEAD_ICON) { //头像
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_c = it.Child('text')) === null || _c === void 0 ? void 0 : _c.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.headicon_title'), 5));
            }
            else {
                (_d = it.Child('text')) === null || _d === void 0 ? void 0 : _d.setLocaleKey('ui.headicon_title');
            }
        }
        else if (item.type === Enums_1.CType.CHAT_EMOJI) { //聊天表情
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_e = it.Child('text')) === null || _e === void 0 ? void 0 : _e.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.chat_emoji_title'), 5));
            }
            else {
                (_f = it.Child('text')) === null || _f === void 0 ? void 0 : _f.setLocaleKey('ui.chat_emoji_title');
            }
        }
        else if (item.type === Enums_1.CType.TREASURE) { //宝箱
            var lv = ((_g = assetsMgr.getJsonData('treasure', item.id)) === null || _g === void 0 ? void 0 : _g.lv) || 1;
            if (isFormat) {
                (_h = it.Swih('text')[0]) === null || _h === void 0 ? void 0 : _h.setLocaleKey('ui.treasure_reward_desc', ut.nameFormator(assetsMgr.lang('ui.treasure_name_' + lv), 5), item.count);
            }
            else {
                (_j = it.Swih('text')[0]) === null || _j === void 0 ? void 0 : _j.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count);
            }
        }
        else if (item.type === Enums_1.CType.TITLE) { //称号
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_k = it.Child('text')) === null || _k === void 0 ? void 0 : _k.setLocaleKey(ut.nameFormator(assetsMgr.lang('titleText.' + item.id), 5));
            }
            else {
                (_l = it.Child('text')) === null || _l === void 0 ? void 0 : _l.setLocaleKey('titleText.' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.WIN_POINT) { //胜点
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_m = it.Child('text')) === null || _m === void 0 ? void 0 : _m.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.rank_score_num_2'), 5), item.count);
            }
            else {
                (_o = it.Child('text')) === null || _o === void 0 ? void 0 : _o.setLocaleKey('ui.rank_score_num_2', item.count);
            }
        }
        else if (item.type === Enums_1.CType.PAWN) { //士兵
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_p = it.Child('text')) === null || _p === void 0 ? void 0 : _p.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + item.id), 5));
            }
            else {
                (_q = it.Child('text')) === null || _q === void 0 ? void 0 : _q.setLocaleKey('pawnText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.EQUIP) { //装备
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_r = it.Child('text')) === null || _r === void 0 ? void 0 : _r.setLocaleKey(ut.nameFormator(assetsMgr.lang('equipText.name_' + item.id), 5));
            }
            else {
                (_s = it.Child('text')) === null || _s === void 0 ? void 0 : _s.setLocaleKey('equipText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.POLICY) { //政策
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_t = it.Child('text')) === null || _t === void 0 ? void 0 : _t.setLocaleKey(ut.nameFormator(assetsMgr.lang('policyText.name_' + item.id), 5));
            }
            else {
                (_u = it.Child('text')) === null || _u === void 0 ? void 0 : _u.setLocaleKey('policyText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.PAWN_SKIN) { //皮肤
            var textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin');
            if (textNode) { //纯文本 '限定皮肤'
                if (isFormat) {
                    it.Swih('text_click')[0].setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.limited_skin_reward_desc'), 5));
                }
                else {
                    it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc');
                }
                textNode.off('click');
                textNode.on('click', function () { return _this.showPnl('common/ItemBox', item); });
            }
            else if (skinNode) {
                it.Swih('pawn_skin');
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_v = it.Child('text')) === null || _v === void 0 ? void 0 : _v.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + Math.floor(item.id / 1000)), 5));
                }
                else {
                    (_w = it.Child('text')) === null || _w === void 0 ? void 0 : _w.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000));
                }
            }
        }
        else if (item.type === Enums_1.CType.HERO_DEBRIS) { //英雄残卷
            if (it.Child('text_click')) {
                if (isFormat) {
                    (_x = it.Swih('text')[0]) === null || _x === void 0 ? void 0 : _x.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id), 5));
                }
                else {
                    (_y = it.Swih('text')[0]) === null || _y === void 0 ? void 0 : _y.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id));
                }
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_z = it.Child('text')) === null || _z === void 0 ? void 0 : _z.setLocaleKey(ut.nameFormator(assetsMgr.lang('portrayalText.name_' + item.id), 5));
                }
                else {
                    (_0 = it.Child('text')) === null || _0 === void 0 ? void 0 : _0.setLocaleKey('portrayalText.name_' + item.id);
                }
            }
        }
        else if (item.type === Enums_1.CType.HERO_OPT) { //自选英雄包
            if (it.Child('text_click')) {
                if (isFormat) {
                    (_1 = it.Swih('text')[0]) === null || _1 === void 0 ? void 0 : _1.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_' + item.id), 5));
                }
                else {
                    (_2 = it.Swih('text')[0]) === null || _2 === void 0 ? void 0 : _2.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id);
                }
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_3 = it.Child('text')) === null || _3 === void 0 ? void 0 : _3.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_short_' + item.id), 5));
                }
                else {
                    (_4 = it.Child('text')) === null || _4 === void 0 ? void 0 : _4.setLocaleKey('ui.hero_opt_gift_short_' + item.id);
                }
            }
        }
        else if (item.type === Enums_1.CType.COMPLETE_GUIDE) { //完成新手引导
            if (isFormat) {
                (_5 = it.Swih('text')[0]) === null || _5 === void 0 ? void 0 : _5.setLocaleKey(ut.nameFormator(assetsMgr.lang('guideText.guide_task_complete'), 5));
            }
            else {
                (_6 = it.Swih('text')[0]) === null || _6 === void 0 ? void 0 : _6.setLocaleKey('guideText.guide_task_complete');
            }
        }
        else if (item.type === Enums_1.CType.CITY_SKIN) { //城市皮肤
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_7 = it.Child('text')) === null || _7 === void 0 ? void 0 : _7.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.title_main_city_skin'), 5));
            }
            else {
                (_8 = it.Child('text')) === null || _8 === void 0 ? void 0 : _8.setLocaleKey('ui.title_main_city_skin');
            }
        }
        else {
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'count'; });
            if (countLbl) {
                countLbl.string = '' + item.count;
            }
        }
    };
    // 刷新装备显示
    ViewHelper.prototype.updateEquipView = function (node, equip, key, lockEffect, smeltEffects, showRange) {
        var nameNode = node.Child('name') || node.Child('icon/name');
        nameNode.setLocaleKey(equip.name);
        var meltNode = node.Child('melt') || node.Child('icon/melt');
        if (meltNode === null || meltNode === void 0 ? void 0 : meltNode.setActive(equip.isSmelt())) {
            for (var i = 0; i < 2; i++) {
                var it = meltNode.Child(i), data = equip.smeltEffects[i];
                if (it.active = !!data) {
                    ResHelper_1.resHelper.loadEquipIcon(data.id, it, key);
                }
            }
        }
        var noForge = node.Child('no_forge') || node.Child('icon/no_forge');
        noForge === null || noForge === void 0 ? void 0 : noForge.setActive(false);
        var exclusive = node.Child('exclusive');
        if (exclusive === null || exclusive === void 0 ? void 0 : exclusive.setActive(!!equip.exclusive_pawn)) {
            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + equip.exclusive_pawn);
        }
        this.updateEquipAttrView(node.Child('attrs'), equip, lockEffect, smeltEffects, showRange);
    };
    ViewHelper.prototype.updateEquipAttrView = function (node, equip, lockEffect, smeltEffects, showRange) {
        var _a, _b;
        var attrNode = node.Child('attr'), effectNode = node.Child('effects'), skillIntensifyNode = node.Child('skill_intensify');
        (_a = node.Child('more_desc')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        (_b = node.Child('more_effects')) === null || _b === void 0 ? void 0 : _b.setActive(false);
        // 属性
        var serverRunDay = GameHelper_1.gameHpr.getServerRunDay();
        attrNode.Items(equip.mainAttrs, function (it, data) {
            var _a, _b;
            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
            var value = data.value, canShowRange = showRange && data.base.length > 0;
            if (!!data.todayAdd) {
                value += serverRunDay * data.todayAdd;
            }
            it.Child('val', cc.Label).string = '+' + value;
            // 额外添加的
            var add = value - data.initValue;
            if ((_a = it.Child('add')) === null || _a === void 0 ? void 0 : _a.setActive(!!add)) {
                it.Child('add/0', cc.Label).string = '(' + data.initValue;
                var addLbl = it.Child('add/1', cc.Label);
                if (addLbl.setActive(!canShowRange)) {
                    addLbl.string = '+' + add;
                }
            }
            // 随机范围
            if ((_b = it.Child('base')) === null || _b === void 0 ? void 0 : _b.setActive(canShowRange)) {
                data.base.forEach(function (v, i) { return it.Child('base/' + i, cc.Label).string = v + ''; });
            }
        });
        // 效果
        var effectCount = equip.effects.length;
        if (effectNode.active = effectCount > 0) {
            var mult_1 = effectCount > 1;
            var effects = equip.effects, smeltEffectMap_1 = null, effectIdMap_1 = null;
            // 如果有锁定和融炼 排个序
            if (lockEffect || smeltEffects) {
                smeltEffectMap_1 = {};
                smeltEffects.forEach(function (m) { return smeltEffectMap_1[m.type] = m.id; });
                effects = effects.slice().sort(function (a, b) {
                    var aw = smeltEffectMap_1[a.type] ? 1 : 0, bw = smeltEffectMap_1[b.type] ? 1 : 0;
                    aw = aw * 10 + Number(a.type === lockEffect);
                    bw = bw * 10 + Number(b.type === lockEffect);
                    return aw - bw;
                });
            }
            if (smeltEffects && equip.isExclusive()) {
                effectIdMap_1 = {};
                GameHelper_1.gameHpr.world.getExclusiveEquipEffects(equip.id).forEach(function (m) { return effectIdMap_1[m] = true; });
            }
            effectNode.Items(effects, function (it, data) {
                var descParams = data.getDescParams(showRange), smeltId = smeltEffectMap_1 === null || smeltEffectMap_1 === void 0 ? void 0 : smeltEffectMap_1[data.type];
                if (smeltId) { // 融炼词条
                    it.Color('#C2B3A1');
                    it.setLocaleKey('ui.yet_smelt_equip_' + Number(!!effectIdMap_1[data.type]), assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)).replace(/#000001/g, '#C2B3A1'), 'equipText.name_' + smeltId);
                }
                else if (data.type === lockEffect) { // 锁定词条
                    it.Color('#C2B3A1');
                    it.setLocaleKey('ui.yet_lock_equip', assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)).replace(/#000001/g, '#C2B3A1'));
                }
                else if (mult_1) { // 正常词条
                    it.Color('#756963');
                    it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)));
                }
                else {
                    it.Color('#756963');
                    it.setLocaleKey.apply(it, __spread([data.name], descParams));
                }
            });
        }
        // 技能强化效果
        if (skillIntensifyNode === null || skillIntensifyNode === void 0 ? void 0 : skillIntensifyNode.setActive(!!equip.skillIntensify && equip.skillIntensify.length > 0)) {
            skillIntensifyNode.setLocaleKey("pawnSkillText.intensify_desc_" + equip.skillIntensify[0] + "_" + equip.skillIntensify[1]);
        }
    };
    // 刷新装备基础信息显示
    ViewHelper.prototype.updateEquipBaseView = function (node, json) {
        var _this = this;
        var _a;
        var nameNode = node.Child('name') || node.Child('icon/name');
        nameNode.setLocaleKey('equipText.name_' + json.id);
        var exclusive = node.Child('exclusive') || node.Child('icon/exclusive');
        if (exclusive === null || exclusive === void 0 ? void 0 : exclusive.setActive(!!json.exclusive_pawn)) {
            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + json.exclusive_pawn);
        }
        var noForge = node.Child('no_forge') || node.Child('icon/no_forge');
        noForge === null || noForge === void 0 ? void 0 : noForge.setActive(true);
        // 属性
        var attrNode = node.Child('attrs/attr'), attrs = this.getEquipAttrs(json);
        attrNode.Items(attrs, function (it, data) {
            var _a, _b;
            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
            it.Child('val', cc.Label).string = _this.wrapEquipVal(data.val);
            (_a = it.Child('add')) === null || _a === void 0 ? void 0 : _a.setActive(false);
            (_b = it.Child('base')) === null || _b === void 0 ? void 0 : _b.setActive(false);
        });
        // 效果
        var effectIds = ut.stringToNumbers(json.effect, '|');
        if (!!json.exclusive_pawn) {
            effectIds = GameHelper_1.gameHpr.world.getExclusiveEquipEffects(json.id);
        }
        var effects = effectIds.map(function (m) { return assetsMgr.getJsonData('equipEffect', m); }).sort(function (a, b) { return a.sort - b.sort; });
        var effectsNode = node.Child('attrs/effects'), moreDescNode = node.Child('attrs/more_desc'), moreEffectsNode = node.Child('attrs/more_effects'), skillIntensifyNode = node.Child('attrs/skill_intensify');
        // 多个的处理
        moreDescNode.active = moreEffectsNode.active = effects.length > 1;
        if (moreEffectsNode.active) {
            moreDescNode.setLocaleKey('ui.equip_more_effect_desc', json.effect_count);
            moreEffectsNode.Items(effects, function (it, m) { return _this.updateEquipBaseEffect(it, m, true); });
        }
        // 单个的处理
        if (effectsNode.active = !!effects.length && !moreEffectsNode.active) {
            effectsNode.Items(effects, function (it, m) { return _this.updateEquipBaseEffect(it, m, false); });
        }
        // 技能强化效果
        if (skillIntensifyNode.active = !!json.skill_intensify) {
            var _b = __read(ut.stringToNumbers(json.skill_intensify, ','), 2), a = _b[0], b = _b[1];
            skillIntensifyNode.setLocaleKey("pawnSkillText.intensify_desc_" + a + "_" + b);
        }
        // 空属性
        (_a = node.Child('attrs/empty_effect')) === null || _a === void 0 ? void 0 : _a.setActive(!effects.length);
    };
    ViewHelper.prototype.wrapEquipVal = function (arr) {
        return arr[0] === arr[1] ? (arr[0] + '') : "[" + arr.join('-') + "]";
    };
    ViewHelper.prototype.getEquipAttrs = function (data) {
        var arr = [];
        if (data.hp) {
            arr.push({ type: 1, val: ut.stringToNumbers(data.hp, ',') });
        }
        if (data.attack) {
            arr.push({ type: 2, val: ut.stringToNumbers(data.attack, ',') });
        }
        return arr;
    };
    ViewHelper.prototype.updateEquipBaseEffect = function (it, json, isMore) {
        var params = [], _a = __read(json.value.split(','), 2), a = _a[0], b = _a[1];
        if (json.id === Enums_1.EquipEffectType.MINGGUANG_ARMOR || json.id === Enums_1.EquipEffectType.BAIBI_SWORD) {
            params.push('0');
        }
        else if (!json.value) {
            params.push('');
        }
        else if (a === b) {
            params.push("" + a + json.suffix);
        }
        else {
            params.push("[" + a + "-" + b + "]" + json.suffix);
        }
        if (json.odds) {
            params.push("[" + ut.stringToNumbers(json.odds, ',').join('-') + "]%");
        }
        it.Color('#756963');
        if (isMore) {
            it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang('equipText.effect_' + json.id, params));
        }
        else {
            it.setLocaleKey('equipText.effect_' + json.id, params);
        }
    };
    // 刷新宠物信息
    ViewHelper.prototype.updatePetView = function (it, id, lv) {
        it.Child('name').setLocaleKey('pawnText.name_' + id);
        var lvNode = it.Child('name/lv');
        if (lvNode.active = !!lv) {
            lvNode.setLocaleKey('ui.lv', lv);
        }
        it.Child('none').active = !lv;
        this.updatePetAttrView(it.Child('attrs'), id, lv);
    };
    ViewHelper.prototype.updatePetAttrView = function (node, id, lv) {
        if (node.active = !!lv) {
            var attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + lv);
            var mainAttrs = [{ type: 1, value: attrJson.hp }, { type: 2, value: attrJson.attack }];
            // 属性
            node.Child('attr').Items(mainAttrs, function (it, data) {
                it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
                it.Child('val', cc.Label).string = '' + data.value;
            });
            // 技能
            var skillNode = node.Child('skill');
            if (skillNode.active = !!attrJson.skill) {
                skillNode.Items(ut.stringToNumbers(attrJson.skill), function (node, data) {
                    var json = assetsMgr.getJsonData('pawnSkill', data);
                    var text = assetsMgr.lang(json.desc, json.desc_params.split('|'));
                    node.setLocaleKey('ui.res_transit_cap_desc', "<color=#333333>" + assetsMgr.lang(json.name) + "</c>", text);
                });
            }
        }
    };
    // 刷新玩家简介
    ViewHelper.prototype.updatePlayerPersonalDesc = function (node, uid, plr) {
        var personalDescLbl = node.Component(cc.Label);
        personalDescLbl.Color('#B6A591').string = '...';
        GameHelper_1.gameHpr.getUserPersonalDesc(uid, plr).then(function (val) {
            if (personalDescLbl.isValid) {
                personalDescLbl.Color(val ? '#756963' : '#B6A591').string = val || assetsMgr.lang('ui.empty_personal_desc');
            }
        });
    };
    // 刷新称号显示
    ViewHelper.prototype.updatePlayerTitleText = function (node, uid, plr) {
        if (!node) {
            return;
        }
        GameHelper_1.gameHpr.getUserTitle(uid, plr).then(function (title) {
            if (node.isValid) {
                var json = assetsMgr.getJsonData('title', title);
                node.Color((json === null || json === void 0 ? void 0 : json.color) || '#756963').setLocaleKey(json ? 'titleText.' + json.id : 'ui.nought');
            }
        });
    };
    // 刷新人气显示
    ViewHelper.prototype.updatePlayerPopularity = function (root, buttonNode, uid, key, plr) {
        var _a, _b;
        var button = (_b = (_a = buttonNode.Swih(uid === GameHelper_1.gameHpr.getUid() ? 'popularity_record_be' : 'add_popularity_be')) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.Component(cc.Button);
        if (button) {
            button.node.opacity = 120;
            button.interactable = false;
        }
        root.Items(1, function (it, _) {
            ResHelper_1.resHelper.loadGiftIcon(101, it.Child('icon'), key);
            it.Child('count').active = false;
            it.Child('loading').active = true;
        });
        GameHelper_1.gameHpr.getUserPopularity(uid, plr).then(function (info) {
            var _a;
            if (!root.isValid) {
                return;
            }
            else if (!((_a = info === null || info === void 0 ? void 0 : info.list) === null || _a === void 0 ? void 0 : _a.length)) {
                info = { list: [[101, 0]] };
            }
            if (button === null || button === void 0 ? void 0 : button.isValid) {
                button.node.opacity = 255;
                button.interactable = true;
            }
            root.Items(info.list, function (it, _a) {
                var _b = __read(_a, 2), id = _b[0], count = _b[1];
                it.Child('count').active = true;
                it.Child('loading').active = false;
                ResHelper_1.resHelper.loadGiftIcon(id, it.Child('icon'), key);
                it.Child('count', cc.Label).string = count + '';
            });
        });
    };
    // 刷新段位
    ViewHelper.prototype.updatePlayerRankInfo = function (node, uid, key, plr) {
        GameHelper_1.gameHpr.getUserRankScore(uid, plr).then(function (data) {
            if (node.isValid) {
                var _a = GameHelper_1.gameHpr.resolutionRankScore(data.score, data.count), id = _a.id, winPoint = _a.winPoint;
                var icon = node.Child('icon');
                icon.Swih(id >= 0 ? 'val' : 'none');
                id >= 0 && ResHelper_1.resHelper.loadRankScoreIcon(id, icon, key);
                node.Child('rank').setLocaleKey(id >= 0 ? 'ui.rank_name_' + id : 'ui.rank_name_none');
                node.Child('rank_val').setLocaleKey('ui.rank_score_num', winPoint);
                node.Child('ranked_val', cc.Label).string = data.count + '';
            }
        });
    };
    // 刷新总局数
    ViewHelper.prototype.updateTotalGameCount = function (node, uid, plr) {
        var valLbl = node.Child('val', cc.Label), winRate = node.Child('win_rate');
        valLbl.string = '-';
        winRate.active = false;
        GameHelper_1.gameHpr.getUserTotalGameCount(uid, plr).then(function (info) {
            if (node.isValid) {
                // winRate.active = true
                var _a = __read(info, 2), win = _a[0], total = _a[1];
                valLbl.string = '' + total;
                // winRate.setLocaleKey('ui.win_rate', total ? Math.floor(win / total * 100) : 0)
            }
        });
    };
    // 在主场景显示建筑信息
    ViewHelper.prototype.showBuildInfoByMain = function (id, params) {
        return __awaiter(this, void 0, void 0, function () {
            var ui, area, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ui = Constant_1.FIXATION_MENU_CONFIG[id];
                        if (!ui) {
                            return [2 /*return*/, false];
                        }
                        this.showNetWait(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.areaCenter.reqAreaByIndex(GameHelper_1.gameHpr.player.getMainCityIndex())];
                    case 1:
                        area = _a.sent();
                        this.showNetWait(false);
                        data = area === null || area === void 0 ? void 0 : area.getBuildsById(id)[0];
                        if (!data) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.showPnl(ui, data, params)];
                    case 2:
                        _a.sent();
                        return [2 /*return*/, true];
                    case 3: return [2 /*return*/, false];
                }
            });
        });
    };
    // 显示金币不足
    ViewHelper.prototype.showGoldNotEnough = function () {
        var _this = this;
        this.showMessageBox('ui.gold_not_enough_tip', {
            okText: 'ui.button_exchange',
            cancelText: 'ui.button_no',
            ok: function () { return _this.showPnl('common/ShopBuyGoldTip'); },
            cancel: function () { }
        });
    };
    // 初始化转盘界面
    ViewHelper.prototype.initWheelItem = function (it, data, descColor) {
        var items = [];
        if (data.factor > 0) {
            it.Data = data;
            var runDay = GameHelper_1.gameHpr.user.getWheelInRoomRunDay();
            if (runDay > 0) {
                var count = Math.floor(30 * Math.min(runDay, 10) * data.factor);
                items = count > 0 ? [new CTypeObj_1.default().init(Enums_1.CType.CEREAL, 0, count), new CTypeObj_1.default().init(Enums_1.CType.TIMBER, 0, count), new CTypeObj_1.default().init(Enums_1.CType.STONE, 0, count)] : [];
            }
        }
        else if (data.factor === -1) {
            it.Data = data;
            items = GameHelper_1.gameHpr.user.getWheelRandomAwards();
        }
        else {
            it.Data = null;
            items = GameHelper_1.gameHpr.stringToCTypes(data.award);
        }
        this.updateItemByCTypes(it.Child('award'), items);
        it.Child('loading').active = !!it.Data && !items.length;
        it.Child('desc').Color(descColor).setLocaleKey(items.length === 0 ? 'ui.empty' : '');
    };
    // 打开关闭弹出框
    ViewHelper.prototype.changePopupBoxList = function (node, val, isDown) {
        node.Child('select_mask_be').active = val;
        var mask = node.Child('mask'), root = mask.Child('root');
        if (val) {
            mask.active = true;
            root.y = isDown ? mask.height : -mask.height;
            var y = isDown ? -4 : 4;
            cc.tween(root).to(0.15, { y: y }, { easing: cc.easing.sineOut }).start();
        }
        else {
            root.y = isDown ? -4 : 4;
            var y = isDown ? mask.height : -mask.height;
            cc.tween(root).to(0.1, { y: y }).call(function () { return mask.active = false; }).start();
        }
        cc.tween(node.Child('icon')).to(0.15, { angle: val ? -180 : 0 }).start();
    };
    ViewHelper.prototype.closePopupBoxList = function (node) {
        node.Child('select_mask_be').active = false;
        node.Child('mask').active = false;
        node.Child('icon').angle = 0;
    };
    // 显示不再提示
    ViewHelper.prototype.showNoLongerTip = function (key, data) {
        if (!GameHelper_1.gameHpr.isNoLongerTip(key)) {
            data.noKey = key;
            data.okText = data.okText || 'ui.button_gotit';
            this.showPnl('common/NoLongerTip', data);
            return true;
        }
        return false;
    };
    // 显示不再提示
    ViewHelper.prototype.showResFullNoLongerTip = function (key, items, data) {
        if (!GameHelper_1.gameHpr.isNoLongerTipBySid(key)) {
            data.noKey = key;
            data.okText = data.okText || 'ui.button_gotit';
            this.showPnl('common/ResFullNoLongerTip', items, data);
            return true;
        }
        return false;
    };
    // 添加人气
    ViewHelper.prototype.addPlayerPopularity = function (data, cb) {
        var info = data.popularityInfo;
        if (info === null || info === void 0 ? void 0 : info.reqing) {
            return;
        }
        else if (info && info.records.length > 0) {
            var lastTime_1 = 0, d_1 = null, uid_1 = GameHelper_1.gameHpr.getUid();
            info.records.forEach(function (m) {
                if (m.time > lastTime_1) {
                    lastTime_1 = m.time;
                }
                if (m.uid === uid_1) {
                    d_1 = m;
                }
            });
            var now = Date.now();
            if (d_1 && now - d_1.time < Constant_1.ONE_USER_POPULARITY_CHANGE_INTERVAL) {
                var day = Math.max(1, Math.floor((Constant_1.ONE_USER_POPULARITY_CHANGE_INTERVAL - (now - d_1.time)) / ut.Time.Day));
                return this.showMessageBox('ui.month_add_popularity_tip', { params: [day], okText: 'ui.button_gotit' });
            }
            else if (lastTime_1 > ut.dateZeroTime(GameHelper_1.gameHpr.getServerNowTime())) {
                return this.showMessageBox('ui.today_add_popularity_tip', { okText: 'ui.button_gotit' });
            }
        }
        this.showPnl('common/AddPopularityTip', data, cb);
    };
    // 刷新图鉴心
    ViewHelper.prototype.updateBookStar = function (node, star) {
        var val = star * 0.5;
        node.children.forEach(function (m, i) {
            if (val >= 1) {
                val -= 1;
                m.Component(cc.MultiFrame).setFrame(2);
            }
            else if (val >= 0.5) {
                val -= 0.5;
                m.Component(cc.MultiFrame).setFrame(1);
            }
            else {
                m.Component(cc.MultiFrame).setFrame(0);
            }
        });
    };
    // 显示画像名字
    ViewHelper.prototype.showPortrayalName = function (node, name, vice) {
        node.Child('val').setLocaleKey(name);
        var viceNode = node.Child('vice');
        if (viceNode.active = !!vice) {
            viceNode.setLocaleKey('ui.bracket', vice);
        }
    };
    // 显示立绘
    ViewHelper.prototype.updatePicture = function (id, isUnlock, iconNode, offset, hasAnim, key) {
        var valNode = iconNode.Child('val');
        var anim = valNode.Component(FrameAnimationCmpt_1.default);
        anim === null || anim === void 0 ? void 0 : anim.clean();
        ResHelper_1.resHelper.loadPortrayalImage(id, valNode, key);
        iconNode.setPosition(offset);
        valNode.opacity = isUnlock ? 255 : 100;
        if (isUnlock) {
            iconNode.Component(cc.Sprite).spriteFrame = null;
        }
        else {
            ResHelper_1.resHelper.loadPortrayalImage(id, iconNode, key);
        }
        if (anim && isUnlock && hasAnim) {
            anim.init('portrayal_' + id, key).then(function () {
                if (valNode.isValid) {
                    anim.play('standby');
                }
            });
        }
    };
    // 刷新画像碎片数量
    ViewHelper.prototype.updatePortrayalDebrisCount = function (it, debris) {
        var isCanComp = debris >= Constant_1.PORTRAYAL_COMP_NEED_COUNT;
        it.Child('debris_count/val', cc.Label).Color(isCanComp ? cc.Color.GREEN : cc.Color.WHITE).string = debris + '';
        it.Child('debris_count').Color(isCanComp ? '#FFA647' : cc.Color.GRAY);
    };
    // 显示获得画像
    ViewHelper.prototype.showGainPortrayalDebris = function (id, count) {
        this.showPnl('common/GetPortrayal', id, count);
    };
    // 刷新英雄属性
    ViewHelper.prototype.updatePortrayalAttr = function (node, data, isHero) {
        var _this = this;
        var root = node.Child('attrs');
        //
        root.Child('avatar/val').setLocaleKey(data.avatarPawnName);
        // 属性
        root.Child('attr').Items(this.getPortrayalMainAttrs(data, isHero), function (it, d) {
            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1);
            it.Child('val', cc.Label).string = d.value;
        });
        // 技能
        var id = data.json.skill;
        root.Child('skill').setLocaleKey('ui.res_transit_cap_desc', "<color=#333333>" + assetsMgr.lang('portrayalSkillText.name_' + id) + "</c>", assetsMgr.lang('portrayalSkillText.desc_' + id, this.getPortrayalSkillDescParams(data, isHero)));
        // 韬略
        var strategysNode = root.Child('strategys');
        var showStrategy = root.Child('strategy').active = strategysNode.active = isHero && data.strategys.length > 0;
        if (showStrategy) {
            root.Child('strategy/name/count', cc.Label).string = "(" + data.strategys.length + ")";
            strategysNode.Items(data.strategys, function (it, strategy) { return _this.showStrategyText(it, strategy, data.avatarPawnName); });
        }
    };
    // 显示韬略文本
    ViewHelper.prototype.showStrategyText = function (it, strategy, avatarPawnName) {
        it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang(strategy.desc, strategy.getDescParamsRange(avatarPawnName, 1, '#' + it.color.toHEX('#rrggbb'))));
    };
    ViewHelper.prototype.getPortrayalMainAttrs = function (data, isHero) {
        if (isHero && data.mainAttrs.length > 0) {
            return data.mainAttrs.map(function (m) { return { type: m.type, value: '+' + m.value }; });
        }
        return ['hp', 'attack'].map(function (k, i) { return { type: i + 1, value: '[' + data.json[k].replace(',', '-') + ']' }; });
    };
    // 获取说明参数
    ViewHelper.prototype.getPortrayalSkillDescParams = function (data, isHero) {
        if (isHero && data.skill) {
            return data.skill.getDescParams();
        }
        var json = assetsMgr.getJsonData('portrayalSkill', data.json.skill);
        var arr = [];
        if (json.value) {
            arr.push('[' + json.value.replace(',', '-') + ']' + json.suffix);
        }
        if (json.target) {
            arr.push(json.target);
        }
        return arr;
    };
    // 刷新英雄简短属性
    ViewHelper.prototype.updatePortrayalShortAttr = function (node, attrs, avatarPawnName) {
        var mainAttrs = [], skill = null, strategys = [];
        attrs.forEach(function (m) {
            var _a = __read(m.attr, 3), fieldType = _a[0], type = _a[1], value = _a[2];
            if (fieldType === 0) { //属性
                mainAttrs.push({ type: type, value: value });
            }
            else if (fieldType === 1) { //技能
                skill = new PortrayalSkillObj_1.default().init(type, value);
            }
            else if (fieldType === 2) { //韬略
                strategys.push(new StrategyObj_1.default().init(type));
            }
        });
        // 属性
        node.Child('attr').Items(mainAttrs, function (it, d) {
            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1);
            it.Child('val', cc.Label).string = '+' + d.value;
        });
        // 技能
        var skillNode = node.Child('skill');
        if (skillNode.active = !!skill) {
            skillNode.setLocaleKey(skill.desc, skill.getDescParams());
        }
        // 韬略
        node.Child('strategy/name/count', cc.Label).string = "(" + strategys.length + ")";
        node.Child('strategys').Items(strategys, function (it, strategy) { return exports.viewHelper.showStrategyText(it, strategy, avatarPawnName); });
    };
    // 返回大厅
    ViewHelper.prototype.backLobby = function () {
        var _this = this;
        GameHelper_1.gameHpr.resetSelectServer(true).then(function (res) { return _this.gotoWind('lobby'); });
    };
    // 显示英雄自选
    ViewHelper.prototype.showHeroOptSelect = function (lv) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var arr = Constant_1.HERO_OPT_GIFT[lv];
                        var list = arr ? arr.map(function (m) { return new PortrayalInfo_1.default().init(m); }) : assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
                        _this.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.GIFT, list, function (arr) { var _a, _b; return resolve((_b = (_a = arr[0]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0); }, lv);
                    })];
            });
        });
    };
    // 获取状态背景颜色
    ViewHelper.prototype.getHeroStateBgColor = function (state) {
        if (state === 0) {
            return '#FFFFFF';
        }
        else if (state === 1) {
            return '#21DE29';
        }
        else if (state === 2) {
            return '#FFFFFF';
        }
        return '#F45757';
    };
    // 显示申请好友
    ViewHelper.prototype.showApplyFriend = function (data) {
        var _this = this;
        this.showMessageBox('ui.apply_friend_tip', {
            params: [ut.nameFormator(data.nickname, 8)],
            ok: function () { return GameHelper_1.gameHpr.friend.applyFriend(data.uid).then(function (err) {
                if (err) {
                    return _this.showAlert(err);
                }
                _this.showAlert('toast.apply_friend_succeed');
            }); },
            cancel: function () { }
        });
    };
    // 显示拉黑
    ViewHelper.prototype.showBlacklist = function (data, event, buttonsNode) {
        var _this = this;
        var state = GameHelper_1.gameHpr.friend.isInBlacklist(data.uid);
        this.showMessageBox(state ? 'ui.cancel_blacklist_desc' : 'ui.add_blacklist_desc', {
            params: [ut.nameFormator(data.nickname, 8)],
            ok: function () { return GameHelper_1.gameHpr.friend.doBlacklist(data.uid, state).then(function (err) {
                if (err) {
                    return _this.showAlert(err);
                }
                else if (buttonsNode.isValid) {
                    event.target.Child('val', cc.MultiFrame).setFrame(!state);
                    buttonsNode.Child('add_friend_be').active = state && !GameHelper_1.gameHpr.friend.isFriend(data.uid);
                }
                _this.showAlert(state ? 'toast.cancel_blacklist_succeed' : 'toast.add_blacklist_succeed');
            }); },
            cancel: function () { }
        });
    };
    ViewHelper.prototype.showFeedback = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isGLobal()) {
                    return [2 /*return*/, this.showPnl('login/HDFeedback')];
                }
                else {
                    return [2 /*return*/, this.showPnl('login/Feedback')];
                }
                return [2 /*return*/];
            });
        });
    };
    // 查看奖励详情
    ViewHelper.prototype.previewRewardDetail = function (reward) {
        if (reward.type === Enums_1.CType.PAWN_SKIN) {
            this.showPnl('common/ItemBox', reward);
        }
        else if (reward.type === Enums_1.CType.HERO_DEBRIS) {
            var json = assetsMgr.getJsonData('portrayalBase', reward.id);
            this.showPnl('common/PortrayalBaseInfo', json, 'shop');
        }
        else if (reward.type === Enums_1.CType.HERO_OPT) {
            var id = reward.id, arr = Constant_1.HERO_OPT_GIFT[id];
            var list = arr ? arr.map(function (m) { return new PortrayalInfo_1.default().init(m); }) : assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
            this.showPnl('common/SelectPortrayalPreview', id, list);
        }
    };
    // 通过deeplink打开相应游戏界面
    ViewHelper.prototype.openUIByDeepLink = function () {
        var data = GameHelper_1.gameHpr.getEnterQuery();
        if (data === null || data === void 0 ? void 0 : data.openUI) {
            var url = data.openUI.replace('_', '/'), params = data.params.split('_');
            this.showPnl.apply(this, __spread([url], params));
        }
    };
    // 刷新士兵属性
    ViewHelper.prototype.updatePawnAttrs = function (node, pawn) {
        node.Child('hp/val', cc.Label).string = pawn.getHpText();
        node.Child('anger/val', cc.Label).string = pawn.getAngerText();
        node.Child('attack/val', cc.Label).string = pawn.getAttackText();
        node.Child('attack_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getAttackRange());
        node.Child('move_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getMoveRange());
        if (node.Child('cereal_c').active = !!pawn.baseJson.cereal_cost) {
            node.Child('cereal_c/val', cc.Label).string = pawn.baseJson.cereal_cost;
        }
    };
    // 刷新士兵技能
    ViewHelper.prototype.updatePawnSkills = function (node, pawn, key) {
        var _a, _b;
        // 技能
        var skillNode = node.Child('skills'), portrayalSkill = node.Child('portrayal_skill_be');
        if (skillNode.active = pawn.skills.length > 0) {
            var skills_1 = [];
            pawn.skills.forEach(function (m) {
                if (m.type < Enums_1.PawnSkillType.RESTRAIN) {
                    var d = skills_1.find(function (s) { return s.type === Enums_1.PawnSkillType.RESTRAIN; });
                    if (!d) {
                        d = skills_1.add({ id: 101, useType: m.use_type, type: Enums_1.PawnSkillType.RESTRAIN, name: m.json.name, descs: [], desc_params: [] });
                    }
                    d.descs.push(m.json.desc);
                    d.desc_params.push(String(m.json.desc_params).split('|'));
                }
                else if (m.type === Enums_1.PawnSkillType.INSTABILITY_ATTACK) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getAttackText(), m.json.desc_params]] });
                }
                else if (m.type === Enums_1.PawnSkillType.PEOPLE_BROKEN) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [pawn.getAttackTextByIndex(2)] });
                }
                else if (m.type === Enums_1.PawnSkillType.SKILL_217) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[m.json.desc_params, pawn.getMoveRange()]] });
                }
                else if (m.type === Enums_1.PawnSkillType.CADET) {
                    skills_1.add({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getCadetLvText(), m.json.desc_params, 'pawnText.name_4205']] });
                }
                else {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [String(m.json.desc_params).split('|')] });
                }
            });
            skillNode.Items(skills_1, function (it, skill) {
                it.Data = skill;
                ResHelper_1.resHelper.loadSkillIcon(skill.id, it.Child('val'), key);
            });
        }
        // 英雄技能
        if (portrayalSkill === null || portrayalSkill === void 0 ? void 0 : portrayalSkill.setActive(!!((_b = (_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id))) {
            ResHelper_1.resHelper.loadHeroSkillIcon(pawn.portrayal.skill.id, portrayalSkill.Child('val'), key);
        }
        return skillNode.active || !!(portrayalSkill === null || portrayalSkill === void 0 ? void 0 : portrayalSkill.active);
    };
    // 刷新军队状态
    ViewHelper.prototype.updateArmyState = function (node, data, march, isHasLving, isMe) {
        var _this = this;
        var states = [], tonden = data instanceof ArmyObj_1.default ? GameHelper_1.gameHpr.world.getArmyTondenInfo(data.index, data.uid) : data.tonden;
        if (data.drillPawns.length > 0) {
            states.push(Enums_1.ArmyState.DRILL);
        }
        if (data.curingPawns.length > 0) {
            states.push(Enums_1.ArmyState.CURING);
        }
        if (isHasLving) {
            states.push(Enums_1.ArmyState.LVING);
        }
        if (tonden) {
            states.push(Enums_1.ArmyState.TONDEN);
        }
        if (data.state !== Enums_1.ArmyState.NONE || states.length === 0) {
            states.unshift(data.state);
        }
        var scroll = node.Child('stateScroll'), player = GameHelper_1.gameHpr.player;
        scroll.Child('state').Items(states, function (it, state) {
            var color = Constant_1.ARMY_STATE_COLOR[state];
            it.Child('val').Color(color).setLocaleKey('ui.army_state_' + state);
            var timeLbl = it.Child('time', cc.LabelTimer);
            timeLbl.Color(color);
            if (!isMe) {
                timeLbl.setActive(false);
            }
            else if (state === Enums_1.ArmyState.MARCH && march) {
                var targetIndex_1 = march.targetIndex;
                timeLbl.setActive(true);
                timeLbl.run(march.getSurplusTime() * 0.001, function () {
                    if (node.isValid) {
                        data.index = targetIndex_1;
                        data.state = Enums_1.ArmyState.NONE;
                        data.treasures.forEach(function (m) { return m.index = data.index; });
                        _this.updateArmyState(node, data, march, isHasLving, isMe);
                    }
                });
                timeLbl.node.opacity = 255;
            }
            else if (state === Enums_1.ArmyState.DRILL || state === Enums_1.ArmyState.LVING || state === Enums_1.ArmyState.CURING) { //训练
                timeLbl.setActive(true);
                var _a = state === Enums_1.ArmyState.DRILL ? player.getSumDrillTimeByArmy(data.uid) : state === Enums_1.ArmyState.CURING ? player.getSumCuringTimeByArmy(data.uid) : player.getSumLvingTimeByArmy(data.uid), time = _a.time, pause = _a.pause;
                if (pause) {
                    timeLbl.string = ut.millisecondFormat(time, 'h:mm:ss');
                }
                else {
                    timeLbl.run(time * 0.001);
                }
                timeLbl.node.opacity = pause ? 128 : 255;
            }
            else if (state === Enums_1.ArmyState.TONDEN && tonden) { //屯田
                timeLbl.setActive(true);
                timeLbl.run(tonden.getSurplusTime() * 0.001, function () {
                    if (node.isValid) {
                        if (data instanceof ArmyObj_1.default) {
                        }
                        else {
                            data.tonden = null;
                        }
                        data.state = Enums_1.ArmyState.NONE;
                        _this.updateArmyState(node, data, march, isHasLving, isMe);
                    }
                });
                timeLbl.node.opacity = 255;
            }
            else {
                timeLbl.setActive(false);
            }
        });
    };
    return ViewHelper;
}());
exports.viewHelper = new ViewHelper();
if (cc.sys.isBrowser) {
    window['viewHelper'] = exports.viewHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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