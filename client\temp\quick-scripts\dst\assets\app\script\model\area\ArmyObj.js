
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/ArmyObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '82a01xgsE1JY4cV4syXCG5d', 'ArmyObj');
// app/script/model/area/ArmyObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PawnObj_1 = require("./PawnObj");
// 战场里面的军队
var ArmyObj = /** @class */ (function () {
    function ArmyObj() {
        this.aIndex = 0; //所属哪个区域
        this.enterDir = -1; //进入方向
        this.uid = '';
        this.name = '';
        this.owner = ''; //拥有者
        this.pawns = []; //士兵列表
        this.drillPawns = []; //训练中的士兵列表
        this.marchSpeed = 0; //行军速度
        this.defaultMarchSpeed = 0; //默认行军速度
        this.state = Enums_1.ArmyState.NONE;
        this.curingPawns = []; // 治疗中的士兵
    }
    ArmyObj.prototype.init = function (index, owner, name) {
        this.aIndex = index;
        this.uid = 'temp_' + ut.UID();
        this.owner = owner;
        this.name = name;
        this.pawns.length = 0;
        this.drillPawns.length = 0;
        this.state = Enums_1.ArmyState.NONE;
        this.curingPawns.length = 0;
        return this;
    };
    ArmyObj.prototype.fromSvr = function (data) {
        var _a, _b, _c;
        this.aIndex = data.index;
        this.enterDir = (_a = data.enterDir) !== null && _a !== void 0 ? _a : -1;
        this.uid = data.uid || '';
        this.name = data.name || '';
        this.owner = data.owner || '';
        this.updatePawns(data.pawns || []);
        this.drillPawns = data.drillPawns || [];
        this.state = data.state || Enums_1.ArmyState.NONE;
        this.defaultMarchSpeed = (_c = (_b = this.getPawnByMinMarchSpeed()) === null || _b === void 0 ? void 0 : _b.marchSpeed) !== null && _c !== void 0 ? _c : 0;
        this.marchSpeed = data.marchSpeed || this.defaultMarchSpeed;
        this.curingPawns = data.curingPawns || [];
        return this;
    };
    ArmyObj.prototype.strip = function () {
        return {
            index: this.aIndex,
            uid: this.uid,
            name: this.name,
            owner: this.owner,
            pawns: this.pawns.map(function (p) { return p.strip(); }),
            drillPawns: this.drillPawns,
            state: this.state,
            enterDir: this.enterDir,
            marchSpeed: this.marchSpeed,
            curingPawns: this.curingPawns,
        };
    };
    ArmyObj.prototype.toPawnsByHP = function () {
        return {
            pawns: this.pawns.map(function (p) {
                return { uid: p.uid, curHp: Math.min(p.curHp, p.maxHp), maxHp: p.maxHp };
            })
        };
    };
    Object.defineProperty(ArmyObj.prototype, "index", {
        get: function () { return this.aIndex; },
        set: function (val) { this.aIndex = val; },
        enumerable: false,
        configurable: true
    });
    // 是否自己的军队
    ArmyObj.prototype.isOwner = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否战斗中
    ArmyObj.prototype.isBattleing = function () {
        return this.state === Enums_1.ArmyState.FIGHT;
    };
    // 是否在治疗中
    ArmyObj.prototype.isCuring = function () {
        return this.state === Enums_1.ArmyState.CURING;
    };
    // 是否有英雄
    ArmyObj.prototype.isHasHero = function () {
        return this.pawns.some(function (m) { return m.isHero(); });
    };
    ArmyObj.prototype.updatePawns = function (pawns) {
        var _this = this;
        // 先删除没有的
        var uidMap = {};
        pawns.forEach(function (m) { return uidMap[m.uid] = true; });
        for (var i = this.pawns.length - 1; i >= 0; i--) {
            if (!uidMap[this.pawns[i].uid]) {
                this.pawns.splice(i, 1);
            }
        }
        // 一个个添加
        pawns.forEach(function (m) { return _this.addPawn(m); });
    };
    // 删除士兵
    ArmyObj.prototype.removePawn = function (uid) {
        this.pawns.remove('uid', uid);
    };
    // 添加士兵
    ArmyObj.prototype.addPawn = function (data) {
        var _a;
        var pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
        if (pawn) {
            pawn.fromSvr(data, this.uid, this.owner, this.name);
        }
        else if (data.uid === ((_a = GameHelper_1.gameHpr.uiShowPawnData) === null || _a === void 0 ? void 0 : _a.uid)) {
            pawn = this.pawns.add(GameHelper_1.gameHpr.uiShowPawnData.fromSvr(data, this.uid, this.owner, this.name));
        }
        else {
            pawn = this.pawns.add(new PawnObj_1.default().fromSvr(data, this.uid, this.owner, this.name));
        }
        pawn.enterDir = this.enterDir;
        return pawn;
    };
    // 获取实际士兵个数
    ArmyObj.prototype.getActPawnCount = function () {
        return this.pawns.length + this.drillPawns.length + this.curingPawns.length;
    };
    // 获取士兵的实际数量
    ArmyObj.prototype.getPawnActCount = function () {
        return this.pawns.filter(function (m) { return !m.isDie(); }).length;
    };
    // 是否可以训练士兵
    ArmyObj.prototype.isCanDrillPawn = function () {
        return this.isOwner() && this.state !== Enums_1.ArmyState.MARCH;
    };
    // 是否可以治疗士兵
    ArmyObj.prototype.isCanCurePawn = function () {
        return this.isOwner() && this.state !== Enums_1.ArmyState.MARCH;
    };
    // 获取所有士兵宝箱数量
    ArmyObj.prototype.getAllPawnTreasureCount = function () {
        return this.pawns.reduce(function (val, cur) { return cur.treasures.length + val; }, 0);
    };
    // 获取所有士兵宝箱列表
    ArmyObj.prototype.getAllPawnTreasures = function () {
        var arr = [];
        this.pawns.forEach(function (m) { return arr.pushArr(m.treasures); });
        return arr;
    };
    Object.defineProperty(ArmyObj.prototype, "treasures", {
        get: function () { return this.getAllPawnTreasures(); },
        enumerable: false,
        configurable: true
    });
    // 设置士兵位置
    ArmyObj.prototype.setPawnsPoint = function (pawns) {
        if (!pawns || pawns.length === 0) {
            return;
        }
        var obj = {};
        pawns.forEach(function (m) { return obj[m.uid] = m.point; });
        this.pawns.forEach(function (m) {
            var point = obj[m.uid];
            if (point) {
                m.setPoint(point);
            }
        });
    };
    /////////////////////////////////////////////////////////// 新手村相关 ///////////////////////////////////////////////////////////////////
    // 获取行军速度最少士兵
    ArmyObj.prototype.getPawnByMinMarchSpeed = function () {
        var speed = -1, pawn = null;
        this.pawns.forEach(function (m) {
            if (speed === -1 || m.marchSpeed < speed || (m.marchSpeed == speed && m.skinId > 0)) {
                speed = m.marchSpeed;
                pawn = m;
            }
        });
        return pawn || this.pawns[0];
    };
    // 回复血量
    ArmyObj.prototype.recoverAllPawn = function () {
        this.pawns.forEach(function (m) { return m.curHp = m.maxHp; });
    };
    // 设置士兵位置
    ArmyObj.prototype.setPawnPoints = function (points) {
        var len = points === null || points === void 0 ? void 0 : points.length;
        if (!len) {
            return;
        }
        for (var i = 0, l = this.pawns.length; i < l; i++) {
            if (i < len) {
                this.pawns[i].setPoint(points[i]);
            }
            else {
                this.pawns[i].setPoint(points[0]);
            }
        }
    };
    // 是否可以战斗
    ArmyObj.prototype.isCanBattle = function () {
        return this.state === Enums_1.ArmyState.NONE && this.drillPawns.length === 0 && this.pawns.length > 0 && !this.pawns.some(function (m) { return GameHelper_1.gameHpr.noviceServer.checkPawnLving(m.uid); });
    };
    ArmyObj.prototype.updatePawnEquipAttr = function (equip) {
        this.pawns.forEach(function (m) { return m.updateEquipAttr(equip.id, equip.attrs); });
    };
    return ArmyObj;
}());
exports.default = ArmyObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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