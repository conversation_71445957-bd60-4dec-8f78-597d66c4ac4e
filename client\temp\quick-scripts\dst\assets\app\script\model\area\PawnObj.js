
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/PawnObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e3304Wt8+VDip92tR5grFfv', 'PawnObj');
// app/script/model/area/PawnObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PortrayalInfo_1 = require("../common/PortrayalInfo");
var EquipInfo_1 = require("../main/EquipInfo");
var BuffObj_1 = require("./BuffObj");
var PawnSkillObj_1 = require("./PawnSkillObj");
var PawnStateObj_1 = require("./PawnStateObj");
// 一个士兵
var PawnObj = /** @class */ (function () {
    function PawnObj() {
        this.aIndex = 0; //所属哪个区域
        this.enterDir = -1;
        this.uid = '';
        this.cuid = ''; //客户端uid
        this.armyUid = ''; //所属队伍uid
        this.armyName = ''; //军队名字
        this.owner = '';
        this.recordDataMap = {}; //记录
        this.treasures = []; //当前宝箱个数
        this.id = 0;
        this.point = cc.v2();
        this.lv = 0; //等级
        this.skinId = 0; //皮肤id
        this.curHp = 0; //当前血量
        this.maxHp = 0;
        this.curAnger = 0; //怒气
        this.maxAnger = 0;
        this.attack = 0;
        this.attackRange = 0; //攻击范围
        this.moveRange = 0; //移动范围
        this.attackSpeed = 0; //当前出手速度
        this.equip = null; //当前携带的装备
        this.portrayal = null; //携带的画像
        this.rodeleroCadetLv = 0; //当前 见习勇者 层数
        this.petId = 0; //携带的宠物
        this.state = null; //当前状态
        this.skills = []; //技能列表
        this.actioning = false; //是否行动中
        this.buffs = []; //当前的buff列表
        this.strategyBuffMap = {}; //韬略map
        this.attrId = 0;
        this.baseJson = null;
        this.attrJson = null;
        this.upCost = []; //升级费用
        this.tempCurrHp = -1; //用于记录
        this.tempAttackAnimTimes = null; //攻击动画时间
        this.cuid = ut.UID();
    }
    PawnObj.prototype.toString = function () {
        return "uid:" + this.uid + ", point:" + this.point.Join(",") + ", hp:" + this.curHp + "/" + this.getMaxHp() + ", attack:" + this.attack + ", attackRange:" + this.attackRange + ", moveRange:" + this.moveRange;
    };
    PawnObj.prototype.init = function (id, equip, lv, skinId, rodeleroCadetLv) {
        var _a;
        this.id = id;
        this.lv = lv || 1;
        this.skinId = skinId || 0;
        this.equip = new EquipInfo_1.default().fromSvr(equip || { id: 0, attrs: [] });
        this.rodeleroCadetLv = rodeleroCadetLv !== null && rodeleroCadetLv !== void 0 ? rodeleroCadetLv : (this.id === 3205 ? (((_a = GameHelper_1.gameHpr.getPlayerInfo(this.owner || GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.rodeleroCadetLv) || 0) : 0);
        this.tempCurrHp = -1;
        this.initJson();
        this.curHp = this.maxHp;
        return this;
    };
    PawnObj.prototype.fromSvr = function (data, auid, owner, aname) {
        var _a, _b;
        // cc.log(data)
        this.aIndex = data.index;
        this.uid = data.uid;
        this.id = data.id;
        this.lv = data.lv || 1;
        this.skinId = data.skinId || 0;
        this.point.set(data.point);
        this.armyUid = auid;
        this.attackSpeed = data.attackSpeed || 0;
        this.equip = new EquipInfo_1.default().fromSvr(data.equip || { id: 0, attrs: [] });
        this.portrayal = data.portrayal ? new PortrayalInfo_1.default().fromSvr(data.portrayal) : null;
        this.rodeleroCadetLv = data.rodeleroCadetLv || 0;
        this.petId = data.petId || 0;
        this.armyName = aname || '';
        this.owner = owner;
        this.recordDataMap = data.recordDataMap || {};
        this.tempCurrHp = -1;
        this.updateTreasures(data.treasures);
        this.setBuffs(data.buffs || []);
        this.strategyBuffMap = {};
        this.initJson(data.isFight);
        this.curHp = (_b = (_a = data.hp) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : this.maxHp;
        this.curAnger = data.curAnger;
        if (!this.curAnger && !this.isBattleing()) {
            this.initAnger();
        }
        return this;
    };
    PawnObj.prototype.strip = function () {
        var _a;
        return {
            index: this.aIndex,
            armyUid: this.armyUid,
            uid: this.uid,
            skinId: this.skinId,
            point: this.point.toJson(),
            id: this.id,
            lv: this.lv,
            curAnger: this.curAnger,
            attackSpeed: this.attackSpeed,
            equip: this.equip.strip(),
            portrayal: (_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.strip(),
            rodeleroCadetLv: this.rodeleroCadetLv,
            petId: this.petId,
            treasures: ut.deepClone(this.treasures),
            hp: [this.curHp, this.maxHp],
            buffs: this.buffs.map(function (m) { return m.strip(); }),
            recordDataMap: ut.deepClone(this.recordDataMap)
        };
    };
    Object.defineProperty(PawnObj.prototype, "index", {
        get: function () { return this.aIndex; },
        enumerable: false,
        configurable: true
    });
    PawnObj.prototype.initJson = function (isFight) {
        var _a;
        this.baseJson = assetsMgr.getJsonData('pawnBase', this.id);
        this.attackSpeed = this.attackSpeed || (((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.attack_speed) || 9);
        this.changeState(isFight ? Enums_1.PawnState.STAND : Enums_1.PawnState.NONE, null, true);
        this.updateAttrJson();
    };
    // 刷新属性json
    PawnObj.prototype.updateAttrJson = function () {
        var _a, _b;
        if (this.isMachine()) {
            this.lv = 1;
        }
        this.attrId = this.id * 1000 + this.lv;
        this.attrJson = assetsMgr.getJsonData('pawnAttr', this.attrId);
        if (!this.attrJson) {
            return ErrorReportHelper_1.errorReportHelper.reportError('PawnObj.updateAttrJson', { id: this.id, lv: this.lv });
        }
        this.maxHp = this.attrJson.hp || 0;
        this.maxAnger = this.attrJson.anger || 0;
        this.attack = this.attrJson.attack || 0;
        this.attackRange = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.attack_range) || 0;
        this.moveRange = ((_b = this.attrJson) === null || _b === void 0 ? void 0 : _b.move_range) || 0;
        this.skills = ut.stringToNumbers(this.attrJson.skill).map(function (m) { return new PawnSkillObj_1.default().init(m); });
        this.upCost = GameHelper_1.gameHpr.getPawnCost(this.id, this.lv, this.attrJson);
        this.updateAttr(true);
    };
    // 获取攻击动画时间
    PawnObj.prototype.getAttackAnimTimes = function () {
        var _a, _b, _c;
        if (!this.tempAttackAnimTimes) {
            var attackAnimTimeStr = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.attack_anim_time) || '';
            if (this.isHero()) {
                attackAnimTimeStr = ((_b = assetsMgr.getJsonData('portrayalBase', this.portrayal.id)) === null || _b === void 0 ? void 0 : _b.attack_anim_time) || attackAnimTimeStr;
            }
            else if (this.skinId > 0) {
                attackAnimTimeStr = ((_c = assetsMgr.getJsonData('pawnSkin', this.skinId)) === null || _c === void 0 ? void 0 : _c.attack_anim_time) || attackAnimTimeStr;
            }
            this.tempAttackAnimTimes = attackAnimTimeStr.split('|').map(function (m) { return ut.stringToNumbers(m, ','); });
        }
        return this.tempAttackAnimTimes;
    };
    // 记录当前的血量 用于打开士兵面板时 切换装备的时候可以记录一开始的血量
    PawnObj.prototype.recordCurrHp = function (val) {
        this.tempCurrHp = val ? this.curHp : -1;
    };
    PawnObj.prototype.getViewId = function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) || this.skinId || this.id; };
    PawnObj.prototype.getPrefabUrl = function () { return 'pawn/PAWN_' + this.getViewId(); };
    PawnObj.prototype.getUid = function () { return this.uid; };
    PawnObj.prototype.getAbsUid = function () { return this.uid + this.getViewId(); };
    PawnObj.prototype.getPoint = function () { return this.point; };
    PawnObj.prototype.setPoint = function (point) { this.point.set(point); };
    PawnObj.prototype.getPawnType = function () { return this.type; };
    PawnObj.prototype.getPortrayalSkill = function () { var _a; return (_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.skill; };
    PawnObj.prototype.getPortrayalId = function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) || 0; };
    Object.defineProperty(PawnObj.prototype, "name", {
        get: function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.getChatName()) || 'pawnText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "type", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.type) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "typeName", {
        get: function () { return this.type ? 'ui.pawn_type_' + this.type : ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "marchSpeed", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.march_speed) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "cerealCost", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.cereal_cost) || 1; } //粮耗
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "behaviorId", {
        get: function () { var _a; return (_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.behavior_id; } //行为树配置id
        ,
        enumerable: false,
        configurable: true
    });
    PawnObj.prototype.getAttackRange = function () {
        return this.attackRange + this.getStrategyValue(40101) + this.getStrategyValue(40302) + this.getStrategyValue(50013);
    };
    PawnObj.prototype.getMoveRange = function () {
        return this.moveRange + this.getStrategyValue(31901);
    };
    // 获取移动速度 每秒移动距离
    PawnObj.prototype.getMoveVelocity = function () {
        var _a;
        if (this.portrayal) {
            return this.portrayal.moveVelocity;
        }
        return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.velocity) || 100;
    };
    PawnObj.prototype.getAttackText = function () {
        var attack = this.amendAttack(this.attack) + '';
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (skill) {
            var maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
            return attack + '-' + this.amendAttack(maxAttack);
        }
        return attack;
    };
    PawnObj.prototype.getAttackTextByIndex = function (index) {
        var minAttack = this.amendAttack(this.attack);
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (!skill) {
            return minAttack + '';
        }
        var maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
        maxAttack = this.amendAttack(maxAttack);
        minAttack += (Math.round((maxAttack - minAttack) / 3) * index + 1);
        return minAttack + '-' + maxAttack;
    };
    // 获取最大攻击力 目前只用于陌刀
    PawnObj.prototype.getInstabilityMaxAttack = function () {
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (!skill) {
            return this.attack;
        }
        var attack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
        return this.amendAttack(attack);
    };
    PawnObj.prototype.amendAttack = function (attack, ignoreBuffType) {
        var _this = this;
        var addAttackRatio = 0, lowAttackRatio = 0;
        this.buffs.forEach(function (m) {
            var _a, _b, _c, _d, _e;
            if (m.type === ignoreBuffType) {
                return;
            }
            else if (m.type === Enums_1.BuffType.ATTACK_HALO || m.type === Enums_1.BuffType.LV_1_POWER || m.type === Enums_1.BuffType.LONGYUAN_SWORD_ATTACK) {
                addAttackRatio += (m.value * 0.01); //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || m.type === Enums_1.BuffType.KUROU_ADD_ATTACK) {
                addAttackRatio += m.value; //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.ADD_ATTACK || m.type === Enums_1.BuffType.INSPIRE || m.type === Enums_1.BuffType.WORTHY_MONARCH || m.type === Enums_1.BuffType.ADD_EXECUTE_ATTACK || m.type === Enums_1.BuffType.KILL_ADD_ATTACK || m.type === Enums_1.BuffType.GOD_WAR || m.type === Enums_1.BuffType.DDIE_ADD_ATTACK) {
                attack += m.value; //增加攻击力
            }
            else if (m.type === Enums_1.BuffType.WISDOM_COURAGE) { //姜维 智勇
                if (((_b = (_a = _this.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.JIANG_WEI) {
                    attack += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.VALOR) { //李嗣业 勇猛
                if (((_d = (_c = _this.portrayal) === null || _c === void 0 ? void 0 : _c.skill) === null || _d === void 0 ? void 0 : _d.id) === Enums_1.HeroType.LI_SIYE) {
                    attack += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.MORALE) { //曹操 士气
                var v = ((_e = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_CAO)) === null || _e === void 0 ? void 0 : _e.target) || 0;
                attack += (m.value * v);
            }
            else if (m.type === Enums_1.BuffType.TIGER_MANIA) { //许褚 虎痴
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.DESTROY_WEAPONS) { //摧毁武器 降低攻击力
                lowAttackRatio += m.value;
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK_S) { //韬略 加攻击力
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.THOUSAND_UMBRELLA) { //千机伞
                var effect = _this.getEquipEffectByType(Enums_1.EquipEffectType.THOUSAND_UMBRELLA);
                if (effect) {
                    var val = m.value === 0 ? effect.value * 2 : effect.value;
                    addAttackRatio += (val * 0.01);
                }
            }
            else if (m.type === Enums_1.BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //养由基 投喂强化
                addAttackRatio += (m.value * 0.06);
            }
            else if (m.type === Enums_1.BuffType.COURAGEOUSLY) { //典韦 奋勇
                attack += m.value;
            }
            else if (m.type === Enums_1.BuffType.KERIAN) { //辛弃疾 金戈
                addAttackRatio += (m.value * 0.01);
            }
        });
        // 韬略
        for (var key in this.strategyBuffMap) {
            var s = this.strategyBuffMap[key];
            if (s.type === 20001 || s.type === 40301 || s.type === 50010) {
                attack += s.value;
            }
            else if (s.type === 20003 || s.type === 50029) {
                addAttackRatio += (s.value * 0.01);
            }
        }
        // 提升比列
        if (addAttackRatio > 0) {
            attack += Math.round(attack * addAttackRatio);
        }
        // 降低比列
        if (lowAttackRatio > 0) {
            attack = Math.max(0, attack - Math.round(attack * lowAttackRatio));
        }
        return attack;
    };
    PawnObj.prototype.getInitMaxHp = function () {
        return this.maxHp;
    };
    PawnObj.prototype.getMaxHp = function () {
        return this.amendMaxHp();
    };
    PawnObj.prototype.amendMaxHp = function (ignoreBuffType) {
        var _this = this;
        if (ignoreBuffType === void 0) { ignoreBuffType = Enums_1.BuffType.NONE; }
        var hp = this.maxHp;
        var addHpRatio = 0, lowHpRatio = 0;
        this.buffs.forEach(function (m) {
            var _a, _b, _c;
            if (m.type === ignoreBuffType) {
                return;
            }
            else if (m.type === Enums_1.BuffType.DEFEND_HALO || m.type === Enums_1.BuffType.LV_1_POWER) {
                addHpRatio += (m.value * 0.01); //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.ADD_MAX_HP) {
                hp += m.value;
            }
            else if (m.type === Enums_1.BuffType.MORALE) { //曹操 士气
                var v = ((_a = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_CAO)) === null || _a === void 0 ? void 0 : _a.params) || 0;
                hp += (m.value * v);
            }
            else if (m.type === Enums_1.BuffType.TOUGH) { //曹仁 坚韧
                if (((_c = (_b = _this.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.CAO_REN) {
                    hp += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵
                addHpRatio += 0.1;
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //养由基 投喂强化
                addHpRatio += (m.value * 0.04);
            }
            else if (m.type === Enums_1.BuffType.TYRANNICAL) { //董卓 暴虐
                lowHpRatio += (m.value * 0.04);
            }
        });
        // 韬略
        for (var key in this.strategyBuffMap) {
            var s = this.strategyBuffMap[key];
            if (s.type === 20002) {
                hp += s.value;
            }
            else if (s.type === 50010) {
                hp += s.params;
            }
            else if (s.type === 20004) {
                addHpRatio += (s.value * 0.01);
            }
        }
        // 提升比列
        if (addHpRatio > 0) {
            hp += Math.round(hp * addHpRatio);
        }
        // 降低比列
        if (lowHpRatio > 0) {
            hp = Math.max(1, hp - Math.round(hp * lowHpRatio));
        }
        return hp;
    };
    PawnObj.prototype.getCadetLvText = function () {
        var _a;
        if (this.rodeleroCadetLv < 0) {
            return '0';
        }
        var actLv = ((_a = GameHelper_1.gameHpr.getPlayerInfo(this.owner || GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.rodeleroCadetLv) || 0;
        if (!this.uid) {
            return actLv;
        }
        var curLv = this.rodeleroCadetLv;
        if (actLv > curLv) {
            return curLv + "(+" + (actLv - curLv) + ")";
        }
        return actLv + '';
    };
    PawnObj.prototype.getPetId = function () {
        return this.petId;
    };
    PawnObj.prototype.setPetId = function (id) {
        this.petId = id;
    };
    // 改变军队
    PawnObj.prototype.changeArmy = function (data) {
        this.armyUid = data.uid;
        this.armyName = data.name;
    };
    // 是否英雄
    PawnObj.prototype.isHero = function () { return !!this.portrayal; };
    // 是否boss
    PawnObj.prototype.isBoss = function () { return (this.type === Enums_1.PawnType.BEAST || this.type === Enums_1.PawnType.CATERAN) && this.attrJson.move_range === 0; };
    // 是否器械
    PawnObj.prototype.isMachine = function () { return this.type === Enums_1.PawnType.MACHINE; };
    // 是否建筑
    PawnObj.prototype.isBuilding = function () { return this.type === Enums_1.PawnType.BUILD; };
    // 是否非战斗单位
    PawnObj.prototype.isNoncombat = function () { return this.type === Enums_1.PawnType.NONCOMBAT; };
    // 获取血条预制体url
    PawnObj.prototype.getHPBarPrefabUrl = function () {
        if (this.isHero()) {
            return 'pawn/HERO_HP_BAR';
        }
        else if (this.isBoss()) {
            return 'pawn/BOSS_HP_BAR';
        }
        return 'pawn/HP_BAR';
    };
    // 是否可以穿装备
    PawnObj.prototype.isCanWearEquip = function () {
        var _a;
        return (this.type < Enums_1.PawnType.MACHINE || this.isBoss()) && (!!this.owner || !!((_a = this.equip) === null || _a === void 0 ? void 0 : _a.id));
    };
    // 是否满级
    PawnObj.prototype.isMaxLv = function () {
        var _a;
        return !((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.lv_cost);
    };
    // 是否自己的士兵
    PawnObj.prototype.isOwner = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否战斗中
    PawnObj.prototype.isBattleing = function () {
        var _a;
        return ((_a = this.state) === null || _a === void 0 ? void 0 : _a.type) >= Enums_1.PawnState.STAND || this.aIndex < 0;
    };
    // 获取战斗阵营
    PawnObj.prototype.getBattleCamp = function () {
        var _a, _b, _c;
        var ctrl = (_b = (_a = GameHelper_1.gameHpr.areaCenter.getArea(this.aIndex)) === null || _a === void 0 ? void 0 : _a.getFspModel()) === null || _b === void 0 ? void 0 : _b.getBattleController();
        return (_c = ctrl === null || ctrl === void 0 ? void 0 : ctrl.getFighterCampIndex(this.uid)) !== null && _c !== void 0 ? _c : -1;
    };
    PawnObj.prototype.getHpText = function () {
        var maxHp = this.getMaxHp();
        var curHp = this.uid ? this.curHp : maxHp;
        return curHp + '/' + maxHp;
    };
    PawnObj.prototype.getHpRatio = function () {
        var maxHp = this.getMaxHp();
        return maxHp > 0 ? this.curHp / maxHp : 0;
    };
    PawnObj.prototype.getMaxAnger = function () {
        var _a;
        if (this.maxAnger === 0) {
            return 0;
        }
        var maxAnger = this.maxAnger;
        // 吕蒙 -50%怒气
        if (((_a = this.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.LV_MENG) {
            maxAnger = Math.round(this.maxAnger * 0.5);
        }
        // 韬略 怒气-1
        if (this.isHasStrategys(40102, 40201, 40303, 40401)) {
            maxAnger = Math.max(1, maxAnger - 1);
        }
        return maxAnger;
    };
    PawnObj.prototype.getCurAnger = function () {
        return this.curAnger;
    };
    PawnObj.prototype.getAngerText = function () {
        return this.maxAnger > 0 ? this.curAnger + '/' + this.getMaxAnger() : '0/0';
    };
    PawnObj.prototype.getAngerRatio = function () {
        return this.maxAnger > 0 ? this.curAnger / this.getMaxAnger() : 0;
    };
    PawnObj.prototype.isDie = function () {
        return this.curHp <= 0 && this.maxHp > 0;
    };
    PawnObj.prototype.getState = function () {
        var _a;
        return ((_a = this.state) === null || _a === void 0 ? void 0 : _a.type) || Enums_1.PawnState.NONE;
    };
    // 改变状态
    PawnObj.prototype.changeState = function (state, data, init) {
        if (!this.state) {
            this.state = new PawnStateObj_1.default();
        }
        this.state.init(state, data);
        // cc.log('changeState', this.uid, this.point.ID(), PawnState[state], data)
        // 非战斗状态
        if (!init && !this.isBattleing()) {
            this.initAnger();
            this.cleanAllBuffs();
            this.tempAttackAnimTimes = null;
        }
    };
    // 设置画像
    PawnObj.prototype.setPortrayal = function (data) {
        var _a;
        if (((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) !== data.id) {
            this.skinId = 0; //有画像就一定没有皮肤
            this.portrayal = new PortrayalInfo_1.default().fromSvr(data);
            this.updateAttr();
            this.initAnger();
            eventCenter.emit(EventType_1.default.CHANGE_PAWN_PORTRAYAL, this);
        }
    };
    // 改变攻击速度
    PawnObj.prototype.changeAttackSpeed = function (val) {
        var num = this.attackSpeed + val;
        if (num < 1) {
            num = 9;
        }
        else if (num > 9) {
            num = 1;
        }
        this.attackSpeed = num;
    };
    PawnObj.prototype.setAttackSpeed = function (val) {
        this.attackSpeed = val;
    };
    PawnObj.prototype.changeSkin = function (skinId) {
        if (this.skinId !== skinId) {
            this.skinId = skinId;
            eventCenter.emit(EventType_1.default.CHANGE_PAWN_SKIN, this);
        }
    };
    // 切换装备
    PawnObj.prototype.changeEquip = function (data, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (this.equip.uid !== data.uid) {
            this.equip.setId(data.uid, data.id);
            this.updateEquipAttr(this.equip.uid, data.attrs);
            isEmit && eventCenter.emit(EventType_1.default.CHANGE_PAWN_EQUIP, this);
        }
    };
    // 刷新装备信息
    PawnObj.prototype.updateEquipAttr = function (uid, attrs) {
        if (!this.equip.uid || !attrs) {
            this.equip.setAttr([]);
        }
        else if (this.equip.uid === uid) {
            // 如果是专属 检测自己是否可以携带
            if (!this.equip.isExclusive() || this.equip.checkExclusivePawn(this.id)) {
                this.equip.setAttr(attrs);
            }
            else {
                this.equip.clean();
            }
        }
        this.updateAttr();
    };
    // 刷新英雄信息
    PawnObj.prototype.updateHeroAttr = function (id, attrs) {
        var _a;
        if (((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) !== id || !attrs) {
            return;
        }
        this.portrayal.setAttr(attrs);
        this.updateAttr();
    };
    // 刷新属性 这个只是前端模拟 还是需要以服务器数据为准
    // 一般如果在场景没有战斗并改变属性的时候可以前端自行计算
    PawnObj.prototype.updateAttr = function (init) {
        var _this = this;
        var _a, _b, _c, _d;
        if (!init && this.isBattleing()) {
            return;
        }
        var maxHp = this.attrJson.hp || 0, attack = this.attrJson.attack || 0, attackRange = this.attrJson.attack_range || 0, moveRange = this.attrJson.move_range || 0;
        this.maxHp = maxHp;
        this.attack = attack;
        this.attackRange = attackRange;
        this.moveRange = moveRange;
        var runDay = GameHelper_1.gameHpr.getServerRunDay();
        var addHpRatio = 0, addAttackRatio = 0;
        // 加上装备的
        this.getEquipEffects().forEach(function (m) {
            if (m.type === Enums_1.EquipEffectType.MINGGUANG_ARMOR) { //提高生命
                _this.maxHp += m.value * 10;
            }
            else if (m.type === Enums_1.EquipEffectType.BAIBI_SWORD) { //提高攻击
                _this.attack += m.value;
            }
            else if (m.type === Enums_1.EquipEffectType.TODAY_ADD_HP) { //根据服务器运行时间加生命
                _this.maxHp += m.value * runDay;
            }
            else if (m.type === Enums_1.EquipEffectType.TODAY_ADD_ATTACK) { //根据服务器运行时间加攻击
                _this.attack += m.value * runDay;
            }
            else if (m.type === Enums_1.EquipEffectType.CENTERING_HELMET) { //提高生命比例
                addHpRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.EquipEffectType.LONGYUAN_SWORD) { //提高攻击比例
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.EquipEffectType.NOT_DODGE) { //攻击范围 +1
                _this.attackRange += 1;
            }
            else if (m.type === Enums_1.EquipEffectType.ADD_MOVE_RANGE) { //移动范围 +1
                _this.moveRange += 1;
            }
        });
        this.maxHp += this.equip.hp;
        this.attack += this.equip.attack;
        // 画像属性
        if (this.portrayal) {
            this.maxHp += this.portrayal.hp;
            this.attack += this.portrayal.attack;
            // 裴行俨 自带攻击力
            if (((_a = this.portrayal.skill) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.PEI_XINGYAN) {
                addAttackRatio += this.portrayal.skill.value * 0.01;
            }
        }
        // 见习勇者
        if (this.rodeleroCadetLv > 0) {
            var max = ((_b = this.getSkillByType(Enums_1.PawnSkillType.CADET)) === null || _b === void 0 ? void 0 : _b.value) || 0;
            var cadetLv = Math.min(this.rodeleroCadetLv, max);
            this.maxHp += cadetLv * 5;
            this.attack += Math.round(cadetLv * 0.5);
        }
        // 生命 提升比列
        if (addHpRatio > 0) {
            this.maxHp += Math.round(this.maxHp * addHpRatio);
        }
        // 攻击 提升比例
        if (addAttackRatio > 0) {
            this.attack += Math.round(this.attack * addAttackRatio);
        }
        // 是否有技能强化
        if (this.equip.skillIntensify) {
            var _e = __read(this.equip.skillIntensify, 2), id_1 = _e[0], type = _e[1];
            (_c = this.skills.find(function (m) { return m.baseId === id_1; })) === null || _c === void 0 ? void 0 : _c.setIntensifyType(type);
        }
        else {
            this.skills.forEach(function (m) { return m.setIntensifyType(0); });
        }
        // 如果是主城或配置士兵直接满血
        if ((_d = GameHelper_1.gameHpr.world.getMapCellByIndex(this.aIndex)) === null || _d === void 0 ? void 0 : _d.isRecoverPawnHP()) {
            this.curHp = this.getMaxHp();
        }
        else if (this.tempCurrHp >= 0) {
            this.curHp = Math.min(this.tempCurrHp, this.getMaxHp());
        }
        else {
            this.curHp = Math.min(this.curHp, this.getMaxHp());
        }
    };
    // 获取技能信息
    PawnObj.prototype.getSkillByType = function (type) {
        return this.skills.find(function (m) { return m.type === type; });
    };
    // 获取主动技能
    PawnObj.prototype.getActiveSkill = function () {
        return this.skills.find(function (m) { return m.use_type === 1 || m.type === Enums_1.PawnSkillType.FULL_STRING || m.type === Enums_1.PawnSkillType.LONGITUDINAL_CLEFT; }); //目前满弦、顺劈也算主动
    };
    PawnObj.prototype.setBuffs = function (buffs) {
        this.buffs = buffs.map(function (m) { return new BuffObj_1.default().fromSvr(m); });
    };
    PawnObj.prototype.addBuffs = function (buffs) {
        var _this = this;
        if (buffs === null || buffs === void 0 ? void 0 : buffs.length) {
            var buffMap_1 = {};
            this.buffs.forEach(function (m) { return buffMap_1[m.type] = m; });
            buffs.forEach(function (m) {
                var buff = buffMap_1[m.type];
                if (buff) {
                    buff.fromSvr(m);
                }
                else {
                    _this.buffs.push(new BuffObj_1.default().fromSvr(m));
                }
            });
        }
    };
    PawnObj.prototype.isHasBuff = function (type) {
        return this.buffs.some(function (m) { return m.type === type; });
    };
    PawnObj.prototype.getBuffValue = function (type) {
        var _a;
        return ((_a = this.buffs.find(function (m) { return m.type === type; })) === null || _a === void 0 ? void 0 : _a.value) || 0;
    };
    PawnObj.prototype.cleanAllBuffs = function () {
        this.cleanBuffs();
        this.cleanStrategyBuffs();
    };
    PawnObj.prototype.cleanBuffs = function () {
        this.buffs.length = 0;
    };
    // 获取护盾值
    PawnObj.prototype.getShieldValue = function () {
        var val = 0;
        this.buffs.forEach(function (m) {
            if (m.isHasShield()) {
                val += m.value;
            }
        });
        return val;
    };
    PawnObj.prototype.cleanStrategyBuffs = function () {
        this.strategyBuffMap = {};
    };
    PawnObj.prototype.addStrategyBuff = function (strategy) {
        this.strategyBuffMap[strategy.type] = strategy;
    };
    PawnObj.prototype.getStrategyBuff = function (tp) {
        return this.strategyBuffMap[tp];
    };
    // 获取韬略数值
    PawnObj.prototype.getStrategyValue = function (tp) {
        var _a;
        return ((_a = this.getStrategyBuff(tp)) === null || _a === void 0 ? void 0 : _a.value) || 0;
    };
    // 是否有某个韬略
    PawnObj.prototype.isHasStrategys = function () {
        var _this = this;
        var tps = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            tps[_i] = arguments[_i];
        }
        return tps.some(function (m) { return !!_this.strategyBuffMap[m]; });
    };
    PawnObj.prototype.isHasStrategy = function () {
        return !ut.isEmptyObject(this.strategyBuffMap);
    };
    PawnObj.prototype.initAnger = function () {
        var _a, _b;
        this.curAnger = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.init_anger) || 0;
        if (this.curAnger > 0) {
            // 吕蒙
            if (((_b = this.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.LV_MENG) {
                this.curAnger = Math.round(this.curAnger * 0.5);
            }
        }
        return this;
    };
    PawnObj.prototype.isHasAnger = function () {
        return this.maxAnger > 0;
    };
    // 获取装备效果列表
    PawnObj.prototype.getEquipEffects = function () {
        var _a;
        return (_a = this.equip) === null || _a === void 0 ? void 0 : _a.effects;
    };
    // 获取某个装备效果
    PawnObj.prototype.getEquipEffectByType = function (type) {
        var _a;
        return (_a = this.equip) === null || _a === void 0 ? void 0 : _a.effects.find(function (m) { return m.type === type; });
    };
    // 刷新宝箱
    PawnObj.prototype.updateTreasures = function (treasures) {
        var _this = this;
        this.treasures = (treasures || []).map(function (m) { return GameHelper_1.gameHpr.fromSvrTreasureInfo(m, _this.aIndex, _this.armyUid, _this.uid); });
    };
    return PawnObj;
}());
exports.default = PawnObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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