
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/PlayerModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '770fe0z1u5PoKxie/Onbh/c', 'PlayerModel');
// app/script/model/main/PlayerModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ProtoHelper_1 = require("../../../proto/ProtoHelper");
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var DBHelper_1 = require("../../common/helper/DBHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var MerchantObj_1 = require("../bazaar/MerchantObj");
var BTInfoObj_1 = require("./BTInfoObj");
var CTypeObj_1 = require("../common/CTypeObj");
var EquipInfo_1 = require("./EquipInfo");
var ForgeEquipInfo_1 = require("./ForgeEquipInfo");
var OutputObj_1 = require("./OutputObj");
var PawnDrillInfoObj_1 = require("./PawnDrillInfoObj");
var PawnLevelingInfoObj_1 = require("./PawnLevelingInfoObj");
var PolicyObj_1 = require("./PolicyObj");
var SmeltEquipInfo_1 = require("./SmeltEquipInfo");
var TaskObj_1 = require("../common/TaskObj");
var GuideConfig_1 = require("../guide/GuideConfig");
var HeroSlotObj_1 = require("./HeroSlotObj");
var PawnCureInfoObj_1 = require("./PawnCureInfoObj");
var EquipSlotObj_1 = require("./EquipSlotObj");
var PawnSlotObj_1 = require("./PawnSlotObj");
/**
 * 玩家
 */
var PlayerModel = /** @class */ (function (_super) {
    __extends(PlayerModel, _super);
    function PlayerModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.user = null;
        _this.alliance = null;
        _this.initTime = 0; //初始化时间
        _this.cereal = null; //粮
        _this.timber = null; //木
        _this.stone = null; //石
        _this.expBook = 0; //经验书
        _this.iron = 0; //铁
        _this.upScroll = 0; //卷轴
        _this.fixator = 0; //固定器
        _this.cerealConsume = 0; //当前粮耗
        _this.granaryCap = 0; //粮仓容量
        _this.warehouseCap = 0; //仓库容量
        _this.stamina = 0; //当前奖励点
        _this.captureInfo = null; //被攻陷的信息
        _this.sumOnlineTime = 0; //累计在线时间
        _this.mainCityIndex = 0; //主城所在位置
        _this.mainBuilds = []; //主城当前的建筑列表
        _this.unlockPawnIds = []; //当前额外解锁的兵种列表
        _this.unlockEquipIds = []; //当前额外解锁的装备列表
        _this.btQueues = []; //当前的建造队列
        _this.pawnDrillQueueMap = new Map(); //士兵训练队列
        _this.pawnLevelingQueues = []; //士兵练级队列
        _this.baseArmys = []; //临时的军队列表
        _this.armyDistMap = {};
        _this.merchants = []; //商人列表
        _this.guideTasks = []; //新手任务列表
        _this.todayTasks = []; //每日任务列表
        _this.otherTasks = []; //其他任务列表
        _this.equips = []; //已有的装备列表
        _this.currForgeEquip = null; //当前打造装备信息
        _this.currSmeltEquip = null; //当前正在融炼得装备信息
        _this.configPawnMap = {}; //配置士兵的信息
        _this.citySkinConfigMap = {}; //城市皮肤配置的信息
        _this.fortAutoSupports = []; //要塞自动支援配置
        _this.addOutputSurplusTime = {}; //添加产量剩余时间
        _this.getAddOutputTime = 0;
        _this.policySlots = {}; //当前政策槽位列表
        _this.equipSlots = {}; //当前装备槽位列表
        _this.pawnSlots = {}; //当前士兵槽位列表
        _this.heroSlots = []; //当前英雄槽位信息
        _this.hidePChatChannels = {}; //隐藏的私聊频道
        _this.exitAllianceCount = 0; //退出联盟次数
        _this.todayOccupyCellCount = 0; //每日打地数量
        _this.accTotalGiveResCount = 0; //累计赠送资源数量
        _this.todayReplacementCount = 0; //每日置换次数
        _this.landScore = 0; //领地积分
        _this.occupyLandCountMap = {}; //历史攻占野地数量 key=地块等级 val=数量
        _this.maxOccupyLandDifficulty = 0; //历史最大攻占野地难度
        _this.killRecordMap = {}; //击杀数量 key=id val=数量
        _this.mapMarks = []; //地图标记
        _this.reCreateMainCityCount = 0; //重新创建主城次数
        _this.cellTondenCount = 0; //每日屯田次数
        _this.upRecruitPawnCount = 0; //加速招募士兵数量
        _this.freeRecruitPawnCount = 0; //免费招募士兵数量
        _this.freeLevingPawnCount = 0; //免费训练士兵数量
        _this.freeCurePawnCount = 0; //免费治疗士兵数量
        _this.freeForgeCount = 0; //免费打造/重铸数量
        _this.isSettled = false; //是否已结算（只有血战到底模式有该值）
        _this.armyMaxCount = 0; //军队最大数量
        _this.mainCityRect = { min: cc.v2(), max: cc.v2() };
        _this.injuryPawns = []; // 可治疗的伤兵
        _this.curingPawns = []; // 治疗中的伤兵
        _this.lastReqSelectArmysTime = 0; //最后一次请求军队时间
        _this.tempSelectArmyErr = '';
        _this.tempSelectArmyList = []; //临时的选择军队信息
        _this.tempSelectArmyCanGotoCount = 0; //临时的选择军队 可前往数量
        _this.lastReqArmysTime = 0; //最后一次请求军队时间
        _this.tempArmyList = []; //临时的军队信息
        _this.lastReqArmyMarchRecordTime = 0; //最后一次请求军队记录时间
        _this.tempArmyMarchRecordList = []; //军队记录列表
        _this.lastReqArmyBattleRecordTime = 0; //最后一次请求军队记录时间
        _this.tempArmyBattleRecordList = []; //军队记录列表
        _this.lastReqBazaarRecordTime = 0; //最后一次请求市场记录时间
        _this.tempBazaarRecordList = []; //市场记录列表
        _this.tempBattleForecastRets = []; //临时记录玩家战斗预测结果
        _this.tempStopPlayMessageSound = {}; //临时需要停止播放的音效
        _this.tempCanForgeEquips = null;
        _this.tempCanRecruitPawns = {};
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        return _this;
    }
    PlayerModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.user = this.getModel('user');
        this.alliance = this.getModel('alliance');
    };
    // 初始化信息
    PlayerModel.prototype.init = function (data, isLocal) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        cc.log('init player', data);
                        this.resetTempInfo();
                        this.lastReqSelectArmysTime = 0;
                        this.lastReqArmysTime = 0;
                        this.lastReqArmyMarchRecordTime = 0;
                        this.lastReqArmyBattleRecordTime = 0;
                        this.lastReqBazaarRecordTime = 0;
                        this.initTime = Date.now();
                        this.sumOnlineTime = data.sumOnlineTime || 0;
                        this.setCaptureInfo(data.captureInfo);
                        this.expBook = data.expBook || 0;
                        this.iron = data.iron || 0;
                        this.upScroll = data.upScroll || 0;
                        this.fixator = data.fixator || 0;
                        this.granaryCap = data.granaryCap || 1;
                        this.warehouseCap = data.warehouseCap || 1;
                        this.cereal = new OutputObj_1.default(EventType_1.default.UPDATE_CEREAL).fromSvr(data.cereal);
                        this.timber = new OutputObj_1.default(EventType_1.default.UPDATE_TIMBER).fromSvr(data.timber);
                        this.stone = new OutputObj_1.default(EventType_1.default.UPDATE_STONE).fromSvr(data.stone);
                        this.cerealConsume = data.cerealConsume || 0;
                        this.stamina = data.stamina || 0;
                        this.mainCityIndex = data.mainCityIndex || 0;
                        this.mainBuilds = data.builds || [];
                        this.unlockPawnIds = data.unlockPawnIds || [];
                        this.unlockEquipIds = data.unlockEquipIds || [];
                        this.updateBtQueue(data.btQueues || [], false);
                        this.updatePawnDrillQueue(data.pawnDrillQueues || {}, false);
                        this.updatePawnLevelingQueue(data.pawnLevelingQueues || [], false);
                        this.updatePawnCuringQueue(data.curingQueues || [], false);
                        this.updateArmyDists(data.armyDists || [], false);
                        this.merchants = (data.merchants || []).map(function (m) { return new MerchantObj_1.default().fromSvr(m); });
                        this.equips = (data.equips || []).map(function (m) { return new EquipInfo_1.default().fromSvr(m); });
                        this.configPawnMap = data.configPawnMap || {};
                        this.citySkinConfigMap = data.citySkinConfigMap || {};
                        this.fortAutoSupports = data.fortAutoSupports || [];
                        this.updateAddOutputTime(data.addOutputSurplusTime);
                        this.updatePolicySlots(data.policySlots || {}, false);
                        this.updatePawnSlots(data.pawnSlots || {}, false);
                        this.updateEquipSlots(data.equipSlots || {}, false);
                        this.updateCurrForgeEquip(data.currForgeEquip);
                        this.updateCurrSmeltEquip(data.currSmeltEquip);
                        this.heroSlots = (data.heroSlots || []).map(function (m) { return new HeroSlotObj_1.default().fromSvr(m); });
                        this.hidePChatChannels = data.hidePChatChannels || {};
                        this.exitAllianceCount = data.exitAllianceCount || 0;
                        this.updateArmyMaxCount(); //刷新军队最大数量
                        this.todayOccupyCellCount = data.todayOccupyCellCount || 0;
                        this.accTotalGiveResCount = data.accTotalGiveResCount || 0;
                        this.todayReplacementCount = data.todayReplacementCount || 0;
                        this.landScore = data.landScore || 0;
                        this.updateOccupyLandCountMap(data.occupyLandCountMap || {});
                        this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1;
                        this.killRecordMap = data.killRecordMap || {};
                        this.mapMarks = (data.mapMarks || []).map(function (m) { return { name: m.name, point: cc.v2(m.point) }; });
                        this.reCreateMainCityCount = data.reCreateMainCityCount || 0;
                        this.cellTondenCount = data.cellTondenCount || 0;
                        this.injuryPawns = data.injuryPawns || [];
                        this.upRecruitPawnCount = data.upRecruitPawnCount || 0;
                        this.freeRecruitPawnCount = data.freeRecruitPawnCount || 0;
                        this.freeLevingPawnCount = data.freeLevingPawnCount || 0;
                        this.freeCurePawnCount = data.freeCurePawnCount || 0;
                        this.freeForgeCount = data.freeForgeCount || 0;
                        this.isSettled = !!data.isSettled;
                        this.updateGuideTasks(data.guideTasks || [], false);
                        this.updateTodayTasks(data.todayTasks || [], false); //需要放到这个位置 因为需要前面的todayOccupyCellCount
                        this.updateOtherTasks(data.otherTasks || [], false);
                        // 主城的矩形区域
                        this.updateMainCityRect(this.mainCityIndex);
                        if (!!isLocal) return [3 /*break*/, 2];
                        // 获取联盟信息
                        return [4 /*yield*/, this.alliance.init(data.allianceUid || '')];
                    case 1:
                        // 获取联盟信息
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        // 宝箱
                        ReddotHelper_1.reddotHelper.set('treasure_main', !!data.hasNewTreasure);
                        if (this.guideTasks.length > 0) {
                            ReddotHelper_1.reddotHelper.unregister('guide_task');
                            ReddotHelper_1.reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1);
                        }
                        // 监听消息
                        this.net.on('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this);
                        return [2 /*return*/];
                }
            });
        });
    };
    PlayerModel.prototype.clean = function () {
        this.net.off('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this);
        this.tempBattleForecastRets = [];
        this.tempArmyList = [];
        this.mainBuilds = [];
        this.guideTasks = [];
        this.todayTasks = [];
        this.otherTasks = [];
        this.heroSlots = [];
        this.baseArmys = [];
        this.pawnDrillQueueMap.clear();
        this.pawnLevelingQueues = [];
        this.tempSelectArmyList = [];
        this.tempArmyMarchRecordList = [];
        this.tempArmyBattleRecordList = [];
        this.tempBazaarRecordList = [];
        this.resetTempInfo();
    };
    PlayerModel.prototype.resetTempInfo = function () {
        this.tempCanForgeEquips = null;
        this.tempCanRecruitPawns = {};
    };
    PlayerModel.prototype.getInitTIme = function () { return this.initTime; };
    PlayerModel.prototype.getToInitElapsedTime = function () { return Date.now() - this.initTime; }; //到初始化经过的时间
    PlayerModel.prototype.getCaptureInfo = function () { return this.captureInfo; };
    PlayerModel.prototype.isCapture = function () { var _a; return !!this.captureInfo || !((_a = this.mainBuilds) === null || _a === void 0 ? void 0 : _a.length); }; //是否沦陷
    PlayerModel.prototype.getMainCityIndex = function () { return this.mainCityIndex; };
    PlayerModel.prototype.getMainCityPoint = function () { return MapHelper_1.mapHelper.indexToPoint(this.mainCityIndex); };
    PlayerModel.prototype.getAlliance = function () { return this.alliance; };
    PlayerModel.prototype.getAllianceUid = function () { return this.alliance.getUid(); };
    PlayerModel.prototype.isHasAlliance = function () { return !!this.alliance.getUid(); };
    PlayerModel.prototype.getCereal = function () { var _a; return ((_a = this.cereal) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getCerealOp = function () { var _a; return ((_a = this.cereal) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setCereal = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.cereal.set(val, isEmit);
    };
    PlayerModel.prototype.changeCereal = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.cereal.change(val, isEmit);
    };
    PlayerModel.prototype.getTimber = function () { var _a; return ((_a = this.timber) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getTimberOp = function () { var _a; return ((_a = this.timber) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setTimber = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.timber.set(val, isEmit);
    };
    PlayerModel.prototype.changeTimber = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.timber.change(val, isEmit);
    };
    PlayerModel.prototype.getStone = function () { var _a; return ((_a = this.stone) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getStoneOp = function () { var _a; return ((_a = this.stone) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setStone = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.stone.set(val, isEmit);
    };
    PlayerModel.prototype.changeStone = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.stone.change(val, isEmit);
    };
    PlayerModel.prototype.getCerealConsume = function () { return this.cerealConsume; };
    PlayerModel.prototype.getCerealCapRatio = function () { return this.getCereal() / this.granaryCap; };
    PlayerModel.prototype.getTimberCapRatio = function () { return this.getTimber() / this.warehouseCap; };
    PlayerModel.prototype.getStoneCapRatio = function () { return this.getStone() / this.warehouseCap; };
    PlayerModel.prototype.getGranaryCap = function () { return this.granaryCap; };
    PlayerModel.prototype.getWarehouseCap = function () { return this.warehouseCap; };
    PlayerModel.prototype.getAddOutputSurplusTime = function () { return this.addOutputSurplusTime; };
    PlayerModel.prototype.getAddOutputElapsedTime = function () { return Date.now() - this.getAddOutputTime; };
    PlayerModel.prototype.getExitAllianceCount = function () { return this.exitAllianceCount; };
    PlayerModel.prototype.setExitAllianceCount = function (val) { this.exitAllianceCount = val; };
    PlayerModel.prototype.getHeroSlots = function () { return this.heroSlots; };
    PlayerModel.prototype.getHidePChatChannels = function () { return this.hidePChatChannels; };
    PlayerModel.prototype.getUnlockPawnIds = function () { return this.unlockPawnIds; };
    PlayerModel.prototype.getUnlockEquipIds = function () { return this.unlockEquipIds; };
    PlayerModel.prototype.getTodayOccupyCellCount = function () { return this.todayOccupyCellCount; };
    PlayerModel.prototype.addTodayOccupyCellCount = function (val) { return this.todayOccupyCellCount += val; };
    PlayerModel.prototype.getAccTotalGiveResCount = function () { return this.accTotalGiveResCount; };
    PlayerModel.prototype.addAccTotalGiveResCount = function (val) { return this.accTotalGiveResCount += val; };
    PlayerModel.prototype.getTodayReplacementCount = function () { return this.todayReplacementCount; };
    PlayerModel.prototype.setTodayReplacementCount = function (val) { this.todayReplacementCount = val; };
    PlayerModel.prototype.getUpRecruitPawnCount = function () { return this.upRecruitPawnCount; };
    PlayerModel.prototype.setUpRecruitPawnCount = function (val) { this.upRecruitPawnCount = val; };
    PlayerModel.prototype.getFreeRecruitPawnCount = function () { return this.freeRecruitPawnCount; };
    PlayerModel.prototype.setFreeRecruitPawnCount = function (val) { this.freeRecruitPawnCount = val; };
    PlayerModel.prototype.getFreeLevingPawnCount = function () { return this.freeLevingPawnCount; };
    PlayerModel.prototype.setFreeLevingPawnCount = function (val) { this.freeLevingPawnCount = val; };
    PlayerModel.prototype.getFreeCurePawnCount = function () { return this.freeCurePawnCount; };
    PlayerModel.prototype.setFreeCurePawnCount = function (val) { this.freeCurePawnCount = val; };
    PlayerModel.prototype.getFreeForgeCount = function () { return this.freeForgeCount; };
    PlayerModel.prototype.setFreeForgeCount = function (val) { this.freeForgeCount = val; };
    PlayerModel.prototype.getMaxOccupyLandDifficulty = function () { return this.maxOccupyLandDifficulty; };
    PlayerModel.prototype.getLandScore = function () { return this.landScore; };
    PlayerModel.prototype.getReCreateMainCityCount = function () { return this.reCreateMainCityCount; };
    PlayerModel.prototype.getCellTondenCount = function () { return this.cellTondenCount; };
    PlayerModel.prototype.getSumOnlineTime = function () {
        return this.sumOnlineTime + (Date.now() - this.initTime);
    };
    // 获取免费招募剩余次数
    PlayerModel.prototype.getFreeRecruitPawnSurplusCount = function () {
        var freeCount = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_DRILL_COUNT);
        if (GameHelper_1.gameHpr.isNoviceMode) {
            freeCount += 9; //新手村给9个
        }
        return Math.max(0, freeCount - this.freeRecruitPawnCount);
    };
    // 获取免费训练士兵剩余次数
    PlayerModel.prototype.getFreeLevingPawnSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_LEVING_COUNT) - this.freeLevingPawnCount);
    };
    // 获取免费治疗士兵剩余次数
    PlayerModel.prototype.getFreeCurePawnSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_FREE_COUNT) - this.freeCurePawnCount);
    };
    // 获取免费免费打造/重铸剩余次数
    PlayerModel.prototype.getfreeForgeSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_RECAST_COUNT) - this.freeForgeCount);
    };
    // 兼容刷新一下固定菜单
    PlayerModel.prototype.updateFixationMenuData = function () {
        var _this = this;
        var ids = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA) || [];
        if (!this.captureInfo) {
            var len = ids.length;
            ids.delete(function (id) { return !_this.mainBuilds.has('id', id); });
            if (ids.length !== len) {
                this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, ids);
            }
        }
        else if (ids.length > 0) {
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, []);
        }
    };
    PlayerModel.prototype.updateMainCityRect = function (index) {
        var pos = MapHelper_1.mapHelper.indexToPoint(index).mul(Constant_1.TILE_SIZE);
        this.mainCityRect.min.x = pos.x;
        this.mainCityRect.min.y = pos.y;
        this.mainCityRect.max.x = pos.x + Constant_1.TILE_SIZE * 2;
        this.mainCityRect.max.y = pos.y + Constant_1.TILE_SIZE * 2;
    };
    // 主城是否不在屏幕范围内
    PlayerModel.prototype.checkMainNotInScreenRange = function () {
        if (this.isCapture() && !GameHelper_1.gameHpr.isSpectate()) {
            return false;
        }
        var outMin = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(this.mainCityRect.min, this._temp_vec2_1);
        var outMax = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(this.mainCityRect.max, this._temp_vec2_2);
        return outMax.x <= 0 || outMax.y <= 0 || outMin.x >= cc.winSize.width || outMin.y >= cc.winSize.height;
    };
    // 经验书
    PlayerModel.prototype.getExpBook = function () { return this.expBook; };
    PlayerModel.prototype.setExpBook = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.expBook);
        this.expBook = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_EXP_BOOK, add);
        }
    };
    // 铁
    PlayerModel.prototype.getIron = function () { return this.iron; };
    PlayerModel.prototype.setIron = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.iron);
        this.iron = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_IRON, add);
        }
    };
    // 卷轴
    PlayerModel.prototype.getUpScroll = function () { return this.upScroll; };
    PlayerModel.prototype.setUpScroll = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.upScroll);
        this.upScroll = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_UPSCROLL, add);
        }
    };
    // 固定器
    PlayerModel.prototype.getFixator = function () { return this.fixator; };
    PlayerModel.prototype.setFixator = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.fixator);
        this.fixator = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_FIXATOR, add);
        }
    };
    // 奖励点
    PlayerModel.prototype.getStamina = function () { return this.stamina; };
    PlayerModel.prototype.setStamina = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.stamina);
        this.stamina = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_STAMINA, add);
        }
    };
    PlayerModel.prototype.setUnlockEquipIds = function (ids) {
        this.unlockEquipIds = ids || [];
        this.tempCanForgeEquips = null; //重新获取
    };
    PlayerModel.prototype.setUnlockPawnIds = function (ids) {
        this.unlockPawnIds = ids || [];
        this.tempCanRecruitPawns = {}; //重新获取
    };
    // 获取修建队列
    PlayerModel.prototype.getBtQueueCount = function () {
        return Constant_1.DEFAULT_BT_QUEUE_COUNT + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.BT_QUEUE);
    };
    // 设置被沦陷了
    PlayerModel.prototype.setCaptureInfo = function (data) {
        if (!(data === null || data === void 0 ? void 0 : data.uid)) {
            this.captureInfo = null;
        }
        else {
            this.captureInfo = data;
            var ids = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA);
            if (ids && ids.length > 0) {
                this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, []);
            }
            // 固定器效果重置
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.LOCK_EQUIP_EFFECT_CONF, {});
        }
    };
    // 主城建筑列表信息
    PlayerModel.prototype.getMainBuilds = function () { return this.mainBuilds; };
    PlayerModel.prototype.updateMainBuildInfo = function (data) {
        GameHelper_1.gameHpr.cleanUnlockBuildCondText(); //清理一下 因为建筑有改变
        var build = this.mainBuilds.find(function (m) { return m.uid === data.uid; });
        if (build) {
            build.lv = data.lv;
        }
        else {
            this.mainBuilds.push(data);
        }
        if (data.id === Enums_1.BUILD_NID.MAIN) {
            this.updateArmyMaxCount();
        }
        this.emit(EventType_1.default.MAIN_BUILD_CHANGE_LV, data);
    };
    // 获取建筑等级
    PlayerModel.prototype.getBuildLv = function (id) {
        var _a;
        return ((_a = this.mainBuilds.find(function (m) { return m.id === id; })) === null || _a === void 0 ? void 0 : _a.lv) || 0;
    };
    PlayerModel.prototype.getMainBuildLv = function () {
        return this.getBuildLv(Enums_1.BUILD_NID.MAIN);
    };
    PlayerModel.prototype.updateArmyMaxCount = function () {
        var _a;
        var lv = this.getMainBuildLv();
        var effect = GameHelper_1.gameHpr.stringToCEffects((_a = assetsMgr.getJsonData('buildAttr', Enums_1.BUILD_NID.MAIN * 1000 + lv)) === null || _a === void 0 ? void 0 : _a.effects)[0];
        this.armyMaxCount = this.isCapture() ? 0 : (effect === null || effect === void 0 ? void 0 : effect.value) || 4;
    };
    // 当前的军队最大数量
    PlayerModel.prototype.getArmyMaxCount = function () {
        return this.armyMaxCount + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.ARMY_COUNT);
    };
    // 军队数量是否满了
    PlayerModel.prototype.isArmyCountFull = function () {
        return this.baseArmys.length >= this.getArmyMaxCount();
    };
    // 军队分布信息
    PlayerModel.prototype.getBaseArmys = function () { return this.baseArmys; };
    PlayerModel.prototype.getArmyDistMap = function () { return this.armyDistMap; };
    PlayerModel.prototype.getDistArmysByIndex = function (index) { return this.armyDistMap[index] || []; };
    PlayerModel.prototype.updateArmyDists = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.armyDistMap = {};
        this.baseArmys.length = 0;
        datas.forEach(function (m) {
            var armys = [];
            m.armys.forEach(function (army) {
                army.index = m.index;
                if (army.state !== Enums_1.ArmyState.MARCH) {
                    armys.push(army);
                }
                _this.baseArmys.push(army);
                _this.updateTempArmyIndex(army);
            });
            if (armys.length > 0) {
                _this.armyDistMap[m.index] = armys;
            }
        });
        isEmit && this.emit(EventType_1.default.UPDATE_ARMY_DIST_INFO);
    };
    // 是否可以刷新产出
    PlayerModel.prototype.isCanUpdateOutput = function () {
        return this.user.getSid() > 0 || GameHelper_1.gameHpr.isNoviceMode;
    };
    // 刷新产出信息
    PlayerModel.prototype.updateOutput = function (data) {
        var _a, _b, _c;
        data = data || {};
        this.granaryCap = (_a = data.granaryCap) !== null && _a !== void 0 ? _a : this.granaryCap;
        this.warehouseCap = (_b = data.warehouseCap) !== null && _b !== void 0 ? _b : this.warehouseCap;
        this.cereal.updateInfo(data.cereal);
        this.timber.updateInfo(data.timber);
        this.stone.updateInfo(data.stone);
        this.cerealConsume = (_c = data.cerealConsume) !== null && _c !== void 0 ? _c : this.cerealConsume;
        // 如果只更新了容量
        if ((data.granaryCap || data.warehouseCap) && (!data.cereal && !data.timber && !data.stone)) {
            this.emit(EventType_1.default.UPDATE_RES_CAP);
        }
    };
    // 刷新产出信息（位标记更新）
    PlayerModel.prototype.updateOutputByFlags = function (data) {
        var _a, _b, _c;
        if (!this.isCanUpdateOutput()) {
            return;
        }
        else if ((data === null || data === void 0 ? void 0 : data.flag) === undefined) {
            return this.updateOutput(data);
        }
        else if (data.flag === 0) {
            return;
        }
        var granaryCapFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.GranaryCap);
        var warehouseCapFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarehouseCap);
        var cerealFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Cereal);
        var timberFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Timber);
        var stoneFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stone);
        var cerealConsumeFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CerealConsume);
        //
        if (granaryCapFlag)
            this.granaryCap = (_a = data.granaryCap) !== null && _a !== void 0 ? _a : this.granaryCap;
        if (warehouseCapFlag)
            this.warehouseCap = (_b = data.warehouseCap) !== null && _b !== void 0 ? _b : this.warehouseCap;
        if (cerealFlag)
            this.cereal.updateInfo(data.cereal);
        if (timberFlag)
            this.timber.updateInfo(data.timber);
        if (stoneFlag)
            this.stone.updateInfo(data.stone);
        if (cerealConsumeFlag)
            this.cerealConsume = (_c = data.cerealConsume) !== null && _c !== void 0 ? _c : this.cerealConsume;
        // 如果只更新了容量
        if ((granaryCapFlag || warehouseCapFlag) && (!cerealFlag && !timberFlag && !stoneFlag)) {
            this.emit(EventType_1.default.UPDATE_RES_CAP);
        }
    };
    // 刷新奖励信息
    PlayerModel.prototype.updateRewardItems = function (data) {
        if (data) {
            this.user.setGold(data.gold);
            this.user.setIngot(data.ingot);
            this.user.setWarToken(data.warToken);
            this.user.setTitles(data.titles);
            this.updateOutputByFlags(data);
            if (this.isCanUpdateOutput()) {
                this.setExpBook(data.expBook);
                this.setIron(data.iron);
                this.setUpScroll(data.upScroll);
                this.setFixator(data.fixator);
                this.setStamina(data.stamina);
                this.setUpRecruitPawnCount(data.upRecruitPawnCount);
            }
        }
    };
    // 刷新奖励信息（位标记更新）
    PlayerModel.prototype.updateRewardItemsByFlags = function (data) {
        if ((data === null || data === void 0 ? void 0 : data.flag) === undefined) {
            return this.updateRewardItems(data);
        }
        else if (data.flag) {
            this.updateOutputByFlags(data);
            if (this.isCanUpdateOutput()) {
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ExpBook))
                    this.setExpBook(data.expBook);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Iron))
                    this.setIron(data.iron);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpScroll))
                    this.setUpScroll(data.upScroll);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Fixator))
                    this.setFixator(data.fixator);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Equip))
                    this.setUnlockEquipIds(data.unlockEquipIds);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Pawn))
                    this.setUnlockPawnIds(data.unlockPawnIds);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stamina))
                    this.setStamina(data.stamina);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpRecruit))
                    this.setUpRecruitPawnCount(data.upRecruitPawnCount);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeRecruit))
                    this.setFreeRecruitPawnCount(data.freeRecruitPawnCount); //免费招募
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeLeving))
                    this.setFreeLevingPawnCount(data.freeLevingPawnCount); //免费训练
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeCure))
                    this.setFreeCurePawnCount(data.freeCurePawnCount); //免费治疗
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeForge))
                    this.setFreeForgeCount(data.freeForgeCount); //免费打造
            }
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Gold))
                this.user.setGold(data.gold);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Ingot))
                this.user.setIngot(data.ingot);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarToken))
                this.user.setWarToken(data.warToken);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Title))
                this.user.setTitles(data.titles);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.PawnSkin))
                this.user.setUnlockPawnSkinIds(data.pawnSkins);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Portrayal))
                this.user.setPortrayals(data.portrayals);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.SkinItemEnum))
                this.user.setSkinItemList(data.skinItems);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CitySkin))
                this.user.setUnlockCitySkinIds(data.citySkins);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.HeadIcon))
                this.user.setUnlockHeadIcons(data.unlockHeadIcons);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ChatEmoji))
                this.user.setUnlockChatEmojiIds(data.unlockChatEmojiIds);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.RankCoin))
                this.user.setRankCoin(data.rankCoin);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Botany))
                this.user.setUnlockBotanys(data.unlockBotanys);
        }
    };
    // 兵营训练队列
    PlayerModel.prototype.getPawnDrillQueues = function (uid) { return this.pawnDrillQueueMap.get(uid) || []; };
    PlayerModel.prototype.updatePawnDrillQueue = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.pawnDrillQueueMap.clear();
        for (var uid in datas) {
            this.pawnDrillQueueMap.set(uid, datas[uid].list.map(function (m) {
                var data = new PawnDrillInfoObj_1.default().fromSvr(m);
                // 添加训练完成消息
                if (data.surplusTime > 0) {
                    var type = Math.floor(data.id / 100);
                    GameHelper_1.gameHpr.addMessage({
                        key: 'ui.message_101',
                        params: ['pawnText.name_' + data.id, type === 35 ? 'ui.button_produce' : 'ui.button_drill'],
                        tag: data.uid,
                        delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                    });
                }
                return data;
            }));
        }
        // 检测
        this.pawnDrillQueueMap.forEach(function (arr, key) {
            if (arr.length === 1 && !arr[0].surplusTime) {
                _this.pawnDrillQueueMap.delete(key);
            }
        });
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_DRILL_QUEUE, this.mainCityIndex);
    };
    PlayerModel.prototype.getAllPawnDrillList = function () {
        var arr = [];
        this.pawnDrillQueueMap.forEach(function (m) { return arr.pushArr(m); });
        return arr;
    };
    // 获取某个军队训练士兵剩余时间
    PlayerModel.prototype.getSumDrillTimeByArmy = function (uid) {
        var maxTime = 0;
        this.pawnDrillQueueMap.forEach(function (arr) {
            var time = 0;
            arr.forEach(function (m) {
                if (m.surplusTime > 0) {
                    time += m.getSurplusTime();
                }
                else {
                    time += m.needTime;
                }
                if (m.auid === uid && time > maxTime) {
                    maxTime = time;
                }
            });
        });
        return { time: maxTime };
    };
    // 获取某个军队治疗士兵剩余时间
    PlayerModel.prototype.getSumCuringTimeByArmy = function (uid) {
        var maxTime = 0, time = 0;
        this.curingPawns.forEach(function (m) {
            if (m.surplusTime > 0) {
                time += m.getSurplusTime();
            }
            else {
                time += m.needTime;
            }
            if (m.auid === uid && time > maxTime) {
                maxTime = time;
            }
        });
        return { time: maxTime };
    };
    // 获取受伤的士兵
    PlayerModel.prototype.getInjuryPawns = function () {
        return this.injuryPawns;
    };
    PlayerModel.prototype.updateInjuryPawns = function (datas) {
        this.injuryPawns = datas;
    };
    PlayerModel.prototype.addInjuryPawn = function (data) {
        this.injuryPawns.push(data);
        this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
    };
    PlayerModel.prototype.removeInjuryPawn = function (uid) {
        this.injuryPawns.remove('uid', uid);
        this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
    };
    // 获取治疗中的士兵
    PlayerModel.prototype.getCuringPawnsQueue = function () {
        return this.curingPawns;
    };
    PlayerModel.prototype.updatePawnCuringQueue = function (datas, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.curingPawns.length = 0;
        for (var i = 0; i < datas.length; i++) {
            var data = new PawnCureInfoObj_1.default().fromSvr(datas[i]);
            // 添加治疗完成消息
            if (data.surplusTime > 0) {
                // const type = Math.floor(data.id / 100)
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_101',
                    params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv]), 'ui.button_cure'],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                });
            }
            this.curingPawns.push(data);
        }
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_CURING_QUEUE, this.mainCityIndex);
    };
    // 获取士兵练级队列
    PlayerModel.prototype.getPawnLevelingQueues = function () { return this.pawnLevelingQueues; };
    PlayerModel.prototype.updatePawnLevelingQueue = function (datas, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.pawnLevelingQueues = datas.map(function (m) {
            var data = new PawnLevelingInfoObj_1.default().fromSvr(m);
            // 添加训练完成消息
            if (data.surplusTime > 0) {
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_104',
                    params: ['pawnText.name_' + data.id, data.lv],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                });
            }
            return data;
        });
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_LVING_QUEUE, this.mainCityIndex);
    };
    // 是否在队列中
    PlayerModel.prototype.isInPawnLvingQueue = function (uid) { return this.pawnLevelingQueues.has('puid', uid); };
    // 获取某个军队士兵练级剩余时间
    PlayerModel.prototype.getSumLvingTimeByArmy = function (uid) {
        var maxTime = 0, isBattleing = GameHelper_1.gameHpr.isBattleingByIndex(this.mainCityIndex), pause = true;
        var time = 0;
        this.pawnLevelingQueues.forEach(function (m) {
            if (m.surplusTime > 0) {
                time += isBattleing ? m.surplusTime : m.getSurplusTime();
                pause = isBattleing;
            }
            else {
                time += m.needTime;
            }
            if (m.auid === uid && time > maxTime) {
                maxTime = time;
            }
        });
        return { time: maxTime, pause: pause };
    };
    PlayerModel.prototype.getConfigPawnMap = function () { return this.configPawnMap; };
    // 获取配置士兵的装备信息
    PlayerModel.prototype.getConfigPawnInfo = function (pawnId) {
        if (!pawnId) {
            return null;
        }
        var info = this.configPawnMap[pawnId], uid = info === null || info === void 0 ? void 0 : info.equipUid;
        var conf = {
            equip: { uid: '', attrs: [] },
            skinId: (info === null || info === void 0 ? void 0 : info.skinId) || 0,
            attackSpeed: (info === null || info === void 0 ? void 0 : info.attackSpeed) || 0,
        };
        // 兼容装备
        if (uid) {
            var equip = this.getEquipByUid(uid);
            if (equip && (!equip.isExclusive() || equip.checkExclusivePawn(pawnId))) {
                conf.equip = { uid: equip.uid, attrs: equip.attrs };
            }
            else {
                conf.equip = { uid: '', attrs: [] };
            }
            if (info) {
                info.equipUid = conf.equip.uid;
            }
        }
        return conf;
    };
    PlayerModel.prototype.changeConfigPawnInfo = function (id, equipUid, skinId, attackSpeed) {
        this.configPawnMap[id] = { equipUid: equipUid, skinId: skinId, attackSpeed: attackSpeed };
    };
    // 改变皮肤配置
    PlayerModel.prototype.changeConfigPawnInfoByData = function (data) {
        var _a, _b, _c;
        var conf = this.configPawnMap[data.id];
        if (conf) {
            conf.skinId = data.skinId;
            conf.equipUid = ((_a = data.equip) === null || _a === void 0 ? void 0 : _a.uid) || '';
        }
        else {
            this.configPawnMap[data.id] = { equipUid: ((_b = data.equip) === null || _b === void 0 ? void 0 : _b.uid) || '', skinId: data.skinId, attackSpeed: data.attackSpeed };
        }
        var buildId = (_c = data.baseJson) === null || _c === void 0 ? void 0 : _c.spawn_build_id;
        if (buildId) {
            this.tempCanRecruitPawns[buildId] = null;
        }
    };
    // 获取城市皮肤配置
    PlayerModel.prototype.getCitySkinConfigMap = function () { return this.citySkinConfigMap; };
    PlayerModel.prototype.getStudySlots = function (slots) {
        var list = [];
        for (var k in slots) {
            var slot = slots[k];
            if (slot.isYetStudy()) {
                list.push(slot);
            }
        }
        return list;
    };
    // ---------------------------------------------------------政策----------------------------------------------------------------
    // 当前政策槽位
    PlayerModel.prototype.getPolicySlots = function () { return this.policySlots; };
    // 获取已经研究的政策ids
    PlayerModel.prototype.getStudyPolicySlots = function () { return this.getStudySlots(this.policySlots); };
    // 刷新
    PlayerModel.prototype.updatePolicySlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.policySlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, PolicyObj_1.default);
        ReddotHelper_1.reddotHelper.set('can_study_policy', GameHelper_1.gameHpr.checkStudySlotsReddot(this.policySlots));
        isEmit && this.emit(EventType_1.default.UPDATE_POLICY_SLOTS);
    };
    // ---------------------------------------------------------士兵----------------------------------------------------------------
    // 当前士兵槽位
    PlayerModel.prototype.getPawnSlots = function () { return this.pawnSlots; };
    PlayerModel.prototype.getStudyPawnSlots = function () { return this.getStudySlots(this.pawnSlots); };
    PlayerModel.prototype.getPawnSlotByLv = function (lv) { return this.pawnSlots[lv]; };
    PlayerModel.prototype.updatePawnSlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.pawnSlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, PawnSlotObj_1.default);
        this.checkPawnSlotInfo();
        ReddotHelper_1.reddotHelper.set('can_study_pawn', GameHelper_1.gameHpr.checkStudySlotsReddot(this.pawnSlots));
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_SLOTS);
    };
    // 检测槽位
    PlayerModel.prototype.checkPawnSlotInfo = function () {
        var _this = this;
        Constant_1.PAWN_SLOT_CONF.forEach(function (lv) {
            var slot = _this.pawnSlots[lv];
            if (slot) {
                slot.initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(slot.id));
            }
            else {
                _this.pawnSlots[lv] = new PawnSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
        });
        this.tempCanRecruitPawns = {};
    };
    // 获取可以招募的士兵
    PlayerModel.prototype.getCanRecruitPawns = function (buildId) {
        var _this = this;
        var slots = this.tempCanRecruitPawns[buildId];
        if (slots) {
            return slots;
        }
        slots = this.tempCanRecruitPawns[buildId] = [];
        var hasMap = {};
        // 加入固定的槽位信息
        Constant_1.PAWN_SLOT_CONF.forEach(function (lv) {
            var slot = _this.pawnSlots[lv];
            if (slot) {
                hasMap[slot.id] = true;
            }
            else {
                slot = new PawnSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
            slots.push(slot);
        });
        // 加入英雄殿固定的几个槽位信息
        this.heroSlots.forEach(function (m) {
            var _a;
            var id = (_a = m.hero) === null || _a === void 0 ? void 0 : _a.avatarPawn;
            if (!id) {
                slots.push(new PawnSlotObj_1.default().fromSvr({ lv: -m.lv }).init());
            }
            else if (!hasMap[id]) {
                hasMap[id] = true;
                slots.push(new PawnSlotObj_1.default().fromSvr({ lv: -m.lv, id: id }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(id)));
            }
        });
        // 加入直接解锁的士兵
        this.unlockPawnIds.forEach(function (id) {
            var _a;
            if (!hasMap[id] && ((_a = assetsMgr.getJsonData('pawnBase', id)) === null || _a === void 0 ? void 0 : _a.spawn_build_id) === buildId) {
                hasMap[id] = true;
                slots.push(new PawnSlotObj_1.default().fromSvr({ id: id, lv: 1 }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(id)));
            }
        });
        return slots;
    };
    // 获取可生产的器械
    PlayerModel.prototype.getCanProduceMachines = function (buildId) {
        var _this = this;
        var slots = this.tempCanRecruitPawns[buildId];
        if (slots) {
            return slots;
        }
        slots = this.tempCanRecruitPawns[buildId] = [];
        assetsMgr.getJson('pawnBase').datas.filter(function (m) { return m.spawn_build_id === buildId && !m.need_unlock; }).forEach(function (m) {
            slots.push(new PawnSlotObj_1.default().fromSvr({ id: m.id, lv: m.need_build_lv }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(m.id)));
        });
        return slots;
    };
    // ---------------------------------------------------------装备----------------------------------------------------------------
    // 当前装备槽位
    PlayerModel.prototype.getEquipSlots = function () { return this.equipSlots; };
    PlayerModel.prototype.getStudyEquipSlots = function () { return this.getStudySlots(this.equipSlots); };
    PlayerModel.prototype.getEquipSlotByLv = function (lv) { return this.equipSlots[lv]; };
    PlayerModel.prototype.updateEquipSlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.equipSlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, EquipSlotObj_1.default);
        this.checkEquipSlotInfo();
        ReddotHelper_1.reddotHelper.set('can_study_equip', GameHelper_1.gameHpr.checkStudySlotsReddot(this.equipSlots));
        isEmit && this.emit(EventType_1.default.UPDATE_EQUIP_SLOTS);
    };
    // 将打造的装备更新到槽位里面
    PlayerModel.prototype.checkEquipSlotInfo = function () {
        var _this = this;
        var equipMap = {};
        this.equips.forEach(function (m) { return equipMap[m.uid] = m; });
        // 刷新已经打造的信息
        Constant_1.EQUIP_SLOT_CONF.forEach(function (lv) {
            var slot = _this.equipSlots[lv];
            if (!slot) {
                _this.equipSlots[lv] = new EquipSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
            else if (slot === null || slot === void 0 ? void 0 : slot.isYetStudy()) {
                slot.equip = equipMap[slot.uid];
            }
        });
        this.tempCanForgeEquips = null; //重新获取
    };
    // 获取可以打造的装备
    PlayerModel.prototype.getCanForgeEquips = function () {
        var _this = this;
        if (this.tempCanForgeEquips && this.tempCanForgeEquips.length > 0) {
            return this.tempCanForgeEquips;
        }
        var hasMap = {};
        this.tempCanForgeEquips = [];
        Constant_1.EQUIP_SLOT_CONF.forEach(function (lv) {
            var slot = _this.equipSlots[lv];
            if (slot) {
                hasMap[slot.id] = true;
                _this.tempCanForgeEquips.push(slot);
            }
        });
        this.equips.forEach(function (m) {
            if (!hasMap[m.id]) {
                hasMap[m.id] = true;
                _this.tempCanForgeEquips.push(new EquipSlotObj_1.default().fromSvr({ id: m.id }).init().setEquip(m));
            }
        });
        // 加入直接解锁的
        this.unlockEquipIds.forEach(function (m) {
            if (!hasMap[m]) {
                hasMap[m] = true;
                _this.tempCanForgeEquips.push(new EquipSlotObj_1.default().fromSvr({ id: m, lv: 1 }).init());
            }
        });
        return this.tempCanForgeEquips;
    };
    // 打造装备
    PlayerModel.prototype.forgeEquip = function (uid, lockEffect) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, equip;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqForgeEquip({ uid: uid, lockEffect: lockEffect })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            equip = this.getEquipByUid(uid);
                            if (equip) {
                                equip.nextForgeFree = !!data.nextForgeFree;
                            }
                            this.updateCurrForgeEquip(data.currForgeEquip);
                            this.updateRewardItemsByFlags(data.cost);
                            this.setFreeForgeCount(data.freeForgeCount || 0);
                            this.emit(EventType_1.default.FORGE_EQUIP_BEGIN);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 立即完成打造
    PlayerModel.prototype.inDoneForge = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.IN_DONE_FORGE_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_InDoneForge', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.user.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取当前打造装备信息
    PlayerModel.prototype.getCurrForgeEquip = function () { return this.currForgeEquip; };
    PlayerModel.prototype.updateCurrForgeEquip = function (data) {
        this.currForgeEquip = data ? new ForgeEquipInfo_1.default().fromSvr(data) : null;
        if (this.currForgeEquip) {
            this.currForgeEquip.isYetForge = !!this.equips.find(function (m) { return m.id === data.id; });
        }
    };
    // 刷新装备
    PlayerModel.prototype.updateEquip = function (data) {
        var equip = this.equips.find(function (m) { return m.uid === data.uid; });
        if (equip) {
            equip.updateInfo(data);
        }
        else {
            this.equips.push(new EquipInfo_1.default().fromSvr(data));
            this.checkEquipSlotInfo();
        }
        return !equip; //是否新装备
    };
    PlayerModel.prototype.updateEquipInfo = function (data) {
        var isNew = this.updateEquip(data);
        this.updatePawnEquipAttr(data.uid, data.attrs);
        this.tempCanRecruitPawns = {}; //重新获取可训练的士兵 因为士兵有可能有装备
        return isNew;
    };
    PlayerModel.prototype.getEquips = function () { return this.equips; };
    PlayerModel.prototype.getEquipById = function (id) { return id ? this.equips.find(function (m) { return m.id === id; }) : null; };
    PlayerModel.prototype.getEquipByUid = function (uid) { return uid ? this.equips.find(function (m) { return m.uid === uid; }) : null; };
    // 获取士兵可以携带的装备列表
    PlayerModel.prototype.getPawnEquips = function (pawnId) {
        return this.equips.filter(function (m) { return !m.exclusive_pawn || m.exclusive_pawn === pawnId; });
    };
    // 获取已经参与融炼的装备idMap
    PlayerModel.prototype.getYetSmeltEquipIdMap = function (uid) {
        var smeltEquipIdMap = {};
        this.equips.forEach(function (m) {
            if (m.uid !== uid) {
                m.smeltEffects.forEach(function (m) { return smeltEquipIdMap[m.id] = true; });
            }
        });
        return smeltEquipIdMap;
    };
    // 添加打造消息通知
    PlayerModel.prototype.addForgeMessage = function (id, isNew) {
        GameHelper_1.gameHpr.addMessage({
            key: isNew ? 'ui.message_102' : 'ui.message_103',
            params: ['equipText.name_' + id],
            tag: id + '',
        });
    };
    // 获取正在融炼的装备
    PlayerModel.prototype.getCurrSmeltEquip = function () { return this.currSmeltEquip; };
    // 刷新融炼装备
    PlayerModel.prototype.updateCurrSmeltEquip = function (data) {
        this.currSmeltEquip = data ? new SmeltEquipInfo_1.default().fromSvr(data) : null;
    };
    // 融炼装备
    PlayerModel.prototype.smeltEquip = function (mainUid, viceIds) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_SmeltingEquip', { mainUid: mainUid, viceIds: viceIds }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateCurrSmeltEquip(data.currSmeltEquip);
                            this.setFixator(data.fixator);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 还原融炼
    PlayerModel.prototype.restoreSmeltEquip = function (mainUid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_RestoreSmeltEquip', { mainUid: mainUid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateEquipInfo(data.equip);
                            this.emit(EventType_1.default.UPDATE_EQUIP_ATTR, data.equip.uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 刷新士兵的装备等级
    PlayerModel.prototype.updatePawnEquipAttr = function (uid, attrs) {
        var areaCenter = GameHelper_1.gameHpr.areaCenter;
        this.baseArmys.forEach(function (m) {
            var _a;
            var area = areaCenter.getArea(m.index);
            if (area && !area.isBattleing()) {
                (_a = area.getArmyByUid(m.uid)) === null || _a === void 0 ? void 0 : _a.pawns.forEach(function (pawn) { return pawn.updateEquipAttr(uid, attrs); });
            }
        });
    };
    // 还原装备属性
    PlayerModel.prototype.restoreEquipAttr = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqRestoreForge({ uid: uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setIron(data.iron);
                            this.updateEquipInfo(data.equip);
                            this.emit(EventType_1.default.UPDATE_EQUIP_ATTR, data.equip.uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 添加打造消息通知
    PlayerModel.prototype.addSmeltMessage = function (id) {
        GameHelper_1.gameHpr.addMessage({
            key: 'ui.message_109',
            tag: id + '',
        });
    };
    // 获取要塞自动资源配置
    PlayerModel.prototype.getFortAutoSupports = function () { return this.fortAutoSupports; };
    // 是否自动支援
    PlayerModel.prototype.isForAutoSupport = function (index) {
        var _a;
        return !!((_a = this.fortAutoSupports.find(function (m) { return m.index === index; })) === null || _a === void 0 ? void 0 : _a.isAuto);
    };
    // 刷新自动支援配置
    PlayerModel.prototype.updateForAutoSupport = function (index, isAuto) {
        var data = this.fortAutoSupports.find(function (m) { return m.index === index; });
        if (data) {
            data.isAuto = isAuto;
        }
        else {
            this.fortAutoSupports.push({ index: index, isAuto: isAuto });
        }
    };
    // 建筑升级效果通知
    PlayerModel.prototype.addBuildEffectMessage = function (data) {
        if (!Enums_1.BUILD_NID[data.id]) {
            return;
        }
        // 查找定义的效果文本
        var key = 'ui.msg_build_effect_' + Enums_1.BUILD_NID[data.id].toLowerCase();
        var localeText = assetsMgr.lang(key);
        if (localeText === key) {
            return;
        }
        var diff = DBHelper_1.default.buildEffectDelta(data.id, data.lv - 1, data.lv);
        var sign = '-';
        var unit = '%';
        if ([Enums_1.BUILD_NID.MAIN, Enums_1.BUILD_NID.WAREHOUSE, Enums_1.BUILD_NID.GRANARY, Enums_1.BUILD_NID.FREE_BAZAAR, Enums_1.BUILD_NID.ALLI_BAZAAR].includes(data.id)) {
            sign = '+';
            unit = '';
        }
        var delay = 0.5;
        if (diff > 0) {
            GameHelper_1.gameHpr.addMessage({
                key: key,
                params: [" <color=" + Constant_1.COLOR_NORMAL.DONE + ">" + sign + diff + unit + "</color>"],
                tag: data.uid + '_desc',
                delay: Math.max(0, data.getSurplusTime() * 0.001 - 1) + delay,
            });
            delay += 0.5;
        }
        // 兵营特有的二级解锁刀盾兵提示
        if (data.id === Enums_1.BUILD_NID.BARRACKS && data.lv === 2) {
            // 解锁刀盾兵提示
            GameHelper_1.gameHpr.addMessage({
                key: 'ui.msg_build_effect_barracks_2',
                params: ["<color=" + Constant_1.COLOR_NORMAL.DONE + "> " + assetsMgr.lang('pawnText.name_3201') + "</color>"],
                tag: data.uid + '_desc_2',
                delay: Math.max(0, data.getSurplusTime() * 0.001 - 1) + delay,
            });
        }
    };
    // 修建队列
    PlayerModel.prototype.getBtQueues = function () { return this.btQueues; };
    PlayerModel.prototype.updateBtQueue = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.btQueues = datas.map(function (m) {
            var data = new BTInfoObj_1.default().fromSvr(m);
            // 添加训练完成消息
            if (data.surplusTime > 0) {
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_101',
                    params: ['buildText.name_' + data.id, data.lv > 1 ? 'ui.button_up' : 'ui.button_build'],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 - 1),
                });
                _this.addBuildEffectMessage(data);
            }
            return data;
        });
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_BT_QUEUE);
            // 如果当前是在做引导 并且没有修建兵营的队列了
            if (GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.CHOOSE_BTING_BUTTON) && !this.btQueues.has('id', Enums_1.BUILD_NID.BARRACKS)) {
                GameHelper_1.gameHpr.guide.gotoNextStep(GuideConfig_1.GuideTagType.CHECK_CAN_XL_PAWN, true);
            }
        }
    };
    PlayerModel.prototype.removeLocalBTQueues = function (uid) {
        this.btQueues.remove('uid', uid);
    };
    // 取消修建
    PlayerModel.prototype.cancelBtToServer = function (index, uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqCancelBT({ index: index, uid: uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.updateBtQueue(data.queues);
                        this.updateOutputByFlags(data.output);
                        GameHelper_1.gameHpr.delMessageByTag(uid);
                        GameHelper_1.gameHpr.delMessageByTag(uid + '_desc');
                        GameHelper_1.gameHpr.delMessageByTag(uid + '_desc_2');
                        return [2 /*return*/];
                }
            });
        });
    };
    // 立即完成
    PlayerModel.prototype.inDoneBt = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.IN_DONE_BT_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqInDoneBt()];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            audioMgr.playSFX('common/sound_ui_023');
                            this.btQueues.forEach(function (m) {
                                GameHelper_1.gameHpr.delMessageByTag(m.uid);
                                GameHelper_1.gameHpr.message.delayEndByTag(m.uid + '_desc');
                                GameHelper_1.gameHpr.message.delayEndByTag(m.uid + '_desc_2');
                            });
                            this.updateBtQueue(data.queues);
                            this.user.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 是否有建筑在队列中
    PlayerModel.prototype.hasBuildInBtQueue = function (uid) {
        var it = this.btQueues.find(function (m) { return m.uid === uid; });
        return !!(it === null || it === void 0 ? void 0 : it.getSurplusTime());
    };
    PlayerModel.prototype.getBuildBtInfo = function (uid) {
        return this.btQueues.find(function (m) { return m.uid === uid; });
    };
    // 获取军队列表
    PlayerModel.prototype.getAllArmys = function (interval, wait) {
        if (interval === void 0) { interval = 0.5; }
        if (wait === void 0) { wait = true; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isCapture()) {
                            return [2 /*return*/, []]; //如果被沦陷了 直接返回
                        }
                        else if (interval > 0 && this.lastReqArmysTime > 0 && Date.now() - this.lastReqArmysTime <= interval * 1000) {
                            return [2 /*return*/, this.tempArmyList];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetPlayerArmys(wait)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmysTime = Date.now();
                        this.tempArmyList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        // 统计宝箱
                        this.tempArmyList.forEach(function (army) {
                            army.treasures = [];
                            army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
                        });
                        return [2 /*return*/, this.tempArmyList];
                }
            });
        });
    };
    PlayerModel.prototype.getTempArmyList = function () { return this.tempArmyList; };
    // 获取选择军队列表
    PlayerModel.prototype.getSelectArmys = function (index, type, interval) {
        if (interval === void 0) { interval = 1; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isCapture()) {
                            return [2 /*return*/, { list: [], canGotoCount: 0 }]; //如果被沦陷了 直接返回
                        }
                        else if (interval > 0 && this.lastReqSelectArmysTime > 0 && Date.now() - this.lastReqSelectArmysTime <= interval * 1000) {
                            return [2 /*return*/, { err: this.tempSelectArmyErr, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetSelectArmys({ index: index, type: type })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqSelectArmysTime = Date.now();
                        this.tempSelectArmyErr = err;
                        this.tempSelectArmyList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.tempSelectArmyCanGotoCount = (data === null || data === void 0 ? void 0 : data.canGotoCount) || 0;
                        // 统计宝箱
                        this.tempSelectArmyList.forEach(function (army) {
                            army.treasures = [];
                            army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
                        });
                        return [2 /*return*/, { err: err, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }];
                }
            });
        });
    };
    PlayerModel.prototype.getTempSelectArmyList = function () { return this.tempSelectArmyList; };
    // 刷新临时军队宝箱信息
    PlayerModel.prototype.updateTempArmyTreasureInfo = function (treasures, auid, puid) {
        this.updateTempArmyTreasureInfoOne(this.tempArmyList, treasures, auid, puid);
        this.updateTempArmyTreasureInfoOne(this.tempSelectArmyList, treasures, auid, puid);
        this.emit(EventType_1.default.UPDATE_ARMY_TREASURE, auid);
    };
    PlayerModel.prototype.updateTempArmyTreasureInfoOne = function (armys, treasures, auid, puid) {
        var army = armys.find(function (m) { return m.uid === auid; });
        if (army) {
            var pawn = army.pawns.find(function (m) { return m.uid === puid; });
            if (pawn) {
                pawn.treasures.length = 0;
                pawn.treasures.pushArr(treasures);
                army.treasures.length = 0;
                army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
            }
        }
    };
    // 刷新临时军队所在位置
    PlayerModel.prototype.updateTempArmyIndex = function (data) {
        this.updateTempArmyIndexOne(this.tempArmyList, data);
        this.updateTempArmyIndexOne(this.tempSelectArmyList, data);
        this.emit(EventType_1.default.UPDATE_ARMY_AREA_INDEX, data.uid, data.index);
    };
    PlayerModel.prototype.updateTempArmyIndexOne = function (armys, data) {
        var _a;
        var army = armys.find(function (m) { return m.uid === (data === null || data === void 0 ? void 0 : data.uid); });
        if (army) {
            army.index = (_a = data === null || data === void 0 ? void 0 : data.index) !== null && _a !== void 0 ? _a : army.index;
        }
    };
    // 获取商人列表
    PlayerModel.prototype.getMerchants = function () { return this.merchants; };
    // 刷新商人列表
    PlayerModel.prototype.updateMerchants = function (datas) {
        this.merchants = datas.map(function (m) { return new MerchantObj_1.default().fromSvr(m); });
        this.emit(EventType_1.default.UPDATE_MERCHANTS);
    };
    // 获取一个商人的运输量
    PlayerModel.prototype.getMerchantTransitCap = function () { return 1000 + (GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.TRANSIT_CD) > 0 ? 1000 : 0); };
    // 获取军队记录
    PlayerModel.prototype.getArmyMarchRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (GameHelper_1.gameHpr.isNoviceMode) {
                            return [2 /*return*/, []];
                        }
                        else if (this.lastReqArmyMarchRecordTime > 0 && Date.now() - this.lastReqArmyMarchRecordTime <= 5000) {
                            return [2 /*return*/, this.tempArmyMarchRecordList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetArmyRecords', { isBattle: false })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmyMarchRecordTime = Date.now();
                        if (err) {
                            this.tempArmyMarchRecordList = [];
                        }
                        else {
                            this.tempArmyMarchRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        }
                        return [2 /*return*/, this.tempArmyMarchRecordList];
                }
            });
        });
    };
    PlayerModel.prototype.getArmyBattleRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqArmyBattleRecordTime > 0 && Date.now() - this.lastReqArmyBattleRecordTime <= 5000) {
                            return [2 /*return*/, this.tempArmyBattleRecordList];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetBattleRecordsList()];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmyBattleRecordTime = Date.now();
                        if (err) {
                            this.tempArmyBattleRecordList = [];
                        }
                        else {
                            this.tempArmyBattleRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        }
                        return [2 /*return*/, this.tempArmyBattleRecordList];
                }
            });
        });
    };
    // 获取市场记录
    PlayerModel.prototype.getBazaarRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, uid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqBazaarRecordTime > 0 && Date.now() - this.lastReqBazaarRecordTime <= 5000) {
                            return [2 /*return*/, this.tempBazaarRecordList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetBazaarRecords', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        uid = GameHelper_1.gameHpr.getUid();
                        this.lastReqBazaarRecordTime = Date.now();
                        this.tempBazaarRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.tempBazaarRecordList.forEach(function (m) {
                            var _a, _b, _c;
                            m.nickname = (_a = m.names) === null || _a === void 0 ? void 0 : _a[0];
                            if (m.type === 6) {
                                m.type = m.type * 10 + (m.owner === uid ? 1 : 0);
                                m.nickname = m.owner === uid ? (_b = m.names) === null || _b === void 0 ? void 0 : _b[1] : (_c = m.names) === null || _c === void 0 ? void 0 : _c[0];
                            }
                            var res = m.res || {};
                            m.res0 = res.resType !== undefined ? new CTypeObj_1.default().init(res.resType, 0, res.resCount) : null;
                            m.res1 = res.costType !== undefined ? new CTypeObj_1.default().init(res.costType, 0, res.costCount) : null;
                            m.actRes = res.actCount !== undefined ? new CTypeObj_1.default().init(res.resType, 0, res.actCount) : null;
                        });
                        return [2 /*return*/, this.tempBazaarRecordList];
                }
            });
        });
    };
    // 获取新手任务列表
    PlayerModel.prototype.getGuideTasks = function () {
        return this.guideTasks;
    };
    PlayerModel.prototype.updateGuideTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.guideTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'guideTask');
            task && _this.guideTasks.push(task);
        });
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
        if (this.guideTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('guide_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('guide_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('guide_task');
            ReddotHelper_1.reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1);
        }
    };
    // 更新新手任务进度
    PlayerModel.prototype.updateGuideTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (info) {
                var _a;
                var data = _this.guideTasks.find(function (m) { return m.id === info.id; });
                if (!data) {
                    var task = new TaskObj_1.default().init(info, 'guideTask');
                    task && _this.guideTasks.push(task);
                }
                else {
                    (_a = data.cond) === null || _a === void 0 ? void 0 : _a.updateProgress(info.progress);
                }
            });
            this.updateGuideTaskState(false);
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetGuideTask = function () {
        var task = null;
        this.guideTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateGuideTaskState = function (isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (this.guideTasks.length === 0) {
            return;
        }
        this.guideTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetGuideTask();
        ReddotHelper_1.reddotHelper.set('guide_task', !!task);
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkGuideTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetGuideTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimTaskReward = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqClaimTaskReward({ id: id })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateGuideTasks(data.tasks);
                            this.updateGuideTaskState();
                            this.updateTodayTasks(data.todayTasks || []);
                            this.updateTodayTaskState();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取每日任务列表
    PlayerModel.prototype.getTodayTasks = function () {
        return this.todayTasks;
    };
    PlayerModel.prototype.updateTodayTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.todayTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'todayTask');
            task && _this.todayTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        if (this.todayTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('today_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('today_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('today_task');
            ReddotHelper_1.reddotHelper.register('today_task', this.checkTodayTaskState, this, 1);
        }
    };
    // 更新每日任务进度
    PlayerModel.prototype.updateTodayTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (taskInfo) { var _a, _b; return (_b = (_a = _this.todayTasks.find(function (m) { return m.id === taskInfo.id; })) === null || _a === void 0 ? void 0 : _a.cond) === null || _b === void 0 ? void 0 : _b.updateProgress(taskInfo.progress); });
            this.updateTodayTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetTodayTask = function () {
        var task = null;
        this.todayTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateTodayTaskState = function () {
        if (this.todayTasks.length === 0) {
            if (this.guideTasks.length === 0) {
                this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE);
            }
            return;
        }
        this.todayTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetTodayTask();
        ReddotHelper_1.reddotHelper.set('today_task', !!task);
        this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkTodayTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetTodayTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimTodayTaskReward = function (id, treasureIndex, selectIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ClaimTodayTaskReward', { id: id, treasureIndex: treasureIndex, selectIndex: selectIndex }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateTodayTasks(data.todayTasks);
                            this.updateTodayTaskState();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取其他任务列表
    PlayerModel.prototype.getOtherTasks = function () {
        return this.otherTasks;
    };
    PlayerModel.prototype.updateOtherTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.otherTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'otherTask');
            task && _this.otherTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        if (this.otherTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('other_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('other_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('other_task');
            ReddotHelper_1.reddotHelper.register('other_task', this.checkOtherTaskState, this, 1);
        }
    };
    // 更新其他任务进度
    PlayerModel.prototype.updateOtherTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (info) {
                var _a;
                var data = _this.otherTasks.find(function (m) { return m.id === info.id; });
                if (data) {
                    (_a = data.cond) === null || _a === void 0 ? void 0 : _a.updateProgress(info.progress);
                }
                else {
                    var task = new TaskObj_1.default().init(info, 'otherTask');
                    task && _this.otherTasks.push(task);
                }
            });
            this.updateOtherTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetOtherTask = function () {
        var task = null;
        this.otherTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateOtherTaskState = function () {
        if (this.otherTasks.length === 0) {
            return;
        }
        this.otherTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetOtherTask();
        ReddotHelper_1.reddotHelper.set('other_task', !!task);
        this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkOtherTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetOtherTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimOtherTaskReward = function (id, treasureIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ClaimOtherTaskReward', { id: id, treasureIndex: treasureIndex }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateOtherTasks(data.otherTasks);
                            if (this.otherTasks.length === 0) {
                                this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE);
                            }
                            else {
                                this.updateOtherTaskState();
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取所有任务
    PlayerModel.prototype.getPlayerAllTasks = function () {
        var tasks = [];
        tasks.pushArr(this.getGuideTasks());
        if (tasks.length === 0) {
            tasks.pushArr(this.getTodayTasks());
        }
        tasks.pushArr(this.getOtherTasks());
        return tasks;
    };
    PlayerModel.prototype.getPlayerTaskCount = function () {
        return (this.guideTasks.length || this.todayTasks.length) + this.otherTasks.length;
    };
    PlayerModel.prototype.getCangetPlayerTask = function () {
        return this.getCangetGuideTask() || this.getCangetTodayTask() || this.getCangetOtherTask();
    };
    // 刷新添加产量时间
    PlayerModel.prototype.updateAddOutputTime = function (timeMap) {
        this.addOutputSurplusTime = timeMap || {};
        this.getAddOutputTime = Date.now();
    };
    // 购买添加产量时间
    PlayerModel.prototype.buyAddOutputTime = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.ADD_OUTPUT_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_BuyAddOutput', { type: type }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateAddOutputTime(data.addOutputSurplusTime);
                            this.user.setGold(data.gold);
                            this.updateOutputByFlags(data.output);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 添加隐藏的私聊频道
    PlayerModel.prototype.addHidePChatChannels = function (channel, uid) {
        this.hidePChatChannels[channel] = uid;
    };
    PlayerModel.prototype.getOccupyLandCountMap = function () { return this.occupyLandCountMap; };
    // 刷新攻占领地数量
    PlayerModel.prototype.updateOccupyLandCountMap = function (data) {
        this.occupyLandCountMap = {};
        for (var key in data) {
            this.occupyLandCountMap[key] = data[key].arr;
        }
    };
    // 击杀数量
    PlayerModel.prototype.getKillRecordMap = function () { return this.killRecordMap; };
    PlayerModel.prototype.recordKillCount = function (id, count) {
        var val = this.killRecordMap[id] || 0;
        this.killRecordMap[id] = val + count;
    };
    // 记录结果
    PlayerModel.prototype.getBattleForecastRetData = function (index, key) {
        var _a;
        return (_a = this.tempBattleForecastRets.find(function (m) { return m.index === index && m.key === key; })) === null || _a === void 0 ? void 0 : _a.data;
    };
    PlayerModel.prototype.setBattleForecastRetMap = function (index, key, data) {
        var it = this.tempBattleForecastRets.find(function (m) { return m.index === index && m.key === key; });
        if (it) {
            it.data = data;
            return;
        }
        else if (this.tempBattleForecastRets.length > 30) {
            this.tempBattleForecastRets.shift();
        }
        this.tempBattleForecastRets.push({ index: index, key: key, data: data });
    };
    // 刷新士兵的装备等级
    PlayerModel.prototype.updatePawnHeroAttr = function (id, attrs) {
        var areaCenter = GameHelper_1.gameHpr.areaCenter;
        this.baseArmys.forEach(function (m) {
            var _a;
            var area = areaCenter.getArea(m.index);
            if (area && !area.isBattleing()) {
                (_a = area.getArmyByUid(m.uid)) === null || _a === void 0 ? void 0 : _a.pawns.forEach(function (pawn) { return pawn.updateHeroAttr(id, attrs); });
            }
        });
    };
    // 刷新单个槽位信息
    PlayerModel.prototype.updateHeroSlotOne = function (data) {
        var index = Constant_1.HERO_SLOT_LV_COND.indexOf(data.lv);
        if (index === -1) {
            return;
        }
        var info = this.heroSlots[index];
        if (info) {
            info.fromSvr(data);
        }
        else {
            this.heroSlots[index] = new HeroSlotObj_1.default().fromSvr(data);
        }
        this.emit(EventType_1.default.UPDATE_HERO_SLOT_INFO);
    };
    // 供奉英雄
    PlayerModel.prototype.worshipHero = function (index, id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqWorshipHero({ index: index, id: id })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateHeroSlotOne(data.slot);
                            this.setUnlockPawnIds(data.unlockPawnIds);
                            this.updatePawnSlots(data.pawnSlots);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 改变士兵画像
    PlayerModel.prototype.changePawnPortrayal = function (index, armyUid, uid, portrayalId) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnPortrayal({ index: index, armyUid: armyUid, uid: uid, portrayalId: portrayalId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateHeroSlotOne(data.slot);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 检测某个士兵是否可化身
    PlayerModel.prototype.checkCanAvatarPawn = function (id) {
        return this.heroSlots.some(function (m) { return !!m.hero && !m.avatarArmyUID && !m.isDie() && m.hero.avatarPawn === id; });
    };
    PlayerModel.prototype.getHeroSlotByPawnId = function (id) {
        return this.heroSlots.find(function (m) { var _a; return ((_a = m.hero) === null || _a === void 0 ? void 0 : _a.avatarPawn) === id; });
    };
    PlayerModel.prototype.findMapMark = function (point) {
        return this.mapMarks.find(function (m) { return m.point.equals(point); });
    };
    PlayerModel.prototype.getMapMarks = function () {
        return this.mapMarks;
    };
    // 添加标记
    PlayerModel.prototype.addMapMark = function (data) {
        var mark = this.findMapMark(data.point);
        if (mark) {
            mark.name = data.name;
        }
        else {
            this.mapMarks.push(data);
        }
        this.emit(EventType_1.default.UPDATE_MAP_MARK);
    };
    // 删除标记
    PlayerModel.prototype.removeMapMark = function (point) {
        return __awaiter(this, void 0, void 0, function () {
            var index, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        index = this.mapMarks.findIndex(function (m) { return m.point.equals(point); });
                        if (index === -1) {
                            return [2 /*return*/, ''];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_RemoveMapMark', { point: point }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.mapMarks.splice(index, 1);
                            this.emit(EventType_1.default.UPDATE_MAP_MARK);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 更新玩家信息
    PlayerModel.prototype.OnUpdatePlayerInfo = function (data) {
        var _this = this;
        cc.log('OnUpdatePlayerInfo', data.list);
        data.list.forEach(function (m) {
            var data = m['data_' + m.type];
            if (m.type === Enums_1.NotifyType.OUTPUT) { //产出
                _this.updateOutputByFlags(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_ITEMS) { //更新通用物品
                _this.updateRewardItemsByFlags(data);
            }
            else if (m.type === Enums_1.NotifyType.BT_QUEUE) { //建造队列
                _this.updateBtQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.BUILD_UP) { //建筑升级
                _this.updateMainBuildInfo(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_DRILL_QUEUE) { //训练队列
                _this.updatePawnDrillQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_LEVELING_QUEUE) { //练级队列
                _this.updatePawnLevelingQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_CURING_QUEUE) { // 刷新士兵治疗队列
                _this.updatePawnCuringQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_INJURY_ADD) { // 添加受伤的士兵
                _this.addInjuryPawn(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_INJURY_REMOVE) { // 受伤士兵移除
                _this.removeInjuryPawn(data);
            }
            else if (m.type === Enums_1.NotifyType.ARMY_DIST) { //军队分布
                _this.updateArmyDists(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_MERCHANT) { //更新商人
                _this.updateMerchants(data);
            }
            else if (m.type === Enums_1.NotifyType.FORGE_EQUIP_RET) { //打造装备结果
                _this.updateCurrForgeEquip(null);
                var isNew = _this.updateEquipInfo(data);
                _this.emit(EventType_1.default.FORGE_EQUIP_COMPLETE, data.uid);
                _this.addForgeMessage(data.uid.split('_')[0], isNew);
            }
            else if (m.type === Enums_1.NotifyType.SMELT_EQUIP_RET) { //融炼装备结果
                _this.updateCurrSmeltEquip(null);
                _this.updateEquipInfo(data);
                _this.emit(EventType_1.default.SMELT_EQUIP_COMPLETE, data.uid);
                _this.addSmeltMessage(data.uid.split('_')[0]);
            }
            else if (m.type === Enums_1.NotifyType.ADD_OUTPUT_TIME) { //刷新添加的产量时间
                _this.updateAddOutputTime(data);
                _this.emit(EventType_1.default.UPDATE_ADD_OUTPUT_TIME);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_GENERAL_TASKS) { //刷新常规任务
                GameHelper_1.gameHpr.task.updateGeneralTasks(data || []);
            }
            else if (m.type === Enums_1.NotifyType.NEW_TREASURE) { //是否有新的宝箱
                ReddotHelper_1.reddotHelper.set('treasure_main', !!data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TODAY_INFO) { //每日信息
                _this.todayOccupyCellCount = data.todayOccupyCellCount || 0;
                _this.todayReplacementCount = data.todayReplacementCount || 0;
                _this.cellTondenCount = data.cellTondenCount || 0;
                _this.updateTodayTasks(data.todayTasks || []);
                _this.updateTodayTaskState();
                _this.emit(EventType_1.default.UPDATE_TONDEN_COUNT);
            }
            else if (m.type === Enums_1.NotifyType.CELL_TONDEN_COUNT) { //刷新屯田次数
                _this.cellTondenCount = data || 0;
                _this.emit(EventType_1.default.UPDATE_TONDEN_COUNT);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TASKS) { //刷新任务进度
                _this.updateGuideTasksProgress(data.guideTasks || []);
                _this.updateTodayTasksProgress(data.todayTasks || []);
                _this.updateOtherTasksProgress(data.otherTasks || []);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_LAND_SCORE) { //刷新玩家领地积分
                _this.landScore = data.landScore || 0;
                _this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1;
                _this.updateOccupyLandCountMap(data.occupyLandCountMap || {});
            }
            else if (m.type === Enums_1.NotifyType.CHANGE_HERO_SLOT_INFO) { //英雄殿信息改变
                _this.updateHeroSlotOne(data);
            }
            else if (m.type === Enums_1.NotifyType.COMPENSATE) { //战损补偿通知
                ViewHelper_1.viewHelper.showMessageBox('ui.battle_compensate_tip', { okText: 'ui.button_gotit' });
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_POLICY_SLOT) { //政策槽位更新
                _this.updatePolicySlots(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_EQUIP_SLOT) { //装备槽位更新
                _this.updateEquipSlots(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_PAWN_SLOT) { //士兵槽位更新
                _this.updatePawnSlots(data);
            }
        });
    };
    __decorate([
        ut.syncLock
    ], PlayerModel.prototype, "getBazaarRecords", null);
    PlayerModel = __decorate([
        mc.addmodel('player')
    ], PlayerModel);
    return PlayerModel;
}(mc.BaseModel));
exports.default = PlayerModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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