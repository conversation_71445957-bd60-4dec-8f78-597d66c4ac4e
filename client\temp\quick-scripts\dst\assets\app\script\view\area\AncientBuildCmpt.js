
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AncientBuildCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'de2ef8h2fZIPpcnDYYevUvO', 'AncientBuildCmpt');
// app/script/view/area/AncientBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AncientObj_1 = require("../../model/main/AncientObj");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var AncientBTAnimRoleConf_1 = require("./AncientBTAnimRoleConf");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 遗迹建筑
var AncientBuildCmpt = /** @class */ (function (_super) {
    __extends(AncientBuildCmpt, _super);
    function AncientBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.lvNode = null; //等级节点
        _this.upLvAnimNode = null; //升级动画
        _this.touchCmpt = null;
        _this.ancientInfo = null;
        return _this;
    }
    AncientBuildCmpt.prototype.init = function (data, origin, originY, owner) {
        _super.prototype.init.call(this, data, origin, originY, owner);
        this.initAncientInfo(data);
        this.touchCmpt = this.body.addComponent(ClickTouchCmpt_1.default).on(this.onClick, this);
        this.syncPoint();
        this.syncZindex();
        // 初始化等级
        this.initLv();
        this.updateLv(this.ancientInfo.lv);
        // 显示是否在升级中
        this.updateUpLvAnim();
        return this;
    };
    // 重新同步
    AncientBuildCmpt.prototype.resync = function (data) {
        this.data = data;
        this.initAncientInfo(data);
        this.syncPoint();
        this.syncZindex();
        this.setCanClick(true);
        this.updateLv(this.ancientInfo.lv);
        this.updateUpLvAnim();
        return this;
    };
    // 初始化遗迹信息
    AncientBuildCmpt.prototype.initAncientInfo = function (data) {
        if (data.aIndex < 0) {
            this.ancientInfo = new AncientObj_1.default().init(GameHelper_1.gameHpr.world.getMapCellByIndex(data.aIndex));
            this.ancientInfo.updateInfo({ lv: data.lv });
        }
        else {
            this.ancientInfo = GameHelper_1.gameHpr.world.getAncientInfo(data.aIndex);
        }
    };
    AncientBuildCmpt.prototype.initLv = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.lvNode = cc.instantiate2(pfb, this.node);
                        this.lvNode.zIndex = 1;
                        this.lvNode.setPosition(Math.floor(this.data.size.x * 0.5) * Constant_1.TILE_SIZE, 36);
                        this.lvNode.Child('val', cc.Label).string = '' + this.ancientInfo.lv;
                        this.lvNode.active = true;
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载升级动画
    AncientBuildCmpt.prototype.loadUpLvAnim = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_ANCIENT_BT', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.upLvAnimNode = cc.instantiate2(pfb, this.node);
                        this.upLvAnimNode.zIndex = 1;
                        this.upLvAnimNode.setPosition(0, 0);
                        this.upLvAnimNode.active = false;
                        this.updateUpLvAnim();
                        return [2 /*return*/];
                }
            });
        });
    };
    AncientBuildCmpt.prototype.clean = function () {
        var _a;
        this.unscheduleAllCallbacks();
        this.node.stopAllActions();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.clean();
        this.node.destroy();
        this.data = null;
    };
    AncientBuildCmpt.prototype.onClick = function () {
        if (!this.data) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.ancientInfo && this.data.aIndex >= 0 && GameHelper_1.gameHpr.checkIsOneAlliance(this.ancientInfo.owner)) {
            ViewHelper_1.viewHelper.showPnl(this.data.getUIUrl(), this.data);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', this.data);
        }
    };
    // 设置是否可以点击
    AncientBuildCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 刷新等级
    AncientBuildCmpt.prototype.updateLv = function (lv) {
        var _a;
        if (this.lvNode) {
            this.lvNode.Child('val', cc.Label).string = '' + lv;
        }
        if (this.data.isMaxLv()) {
            this.Child('body/val', cc.MultiFrame).setFrame(5);
        }
        else if ((_a = this.ancientInfo) === null || _a === void 0 ? void 0 : _a.owner) {
            this.Child('body/val', cc.MultiFrame).setFrame(Math.floor(lv / 5) + 1);
        }
        else {
            this.Child('body/val', cc.MultiFrame).setFrame(0);
        }
    };
    // 刷新升级动画
    AncientBuildCmpt.prototype.updateUpLvAnim = function () {
        var _a, _b;
        if (!this.ancientInfo.owner || this.data.isMaxLv() || this.ancientInfo.state !== 1 || !!this.ancientInfo.pauseState) {
            (_a = this.upLvAnimNode) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else if (this.upLvAnimNode) {
            this.upLvAnimNode.active = true;
            this.upLvAnimNode.Child('time', cc.LabelTimer).run(this.ancientInfo.getSurplusTime() * 0.001);
            var roles = ((_b = AncientBTAnimRoleConf_1.ANCIENT_BTANIM_ROLE_POSITION[this.data.id]) === null || _b === void 0 ? void 0 : _b[Math.floor(this.ancientInfo.lv / 5)]) || [];
            this.upLvAnimNode.Child('root').Items(roles, function (it, data) {
                it.setPosition(data.x, data.y);
                it.scaleX = data.scaleX;
                it.Component(cc.Animation).play('ancient_bt', ut.random(1, 5) * 0.1);
            });
        }
        else {
            this.loadUpLvAnim();
        }
    };
    AncientBuildCmpt = __decorate([
        ccclass
    ], AncientBuildCmpt);
    return AncientBuildCmpt;
}(BaseBuildCmpt_1.default));
exports.default = AncientBuildCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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