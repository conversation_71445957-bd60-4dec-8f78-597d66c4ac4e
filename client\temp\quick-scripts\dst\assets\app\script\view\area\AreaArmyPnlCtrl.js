
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaArmyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ec3dakw2/tDeJ/37Uxmro8h', 'AreaArmyPnlCtrl');
// app/script/view/area/AreaArmyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var AreaArmyPnlCtrl = /** @class */ (function (_super) {
    __extends(AreaArmyPnlCtrl, _super);
    function AreaArmyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabLbl_ = null; // path://root/info/bg/tab_l
        _this.showPawnEquipTge_ = null; // path://root/info/show_pawn_equip_te_t
        _this.listSv_ = null; // path://root/list_sv
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.PKEY_TAB = 'AREA_ARMY_TAB';
        _this.tab = 0;
        _this.area = null;
        _this.player = null;
        _this.preShowPawnEquip = false;
        _this.hpBarList = [];
        return _this;
    }
    AreaArmyPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PAWN_TREASURE] = this.onUpdatePawnTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY_TREASURES] = this.onUpdateArmyTreasures, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_ARMY_NAME] = this.onUpdateArmyName, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_AREA_ARMY_LIST] = this.onUpdateAreaArmyList, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.REMOVE_ARMY] = this.onUpdateAreaArmyList, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdateAreaArmyList, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_PAWN_LVING_QUEUE] = this.onUpdateAreaArmyList, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdateAreaArmyList, _h.enter = true, _h),
        ];
    };
    AreaArmyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    AreaArmyPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        var area = this.area = GameHelper_1.gameHpr.areaCenter.getLookArea();
        if (!area) {
            return this.hide();
        }
        this.preShowPawnEquip = this.showPawnEquipTge_.isChecked = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED)) !== null && _a !== void 0 ? _a : false;
        var _b = __read(this.updateArmyCount(), 2), defCount = _b[0], atkCount = _b[1];
        var tabStr = GameHelper_1.gameHpr.user.getTempPreferenceMap(this.PKEY_TAB) || '';
        var _c = __read(ut.stringToNumbers(tabStr, '_'), 2), index = _c[0], type = _c[1];
        if (index !== area.index) {
            this.listSv_.node.Data = -1;
            if (GameHelper_1.gameHpr.checkIsOneAlliance(area.owner)) {
                type = defCount > 0 || atkCount === 0 ? 0 : 1;
            }
            else {
                type = atkCount > 0 || defCount === 0 ? 1 : 0;
            }
        }
        this.tabsTc_.Tabs(type !== null && type !== void 0 ? type : 0);
    };
    AreaArmyPnlCtrl.prototype.onRemove = function () {
        this.hpBarList = [];
        this.area = null;
        if (this.preShowPawnEquip !== this.showPawnEquipTge_.isChecked) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED, this.showPawnEquipTge_.isChecked);
        }
    };
    AreaArmyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/treasure_be
    AreaArmyPnlCtrl.prototype.onClickTreasure = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (data.owner !== GameHelper_1.gameHpr.getUid()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_OPEN_OTHER_TREASURE);
        }
        var treasures = data === null || data === void 0 ? void 0 : data.getAllPawnTreasures();
        if (treasures && treasures.length > 0) {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', treasures);
        }
    };
    // path://root/list_sv/view/content/item/name/edit/edit_name_be
    AreaArmyPnlCtrl.prototype.onClickEditName = function (event, _) {
        var data = event.target.parent.parent.parent.parent.Data;
        if (!data || !data.isOwner()) {
        }
        else if (this.area.isBattleing()) {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('area/EditArmyName', data);
        }
    };
    // path://root/list_sv/view/content/item/pawns/pawn_be
    AreaArmyPnlCtrl.prototype.onClickPawn = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (!data) {
        }
        else if (data.drillInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByDrillInfo(data.drillInfo), data.drillInfo, 'area_army');
        }
        else if (data.curingInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByCureInfo(data.curingInfo), data.curingInfo, 'area_army');
        }
        else if (data.lvingInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByLvingInfo(data.pawn, data.lvingInfo), data.lvingInfo, 'area_army');
        }
        else if (data.pawn) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', data.pawn, null, 'area_army');
        }
    };
    // path://root/info/show_pawn_equip_te_t
    AreaArmyPnlCtrl.prototype.onClickShowPawnEquip = function (event, data) {
        audioMgr.playSFX('click');
        this.updateArmyList();
        ViewHelper_1.viewHelper.showNoLongerTip('area_army_show_equip', { content: 'ui.area_army_show_equip_tip' });
    };
    // path://root/list_sv/view/content/item/march_speed_be
    AreaArmyPnlCtrl.prototype.onClickMarchSpeed = function (event, _) {
        var _this = this;
        var it = event.target.parent.parent;
        var data = it === null || it === void 0 ? void 0 : it.Data;
        if (!data) {
            return;
        }
        else if (this.area.isBattleing()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        ViewHelper_1.viewHelper.showPnl('main/ModifyMarchSpeed', data, function (speed) {
            if (speed) {
                data.marchSpeed = speed;
                if (_this.isValid) {
                    _this.updateArmyMarchSpeed(it.Child('top'), data);
                }
            }
        });
    };
    // path://root/list_sv/view/content/item/force_revoke_be
    AreaArmyPnlCtrl.prototype.onClickForceRevoke = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (data.owner === GameHelper_1.gameHpr.getUid() || data.aIndex !== GameHelper_1.gameHpr.player.getMainCityIndex() || data.isBattleing()) {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.force_revoke_tip', {
            params: [GameHelper_1.gameHpr.getPlayerName(data.owner), data.name],
            ok: function () {
                GameHelper_1.gameHpr.world.forceRevoke(data.uid).then(function (err) {
                    if (err) {
                        return ViewHelper_1.viewHelper.showAlert(err);
                    }
                    else if (_this.isValid) {
                        // this.hide()
                    }
                });
            },
            cancel: function () { }
        });
    };
    // path://root/tabs_tc_tce
    AreaArmyPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var tab = this.tab = Number(event.node.name);
        GameHelper_1.gameHpr.user.setTempPreferenceData(this.PKEY_TAB, this.area.index + '_' + tab);
        this.updateArmyList();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新士兵宝箱
    AreaArmyPnlCtrl.prototype.onUpdatePawnTreasure = function (pawn) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === pawn.armyUid; });
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data);
        }
    };
    // 刷新军队宝箱
    AreaArmyPnlCtrl.prototype.onUpdateArmyTreasures = function (army) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === army.uid; });
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data);
        }
    };
    // 刷新军队名字
    AreaArmyPnlCtrl.prototype.onUpdateArmyName = function (uid, name) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid; });
        if (it) {
            it.Child('top').Child('name/val', cc.Label).string = name;
        }
    };
    AreaArmyPnlCtrl.prototype.onUpdateAreaArmyList = function (index) {
        if (this.area.index === index) {
            this.updateArmyCount();
            this.updateArmyList();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新军队数量
    AreaArmyPnlCtrl.prototype.updateArmyCount = function () {
        var owner = this.area.owner, maxArmyCount = this.area.maxArmyCount;
        var defCount = 0, atkCount = 0;
        // 计算各个军队数量
        this.area.armys.forEach(function (m) {
            if (m.getPawnActCount() === 0) {
                return;
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defCount += 1;
            }
            else {
                atkCount += 1;
            }
        });
        // 从这个区域开始行军的军队数量
        var index = this.area.index;
        GameHelper_1.gameHpr.world.getMarchs().filter(function (m) { return m.armyIndex === index; }).forEach(function (m) {
            if (m.autoRevoke) {
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defCount += 1;
            }
            else {
                atkCount += 1;
            }
        });
        this.tabsTc_.Child('0/Background/lay/count', cc.Label).string = "(" + defCount + "/" + maxArmyCount + ")";
        this.tabsTc_.Child('0/checkmark/lay/count', cc.Label).string = "(" + defCount + "/" + maxArmyCount + ")";
        this.tabsTc_.Child('1/Background/lay/count', cc.Label).string = "(" + atkCount + "/" + maxArmyCount + ")";
        this.tabsTc_.Child('1/checkmark/lay/count', cc.Label).string = "(" + atkCount + "/" + maxArmyCount + ")";
        return [defCount, atkCount];
    };
    // 刷新军队列表
    AreaArmyPnlCtrl.prototype.updateArmyList = function () {
        var _this = this;
        this.tabLbl_.setLocaleKey('ui.area_army_' + this.tab);
        var marchs = {};
        GameHelper_1.gameHpr.world.getMarchs().forEach(function (m) { return marchs[m.armyUid] = true; });
        var owner = this.area.owner, uid = GameHelper_1.gameHpr.getUid();
        var arr = this.area.armys.filter(function (m) {
            if (m.pawns.length === 0 && m.owner !== uid) {
                return false;
            }
            return !_this.tab === GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner);
        });
        var pawnDrillMap = {}, lvingPawnLvMap = {}, curingPawnLvMap = {};
        this.player.getAllPawnDrillList().forEach(function (m) {
            var p = pawnDrillMap[m.auid];
            if (!p) {
                p = pawnDrillMap[m.auid] = [];
            }
            p.push(m);
        });
        this.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m; });
        this.player.getCuringPawnsQueue().forEach(function (m) { return curingPawnLvMap[m.uid] = m; });
        this.listSv_.Child('empty').setActive(arr.length === 0);
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode, showEquip = this.showPawnEquipTge_.isChecked;
        var mainCityIndex = GameHelper_1.gameHpr.player.getMainCityIndex(), isAncient = this.area.isAncient();
        this.hpBarList = [];
        if (this.listSv_.node.Data !== this.tab) {
            this.listSv_.node.Data = this.tab;
            this.listSv_.stopAutoScroll();
            this.listSv_.content.y = 0;
        }
        this.listSv_.Items(arr, function (it, data) {
            var _a;
            it.Data = data;
            var top = it.Child('top'), bottom = it.Child('bottom');
            var pawns = data.pawns, isHasLving = false, isOwner = data.isOwner(), isOneAlliance = GameHelper_1.gameHpr.checkIsOneAlliance(data.owner, uid);
            it.Color(isOwner ? '#E9DDC7' : isOneAlliance ? '#DAEBDD' : '#F6D6CD');
            var armyName = data.owner ? data.name : isAncient ? assetsMgr.lang('ui.ancient_army_name') : assetsMgr.lang(pawns[0] ? 'ui.pawn_type_' + (pawns[0].type || 6) : 'ui.neutral_pawn');
            top.Child('name/val', cc.Label).Color(isOwner ? '#564C49' : isOneAlliance ? '#4A85D5' : '#D54A4A').string = armyName;
            top.Child('name/edit').active = isOwner && !isNoviceMode;
            var other = top.Child('name/other'), alli = top.Child('alli');
            if (other.active = !isOwner && !!data.owner) {
                var plr = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
                if (plr) {
                    ResHelper_1.resHelper.loadPlayerHead(other.Child('head'), plr.headIcon, _this.key);
                    other.Child('name', cc.Label).string = ut.nameFormator(plr.nickname, 7);
                    // 联盟
                    if (alli.active = !!plr.allianceUid && !isOneAlliance) {
                        ResHelper_1.resHelper.loadAlliIcon(plr.allianceIcon, alli.Child('icon'), _this.key);
                        alli.Child('name', cc.Label).string = plr.allianceName;
                    }
                }
                else {
                    other.active = alli.active = false;
                }
            }
            else {
                alli.active = false;
            }
            var drills = ((_a = pawnDrillMap[data.uid]) === null || _a === void 0 ? void 0 : _a.slice()) || [];
            var list = isOwner ? pawns.concat(data.drillPawns).concat(data.curingPawns) : pawns;
            it.Child('pawns').Items(list, function (node, pawn) {
                var _a, _b;
                var icon = node.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                if (isId) {
                    node.Data = { id: pawn, drillInfo: drills.remove('id', pawn) };
                }
                else if (isCuring) {
                    node.Data = { id: pawn, curingInfo: curingPawnLvMap[pawn.uid] };
                }
                else {
                    node.Data = { pawn: pawn, id: pawn.id, lvingInfo: lvingPawnLvMap[pawn.uid] };
                }
                var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                var lv = isLving ? (_a = lvingPawnLvMap[pawn.uid]) === null || _a === void 0 ? void 0 : _a.lv : (isId ? 1 : pawn.lv);
                icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_b = pawn.portrayal) === null || _b === void 0 ? void 0 : _b.id) || pawn.id), icon, _this.key, false);
                node.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                if (node.Child('hp').active = (!isId && !isCuring)) {
                    var spr = node.Child('hp/bar', cc.Sprite);
                    spr.fillRange = pawn.getHpRatio();
                    _this.hpBarList.push({ bar: spr, pawn: pawn });
                }
                var showNode = node.Child('show');
                if (showNode.active = showEquip) {
                    // 出手速度
                    showNode.Child('speed', cc.Label).string = (isId || isCuring) ? '' : '' + pawn.attackSpeed;
                    // 装备
                    if (showNode.Child('equip').active = !isId && !isCuring && !!(pawn === null || pawn === void 0 ? void 0 : pawn.isCanWearEquip())) {
                        var spr = showNode.Child('equip/val', cc.Sprite), equip = pawn.equip;
                        if (equip === null || equip === void 0 ? void 0 : equip.id) {
                            ResHelper_1.resHelper.loadEquipIcon(equip.id, spr, _this.key, equip.getSmeltCount());
                        }
                        else {
                            spr.spriteFrame = null;
                        }
                    }
                }
                if (isLving) {
                    isHasLving = true;
                }
            });
            bottom.Child('force_revoke_be').active = data.aIndex === mainCityIndex && !isOwner && !data.isBattleing();
            ViewHelper_1.viewHelper.updateArmyState(bottom, data, marchs[data.uid], isHasLving, data.isOwner());
            _this.updateArmyMarchSpeed(top, data);
            _this.updateArmyTreasure(bottom, data);
        });
    };
    // 行军速度
    AreaArmyPnlCtrl.prototype.updateArmyMarchSpeed = function (it, data) {
        var node = it.Child('march_speed_be'), isOneAlliance = GameHelper_1.gameHpr.checkIsOneAlliance(data.owner);
        if (node.active = isOneAlliance && !GameHelper_1.gameHpr.isNoviceMode && data.pawns.length > 0 && data.defaultMarchSpeed > 0) {
            var marchSpeedLbl = node.Component(cc.Label), line = node.Child('line'), isOwner = data.isOwner();
            node.Color(/* isOwner &&  */ data.marchSpeed === data.defaultMarchSpeed ? '#936E5A' : '#B6A591').setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            node.Component(cc.Button).interactable = isOwner;
            if (line.active = isOwner) {
                marchSpeedLbl._forceUpdateRenderData();
                line.width = marchSpeedLbl.node.width;
            }
        }
    };
    // 刷新宝箱信息
    AreaArmyPnlCtrl.prototype.updateArmyTreasure = function (it, data) {
        var node = it.Child('treasure_be'), treasureCount = data.getAllPawnTreasureCount();
        if (node.active = treasureCount > 0 && data.isOwner()) {
            node.Child('treasure', TextButtonCmpt_1.default).setKey('ui.get_treasure_count', treasureCount);
        }
    };
    AreaArmyPnlCtrl.prototype.update = function (dt) {
        this.hpBarList.forEach(function (m) {
            if (m.pawn) {
                m.bar.fillRange = m.pawn.getHpRatio();
            }
        });
    };
    AreaArmyPnlCtrl = __decorate([
        ccclass
    ], AreaArmyPnlCtrl);
    return AreaArmyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AreaArmyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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