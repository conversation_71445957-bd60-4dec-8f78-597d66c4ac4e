
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9ec9aBNxbRL7r2yUkdcDY7W', 'AreaWindCtrl');
// app/script/view/area/AreaWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var PawnCmpt_1 = require("./PawnCmpt");
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var NetEvent_1 = require("../../common/event/NetEvent");
var HPBarCmpt_1 = require("./HPBarCmpt");
var Constant_1 = require("../../common/constant/Constant");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var Enums_1 = require("../../common/constant/Enums");
var ECode_1 = require("../../common/constant/ECode");
var SearchCircle_1 = require("../../common/astar/SearchCircle");
var GameHelper_1 = require("../../common/helper/GameHelper");
var SelectCellCmpt_1 = require("../cmpt/SelectCellCmpt");
var NetHelper_1 = require("../../common/helper/NetHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var ccclass = cc._decorator.ccclass;
var AreaWindCtrl = /** @class */ (function (_super) {
    __extends(AreaWindCtrl, _super);
    function AreaWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.gridNode_ = null; // path://root/map_n/grid_n
        _this.skillDiNode_ = null; // path://root/map_n/skill_di_n
        _this.buildNode_ = null; // path://root/map_n/build_n
        _this.selectPawnNode_ = null; // path://root/select_pawn_n
        _this.roleNode_ = null; // path://root/role_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        _this.editJiantouNode_ = null; // path://root/edit_jiantou_n
        _this.weakGuideNode_ = null; // path://root/weak_guide_n
        //@end
        _this.PAWN_SIZE = cc.v2(1, 1);
        _this.diNode = null;
        _this.maskNode = null;
        _this.touchCmpt = null;
        _this.model = null;
        _this.areaCenter = null;
        _this.centre = cc.v2();
        _this.preCameraZoomRatio = 0;
        _this.areaSize = cc.v2(); //战场大小
        _this.buildSize = cc.v2(); //建筑区域
        _this.areaActSize = cc.v2(); //战场的实际大小
        _this.borderSize = cc.v2(); //地图边框宽度
        _this.buildOrigin = cc.v2(); //建筑起点
        _this.walls = []; //城墙列表
        _this.flames = []; //火焰列表
        _this.alliFlags = []; //联盟旗帜
        _this.areaOutDecorate = []; //装饰
        _this.builds = []; //建筑列表
        _this.buildMap = {};
        _this.pawns = []; //士兵列表
        _this.pawnMap = {};
        _this.wallLvNode = null;
        _this.hpBar = null; //血条
        _this.currEditBuild = null; //当前编辑的建筑
        _this.currEditPawn = null; //当前编辑的士兵
        _this.searchCircle = null;
        _this.isPawnMoveing = false; //当前是否士兵移动中
        _this.editPawns = {}; //编辑过的士兵列表
        _this.tempSeasonType = 0;
        _this._temp_vec2_0 = cc.v2();
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        return _this;
    }
    AreaWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.REENTER_AREA_WIND] = this.onReenterAreaWind, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.SHOW_BUILD_JIANTOU] = this.onShowBuildJiantou, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.LONG_PRESS_BUILD] = this.onLongPressBuild, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.MOVE_BUILD] = this.onMoveBuild, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CLICK_EDIT_BUILD_MENU] = this.onClickEditBuildMenu, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.EDIT_PAWN_POS] = this.onEditPawnPos, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.CLICK_EDIT_PAWN_MENU] = this.onClickEditPawnMenu, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.ADD_BUILD] = this.onAddBuild, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.REMOVE_BUILD] = this.onRemoveBuild, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_BUILD_POINT] = this.onUpdateBuildPoint, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_BUILDS] = this.onUpdateBuilds, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_AREA_HP] = this.onUpdateAreaHp, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.UPDATE_ANCIENT_INFO] = this.onUpdateAncientInfo, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.ADD_ARMY] = this.onAddArmy, _r.enter = true, _r),
            (_s = {}, _s[EventType_1.default.ADD_PAWN] = this.onAddPawn, _s.enter = true, _s),
            (_t = {}, _t[EventType_1.default.REMOVE_ARMY] = this.onRemoveArmy, _t.enter = true, _t),
            (_u = {}, _u[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _u.enter = true, _u),
            (_v = {}, _v[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateAllArmy, _v.enter = true, _v),
            (_w = {}, _w[EventType_1.default.REMOVE_PAWN] = this.onRemovePawn, _w.enter = true, _w),
            (_x = {}, _x[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _x.enter = true, _x),
            (_y = {}, _y[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _y.enter = true, _y),
            (_z = {}, _z[EventType_1.default.AREA_MAIN_HIT] = this.onAreaMainHit, _z.enter = true, _z),
            (_0 = {}, _0[EventType_1.default.PLAY_FLUTTER_HP] = this.onPlayFlutterHp, _0.enter = true, _0),
            (_1 = {}, _1[EventType_1.default.PLAY_FLUTTER_ANGER] = this.onPlayFlutterAnger, _1.enter = true, _1),
            (_2 = {}, _2[EventType_1.default.PLAY_BULLET_FLY] = this.onPlayBulletFly, _2.enter = true, _2),
            (_3 = {}, _3[EventType_1.default.PLAY_BATTLE_EFFECT] = this.onPlayBattleEffect, _3.enter = true, _3),
            (_4 = {}, _4[EventType_1.default.PLAY_BATTLE_SFX] = this.onPlayBattleSfx, _4.enter = true, _4),
            (_5 = {}, _5[EventType_1.default.PLAY_BATTLE_SCENE_SHAKE] = this.onPlayBattleSceneShake, _5.enter = true, _5),
            (_6 = {}, _6[EventType_1.default.FOCUS_PAWN] = this.onFocusPawn, _6.enter = true, _6),
            (_7 = {}, _7[EventType_1.default.UPDATE_BT_QUEUE] = this.onUpdateBtQueue, _7.enter = true, _7),
            (_8 = {}, _8[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdatePawnDrillQueue, _8.enter = true, _8),
            (_9 = {}, _9[EventType_1.default.UPDATE_PAWN_LVING_QUEUE] = this.onUpdatePawnDrillQueue, _9.enter = true, _9),
            (_10 = {}, _10[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdatePawnDrillQueue, _10.enter = true, _10),
            (_11 = {}, _11[EventType_1.default.CHANGE_SHOW_PAWN_LV] = this.onChangeShowPawnLv, _11.enter = true, _11),
            (_12 = {}, _12[EventType_1.default.CHANGE_SHOW_PAWN_EQUIP] = this.onChangeShowPawnEquip, _12.enter = true, _12),
            (_13 = {}, _13[EventType_1.default.CHANGE_PAWN_EQUIP] = this.onChangePawnEquip, _13.enter = true, _13),
            (_14 = {}, _14[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _14.enter = true, _14),
            (_15 = {}, _15[EventType_1.default.CHANGE_PAWN_PORTRAYAL] = this.onChangePawnPortrayal, _15.enter = true, _15),
            (_16 = {}, _16[EventType_1.default.FORGE_EQUIP_BEGIN] = this.onForgeEquipBegin, _16.enter = true, _16),
            (_17 = {}, _17[EventType_1.default.FORGE_EQUIP_COMPLETE] = this.onForgeEquipComplete, _17.enter = true, _17),
            (_18 = {}, _18[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _18.enter = true, _18),
        ];
    };
    AreaWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.setParam({ isClean: false });
                        this.areaCenter = this.getModel('areaCenter');
                        this.diNode = this.mapNode_.FindChild('di');
                        this.maskNode = this.mapNode_.FindChild('mask');
                        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                        this.selectPawnNode_.active = false;
                        this.editJiantouNode_.active = false;
                        this.gridNode_.active = false;
                        // 加载UI
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadPnl('area/AreaUI')];
                    case 1:
                        // 加载UI
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.onReady = function () {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var _c, range, count;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        _c = this;
                        return [4 /*yield*/, this.areaCenter.reqAreaByIndex((_b = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : -1)];
                    case 1:
                        _c.model = _d.sent();
                        if (!this.model) {
                            return [2 /*return*/];
                        }
                        this.areaCenter.setLookArea(this.model);
                        // 区域大小
                        this.areaSize.set(this.model.areaSize);
                        this.buildSize.set(this.model.buildSize);
                        // 获取地图边框的宽度 至少都有2格
                        this.model.getBorderSize(this.borderSize);
                        // 重新计算地图的真实大小
                        this.borderSize.mul(2, this.areaActSize).addSelf(this.areaSize);
                        // 计算建筑的起点
                        this.model.buildOrigin.add(this.borderSize, this.buildOrigin);
                        range = GameHelper_1.gameHpr.world.getMaxTileRange(), count = (range.x * 2 + 1) * (range.y * 2 + 1);
                        this.diNode.Items(count);
                        // 初始化城墙
                        return [4 /*yield*/, this.initWall()];
                    case 2:
                        // 初始化城墙
                        _d.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.onEnter = function (reenter) {
        if (!this.model) {
            ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey());
            if (GameHelper_1.gameHpr.net.isConnected()) {
                ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.UNKNOWN);
            }
            return;
        }
        this.buildNode_.Data = true;
        this.topLayerNode_.Data = true;
        this.model.setActive(true);
        this.tempSeasonType = GameHelper_1.gameHpr.world.getSeasonType();
        // 刷新宝箱红点
        this.model.updateTreasureReddot();
        // 设置中心位置
        this.areaActSize.mul(0.5, this.centre).subSelf(cc.v2(0.5, 0.5));
        // 初始化相机位置
        var zr = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO);
        CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(this.centre), this.areaActSize, cc.Vec2.ZERO, zr);
        // 绘制士兵
        this.initPawns();
        // 绘制建筑
        this.initBuilds();
        // 刷新地图
        this.updateMap(this.centre.floor());
        // UI
        if (reenter) {
            this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('area/AreaUI', this.model);
        }
        //
        this.touchCmpt.init(this.onClickMap.bind(this));
        //
        GameHelper_1.gameHpr.playAreaBgm(this.model.isBattleing());
    };
    AreaWindCtrl.prototype.onLeave = function () {
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO, CameraCtrl_1.cameraCtrl.zoomRatio);
        ViewHelper_1.viewHelper.hidePnl('area/AreaUI');
        this.touchCmpt.clean();
        GameHelper_1.gameHpr.world.setLookCell(null);
        this.clean();
        this.cleanPawns();
        ResHelper_1.resHelper.cleanNodeChildren(this.diNode);
        ResHelper_1.resHelper.cleanNodeChildren(this.maskNode);
        this.buildNode_.removeAllChildren();
        this.buildNode_.Data = false;
        this.topLayerNode_.removeAllChildren();
        this.topLayerNode_.Data = false;
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        AnimHelper_1.animHelper.clean();
        assetsMgr.releaseTempResByTag(this.key);
        audioMgr.releaseByMod('build');
        audioMgr.releaseByMod('pawn');
    };
    AreaWindCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AreaWindCtrl.prototype.onNetReconnect = function () {
        this.reinit();
    };
    AreaWindCtrl.prototype.onReenterAreaWind = function () {
        return this.reenter();
    };
    // 显示编辑家具的箭头
    AreaWindCtrl.prototype.onShowBuildJiantou = function (item, index) {
        this.editJiantouNode_.active = !!item;
        if (item) {
            this.editJiantouNode_.setPosition(item.getBodyOffsetTopPosition(68));
            this.editJiantouNode_.Component(cc.MultiFrame).setFrame(index);
        }
    };
    // 长按选中一个家具
    AreaWindCtrl.prototype.onLongPressBuild = function (item) {
        // 设置可以点击选择了
        this.builds.forEach(function (m) { return item.uid !== m.uid && m.setCanClickSelect(true); });
        this.pawns.forEach(function (m) { return m.setCanClick(false); });
        // 显示编辑UI
        this.openEditBuild(item);
    };
    // 移动建筑
    AreaWindCtrl.prototype.onMoveBuild = function (item, pos) {
        var point = item.getActPointByPixel(pos);
        this.model.amendBuildPoint(point, item.data.size); //修正一下
        item.setOffsetPositionByPoint(point);
        item.updateEditState(this.model.getBuildGroundPointMap(), point);
    };
    // 点击编辑建筑菜单
    AreaWindCtrl.prototype.onClickEditBuildMenu = function (type) {
        if (!this.currEditBuild || !this.currEditBuild.data /* || this.model.isBattleing() */) {
            return;
        }
        var item = this.currEditBuild;
        if (type === 'cancel') { //取消
            item.cancel();
        }
        else if (type === 'ok') { //确定
            if (item.editState) {
                return ViewHelper_1.viewHelper.showAlert(item.editState);
            }
            item.confirm(item.getActPointByPixel(item.getTempPosition()));
            // audioMgr.playSFX('area/sound06')
        }
        ViewHelper_1.viewHelper.hidePnl('area/EditBuild'); //隐藏编辑UI
        this.builds.forEach(function (m) { return m.setCanClickSelect(false); }); //关闭点击选择
        this.pawns.forEach(function (m) { return m.setCanClick(true); });
        item.syncZindex(); //同步zindex
        this.closeEditBuild();
    };
    // 编辑士兵
    AreaWindCtrl.prototype.onEditPawnPos = function (index, uid) {
        if (this.model.index !== index || this.model.isBattleing()) {
            return;
        }
        var pawn = this.pawns.find(function (m) { return m.uid === uid; });
        if (pawn) {
            this.builds.forEach(function (m) { return m.setCanClick(false); });
            this.pawns.forEach(function (m) { return m.setCanClick(false); });
            this.maskNode.children.forEach(function (m) { return m.active = !!m.Data; });
            // 行动中
            pawn.data.actioning = true;
            // 显示pnl
            ViewHelper_1.viewHelper.showPnl('area/EditPawn', pawn);
            this.currEditPawn = pawn;
            this.selectPawnNode_.Component(SelectCellCmpt_1.default).open(this.currEditPawn.getTempPosition(), this.PAWN_SIZE);
        }
    };
    // 点击编辑士兵的菜单
    AreaWindCtrl.prototype.onClickEditPawnMenu = function (type) {
        if (!this.currEditPawn || !this.currEditPawn.data || this.model.isBattleing()) {
            ViewHelper_1.viewHelper.hidePnl('area/EditPawn');
            this.builds.forEach(function (m) { return m.setCanClick(true); });
            this.pawns.forEach(function (m) { return m.setCanClick(true); });
            this.maskNode.children.forEach(function (m) { return m.active = false; });
            this.closeEditPawn();
        }
        else if (type === 'gather') { //集合
            this.gatherPawn();
        }
        else if (type === 'ok') {
            this.editPawnEnd();
        }
    };
    // 添加建筑
    AreaWindCtrl.prototype.onAddBuild = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            var isOwner_1 = this.model.isOwner();
            this.createBuild(data).then(function (item) {
                if (item && _this.isActive() && isOwner_1) {
                    var body = item.getBody();
                    // 摄像机移动到这个位置来
                    CameraCtrl_1.cameraCtrl.setTargetOnce(body);
                    // 扫光
                    // animHelper.playFlashLight([body])
                }
            });
        }
    };
    // 删除建筑
    AreaWindCtrl.prototype.onRemoveBuild = function (data) {
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            var build = this.builds.remove('uid', data.uid);
            if (build) {
                this.cleanBuild(build);
                assetsMgr.releaseTempRes(data.getPrefabUrl(), this.key);
            }
        }
    };
    // 刷新建筑等级
    AreaWindCtrl.prototype.onUpdateBuildLv = function (data) {
        var _a, _b;
        if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
        }
        else if (data.uid === this.model.wall.uid) {
            this.updateWallLv(data.lv);
        }
        else {
            (_b = this.builds.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.updateLv(data.lv);
        }
    };
    // 刷新建筑位置
    AreaWindCtrl.prototype.onUpdateBuildPoint = function (data) {
        var _a, _b;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            (_b = this.builds.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.syncPoint();
        }
    };
    // 刷新血量
    AreaWindCtrl.prototype.onUpdateAreaHp = function (index) {
        var _a, _b;
        if (index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        }
    };
    // 刷新遗迹信息
    AreaWindCtrl.prototype.onUpdateAncientInfo = function (data) {
        var _a;
        if (data.index !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            return;
        }
        var build = this.builds.find(function (m) { return m.data.isAncient(); });
        if (build) {
            build.updateLv(data.lv);
            build.updateUpLvAnim();
        }
    };
    // 刷新城市
    AreaWindCtrl.prototype.onUpdateBuilds = function (index) {
        var _a;
        if (index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            this.initBuilds();
        }
    };
    // 添加军队
    AreaWindCtrl.prototype.onAddArmy = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            data.pawns.forEach(function (m) { return _this.createPawn(m); });
        }
    };
    // 删除军队
    AreaWindCtrl.prototype.onRemoveArmy = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            data.pawns.forEach(function (m) {
                var i = _this.pawns.findIndex(function (p) { var _a; return p.uid === m.uid && ((_a = p.data) === null || _a === void 0 ? void 0 : _a.armyUid) === data.uid; });
                if (i !== -1) {
                    _this.cleanPawn(_this.pawns.splice(i, 1)[0], true);
                }
            });
        }
    };
    // 更新军队信息
    AreaWindCtrl.prototype.onUpdateArmy = function (data) {
        var _this = this;
        var _a, _b;
        if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            return;
        }
        // 先删除没有的
        for (var i = this.pawns.length - 1; i >= 0; i--) {
            var m = this.pawns[i];
            if (((_b = m.data) === null || _b === void 0 ? void 0 : _b.armyUid) !== data.uid) {
                continue;
            }
            else if (!data.pawns.has('uid', m.uid)) {
                this.pawns.splice(i, 1);
                this.cleanPawn(m, true);
            }
        }
        data.pawns.forEach(function (m) { return _this.createPawn(m); });
    };
    // 更新所有军队
    AreaWindCtrl.prototype.onUpdateAllArmy = function (index) {
        if (this.model.index === index) {
            this.initPawns();
        }
    };
    // 添加士兵
    AreaWindCtrl.prototype.onAddPawn = function (index, data) {
        if (this.model.index === index) {
            this.createPawn(data);
        }
    };
    // 删除士兵
    AreaWindCtrl.prototype.onRemovePawn = function (index, uid) {
        if (this.model.index === index) {
            this.cleanPawn(this.pawns.remove('uid', uid), true);
        }
    };
    // 战斗开始
    AreaWindCtrl.prototype.onAreaBattleBegin = function (index) {
        var _a, _b;
        if (this.model.index !== index) {
            return;
        }
        // 关闭当前正在编辑的建筑
        this.checkConfirmEditBuild();
        this.closeEditBuild();
        // 关闭当前正在编辑的士兵
        (_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.cancel();
        this.closeEditPawn();
        // 初始化血量
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        // 初始化士兵
        this.initPawns();
        // 战斗时间
        this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        //
        GameHelper_1.gameHpr.playAreaBgm(true);
    };
    // 战斗结束
    AreaWindCtrl.prototype.onAreaBattleEnd = function (index) {
        var _a, _b;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) !== index) {
            return;
        }
        else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
            return this.reenter();
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        this.initBuilds(true);
        this.initPawns();
        // 战斗时间
        this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        //
        GameHelper_1.gameHpr.playAreaBgm(false);
    };
    // 受到伤害
    AreaWindCtrl.prototype.onAreaMainHit = function (data) {
        var _a, _b;
        if (data.index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            if (this.model.isBattleing()) {
                (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.play();
            }
            AnimHelper_1.animHelper.playFlutterHp({ type: 'isDamage', value: data.value }, this.topLayerNode_, this.getPixelByPoint(data.point), this.key);
        }
    };
    // 播放飘血
    AreaWindCtrl.prototype.onPlayFlutterHp = function (data) {
        var _a, _b;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            var pos = data.point ? this.getPixelByPoint(data.point).clone() : (_b = this.pawns.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.getPosition();
            if (pos) {
                AnimHelper_1.animHelper.readyPlayFlutterHp(data, pos, this.topLayerNode_, this.key);
            }
        }
    };
    // 播放增加怒气
    AreaWindCtrl.prototype.onPlayFlutterAnger = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            var pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
            if (pawn) {
                AnimHelper_1.animHelper.playFlutterAnger(data.value, this.topLayerNode_, pawn.getPosition(), this.key);
            }
        }
    };
    // 播放子弹飞行
    AreaWindCtrl.prototype.onPlayBulletFly = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            data.startPos = this.getPixelByPoint(data.startPoint).clone();
            data.targetPos = this.getPixelByPoint(data.targetPoint).clone();
            AnimHelper_1.animHelper.playBulletFly(data, this.topLayerNode_, this.key);
        }
    };
    // 播放战斗特效
    AreaWindCtrl.prototype.onPlayBattleEffect = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            data.pos = this.getPixelByPoint(data.point).clone();
            var root = this.skillDiNode_;
            if (data.root === 'top') {
                root = this.topLayerNode_;
            }
            else if (data.root === 'role') {
                root = this.roleNode_;
                data.zIndex = (Constant_1.AREA_MAX_ZINDEX - (data.pos.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10 + 3;
            }
            AnimHelper_1.animHelper.playBattleEffect(data, root, this.key);
        }
    };
    // 播放音效
    AreaWindCtrl.prototype.onPlayBattleSfx = function (index, url, data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            audioMgr.playSFX(url, data);
        }
    };
    // 播放屏幕抖动
    AreaWindCtrl.prototype.onPlayBattleSceneShake = function (index, time) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            CameraCtrl_1.cameraCtrl.shake(time);
        }
    };
    // 聚焦士兵
    AreaWindCtrl.prototype.onFocusPawn = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) !== (data === null || data === void 0 ? void 0 : data.index)) {
            return;
        } /*  else if (cameraCtrl.getNoDragTime() < 5000) {
            return //多久没拖动才可以聚焦
        } */
        var pos = this.getPixelByPoint(data.point);
        if (CameraCtrl_1.cameraCtrl.isInScreenRangeByWorld(pos)) {
            CameraCtrl_1.cameraCtrl.moveTo(0.5, pos, true);
        }
    };
    // 刷新修建队列
    AreaWindCtrl.prototype.onUpdateBtQueue = function () {
        this.builds.forEach(function (m) { return m.updateUpLvAnim(); });
    };
    // 刷新训练队列
    AreaWindCtrl.prototype.onUpdatePawnDrillQueue = function (index) {
        if (this.model.index === index) {
            this.builds.forEach(function (m) { return m.updateDrillPawn(); });
        }
    };
    // 切换显示士兵等级
    AreaWindCtrl.prototype.onChangeShowPawnLv = function (val) {
        this.pawns.forEach(function (m) { return m.showPawnLv(val); });
    };
    // 切换显示士兵装备
    AreaWindCtrl.prototype.onChangeShowPawnEquip = function (val) {
        this.pawns.forEach(function (m) { return m.showPawnEquip(val); });
    };
    // 切换士兵装备
    AreaWindCtrl.prototype.onChangePawnEquip = function (data) {
        var _a;
        (_a = this.pawns.find(function (m) { return m.uid === data.uid; })) === null || _a === void 0 ? void 0 : _a.updateShowPawnEquip();
    };
    // 切换士兵皮肤
    AreaWindCtrl.prototype.onChangePawnSkin = function (data) {
        var i = this.pawns.findIndex(function (m) { return m.uid === data.uid; });
        if (i !== -1) {
            var pawn = this.pawns[i];
            if (pawn.curSkinId !== data.skinId) {
                this.pawns.splice(i, 1);
                this.cleanPawn(pawn);
                this.createPawn(data);
            }
        }
        this.builds.forEach(function (m) { return m.updateDrillPawn(); });
    };
    // 化身英雄
    AreaWindCtrl.prototype.onChangePawnPortrayal = function (data) {
        var _this = this;
        if (!data.portrayal) {
            return;
        }
        var i = this.pawns.findIndex(function (m) { return m.uid === data.uid; });
        if (i !== -1) {
            var pawn_1 = this.pawns[i];
            if (pawn_1.curPortrayalId !== data.portrayal.id) {
                pawn_1.playAvatarHeroAnim(data.portrayal.id).then(function () {
                    if (_this.isActive()) {
                        _this.pawns.splice(i, 1);
                        _this.cleanPawn(pawn_1);
                        _this.createPawn(data);
                    }
                });
            }
        }
    };
    // 打造装备开始
    AreaWindCtrl.prototype.onForgeEquipBegin = function () {
        var _a;
        (_a = this.builds.find(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID; })) === null || _a === void 0 ? void 0 : _a.updateForgeEquip();
    };
    // 打造装备完成
    AreaWindCtrl.prototype.onForgeEquipComplete = function () {
        var _a;
        (_a = this.builds.find(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID; })) === null || _a === void 0 ? void 0 : _a.updateForgeEquip();
    };
    // 若引导
    AreaWindCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'area') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AreaWindCtrl.prototype.isActive = function () { var _a; return this.isValid && !!((_a = this.model) === null || _a === void 0 ? void 0 : _a.active); };
    AreaWindCtrl.prototype.getPixelByPoint = function (point) {
        return point && MapHelper_1.mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_3));
    };
    AreaWindCtrl.prototype.getPointByPixel = function (pixel) {
        return pixel && MapHelper_1.mapHelper.getPointByPixel(pixel).subSelf(this.borderSize);
    };
    AreaWindCtrl.prototype.getBuildPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.buildOrigin, this._temp_vec2_1), this._temp_vec2_1);
    };
    AreaWindCtrl.prototype.clean = function () {
        var _a;
        this.areaCenter.setLookArea(null);
        (_a = this.model) === null || _a === void 0 ? void 0 : _a.setActive(false);
        this.model = null;
        GameHelper_1.gameHpr.cleanPawnAstarMap();
        this.searchCircle = null;
        this.cleanWalls();
        this.cleanFlames();
        this.cleanAlliFlags();
        this.cleanAreaOutDecorate();
        this.cheanBuilds();
        // this.cleanPawns()
        this.closeEditBuild();
        this.closeEditPawn();
    };
    // 重连之后的初始化
    AreaWindCtrl.prototype.reinit = function () {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var world, _e;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        GameHelper_1.gameHpr.cleanPawnAstarMap();
                        // 关闭当前正在编辑的建筑
                        this.checkConfirmEditBuild();
                        this.closeEditBuild();
                        // 关闭当前正在编辑的士兵
                        (_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.cancel();
                        this.closeEditPawn();
                        world = GameHelper_1.gameHpr.world;
                        _e = this;
                        return [4 /*yield*/, this.areaCenter.reqAreaByIndex((_c = (_b = world.getLookCell()) === null || _b === void 0 ? void 0 : _b.index) !== null && _c !== void 0 ? _c : -1, true)];
                    case 1:
                        _e.model = _f.sent();
                        if (!this.model) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.gotoWind(world.getSceneKey())];
                        }
                        else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
                            return [2 /*return*/, this.reenter()];
                        }
                        this.model.setActive(true);
                        // 刷新地图
                        this.updateMap(this.centre.floor());
                        // 城墙等级
                        this.model.wall && this.updateWallLv(this.model.wall.lv);
                        // 血条
                        (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.init(this.model);
                        // 绘制建筑
                        this.initBuilds();
                        // 绘制士兵
                        this.initPawns();
                        // 战斗时间
                        this.emit(EventType_1.default.REINIT_AREA_UI, this.model);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 重新绘制
    AreaWindCtrl.prototype.reenter = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.emit(mc.Event.READY_BEGIN_WIND);
                        this.clean();
                        return [4 /*yield*/, this.onReady()];
                    case 1:
                        _a.sent();
                        this.emit(mc.Event.READY_END_WIND);
                        this.onEnter(true);
                        this.emit(EventType_1.default.UPDATE_REENTER_AREA);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化城墙
    AreaWindCtrl.prototype.initWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb, node, size;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanWalls();
                        if (!!this.model.isBoss()) return [3 /*break*/, 6];
                        return [4 /*yield*/, assetsMgr.loadTempRes('wall/WALL_HP_BAR', cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb || !this.isValid) {
                            return [2 /*return*/];
                        }
                        node = cc.instantiate2(pfb, this.roleNode_);
                        this.hpBar = node.addComponent(HPBarCmpt_1.default).init(this.model);
                        size = this.model.buildSize;
                        ViewHelper_1.viewHelper.drawGrid(this.gridNode_.Component(cc.Graphics), cc.v2(size.x - 2, size.y - 2), cc.v2(this.buildOrigin.x + 1, this.buildOrigin.y + 1));
                        this.gridNode_.active = false;
                        if (!this.model.wall) return [3 /*break*/, 5];
                        if (!!this.model.isAncient()) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.createWall()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (!(this.model.wall.lv > 0)) return [3 /*break*/, 5];
                        return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, this.key)];
                    case 4:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = this.wallLvNode = cc.instantiate2(pfb, this.roleNode_);
                            this.updateWallLv(this.model.wall.lv);
                        }
                        _a.label = 5;
                    case 5:
                        this.updateWallHpPosition();
                        _a.label = 6;
                    case 6: 
                    // 创建区域外的装饰
                    return [4 /*yield*/, this.createAreaOutDecorate()
                        // 创建联盟旗帜
                    ];
                    case 7:
                        // 创建区域外的装饰
                        _a.sent();
                        // 创建联盟旗帜
                        return [4 /*yield*/, this.createAlliFlags(GameHelper_1.gameHpr.getPlayerAlliIcon(this.model.owner))];
                    case 8:
                        // 创建联盟旗帜
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.createWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var skinId;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        while (this.walls.length > 0) {
                            this.walls.pop().destroy();
                        }
                        this.walls.length = 0;
                        skinId = 0;
                        return [4 /*yield*/, Promise.all(this.model.walls.map(function (m) { return __awaiter(_this, void 0, void 0, function () {
                                var url, pfb, node;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            url = "wall/" + skinId + "/WALL_" + skinId + "_" + m.type + "_" + m.dir;
                                            if (m.index) {
                                                url += '_' + m.index;
                                            }
                                            return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                                        case 1:
                                            pfb = _a.sent();
                                            if (pfb && this.isValid) {
                                                node = cc.instantiate2(pfb, this.buildNode_);
                                                node.Data = this.model.wall;
                                                node.setPosition(MapHelper_1.mapHelper.getPixelByPoint(m.point.add(this.buildOrigin, this._temp_vec2_1)));
                                                node.addComponent(ClickTouchCmpt_1.default).on(this.onClickWall, this);
                                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                                this.walls.push(node);
                                            }
                                            return [2 /*return*/];
                                    }
                                });
                            }); }))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanWalls = function () {
        var _a, _b;
        (_a = this.wallLvNode) === null || _a === void 0 ? void 0 : _a.destroy();
        this.wallLvNode = null;
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.clean();
        this.hpBar = null;
        while (this.walls.length > 0) {
            this.walls.pop().destroy();
        }
        this.walls.length = 0;
    };
    // 初始化火焰
    AreaWindCtrl.prototype.createFlames = function () {
        return __awaiter(this, void 0, void 0, function () {
            var flames, pfb;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        flames = this.model.flames;
                        if (this.flames.length === flames.length) {
                            return [2 /*return*/, flames.forEach(function (point, i) {
                                    var node = _this.flames[i], pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                    node.setPosition(pos.x - 2, pos.y + 26);
                                    node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                })];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes('build/FLAME', cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            flames.forEach(function (point) {
                                var node = cc.instantiate2(pfb, _this.roleNode_), pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                node.setPosition(pos.x - 2, pos.y + 26);
                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                _this.flames.push(node);
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanFlames = function () {
        while (this.flames.length > 0) {
            this.flames.pop().destroy();
        }
        this.flames.length = 0;
    };
    // 创建联盟旗帜
    AreaWindCtrl.prototype.createAlliFlags = function (icon) {
        return __awaiter(this, void 0, void 0, function () {
            var points, pfb;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!icon) {
                            return [2 /*return*/, this.cleanAlliFlags()];
                        }
                        points = this.model.alliFlags;
                        if (this.alliFlags.length === points.length && this.alliFlags[0].Data === icon) {
                            return [2 /*return*/, points.forEach(function (point, i) {
                                    var node = _this.alliFlags[i], pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                    node.setPosition(pos);
                                    node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                })];
                        }
                        this.cleanAlliFlags();
                        return [4 /*yield*/, assetsMgr.loadTempRes('alli_flag/ALLI_FLAG_' + icon, cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            points.forEach(function (point) {
                                var node = cc.instantiate2(pfb, _this.roleNode_), pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                node.Child('body').y = -28;
                                node.setPosition(pos);
                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                _this.alliFlags.push(node);
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanAlliFlags = function () {
        while (this.alliFlags.length > 0) {
            this.alliFlags.pop().destroy();
        }
        this.alliFlags.length = 0;
    };
    // 创建区域外的装饰
    AreaWindCtrl.prototype.createAreaOutDecorate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var seasonType, cell, drawType, url, pfb, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanAreaOutDecorate();
                        seasonType = 0;
                        cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.model.index), drawType = cell.getLandDrawType();
                        url = "area_decorate/" + seasonType + "/AREA_DECORATE_" + drawType;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = cc.instantiate2(pfb, this.mapNode_.Child('bg'));
                            node.setPosition(this.borderSize.x * Constant_1.TILE_SIZE, this.borderSize.y * Constant_1.TILE_SIZE);
                            this.areaOutDecorate.push(node);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanAreaOutDecorate = function () {
        while (this.areaOutDecorate.length > 0) {
            this.areaOutDecorate.pop().destroy();
        }
        this.areaOutDecorate.length = 0;
    };
    // 初始化建筑
    AreaWindCtrl.prototype.initBuilds = function (playOccupyEffect) {
        return __awaiter(this, void 0, void 0, function () {
            var builds, i;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.buildMap = {};
                        builds = this.model.builds;
                        builds.forEach(function (m) { return _this.buildMap[m.uid] = 1; });
                        for (i = this.builds.length - 1; i >= 0; i--) {
                            if (!this.buildMap[this.builds[i].uid]) {
                                this.cleanBuild(this.builds.splice(i, 1)[0]);
                            }
                        }
                        return [4 /*yield*/, Promise.all(builds.map(function (m) { return _this.createBuild(m); }))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.createBuild = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var build, pfb;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        build = this.builds.find(function (m) { return m.uid === data.uid; });
                        if (build) {
                            this.buildMap[data.uid] = 2;
                            return [2 /*return*/, build.resync(data, this.model.owner)];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        else if (this.buildMap[data.uid] === 2) {
                            return [2 /*return*/, null]; //防止重复创建或创建没有的
                        }
                        else if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
                            return [2 /*return*/, null];
                        }
                        this.buildMap[data.uid] = 2;
                        build = cc.instantiate2(pfb, this.roleNode_).getComponent(BaseBuildCmpt_1.default).init(data, this.buildOrigin, this.borderSize.y * Constant_1.TILE_SIZE, this.model.owner);
                        this.builds.push(build);
                        return [2 /*return*/, build];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cheanBuilds = function () {
        while (this.builds.length > 0) {
            this.builds.pop().clean();
        }
        this.buildMap = {};
    };
    AreaWindCtrl.prototype.cleanBuild = function (data) {
        if (data) {
            data.clean();
            delete this.buildMap[data.uid];
        }
    };
    // 刷新墙的等级
    AreaWindCtrl.prototype.updateWallLv = function (lv) {
        if (this.wallLvNode) {
            this.wallLvNode.Child('val', cc.Label).string = '' + lv;
        }
    };
    // 刷新血条位置
    AreaWindCtrl.prototype.updateWallHpPosition = function () {
        if (this.hpBar) {
            var node = this.hpBar.node, pos = cc.v2();
            if (this.model.cityId === Constant_1.CITY_MAIN_NID) {
                pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + 25);
            }
            else if (this.model.isAncient()) {
                pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + 25);
            }
            else {
                pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
                node.setPosition(pos.x, pos.y + 47);
            }
            node.zIndex = (Constant_1.AREA_MAX_ZINDEX - (pos.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10 + 1;
        }
        if (this.wallLvNode) {
            var pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
            this.wallLvNode.setPosition(pos.x, pos.y + 16);
            this.wallLvNode.zIndex = this.hpBar.node.zIndex + 1;
        }
    };
    // 打开编辑建筑
    AreaWindCtrl.prototype.openEditBuild = function (item) {
        // 显示pnl
        ViewHelper_1.viewHelper.showPnl('area/EditBuild');
        this.gridNode_.active = true;
        // 如果之前有就放下
        this.checkConfirmEditBuild();
        this.currEditBuild = item;
        // 刷新一下地面点
        this.model.updateBuildGroundPoints(item.data);
        // 刷新编辑状态
        item.updateEditState(this.model.getBuildGroundPointMap(), item.point);
    };
    // 如果有建筑在编辑状态 就放下
    AreaWindCtrl.prototype.checkConfirmEditBuild = function () {
        if (!this.isEditBuildState()) {
        }
        else if (this.currEditBuild.editState) {
            this.currEditBuild.cancel();
        }
        else {
            this.currEditBuild.confirm(this.currEditBuild.getActPointByPixel(this.currEditBuild.getTempPosition()));
        }
    };
    // 关闭编辑建筑
    AreaWindCtrl.prototype.closeEditBuild = function () {
        this.currEditBuild = null;
        this.editJiantouNode_.active = false;
        this.gridNode_.active = false;
    };
    // 关闭编辑士兵
    AreaWindCtrl.prototype.closeEditPawn = function () {
        var _a;
        for (var key in this.editPawns) {
            var pawn = this.editPawns[key], state = (_a = pawn.data) === null || _a === void 0 ? void 0 : _a.getState();
            if (state && state < Enums_1.PawnState.STAND) {
                pawn.data.changeState(Enums_1.PawnState.NONE);
            }
        }
        this.editPawns = {};
        this.currEditPawn = null;
        this.selectPawnNode_.Component(SelectCellCmpt_1.default).close();
        this.isPawnMoveing = false;
    };
    AreaWindCtrl.prototype.cleanPawns = function () {
        while (this.pawns.length > 0) {
            this.pawns.pop().clean();
        }
        this.pawnMap = {};
    };
    // 初始化士兵
    AreaWindCtrl.prototype.initPawns = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawns, uidMap, i, m;
            var _this = this;
            return __generator(this, function (_a) {
                pawns = this.model.getAllPawns(), uidMap = {};
                pawns.forEach(function (m) { return uidMap[m.getAbsUid()] = true; });
                for (i = this.pawns.length - 1; i >= 0; i--) {
                    m = this.pawns[i];
                    if (!uidMap[m.getAbsUid()] || !m.data || m.data.isDie()) {
                        this.cleanPawn(this.pawns.splice(i, 1)[0]);
                    }
                }
                return [2 /*return*/, Promise.all(pawns.map(function (m) { return _this.createPawn(m); }))];
            });
        });
    };
    AreaWindCtrl.prototype.createPawn = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var pawn, pfb, uid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isActive() || !data) {
                            return [2 /*return*/, null];
                        }
                        pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
                        if (!pawn) {
                        }
                        else if (pawn.curSkinId !== data.skinId || pawn.curPortrayalId !== data.getPortrayalId()) {
                            this.pawns.remove('uid', data.uid);
                            this.cleanPawn(pawn);
                        }
                        else if (data.isDie()) {
                            this.pawns.remove('uid', data.uid);
                            this.cleanPawn(pawn);
                            return [2 /*return*/, null];
                        }
                        else {
                            return [2 /*return*/, pawn.resync(data)];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        else if (this.pawnMap[data.uid]) {
                            return [2 /*return*/, null]; //防止多次创建
                        }
                        else if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
                            return [2 /*return*/, null];
                        }
                        uid = data.uid;
                        data = this.model.getPawn(uid) || this.model.getBattleTempPawn(uid);
                        if (!data || data.isDie()) {
                            return [2 /*return*/, null];
                        }
                        pawn = cc.instantiate2(pfb, this.roleNode_).getComponent(PawnCmpt_1.default).init(data, this.borderSize, this.key);
                        this.pawns.push(pawn);
                        this.pawnMap[data.uid] = pawn;
                        return [2 /*return*/, pawn];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanPawn = function (pawn, release) {
        if (pawn) {
            delete this.pawnMap[pawn.uid];
            pawn.clean(release);
        }
    };
    // 是否编辑建筑中
    AreaWindCtrl.prototype.isEditBuildState = function () {
        var _a;
        return !!((_a = this.currEditBuild) === null || _a === void 0 ? void 0 : _a.data);
    };
    // 是否编辑小兵中
    AreaWindCtrl.prototype.isEditPawnState = function () {
        var _a;
        return !!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data);
    };
    // 绘制地图
    AreaWindCtrl.prototype.updateMap = function (centre) {
        var _this = this;
        var seasonType = /* gameHpr.world.getSeasonType() */ 0;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.model.index), seasonColorConf = Constant_1.AREA_DI_COLOR_CONF[seasonType];
        var colorConf = seasonColorConf[cell.getLandDrawType()] || seasonColorConf[0];
        var areaSize = this.model.areaSize, oYindex = (areaSize.x + 1) % 2;
        // 设置整个背景颜色
        CameraCtrl_1.cameraCtrl.setBgColor(colorConf.bg);
        //
        // this.preCameraZoomRatio = cameraCtrl.zoomRatio
        // this.centre.set(centre)
        var buildOrigin = this.model.buildOrigin, buildSize = this.model.buildSize;
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, GameHelper_1.gameHpr.world.getMaxTileRange());
        var isBoss = this.model.isBoss();
        var mi = 0;
        this.diNode.Items(points, function (it, point, i) {
            var x = point.x - _this.borderSize.x, y = point.y - _this.borderSize.y;
            var bx = x - buildOrigin.x, by = y - buildOrigin.y;
            var index = it.Data = MapHelper_1.mapHelper.pointToIndexByNumer(x, y, areaSize);
            var id = x + '_' + y;
            it.setPosition(MapHelper_1.mapHelper.getPixelByPoint(point, _this._temp_vec2_0));
            if (MapHelper_1.mapHelper.isBorder(x, y, areaSize)) { //边界外
                it.Component(cc.Sprite).spriteFrame = null;
            }
            else if (MapHelper_1.mapHelper.isBorder(bx, by, buildSize)) { //战斗区域
                var idx = y % 2 === 0 ? index : index + oYindex;
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                it.Color(colorConf.battle[Number(idx % 2 !== 0)]);
                if (_this.model.banPlacePawnPointMap[id]) {
                    var mn = ResHelper_1.resHelper.getNodeByIndex(_this.maskNode, mi++, _this._temp_vec2_0);
                    mn.Data = true;
                    mn.active = _this.isEditPawnState();
                }
            }
            else if (!isBoss) { //建筑区域
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                if (bx === 0 || bx === buildSize.x - 1 || by === 0 || by === buildSize.y - 1) {
                    it.Color(colorConf.build);
                }
                else if (cell.isMainCity() || cell.isAncient()) {
                    it.Color('#D6DBAA');
                }
            }
            else if (bx === 1 && by === 1) { //boss位置
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('land_area_boss_' + cell.landType);
            }
            else {
                it.Component(cc.Sprite).spriteFrame = null;
            }
        });
        // 隐藏多余的
        ResHelper_1.resHelper.hideNodeByIndex(this.maskNode, mi);
    };
    AreaWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandItemIcon(icon, this.tempSeasonType);
    };
    // 点击地图
    AreaWindCtrl.prototype.onClickMap = function (worldLocation) {
        if (!this.model || !worldLocation) {
            return;
        }
        var point = this.getPointByPixel(worldLocation);
        if (!point) {
            return;
        }
        else if (MapHelper_1.mapHelper.isBorder(point.x, point.y, this.model.areaSize)) {
            return;
        }
        // 是否点击的建筑区域
        var bpoint = point.sub(this.model.buildOrigin, this._temp_vec2_1);
        if (MapHelper_1.mapHelper.isBorder(bpoint.x, bpoint.y, this.model.buildSize)) {
            this.onClickMapArea(point);
        }
        else {
            this.onClickBuildArea(bpoint);
        }
    };
    // 点击地图区域
    AreaWindCtrl.prototype.onClickMapArea = function (point) {
        if (!this.isEditPawnState() || this.isEditBuildState() || this.isPawnMoveing) {
            return;
        }
        var uid = this.currEditPawn.uid;
        if (!this.model.banPlacePawnPointMap[point.ID()] && !this.pawns.some(function (m) { return m.uid !== uid && m.getActPoint().equals(point); })) {
            this.selectPawnNode_.Component(SelectCellCmpt_1.default).open(this.getPixelByPoint(point), this.PAWN_SIZE);
            this.movePawn(point);
        }
    };
    // 点击建筑区域
    AreaWindCtrl.prototype.onClickBuildArea = function (point) {
        if (!this.isEditBuildState() || this.isEditPawnState()) {
            return;
        }
        else if (!this.model.isBuildBorder(point)) {
            this.currEditBuild.setOffsetPositionByPoint(point);
            this.currEditBuild.updateEditState(this.model.getBuildGroundPointMap(), point);
        }
    };
    // 点击墙
    AreaWindCtrl.prototype.onClickWall = function () {
        if (!this.model.wall || this.isEditBuildState() || this.isEditPawnState()) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.model.isAncient()) {
            var build = this.model.getBuildById(this.model.cityId);
            if (!build) {
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(this.model.owner)) {
                ViewHelper_1.viewHelper.showPnl(build.getUIUrl(), build);
            }
            else {
                ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', build);
            }
        }
        else if (this.model.isOwner()) {
            ViewHelper_1.viewHelper.showPnl(this.model.wall.getUIUrl(), this.model.wall);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildCity', this.model.wall);
        }
    };
    AreaWindCtrl.prototype.getSearchCircle = function () {
        if (!this.searchCircle) {
            var model_1 = this.model;
            this.searchCircle = new SearchCircle_1.default().init(function (x, y) { return model_1.checkIsBattleArea(x, y) && !model_1.banPlacePawnPointMap[x + '_' + y]; });
        }
        return this.searchCircle;
    };
    // 移动士兵
    AreaWindCtrl.prototype.movePawn = function (point) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.currEditPawn) return [3 /*break*/, 2];
                        this.isPawnMoveing = true;
                        this.emit(EventType_1.default.EDIT_PAWN_MOVEING, true);
                        return [4 /*yield*/, this.movePawnOne(this.currEditPawn, point)];
                    case 1:
                        _a.sent();
                        if (this.isActive()) {
                            this.isPawnMoveing = false;
                            this.emit(EventType_1.default.EDIT_PAWN_MOVEING, false);
                        }
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 移动单个士兵
    AreaWindCtrl.prototype.movePawnOne = function (pawn, point) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var data, sp, area, as, points, time;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!(pawn === null || pawn === void 0 ? void 0 : pawn.data) || ((_a = pawn.data) === null || _a === void 0 ? void 0 : _a.getState()) === Enums_1.PawnState.EDIT_MOVE) {
                            return [2 /*return*/];
                        }
                        data = pawn.data;
                        sp = pawn.getActPoint();
                        area = this.model, as = GameHelper_1.gameHpr.getPawnASatr(data.uid).init(function (x, y) { return area.checkIsBattleArea(x, y) && !area.banPlacePawnPointMap[x + '_' + y]; });
                        return [4 /*yield*/, as.search(sp, point)];
                    case 1:
                        points = _b.sent();
                        if (!this.isActive() || points.length === 0) {
                            return [2 /*return*/];
                        }
                        else if (!this.editPawns[pawn.uid]) {
                            this.editPawns[pawn.uid] = pawn;
                        }
                        time = MapHelper_1.mapHelper.getMoveNeedTime(points, 400);
                        data.changeState(Enums_1.PawnState.EDIT_MOVE, { paths: points, needMoveTime: time });
                        return [4 /*yield*/, ut.wait(time * 0.001)];
                    case 2:
                        _b.sent();
                        if (data.getState() < Enums_1.PawnState.STAND) {
                            data.changeState(Enums_1.PawnState.NONE);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 集合士兵
    AreaWindCtrl.prototype.gatherPawn = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var pawn, uid, armyUid, point, pawns, otherPawns, count, points;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data)) {
                            return [2 /*return*/];
                        }
                        pawn = this.currEditPawn;
                        uid = this.currEditPawn.uid;
                        armyUid = this.currEditPawn.data.armyUid;
                        point = pawn.getActPoint();
                        pawns = [], otherPawns = {};
                        this.pawns.forEach(function (m) {
                            var _a;
                            if (m.uid === uid) {
                            }
                            else if (((_a = m.data) === null || _a === void 0 ? void 0 : _a.armyUid) === armyUid) {
                                pawns.push(m);
                            }
                            else {
                                otherPawns[m.point.ID()] = true;
                            }
                        });
                        count = pawns.length;
                        if (count === 0) {
                            return [2 /*return*/];
                        }
                        points = this.getSearchCircle().search(point, count, otherPawns);
                        // 删除已经在这个位置的士兵
                        points.delete(function (m) {
                            var i = pawns.findIndex(function (p) { return p.getActPoint().equals(m); });
                            if (i !== -1) {
                                pawns.splice(i, 1);
                                return true;
                            }
                            return false;
                        });
                        if (points.length === 0) {
                            return [2 /*return*/];
                        }
                        this.isPawnMoveing = true;
                        this.emit(EventType_1.default.EDIT_PAWN_MOVEING, true);
                        return [4 /*yield*/, Promise.all(points.map(function (m, i) { return _this.movePawnOne(pawns[i], m); }))];
                    case 1:
                        _b.sent();
                        if (this.isActive()) {
                            this.isPawnMoveing = false;
                            this.emit(EventType_1.default.EDIT_PAWN_MOVEING, false);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 保存编辑士兵的信息
    AreaWindCtrl.prototype.editPawnEnd = function () {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var info, pawns, moveInfos, key, pawn, point, _c, err, data, key;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        if (!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data) || this.isPawnMoveing) {
                            return [2 /*return*/];
                        }
                        info = this.currEditPawn.data;
                        pawns = [];
                        moveInfos = [];
                        for (key in this.editPawns) {
                            pawn = this.editPawns[key];
                            point = pawn.getActPoint();
                            moveInfos.push({
                                uid: pawn.uid,
                                point: point.toJson()
                            });
                            if (!((_b = pawn.point) === null || _b === void 0 ? void 0 : _b.equals(point))) {
                                pawns.push({ uid: pawn.uid, point: point.toJson() });
                            }
                        }
                        if (!(pawns.length > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, NetHelper_1.netHelper.reqMoveAreaPawns({ index: info.aIndex, armyUid: info.armyUid, pawns: moveInfos })];
                    case 1:
                        _c = _d.sent(), err = _c.err, data = _c.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (!this.isActive()) {
                            return [2 /*return*/];
                        }
                        _d.label = 2;
                    case 2:
                        ViewHelper_1.viewHelper.hidePnl('area/EditPawn');
                        for (key in this.editPawns) {
                            this.editPawns[key].confirm();
                        }
                        this.currEditPawn.data.actioning = false;
                        this.builds.forEach(function (m) { return m.setCanClick(true); });
                        this.pawns.forEach(function (m) { return m.setCanClick(true); });
                        this.maskNode.children.forEach(function (m) { return m.active = false; });
                        this.closeEditPawn();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.update = function (dt) {
        var _a;
        if (!((_a = this.model) === null || _a === void 0 ? void 0 : _a.active)) {
            return;
        }
        // 检测是否需要填充地图
        // this.checkUpdateMap()
    };
    AreaWindCtrl.prototype.checkUpdateMap = function () {
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2_2);
        if (!this.centre.equals(point) || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateMap(point);
        }
    };
    AreaWindCtrl = __decorate([
        ccclass
    ], AreaWindCtrl);
    return AreaWindCtrl;
}(mc.BaseWindCtrl));
exports.default = AreaWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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