
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/BaseBuildCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2721aJDkD1J0bjlAQDgFp+H', 'BaseBuildCmpt');
// app/script/view/area/BaseBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var MapHelper_1 = require("../../common/helper/MapHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 建筑
var BaseBuildCmpt = /** @class */ (function (_super) {
    __extends(BaseBuildCmpt, _super);
    function BaseBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.data = null;
        _this.owner = '';
        _this.body = null;
        _this.origin = cc.v2(); //起点
        _this.originY = 0; //实际地图的七点 像素
        _this.tempBodyPosition = cc.v2();
        _this.tempIndex = 0;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_position = cc.v2();
        return _this;
    }
    BaseBuildCmpt.prototype.init = function (data, origin, originY, owner) {
        this.data = data;
        this.origin.set(origin);
        this.originY = originY;
        this.owner = owner;
        this.body = this.FindChild('body');
        this.body.getPosition(this.tempBodyPosition);
        return this;
    };
    BaseBuildCmpt.prototype.clean = function () {
    };
    Object.defineProperty(BaseBuildCmpt.prototype, "id", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseBuildCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.uid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseBuildCmpt.prototype, "point", {
        get: function () { return this.data.point; },
        enumerable: false,
        configurable: true
    });
    BaseBuildCmpt.prototype.getBody = function () { return this.body; };
    BaseBuildCmpt.prototype.getTempPosition = function () { return this.getPosition(this._temp_position); };
    BaseBuildCmpt.prototype.getBodyOffsetTopPosition = function (y) {
        if (y === void 0) { y = 0; }
        var pos = this.getTempPosition();
        pos.y += y + this.getBuildY();
        return pos;
    };
    BaseBuildCmpt.prototype.getBuildY = function () {
        return (this.data.size.y - 1) * Constant_1.TILE_SIZE;
    };
    // 同步位置
    BaseBuildCmpt.prototype.syncPoint = function () {
        if (this.data) {
            this.node.setPosition(this.getActPixelByPoint(this.data.point));
        }
    };
    // 同步zindex
    BaseBuildCmpt.prototype.syncZindex = function () {
        if (this.data) {
            var y = this.node.y - this.originY;
            var index = (Constant_1.AREA_MAX_ZINDEX - y) * 10;
            if (this.data.id === Constant_1.BUILD_DRILLGROUND_NID) {
                index += 1;
            }
            this.tempIndex = this.node.zIndex = index;
        }
    };
    // 根据像素点获取网格点
    BaseBuildCmpt.prototype.getActPointByPixel = function (pixel) {
        return MapHelper_1.mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin);
    };
    // 根据网格点获取像素点
    BaseBuildCmpt.prototype.getActPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1);
    };
    // 重新同步
    BaseBuildCmpt.prototype.resync = function (data, owner) { return this; };
    // 刷新等级
    BaseBuildCmpt.prototype.updateLv = function (lv) { };
    // 设置是否可以点击
    BaseBuildCmpt.prototype.setCanClick = function (val) { };
    // 设置可以点击选择
    BaseBuildCmpt.prototype.setCanClickSelect = function (val) { };
    // 刷新升级动画
    BaseBuildCmpt.prototype.updateUpLvAnim = function () { };
    // 刷新训练士兵
    BaseBuildCmpt.prototype.updateDrillPawn = function () { };
    // 刷新打造装备
    BaseBuildCmpt.prototype.updateForgeEquip = function () { };
    BaseBuildCmpt = __decorate([
        ccclass
    ], BaseBuildCmpt);
    return BaseBuildCmpt;
}(cc.Component));
exports.default = BaseBuildCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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