
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/PawnCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '79c8fQaFpRErq1Em9pLIvcb', 'PawnCmpt');
// app/script/view/area/PawnCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WxHelper_1 = require("../../common/helper/WxHelper");
var OutlineShaderCtrl_1 = require("../../common/shader/OutlineShaderCtrl");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var FrameAnimationCmpt_1 = require("../cmpt/FrameAnimationCmpt");
var HPBarCmpt_1 = require("./HPBarCmpt");
var PawnAnimConf_1 = require("./PawnAnimConf");
var PawnAnimationCmpt_1 = require("./PawnAnimationCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 士兵
var PawnCmpt = /** @class */ (function (_super) {
    __extends(PawnCmpt, _super);
    function PawnCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.data = null;
        _this.body = null;
        _this.curSkinId = 0;
        _this.curPortrayalId = 0;
        _this.animNode = null;
        _this.animCmpt = null;
        _this.touchCmpt = null;
        _this.hpBar = null;
        _this.animNodeInitY = 0;
        _this.origin = cc.v2(); //起点
        _this.originY = 0;
        _this.tempBodyPosition = cc.v2();
        _this.prePoint = cc.v2();
        _this.prePositionY = -1;
        _this.preAnger = -1;
        _this.curShieldValue = -1; //当前护盾值
        _this.preShieldValue = -1;
        _this.preActioning = false;
        _this.preStateUid = '';
        _this.preAnimName = '';
        _this.currAnimName = '';
        _this.isDie = false; //是否在视图层死亡
        _this.isShowStandShield = false; //是否显示立盾
        _this.isShowBuffMap = {}; //外显buff
        _this.mutualBuffType = 0; //当前显示的互斥buff
        _this.isLoadBuffMap = {};
        _this.buffNodes = []; //buff节点列表
        _this.buffIconCmpt = null; //buff图标容器
        _this.isDiaup = false;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_position = cc.v2();
        _this.tempIndex = 0;
        return _this;
    }
    PawnCmpt.prototype.init = function (data, origin, key) {
        var _this = this;
        this.reset(data.uid);
        this.data = data;
        this.key = key;
        this.curSkinId = data.skinId;
        this.curPortrayalId = data.getPortrayalId();
        this.origin.set(origin);
        this.originY = origin.y * Constant_1.TILE_SIZE;
        this.body = this.FindChild('body');
        this.body.getPosition(this.tempBodyPosition);
        this.updatePosition(true);
        this.updateZIndex();
        this.updateAnger();
        this.updateShieldValue();
        this.preStateUid = '';
        this.animNode = this.FindChild('body/anim');
        this.animCmpt = this.node.addComponent(PawnAnimationCmpt_1.default).init(this.animNode.getComponent(cc.Sprite), data.getViewId(), key);
        if (this.animNodeInitY === 0) {
            this.animNodeInitY = this.animNode.y;
        }
        this.animNode.scale = 1;
        this.animNode.y = this.animNodeInitY;
        // 设置体型
        this.updateAnimScale(1 + data.getBuffValue(Enums_1.BuffType.FEED_INTENSIFY) * 0.02);
        // 非战斗单位不用加点击
        var isNoncombat = data.isNoncombat();
        if (!isNoncombat) {
            this.touchCmpt = this.FindChild('touch').addComponent(ClickTouchCmpt_1.default).on(this.onClick, this); /* .setPlayAction(true).setTarget(this.body) */
        }
        if (data.isBuilding() || isNoncombat) {
            // 秦良玉的矛 特殊处理下
            this.body.scaleX = data.id === Constant_1.SPEAR_PAWN_ID ? data.enterDir : 1;
            if (data.id !== Constant_1.FIRE_PAWN_ID || data.enterDir === 2) {
                this.playAnimation('create', function () {
                    if (_this.isValid) {
                        _this.loadHPBar();
                        _this.playAnimation('idle');
                    }
                });
                return this;
            }
        }
        else if (data.isHasBuff(Enums_1.BuffType.STAND_SHIELD)) {
            this.loadHPBar();
            this.playAnimation('stand_shield'); //如果有立盾
            return this;
        }
        this.loadHPBar();
        this.playAnimation('idle');
        return this;
    };
    PawnCmpt.prototype.reset = function (uid) {
        this.stopSFXByKey('move_sound', uid);
        this.putAllBuff();
        this.isShowStandShield = false;
        this.isShowBuffMap = {};
        this.mutualBuffType = 0;
        this.isLoadBuffMap = {};
    };
    PawnCmpt.prototype.putAllBuff = function () {
        for (var k in Constant_1.NEED_SHOW_BUFF) {
            this.putBuff(Number(k));
        }
        this.putBuff(this.mutualBuffType);
    };
    // 重新同步一下信息
    PawnCmpt.prototype.resync = function (data, jump) {
        var _a, _b, _c, _d, _e, _f;
        if (jump === void 0) { jump = false; }
        this.reset(data.uid);
        // this.animCmpt?.stop()
        (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.resetMove();
        var point = this.getActPoint();
        this.data = data;
        if (!jump && point && !data.isBattleing() && data.aIndex >= 0 && !data.point.equals(point)) {
            this.prePoint.set(data.point);
            this.updatePositionForMove(point, data.point.clone());
        }
        else {
            this.prePoint.set2(-1, -1);
            this.updatePosition();
            this.updateZIndex();
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(data);
        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.updateAnger(data.getAngerRatio());
        this.showPawnLv((_d = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_LV)) !== null && _d !== void 0 ? _d : 1);
        this.showPawnEquip(this.isCanShowPawnEquip());
        this.setCanClick(true);
        this.preStateUid = '';
        (_e = this.animCmpt) === null || _e === void 0 ? void 0 : _e.initModel();
        // 秦良玉没矛状态
        if (((_f = data.getPortrayalSkill()) === null || _f === void 0 ? void 0 : _f.id) === Enums_1.HeroType.QIN_LIANGYU && data.getState() === Enums_1.PawnState.STAND) {
            if (data.isHasBuff(Enums_1.BuffType.BARB)) {
                this.playAnimation('idle_barb');
            }
            else {
                this.playAnimation('idle');
            }
        }
        return this;
    };
    PawnCmpt.prototype.clean = function (release) {
        var _a, _b, _c, _d, _e, _f;
        if (!this.isValid || !this.node) {
            return cc.error('clean error?');
        }
        this.unscheduleAllCallbacks();
        this.stopSFXByKey('move_sound', this.uid);
        this.node.stopAllActions();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.clean();
        (_b = this.animCmpt) === null || _b === void 0 ? void 0 : _b.clean();
        this.isShowStandShield = false;
        this.isShowBuffMap = {};
        this.mutualBuffType = 0;
        this.isLoadBuffMap = {};
        nodePoolMgr.put((_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.node);
        this.hpBar = null;
        this.buffNodes.forEach(function (m) { return nodePoolMgr.put(m); });
        this.buffNodes.length = 0;
        (_d = this.buffIconCmpt) === null || _d === void 0 ? void 0 : _d.clean();
        nodePoolMgr.put((_e = this.buffIconCmpt) === null || _e === void 0 ? void 0 : _e.node);
        this.buffIconCmpt = null;
        this.node.destroy();
        release && assetsMgr.releaseTempRes((_f = this.data) === null || _f === void 0 ? void 0 : _f.getPrefabUrl(), this.key);
        this.data = null;
    };
    Object.defineProperty(PawnCmpt.prototype, "id", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.uid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "cuid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.cuid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "point", {
        get: function () { return this.data.point; },
        enumerable: false,
        configurable: true
    });
    PawnCmpt.prototype.getBody = function () { return this.body; };
    PawnCmpt.prototype.getTempPosition = function () { return this.getPosition(this._temp_position); };
    PawnCmpt.prototype.getAbsUid = function () { return this.uid + (this.curPortrayalId || this.curSkinId || this.id); };
    PawnCmpt.prototype.getActPoint = function () {
        return this.getActPointByPixel(this.getTempPosition());
    };
    // 根据像素点获取网格点
    PawnCmpt.prototype.getActPointByPixel = function (pixel) {
        return MapHelper_1.mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin);
    };
    // 根据网格点获取像素点
    PawnCmpt.prototype.getActPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1);
    };
    // 播放化身
    PawnCmpt.prototype.playAvatarHeroAnim = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var it, anim;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('pawn/AVATAR_HERO', this.key)];
                    case 1:
                        it = _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = this.node;
                        it.zIndex = 20;
                        it.active = true;
                        anim = it.Child('val', FrameAnimationCmpt_1.default);
                        return [4 /*yield*/, anim.init('avatar_' + id, this.key)];
                    case 2:
                        _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        anim.play('avatar');
                        this.playSFX('sound_074');
                        ut.wait(2.42).then(function () { return nodePoolMgr.put(it); });
                        return [4 /*yield*/, ut.wait(1.98)];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.playAnimation = function (name, cb, startTime, intervalMul) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f;
        if (name === 'move' && this.data.getState() === Enums_1.PawnState.MOVE) {
            this.playSFXByKey('move_sound', '', { loop: true, tag: this.uid });
        }
        else {
            this.fadeOutSFXByKey('move_sound', this.uid);
        }
        if (this.isDiaup && name !== 'idle') {
            this.isDiaup = false;
        }
        if (name !== 'die' && name !== 'stand_shield' && ((_a = this.data) === null || _a === void 0 ? void 0 : _a.isHasBuff(Enums_1.BuffType.STAND_SHIELD))) {
            this.currAnimName = name;
            if (name !== 'hit') {
            }
            else if (((_c = (_b = this.data.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.CAO_REN) { //曹仁需要播放受击动画
                (_d = this.animCmpt) === null || _d === void 0 ? void 0 : _d.play('stand_hit', function () { return _this.isValid && _this.playAnimation('stand_shield'); }, startTime);
            }
            else {
                ut.wait(0.5, this).then(function () {
                    var _a, _b, _c;
                    if (_this.isValid && ((_b = (_a = _this.data) === null || _a === void 0 ? void 0 : _a.state) === null || _b === void 0 ? void 0 : _b.type) === Enums_1.PawnState.HIT) {
                        _this.data.changeState(Enums_1.PawnState.STAND);
                        _this.currAnimName = ((_c = _this.data) === null || _c === void 0 ? void 0 : _c.isHasBuff(Enums_1.BuffType.STAND_SHIELD)) ? 'stand_shield' : 'idle';
                    }
                });
            }
            return; //如果是立盾状态就不播放其他动画了
        }
        else if (((_e = this.animCmpt) === null || _e === void 0 ? void 0 : _e.playAnimName) === 'create' && name !== 'die') {
            return cb && cb(); //如果还在播放创建 其他动画就不要播放了 死亡还是可以的
        }
        if (this.isPullStringState(name)) {
            name = name + '_pull'; //检测是否有拉弦状态
        }
        else if (this.isBarbState(name)) {
            name = name + '_barb'; //检测秦良玉手上是否有矛
        }
        if (this.data.id === Constant_1.FIRE_PAWN_ID && name === 'idle') {
            name = 'fire_' + this.data.lv; //火
        }
        this.currAnimName = name;
        (_f = this.animCmpt) === null || _f === void 0 ? void 0 : _f.play(name, cb, startTime);
    };
    // 播放音效
    PawnCmpt.prototype.playSFXByKey = function (key, suffix, opts) {
        if (suffix === void 0) { suffix = ''; }
        if (this.data.isHero()) {
            var prev = this.data.portrayal.json[key];
            if (prev) {
                return this.playSFX(prev + suffix, opts);
            }
        }
        else if (this.data.skinId > 0) {
            var json = assetsMgr.getJsonData('pawnSkin', this.data.skinId);
            var prev = json === null || json === void 0 ? void 0 : json[key];
            if (prev) {
                return this.playSFX(prev + suffix, opts);
            }
        }
        if (this.data.baseJson) {
            var url = this.data.baseJson[key];
            if (url) {
                this.playSFX(url + suffix, opts);
            }
        }
    };
    // 播放音效
    PawnCmpt.prototype.playSFX = function (url, opts) {
        var _a;
        if (!url) {
            return;
        }
        url = (_a = PawnAnimConf_1.PAWN_SOUND_CONF_TRANSITION[url]) !== null && _a !== void 0 ? _a : url;
        audioMgr.playSFX('pawn/' + url, opts);
    };
    PawnCmpt.prototype.stopSFX = function (url, tag) {
        if (!url) {
            return;
        }
        audioMgr.stopSFX('pawn/' + url, tag);
    };
    PawnCmpt.prototype.stopSFXByKey = function (key, tag) {
        if (!this.data) {
            return;
        }
        var url = null;
        if (this.data.isHero()) {
            url = this.data.portrayal.json[key];
        }
        else if (this.data.baseJson) {
            url = this.data.baseJson[key];
        }
        this.stopSFX(url, tag);
    };
    PawnCmpt.prototype.fadeOutSFXByKey = function (key, tag) {
        var url = null;
        if (this.data.isHero()) {
            url = 'pawn/' + this.data.portrayal.json[key];
        }
        else if (this.data.baseJson) {
            url = 'pawn/' + this.data.baseJson[key];
        }
        if (url) {
            audioMgr.fadeOutSFX(0.15, url, tag);
        }
    };
    // 是否拉弦状态
    PawnCmpt.prototype.isPullStringState = function (name) {
        var _a;
        if (((_a = this.data) === null || _a === void 0 ? void 0 : _a.id) !== Constant_1.PAWN_CROSSBOW_ID) {
            return false;
        }
        else if (name !== 'idle' && name !== 'move' && name !== 'hit' && name !== 'diaup' && name !== 'die') {
            return false;
        }
        return !!this.data.getSkillByType(Enums_1.PawnSkillType.PULL_STRING) && this.data.getCurAnger() !== 0;
    };
    // 是否无矛状态
    PawnCmpt.prototype.isBarbState = function (name) {
        var _a, _b, _c;
        if (((_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) !== Enums_1.HeroType.QIN_LIANGYU || !((_c = this.data) === null || _c === void 0 ? void 0 : _c.isHasBuff(Enums_1.BuffType.BARB))) {
            return false;
        }
        return name === 'idle' || name === 'hit' || name === 'diaup';
    };
    // 设置方向
    PawnCmpt.prototype.setDir = function (dir) {
        if (this.data.isBuilding()) {
            this.body.scaleX = 1; //建筑不需要翻转
        }
        else if (dir !== 0) {
            this.body.scaleX = ut.normalizeNumber(dir);
        }
    };
    // 加载血条
    PawnCmpt.prototype.loadHPBar = function () {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0:
                        if (this.data.id === Constant_1.FIRE_PAWN_ID) {
                            return [2 /*return*/, (_a = this.body.Child('bar')) === null || _a === void 0 ? void 0 : _a.Color(GameHelper_1.gameHpr.getBattleFireBarColor(this.data))];
                        }
                        else if (this.data.maxHp === 0 || this.data.isNoncombat()) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get(this.data.getHPBarPrefabUrl(), this.key)];
                    case 1:
                        it = _e.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = this.node;
                        it.zIndex = 10;
                        it.active = true;
                        if (this.data.isBoss()) {
                            it.setPosition(0, 113);
                        }
                        else {
                            it.setPosition(0, this.data.isHero() ? 56 : 46);
                        }
                        this.hpBar = it.addComponent(HPBarCmpt_1.default).init(this.data);
                        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.updateAnger(this.data.getAngerRatio());
                        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.updateShieldValue(this.data.getShieldValue(), this.data.curHp, this.data.getMaxHp());
                        this.showPawnLv((_d = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_LV)) !== null && _d !== void 0 ? _d : 1);
                        this.showPawnEquip(this.isCanShowPawnEquip());
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.showPawnLv = function (val) {
        if (this.hpBar) {
            this.hpBar.Child('root').opacity = val ? 255 : 0;
            var armyNameLbl = this.hpBar.Child('army_name', cc.Label);
            if (armyNameLbl) {
                armyNameLbl.string = (val === 1 && this.data.armyName) ? ut.nameFormator(this.data.armyName, 4) : '';
            }
            this.hpBar.Child('lv', cc.Label).string = (val === 1 && this.data.lv && !this.data.isMachine() && !this.data.isBuilding()) ? this.data.lv + '' : '';
        }
    };
    // 是否可以显示装备信息 这里如果还没有铁匠铺就不会显示
    PawnCmpt.prototype.isCanShowPawnEquip = function () {
        var player = GameHelper_1.gameHpr.player;
        return !!GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP) && (player.isCapture() || player.getMainBuilds().some(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID && m.lv >= 1; }));
    };
    // 是否可以显示没有装备的提示
    PawnCmpt.prototype.isCanShowNotEquipTip = function () {
        return !this.data.equip.id && this.data.isCanWearEquip() && this.data.isOwner() && GameHelper_1.gameHpr.player.getEquips().length > 0;
    };
    // 显示装备信息
    PawnCmpt.prototype.showPawnEquip = function (val) {
        var _a, _b;
        if (!this.hpBar || !this.hpBar.Child('equip')) {
        }
        else if (this.hpBar.Child('equip').active = val && this.data.isCanWearEquip()) {
            this.updateShowPawnEquipInfo();
            (_a = this.hpBar.Child('not_equip')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else {
            (_b = this.hpBar.Child('not_equip')) === null || _b === void 0 ? void 0 : _b.setActive(this.isCanShowNotEquipTip());
        }
    };
    PawnCmpt.prototype.updateShowPawnEquip = function () {
        var _a, _b;
        if (!this.hpBar || !this.hpBar.Child('equip')) {
            return;
        }
        else if (this.hpBar.Child('equip').active) {
            this.updateShowPawnEquipInfo();
            (_a = this.hpBar.Child('not_equip')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else {
            (_b = this.hpBar.Child('not_equip')) === null || _b === void 0 ? void 0 : _b.setActive(this.isCanShowNotEquipTip());
        }
        this.hpBar.initInfo();
    };
    PawnCmpt.prototype.updateShowPawnEquipInfo = function () {
        var _a, _b;
        this.hpBar.Child('equip/add').active = !((_a = this.data.equip) === null || _a === void 0 ? void 0 : _a.id) && this.data.isOwner();
        var spr = this.hpBar.Child('equip/val', cc.Sprite);
        if ((_b = this.data.equip) === null || _b === void 0 ? void 0 : _b.id) {
            ResHelper_1.resHelper.loadEquipIcon(this.data.equip.id, spr, this.key, this.data.equip.getSmeltCount());
        }
        else {
            spr.spriteFrame = null;
        }
    };
    PawnCmpt.prototype.showOutline = function (v) {
        var outline = this.getComponent(OutlineShaderCtrl_1.default);
        if (!outline) {
            outline = this.addComponent(OutlineShaderCtrl_1.default);
            outline.setTarget(outline.FindChild('body/anim').Component(cc.Sprite));
            outline.setOutlineSize(4);
            outline.setColor(cc.Color.WHITE);
        }
        outline.setVisible(v);
    };
    // 被点击了
    PawnCmpt.prototype.onClick = function () {
        if (this.data) {
            audioMgr.playSFX('click');
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.data);
            // this.showOutline(true)
        }
    };
    // 设置是否可以点击
    PawnCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 移动位置
    PawnCmpt.prototype.movePoint = function (point) {
        this.node.setPosition(this.getActPixelByPoint(point));
    };
    // 取消编辑
    PawnCmpt.prototype.cancel = function () {
        this.node.setPosition(this.getActPixelByPoint(this.data.point));
    };
    // 确认编辑
    PawnCmpt.prototype.confirm = function () {
        var point = this.getActPoint();
        this.point.set(point);
        this.prePoint.set(point);
    };
    PawnCmpt.prototype.update = function (dt) {
        if (!this.data) {
            return;
        }
        this.updateZIndex();
        this.updateAnger();
        this.updateBuff();
        this.updateShieldValue();
        this.updateState();
        this.updateCheckDie();
    };
    // 同步zindex
    PawnCmpt.prototype.updateZIndex = function () {
        var _a, _b;
        if (this.prePositionY !== this.node.y || (this.preActioning !== !!((_a = this.data) === null || _a === void 0 ? void 0 : _a.actioning) || this.preAnimName !== this.currAnimName)) {
            this.prePositionY = this.node.y;
            this.preActioning = !!((_b = this.data) === null || _b === void 0 ? void 0 : _b.actioning);
            this.preAnimName = this.currAnimName;
            var y = this.prePositionY - this.originY;
            var add = 0;
            if (this.data.getPawnType() === Enums_1.PawnType.NONCOMBAT) {
                add = 3; //非战斗单位在最上层
            }
            else if (this.preActioning) {
                var state = this.data.getState();
                add = state === Enums_1.PawnState.ATTACK || state >= Enums_1.PawnState.SKILL ? 2 : 1;
            }
            else if (this.preAnimName === 'die') {
                return;
            }
            else {
                add = !this.preAnimName || this.preAnimName === 'create' || this.preAnimName === 'stand_shield' || this.preAnimName === 'idle' || this.preAnimName === 'idle_pull' ? 0 : 1;
            }
            var index = (Constant_1.AREA_MAX_ZINDEX - y) * 10 + add;
            if (index !== this.node.zIndex) {
                this.tempIndex = this.node.zIndex = index;
            }
            // cc.log('updateZIndex', this.data.id, this.data.uid, this.preActioning, this.preAnimName, add)
        }
    };
    // 同步位置
    PawnCmpt.prototype.updatePosition = function (init) {
        if (init || !this.prePoint.equals(this.data.point)) {
            this.prePoint.set(this.data.point);
            this.node.setPosition(this.getActPixelByPoint(this.data.point));
        }
    };
    PawnCmpt.prototype.updatePositionForMove = function (sp, ep) {
        return __awaiter(this, void 0, void 0, function () {
            var data, points, area, time;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        data = this.data;
                        points = [sp, ep];
                        area = GameHelper_1.gameHpr.areaCenter.getArea(data.index);
                        if (!area) return [3 /*break*/, 2];
                        return [4 /*yield*/, GameHelper_1.gameHpr.getPawnASatr(data.uid).init(function (x, y) { return area.checkIsBattleArea(x, y); }).search(sp, ep)];
                    case 1:
                        points = _a.sent();
                        _a.label = 2;
                    case 2:
                        time = MapHelper_1.mapHelper.getMoveNeedTime(points, 400);
                        data.changeState(Enums_1.PawnState.EDIT_MOVE, { paths: points, needMoveTime: time });
                        return [4 /*yield*/, ut.wait(time * 0.001)];
                    case 3:
                        _a.sent();
                        if (data.getState() < Enums_1.PawnState.STAND) {
                            data.changeState(Enums_1.PawnState.NONE);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新怒气
    PawnCmpt.prototype.updateAnger = function () {
        var _a;
        if (this.preAnger !== this.data.curAnger) {
            this.preAnger = this.data.curAnger;
            (_a = this.hpBar) === null || _a === void 0 ? void 0 : _a.updateAnger(this.data.getAngerRatio());
        }
    };
    // 刷新白盾
    PawnCmpt.prototype.updateShieldValue = function () {
        var _a;
        if (this.preShieldValue !== this.curShieldValue) {
            this.preShieldValue = this.curShieldValue;
            (_a = this.hpBar) === null || _a === void 0 ? void 0 : _a.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp());
        }
    };
    // 刷新buff效果
    PawnCmpt.prototype.updateBuff = function () {
        var _this = this;
        this.curShieldValue = 0;
        var showBuffTypeMap = {}, mutualBuff = 0, standShield = false, feedIntensifyValue = 0;
        this.data.buffs.forEach(function (m) {
            if (Constant_1.SHIELD_BUFF[m.type]) { //记录护盾值
                _this.curShieldValue += m.value;
            }
            if (Constant_1.NEED_SHOW_BUFF[m.type]) {
                showBuffTypeMap[m.type] = true;
            }
            else if (Constant_1.NEED_MUTUAL_BUFF[m.type]) {
                mutualBuff = m.type; //互斥buff
            }
            else if (m.type === Enums_1.BuffType.STAND_SHIELD) { //立盾
                standShield = true;
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //投喂强化
                feedIntensifyValue = m.value;
            }
        });
        // 刷新外显buff
        for (var k in Constant_1.NEED_SHOW_BUFF) {
            var type = Number(k);
            this.updateShowBuff(type, showBuffTypeMap[type]);
        }
        // 刷新互斥buff
        this.updateMutualBuff(mutualBuff);
        // 刷新立盾
        this.updateStandShield(standShield);
        // 体型
        this.updateAnimScale(1 + feedIntensifyValue * 0.02);
    };
    // 刷新立盾
    PawnCmpt.prototype.updateStandShield = function (val) {
        if (!this.isShowStandShield && val) {
            this.isShowStandShield = true;
            this.playAnimation('stand_shield');
        }
        else if (this.isShowStandShield && !val) {
            this.isShowStandShield = false;
            if (this.currAnimName !== 'shield_end') {
                this.playAnimation('idle');
            }
        }
    };
    // 刷新外显buff
    PawnCmpt.prototype.updateShowBuff = function (type, val) {
        if (this.isLoadBuffMap[type]) {
        }
        else if (!this.isShowBuffMap[type] && val) {
            this.showBuff(type);
            this.isShowBuffMap[type] = true;
        }
        else if (this.isShowBuffMap[type] && !val) {
            this.isShowBuffMap[type] = false;
            this.putBuff(type);
        }
    };
    // 刷新互斥buff效果 就是只会显示一个效果
    PawnCmpt.prototype.updateMutualBuff = function (type) {
        if (this.mutualBuffType === type) {
            return;
        }
        else if (this.mutualBuffType) {
            this.putBuff(this.mutualBuffType);
            this.mutualBuffType = 0;
        }
        if (type && !this.isLoadBuffMap[type]) {
            this.mutualBuffType = type;
            this.showBuff(type);
        }
    };
    // 显示一个buff
    PawnCmpt.prototype.showBuff = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var showType, name, it, cmpt;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        showType = Constant_1.BUFF_SHOW_TYPE_TRAN[type] || type;
                        name = 'BUFF_' + showType;
                        it = this.buffNodes.find(function (m) { return m.name === name; });
                        if (!it) return [3 /*break*/, 1];
                        this.isLoadBuffMap[showType] = false;
                        return [3 /*break*/, 5];
                    case 1:
                        if (!!this.isLoadBuffMap) return [3 /*break*/, 2];
                        return [2 /*return*/, WxHelper_1.wxHelper.errorAndFilter('showBuff', '!this.isLoadBuffMap')];
                    case 2:
                        if (!this.isLoadBuffMap[showType]) return [3 /*break*/, 3];
                        return [2 /*return*/];
                    case 3:
                        this.isLoadBuffMap[showType] = true;
                        return [4 /*yield*/, nodePoolMgr.get('buff/' + name, this.key)];
                    case 4:
                        it = _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        this.isLoadBuffMap[showType] = false;
                        this.buffNodes.push(it);
                        _a.label = 5;
                    case 5:
                        it.opacity = 255;
                        it.parent = this.node;
                        it.zIndex = Constant_1.BUFF_NODE_ZINDEX[showType] || 10;
                        cmpt = it.Component(PawnAnimationCmpt_1.default).init(it.Child('body/anim', cc.Sprite), showType, this.key);
                        if (showType === Enums_1.BuffType.SHIELD
                            || showType === Enums_1.BuffType.PROTECTION_SHIELD
                            || showType === Enums_1.BuffType.RODELERO_SHIELD
                            || showType === Enums_1.BuffType.RODELERO_SHIELD_001
                            || showType === Enums_1.BuffType.RODELERO_SHIELD_102
                            || showType === Enums_1.BuffType.ABNEGATION_SHIELD
                            || showType === Enums_1.BuffType.POISONED_WINE) {
                            cmpt.play('trigger', function () { return cmpt.isValid && cmpt.play('stand'); });
                        }
                        else {
                            cmpt.play('stand');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.putBuff = function (type) {
        var showType = Constant_1.BUFF_SHOW_TYPE_TRAN[type] || type;
        var name = 'BUFF_' + showType;
        var node = this.buffNodes.remove('name', name);
        if (!node) {
        }
        else if (type === Enums_1.BuffType.SHIELD
            || showType === Enums_1.BuffType.PROTECTION_SHIELD
            || type === Enums_1.BuffType.RODELERO_SHIELD
            || type === Enums_1.BuffType.RODELERO_SHIELD_001
            || type === Enums_1.BuffType.RODELERO_SHIELD_102
            || type === Enums_1.BuffType.ABNEGATION_SHIELD) {
            node.Component(PawnAnimationCmpt_1.default).play('die', function () { return nodePoolMgr.put(node); });
        }
        else {
            nodePoolMgr.put(node);
        }
    };
    // 刷新体型
    PawnCmpt.prototype.updateAnimScale = function (val) {
        if (this.animNode.scale !== val) {
            this.animNode.scale = val;
            this.animNode.y = this.animNodeInitY + (this.animNodeInitY + 36) * (val - 1);
        }
    };
    // 检测是否死亡
    PawnCmpt.prototype.updateCheckDie = function () {
        var _this = this;
        var _a, _b;
        if (!this.isDie && ((_a = this.data) === null || _a === void 0 ? void 0 : _a.isDie())) {
            this.isDie = true;
            var name = (_b = this.animCmpt) === null || _b === void 0 ? void 0 : _b.playAnimName;
            if (name !== 'hit' && name !== 'die' && name !== 'hit_pull' && name !== 'die_pull') {
                this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, _this.data.aIndex, _this.data.uid); });
            }
        }
    };
    // 同步状态信息
    PawnCmpt.prototype.updateState = function () {
        var _a;
        if (GameHelper_1.gameHpr.playback.isSimulating) {
            return;
        }
        else if (!((_a = this.data) === null || _a === void 0 ? void 0 : _a.state) || this.preStateUid === this.data.state.uid || this.isDie) {
            return;
        }
        this.preStateUid = this.data.state.uid;
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
        var state = this.data.state.type, data = this.data.state.data;
        // cc.log('updateState', this.uid, this.point.ID(), PawnState[state])
        // this.data.actioning = this.data.actioning || (state !== PawnState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动
        if (state === Enums_1.PawnState.STAND) { //待机
            this.doStand();
        }
        else if (state === Enums_1.PawnState.MOVE || state === Enums_1.PawnState.EDIT_MOVE) { //移动
            this.doMove(data);
        }
        else if (state === Enums_1.PawnState.ATTACK) { //攻击
            this.doAttack(data);
        }
        else if (state === Enums_1.PawnState.HIT) { //受击
            this.doHit(data);
        }
        else if (state === Enums_1.PawnState.DIAUP) { //击飞
            this.doDiaup(data);
        }
        else if (state === Enums_1.PawnState.HEAL) { //回血
            this.doHeal(data);
        }
        else if (state === Enums_1.PawnState.DEDUCT_HP) { //掉血
            this.doDeductHp(data);
        }
        else if (state === Enums_1.PawnState.ADD_ANGER) { //加怒气
            this.doAddAnger(data);
        }
        else if (state === Enums_1.PawnState.FEAR) { //恐惧
            this.doFear(data);
        }
        else if (state === Enums_1.PawnState.DIE) { //直接死亡
            this.doDie(data);
        }
        else if (state >= Enums_1.PawnState.SKILL && state <= Enums_1.PawnState.SKILL_MAX) { //技能
            this.doSkill(data);
        }
        else {
            this.playAnimation('idle');
        }
        // 通知聚焦
        // if (state === PawnState.MOVE || state === PawnState.ATTACK || (state >= PawnState.SKILL && state <= PawnState.SKILL_MAX)) {
        //     eventCenter.emit(EventType.FOCUS_PAWN, { index: this.data.aIndex, uid: this.data.uid, point: this.data.point })
        // }
    };
    // 待机
    PawnCmpt.prototype.doStand = function () {
        var _a, _b;
        var animName = (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.playAnimName;
        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle
            this.playAnimation('idle');
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.initInfo();
        this.updatePosition();
    };
    // 移动
    PawnCmpt.prototype.doMove = function (data) {
        var _this = this;
        var _a, _b;
        this.updatePosition();
        var paths = (data.paths || []).map(function (m) { return _this.getActPixelByPoint(cc.v2(m)).clone(); });
        var currMoveTime = (_a = data.currMoveTime) !== null && _a !== void 0 ? _a : 0;
        var needMoveTime = (_b = data.needMoveTime) !== null && _b !== void 0 ? _b : 0;
        // 计算各个距离信息
        var sumDis = 0, arr = [];
        for (var i = 1, l = paths.length; i < l; i++) {
            var curr = paths[i], prep = paths[i - 1], speed = curr.sub(prep, this._temp_vec2_3);
            var dis = speed.mag();
            sumDis += dis;
            arr.push({ dis: dis, progress: sumDis, speed: speed.normalize(), prep: prep, pos: curr });
        }
        var ratio = currMoveTime / needMoveTime;
        var startPos = null, list = [];
        for (var i = 0, l = arr.length; i < l; i++) {
            var m = arr[i], pr = m.progress / sumDis;
            if (ratio > pr) {
                continue;
            }
            else if (!startPos) { //找出起点
                var dr = m.dis / sumDis;
                var r = Math.max(ratio - (pr - dr), 0);
                var d = sumDis * r; //超出的一段距离
                startPos = m.speed.mul(d).addSelf(m.prep);
                m.dis -= d; //减去已经走过的路
            }
            list.push({ time: m.dis / sumDis * needMoveTime, endPos: m.pos });
        }
        if (!startPos) {
            return;
        }
        // 开始移动
        this.playAnimation('move');
        this.node.setPosition(startPos);
        this.animCmpt.resetMove();
        this.runMove(list);
    };
    PawnCmpt.prototype.runMove = function (list) {
        var _this = this;
        if (!this.isValid) {
        }
        else if (list.length > 0) {
            var d = list.shift(), pos = d.endPos;
            this.setDir(pos.x - this.node.x);
            this.animCmpt.moveNodeOne(0, d.time, this.getPosition(), pos, function () { return _this.runMove(list); });
        }
        else if (this.data.getState() === Enums_1.PawnState.MOVE || this.data.getState() === Enums_1.PawnState.EDIT_MOVE) {
            this.playAnimation('idle');
        }
    };
    // 攻击
    PawnCmpt.prototype.doAttack = function (data) {
        var _this = this;
        var _a;
        var currAttackTime = (_a = data.currAttackTime) !== null && _a !== void 0 ? _a : 0;
        var targetPoint = data.targetPoint || this.point;
        var suffix = data.instabilityAttackIndex || '';
        this.updatePosition();
        this.setDir(targetPoint.x - this.point.x);
        this.playSFXByKey('attack_sound', suffix);
        this.playAnimation('attack' + suffix, function () { return _this.isValid && _this.playAnimation('idle'); }, currAttackTime);
    };
    // 技能
    PawnCmpt.prototype.doSkill = function (data) {
        var _this = this;
        var _a, _b, _c;
        var currAttackTime = (_a = data.currAttackTime) !== null && _a !== void 0 ? _a : 0;
        var targetPoint = data.targetPoint || this.point;
        var skill = data.skill, heroSkill = this.data.getPortrayalSkill();
        // 位移
        if (!this.prePoint.equals(this.data.point) && ((skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_208
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_212
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_213
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_219
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_306
            || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_QIONG)) {
            this.prePoint.set(this.data.point);
            var pos = this.getActPixelByPoint(this.data.point);
            this.setDir(pos.x - this.node.x);
            var params = skill.params;
            if (!heroSkill) {
            }
            else if (heroSkill.id === Enums_1.HeroType.ZHANG_FEI
                || heroSkill.id === Enums_1.HeroType.XU_CHU
                || heroSkill.id === Enums_1.HeroType.PEI_XINGYAN
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_QIONG
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.GAO_SHUN
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.HUO_QUBING
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.DIAN_WEI) {
                params = heroSkill.params; //张飞 高顺 许褚 裴行俨 秦琼
            }
            else if (this.data.skinId === 3404103) {
                params = '0.36,0.6'; //重骑冬季皮肤
            }
            var _d = __read(ut.stringToNumbers(params, ','), 2), delay = _d[0], time = _d[1];
            this.animCmpt.resetMove().setMoveDelay((delay !== null && delay !== void 0 ? delay : 0) * 1000).moveNodeOne(0, (time !== null && time !== void 0 ? time : 0.1) * 1000, this.getPosition(), pos, function () { return _this.setDir(targetPoint.x - _this.point.x); });
        }
        else {
            this.updatePosition();
            this.setDir(targetPoint.x - this.point.x);
        }
        if (data.sound !== undefined) {
            this.playSFX(data.sound);
        }
        else {
            this.playSFXByKey('skill_sound');
        }
        var isStandShield = (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_205; //立盾
        var isSpearthrowing = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_LIANGYU && !data.skillName;
        this.playAnimation(data.skillName || 'skill', function () {
            if (!_this.isValid) {
            }
            else if (isStandShield) {
                _this.playAnimation('stand_shield');
            }
            else if (isSpearthrowing) {
                _this.playAnimation('idle_barb');
            }
            else {
                _this.playAnimation('idle');
            }
        }, currAttackTime);
        // 播放粉碎大地 地面效果
        if ((skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_215) {
            var type = skill.type, delay = 0.9;
            // 黄盖特殊处理下
            if (((_c = (_b = this.data.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.HUANG_GAI) {
                type = 215001;
                delay = 0.6;
            }
            eventCenter.emit(EventType_1.default.PLAY_BATTLE_EFFECT, {
                type: type, delay: delay,
                index: this.data.aIndex,
                point: this.data.point,
            });
        }
        // 秦良玉 收矛
        if (data.skillName === 'recycle_spear') {
            eventCenter.emit(EventType_1.default.PLAY_BULLET_FLY, {
                bulletId: 5022,
                currTime: currAttackTime,
                needTime: 1000,
                index: this.data.aIndex,
                startPoint: targetPoint,
                targetPoint: this.data.point,
            });
        }
    };
    // 受击
    PawnCmpt.prototype.doHit = function (data) {
        var _this = this;
        var _a, _b, _c, _d, _e;
        var index = this.data.aIndex;
        var damage = (_a = data.damage) !== null && _a !== void 0 ? _a : 0;
        var trueDamage = (_b = data.trueDamage) !== null && _b !== void 0 ? _b : 0;
        var isCrit = !!data.isCrit; //暴击
        var heal = (_c = data.heal) !== null && _c !== void 0 ? _c : 0; //回复
        var isDodge = damage === -1; //闪避
        var isParry = damage === -2; //格挡
        var isTurntheblade = damage === -3; //招架
        var isWithstand = damage === -4; //抵挡
        damage = isDodge || isParry || isTurntheblade || isWithstand ? 0 : damage;
        var attackPoint = data.attackPoint || this.point;
        var isDie = this.isDie = !!data.isDie;
        var time = (_d = data.time) !== null && _d !== void 0 ? _d : 0; //经过的时间
        var sound = data.sound; //受击音效
        var uid = this.uid;
        var isDiaup = this.isDiaup;
        this.isDiaup = false;
        this.setDir(attackPoint.x - this.point.x);
        (_e = this.hpBar) === null || _e === void 0 ? void 0 : _e.play();
        if (damage + trueDamage === 0) {
            return this.playAnimation('idle');
        }
        else if (isDie) {
            if (this.data.getPawnType() !== Enums_1.PawnType.NONCOMBAT) {
                this.node.zIndex = 0;
            }
            this.putAllBuff();
        }
        else if (isDiaup) { //如果没有死亡且上一个动作是击飞
            return this.playAnimation('idle');
        }
        var animName = 'hit';
        if (isDie) {
            animName = 'die';
            this.playSFXByKey('die_sound');
        }
        else if (sound) {
            this.playSFX(sound);
        }
        this.playAnimation(animName, function () {
            if (isDie) {
                eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid);
            }
            else if (_this.isValid) {
                _this.playAnimation('idle');
            }
        });
    };
    // 直接死亡
    PawnCmpt.prototype.doDie = function (data) {
        var _a;
        var index = this.data.aIndex;
        var uid = this.uid;
        if (!this.data.isNoncombat()) {
            this.node.zIndex = 0;
        }
        this.putAllBuff();
        this.playSFX((_a = this.data.baseJson) === null || _a === void 0 ? void 0 : _a.die_sound);
        this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid); });
    };
    // 击飞
    PawnCmpt.prototype.doDiaup = function (data) {
        var _this = this;
        var _a, _b;
        var time = (_a = data.time) !== null && _a !== void 0 ? _a : 0; //经过的时间
        var attackPoint = data.attackPoint || this.prePoint;
        this.setDir(attackPoint.x - this.point.x);
        if (time > 0) {
            this.prePoint.set(this.data.point);
            this.playSFX('sound_037_1');
            this.playAnimation('diaup');
            this.isDiaup = true;
            var pos = this.getActPixelByPoint(this.data.point);
            this.animCmpt.resetMove().setMoveParabolaHeight((_b = data.parabolaHeight) !== null && _b !== void 0 ? _b : 20).moveNodeOne(0, time, this.getPosition(), pos, function () {
                if (_this.isValid && !_this.isDie) {
                    _this.playAnimation('idle');
                }
            });
        }
        else {
            this.playAnimation('idle');
            this.updatePosition();
        }
    };
    // 恐惧
    PawnCmpt.prototype.doFear = function (data) {
        var _this = this;
        var _a;
        var time = (_a = data.time) !== null && _a !== void 0 ? _a : 0; //经过的时间
        if (time > 0 && !this.prePoint.equals(this.data.point)) {
            this.setDir(this.point.x - this.prePoint.x);
            this.prePoint.set(this.data.point);
            var pos = this.getActPixelByPoint(this.data.point);
            this.playAnimation('move', null, 0, 0.5);
            this.animCmpt.resetMove().moveNodeOne(0, time, this.getPosition(), pos, function () {
                if (_this.isValid && !_this.isDie) {
                    _this.playAnimation('idle');
                }
            });
        }
        else {
            this.playAnimation('idle');
            this.updatePosition();
        }
    };
    // 回血
    PawnCmpt.prototype.doHeal = function (data) {
        var _a, _b, _c, _d;
        var index = this.data.aIndex;
        var val = (_a = data.val) !== null && _a !== void 0 ? _a : 0;
        var time = (_b = data.time) !== null && _b !== void 0 ? _b : 0;
        var uid = this.uid;
        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.play();
        if (this.preShieldValue > 0) { //这里主动刷新一下护盾
            (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp());
        }
    };
    // 掉血
    PawnCmpt.prototype.doDeductHp = function (data) {
        var _a, _b, _c, _d, _e;
        var index = this.data.aIndex;
        var damage = (_a = data.damage) !== null && _a !== void 0 ? _a : 0;
        var trueDamage = (_b = data.trueDamage) !== null && _b !== void 0 ? _b : 0;
        var time = (_c = data.time) !== null && _c !== void 0 ? _c : 0;
        var isDie = this.isDie = !!data.isDie;
        var uid = this.uid;
        if (isDie) {
            (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.setActive(false);
            this.node.zIndex = 0;
            this.putAllBuff();
            this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid); });
        }
        else {
            (_e = this.hpBar) === null || _e === void 0 ? void 0 : _e.play();
        }
    };
    // 添加怒气
    PawnCmpt.prototype.doAddAnger = function (data) {
        this.updateAnger();
        // this.doStand()
    };
    PawnCmpt = __decorate([
        ccclass
    ], PawnCmpt);
    return PawnCmpt;
}(cc.Component));
exports.default = PawnCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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