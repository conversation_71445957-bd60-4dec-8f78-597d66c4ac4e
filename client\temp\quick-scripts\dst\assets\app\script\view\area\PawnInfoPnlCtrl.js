
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/PawnInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a0bf3bKAGhA8oj2JJzlsoGa', 'PawnInfoPnlCtrl');
// app/script/view/area/PawnInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PawnInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(PawnInfoPnlCtrl, _super);
    function PawnInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.headNode_ = null; // path://root/head_be_n
        _this.lvEditLbl_ = null; // path://root/head_be_n/lv/edit/num/lv_edit_l
        _this.attackSpeedEditLbl_ = null; // path://root/head_be_n/attack_speed/edit/num/attack_speed_edit_l
        _this.attrNode_ = null; // path://root/attr_n_be
        _this.skillNode_ = null; // path://root/skill_n
        _this.equipNode_ = null; // path://root/equip_n
        _this.fromNode_ = null; // path://root/from_n
        _this.buttonNode_ = null; // path://root/button_n
        _this.buffNode_ = null; // path://root/buff/buff_n
        _this.selectSkinBoxNode_ = null; // path://select_skin_box_be_n
        _this.syncSkinNode_ = null; // path://select_skin_box_be_n/sync_skin_be_n
        _this.syncSkinMaskNode_ = null; // path://select_skin_box_be_n/sync_skin_mask_be_n
        _this.settingSyncSkinNode_ = null; // path://select_skin_box_be_n/setting_sync_skin_n
        _this.selectEquipBoxNode_ = null; // path://select_equip_box_be_n
        _this.syncEquipNode_ = null; // path://select_equip_box_be_n/sync_equip_be_n
        _this.syncEquipMaskNode_ = null; // path://select_equip_box_be_n/sync_equip_mask_be_n
        _this.settingSyncEquipNode_ = null; // path://select_equip_box_be_n/setting_sync_equip_n
        _this.selectPetBoxNode_ = null; // path://select_pet_box_be_n
        _this.selectArmyBoxNode_ = null; // path://select_army_box_be_n
        //@end
        _this.root = null;
        _this.data = null;
        _this.drillInfo = null;
        _this.fromTo = '';
        _this.preAttackSpeed = 0;
        _this.preEquipUid = '';
        _this.preSkinId = 0;
        _this.prePetId = 0;
        _this.preRootHeight = -1;
        _this.isCanEditSkin = false;
        _this.isCanEdit = false;
        _this.isConfPawn = false;
        _this.isBattleing = false;
        return _this;
    }
    PawnInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PAWN_TREASURE] = this.onUpdatePawnTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_BUFF] = this.onUpdateBuff, _c.enter = true, _c),
        ];
    };
    PawnInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.root = this.FindChild('root');
                this.selectSkinBoxNode_.active = false;
                this.selectEquipBoxNode_.active = false;
                this.selectArmyBoxNode_.active = false;
                return [2 /*return*/];
            });
        });
    };
    PawnInfoPnlCtrl.prototype.onEnter = function (data, drillInfo, fromTo) {
        var _a, _b;
        GameHelper_1.gameHpr.uiShowPawnData = data;
        this.data = data;
        this.drillInfo = drillInfo;
        this.fromTo = fromTo;
        this.preAttackSpeed = data.attackSpeed;
        this.preEquipUid = data.equip.uid;
        this.prePetId = data.petId;
        var isOwner = data.isOwner();
        if (isOwner && !GameHelper_1.gameHpr.isSpectate() && !GameHelper_1.gameHpr.user.isHasPawnSkinById(data.skinId)) {
            data.skinId = 0;
        }
        this.preSkinId = data.skinId;
        this.data.recordCurrHp(true); //先记录一下
        var isBattleing = this.isBattleing = data.isBattleing() || GameHelper_1.gameHpr.isBattleingByIndex(data.aIndex), hasOwner = !!data.owner;
        var isCanEdit = this.isCanEdit = isOwner && !isBattleing && data.getState() === Enums_1.PawnState.NONE && !drillInfo && (!fromTo || fromTo === 'area_army');
        var isConfPawn = this.isConfPawn = !data.uid && !drillInfo && fromTo !== 'ceri' && fromTo !== 'book'; //配置士兵
        var isInArea = mc.currWindName === 'area'; //是否在战斗场景
        var isFromDrillground = fromTo === 'drillground';
        var isMachine = data.type >= Enums_1.PawnType.MACHINE; //是否器械
        var isHero = data.isHero(); //是否英雄
        var isCanEditEquip = isOwner && !isBattleing && data.getState() === Enums_1.PawnState.NONE && (!fromTo || fromTo === 'area_army');
        // 头像
        var editNode = this.headNode_.Child('edit'), headValNode = this.headNode_.Child('val');
        headValNode.active = true;
        if (data.isBoss()) { //boss加载mini头像
            headValNode.y = -32;
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(data.getViewId(), headValNode, this.key);
        }
        else {
            headValNode.y = -36;
            ResHelper_1.resHelper.loadPawnHeadIcon(data.getViewId(), headValNode, this.key);
        }
        editNode.active = this.isCanEditSkin = this.headNode_.Component(cc.Button).interactable = !isHero && (isCanEdit || isConfPawn || fromTo === 'book');
        this.headNode_.Child('name/val').setLocaleKey(data.name);
        this.headNode_.Child('name/type').setLocaleKey(data.type ? data.typeName : '');
        // 同步皮肤设置
        this.settingSyncSkinNode_.active = false;
        this.syncSkinMaskNode_.active = false;
        if (this.syncSkinNode_.active = !isConfPawn && isCanEditEquip) {
            var val = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0;
            this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
        }
        // 等级
        this.updateLv();
        // 出手速度
        var attackSpeed = this.headNode_.Child('attack_speed');
        if (attackSpeed.active = fromTo !== 'book') {
            var isCanEditAs = !drillInfo && fromTo !== 'ceri' && (isCanEdit || isConfPawn);
            attackSpeed.Child('edit').active = isCanEditAs;
            attackSpeed.Child('val').active = !isCanEditAs;
            if (isCanEditAs) {
                this.attackSpeedEditLbl_.string = data.attackSpeed + '';
            }
            else {
                attackSpeed.Child('val', cc.Label).string = data.attackSpeed + '';
            }
        }
        // 属性
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        // 技能
        this.updateSkills();
        // 装备 和 背包
        var isYyj = ((_b = (_a = this.data.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.YANG_YOUJI;
        var isEquip = !isMachine && (hasOwner || isConfPawn || !!drillInfo || isYyj);
        if (this.equipNode_.active = isEquip || !!this.preEquipUid) {
            var info = this.equipNode_.Child('info');
            // 装备
            info.Child('edit_equip_be').active = isCanEditEquip || isConfPawn;
            this.updateEquipInfo(info.Child('equip_show_be'));
            // 宠物
            info.Child('edit_pet_be').active = isCanEditEquip && isYyj;
            var petNode = info.Child('pet_show_be');
            if (petNode.active = isYyj) {
                this.updatePetInfo(petNode);
            }
            // 背包
            var bagNode = info.Child('bag');
            if (isEquip) {
                this.updateBag(bagNode, data.treasures);
            }
            else {
                bagNode.Swih('');
            }
        }
        // 同步装备设置
        this.settingSyncEquipNode_.active = false;
        this.syncEquipMaskNode_.active = false;
        if (this.syncEquipNode_.active = !isConfPawn && isCanEditEquip) {
            var val = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0;
            this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
        }
        // 归属
        var from = this.fromNode_;
        var showMarchSpeed = data.marchSpeed > 0 && !data.uid, showArmy = !!data.armyName, showPlayer = hasOwner && !isOwner;
        if (from.active = showMarchSpeed || showArmy || showPlayer) {
            // 行军速度
            if (from.Child('march_speed').active = showMarchSpeed) {
                from.Child('march_speed/val', cc.Label).setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            }
            // 所属军队
            var army = from.Child('army');
            if (army.active = showArmy) {
                army.Child('val/edit').active = isCanEdit && isInArea && !isFromDrillground;
                this.updateArmyInfo(army, data.armyName);
            }
            var playerInfo = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
            // 所属玩家
            if (from.Child('player').active = showPlayer) {
                from.Child('player/val', cc.Label).string = ut.nameFormator((playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.nickname) || '???', 8);
            }
            // 所属联盟
            var alliName = playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.allianceName;
            if (from.Child('alli').active = showPlayer && !!alliName) {
                from.Child('alli/val', cc.Label).string = alliName;
            }
        }
        // 按钮
        do {
            var buttonRoot = this.buttonNode_.Child('root');
            var isEditPos = isCanEdit && isInArea && !isFromDrillground;
            if (isEditPos) {
                buttonRoot.Swih('edit_pos_be');
            }
            else if (drillInfo) {
                var node = buttonRoot.Swih('cancel_' + drillInfo.type + '_be')[0];
                if (drillInfo.type === 'drill') {
                    node.Child('val').setLocaleKey(data.isMachine() ? 'ui.button_cancel_sc' : 'ui.button_cancel_drill');
                }
                else if (drillInfo.type === 'cure') {
                    node.Child('val').setLocaleKey('ui.button_cancel_cure');
                }
            }
            else {
                this.buttonNode_.active = false;
                break;
            }
            var player = GameHelper_1.gameHpr.player;
            var isLving = player.isInPawnLvingQueue(data.uid);
            this.buttonNode_.active = true;
            this.buttonNode_.Child('uplv_be').active = isEditPos && !isMachine && !data.isMaxLv() && !isLving;
            var avatarNode = this.buttonNode_.Child('avatar_be');
            var isCanAvatar = isOwner && !isBattleing && !isHero && !drillInfo;
            if (avatarNode.active = isCanAvatar) {
                var army = GameHelper_1.gameHpr.areaCenter.getArmy(data.aIndex, data.armyUid);
                var isNotAvatar = !army || army.pawns.some(function (m) { return m.isHero(); }) || player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL) <= 0 || player.getHeroSlots().every(function (m) { return !m.hero; }) || !player.checkCanAvatarPawn(data.id);
                avatarNode.opacity = isNotAvatar ? 100 : 255;
            }
        } while (false);
        // 刷新buff
        this.updateBuffs();
        this.buffNode_.stopAllActions();
        this.buffNode_.opacity = 0;
        cc.tween(this.buffNode_).delay(0.2).to(0.5, { opacity: 255 }).start();
    };
    PawnInfoPnlCtrl.prototype.onRemove = function () {
        GameHelper_1.gameHpr.uiShowPawnData = null;
        this.selectEquipBoxNode_.active = false;
        this.closeArmyList();
        this.syncInfoToServer();
        this.data.recordCurrHp(false);
        this.data = null;
    };
    PawnInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/head_be_n/attack_speed/edit/0/attack_speed_be
    PawnInfoPnlCtrl.prototype.onClickAttackSpeed = function (event, data) {
        var type = event.target.parent.name;
        this.data.changeAttackSpeed(type === '0' ? -1 : 1);
        this.attackSpeedEditLbl_.string = this.data.attackSpeed + '';
    };
    // path://root/button_n/root/edit_pos_be
    PawnInfoPnlCtrl.prototype.onClickEditPos = function (event, data) {
        this.emit(EventType_1.default.EDIT_PAWN_POS, this.data.aIndex, this.data.uid);
        ViewHelper_1.viewHelper.hidePnl('area/AreaArmy');
        this.hide();
    };
    // path://root/button_n/root/cancel_drill_be
    PawnInfoPnlCtrl.prototype.onClickCancelDrill = function (event, _) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox(this.data.isMachine() ? 'ui.cancel_sc_no_back_cost_tip' : 'ui.cancel_drill_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelDrill(_this.drillInfo); },
                cancel: function () { },
            });
        }
        this.cancelDrill(this.drillInfo);
    };
    // path://root/button_n/root/cancel_lving_be
    PawnInfoPnlCtrl.prototype.onClickCancelLving = function (event, _) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_lving_no_back_cost_tip', {
                ok: function () {
                    _this.cancelLving(_this.drillInfo);
                },
                cancel: function () { },
            });
        }
        this.cancelLving(this.drillInfo);
    };
    // path://root/equip_n/info/edit_equip_be
    PawnInfoPnlCtrl.prototype.onClickEditEquip = function (event, data) {
        this.showEquipList();
    };
    // path://select_equip_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectEquipBox = function (event, data) {
        this.closeEquipList();
    };
    // path://select_equip_box_be_n/root/list/view/content/equip_item_be
    PawnInfoPnlCtrl.prototype.onClickEquipItem = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        this.data.changeEquip(data);
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        if (this.data.id === 3104) { //陌刀兵还需要刷新技能信息
            this.skillNode_.Child('root/skills').children.forEach(function (m) {
                if (!m.Data) {
                }
                else if (m.Data.type === Enums_1.PawnSkillType.INSTABILITY_ATTACK) {
                    m.Data.desc_params = [_this.data.getAttackText()];
                }
                else if (m.Data.type === Enums_1.PawnSkillType.PEOPLE_BROKEN) {
                    m.Data.desc_params = [_this.data.getAttackTextByIndex(2)];
                }
            });
        }
        if (GameHelper_1.gameHpr.guide.isGuideById(3)) {
            this.closeEquipList();
        }
        else {
            this.updateEquipListSelect(data);
        }
    };
    // path://root/from_n/army/val/edit/edit_army_be
    PawnInfoPnlCtrl.prototype.onClickEditArmy = function (event, data) {
        this.showArmyList();
    };
    // path://select_army_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectArmyBox = function (event, data) {
        this.closeArmyList();
    };
    // path://select_army_box_be_n/root/list/army_item_be
    PawnInfoPnlCtrl.prototype.onClickArmyItem = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        if (data) {
            if (this.data.isHero() && data.isHasHero()) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_ONLY_AVATAR_ONE);
            }
            return this.changeArmy(data.uid, false);
        }
        else if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.changeArmy(name, true);
            }
        });
    };
    // path://root/skill_n/root/skills/skill_be
    PawnInfoPnlCtrl.prototype.onClickSkill = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/SkillInfoBox', event.target.Data);
    };
    // path://root/equip_n/info/equip_show_be
    PawnInfoPnlCtrl.prototype.onClickEquipShow = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data === null || data === void 0 ? void 0 : data.id) {
            ViewHelper_1.viewHelper.showPnl('common/EquipInfoBox', data);
        }
        else {
            this.showEquipList();
        }
    };
    // path://root/equip_n/info/bag/bag_be
    PawnInfoPnlCtrl.prototype.onClickBag = function (event, data) {
        if (this.data.owner !== GameHelper_1.gameHpr.getUid()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_OPEN_OTHER_TREASURE);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', [event.target.Data]);
        }
    };
    // path://root/attr_n_be
    PawnInfoPnlCtrl.prototype.onClickAttr = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/PawnAttrBox', this.data);
    };
    // path://select_skin_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectSkinBox = function (event, data) {
        this.closeSkinList(this.selectSkinBoxNode_.Child('skin_show').Data || 0);
    };
    // path://select_skin_box_be_n/root/list/view/content/skin_item_be
    PawnInfoPnlCtrl.prototype.onClickSkinItem = function (event, _) {
        var data = event.target.Data;
        data && this.updateSkinListSelect(data);
    };
    // path://root/head_be_n
    PawnInfoPnlCtrl.prototype.onClickHead = function (event, data) {
        audioMgr.playSFX('click');
        this.showSkinList();
    };
    // path://select_skin_box_be_n/root/button/buy_skin_be
    PawnInfoPnlCtrl.prototype.onClickBuySkin = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        if (data && data.gold > 0) {
            if (GameHelper_1.gameHpr.user.getGold() < data.gold) {
                return ViewHelper_1.viewHelper.showGoldNotEnough();
            }
            GameHelper_1.gameHpr.user.buyPawnSkin(data.id).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.closeSkinList(data.id);
                }
            });
        }
    };
    // path://select_equip_box_be_n/sync_equip_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncEquip = function (event, data) {
        this.settingSyncEquipNode_.active = true;
        this.syncEquipMaskNode_.active = true;
        var val = (GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0) + '';
        this.settingSyncEquipNode_.Child('lay').children.forEach(function (m) {
            m.Component(cc.Toggle).isChecked = m.name === val;
        });
    };
    // path://select_equip_box_be_n/sync_equip_mask_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncEquipMask = function (event, data) {
        var it = this.settingSyncEquipNode_.Child('lay').children.find(function (m) { return m.Component(cc.Toggle).isChecked; });
        var val = it ? Number(it.name) : 0;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF, val);
        this.settingSyncEquipNode_.active = false;
        this.syncEquipMaskNode_.active = false;
        this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
    };
    // path://root/button_n/uplv_be
    PawnInfoPnlCtrl.prototype.onClickUplv = function (event, data) {
        var _this = this;
        if (!this.data || this.data.isMaxLv() || this.data.isMachine()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('area/UpPawnLv', this.data, function (ok) { return __awaiter(_this, void 0, void 0, function () {
            var cond;
            var _a, _b;
            return __generator(this, function (_c) {
                if (!ok || !this.isValid) {
                    return [2 /*return*/, true];
                }
                else if (GameHelper_1.gameHpr.player.getUpScroll() < 1) {
                    ViewHelper_1.viewHelper.showAlert('toast.res_deficiency', { params: [Constant_1.CTYPE_NAME[Enums_1.CType.UP_SCROLL]] });
                    return [2 /*return*/, false];
                }
                cond = GameHelper_1.gameHpr.checkCondsByString((_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.attrJson) === null || _b === void 0 ? void 0 : _b.lv_cond);
                if ((cond === null || cond === void 0 ? void 0 : cond.type) === Enums_1.CType.BUILD_LV) {
                    ViewHelper_1.viewHelper.showAlert('toast.build_cond_unmet', { params: ['buildText.name_' + cond.id, cond.count] });
                    return [2 /*return*/, false];
                }
                return [2 /*return*/, this.upLvByUseScroll()];
            });
        }); });
    };
    // path://root/buff/buff_n/buff_icon_be
    PawnInfoPnlCtrl.prototype.onClickBuffIcon = function (event, _) {
        var data = event.target.Data;
        if (!data) {
        }
        else if (data.iconType === 3) { //韬略
            ViewHelper_1.viewHelper.showPnl('area/PawnStrategyInfo', this.data);
        }
        else if (data.iconType === 4) { //政策
            ViewHelper_1.viewHelper.showPnl('area/PolicyBuffInfo', data.buffs);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/BuffInfoBox', data);
        }
    };
    // path://root/head_be_n/lv/edit/0/edit_lv_be
    PawnInfoPnlCtrl.prototype.onClickEditLv = function (event, data) {
        var type = event.target.parent.name;
        var val = type === '0' ? -1 : 1;
        var lv = this.data.lv + val;
        if (lv > 6) {
            lv = 1;
        }
        else if (lv < 1) {
            lv = 6;
        }
        this.localUplv(this.data, lv);
    };
    // path://root/button_n/avatar_be
    PawnInfoPnlCtrl.prototype.onClickAvatar = function (event, _) {
        var _this = this;
        var pawn = this.data;
        var army = GameHelper_1.gameHpr.areaCenter.getArmy(pawn.aIndex, pawn.armyUid);
        if (!army) {
            return;
        }
        else if (army.pawns.some(function (m) { return m.isHero(); })) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_ONLY_AVATAR_ONE);
        }
        else if (GameHelper_1.gameHpr.player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL) <= 0) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_build_first', { params: ['buildText.name_' + Enums_1.BUILD_NID.HERO_HALL] });
        }
        else if (GameHelper_1.gameHpr.player.getHeroSlots().every(function (m) { return !m.hero; })) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_worship_hero'); //请先在英雄殿供奉一个英雄
        }
        ViewHelper_1.viewHelper.showPnl('area/SelectAvatarHero', pawn.id, function (portrayalId) {
            if (!_this.isValid || !portrayalId) {
                return;
            }
            GameHelper_1.gameHpr.player.changePawnPortrayal(pawn.aIndex, pawn.armyUid, pawn.uid, portrayalId).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.hide();
                    ViewHelper_1.viewHelper.hidePnl('area/AreaArmy');
                    // 聚焦士兵
                    _this.emit(EventType_1.default.FOCUS_PAWN, { index: pawn.aIndex, uid: pawn.uid, point: pawn.point });
                }
            });
        });
    };
    // path://root/skill_n/root/portrayal_skill_be
    PawnInfoPnlCtrl.prototype.onClickPortrayalSkill = function (event, data) {
        audioMgr.playSFX('click');
        if (this.data.portrayal) {
            ViewHelper_1.viewHelper.showPnl('common/PortrayalInfoBox', this.data.portrayal, 'pawn', this.data.owner);
        }
    };
    // path://root/equip_n/info/pet_show_be
    PawnInfoPnlCtrl.prototype.onClickPetShow = function (event, _) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        if (id) {
            ViewHelper_1.viewHelper.showPnl('common/PetInfoBox', id, Constant_1.SUMMON_LV[this.data.lv]);
        }
        else {
            this.showPetList();
        }
    };
    // path://root/equip_n/info/edit_pet_be
    PawnInfoPnlCtrl.prototype.onClickEditPet = function (event, data) {
        this.showPetList();
    };
    // path://select_pet_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectPetBox = function (event, data) {
        this.closePetList();
    };
    // path://select_pet_box_be_n/root/list/view/content/pet_item_be
    PawnInfoPnlCtrl.prototype.onClickPetItem = function (event, _) {
        var id = event.target.Data;
        if (id === Constant_1.DEFAULT_PET_ID || GameHelper_1.gameHpr.player.getKillRecordMap()[id]) {
            this.data.setPetId(id);
        }
        this.updatePetListSelect(id);
    };
    // path://select_skin_box_be_n/sync_skin_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncSkin = function (event, data) {
        this.settingSyncSkinNode_.active = true;
        this.syncSkinMaskNode_.active = true;
        var val = (GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0) + '';
        this.settingSyncSkinNode_.Child('lay').children.forEach(function (m) {
            m.Component(cc.Toggle).isChecked = m.name === val;
        });
    };
    // path://select_skin_box_be_n/sync_skin_mask_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncSkinMask = function (event, data) {
        var it = this.settingSyncSkinNode_.Child('lay').children.find(function (m) { return m.Component(cc.Toggle).isChecked; });
        var val = it ? Number(it.name) : 0;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF, val);
        this.settingSyncSkinNode_.active = false;
        this.syncSkinMaskNode_.active = false;
        this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
    };
    // path://root/button_n/root/cancel_cure_be
    PawnInfoPnlCtrl.prototype.onClickCancelCure = function (event, data) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelCure(_this.drillInfo); },
                cancel: function () { },
            });
        }
        this.cancelCure(this.drillInfo);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    PawnInfoPnlCtrl.prototype.onUpdatePawnTreasure = function (pawn) {
        if (pawn.uid === this.data.uid) {
            this.updateBag(this.equipNode_.Child('info/bag'), pawn.treasures);
        }
    };
    PawnInfoPnlCtrl.prototype.onUpdateArmy = function (army) {
        if (this.data.armyUid === army.uid) {
            var uid_1 = this.data.uid;
            this.data = army.pawns.find(function (m) { return m.uid === uid_1; }) || this.data;
        }
    };
    // 刷新buff
    PawnInfoPnlCtrl.prototype.onUpdateBuff = function () {
        this.updateBuffs();
    };
    // 刷新士兵信息
    PawnInfoPnlCtrl.prototype.onUpdatePawnInfo = function () {
        this.updateLv();
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        this.updateSkills();
        var uplvButton = this.buttonNode_.Child('uplv_be');
        uplvButton.active = uplvButton.active && !this.data.isMaxLv();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateLv = function () {
        var data = this.data;
        var lvNode = this.headNode_.Child('lv'), isNoLv = data.isMachine() || data.isBuilding();
        if (lvNode.Child('edit').active = !isNoLv && this.fromTo === 'book') {
            lvNode.Child('name').Color('#756963').setLocaleKey('ui.level');
            lvNode.Child('val').active = false;
            this.lvEditLbl_.string = data.lv + '';
        }
        else if (lvNode.Child('val').active = !isNoLv) {
            var hasOwner = !!data.owner, isOwner = data.isOwner(), isLving = GameHelper_1.gameHpr.player.isInPawnLvingQueue(data.uid);
            var lvColor = hasOwner && data.isMaxLv() ? '#B6A591' : '#756963';
            lvNode.Child('name').Color(lvColor).setLocaleKey('ui.level');
            lvNode.Child('val/0', cc.Label).Color(lvColor).string = data.lv + '';
            lvNode.Child('val/up').active = isOwner && isLving && !this.drillInfo;
        }
        else {
            lvNode.Child('name').Color('#B6A591').setLocaleKey('ui.not_lv');
        }
    };
    PawnInfoPnlCtrl.prototype.updateSkills = function () {
        this.skillNode_.active = ViewHelper_1.viewHelper.updatePawnSkills(this.skillNode_.Child('root'), this.data, this.key);
    };
    PawnInfoPnlCtrl.prototype.updateListPosition = function () {
        if (this.preRootHeight !== this.root.height) {
            this.preRootHeight = this.root.height;
            // 皮肤列表
            var node = this.selectSkinBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('skin_list'), this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            this.settingSyncSkinNode_.setPosition(node.getPosition());
            this.settingSyncSkinNode_.scale = this.root.scale;
            node = this.selectSkinBoxNode_.Child('skin_show');
            node.setPosition(ut.convertToNodeAR(this.headNode_, this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            node = this.syncSkinNode_;
            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('sync_pos'), this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            // 装备列表
            node = this.selectEquipBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_list'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            this.settingSyncEquipNode_.setPosition(node.getPosition());
            this.settingSyncEquipNode_.scale = this.root.scale;
            node = this.selectEquipBoxNode_.Child('equip_show');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_show_be'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            node = this.syncEquipNode_;
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/sync_pos'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            // 宠物列表
            node = this.selectPetBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_list'), this.selectPetBoxNode_));
            node.scale = this.root.scale;
            node = this.selectPetBoxNode_.Child('pet_show');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_show_be'), this.selectPetBoxNode_));
            node.scale = this.root.scale;
            // 军队列表
            node = this.selectArmyBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.fromNode_.Child('army/val/edit/list'), this.selectArmyBoxNode_));
            node.scale = this.root.scale;
        }
    };
    // 显示皮肤列表 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.showSkinList = function () {
        var _this = this;
        var _a, _b;
        this.headNode_.Child('edit').active = this.headNode_.Child('val').active = false;
        this.selectSkinBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectSkinBoxNode_.Child('root');
        var skins = GameHelper_1.gameHpr.user.getPawnSkins(this.data.id), len = skins.length;
        var sv = root.Child('list', cc.ScrollView);
        sv.Child('empty').active = len === 0;
        sv.Items(skins, function (it, data) {
            it.Data = data;
            var valNode = it.Child('val');
            valNode.opacity = data.unlock ? 255 : 120;
            ResHelper_1.resHelper.loadPawnHeadIcon((data === null || data === void 0 ? void 0 : data.id) || _this.data.id, valNode, _this.key);
        });
        var skinId = this.data.skinId;
        var index = skins.findIndex(function (m) { return m.id === skinId; });
        if (index === -1) {
            index = 0;
            skinId = this.data.skinId = (_b = (_a = skins[index]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0;
        }
        var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
        var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
        var minx = Math.max(w - pw, 0);
        sv.stopAutoScroll();
        sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        this.updateSkinListSelect(skins[index]);
    };
    PawnInfoPnlCtrl.prototype.closeSkinList = function (skinId) {
        this.headNode_.Child('val').active = true;
        this.headNode_.Child('edit').active = this.isCanEditSkin;
        this.selectSkinBoxNode_.active = false;
        if (this.data.skinId !== skinId) {
            this.data.skinId = skinId;
            ResHelper_1.resHelper.loadPawnHeadIcon(this.data.getViewId(), this.headNode_.Child('val'), this.key);
            if (this.fromTo !== 'book') {
                if (!this.data.uid && !this.drillInfo) {
                    GameHelper_1.gameHpr.player.changeConfigPawnInfoByData(this.data);
                }
                eventCenter.emit(EventType_1.default.CHANGE_PAWN_SKIN, this.data);
            }
        }
    };
    PawnInfoPnlCtrl.prototype.updateSkinListSelect = function (skin) {
        var _a;
        var root = this.selectSkinBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var _a;
            var id = ((_a = it.Data) === null || _a === void 0 ? void 0 : _a.id) || 0;
            it.Child('select1').active = it.Child('select2').active = skin.id === id;
            it.Component(cc.Button).interactable = skin.id !== id;
        });
        // 显示选择的
        if (skin.unlock || !skin.id) {
            var node = this.selectSkinBoxNode_.Child('skin_show');
            node.Data = skin.id;
            ResHelper_1.resHelper.loadPawnHeadIcon(skin.id || this.data.id, node.Child('val'), this.key);
        }
        // 显示信息
        var desc = skin.id ? ((_a = assetsMgr.getJsonData('pawnSkin', skin.id)) === null || _a === void 0 ? void 0 : _a.desc) || '' : 'ui.default_pawn_skin';
        root.Child('desc').setLocaleKey(desc);
        var stateNode = root.Child('state');
        if (stateNode.active = !!skin.id && !skin.gold) {
            stateNode.Color(skin.unlock ? '#4AB32E' : '#A18876').setLocaleKey(skin.unlock ? 'ui.yet_owned' : 'ui.not_owned');
        }
        var buttonNode = root.Child('button');
        if (buttonNode.active = !!skin.gold) {
            var buyNode = buttonNode.Child('buy_skin_be');
            buyNode.Data = skin;
            buyNode.Child('lay/gold/val', cc.Label).string = skin.gold + '';
        }
    };
    // 刷新装备信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateEquipInfo = function (node, equip) {
        equip = node.Data = equip || this.data.equip;
        var isOwner = this.data.isOwner() && this.fromTo !== 'drillground';
        var isCanEditEquip = isOwner && !this.isBattleing && this.data.getState() === Enums_1.PawnState.NONE && (!this.fromTo || this.fromTo === 'area_army');
        node.Component(cc.Button).interactable = !!(equip === null || equip === void 0 ? void 0 : equip.id) || this.isConfPawn || isCanEditEquip;
        if (equip === null || equip === void 0 ? void 0 : equip.id) {
            ResHelper_1.resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount());
        }
        else if (isOwner || this.isCanEdit || this.isConfPawn) {
            node.Swih('add')[0].Child('dot').active = GameHelper_1.gameHpr.player.getEquips().length > 0;
        }
        else {
            node.Swih('');
        }
    };
    // 显示装备列表
    PawnInfoPnlCtrl.prototype.showEquipList = function () {
        var _this = this;
        var _a, _b;
        if (this.data.isOwner() && this.isBattleing) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        this.selectEquipBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectEquipBoxNode_.Child('root');
        var equips = GameHelper_1.gameHpr.player.getPawnEquips(this.data.id), len = equips.length;
        var randomArr = ut.stringToNumbers((_b = (_a = assetsMgr.getJson('equipBase').get('exclusive_pawn', this.data.id)) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.random, ',');
        var isHp = randomArr[0] > randomArr[1];
        equips.sort(function (a, b) {
            var aw = a.isExclusive() ? 1 : 0, bw = b.isExclusive() ? 1 : 0;
            if (isHp) {
                aw = aw * 10 + (a.hp ? 1 : 0);
                bw = bw * 10 + (b.hp ? 1 : 0);
            }
            else {
                aw = aw * 10 + (a.attack ? 1 : 0);
                bw = bw * 10 + (b.attack ? 1 : 0);
            }
            return bw - aw;
        });
        var equip = this.equipNode_.Child('info/equip_show_be').Data;
        var sv = root.Child('list', cc.ScrollView);
        sv.Child('empty').active = len === 0;
        sv.Items(equips, function (it, data) {
            it.Data = data;
            ResHelper_1.resHelper.loadEquipIcon(data.id, it.Child('val'), _this.key, data.getSmeltCount());
            it.Child('recommend').active = false;
        });
        if (equip === null || equip === void 0 ? void 0 : equip.uid) {
            var index = equips.findIndex(function (m) { return m.uid === equip.uid; });
            var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
            var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
            var minx = Math.max(w - pw, 0);
            sv.stopAutoScroll();
            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        }
        else {
            sv.scrollToLeft();
        }
        this.updateEquipListSelect(equip);
    };
    // 刷新选择信息
    PawnInfoPnlCtrl.prototype.updateEquipListSelect = function (equip) {
        var _this = this;
        var root = this.selectEquipBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var data = it.Data;
            var select = it.Child('select').active = (equip === null || equip === void 0 ? void 0 : equip.uid) === (data === null || data === void 0 ? void 0 : data.uid);
            it.Component(cc.Button).interactable = !select;
        });
        // 显示选择的
        var node = this.selectEquipBoxNode_.Child('equip_show');
        if (equip === null || equip === void 0 ? void 0 : equip.id) {
            ResHelper_1.resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount());
        }
        else {
            node.Swih('add');
        }
        // 显示信息
        root.Child('empty').active = !(equip === null || equip === void 0 ? void 0 : equip.id);
        var sv = root.Child('info', cc.ScrollView), info = sv.content;
        sv.stopAutoScroll();
        info.y = 0;
        if (sv.setActive(!!(equip === null || equip === void 0 ? void 0 : equip.id))) {
            ViewHelper_1.viewHelper.updateEquipView(info, equip, this.key);
            ut.waitNextFrame(2).then(function () {
                if (_this.isValid) {
                    sv.node.height = Math.min(320, info.height + 4);
                    sv.node.Child('view', cc.Widget).updateAlignment();
                }
            });
        }
    };
    PawnInfoPnlCtrl.prototype.closeEquipList = function () {
        this.selectEquipBoxNode_.active = false;
        this.updateEquipInfo(this.equipNode_.Child('info/equip_show_be'));
    };
    // 刷新宠物信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updatePetInfo = function (node, id) {
        id = node.Data = id || this.data.petId || Constant_1.DEFAULT_PET_ID;
        ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, node.Child('val'), this.key);
    };
    // 显示宠物列表
    PawnInfoPnlCtrl.prototype.showPetList = function () {
        var _this = this;
        if (this.data.isOwner() && this.isBattleing) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        this.selectPetBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectPetBoxNode_.Child('root');
        var pets = assetsMgr.getJson('pawnBase').datas.filter(function (m) { return m.type === Enums_1.PawnType.BEAST && !!m.velocity; }).sort(function (a, b) { return a.drill_time - b.drill_time; }), len = pets.length;
        var id = this.equipNode_.Child('info/pet_show_be').Data;
        var sv = root.Child('list', cc.ScrollView);
        var killRecordMap = GameHelper_1.gameHpr.player.getKillRecordMap();
        sv.Items(pets, function (it, json) {
            it.Data = json.id;
            var icon = it.Child('val');
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(json.id, icon, _this.key);
            icon.opacity = json.id === Constant_1.DEFAULT_PET_ID || killRecordMap[json.id] ? 255 : 120;
        });
        if (id) {
            var index = pets.findIndex(function (m) { return m.id === id; });
            var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
            var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
            var minx = Math.max(w - pw, 0);
            sv.stopAutoScroll();
            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        }
        else {
            sv.scrollToLeft();
        }
        this.updatePetListSelect(id);
    };
    // 刷新选择信息
    PawnInfoPnlCtrl.prototype.updatePetListSelect = function (id) {
        var root = this.selectPetBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            it.Child('select').active = id === it.Data;
            it.Component(cc.Button).interactable = id !== it.Data;
        });
        // 显示选择的
        var node = this.selectPetBoxNode_.Child('pet_show');
        if (id) {
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, node.Swih('val')[0], this.key);
        }
        else {
            node.Swih('add');
        }
        // 显示信息
        var lv = id === Constant_1.DEFAULT_PET_ID || GameHelper_1.gameHpr.player.getKillRecordMap()[id] ? Constant_1.SUMMON_LV[this.data.lv] : 0;
        ViewHelper_1.viewHelper.updatePetView(root.Child('info'), id, lv);
    };
    PawnInfoPnlCtrl.prototype.closePetList = function () {
        this.selectPetBoxNode_.active = false;
        this.updatePetInfo(this.equipNode_.Child('info/pet_show_be'));
    };
    // 刷新军队信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateArmyInfo = function (node, armyName) {
        node = node || this.fromNode_.Child('army');
        armyName = armyName || this.data.armyName;
        node.Child('val/name/val', cc.Label).string = armyName;
    };
    // 显示军队列表
    PawnInfoPnlCtrl.prototype.showArmyList = function () {
        var _a;
        this.selectArmyBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectArmyBoxNode_.Child('root');
        var uid = this.data.armyUid;
        var arr = ((_a = GameHelper_1.gameHpr.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys) || [];
        var armys = [];
        arr.forEach(function (m) {
            if (m.uid == uid) {
                armys.unshift(m);
            }
            else if (m.isCanDrillPawn() && m.getActPawnCount() < Constant_1.ARMY_PAWN_MAX_COUNT) {
                armys.push(m);
            }
        });
        if (armys.length < GameHelper_1.gameHpr.player.getArmyMaxCount()) {
            armys.push(null);
        }
        var len = armys.length;
        root.Swih('list')[0].Items(armys, function (it, data, i) {
            it.Data = data;
            var select = uid === (data === null || data === void 0 ? void 0 : data.uid);
            it.Child('name', cc.Label).Color(select ? '#B6A591' : '#756963').string = (data === null || data === void 0 ? void 0 : data.name) || '';
            it.Child('line').active = i < (len - 1);
            it.Child('select').active = select;
            it.Child('add').active = !data;
            it.Component(cc.Button).interactable = !select;
        });
    };
    PawnInfoPnlCtrl.prototype.closeArmyList = function () {
        this.selectArmyBoxNode_.active = false;
    };
    // 改变军队
    PawnInfoPnlCtrl.prototype.changeArmy = function (newArmyUid, isNewCreate) {
        return __awaiter(this, void 0, void 0, function () {
            var pawn, index, armyUid, uid, attackSpeed, equipUid, skinId, petId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        pawn = this.data;
                        index = pawn.aIndex;
                        armyUid = pawn.armyUid;
                        uid = pawn.uid;
                        attackSpeed = pawn.attackSpeed;
                        equipUid = pawn.equip.uid;
                        skinId = pawn.skinId;
                        petId = pawn.petId;
                        if (!isNewCreate) {
                            pawn.armyUid = newArmyUid;
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnArmy({ index: index, armyUid: armyUid, uid: uid, newArmyUid: newArmyUid, isNewCreate: isNewCreate, attackSpeed: attackSpeed, equipUid: equipUid, skinId: skinId, petId: petId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            pawn.armyUid = armyUid;
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        pawn.changeArmy(data);
                        if (this.isValid) {
                            this.preAttackSpeed = attackSpeed;
                            this.preEquipUid = equipUid;
                            this.preSkinId = skinId;
                            this.updateArmyInfo();
                            this.closeArmyList();
                        }
                        if (this.fromTo === 'area_army') {
                            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, index);
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 背包
    PawnInfoPnlCtrl.prototype.updateBag = function (node, treasures) {
        var _this = this;
        var _a;
        if (this.equipNode_.active && this.data) {
            var data = this.data;
            var cap = Math.max((treasures === null || treasures === void 0 ? void 0 : treasures.length) || 0, ((_a = data.baseJson) === null || _a === void 0 ? void 0 : _a.bag_cap) || 0);
            node.Items(cap, function (it, _, i) {
                var _a;
                var treasure = it.Data = treasures[i];
                it.Swih(treasure ? 'val' : 'empty');
                if (it.Component(cc.Button).interactable = !!treasure) {
                    var state = treasure.rewards.length > 0 ? 1 : 0;
                    ResHelper_1.resHelper.loadIcon('icon/treasure_' + (((_a = treasure.json) === null || _a === void 0 ? void 0 : _a.lv) || 1) + '_' + state, it.Child('val', cc.Sprite), _this.key);
                }
            });
        }
        else {
            node.Swih('');
        }
    };
    // 刷新buff
    PawnInfoPnlCtrl.prototype.updateBuffs = function () {
        var _this = this;
        var _a;
        var buffs = [], policyBuffs = [];
        var heroSkill = (_a = this.data.portrayal) === null || _a === void 0 ? void 0 : _a.skill;
        this.data.buffs.forEach(function (m) {
            var _a, _b;
            if (m.iconType === 4) {
                return policyBuffs.push(m);
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || m.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD) {
                var buff = buffs.find(function (b) { return !!b && (b.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || b.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD); });
                if (buff) {
                    buff.tempParam = m.value;
                }
                else {
                    buffs.push(m);
                }
                return;
            }
            else if (m.type === Enums_1.BuffType.DELAY_DEDUCT_HP) {
                m.tempParam = (_b = (_a = _this.data.getEquipEffectByType(Enums_1.EquipEffectType.FIXED_DAMAGE)) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 0;
            }
            else if (m.type === Enums_1.BuffType.THOUSAND_UMBRELLA) { //千机伞
                var effect = _this.data.getEquipEffectByType(Enums_1.EquipEffectType.THOUSAND_UMBRELLA);
                m.tempParam = [(effect === null || effect === void 0 ? void 0 : effect.value) || 1, (effect === null || effect === void 0 ? void 0 : effect.odds) || 1];
                var v = m.tempParam[m.value];
                if (v) {
                    m.tempParam[m.value] = v * 2;
                }
            }
            else if (m.type === Enums_1.BuffType.TOUGH) { //曹仁 判断是否叠满
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.CAO_REN ? heroSkill.value : 50;
            }
            else if (m.type === Enums_1.BuffType.TIGER_MANIA) { //许褚 虎痴
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.XU_CHU ? heroSkill.value : 200;
            }
            else if (m.type === Enums_1.BuffType.CHECK_ABNEGATION) { //吕蒙 检测克己
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.LV_MENG ? heroSkill.value : 0;
            }
            else if (m.type === Enums_1.BuffType.CHECK_LITTLE_GIRL) { //孙尚香 枭姬
                m.tempParam = _this.data.isMaxLv() ? 2 : 1;
            }
            else if (m.type === Enums_1.BuffType.COURAGEOUSLY) { //典韦 奋勇是否满层
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.DIAN_WEI ? heroSkill.value : 50;
            }
            else if (m.type === Enums_1.BuffType.RECURRENCE) { //孟获 再起
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.MENG_HUO ? heroSkill.value : 15;
            }
            if (m.icon) {
                buffs.push(m);
            }
        });
        buffs.sort(function (a, b) {
            var aw = a ? a.effectType : 100, bw = b ? b.effectType : 100;
            return bw - aw;
        });
        // 是否有韬略
        if (this.data.isHasStrategy()) {
            buffs.unshift({ iconType: 3, icon: 0 });
        }
        // 是否有政策
        if (policyBuffs.length > 0) {
            buffs.unshift({ iconType: 4, icon: 1000, buffs: policyBuffs });
        }
        this.buffNode_.Items(buffs, function (it, data) {
            it.Data = data;
            it.Component(cc.MultiFrame).setFrame(data.iconType);
            ResHelper_1.resHelper.loadBuffIcon(data.icon, it.Child('val'), _this.key, false);
        });
    };
    // 使用卷轴升级士兵
    PawnInfoPnlCtrl.prototype.upLvByUseScroll = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawn, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.syncInfoToServer(true)]; //先同步一下属性
                    case 1:
                        _b.sent(); //先同步一下属性
                        pawn = this.data;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqUseUpScrollUpPawnLv({ index: pawn.aIndex, armyUid: pawn.armyUid, uid: pawn.uid })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                            return [2 /*return*/, false];
                        }
                        GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                        ViewHelper_1.viewHelper.showAlert('toast.up_pawn_lv_succeed');
                        this.localUplv(pawn, data.lv);
                        if (this.fromTo === 'area_army') {
                            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, pawn.index);
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    PawnInfoPnlCtrl.prototype.localUplv = function (pawn, lv) {
        pawn.lv = lv;
        pawn.updateAttrJson();
        pawn.curHp = pawn.getMaxHp();
        pawn.recordCurrHp(true);
        if (this.isValid) {
            this.onUpdatePawnInfo();
        }
    };
    // 同步信息到服务器
    PawnInfoPnlCtrl.prototype.syncInfoToServer = function (wait) {
        return __awaiter(this, void 0, void 0, function () {
            var data, isEquip, isBattleing, isChange, id, equipUid, skinId, attackSpeed, res, syncEquip, syncSkin, res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.fromTo === 'book') {
                            return [2 /*return*/];
                        }
                        data = this.data;
                        isEquip = this.preEquipUid !== data.equip.uid;
                        isBattleing = data.isBattleing() || GameHelper_1.gameHpr.isBattleingByIndex(data.aIndex);
                        isChange = this.preAttackSpeed !== data.attackSpeed || isEquip || this.preSkinId !== data.skinId || this.prePetId !== data.petId;
                        if (!(!data.uid && !this.drillInfo && isChange)) return [3 /*break*/, 2];
                        id = data.id, equipUid = data.equip.uid, skinId = data.skinId, attackSpeed = data.attackSpeed;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangeConfigPawnEquip({ id: id, equipUid: equipUid, skinId: skinId, attackSpeed: attackSpeed }, wait)];
                    case 1:
                        res = _a.sent();
                        if (!res.err) {
                            GameHelper_1.gameHpr.player.changeConfigPawnInfo(id, equipUid, skinId, attackSpeed);
                        }
                        this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, data.index);
                        return [3 /*break*/, 4];
                    case 2:
                        if (!(data.isOwner() && !isBattleing && isChange)) return [3 /*break*/, 4];
                        syncEquip = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0;
                        syncSkin = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnAttr({
                                index: data.aIndex,
                                armyUid: data.armyUid,
                                uid: data.uid,
                                attackSpeed: data.attackSpeed,
                                equipUid: data.equip.uid,
                                syncEquip: syncEquip,
                                skinId: data.skinId,
                                syncSkin: syncSkin,
                                petId: data.petId,
                            }, wait)];
                    case 3:
                        res = _a.sent();
                        if (!res.err && !!syncEquip && isEquip) {
                            ViewHelper_1.viewHelper.showAlert('toast.replace_pawn_equp_suc_' + syncEquip, { params: ['pawnText.name_' + data.id], showTime: 2 });
                        }
                        this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, data.index);
                        _a.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 取消招募
    PawnInfoPnlCtrl.prototype.cancelDrill = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        var isMachine = this.data.isMachine();
        NetHelper_1.netHelper.reqCancelDrillPawn({ index: index, buildUid: info.buid, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateOutputByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyDrillPawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnDrillQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: isMachine ? 'ui.cancel_sc_tip' : 'ui.cancel_drill_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // 取消训练
    PawnInfoPnlCtrl.prototype.cancelLving = function (info) {
        var _this = this;
        var index = info.index;
        var uid = info.uid;
        var id = info.id;
        var lv = info.lv;
        NetHelper_1.netHelper.reqCancelPawnLving({ index: index, uid: uid }).then(function (res) {
            var _a;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                GameHelper_1.gameHpr.player.updatePawnLevelingQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_a = data.needCost) === null || _a === void 0 ? void 0 : _a.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', { text: 'ui.cancel_lving_tip', id: id, cost: data.needCost });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // 取消治疗
    PawnInfoPnlCtrl.prototype.cancelCure = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        NetHelper_1.netHelper.reqCancelCurePawn({ index: index, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateOutputByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnCuringQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                _this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: 'ui.cancel_cure_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    PawnInfoPnlCtrl = __decorate([
        ccclass
    ], PawnInfoPnlCtrl);
    return PawnInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PawnInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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