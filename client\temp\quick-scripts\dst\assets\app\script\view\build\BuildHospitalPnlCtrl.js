
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildHospitalPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '173e2FGrUpPEKdeEFfXjoOt', 'BuildHospitalPnlCtrl');
// app/script/view/build/BuildHospitalPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ArmyObj_1 = require("../../model/area/ArmyObj");
var PawnObj_1 = require("../../model/area/PawnObj");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var BuildHospitalPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildHospitalPnlCtrl, _super);
    function BuildHospitalPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.sortSelectNode_ = null; // path://root/pages_n/1/info/pawn/sort/sort_select_be_n
        _this.queueSv_ = null; // path://root/pages_n/1/cure/content/queue_sv
        _this.upTimeNode_ = null; // path://root/pages_n/1/cure/x/up_time_be_n
        //@end
        _this.PKEY_TAB = 'HOSPITAL_TAB';
        _this.PKEY_SELECT_ARMY = 'HOSPITAL_SELECT_ARMY';
        _this.PKEY_SELECT_PAWN = 'HOSPITAL_SELECT_PAWN';
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.data = null;
        _this.cureProgressTween = {};
        _this.currSelectSort = 0; // 当前选择的排序方式
        _this.preSelectIndex = -1; // 治疗的目标在伤兵中的下标
        _this.tempCreateArmy = null;
        _this.selectArmy = null;
        _this.tempArmySortWeightMap = {};
        return _this;
    }
    BuildHospitalPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PAWN_INJURY_QUEUE] = this.onUpdatePawnInjuryQueue, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdatePawnCuringQueue, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.CHANGE_PAWN_EQUIP] = this.onChangePawnEquip, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.ADD_ARMY] = this.onUpdateArmy, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.REMOVE_ARMY] = this.onUpdateArmy, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateArmy, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmy, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_BATTLE_ARMY_BY_UI] = this.onUpdateArmy, _o.enter = true, _o),
        ];
    };
    BuildHospitalPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.areaCenter = this.getModel('areaCenter');
                return [2 /*return*/];
            });
        });
    };
    BuildHospitalPnlCtrl.prototype.onEnter = function (data, tab) {
        var _a;
        this.data = data;
        this.tempCreateArmy = this.user.getTempPreferenceMap(Enums_1.PreferenceKey.TEMP_CREATE_ARMY);
        var cond = this.pagesNode_.Child('1/info/cond');
        cond.Child('need/title/layout/val').setLocaleKey('ui.drill_cost', 'ui.button_cure');
        this.pagesNode_.Child('1/cure/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_cure');
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
        // 排序选择
        this.selectSortItem(this.sortSelectNode_, (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT)) !== null && _a !== void 0 ? _a : 5, true);
    };
    BuildHospitalPnlCtrl.prototype.onRemove = function () {
        this.selectArmy = null;
        this.tempArmySortWeightMap = {};
        ViewHelper_1.viewHelper.closePopupBoxList(this.sortSelectNode_);
        this.showCreateArmyFingerTip(false);
        if (this.currSelectSort !== GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT)) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT, this.currSelectSort);
        }
        this.user.setTempPreferenceData(Enums_1.PreferenceKey.TEMP_CREATE_ARMY, this.tempCreateArmy);
    };
    BuildHospitalPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildHospitalPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            // viewHelper.updateBuildBaseUI(node, this.data, this.key)
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            // 显示当前的军队列表
            this.selectArmy = null;
            this.tempArmySortWeightMap = {};
            this.updateArmyList(true, node);
            // 显示可治疗的士兵
            this.updateInjuryList(true, node);
            // 费用
            this.updateCureCost(node);
            // 治疗列表
            this.updateCureQueue(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildHospitalPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/info/pawn/list/view/content/pawn_be
    BuildHospitalPnlCtrl.prototype.onClickPawn = function (event, _data) {
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data, uid = data === null || data === void 0 ? void 0 : data.data.uid;
        if (!data) {
            return;
        }
        else if (uid === this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)) {
            uid && ViewHelper_1.viewHelper.showPnl('area/PawnInfo', data.pawn);
            return;
        }
        this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, uid);
        this.preSelectIndex = this.player.getInjuryPawns().findIndex(function (m) { return m.uid === uid; });
        this.updatePawnSelect(false);
        this.updateCureCost();
    };
    // path://root/pages_n/1/army/list/view/content/army_be
    BuildHospitalPnlCtrl.prototype.onClickArmy = function (event, _data) {
        var _a;
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data;
        if (!data) {
            if (this.tempCreateArmy) {
                return ViewHelper_1.viewHelper.showAlert('toast.yet_has_empty_army');
            }
            return this.showCreateArmyUI();
        }
        else if (!data.army) {
            return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
        }
        else if (data.uid !== ((_a = this.selectArmy) === null || _a === void 0 ? void 0 : _a.uid)) {
            this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid);
            this.updateArmySelect(data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/state/cure_be
    BuildHospitalPnlCtrl.prototype.onClickCure = function (event, data) {
        var _this = this;
        var pawnUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        if (!pawnUid) {
            return;
        }
        var area = this.areaCenter.getArea(this.data.aIndex);
        if (!area) {
            return;
        }
        else if (!this.selectArmy) {
            if (area.armys.length === 0 || area.armys.every(function (m) { return m.pawns.length >= Constant_1.ARMY_PAWN_MAX_COUNT; })) { //一个军队也没有
                ViewHelper_1.viewHelper.showAlert('toast.please_create_army', {
                    cb: function () {
                        if (_this.isValid && !_this.player.isArmyCountFull() && !GameHelper_1.gameHpr.isNoLongerTip('no_army')) {
                            _this.showCreateArmyFingerTip(true);
                        }
                    }
                });
            }
            else {
                ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
            }
            return;
        }
        var selectArmyUid = this.selectArmy.uid, tempArmyUid = '', armyName = '';
        var army = area.getArmyByUid(selectArmyUid) || this.getTempCreateArmy(selectArmyUid);
        if (!army) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_NOT_EXIST);
        }
        else if (army.uid.startsWith('temp_')) {
            armyName = army.name;
        }
        else {
            tempArmyUid = army.uid;
        }
        this.areaCenter.curePawnToServer(this.data.aIndex, tempArmyUid, armyName, pawnUid).then(function (res) {
            var _a;
            if (!_this.isValid) {
            }
            else if (res.err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
            }
            else if (res.err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name');
            } /* else if (res.err === ecode.ANTI_CHEAT) {
                viewHelper.showPnl('main/AntiCheat')
            } */
            else if (res.err) {
                ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                audioMgr.playSFX('build/sound_ui_00' + (_this.data.id === 2010 ? '7' : '6'));
                var army_1 = res.army;
                if (((_a = _this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === selectArmyUid) {
                    _this.tempCreateArmy = null;
                    if (_this.selectArmy)
                        _this.selectArmy.uid = army_1.uid;
                    if (_this.tempArmySortWeightMap[selectArmyUid]) {
                        _this.tempArmySortWeightMap[army_1.uid] = _this.tempArmySortWeightMap[selectArmyUid];
                        delete _this.tempArmySortWeightMap[selectArmyUid];
                    }
                    _this.player.getBaseArmys().push({
                        index: _this.data.aIndex,
                        uid: army_1.uid,
                        name: army_1.name,
                        state: Enums_1.ArmyState.CURING,
                        pawns: [],
                    });
                }
                var node = _this.pagesNode_.Child(1);
                _this.updateCureQueue(node);
                _this.updateInjuryList(false, node);
                // this.updateArmyList(false, node)
                // this.updateCureCost(node)
                _this.updateCureCost(node);
            }
        });
    };
    // path://root/pages_n/1/cure/content/0/cure_pawn_be
    BuildHospitalPnlCtrl.prototype.onClickCurePawn = function (event, _data) {
        audioMgr.playSFX('click');
        var data = event.target.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByCureInfo(data), data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/delete_be
    BuildHospitalPnlCtrl.prototype.onClickDelete = function (event, _data) {
        var _this = this;
        var uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN) || '';
        if (uid) {
            if (this.player.getCuringPawnsQueue().some(function (m) { return m.uid === uid; })) { // 治疗中无法删除
                this.updateCureButton(uid);
                return ViewHelper_1.viewHelper.showAlert('toast.delete_curing_pawn_tip');
            }
            var data = this.player.getInjuryPawns().find(function (m) { return m.uid === uid; });
            if (data) {
                ViewHelper_1.viewHelper.showMessageBox('ui.giveup_cure_tip', {
                    params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv])],
                    ok: function () { return _this.isValid && _this.giveUpCure(uid); },
                    cancel: function () { },
                });
            }
        }
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n
    BuildHospitalPnlCtrl.prototype.onClickSortSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n/select_mask_be
    BuildHospitalPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n/mask/root/sort_items_nbe
    BuildHospitalPnlCtrl.prototype.onClickSortItems = function (event, data) {
        var node = this.sortSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var type = Number(event.target.name);
        if (type !== this.currSelectSort) {
            this.selectSortItem(node, type);
        }
    };
    // path://root/pages_n/1/cure/x/up_time_be_n
    BuildHospitalPnlCtrl.prototype.onClickUpTime = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/SpeedUpCure', this.data);
    };
    // path://root/pages_n/1/info/cond/need/title/layout/view_cured_be
    BuildHospitalPnlCtrl.prototype.onClickViewCured = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/Desc', { text: 'ui.cured_count_desc' });
    };
    // path://root/pages_n/1/cure/content/0/cancel_cure_be
    BuildHospitalPnlCtrl.prototype.onClickCancelCure = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!data) {
            return;
        }
        else if (data.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelCure(data); },
                cancel: function () { },
            });
        }
        this.cancelCure(data);
    };
    // path://root/pages_n/0/info/chance_be
    BuildHospitalPnlCtrl.prototype.onClickChance = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/HospitalChanceDesc');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildHospitalPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('lv').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper.updateBuildAttrInfo(node, data);
        }
    };
    BuildHospitalPnlCtrl.prototype.onUpdatePawnInjuryQueue = function () {
        this.updateInjuryList(false);
    };
    // 刷新治疗列表
    BuildHospitalPnlCtrl.prototype.onUpdatePawnCuringQueue = function () {
        this.updateCureQueue();
        // this.updateArmyList(false)
        // this.updateCureCost()
    };
    // 战斗开始
    BuildHospitalPnlCtrl.prototype.onAreaBattleBegin = function (index) {
        if (this.data.aIndex === index) {
            this.updateCureQueue();
        }
    };
    // 战斗结束
    BuildHospitalPnlCtrl.prototype.onAreaBattleEnd = function (index) {
        if (this.data.aIndex === index) {
            this.updateCureQueue();
        }
    };
    // 切换士兵皮肤
    BuildHospitalPnlCtrl.prototype.onChangePawnSkin = function (data) {
        this.updateInjuryList(false);
        this.updateCureQueue();
    };
    // 切换士兵装备
    BuildHospitalPnlCtrl.prototype.onChangePawnEquip = function () {
        this.updateInjuryList(false);
    };
    // 重新刷新军队列表
    BuildHospitalPnlCtrl.prototype.onUpdateArmy = function () {
        this.updateArmyList(false);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildHospitalPnlCtrl.prototype.getTempCreateArmy = function (uid) {
        var _a;
        if (((_a = this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === uid) {
            return this.tempCreateArmy;
        }
        return null;
    };
    // 选择排序
    BuildHospitalPnlCtrl.prototype.selectSortItem = function (node, type, init) {
        node.Data = this.currSelectSort = type;
        node.Child('val', cc.Label).setLocaleKey('ui.portrayal_list_sort_' + type);
        node.Child('mask/root/sort_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            // m.Child('val').Color(select ? '#E6DCC8' : '#B6A591')
            m.Child('select').active = select;
        });
        if (!init) {
            this.updateInjuryList(false);
        }
    };
    // 放弃治疗
    BuildHospitalPnlCtrl.prototype.giveUpCure = function (uid) {
        var _this = this;
        NetHelper_1.netHelper.reqGiveUpInjuryPawn({ uid: uid }).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                _this.player.updateInjuryPawns(data.injuryPawns);
                _this.updateInjuryList(false);
                var selectUid = _this.user.getTempPreferenceMap(_this.PKEY_SELECT_PAWN);
                _this.updateCureButton(selectUid);
            }
        });
    };
    BuildHospitalPnlCtrl.prototype.addArmyToList = function (data, army, pawns) {
        var _a, _b;
        var item = {
            name: data.name,
            uid: data.uid,
            pawns: pawns,
            army: army
        };
        if (!this.tempArmySortWeightMap[data.uid]) {
            var weight = item.army ? 2 : 1;
            weight = weight * 10 + (9 - (((_a = item.army) === null || _a === void 0 ? void 0 : _a.getActPawnCount()) || 0));
            weight = weight * 10 + (9 - (((_b = item.army) === null || _b === void 0 ? void 0 : _b.pawns.length) || 0));
            this.tempArmySortWeightMap[data.uid] = weight;
        }
        return item;
    };
    // 刷新军队列表
    BuildHospitalPnlCtrl.prototype.updateArmyList = function (isLocation, node) {
        var _this = this;
        var _a, _b, _c;
        node = node || this.pagesNode_.Child(1);
        // 当前区域的军队
        var areaArmyMap = {};
        (_a = this.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) {
            if (m.isCanDrillPawn()) {
                areaArmyMap[m.uid] = m;
            }
        });
        var armys = [null];
        // 先装自己所有的军队 再装临时创建的军队
        this.player.getBaseArmys().forEach(function (m) { return armys.push(_this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)); });
        if (this.tempCreateArmy) {
            armys.push(this.addArmyToList(this.tempCreateArmy, this.tempCreateArmy));
        }
        // 排个序
        armys.sort(function (a, b) { return _this.tempArmySortWeightMap[b === null || b === void 0 ? void 0 : b.uid] - _this.tempArmySortWeightMap[a === null || a === void 0 ? void 0 : a.uid]; });
        var countNode = node.Child('army/title/count_bg');
        countNode.Child('cur', cc.Label).string = armys.length + '';
        countNode.Child('max', cc.Label).string = '/' + this.player.getArmyMaxCount();
        var uid = (_c = (_b = this.selectArmy) === null || _b === void 0 ? void 0 : _b.uid) !== null && _c !== void 0 ? _c : this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY);
        var curArmy = uid ? armys.find(function (m) { return !!(m === null || m === void 0 ? void 0 : m.army) && (m === null || m === void 0 ? void 0 : m.uid) === uid; }) : null, index = -1;
        var sv = node.Child('army/list', cc.ScrollView);
        sv.stopAutoScroll();
        // armys.push(null)
        sv.Items(armys, function (it, data, i) {
            var _a, _b;
            it.Data = data;
            var army = data === null || data === void 0 ? void 0 : data.army;
            var root = it.Child('root');
            root.Child('add').active = !data;
            root.Child('count').active = !!data;
            root.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : '';
            var state = root.Child('state');
            if (army) {
                root.Child('count/val', cc.Label).string = (((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || 0) + '';
                var addLbl = root.Child('count/add', cc.Label), dpc = army.drillPawns.length + army.curingPawns.length;
                if (addLbl.node.active = dpc > 0) {
                    addLbl.string = '+' + dpc;
                }
                var isFull = state.active = army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT;
                root.opacity = isFull ? 150 : 255;
                if (isFull) {
                    state.setLocaleKey('ui.yet_full');
                }
                // 显示选择
                if (!curArmy && isFull) {
                }
                else if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, army.uid);
                }
            }
            else if (data) {
                root.opacity = 150;
                root.Child('count/val', cc.Label).string = (((_b = data.pawns) === null || _b === void 0 ? void 0 : _b.length) || 0) + '';
                root.Child('count/add').active = false;
                state.active = true;
                state.setLocaleKey('ui.go_out');
            }
            else {
                root.opacity = 255;
                state.active = false;
            }
        });
        // 将选中的移动到中间
        if (isLocation) {
            sv.SelectItemToCentre(index);
        }
        // 刷新选中
        this.updateArmySelect(curArmy, node);
        /* // 将选中的移动到中间
        if (index !== -1) {
            const lay = sv.content.Component(cc.Layout)
            lay.updateLayout()
            const width = sv.content.children[0].width
            const tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft //当前位置
            const pw = sv.content.parent.width
            const cx = pw * 0.5 //中间位置
            sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0)
        } else {
            sv.scrollToLeft()
        } */
    };
    BuildHospitalPnlCtrl.prototype.updateArmySelect = function (item, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        this.selectArmy = item;
        var uid = (item === null || item === void 0 ? void 0 : item.uid) || '';
        node.Child('army/list', cc.ScrollView).content.children.forEach(function (m) {
            var _a;
            var select = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select;
        });
        var army = item === null || item === void 0 ? void 0 : item.army, pawns = [];
        if (army) {
            pawns.pushArr(army.pawns);
            pawns.pushArr(army.curingPawns);
            pawns.pushArr(army.drillPawns);
        }
        else if (item === null || item === void 0 ? void 0 : item.pawns) {
            pawns.pushArr(item.pawns);
        }
        // 刷新士兵列表
        node.Child('info/army_pawns').children.forEach(function (it, i) {
            var _a;
            var data = pawns[i], isId = typeof (data) === 'number', isCuring = !!data && !!(army === null || army === void 0 ? void 0 : army.curingPawns.some(function (m) { return m.uid === data.uid; }));
            it.Swih('none', !!data);
            if (data) {
                var icon = it.Child('icon');
                icon.opacity = (isId || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? data : (((_a = data.portrayal) === null || _a === void 0 ? void 0 : _a.id) || data.id), icon, _this.key);
                it.Child('lv', cc.Label).string = isId || data.lv <= 1 ? '' : data.lv + '';
            }
        });
        // 刷新按钮
        var buttons = node.Child('info/cond/need/buttons'), button = buttons.Child('state').Swih('cure_be')[0];
        button.Data = army ? (army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT ? 1 : 0) : 2;
        button.opacity = !!item && button.Data ? 120 : 255;
        if (pawns.length < Constant_1.ARMY_PAWN_MAX_COUNT) {
            var uid_1 = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
            this.updateCureButton(uid_1, buttons.parent);
        }
    };
    // 刷新士兵列表
    BuildHospitalPnlCtrl.prototype.updateInjuryList = function (isLocation, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var pawns = this.player.getInjuryPawns(), pawnCount = pawns.length;
        var mapObj = {};
        this.player.getCuringPawnsQueue().forEach(function (m) { return mapObj[m.uid] = true; });
        pawns.sort(function (a, b) {
            var aState = mapObj[a.uid] ? 1 : 0, bState = mapObj[b.uid] ? 1 : 0;
            if (aState !== bState) {
                return aState - bState;
            }
            switch (_this.currSelectSort) {
                case 0: // 兵种升序 > 等级降序 > 时间降序
                    if (a.id !== b.id)
                        return a.id - b.id;
                    if (a.lv !== b.lv)
                        return b.lv - a.lv;
                    return b.deadTime - a.deadTime;
                case 4: // 时间降序 > 兵种升序 > 等级降序 
                    if (a.deadTime !== b.deadTime)
                        return b.deadTime - a.deadTime;
                    if (a.id !== b.id)
                        return a.id - b.id;
                    return b.lv - a.lv;
                case 5: // 等级降序  > 兵种升序 > 时间降序
                    if (a.lv !== b.lv)
                        return b.lv - a.lv;
                    if (a.id !== b.id)
                        return a.id - b.id;
                    return b.deadTime - a.deadTime;
            }
            return b.lv - a.lv;
        });
        var selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN), nextUid = selectUid;
        if (!pawns.some(function (m) { return m.uid === selectUid; }) || mapObj[selectUid]) { // 士兵治疗完成 或 士兵治疗中，需要自动切换治疗目标
            nextUid = 0;
        }
        var curingQueue = this.player.getCuringPawnsQueue();
        var sv = node.Child('info/pawn/list', cc.ScrollView);
        sv.Child('empty').active = !pawnCount;
        sv.Items(pawns, function (it, data, i) {
            var conf = _this.player.getConfigPawnInfo(data.id), pawn = new PawnObj_1.default().init(data.id, conf.equip, data.lv, conf.skinId);
            it.Data = { data: data, pawn: pawn };
            it.Child('icon').opacity = curingQueue.some(function (m) { return m.uid === data.uid; }) ? 120 : 255;
            it.Child('lv/val', cc.Label).string = data.lv + '';
            var iconNode = it.Child('icon');
            ResHelper_1.resHelper.loadPawnHeadIcon(conf.skinId || data.id, iconNode, _this.key);
            if (i >= _this.preSelectIndex && !nextUid && !mapObj[data.uid]) {
                nextUid = _this.user.setTempPreferenceData(_this.PKEY_SELECT_PAWN, data.uid);
            }
        });
        // 没有找到下一个，就找上一个
        if (!nextUid) {
            for (var i = pawns.length - 1; i >= 0; i--) {
                var pawn = pawns[i];
                if (!nextUid && i <= this.preSelectIndex && !mapObj[pawn.uid]) {
                    nextUid = this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, pawn.uid);
                }
            }
        }
        // 将选中的移动到中间
        if (this.preSelectIndex !== -1) {
            var lay = sv.content.Component(cc.Layout);
            lay.updateLayout();
            var width = sv.content.children[0].width;
            var tx = (width + lay.spacingX) * this.preSelectIndex + width * 0.5 + lay.paddingLeft; //当前位置
            var pw = sv.content.parent.width;
            var cx = pw * 0.5; //中间位置
            sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0);
        }
        else {
            sv.scrollToLeft();
        }
        this.updatePawnSelect(isLocation, node);
        if (pawnCount <= 0) {
            this.updateCureCost();
        }
        // 刷新数量
        this.pagesNode_.Child('1/info/pawn/title/val').setLocaleKey('ui.select_wounded', pawnCount, Constant_1.HOSPITAL_PAWN_LIMIT);
    };
    BuildHospitalPnlCtrl.prototype.updatePawnSelect = function (isLocation, node) {
        node = node || this.pagesNode_.Child(1);
        var selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        var selectIndex = -1;
        var sv = node.Child('info/pawn/list', cc.ScrollView);
        sv.content.children.forEach(function (m, i) {
            var _a, _b;
            /* const uid = m.Data?.data?.uid
            m.Child('bg/select').active = m.Child('select').active = uid === selectUid */
            var select = m.Child('bg/select').active = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.data.uid) === selectUid;
            m.Component(cc.Button).interactable = !select || !!((_b = m.Data) === null || _b === void 0 ? void 0 : _b.pawn);
            if (select) {
                selectIndex = i;
            }
        });
        if (isLocation) {
            sv.SelectItemToCentre(selectIndex);
        }
    };
    // 刷新治疗费用
    BuildHospitalPnlCtrl.prototype.updateCureCost = function (node) {
        var _a, _b;
        node = node || this.pagesNode_.Child(1);
        var uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        var data = (_b = (_a = node.Child('info/pawn/list', cc.ScrollView).Find(function (m) { var _a, _b; return ((_b = (_a = m.Data) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.uid) === uid; })) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.data;
        var need = node.Child('info/cond/need'), empty = node.Child('info/cond/empty');
        empty.active = !data;
        if (need.active = !!data) {
            // 计算当前等级总耗时、总耗资
            // (招募三资 + 训练三资) * (1.0 + 治疗次数 * 0.2)
            // (招募时间 + 训练时间) * (0.5 + 阵亡次数 * 0.5) * (1 - 建筑等级  * 0.02)
            var baseCfg = assetsMgr.getJsonData('pawnBase', data.id);
            var cost = [], cureTime = baseCfg.drill_time;
            cost.pushArr(GameHelper_1.gameHpr.stringToCTypes(baseCfg.drill_cost));
            for (var i = 1; i < data.lv; i++) {
                var cfg = assetsMgr.getJsonData('pawnAttr', data.id * 1000 + i);
                cost.pushArr(GameHelper_1.gameHpr.stringToCTypes(cfg.lv_cost));
                cureTime += cfg.lv_time;
            }
            // 粮耗
            var crealCost = new CTypeObj_1.default().init(Enums_1.CType.CEREAL_C, 0, baseCfg.cereal_cost || 0);
            cost.push(crealCost);
            // // 检测是否有治疗士兵费用增加
            // cost = gameHpr.world.getSeason().changeBaseResCost(CEffect.CURE_COST, cost)
            var finalCost = [];
            GameHelper_1.gameHpr.mergeTypeObjsCount.apply(GameHelper_1.gameHpr, __spread([finalCost], cost));
            // 剔除经验书，后续用来加速
            for (var i = finalCost.length - 1; i >= 0; i--) {
                if (finalCost[i].type === Enums_1.CType.EXP_BOOK) {
                    finalCost.splice(i, 1);
                }
            }
            finalCost.forEach(function (m) { return m.type !== Enums_1.CType.CEREAL_C && (m.count = Math.floor(m.count * (1 + data.cureCount * 0.2))); });
            cureTime = cureTime * (0.5 + data.cureCount * 0.5);
            // 治疗消耗信息
            var cd = this.getCureTimeCD(), policyFreeCount = this.player.getFreeCurePawnSurplusCount();
            ViewHelper_1.viewHelper.updateFreeCostView(need, finalCost, cureTime, cd, false, policyFreeCount);
            need.Child('buttons/state/cure_be/val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_cure' : 'ui.button_cure');
            var curedCount = need.Child('title/layout/view_cured_be'), has = !!data.cureCount;
            curedCount.active = has;
            has && curedCount.Component(TextButtonCmpt_1.default).setKey('ui.button_cured_count', [data.cureCount]);
            // need.Child('cost').Items(finalCost || [], (it, cost) => viewHelper.updateCostViewOne(it, cost, true))
            // if (need.Child('time')?.setActive(!!cureTime)) {
            // 	const up = need.Child('time/up', cc.Label)
            // 	if (up?.setActive(!!cd)) {
            // 		up.string = `(-${Math.floor(cd * 100)}%)`
            // 		cureTime = Math.max(3, Math.floor(cureTime * (1 - cd)))
            // 	}
            // 	need.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(cureTime, 'h:mm:ss')
            // }
            // 刷新按钮
            this.updateCureButton(data.uid, need);
        }
    };
    BuildHospitalPnlCtrl.prototype.updateCureButton = function (uid, node) {
        node = node || this.pagesNode_.Child('1/info/cond/need');
        var info = GameHelper_1.gameHpr.player.getCuringPawnsQueue().find(function (m) { return m.uid === uid; });
        var buttons = node.Child('buttons'), button = buttons.Child('state/cure_be');
        button.opacity = button.Data ? 120 : 255;
        buttons.Child('delete_be').opacity = !!info ? 120 : 255;
        if (info) { // 在治疗队列中
            buttons.Child('state').Swih('curing')[0].Child('val').setLocaleKey(info.surplusTime > 0 ? 'ui.army_state_6' : 'ui.queueing');
        }
        else {
            buttons.Child('state').Swih('cure_be');
        }
    };
    // 获取治疗减CDBuff
    BuildHospitalPnlCtrl.prototype.getCureTimeCD = function () {
        var _a;
        var cd = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_CD);
        if (((_a = this.data.effect) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.CEffect.CURE_CD) {
            cd += this.data.effect.value;
        }
        return cd * 0.01;
    };
    // 刷新治疗列表
    BuildHospitalPnlCtrl.prototype.updateCureQueue = function (node) {
        var _a, _b, _c, _d;
        node = node || this.pagesNode_.Child(1);
        var list = this.player.getCuringPawnsQueue();
        this.upTimeNode_.active = list.length > 0;
        list.sort(function (a, b) { return b.surplusTime - a.surplusTime; });
        var pawnConf = this.player.getConfigPawnMap();
        var time = 0;
        // 是否有政策的加成
        var queueCount = 6 + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_QUEUE);
        node.Child('cure/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')';
        for (var i = 0; i < queueCount; i++) {
            var it = null, data = list[i];
            if (i === 0) {
                it = node.Child('cure/content/' + i);
            }
            else {
                var childrenCount = this.queueSv_.content.childrenCount;
                if (childrenCount <= 1) {
                    this.queueSv_.Items(queueCount - 1, function () { });
                }
                it = this.queueSv_.content.children[i - 1];
            }
            it.Data = data;
            var skinId = data ? (((_a = pawnConf[data.id]) === null || _a === void 0 ? void 0 : _a.skinId) || data.id) : 0;
            var has = it.Child('icon').active = it.Child('cure_pawn_be').active = !!data;
            (_b = it.Child('cancel_cure_be')) === null || _b === void 0 ? void 0 : _b.setActive(has);
            (_c = it.Child('icon/progress')) === null || _c === void 0 ? void 0 : _c.setActive(has);
            it.Child('lv/val', cc.Label).string = data ? data.lv + '' : '';
            ResHelper_1.resHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key);
            if (i !== 0) {
                time += (data === null || data === void 0 ? void 0 : data.needTime) || 0;
            }
            else if (data) {
                var progress = it.Child('icon/progress', cc.Sprite);
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, progress, this.key);
                var stime = data.getSurplusTime();
                time += stime;
                (_d = this.cureProgressTween[i]) === null || _d === void 0 ? void 0 : _d.stop();
                this.cureProgressTween[i] = null;
                progress.fillRange = stime / data.needTime;
                var st = stime * 0.001;
                it.Child('time', cc.LabelTimer).run(st);
                this.cureProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start();
            }
            else {
                it.Child('time', cc.LabelTimer).string = '';
            }
        }
        node.Child('cure/desc').active = time > 0;
        if (time > 0) {
            node.Child('cure/desc/title').setLocaleKey('ui.drill_all_desc', 'ui.button_cure');
            node.Child('cure/desc/time/val', cc.LabelTimer).run(time * 0.001);
        }
    };
    BuildHospitalPnlCtrl.prototype.showCreateArmyUI = function () {
        var _this = this;
        if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        this.showCreateArmyFingerTip(false);
        return ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.tempCreateArmy = new ArmyObj_1.default().init(_this.data.aIndex, GameHelper_1.gameHpr.getUid(), name);
                _this.tempArmySortWeightMap = {};
                if (!_this.selectArmy) {
                    _this.selectArmy = {};
                }
                _this.selectArmy.uid = _this.tempCreateArmy.uid;
                _this.updateArmyList(true, _this.pagesNode_.Child(1));
            }
        });
    };
    // 显示创建军队提示手指
    BuildHospitalPnlCtrl.prototype.showCreateArmyFingerTip = function (val) {
        var node = this.pagesNode_.Child(1);
        var sv = node.Child('army/list', cc.ScrollView), finger = sv.Child('finger');
        if (finger.active = val) {
            var count = sv.content.childrenCount;
            sv.stopAutoScroll();
            if (count >= 4) {
                sv.scrollToRight();
            }
            var it = sv.content.children[count - 1];
            var pos = ut.convertToNodeAR(it, sv.node);
            finger.setPosition(pos.x, pos.y - 12);
        }
    };
    // 取消治疗
    BuildHospitalPnlCtrl.prototype.cancelCure = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        NetHelper_1.netHelper.reqCancelCurePawn({ index: index, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateOutputByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnCuringQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                _this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: 'ui.cancel_cure_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
        });
    };
    BuildHospitalPnlCtrl = __decorate([
        ccclass
    ], BuildHospitalPnlCtrl);
    return BuildHospitalPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildHospitalPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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