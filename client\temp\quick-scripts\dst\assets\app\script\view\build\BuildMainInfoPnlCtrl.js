
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildMainInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4ddc9BbGYpPmrbgT4WCZJuO', 'BuildMainInfoPnlCtrl');
// app/script/view/build/BuildMainInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BuildUnlockTipCmpt_1 = require("../cmpt/BuildUnlockTipCmpt");
var ccclass = cc._decorator.ccclass;
var BuildMainInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildMainInfoPnlCtrl, _super);
    function BuildMainInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.landNode_ = null; // path://root/pages_n/0/info/land_n
        _this.unlockTipNode_ = null; // path://root/pages_n/0/bottom/title/unlock_tip_n
        _this.policySv_ = null; // path://root/pages_n/1/info/policy_sv
        //@end
        _this.PKEY_TAB = 'MAIN_INFO_TAB';
        _this.tab = 0;
        _this.user = null;
        _this.player = null;
        _this.data = null;
        _this.unlockTipCmpt = null;
        return _this;
    }
    BuildMainInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_POLICY_SLOTS] = this.onUpdatePolicySlots, _b.enter = true, _b),
        ];
    };
    BuildMainInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt_1.default);
                return [2 /*return*/];
            });
        });
    };
    BuildMainInfoPnlCtrl.prototype.onEnter = function (data, tab) {
        this.data = data;
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    BuildMainInfoPnlCtrl.prototype.onRemove = function () {
    };
    BuildMainInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildMainInfoPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.tab = Number(event.node.name);
        var node = this.pagesNode_.Swih(type)[0];
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        if (type === 0) {
            this.landNode_.Child('val').setLocaleKey('ui.cur_land_count', GameHelper_1.gameHpr.getPlayerOweCellCount(GameHelper_1.gameHpr.getUid()));
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.POLICY_SLOT_CONF, this.key);
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            this.showPolicyInfo(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildMainInfoPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/policy/slot_nbe
    BuildMainInfoPnlCtrl.prototype.onClickSlot = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data, lv = Number(event.target.name);
        var isUnlock = this.data.lv >= lv;
        if (!isUnlock) {
            return ViewHelper_1.viewHelper.showAlert('ui.lv_unlock_new', { params: [assetsMgr.lang('ui.short_lv', lv), 'ui.ceri_type_name_1'] });
        }
        else if (!!(data === null || data === void 0 ? void 0 : data.isYetStudy())) {
            return ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', data.id, 'book');
        }
        else if (!data || data.selectIds.length === 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NEED_STUDY_PER_SLOT);
        }
        ViewHelper_1.viewHelper.showPnl('build/StudySelect', data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildMainInfoPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.POLICY_SLOT_CONF, this.key, data.lv);
        }
    };
    // 刷新政策
    BuildMainInfoPnlCtrl.prototype.onUpdatePolicySlots = function () {
        if (this.tab === 1) {
            this.showPolicyInfo(this.pagesNode_.Child(1));
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 内政
    BuildMainInfoPnlCtrl.prototype.showPolicyInfo = function (node) {
        var _this = this;
        var slot = node.Child('policy/slot_nbe'), buildLv = this.data.lv;
        var policys = this.player.getPolicySlots();
        slot.children.forEach(function (it) {
            var lv = Number(it.name);
            var data = it.Data = policys[lv];
            var isUnlock = buildLv >= lv, isSelect = !!(data === null || data === void 0 ? void 0 : data.isYetStudy());
            if (!isUnlock) { //还未解锁
                it.Swih('lock');
                // state.Color('#C34A32').setLocaleKey('ui.need_lv_unlock', lv)
            }
            else if (!isSelect) { //还未选择
                var canSelect = !!(data === null || data === void 0 ? void 0 : data.selectIds.length);
                it.Swih('add')[0].Child('dot').active = canSelect;
                // state.Color(canSelect ? '#49983C' : '#756963').setLocaleKey(canSelect ? 'ui.can_study' : 'ui.button_wait_study')
            }
            // 选择信息
            if (isSelect) {
                ResHelper_1.resHelper.loadPolicyIcon(data.id, it.Swih('icon')[0], _this.key);
            }
            // it.Component(cc.Button).interactable = isUnlock && !isSelect
        });
        this.updatePolicyEffectInfo();
    };
    // 内政效果
    BuildMainInfoPnlCtrl.prototype.updatePolicyEffectInfo = function () {
        var _this = this;
        var initItemHeight = 92;
        var datas = GameHelper_1.gameHpr.getPlayerPolicysBaseInfo();
        this.policySv_.stopAutoScroll();
        this.policySv_.Items(datas, function (it, data, i) {
            it.Child('name/val').setLocaleKey('policyText.name_' + data.id);
            var value = data.values[Math.min(data.up - 1, data.values.length - 1)];
            var params = "<color=#4AB32E>" + value + "</c>";
            var descRt = it.Child('desc', cc.RichText);
            descRt.setLocaleKey('policyText.desc_' + data.id, params);
            it.Child('policys').Items(data.styles, function (item, style) {
                ResHelper_1.resHelper.loadPolicyIcon(data.id, item.Child('icon'), _this.key);
                var styleSpr = item.Child('style', cc.Sprite);
                if (style >= 10) { // 季节政策
                    ResHelper_1.resHelper.loadIcon('icon/season_' + (style % 10), styleSpr, _this.key);
                }
                else if (style === 2) { // 联盟政策
                    ResHelper_1.resHelper.loadAlliIcon(GameHelper_1.gameHpr.alliance.getIcon(), styleSpr, _this.key);
                }
                else {
                    styleSpr.Component(cc.Sprite).spriteFrame = null;
                }
            });
            var h = Math.max(initItemHeight, (initItemHeight - descRt.lineHeight) + descRt.node.height);
            if (it.height !== h) {
                it.height = h;
                it.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
            }
        });
    };
    BuildMainInfoPnlCtrl = __decorate([
        ccclass
    ], BuildMainInfoPnlCtrl);
    return BuildMainInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildMainInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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