
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ResDetailsPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0d9cfExgSVA1ot0V+P7Ia/i', 'ResDetailsPnlCtrl');
// app/script/view/common/ResDetailsPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ResDetailsPnlCtrl = /** @class */ (function (_super) {
    __extends(ResDetailsPnlCtrl, _super);
    function ResDetailsPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.landsSv_ = null; // path://root/pages/lands_sv
        _this.sumNode_ = null; // path://root/pages/sum_n
        _this.buyAddOpNode_ = null; // path://root/pages/sum_n/5/buy_add_op_be_n
        _this.curLbl_ = null; // path://root/pages/cur_l
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.type = 0;
        _this.resMap = {};
        return _this;
    }
    ResDetailsPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ADD_OUTPUT_TIME] = this.onUpdateAddOutputTime, _a.enter = true, _a),
        ];
    };
    ResDetailsPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.buyAddOpNode_.Child('val', cc.Label).string = Constant_1.ADD_OUTPUT_GOLD + '';
                return [2 /*return*/];
            });
        });
    };
    ResDetailsPnlCtrl.prototype.onEnter = function (type) {
        this.initResDist();
        this.tabsTc_.Tabs(type);
    };
    ResDetailsPnlCtrl.prototype.onRemove = function () {
    };
    ResDetailsPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    ResDetailsPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        this.showResDetails(Number(event.node.name));
    };
    // path://root/pages/sum_n/5/buy_add_op_be_n
    ResDetailsPnlCtrl.prototype.onClickBuyAddOp = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.world.isGameOver()) {
            return ViewHelper_1.viewHelper.showAlert('toast.gold_increase_output');
        }
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return;
        }
        var type = this.type;
        var hasAdd = !!GameHelper_1.gameHpr.player.getAddOutputSurplusTime()[type];
        ViewHelper_1.viewHelper.showMessageBox(hasAdd ? 'ui.add_output_desc_1' : 'ui.add_output_desc_0', {
            params: [Constant_1.ADD_OUTPUT_GOLD, Constant_1.ADD_OUTPUT_RATIO, Constant_1.CTYPE_NAME[type]],
            ok: function () { return GameHelper_1.gameHpr.player.buyAddOutputTime(type).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.showResDetails(type);
                }
            }); },
            cancel: function () { },
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    ResDetailsPnlCtrl.prototype.onUpdateAddOutputTime = function () {
        this.showResDetails(this.type);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ResDetailsPnlCtrl.prototype.initResDist = function () {
        var _a;
        var _this = this;
        var _b, _c;
        this.resMap = (_a = {}, _a[Enums_1.CType.CEREAL] = { arr: [], sum: 0 }, _a[Enums_1.CType.TIMBER] = { arr: [], sum: 0 }, _a[Enums_1.CType.STONE] = { arr: [], sum: 0 }, _a);
        (_c = (_b = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _b === void 0 ? void 0 : _b.cells) === null || _c === void 0 ? void 0 : _c.forEach(function (cell) {
            if (cell.isHasRes()) {
                var json_1 = cell.getResJson() || {};
                Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; }).forEach(function (m) {
                    var info = _this.resMap[Constant_1.RES_FIELDS_CTYPE[m]], val = json_1[m];
                    var it = info.arr.find(function (x) { return (x.cell.landId === cell.landId || x.cell.cityId === cell.cityId) && x.val === val; });
                    if (it) {
                        it.count += 1;
                    }
                    else {
                        info.arr.push({ cell: cell, val: val, count: 1 });
                    }
                    info.sum += val;
                });
            }
        });
        for (var key in this.resMap) {
            this.resMap[key].arr.sort(function (a, b) { return b.val - a.val; });
        }
    };
    ResDetailsPnlCtrl.prototype.showResDetails = function (type) {
        this.type = type;
        var player = GameHelper_1.gameHpr.player;
        var info = this.resMap[type], len = info.arr.length;
        this.landsSv_.Child('empty').active = len === 0;
        this.landsSv_.Items(info.arr, function (it, data, i) {
            it.Child('name').setLocaleKey(data.cell.getName());
            it.Child('val', cc.Label).string = data.val + '';
            it.Child('count', cc.Label).string = data.count + '';
            it.Child('line').active = i !== 4;
        });
        // 初始资源
        var output = player.isCapture() ? 0 : Constant_1.INIT_RES_OUTPUT, addNum = 0;
        this.sumNode_.Child('3/val', cc.Label).string = output + '';
        // 土地资源
        output += info.sum;
        this.sumNode_.Child('1/val', cc.Label).string = info.sum + '';
        // 内政加成 固定
        var policyAddRes = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.RES_OUTPUT);
        if (this.sumNode_.Child('2').active = policyAddRes > 0) {
            output += policyAddRes;
            this.sumNode_.Child('2/val', cc.Label).string = policyAddRes + '';
        }
        // 商城购买 百分比
        var time = player.getAddOutputSurplusTime()[type] || 0;
        addNum = time > 0 ? Math.floor(output * Constant_1.ADD_OUTPUT_RATIO * 0.01) : 0;
        output += addNum;
        this.sumNode_.Child('5/desc/val').setLocaleKey('ui.add_output_desc', '+' + Constant_1.ADD_OUTPUT_RATIO);
        this.sumNode_.Child('5/val', cc.Label).string = addNum + '';
        // 购买加成
        if (this.sumNode_.Child('5').active = !GameHelper_1.gameHpr.isNoviceMode) {
            this.updateAddOutputStateTime(this.sumNode_.Child('5/desc/state'), time);
        }
        // 粮耗
        var cost = type === Enums_1.CType.CEREAL ? player.getCerealConsume() : 0;
        if (this.sumNode_.Child('7').active = cost > 0) {
            output -= cost;
            this.sumNode_.Child('7/val', cc.Label).string = '-' + cost;
        }
        // 总
        this.sumNode_.Child('6/val', cc.Label).string = output + '';
        var cap = type === Enums_1.CType.CEREAL ? player.getGranaryCap() : player.getWarehouseCap();
        this.curLbl_.setLocaleKey('ui.cur_res_cap', GameHelper_1.gameHpr.getCountByCType(type) + '/' + cap);
    };
    ResDetailsPnlCtrl.prototype.updateAddOutputStateTime = function (node, time) {
        var hasAdd = time > 0, color = hasAdd ? '#59A733' : '#D7634D';
        this.buyAddOpNode_.Child('desc').setLocaleKey(hasAdd ? 'ui.button_lengthen' : 'ui.button_enable');
        node.children.forEach(function (m) { return m.Color(color); });
        node.Child('val').setLocaleKey(hasAdd ? 'ui.takeeffecting' : 'ui.not_takeeffect');
        node.Child('s').active = hasAdd;
        var lbl = node.Child('time', cc.LabelTimer);
        if (lbl.setActive(hasAdd)) {
            lbl.run(time * 0.001);
        }
    };
    ResDetailsPnlCtrl = __decorate([
        ccclass
    ], ResDetailsPnlCtrl);
    return ResDetailsPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ResDetailsPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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