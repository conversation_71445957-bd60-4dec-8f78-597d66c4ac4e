
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/ArmyListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9c98fj2wCNEv4PGkOm+oFHe', 'ArmyListPnlCtrl');
// app/script/view/main/ArmyListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var ArmyListPnlCtrl = /** @class */ (function (_super) {
    __extends(ArmyListPnlCtrl, _super);
    function ArmyListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.player = null;
        return _this;
    }
    ArmyListPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ARMY_TREASURE] = this.onUpdateArmyTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY_AREA_INDEX] = this.onUpdateArmyAreaIndex, _b.enter = true, _b),
        ];
    };
    ArmyListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    ArmyListPnlCtrl.prototype.onEnter = function () {
        this.tabsTc_.Tabs(0);
    };
    ArmyListPnlCtrl.prototype.onRemove = function () {
    };
    ArmyListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/pages_n/0/list/view/content/item/pos_be
    ArmyListPnlCtrl.prototype.onClickPos = function (event, _) {
        var _a, _b;
        var data = event.target.parent.Data;
        if (data) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos((_b = (_a = data.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data.index);
        }
    };
    // path://root/tabs_tc_tce
    ArmyListPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = event.node.name;
        var node = this.pagesNode_.Swih(type)[0];
        if (type === '0') {
            this.showAllArmy(node);
        }
        else if (type === '1') {
            this.showArmyMarchRecord(node);
        }
        else if (type === '2') {
            this.showArmyBattleRecord(node);
        }
    };
    // path://root/pages_n/2/view/content/item/5/playback_be
    ArmyListPnlCtrl.prototype.onClickPlayback = function (event, _) {
        var data = event.target.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid;
        if (uid) {
            this.playbackBattle(uid);
        }
    };
    // path://root/pages_n/0/list/view/content/item/treasure_be
    ArmyListPnlCtrl.prototype.onClickTreasure = function (event, _) {
        var _a;
        var data = event.target.parent.Data;
        if (!data) {
            // } else if (gameHpr.isBattleingByIndex(data.index)) {
            //     return viewHelper.showAlert(ecode.BATTLEING)
        }
        else if (((_a = data.treasures) === null || _a === void 0 ? void 0 : _a.length) > 0) {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', data.treasures);
        }
    };
    // path://root/pages_n/2/view/content/item/5/buttons/send_to_chat_be
    ArmyListPnlCtrl.prototype.onClickSendToChat = function (event, _data) {
        var _this = this;
        var data = event.target.parent.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid, index = data.index;
        if (uid) {
            // mapHelper.indexToPoint(data.armyIndex).Join()
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_battle_record_to_chat_tip' }, function (type, childType, select) {
                if (GameHelper_1.gameHpr.chat.sendChat(type, childType, '', { select: select, battleInfo: { uid: uid, index: index } }) === 0) {
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type }).then(function () { return _this.isValid && _this.hide(); });
                }
            });
        }
    };
    // path://root/pages_n/2/view/content/item/5/buttons/battle_statistics_be
    ArmyListPnlCtrl.prototype.onClickBattleStatistics = function (event, _) {
        var data = event.target.parent.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid, uids = (data === null || data === void 0 ? void 0 : data.armyUidList) || [];
        if (uid && uids.length > 0) {
            this.showBattleStatistics(uid, uids);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新士兵宝箱
    ArmyListPnlCtrl.prototype.onUpdateArmyTreasure = function (auid) {
        var _a;
        var it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === auid; });
        if (it === null || it === void 0 ? void 0 : it.Data) {
            var army = this.player.getTempArmyList().find(function (m) { return m.uid === auid; });
            var treasures = it.Data.treasures = (_a = army === null || army === void 0 ? void 0 : army.treasures) !== null && _a !== void 0 ? _a : it.Data.treasures;
            this.updateArmyTreasure(it, treasures || []);
        }
    };
    // 刷新军队所在区域位置
    ArmyListPnlCtrl.prototype.onUpdateArmyAreaIndex = function (auid) {
        var _a, _b;
        var it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === auid; });
        if (it === null || it === void 0 ? void 0 : it.Data) {
            var data_1 = it.Data;
            data_1.march = GameHelper_1.gameHpr.world.getMarchs().find(function (m) { return m.armyUid === data_1.uid; });
            data_1.dis = GameHelper_1.gameHpr.getToMapCellDis((_b = (_a = data_1.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data_1.index, this.player.getMainCityIndex());
            this.updateArmyPos(it, data_1);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 显示所有军队
    ArmyListPnlCtrl.prototype.showAllArmy = function (node) {
        var _this = this;
        var sv = node.Child('list', cc.ScrollView);
        sv.content.Swih('');
        var emptyNode = sv.Child('empty'), countLbl = node.Child('title/bg/val', cc.Label);
        var armyMaxCount = this.player.getArmyMaxCount();
        emptyNode.active = false;
        countLbl.setLocaleKey('ui.own_army_count', '0/' + armyMaxCount);
        this.loadingNode_.active = true;
        this.player.getAllArmys(3, false).then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.loadingNode_.active = false;
            var index = _this.player.getMainCityIndex();
            var marchs = {};
            GameHelper_1.gameHpr.world.getMarchs().forEach(function (x) { return marchs[x.armyUid] = x; });
            var lvingPawnLvMap = {};
            _this.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m.lv; });
            list.forEach(function (m) {
                var _a, _b;
                m.march = marchs[m.uid];
                m.tonden = GameHelper_1.gameHpr.world.getArmyTondenInfo(m.index, m.uid);
                m.dis = GameHelper_1.gameHpr.getToMapCellDis((_b = (_a = m.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : m.index, index);
            });
            list.sort(function (a, b) {
                var aw = _this.getArmySortState(a, lvingPawnLvMap), bw = _this.getArmySortState(b, lvingPawnLvMap);
                return aw === bw ? a.dis - b.dis : bw - aw;
            });
            var len = list.length;
            emptyNode.active = len === 0;
            countLbl.setLocaleKey('ui.own_army_count', len + '/' + _this.player.getArmyMaxCount());
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(len, function (it, i) {
                var data = it.Data = list[i];
                it.Child('name', cc.Label).string = data.name;
                _this.updateArmyPos(it, data);
                var pawns = data.pawns, isHasLving = false;
                it.Child('pawns').Items(pawns.concat(data.drillPawns).concat(data.curingPawns), function (node2, pawn) {
                    var _a;
                    var icon = node2.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                    var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                    var lv = isLving ? lvingPawnLvMap[pawn.uid] : pawn.lv;
                    ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.id) || pawn.id), icon, _this.key);
                    icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                    node2.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                    if (node2.Child('hp').active = (!isId && !isCuring)) {
                        node2.Child('hp/bar', cc.Sprite).fillRange = pawn.hp[0] / pawn.hp[1];
                    }
                    if (isLving) {
                        isHasLving = true;
                    }
                });
                ViewHelper_1.viewHelper.updateArmyState(it, data, data.march, isHasLving, true);
                _this.updateArmyTreasure(it, data.treasures);
            });
        });
    };
    ArmyListPnlCtrl.prototype.getArmySortState = function (army, lvingPawnLvMap) {
        if (army.state !== Enums_1.ArmyState.NONE) {
            return army.state;
        }
        else if (army.drillPawns.length > 0) {
            return Enums_1.ArmyState.DRILL;
        }
        else if (army.curingPawns.length > 0) {
            return Enums_1.ArmyState.CURING;
        }
        else if (army.pawns.some(function (m) { return !!lvingPawnLvMap[m.uid]; })) {
            return Enums_1.ArmyState.LVING;
        }
        else if (army.tonden) {
            return Enums_1.ArmyState.TONDEN;
        }
        return army.state;
    };
    ArmyListPnlCtrl.prototype.updateArmyPos = function (node, data) {
        var _a, _b;
        var isMarching = !!data.march, isHasDis = data.dis > 0;
        var posNode = node.Child('pos_be'), disNode = node.Child('dis');
        if (posNode.active = isHasDis || isMarching) {
            ViewHelper_1.viewHelper.updatePositionView(posNode, (_b = (_a = data.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data.index);
        }
        if (disNode.active = !isHasDis && !posNode.active) {
            disNode.setLocaleKey('ui.in_main_city');
        }
    };
    // 刷新宝箱信息
    ArmyListPnlCtrl.prototype.updateArmyTreasure = function (it, treasures) {
        var node = it.Child('treasure_be'), treasureCount = treasures.length;
        if (node.active = treasureCount > 0) {
            node.Child('treasure', TextButtonCmpt_1.default).setKey('ui.get_treasure_count', treasureCount);
        }
    };
    // 显示军队行军记录列表
    ArmyListPnlCtrl.prototype.showArmyMarchRecord = function (node) {
        var _this = this;
        var sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty');
        sv.content.Swih('');
        this.loadingNode_.active = true;
        emptyNode.active = false;
        this.player.getArmyMarchRecords().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            var len = list.length;
            _this.loadingNode_.active = false;
            if (emptyNode.active = len === 0) {
                emptyNode.setLocaleKey(GameHelper_1.gameHpr.isNoviceMode ? 'ui.army_march_record_empty_1' : 'ui.army_march_record_empty');
            }
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.Items(list, function (it, data) {
                it.Data = data;
                it.Child('0/name', cc.Label).string = data.armyName;
                it.Child('0/time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
                _this.updateRecordInfo(it, data);
            });
        });
    };
    // 显示军队战斗记录列表
    ArmyListPnlCtrl.prototype.showArmyBattleRecord = function (node) {
        var _this = this;
        var sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty');
        sv.content.Swih('');
        this.loadingNode_.active = true;
        emptyNode.active = false;
        this.player.getArmyBattleRecords().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            var len = list.length;
            _this.loadingNode_.active = false;
            if (emptyNode.active = len === 0) {
                emptyNode.setLocaleKey('ui.army_battle_record_empty');
            }
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(list.length, function (it, i) {
                var data = list[i];
                it.Child('win/bg').Color(data.isWin ? '#EB9E4E' : '#96B2C8');
                it.Child('win/bg/val').setLocaleKey('ui.battle_result_' + Number(!!data.isWin));
                it.Child('win/time', cc.Label).string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.beginTime);
                _this.updateRecordInfo(it.Child('0'), { type: 0, armyIndex: data.index });
                it.Child('1/val').setLocaleKey('ui.end_battle_time', ut.millisecondFormat(data.endTime - data.beginTime, 'h:mm:ss'));
                it.Child('2/val').setLocaleKey('ui.battle_army_count', data.armyUidList.length);
                it.Child('3/val').setLocaleKey('ui.alli_battle_record_0_1', (data.invalidInfo[1] || 0) + (data.validInfo[1] || 0));
                it.Child('4/val').setLocaleKey('ui.alli_battle_record_1_1', data.deadInfo.length);
                it.Child('5').Data = data;
                it.Child('5/buttons/battle_statistics_be').active = !!data.armyUidList.length;
                it.Child('5/buttons/send_to_chat_be').active = !GameHelper_1.gameHpr.isNoviceMode;
                it.Child('5/playback_be', cc.Button).interactable = !!data.isCanPlay;
            });
        });
    };
    ArmyListPnlCtrl.prototype.updateRecordInfo = function (it, data) {
        var descNode = it.Child('desc'), texts = Constant_1.ARMY_RECORD_DESC_CONF[data.type];
        if (descNode.active = !!texts) {
            descNode.setLocaleKey('ui.army_record_desc_' + data.type, texts.map(function (m) {
                if (m === 'index') {
                    return " <color=#564C49>" + assetsMgr.lang('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(data.armyIndex), MapHelper_1.mapHelper.indexToPoint(data.armyIndex).Join()) + "</>";
                }
                else if (m === 'target') {
                    return " <color=#564C49>" + assetsMgr.lang('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(data.targetIndex), MapHelper_1.mapHelper.indexToPoint(data.targetIndex).Join()) + "</>";
                }
                return '';
            }));
        }
    };
    // 显示战斗统计
    ArmyListPnlCtrl.prototype.showBattleStatistics = function (battleUid, uids) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqGetArmyRecordsByUids({ battleUid: battleUid, uids: uids })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            ViewHelper_1.viewHelper.showPnl('main/BattleStatistics', data.list);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 回放战斗
    ArmyListPnlCtrl.prototype.playbackBattle = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showWindLoading(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordById(uid)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            ViewHelper_1.viewHelper.showWindLoading(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                    case 2:
                        _a.sent();
                        ViewHelper_1.viewHelper.showWindLoading(false);
                        ViewHelper_1.viewHelper.gotoWind('playback');
                        return [2 /*return*/];
                }
            });
        });
    };
    ArmyListPnlCtrl = __decorate([
        ccclass
    ], ArmyListPnlCtrl);
    return ArmyListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ArmyListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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