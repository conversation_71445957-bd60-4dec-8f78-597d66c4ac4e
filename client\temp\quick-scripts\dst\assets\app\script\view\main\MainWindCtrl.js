
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/MainWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1e364aKCJxAnqBfRB6XE0Ca', 'MainWindCtrl');
// app/script/view/main/MainWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var PopupPnlHelper_1 = require("../../common/helper/PopupPnlHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BuildObj_1 = require("../../model/area/BuildObj");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var SelectCellCmpt_1 = require("../cmpt/SelectCellCmpt");
var CellInfoCmpt_1 = require("./CellInfoCmpt");
var MapAnimNodePool_1 = require("./MapAnimNodePool");
var MarchCmpt_1 = require("./MarchCmpt");
var SceneEffectCmpt_1 = require("./SceneEffectCmpt");
var ccclass = cc._decorator.ccclass;
var MainWindCtrl = /** @class */ (function (_super) {
    __extends(MainWindCtrl, _super);
    function MainWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.cellEffectNode_ = null; // path://root/map_n/cell_effect_n
        _this.selectCellNode_ = null; // path://root/select_cell_n
        _this.textNode_ = null; // path://root/text_n
        _this.ancientTextNode_ = null; // path://root/ancient_text_n
        _this.marchLineNode_ = null; // path://root/march/march_line_n
        _this.marchRoleNode_ = null; // path://root/march/march_role_n
        _this.cellEmojiNode_ = null; // path://root/cell_emoji_n
        _this.sceneEffectNode_ = null; // path://root/scene_effect_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        _this.weakGuideNode_ = null; // path://root/weak_guide_n
        //@end
        _this.INIT_KEY = '_init_main_';
        _this.diNode = null; //装饰
        _this.mountainNode = null; //山脉
        _this.protectLineNode = null; //保护线
        _this.lineNode = null;
        _this.seawaveNode = null; //海浪
        _this.landNode = null;
        _this.cityNode = null; //城市层
        _this.maskNode = null; //遮罩层
        _this.btinfoNode = null; //修建信息层
        _this.outputNode = null; //产出层
        _this.iconNode = null; //小图标层
        _this.tondenNode = null; //屯田中图标层
        _this.battleNode = null; //战斗中图标层
        _this.mapFlagNode = null; //地图标记
        _this.cellInfoCmpt = null;
        _this.touchCmpt = null;
        _this.sceneEffect = null;
        _this.cellEmojiItemMap = {};
        _this.seasonType = 0;
        _this.model = null;
        _this.user = null;
        _this.player = null;
        _this.centre = cc.v2(); //当前的中心位置
        _this.preCameraZoomRatio = 0;
        _this.preCameraPosition = cc.v2();
        _this.marchs = []; //当前所有行军
        _this.tempShowCellMap = {}; //当前屏幕显示的地块信息
        _this.reqSelectArmysing = false; //当前是否请求军队列表中
        _this.cellEmojiMap = {}; //当前的领地表情map
        _this.cityAnimNodePool = null; //城市节点管理
        _this.seawaveAnimNodePool = null; //海浪节点管理
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_vec2_4 = cc.v2();
        _this._temp_vec2_5 = cc.v2();
        return _this;
    }
    MainWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_CELL_INFO] = this.onUpdateCellInfo, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.ADD_MARCH] = this.onAddMarch, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.REMOVE_MARCH] = this.onRemoveMarch, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_ALL_MARCH] = this.onUpdateAllMarch, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.HIDE_WORLD_TEXT] = this.onHideWorldText, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.CLOSE_SELECT_CELL] = this.onCloseSelectCell, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_BATTLE_DIST_INFO] = this.onUpdateBattleDistInfo, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO] = this.onUpdateAvoidWarDistInfo, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_BT_CITY] = this.onUpdateBtCity, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_TONDEN] = this.onUpdateTonden, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmyDistInfo, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.MAP_MOVE_TO] = this.onMapMoveTo, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_PLAYER_NICKNAME] = this.onUpdatePlayerNickname, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.UPDATE_PLAYER_HEAD_ICON] = this.onUpdatePlayerHeadIcon, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.UPDATE_ALLI_MAP_FLAG] = this.onUpdateAlliMapFlag, _r.enter = true, _r),
            (_s = {}, _s[EventType_1.default.UPDATE_MARCH_OPACITY] = this.onUpdateMarchOpacity, _s.enter = true, _s),
            (_t = {}, _t[EventType_1.default.PLAY_NEW_CELL_EFFECT] = this.onPlayNewCellEffect, _t.enter = true, _t),
            (_u = {}, _u[EventType_1.default.PLAY_CELL_TONDEN_EFFECT] = this.onPlayCellTondenEffect, _u.enter = true, _u),
            (_v = {}, _v[EventType_1.default.PLAY_CELL_EMOJI] = this.onPlayCellEmoji, _v.enter = true, _v),
            (_w = {}, _w[EventType_1.default.UPDATE_CITY_OUTPUT] = this.onUpdateCityOutput, _w.enter = true, _w),
            (_x = {}, _x[EventType_1.default.CHANGE_SEASON_COMPLETE] = this.onChangeSeasonComplete, _x.enter = true, _x),
            (_y = {}, _y[EventType_1.default.UPDATE_ANCIENT_INFO] = this.onUpdateAncientInfo, _y.enter = true, _y),
            (_z = {}, _z[EventType_1.default.UPDATE_CITY_SKIN] = this.onUpdateCitySkin, _z.enter = true, _z),
            (_0 = {}, _0[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _0.enter = true, _0),
        ];
    };
    MainWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var i, item;
            return __generator(this, function (_a) {
                this.setParam({ isClean: false });
                this.diNode = this.mapNode_.FindChild('di');
                this.mountainNode = this.mapNode_.FindChild('mountain');
                this.protectLineNode = this.mapNode_.FindChild('protect_line');
                this.lineNode = this.mapNode_.FindChild('line');
                this.seawaveNode = this.mapNode_.FindChild('seawave');
                this.landNode = this.mapNode_.FindChild('land');
                this.cityNode = this.mapNode_.FindChild('city');
                this.maskNode = this.mapNode_.FindChild('mask');
                this.btinfoNode = this.mapNode_.FindChild('btinfo');
                this.outputNode = this.mapNode_.FindChild('output');
                this.iconNode = this.mapNode_.FindChild('icon');
                this.tondenNode = this.mapNode_.FindChild('tonden');
                this.battleNode = this.mapNode_.FindChild('battle');
                this.mapFlagNode = this.mapNode_.FindChild('map_flag');
                for (i = 2; i <= 3; i++) {
                    item = this.cellEmojiItemMap[i] = this.cellEmojiNode_.FindChild('item_' + i);
                    item.parent = null;
                }
                this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                this.cellInfoCmpt = this.FindChild('root/cell_info', CellInfoCmpt_1.default);
                this.cityAnimNodePool = new MapAnimNodePool_1.default().init(this.cityNode, ResHelper_1.resHelper.getCityPrefab.bind(ResHelper_1.resHelper));
                this.model = this.getModel('world');
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                // this.seawaveAnimNodePool = new MapAnimNodePool().init(this.seawaveNode, resHelper.getSeawavePrefab.bind(resHelper))
                // this.seawaveAnimNodePool.setAnimInfo('land_104', 1.76)
                this.selectCellNode_.active = false;
                this.cellInfoCmpt.close();
                this.sceneEffect = this.sceneEffectNode_.getComponent(SceneEffectCmpt_1.default);
                this.updateSeasonSeceneEffect();
                this.maskNode.children[0].color = cc.Color.WHITE.fromHEX(Constant_1.MAP_MASK_ITEM_COLOR[this.model.getSeasonType()]);
                return [2 /*return*/];
            });
        });
    };
    MainWindCtrl.prototype.onReady = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MainWindCtrl.prototype.onEnter = function (data) {
        this.model.initCameraInfo();
        this.cellEffectNode_.Data = true;
        this.cellEmojiNode_.Data = true;
        this.topLayerNode_.Data = true;
        this.checkSeason();
        this.initMarch(); //初始化行军
        this.playNewCellEffect();
        this.playCellTondenEffect();
        this.playCellEmoji();
        this.touchCmpt.init(this.onClickMap.bind(this));
        GameHelper_1.gameHpr.playMainBgm();
        CameraCtrl_1.cameraCtrl.setBgColor(Constant_1.CAMERA_BG_COLOR[this.model.getSeasonType()]);
    };
    MainWindCtrl.prototype.onLeave = function () {
        this.model.saveCameraInfo();
        this.touchCmpt.clean();
        this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
        this.cellInfoCmpt.close();
        this.reqSelectArmysing = false;
        this.cleanMarch();
        this.cellEffectNode_.removeAllChildren();
        this.cellEffectNode_.Data = false;
        this.cellEmojiNode_.removeAllChildren();
        this.cellEmojiNode_.Data = false;
        this.topLayerNode_.removeAllChildren();
        this.topLayerNode_.Data = false;
        // resHelper.cleanNodeChildren(this.diNode) 这里暂时不清理 因为进入其他场景太慢了
        this.cellEmojiMap = {};
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        assetsMgr.releaseTempResByTag(this.key);
        CameraCtrl_1.cameraCtrl.setBgColor('#D1F1F3');
    };
    MainWindCtrl.prototype.onClean = function () {
        var _a;
        for (var k in this.cellEmojiItemMap) {
            this.cellEmojiItemMap[k].destroy();
        }
        this.cellEmojiItemMap = {};
        this.cellEmojiMap = {};
        this.cellInfoCmpt.clean();
        (_a = this.sceneEffect) === null || _a === void 0 ? void 0 : _a.clean();
        assetsMgr.releaseTempResByTag(this.INIT_KEY);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/cell_info/buttons/enter_be
    MainWindCtrl.prototype.onClickEnter = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            this.model.setLookCell(cell);
            ViewHelper_1.viewHelper.gotoWind('area');
            this.hideSelectCell(false);
        }
    };
    // path://root/cell_info/buttons/occupy_be
    MainWindCtrl.prototype.onClickOccupy = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell) {
            return;
        }
        else if (!this.model.checkCanOccupyCell(cell)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ONLY_ATTACK_ADJOIN_CELL);
        }
        else if (cell.checkAttackByProtect()) { //是否有保护
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.CELL_PROTECT);
        }
        else if (cell.isAvoidWar()) { //是否金盾
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.AVOID_WAR_NOT_ATTACK);
        }
        this.occupyCell(cell.actIndex);
    };
    // path://root/cell_info/buttons/tonden_be
    MainWindCtrl.prototype.onClickTonden = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        else if (GameHelper_1.gameHpr.isBattleingByIndex(cell.index)) {
            return ViewHelper_1.viewHelper.showAlert('toast.battleing_not_tonden');
        }
        else if (cell.isBTCitying()) {
            return ViewHelper_1.viewHelper.showAlert('toast.bting_not_tonden');
        }
        this.cellTonden(cell.actIndex);
    };
    // path://root/cell_info/buttons/cancel_tonden_be
    MainWindCtrl.prototype.onClickCancelTonden = function (event, data) {
        var _this = this;
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn() || !cell.isTondening()) {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.cancel_tonden_tip', {
            ok: function () { return _this.isActive && _this.cancelTonden(cell.actIndex); },
            cancel: function () { },
        });
    };
    // path://root/cell_info/buttons/move_be
    MainWindCtrl.prototype.onClickMove = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOneAlliance()) {
            return;
        }
        this.moveToCell(cell.actIndex);
    };
    // path://root/cell_info/buttons/build_be
    MainWindCtrl.prototype.onClickBuild = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        else if (cell.isTondening()) {
            return ViewHelper_1.viewHelper.showAlert('toast.tondening_not_bt');
        }
        ViewHelper_1.viewHelper.showPnl('main/CityList', cell);
    };
    // path://root/cell_info/buttons/dismantle_be
    MainWindCtrl.prototype.onClickDismantle = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('main/DismantleCityTip', cell);
    };
    // path://root/cell_info/buttons/player_info_be
    MainWindCtrl.prototype.onClickPlayerInfo = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        var info = this.model.getPlayerInfo(cell === null || cell === void 0 ? void 0 : cell.owner);
        if (info) {
            ViewHelper_1.viewHelper.showPnl('common/PlayerInfo', info, 'cellinfo');
        }
    };
    // path://root/cell_info/title/share_pos_be
    MainWindCtrl.prototype.onClickSharePos = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            var point_1 = cell.actPoint.Join(',');
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_point_to_chat_tip', params: [point_1] }, function (type, select) {
                if (_this.isValid) {
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type, text: "[" + point_1 + "]" }).then(function () { return _this.isValid && _this.hideSelectCell(false); });
                }
            });
        }
    };
    // path://root/cell_info/buttons/flag_be
    MainWindCtrl.prototype.onClickFlag = function (event, data) {
        if (!GameHelper_1.gameHpr.alliance.isMeMilitary()) {
            return;
        }
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/SelectFlagIcon', cell.actIndex);
        }
    };
    // path://root/cell_info/info/score/score_desc_be
    MainWindCtrl.prototype.onClickScoreDesc = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/LandScoreDesc', cell.landLv);
        }
    };
    // path://root/cell_info/info/stamina/stamina_desc_be
    MainWindCtrl.prototype.onClickStaminaDesc = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType);
        }
    };
    // path://root/cell_info/title/cell_emoji_be
    MainWindCtrl.prototype.onClickCellEmoji = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/CellSelectEmoji', cell.isOwn(), function (id) {
                if (id) {
                    GameHelper_1.gameHpr.ground.sendCellEmoji(id, cell.actIndex);
                }
                if (_this.isValid) {
                    _this.hideSelectCell(false);
                }
            });
        }
    };
    // path://root/map_n/output/item/city_output_be
    MainWindCtrl.prototype.onClickCityOutput = function (event, data) {
        var _this = this;
        var _a;
        var cell = event.target.parent.Data;
        if (!cell || !cell.isOwn()) {
            return;
        }
        var rewards = ((_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.cityOutputMap[cell.index]) || [];
        this.model.claimCityOutput(cell.index).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            // gameHpr.addGainMassage(rewards)
            rewards.forEach(function (m, i) { return AnimHelper_1.animHelper.playFlutterCellOutput(i * 0.4, m, _this.topLayerNode_, cell.actPosition, _this.key); });
        });
    };
    // path://root/cell_info/buttons/ancient_info_be
    MainWindCtrl.prototype.onClickAncientInfo = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            var ancient = this.model.getAncientInfo(cell.index);
            if (ancient) {
                var build = new BuildObj_1.default().init(cell.index, ut.UID(), cc.v2(0, 0), ancient.id, ancient.lv);
                ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', build);
            }
        }
    };
    // path://root/cell_info/buttons/city_skin_be
    MainWindCtrl.prototype.onClickCitySkin = function (event, data) {
        var _this = this;
        var cell = this.cellInfoCmpt.getCell();
        if (cell && cell.cityId > 0) {
            ViewHelper_1.viewHelper.showPnl('main/SelectCitySkin', cell, function (ok) {
                if (_this.isValid && ok) {
                    _this.hideSelectCell(false);
                }
            });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    MainWindCtrl.prototype.onNetReconnect = function () {
        if (!GameHelper_1.gameHpr.guide.isOneGuideWorking()) { //这里如果在新手引导 就不要关闭了
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close();
            this.model.initCameraInfo();
        }
        this.updateMap(this.centre); //刷新地图显示
        this.initMarch(); //初始化行军
    };
    // 更新地块信息
    MainWindCtrl.prototype.onUpdateCellInfo = function () {
        this.updateMap(this.centre);
    };
    // 添加行军
    MainWindCtrl.prototype.onAddMarch = function (data) {
        var _this = this;
        if (!data.isCanShowMarch()) {
            return this.onRemoveMarch(data);
        }
        var march = this.marchs.find(function (m) { return m.uid === data.uid; });
        if (march) {
            march.init(data, this.marchRoleNode_, this.key);
            this.checkMarchLineOffset(data);
        }
        else {
            this.marchLineNode_.AddItem(function (it) {
                march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
                _this.checkMarchLineOffset(data);
            });
        }
    };
    // 删除行军
    MainWindCtrl.prototype.onRemoveMarch = function (data) {
        var march = this.marchs.remove('uid', data.uid);
        if (march) {
            march.clean();
            this.checkMarchLineOffset(data);
        }
    };
    // 刷新所有行军
    MainWindCtrl.prototype.onUpdateAllMarch = function () {
        this.initMarch();
    };
    // 隐藏文本
    MainWindCtrl.prototype.onHideWorldText = function (val) {
        val = !val;
        this.textNode_.active = val;
        this.ancientTextNode_.active = val;
        this.btinfoNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = val); });
        this.tondenNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = val); });
    };
    // 关闭选择地块
    MainWindCtrl.prototype.onCloseSelectCell = function (play) {
        this.hideSelectCell(!!play);
    };
    // 刷新战斗状态
    MainWindCtrl.prototype.onUpdateBattleDistInfo = function () {
        var cells = [], distMap = this.model.getBattleDistMap();
        for (var index in distMap) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push(cell);
        }
        this.battleNode.Items(cells, function (it, data) { return it.setPosition(data.actPosition); });
    };
    // 刷新免战状态
    MainWindCtrl.prototype.onUpdateAvoidWarDistInfo = function () {
        this.updateIconNode();
    };
    // 刷新修建信息
    MainWindCtrl.prototype.onUpdateBtCity = function (index) {
        var _a;
        this.updateMap(this.centre);
        if (((_a = this.cellInfoCmpt.getCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === index) {
            this.cellInfoCmpt.updateInfo();
        }
    };
    // 刷新屯田信息
    MainWindCtrl.prototype.onUpdateTonden = function (index) {
        var _a;
        this.updateMap(this.centre);
        if (((_a = this.cellInfoCmpt.getCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === index) {
            this.cellInfoCmpt.updateInfo();
        }
    };
    // 刷新地图上面的军队分布情况  这里主动绘制一次
    MainWindCtrl.prototype.onUpdateArmyDistInfo = function () {
        this.updateIconNode();
        this.cellInfoCmpt.updateArmyInfo();
    };
    // 移动地图
    MainWindCtrl.prototype.onMapMoveTo = function (point, showCellInfo) {
        var _this = this;
        if (this.centre.equals(point)) {
            return showCellInfo && this.showSelectCell(this.model.getMapCellByPoint(point.clone().floor()));
        }
        else if (!this.tempShowCellMap[MapHelper_1.mapHelper.pointToIndex(point)]) { //如果没有在当前绘制区域就移动到目标点
            var start = this.centre.sub(point, this._temp_vec2_4).normalizeSelf().mulSelf(2).addSelf(point);
            CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(start), MapHelper_1.mapHelper.MAP_SIZE, Constant_1.MAP_SHOW_OFFSET, CameraCtrl_1.cameraCtrl.zoomRatio);
            this.updateMap(start.floor());
            this.checkInCameraMarchLine();
        }
        // 移动
        CameraCtrl_1.cameraCtrl.moveTo(0.25, MapHelper_1.mapHelper.getPixelByPoint(point).subSelf(CameraCtrl_1.cameraCtrl.getWinSizeHalf())).then(function () {
            if (_this.isActive && showCellInfo) {
                _this.showSelectCell(_this.model.getMapCellByPoint(point.clone().floor()));
            }
        });
    };
    // 刷新玩家昵称
    MainWindCtrl.prototype.onUpdatePlayerNickname = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            this.updatePlayerNickname(it, data);
        }
    };
    // 刷新玩家头像
    MainWindCtrl.prototype.onUpdatePlayerHeadIcon = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, this.INIT_KEY);
        }
    };
    // 刷新联盟地图标记
    MainWindCtrl.prototype.onUpdateAlliMapFlag = function () {
        var cells = [], mapFalg = GameHelper_1.gameHpr.alliance.getMapFlag();
        for (var index in mapFalg) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push({ cell: cell, flag: mapFalg[index] });
        }
        this.mapFlagNode.Items(cells, function (it, data) {
            it.setPosition(data.cell.actPosition);
            it.Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(data.flag);
        });
    };
    // 刷新行军线透明度
    MainWindCtrl.prototype.onUpdateMarchOpacity = function () {
        this.marchs.forEach(function (m) { return m.updateOpacity(); });
    };
    // 播放新的地块效果
    MainWindCtrl.prototype.onPlayNewCellEffect = function () {
        this.playNewCellEffect();
    };
    // 播放屯田结束效果
    MainWindCtrl.prototype.onPlayCellTondenEffect = function () {
        this.playCellTondenEffect();
    };
    // 播放地图表情
    MainWindCtrl.prototype.onPlayCellEmoji = function () {
        this.playCellEmoji();
    };
    // 刷新城市产出
    MainWindCtrl.prototype.onUpdateCityOutput = function () {
        var cells = [];
        for (var index in this.tempShowCellMap) {
            var cell = this.tempShowCellMap[index];
            var output = cell.getOutputType();
            if (output) {
                cells.push({ cell: cell, output: output });
            }
        }
        this.outputNode.Items(cells, function (it, data) {
            it.setPosition(data.cell.actPosition);
            it.Child('city_output_be').active = data.cell.isOwn();
            it.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[data.output.type]);
            it.Child('root/val', cc.Label).string = data.output.count > 1 ? data.output.count + '' : '';
        });
    };
    // 改变季节完成
    MainWindCtrl.prototype.onChangeSeasonComplete = function () {
        this.playChangeSeason(this.model.getSeason().type);
    };
    // 刷新遗迹
    MainWindCtrl.prototype.onUpdateAncientInfo = function (data) {
        var index = data.index;
        var it = this.ancientTextNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.index) === index; });
        if (it) {
            this.updateAncientTextInfo(it, data);
        }
    };
    // 刷新城市皮肤
    MainWindCtrl.prototype.onUpdateCitySkin = function (index) {
        this.updateMap(this.centre);
    };
    // 若引导
    MainWindCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'main') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key);
        }
    };
    Object.defineProperty(MainWindCtrl.prototype, "isActive", {
        // ----------------------------------------- custom function ----------------------------------------------------
        get: function () { return this.isValid && this.isEnter(); },
        enumerable: false,
        configurable: true
    });
    // 点击地图
    MainWindCtrl.prototype.onClickMap = function (worldLocation) {
        var cell = this.model.getMapCellByPoint(MapHelper_1.mapHelper.getPointByPixel(worldLocation));
        if (cell && !this.selectCellNode_.Data) {
            audioMgr.playSFX('click');
            this.showSelectCell(cell);
            CameraCtrl_1.cameraCtrl.redressPositionByRange(cell.actPosition, Constant_1.SELECT_CELL_INFO_BOX);
        }
        else {
            this.hideSelectCell();
        }
    };
    // 刷新场景特效
    MainWindCtrl.prototype.updateSeasonSeceneEffect = function () {
        this.sceneEffect.clean();
        var sceneEffectUrl = this.model.getSeason().getCurrSceneEffectUrl();
        if (sceneEffectUrl) { //加载场景特效
            this.sceneEffect.init(sceneEffectUrl, this.INIT_KEY);
        }
    };
    // 绘制地图
    MainWindCtrl.prototype.updateMap = function (centre) {
        var _this = this;
        var _a, _b, _c, _d;
        this.model.initDecorationUseLand();
        this.preCameraZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.centre.set(centre);
        this.model.setCentre(centre);
        // 绘制地面
        var armyDistMap = this.player.getArmyDistMap(), battleDist = this.model.getBattleDistMap();
        var mapFlag = GameHelper_1.gameHpr.alliance.getMapFlag();
        var btCityMap = this.model.getBTCityQueueMap();
        var tondenMap = this.model.getTondenQueueMap();
        var di = 0, linei = 0, li = 0, mi = 0, ii = 0, ti = 0, bi = 0, mfi = 0, oi = 0, mti = 0, pli = 0;
        var texts = [], tondens = [], btCitys = [], ancientTexts = [];
        // this.seawaveAnimNodePool?.reset()
        this.cityAnimNodePool.reset();
        this.tempShowCellMap = {};
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, this.model.getMaxTileRange());
        var seasonType = this.seasonType;
        var tempDecorationLoadMap = {};
        for (var i = 0; i < points.length; i++) {
            var point = points[i], cell = this.model.getMapCellByPoint(point);
            var position = (cell === null || cell === void 0 ? void 0 : cell.position) || MapHelper_1.mapHelper.getPixelByPoint(point);
            if (cell) {
                var btInfo = btCityMap[cell.index], tondenInfo = tondenMap[cell.index];
                this.tempShowCellMap[cell.index] = cell;
                if (cell.cityId > 0) {
                    var animName = cell.cityId === Constant_1.CITY_FORT_NID ? 'city_2102_' + cell.getOwnType() : undefined;
                    var city = this.cityAnimNodePool.showNode(cell.getCityViewId(), cell.actPosition, true, animName);
                    if (cell.cityId === Constant_1.CITY_MAIN_NID) {
                        // 这里先获取后面用来显示文本
                        texts.push(cell);
                        // 是否有保护模式 绘制保护线
                        var state = GameHelper_1.gameHpr.checkPlayerProtectModeState(cell.owner);
                        if (state > 0) {
                            ResHelper_1.resHelper.getNodeByIndex(this.protectLineNode, pli++, cell.actPosition).opacity = state === 1 ? 255 : 100;
                        }
                    }
                    else if (cell.isAncient()) {
                        var info = this.model.getAncientInfo(cell.index);
                        if (info) {
                            ancientTexts.push(info); //遗迹
                            city.Child('val', cc.MultiFrame).setFrame(info.lv === 20);
                        }
                    }
                }
                else if (cell.cityId < 0) {
                }
                else if (tondenInfo) { //绘制屯田地
                    tondens.push({ tondenInfo: tondenInfo, cell: cell });
                    ResHelper_1.resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon('land_tonden', seasonType);
                }
                else if (cell.icon && (!btInfo || btInfo.id === 0)) {
                    ResHelper_1.resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(cell.icon, seasonType);
                }
                // 绘制修建信息
                if (btInfo) {
                    btCitys.push({ btInfo: btInfo, cell: cell });
                    // 只绘制修建 不绘制拆除
                    if (cell.cityId === 0 && btInfo.id > 0) {
                        this.cityAnimNodePool.showNode(btInfo.id, cell.actPosition, false);
                    }
                }
                // 绘制地图军队分布图标
                if (!!armyDistMap[cell.index]) {
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_a = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _a === void 0 ? void 0 : _a.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(-22, y).addSelf(cell.position); //显示到左下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('army_min_icon');
                }
                // 绘制免战图标
                if (cell.isCanShowAvoidWar()) {
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_b = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _b === void 0 ? void 0 : _b.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(22, y).addSelf(cell.getRightPosition(this._temp_vec2_5)); //显示到右下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('avoidwar_icon_1');
                }
                // 绘制战斗图标
                if (!!battleDist[cell.index]) {
                    ResHelper_1.resHelper.getNodeByIndex(this.battleNode, bi++, cell.actPosition);
                }
                var flag = mapFlag[cell.index];
                if (flag) { //地图标记
                    ResHelper_1.resHelper.getNodeByIndex(this.mapFlagNode, mfi++, cell.actPosition).Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(flag);
                }
                // 绘制产出
                var output = cell.getOutputType();
                if (output) {
                    var oNode = ResHelper_1.resHelper.getNodeByIndex(this.outputNode, oi++, cell.actPosition);
                    oNode.Data = cell;
                    oNode.Child('city_output_be').active = cell.isOwn();
                    oNode.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[output.type]);
                    oNode.Child('root/val', cc.Label).string = output.count > 1 ? output.count + '' : '';
                }
                // 记录边框线
                var borderLines = cell.owner ? cell.borderLines : [];
                if (borderLines.length > 0) {
                    var lineItemNode = ResHelper_1.resHelper.getNodeByIndex(this.lineNode, linei++, position);
                    ViewHelper_1.viewHelper.updateCellBorderLines(lineItemNode, borderLines, cell.getBorderLineColor());
                }
                // 绘制遮罩
                if (!cell.owner) {
                    var maskItemNode = ResHelper_1.resHelper.getNodeByIndex(this.maskNode, mi++, position);
                    maskItemNode.opacity = cell.getProtectOwner() ? 20 : 38;
                }
                //绘制地图装饰
                var decorationCell = cell;
                if (!decorationCell.decorationJson && this.model.getDecorationIndex(cell.index)) {
                    decorationCell = this.model.getMapCells()[this.model.getDecorationIndex(cell.index)];
                }
                if (decorationCell.decorationJson && !tempDecorationLoadMap[decorationCell.index]) {
                    tempDecorationLoadMap[decorationCell.index] = true;
                    var itemNode = null;
                    var iconName = this.model.getDecorationIcon(decorationCell);
                    var frame = ResHelper_1.resHelper.getLandItemIcon(iconName, seasonType);
                    switch (decorationCell.decorationJson.type) {
                        case Enums_1.DecorationType.MOUNTAIN:
                            itemNode = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, decorationCell.position);
                            if (!decorationCell.mountainAnchor) {
                                //偶数倍数时锚点偏移
                                var size = frame.getOriginalSize();
                                var anchorX = 0.5, anchorY = 0.5;
                                if (size.width / Constant_1.TILE_SIZE % 2 == 0) {
                                    anchorX = (size.width / 2 - Constant_1.TILE_SIZE / 2) / size.width;
                                }
                                if (size.height / Constant_1.TILE_SIZE % 2 == 0) {
                                    anchorY = (size.height / 2 - Constant_1.TILE_SIZE / 2) / size.height;
                                }
                                decorationCell.mountainAnchor = cc.v2(anchorX, anchorY);
                            }
                            itemNode.setAnchorPoint(decorationCell.mountainAnchor);
                            break;
                        default:
                            itemNode = ResHelper_1.resHelper.getNodeByIndex(this.diNode, di++, decorationCell.position);
                            break;
                    }
                    itemNode.Component(cc.Sprite).spriteFrame = frame;
                }
            }
            else {
                var landId = this.model.getRoundId(point.x, point.y);
                if (landId) {
                    var itemInfo = assetsMgr.getJsonData('land', landId);
                    ResHelper_1.resHelper.getNodeByIndex(this.diNode, di++, position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(itemInfo.icon, seasonType);
                    ResHelper_1.resHelper.getNodeByIndex(this.maskNode, mi++, position);
                }
            }
        }
        // 隐藏多余的
        ResHelper_1.resHelper.hideNodeByIndex(this.diNode, di);
        ResHelper_1.resHelper.hideNodeByIndex(this.protectLineNode, pli);
        ResHelper_1.resHelper.hideNodeByIndex(this.lineNode, linei);
        ResHelper_1.resHelper.hideNodeByIndex(this.landNode, li);
        ResHelper_1.resHelper.hideNodeByIndex(this.maskNode, mi);
        ResHelper_1.resHelper.hideNodeByIndex(this.iconNode, ii);
        ResHelper_1.resHelper.hideNodeByIndex(this.battleNode, bi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mapFlagNode, mfi);
        ResHelper_1.resHelper.hideNodeByIndex(this.outputNode, oi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mountainNode, mti);
        // this.seawaveAnimNodePool?.hideOtherNode()
        this.cityAnimNodePool.hideOtherNode();
        // 当前正在显示的
        var showIndex = (_d = (_c = this.cellInfoCmpt.getCell()) === null || _c === void 0 ? void 0 : _c.actIndex) !== null && _d !== void 0 ? _d : -1;
        var zIndexMaxY = MapHelper_1.mapHelper.MAP_SIZE.y;
        var isCanShowText = !this.touchCmpt.isDraging();
        // 绘制文本层
        this.textNode_.Items(texts, function (it, data) {
            var pos = data.actPosition, index = data.actIndex;
            var d = it.Data;
            var info = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
            it.setPosition(pos.x, pos.y + 76);
            if (!d || d.owner !== data.owner || d.nickname !== (info === null || info === void 0 ? void 0 : info.nickname) || d.title !== (info === null || info === void 0 ? void 0 : info.title) || d.headIcon !== (info === null || info === void 0 ? void 0 : info.headIcon)) {
                ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), info === null || info === void 0 ? void 0 : info.headIcon, _this.INIT_KEY);
                _this.updatePlayerNickname(it, info);
            }
            it.active = showIndex !== index;
            it.Data = { index: index, owner: data.owner, nickname: info === null || info === void 0 ? void 0 : info.nickname, title: info === null || info === void 0 ? void 0 : info.title, headIcon: info === null || info === void 0 ? void 0 : info.headIcon };
        });
        this.textNode_.active = isCanShowText;
        // 绘制遗迹文本层
        this.ancientTextNode_.Items(ancientTexts, function (it, data) {
            var pos = data.cell.actPosition, index = data.index;
            it.setPosition(pos.x, pos.y + 72);
            _this.updateAncientTextInfo(it, data);
            it.active = showIndex !== index;
            it.Data = { index: index };
        });
        this.ancientTextNode_.active = isCanShowText;
        // 绘制修建城市的信息
        this.btinfoNode.Items(btCitys, function (it, data) {
            var _a;
            var info = data.btInfo, index = info.index;
            it.setPosition(data.cell.actPosition);
            var timeNode = it.Child('time');
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.index) !== index) {
                var surplusTime = info.getSurplusTime();
                timeNode.Color(info.id ? '#21DC2D' : '#FF9162');
                timeNode.Component(cc.LabelTimer).run(surplusTime * 0.001);
                // 动画
                var anim_1 = it.Child('anim', cc.Animation);
                var elapsedTime = Math.max(0, info.needTime - surplusTime) * 0.001;
                var tween = cc.tween(it);
                tween.stop();
                if (elapsedTime < 0.62) {
                    anim_1.play('cting_begin', elapsedTime);
                    tween.delay(0.62 - elapsedTime).call(function () { return _this.isValid && anim_1.play('cting_loop'); }).start();
                }
                else {
                    anim_1.play('cting_loop');
                }
            }
            timeNode.active = isCanShowText && showIndex !== index;
            it.Data = { index: index };
            it.zIndex = zIndexMaxY - data.cell.actPoint.y;
        });
        // 绘制屯田信息
        this.tondenNode.Items(tondens, function (it, data) {
            var _a;
            var info = data.tondenInfo, index = info.index;
            it.setPosition(data.cell.actPosition);
            var timeNode = it.Child('time');
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.index) !== index) {
                timeNode.Component(cc.LabelTimer).run(info.getSurplusTime() * 0.001);
            }
            timeNode.active = isCanShowText;
            it.Data = { index: index };
            it.zIndex = zIndexMaxY - data.cell.actPoint.y;
        });
    };
    // 0.上 1.右 2.下 3.左 4.左上 5.右上 6.右下 7.左下
    MainWindCtrl.prototype.getSeaLandIcon = function (point, minx, miny, maxx, maxy) {
        if (point.x < minx) {
            return point.y < miny ? 7 : (point.y < maxy ? 3 : 4);
        }
        else if (point.x < maxx) {
            return point.y < miny ? 2 : 0;
        }
        return point.y < miny ? 6 : (point.y < maxy ? 1 : 5);
    };
    MainWindCtrl.prototype.setSeaLand = function (it, type, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy);
        it.Component(cc.Sprite).spriteFrame = this.getLandIcon(type + "_" + Math.min(dir, 4));
    };
    // 海浪
    MainWindCtrl.prototype.setSeawaveLand = function (position, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy), no = Math.floor(dir / 4) + 1, angle = dir % 4;
        var it = this.seawaveAnimNodePool.showNode(no, position, true);
        it.angle = angle * -90;
    };
    MainWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandIcon(icon);
    };
    MainWindCtrl.prototype.updatePlayerNickname = function (it, data) {
        var _a;
        var nameLbl = it.Child('name/val', cc.Label);
        nameLbl.string = ut.nameFormator((_a = data === null || data === void 0 ? void 0 : data.nickname) !== null && _a !== void 0 ? _a : '???', 7);
        var titleLbl = it.Child('name/title', cc.Label);
        if (titleLbl.setActive(!!(data === null || data === void 0 ? void 0 : data.title))) {
            var json = assetsMgr.getJsonData('title', data.title);
            titleLbl.Color((json === null || json === void 0 ? void 0 : json.color) || '#333333').setLocaleKey('titleText.' + (json === null || json === void 0 ? void 0 : json.id));
            nameLbl.node.y = -10;
        }
        else {
            nameLbl.node.y = 0;
        }
    };
    MainWindCtrl.prototype.updateAncientTextInfo = function (it, data) {
        it.Child('name').setLocaleKey('ui.ancient_name_text', data.name, assetsMgr.lang('ui.short_lv', data.lv || 1));
        if (it.Child('time').active = data.state === 1 && !data.pauseState) {
            it.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
        }
    };
    // 刷新显示文本节点
    MainWindCtrl.prototype.updateHideTextByIndex = function (index) {
        if (index === void 0) { index = -1; }
        this.textNode_.children.forEach(function (m) { return m.active = !!m.Data && m.Data.index !== index; });
        this.ancientTextNode_.children.forEach(function (m) { return m.active = !!m.Data && m.Data.index !== index; });
        this.btinfoNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = m.Data.index !== index); });
    };
    // 刷新图标层
    MainWindCtrl.prototype.updateIconNode = function () {
        var _this = this;
        var offset1 = cc.v2(-22, -22);
        var offset2 = cc.v2(22, -22);
        var cells = [], armyDistMap = this.player.getArmyDistMap();
        for (var key in this.tempShowCellMap) {
            var cell = this.tempShowCellMap[key];
            if (cell.isCanShowAvoidWar()) {
                cells.push({ position: cell.getRightPosition(), offset: offset2, icon: 'avoidwar_icon_1' });
            }
            if (armyDistMap[key]) {
                cells.push({ position: cell.position, offset: offset1, icon: 'army_min_icon' });
            }
        }
        this.iconNode.Items(cells, function (it, data) {
            it.setPosition(_this._temp_vec2_3.set(data.offset).addSelf(data.position));
            it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon(data.icon);
        });
    };
    // 显示选择地块
    MainWindCtrl.prototype.showSelectCell = function (cell) {
        if (!cell || cell.landType == Enums_1.LandType.SEA || cell.landType == Enums_1.LandType.BEACH) {
            return;
        }
        else if (cell.actIndex !== cell.index) {
            cell = this.model.getMapCellByIndex(cell.actIndex);
        }
        var pos = this.selectCellNode_.Data = cell.actPosition;
        this.selectCellNode_.Component(SelectCellCmpt_1.default).open(pos, cell.getSize());
        this.cellInfoCmpt.open(pos, cell);
        // 隐藏文本节点
        this.updateHideTextByIndex(cell.actIndex);
    };
    // 隐藏
    MainWindCtrl.prototype.hideSelectCell = function (play) {
        if (play === void 0) { play = true; }
        if (this.selectCellNode_.Data) {
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close(play);
            this.updateHideTextByIndex();
        }
    };
    // 初始化行军
    MainWindCtrl.prototype.initMarch = function () {
        var _this = this;
        this.cleanMarch();
        var list = this.model.getAllMarchs().filter(function (m) { return m.isCanShowMarch(); });
        this.marchLineNode_.Items(list, function (it, data) {
            var march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
            march.isCheckLineOffset = false;
        });
        this.marchs.forEach(function (m) { return !m.isCheckLineOffset && _this.checkMarchLineOffset(m.getData()); });
    };
    MainWindCtrl.prototype.cleanMarch = function () {
        while (this.marchs.length > 0) {
            this.marchs.pop().clean();
        }
        this.marchs = [];
        // resHelper.cleanNodeChildren(this.marchLineNode_) //这个注释了 不知道什么原因会出现行军线被消耗的情况
        this.marchRoleNode_.removeAllChildren();
    };
    // 检测行军线偏移
    MainWindCtrl.prototype.checkMarchLineOffset = function (data) {
        var others = [];
        this.marchs.forEach(function (m) {
            var d = m.getData();
            if (data.checkOtherMarchLine(d)) {
                m.angleOffset = data.startIndex === d.startIndex ? 0 : -180;
                m.isCheckLineOffset = true;
                others.push(m);
            }
        });
        var len = others.length;
        others.forEach(function (m, i) { return m.updateLineOffset(i, len); });
    };
    // 攻击地块
    MainWindCtrl.prototype.occupyCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 2, 0)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_TIME) {
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_time_tip')];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'occupy', index, list, canGotoCount, function (armys, isSameSpeed, autoBackType) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.occupyCell(armys, index, autoBackType, isSameSpeed).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 移动过去屯田
    MainWindCtrl.prototype.cellTonden = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 3, 0)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err === ECode_1.ecode.BATTLEING) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.battleing_not_tonden')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectTondenArmy', index, list, canGotoCount, function (army) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.cellTonden(army, index).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 取消屯田
    MainWindCtrl.prototype.cancelTonden = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.model.cancelTonden(index)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isActive) {
                            this.hideSelectCell(false);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 移动到地块
    MainWindCtrl.prototype.moveToCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 1)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'move', index, list, canGotoCount, function (armys, isSameSpeed) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.moveCellArmy(armys, index, isSameSpeed).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放新的地块效果
    MainWindCtrl.prototype.playNewCellEffect = function () {
        var _this = this;
        this.model.getNotPlayNewCells().forEach(function (index) {
            var cell = _this.tempShowCellMap[index];
            if (cell) {
                var json_1 = cell.getResJson() || {}, keys = Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; });
                var pos_1 = cell.actPosition, isMore_1 = keys.length > 1;
                keys.forEach(function (key, i) { return AnimHelper_1.animHelper.playFlutterCellRes(key, json_1[key], 0.3 + i * 0.2, isMore_1, _this.topLayerNode_, pos_1, _this.key); });
                AnimHelper_1.animHelper.playNewCellEffect(_this.cellEffectNode_, pos_1, _this.key);
                // 隐藏行军线
                _this.marchs.forEach(function (march) { return march.isHasIndex(index) && march.hide(1.2); });
            }
        });
    };
    // 播放屯田结束的地块效果
    MainWindCtrl.prototype.playCellTondenEffect = function () {
        var _this = this;
        this.model.getNotPlayCellTondens().forEach(function (data) {
            var _a;
            var cell = _this.tempShowCellMap[data.index];
            if (cell) {
                var pos = cell.actPosition;
                var obj_1 = {};
                (_a = data.treasureIds) === null || _a === void 0 ? void 0 : _a.forEach(function (id) {
                    var idObj = obj_1[id];
                    if (!idObj) {
                        var json = assetsMgr.getJsonData('treasure', id);
                        idObj = obj_1[id] = { count: 0, icon: 'treasure_' + ((json === null || json === void 0 ? void 0 : json.lv) || 1) + '_0' };
                    }
                    idObj.count += 1;
                });
                for (var key in obj_1) {
                    var data_1 = obj_1[key];
                    AnimHelper_1.animHelper.playFlutterTreasure(data_1.icon, data_1.count, _this.topLayerNode_, pos, _this.key);
                }
                AnimHelper_1.animHelper.playNewCellEffect(_this.cellEffectNode_, pos, _this.key);
            }
        });
    };
    // 播放地图表情
    MainWindCtrl.prototype.playCellEmoji = function () {
        var _this = this;
        var now = Date.now();
        GameHelper_1.gameHpr.ground.getCellEmojis().forEach(function (m) {
            var cell = _this.tempShowCellMap[m.index], item = _this.cellEmojiItemMap[Math.floor(m.emoji / 1000)];
            if (cell && item) {
                var node = _this.cellEmojiMap[m.index];
                if (node === null || node === void 0 ? void 0 : node.isValid) {
                    node.Child('root').children.forEach(function (it) { return nodePoolMgr.put(it); });
                    node.destroy();
                }
                var startTime = Math.max(0, (now - m.getTime) * 0.001);
                node = _this.cellEmojiMap[m.index] = cc.instantiate2(item, _this.cellEmojiNode_);
                node.Data = m.index;
                node.setPosition(cell.actPosition);
                node.zIndex = cell.point.y;
                AnimHelper_1.animHelper.playCellEmoji(node, m.emoji, m.uid, startTime, _this.key).then(function (n) {
                    if (_this.isValid && (n === null || n === void 0 ? void 0 : n.isValid)) {
                        delete _this.cellEmojiMap[n.Data];
                        n.Child('root').children.forEach(function (it) { return nodePoolMgr.put(it); });
                        n.destroy();
                    }
                });
            }
        });
    };
    MainWindCtrl.prototype.testPlayNewCell = function (x, y, keys, delay) {
        var _this = this;
        // for (let i = 0; i < 5; i++) {
        //     const position = mapHelper.getPixelByPoint(cc.v2(x + i, y)).clone()
        //     animHelper.playFlutterCellRes('stone', 30, this.topLayerNode_, position, this.key)
        //     animHelper.playNewCellEffect(this.cellEffectNode_, position, this.key)
        // }
        var pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(x, y)).clone(), isMore = keys.length > 1;
        keys.forEach(function (key, i) { return AnimHelper_1.animHelper.playFlutterCellRes(key, 1, 0.3 + i * delay, isMore, _this.topLayerNode_, pos, _this.key); });
        AnimHelper_1.animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key);
    };
    // 检测季节
    MainWindCtrl.prototype.checkSeason = function () {
        var _this = this;
        var _a;
        var oldType = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.LAST_PLAY_SEASON_TYPE)) !== null && _a !== void 0 ? _a : -1;
        this.seasonType = cc.misc.clampf(oldType, 0, 3);
        if (ResHelper_1.resHelper.checkLandSkin(this.seasonType)) {
            this.updateMap(this.model.getCentre()); //刷新地图显示
        }
        else {
            ResHelper_1.resHelper.initLandSkin(this.seasonType).then(function () { return _this.isValid && _this.updateMap(_this.model.getCentre()); });
        }
        // 弹界面
        var curType = this.model.getSeason().type;
        if (oldType !== curType) {
            PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/SeasonSwitch' });
        }
    };
    // 播放切换季节
    MainWindCtrl.prototype.playChangeSeason = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.seasonType = type;
                        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.LAST_PLAY_SEASON_TYPE, type);
                        this.hideSelectCell(false);
                        return [4 /*yield*/, ResHelper_1.resHelper.initLandSkin(type)];
                    case 1:
                        _a.sent();
                        this.updateSeasonSeceneEffect();
                        this.updateMap(this.centre); //刷新地图显示
                        ResHelper_1.resHelper.cleanLandSkin();
                        return [2 /*return*/];
                }
            });
        });
    };
    MainWindCtrl.prototype.update = function (dt) {
        var _a;
        //
        (_a = this.seawaveAnimNodePool) === null || _a === void 0 ? void 0 : _a.update(dt);
        // 检测是否需要填充地图
        this.checkUpdateMap();
        // 检测是否在相机范围
        this.checkInCameraRange();
    };
    MainWindCtrl.prototype.checkUpdateMap = function () {
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2_1);
        var size = Math.max(Math.abs(point.x - this.centre.x), Math.abs(point.y - this.centre.y));
        if (size >= Constant_1.MAP_EXTRA_SIZE / 2 || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateMap(point);
            this.checkInCameraMarchLine();
        }
    };
    // 检测只会在在相机范围内的行军线
    MainWindCtrl.prototype.checkInCameraMarchLine = function () {
        var _a;
        var uidMap = {};
        this.marchs.forEach(function (m) {
            m.checkUpdateInCamera();
            uidMap[m.uid] = true;
        });
        // 兼容检测是否有多余的行军角色
        for (var i = this.marchRoleNode_.childrenCount - 1; i >= 0; i--) {
            var node = this.marchRoleNode_.children[i];
            if (!uidMap[(_a = node.Data) === null || _a === void 0 ? void 0 : _a.uid]) {
                node.destroy();
            }
        }
    };
    MainWindCtrl.prototype.checkInCameraRange = function () {
        var _a;
        var position = CameraCtrl_1.cameraCtrl.getPosition();
        if (this.preCameraPosition.equals(position)) {
            return;
        }
        this.preCameraPosition.set(position);
        // 选择地块框
        if ((_a = this.cellInfoCmpt) === null || _a === void 0 ? void 0 : _a.checkNotInScreenRange()) {
            this.hideSelectCell(false);
        }
    };
    MainWindCtrl = __decorate([
        ccclass
    ], MainWindCtrl);
    return MainWindCtrl;
}(mc.BaseWindCtrl));
exports.default = MainWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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