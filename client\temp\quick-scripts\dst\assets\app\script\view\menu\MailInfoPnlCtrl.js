
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/MailInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '03f52oPlWdKPbzt6ww8C/z3', 'MailInfoPnlCtrl');
// app/script/view/menu/MailInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var LabelAutoAnyCmpt_1 = require("../cmpt/LabelAutoAnyCmpt");
var ccclass = cc._decorator.ccclass;
var MailInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(MailInfoPnlCtrl, _super);
    function MailInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleNode_ = null; // path://root/title_n
        _this.senderNode_ = null; // path://root/sender_n
        _this.timeLbl_ = null; // path://root/time_l
        _this.contentSv_ = null; // path://root/content_sv
        _this.listSv_ = null; // path://root/list_sv
        _this.buttonsNode_ = null; // path://root/buttons_n
        _this.autoRemoveNode_ = null; // path://root/auto_remove_n
        //@end
        _this.data = null;
        _this.ITEM_ADAPT_SIZE = cc.size(64, 64);
        return _this;
    }
    MailInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.TRANSLATE_TEXT_COMPLETE] = this.onTranslateTextComplete, _a.enter = true, _a),
        ];
    };
    MailInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MailInfoPnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        this.checkRead();
        this.updateContent(data);
        this.buttonsNode_.Child('reply_be').active = !!data.sender && data.sender !== '-1'; //不能回复系统邮件
    };
    MailInfoPnlCtrl.prototype.onRemove = function () {
    };
    MailInfoPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/remove_be
    MailInfoPnlCtrl.prototype.onClickRemove = function (event, _) {
        var _this = this;
        var _a;
        if (!this.data) {
            return;
        }
        else if (((_a = this.data.items) === null || _a === void 0 ? void 0 : _a.length) > 0 && this.data.state !== Enums_1.MailStateType.READ) {
            ViewHelper_1.viewHelper.showMessageBox('ui.has_not_claim_mail_item', {
                ok: function () { return _this.removeMail(); },
                cancel: function () { },
            });
        }
        else {
            this.removeMail();
        }
    };
    // path://root/buttons_n/claim_be
    MailInfoPnlCtrl.prototype.onClickClaim = function (event, _) {
        var _this = this;
        if (!this.data || !this.data.items || this.data.items.length === 0) {
            return;
        }
        var data = this.data;
        var arr = [], indexs = [];
        data.items.forEach(function (m, i) {
            var _a;
            if (!((_a = data.oneClaims) === null || _a === void 0 ? void 0 : _a.has(i))) {
                arr.push(m);
                indexs.push(i);
            }
        });
        if (arr.length === 0) {
            return;
        }
        var items = GameHelper_1.gameHpr.checkRewardFull(arr);
        if (items.length > 0) {
            return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.claimReward(data); });
        }
        else if (arr.length === 1) {
            return this.claimRewardOne(data, indexs[0]);
        }
        else if (arr.some(function (m) { return m.type === Enums_1.CType.HERO_OPT; })) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.claim_hero_opt_tip', {
                ok: function () { return _this.claimReward(data); },
                cancel: function () { },
            });
        }
        this.claimReward(data);
    };
    // path://root/buttons_n/reply_be
    MailInfoPnlCtrl.prototype.onClickReply = function (event, data) {
        this.hide();
        ViewHelper_1.viewHelper.showPnl('menu/WriteMail', this.data);
    };
    // path://root/list_sv/view/content/item_be
    MailInfoPnlCtrl.prototype.onClickItem = function (event, _) {
        var _this = this;
        if (!this.data || !this.data.items || this.data.items.length === 0) {
            return;
        }
        var data = this.data, index = event.target.Data;
        var item = data.items[index];
        if (!item) {
            return;
        }
        var items = GameHelper_1.gameHpr.checkRewardFull([item]);
        if (items.length > 0) {
            return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.claimRewardOne(data, index); });
        }
        this.claimRewardOne(data, index);
    };
    // path://root/content_sv/view/content/translate_be
    MailInfoPnlCtrl.prototype.onClickTranslate = function (event, data) {
        GameHelper_1.gameHpr.translateText(this.data, 'mail');
        this.updateContentText(this.data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 翻译完成
    MailInfoPnlCtrl.prototype.onTranslateTextComplete = function (type, data) {
        if (type !== 'mail' || data.uid !== this.data.uid) {
            return;
        }
        this.updateContentText(data);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 检测是否已读
    MailInfoPnlCtrl.prototype.checkRead = function () {
        var data = this.data;
        if (data.state === Enums_1.MailStateType.NONE) {
            data.state = (data.items && data.items.length > 0) ? Enums_1.MailStateType.NOT_CLAIM : Enums_1.MailStateType.READ;
            GameHelper_1.gameHpr.net.send('mail/HD_ReadMail', { uid: data.uid }); //标记已读
            this.emit(EventType_1.default.UPDATE_MAIL_STATE, data);
        }
    };
    MailInfoPnlCtrl.prototype.updateContent = function (data) {
        this.titleNode_.Child('val', cc.Label).string = data.title;
        var isSys = data.sender === '-1';
        this.senderNode_.Child('val', cc.Label).Color(isSys ? '#BE772B' : '#756963').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8);
        this.timeLbl_.string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime);
        // 内容
        this.updateContentText(data);
        // 道具
        var items = data.items || [];
        var claimBtn = this.buttonsNode_.Child('claim_be', cc.Button);
        var hasItem = claimBtn.setActive(items.length > 0);
        if (this.listSv_.setActive(hasItem)) {
            this.updateClaimButton(claimBtn, data.state);
            this.updateItems();
        }
        // 刷新内容高度
        this.contentSv_.node.height = hasItem ? 280 : 420;
        this.contentSv_.scrollToTop();
        //
        if (this.autoRemoveNode_.active = !!data.autoDelSurplusTime) {
            var autoDelTime = Math.max(0, data.autoDelSurplusTime - (Date.now() - data.getTime));
            this.autoRemoveNode_.setLocaleKey('ui.auto_remove_mail_desc', GameHelper_1.gameHpr.millisecondToStringForDay(autoDelTime));
        }
    };
    MailInfoPnlCtrl.prototype.updateContentText = function (data) {
        var _a, _b;
        // 内容
        var content = this.contentSv_.content, contentLbl = content.Child('val', cc.Label);
        contentLbl.string = data.content;
        // 翻译
        content.Child('translate_be').active = !data.translate && !data.contentId && GameHelper_1.gameHpr.isGLobal();
        var lineNode = content.Child('line'), translateLoading = content.Child('loading'), translateLbl = content.Child('translate', cc.Label);
        lineNode.active = !!data.translate;
        translateLoading.active = !!((_a = data.translate) === null || _a === void 0 ? void 0 : _a.req);
        if (translateLbl.setActive(!!((_b = data.translate) === null || _b === void 0 ? void 0 : _b.text))) {
            translateLbl.string = data.translate.text;
            translateLbl.Component(LabelAutoAnyCmpt_1.default).check();
            lineNode.width = translateLbl.node.width * 0.5;
        }
        else if (lineNode.active) {
            contentLbl._forceUpdateRenderData();
            lineNode.width = contentLbl.node.width * 0.5;
        }
    };
    MailInfoPnlCtrl.prototype.updateClaimButton = function (button, state) {
        button.interactable = state !== Enums_1.MailStateType.READ;
        button.Child('val').setLocaleKey(state === Enums_1.MailStateType.READ ? 'ui.yet_take' : 'ui.button_one_take');
    };
    MailInfoPnlCtrl.prototype.updateItems = function () {
        var _this = this;
        var data = this.data, claims = data.oneClaims || [], isClaim = data.state === Enums_1.MailStateType.READ;
        var len = data.items.length;
        this.listSv_.Items(data.items, function (it, item, i) {
            it.Data = i;
            ViewHelper_1.viewHelper.updateItemByCTypeOne(it, item, _this.key, _this.ITEM_ADAPT_SIZE);
            var claim = it.Child('claim').active = isClaim || claims.has(i);
            it.Child('icon').opacity = claim ? 120 : 255;
            it.Component(cc.Button).interactable = !claim;
        });
        if (len <= 4) {
            this.listSv_.node.width = Math.max(80, len * 80 + (len - 1) * 40) + 28 + 4;
            this.listSv_.node.x = 2;
        }
        else {
            this.listSv_.node.width = 528;
            this.listSv_.node.x = 0;
        }
        this.listSv_.node.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
    };
    // 领取单个物品
    MailInfoPnlCtrl.prototype.claimRewardOne = function (data, index) {
        var _this = this;
        var _a;
        var item = (_a = data.items) === null || _a === void 0 ? void 0 : _a[index];
        if (!item) {
            return;
        }
        else if (item.type === Enums_1.CType.HERO_OPT) {
            ViewHelper_1.viewHelper.showHeroOptSelect(item.id).then(function (id) { return (_this.isValid && id > 0) && _this.claimRewardOneDo(data, index, id); });
        }
        else {
            this.claimRewardOneDo(data, index, 0);
        }
    };
    MailInfoPnlCtrl.prototype.claimRewardOneDo = function (data, index, heroId) {
        var _this = this;
        GameHelper_1.gameHpr.net.request('mail/HD_ClaimMailItemOne', { uid: data.uid, index: index, heroId: heroId }, true).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            ViewHelper_1.viewHelper.showAlert('toast.take_succeed');
            GameHelper_1.gameHpr.addGainMassage(data.items[index]);
            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(res.data.rewards);
            if (data.oneClaims) {
                data.oneClaims.push(index);
            }
            else {
                data.oneClaims = [index];
            }
            if (data.oneClaims.length === data.items.length) {
                data.state = Enums_1.MailStateType.READ;
                _this.emit(EventType_1.default.UPDATE_MAIL_STATE, data);
            }
            if (_this.isValid) {
                _this.updateClaimButton(_this.buttonsNode_.Child('claim_be', cc.Button), data.state);
                _this.updateItems();
            }
        });
    };
    // 领取奖励
    MailInfoPnlCtrl.prototype.claimReward = function (data) {
        var _this = this;
        GameHelper_1.gameHpr.net.request('mail/HD_ClaimMailItem', { uid: data.uid }, true).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            ViewHelper_1.viewHelper.showAlert('toast.take_succeed');
            GameHelper_1.gameHpr.addGainMassage(data.items);
            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(res.data.rewards);
            data.state = Enums_1.MailStateType.READ;
            _this.emit(EventType_1.default.UPDATE_MAIL_STATE, data);
            if (_this.isValid) {
                _this.updateClaimButton(_this.buttonsNode_.Child('claim_be', cc.Button), data.state);
                _this.updateItems();
            }
        });
    };
    // 删除邮件
    MailInfoPnlCtrl.prototype.removeMail = function () {
        var _this = this;
        GameHelper_1.gameHpr.user.removeMail(this.data.uid).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.hide();
            }
        });
    };
    MailInfoPnlCtrl = __decorate([
        ccclass
    ], MailInfoPnlCtrl);
    return MailInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MailInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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