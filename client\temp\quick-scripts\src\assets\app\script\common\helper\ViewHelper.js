"use strict";
cc._RF.push(module, 'd7769eBkKxF1YXHywafMCu6', 'ViewHelper');
// app/script/common/helper/ViewHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewHelper = void 0;
var CTypeObj_1 = require("../../model/common/CTypeObj");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var Constant_1 = require("../constant/Constant");
var ECode_1 = require("../constant/ECode");
var Enums_1 = require("../constant/Enums");
var NetEvent_1 = require("../event/NetEvent");
var NotEvent_1 = require("../event/NotEvent");
var GameHelper_1 = require("./GameHelper");
var MapHelper_1 = require("./MapHelper");
var ResHelper_1 = require("./ResHelper");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var StrategyObj_1 = require("../../model/common/StrategyObj");
var FrameAnimationCmpt_1 = require("../../view/cmpt/FrameAnimationCmpt");
var PortrayalSkillObj_1 = require("../../model/common/PortrayalSkillObj");
var ArmyObj_1 = require("../../model/area/ArmyObj");
/**
 * 视图帮助方法
 */
var ViewHelper = /** @class */ (function () {
    function ViewHelper() {
    }
    // 跳转场景
    ViewHelper.prototype.gotoWind = function (val) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit.apply(eventCenter, __spread([mc.Event.GOTO_WIND, val, resolve], params)); })];
            });
        });
    };
    // 预加载场景
    ViewHelper.prototype.preloadWind = function (key, progress) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit(mc.Event.PRELOAD_WIND, key, resolve, progress); })];
            });
        });
    };
    // 预加载UI
    ViewHelper.prototype.preloadPnl = function (key, progress) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit(mc.Event.PRELOAD_PNL, key, resolve, progress); })];
            });
        });
    };
    // 显示UI
    ViewHelper.prototype.showPnl = function (key) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit.apply(eventCenter, __spread([mc.Event.OPEN_PNL, key, resolve], params)); })];
            });
        });
    };
    // 隐藏UI
    ViewHelper.prototype.hidePnl = function (key) {
        eventCenter.emit(mc.Event.HIDE_PNL, key);
    };
    // 显示提示框
    ViewHelper.prototype.showAlert = function (msg, opts) {
        if (msg === ECode_1.ecode.NOT_BIND_UID) {
            return eventCenter.emit(NetEvent_1.default.NET_DISCONNECT);
        }
        eventCenter.emit(NotEvent_1.default.OPEN_ALERT, msg, opts);
    };
    // 显示对话框
    ViewHelper.prototype.showMessageBox = function (msg, opts) {
        eventCenter.emit(NotEvent_1.default.OPEN_MESSAGE_BOX, msg, opts);
    };
    // 主动关闭对话框
    ViewHelper.prototype.hideMessageBox = function () {
        eventCenter.emit(NotEvent_1.default.HIDE_MESSAGE_BOX);
    };
    // 显示说明
    ViewHelper.prototype.showDesc = function (text, params) {
        this.showPnl('common/Desc', { text: text, params: params });
    };
    // 显示说明信息
    ViewHelper.prototype.showDescInfo = function (list, title) {
        if (title === void 0) { title = 'ui.explain'; }
        this.showPnl('common/DescInfo', { title: title, list: list });
    };
    // 显示网络等待
    ViewHelper.prototype.showNetWait = function (val, delay) {
        if (val) {
            eventCenter.emit(NetEvent_1.default.NET_REQ_BEGIN, delay);
        }
        else {
            eventCenter.emit(NetEvent_1.default.NET_REQ_END);
        }
    };
    // 显示通用加载动画
    ViewHelper.prototype.showLoadingWait = function (val) {
        if (val) {
            eventCenter.emit(mc.Event.LOADING_WAIT_BEGIN);
        }
        else {
            eventCenter.emit(mc.Event.LOADING_WAIT_END);
        }
    };
    // 显示加载wind的动画
    ViewHelper.prototype.showWindLoading = function (val) {
        if (val) {
            eventCenter.emit(mc.Event.LOAD_BEGIN_WIND);
        }
        else {
            eventCenter.emit(mc.Event.LOAD_END_WIND);
        }
    };
    // 显示连接失败
    ViewHelper.prototype.showConnectFail = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.connect_server_fail');
                        _this.showMessageBox(text, {
                            lockClose: true,
                            okText: 'login.button_retry',
                            ok: function () { return resolve(true); },
                            cancel: function () { return resolve(false); },
                        });
                    })];
            });
        });
    };
    // 刷新地块的边框线
    ViewHelper.prototype.updateCellBorderLines = function (node, lines, color) {
        node.Items(lines, function (it, line) {
            var conf = Constant_1.BORDER_LINE_CONF[line];
            it.setContentSize(conf.size);
            it.setPosition(conf.pos);
            it.Color(color);
        });
    };
    // 绘制网格
    ViewHelper.prototype.drawGrid = function (g, size, start) {
        g.clear();
        var pos = cc.v2();
        var w = size.x * Constant_1.TILE_SIZE, h = size.y * Constant_1.TILE_SIZE;
        for (var i = 0; i <= size.x; i++) {
            pos.set(start).x += i;
            MapHelper_1.mapHelper.getPixelByPoint(pos, pos).subSelf(Constant_1.TILE_SIZE_HALF);
            g.moveTo(pos.x, pos.y);
            g.lineTo(pos.x, pos.y + h);
        }
        for (var i = 0; i <= size.y; i++) {
            pos.set(start).y += i;
            MapHelper_1.mapHelper.getPixelByPoint(pos, pos).subSelf(Constant_1.TILE_SIZE_HALF);
            g.moveTo(pos.x, pos.y);
            g.lineTo(pos.x + w, pos.y);
        }
        g.stroke();
    };
    // 显示建筑的基础信息
    ViewHelper.prototype.updateBuildBaseUI = function (node, data, key) {
        this.updateBuildBaseInfo(node.Child('top'), data, key);
        this.updateBuildAttrInfo(node, data);
    };
    // 基础上信息
    ViewHelper.prototype.updateBuildBaseInfo = function (node, data, key) {
        ResHelper_1.resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key);
        node.Child('icon/name').setLocaleKey(data.name);
        node.Child('icon/lv').setLocaleKey('ui.lv', data.lv);
        var desc = node.Child('desc') || node.Child('info/desc');
        desc.setLocaleKey(data.desc);
    };
    ViewHelper.prototype._updateBuildBaseInfo = function (node, data, key) {
        ResHelper_1.resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key);
        node.Child('icon/name').setLocaleKey(data.name);
        node.Child('icon/lv/val').setLocaleKey('ui.lv', data.lv);
        var descLbl = node.Child('desc', cc.Label);
        descLbl.setLocaleKey(data.desc);
        descLbl._forceUpdateRenderData();
        node.height = Math.max(220, descLbl.node.height + 168);
    };
    ViewHelper.prototype._updateBuildAttrInfo = function (data, attr, bottom, attrs, key) {
        var _a;
        var top = attr.Child('top');
        // 显示下级信息和升级费用
        var isMaxLv = data.isMaxLv();
        top.Child('curr').setLocaleKey('ui.lv', data.lv);
        var nextLv = data.tempNextLv || ((_a = data.nextLvInfo) === null || _a === void 0 ? void 0 : _a.lv);
        if (nextLv) {
            top.Child('next').Color('#625450').setLocaleKey('ui.lv', nextLv);
        }
        else {
            top.Child('next').Color('#B6A591').setLocaleKey('ui.maxlv1');
        }
        attr.Child('items').Items(attrs, function (it, data, i) {
            var curr = data.curr, nextVal = data.nextVal;
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey(curr.key, curr.params);
            if (it.Child('next').active = !!nextVal) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = nextVal;
            }
            it.Child('line').active = i < attrs.length - 1;
        });
        // 刷新费用和按钮
        if (bottom.active = !isMaxLv) {
            this.updateBuildBottomInfo(data, bottom);
        }
    };
    ViewHelper.prototype.updateBuildBottomInfo = function (data, bottom) {
        var needMainLv = data.id !== Constant_1.BUILD_MAIN_NID && data.lv >= GameHelper_1.gameHpr.player.getMainBuildLv(); // 只要不是主城 就不能比主城等级高
        var params = needMainLv ? [data.lv + 1] : [];
        var condText = data.id === Constant_1.BUILD_MAIN_NID ? GameHelper_1.gameHpr.checkCellCountCond(data.attrJson.prep_cond) : GameHelper_1.gameHpr.checkUnlcokBuildCond(data.attrJson.prep_cond) || (needMainLv ? 'ui.need_main_lv' : '');
        if (!condText) {
            bottom.Child('title/val').setLocaleKey('ui.up_cost');
            bottom.Child('cond').active = false;
            var need = bottom.Child('need');
            need.active = true;
            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time);
        }
        else {
            bottom.Child('title/val').setLocaleKey('ui.up_cond');
            bottom.Child('need').active = false;
            var cond = bottom.Child('cond');
            cond.active = true;
            cond.Child('val').setLocaleKey(condText, params);
        }
        this.updateBuildButtons(bottom.Child('buttons'), data, condText);
    };
    // 显示建筑的属性信息
    ViewHelper.prototype.updateBuildAttrInfo = function (node, data) {
        var _a;
        var attr = node.Child('attrs/attr');
        var dtype = data.id === Constant_1.BUILD_PLANT_NID ? 501 : (_a = data.effect) === null || _a === void 0 ? void 0 : _a.getDescType();
        var showAttr = attr.active = !!dtype && (data.id !== Constant_1.BUILD_EMBASSY_NID || GameHelper_1.gameHpr.alliance.isMeCreater());
        // 显示下级信息和升级费用
        var isMaxLv = data.isMaxLv(), nextInfo = data.nextLvInfo;
        var top = attr.Child('top'), need = node.Child('need');
        top.active = need.active = !isMaxLv;
        attr.Child('items').Items(1, function (it, _data) {
            var _a, _b;
            if (showAttr) {
                it.Child('cur/val').setLocaleKey('ui.build_eff_desc_' + dtype, ((_a = data.effect) === null || _a === void 0 ? void 0 : _a.getValueText()) || '');
            }
            var nextLbl = it.Child('next', cc.Label);
            if (it.Child('arrow').active = nextLbl.setActive(!isMaxLv)) {
                nextLbl.setLocaleKey(((_b = nextInfo.effect) === null || _b === void 0 ? void 0 : _b.getValueText()) || '');
            }
        });
        if (!isMaxLv) {
            top.Child('cur').setLocaleKey('ui.lv', data.lv);
            top.Child('next').setLocaleKey('ui.lv', data.nextLvInfo.lv);
            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time);
        }
        this.updateBuildButtons(need.Child('buttons'), data);
    };
    // 刷新按钮
    ViewHelper.prototype.updateBuildButtons = function (buttonsNode, data, condText) {
        if (data.isMaxLv()) {
            return buttonsNode.Swih('');
        }
        buttonsNode.opacity = !!condText ? 120 : 255;
        var player = GameHelper_1.gameHpr.player;
        var bt = player.getBuildBtInfo(data.uid);
        if (bt) { //是否在队列中
            buttonsNode.Swih('uping')[0].Child('val').setLocaleKey(bt.isRuning() ? 'ui.uping' : 'ui.queueing');
        }
        else {
            buttonsNode.Swih('up_be');
        }
    };
    ViewHelper.prototype.updateCostViewForBuild = function (node, ctypes, time, cd) {
        // const up = node.Child('time/guide_up')
        if (GameHelper_1.gameHpr.isNoviceMode) {
            time /= NoviceConfig_1.NOVICE_BUILD_SPEED_MUL;
            time = Math.max(3, Math.floor(time * (1 - (cd || 0))));
            cd = 0;
        }
        this.updateCostView(node, ctypes, time, cd);
        // if (up?.getActive()) {
        //     node.Child('time/val', cc.Label).Color('#49983C')
        // }
    };
    // 刷新费用
    ViewHelper.prototype.updateCostView = function (node, ctypes, time, cd) {
        var _this = this;
        var _a;
        node.Child('cost').Items(ctypes || [], function (it, cost) { return _this.updateCostViewOne(it, cost, true); });
        if ((_a = node.Child('time')) === null || _a === void 0 ? void 0 : _a.setActive(!!time)) {
            cd = cd || 0;
            var up = node.Child('time/up', cc.Label);
            if (up === null || up === void 0 ? void 0 : up.setActive(!!cd)) {
                up.string = "(-" + Math.floor(cd * 100) + "%)";
            }
            if (cd) {
                time = Math.max(3, Math.floor(time * (1 - cd)));
            }
            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss');
        }
    };
    // 刷新费用
    ViewHelper.prototype.updateFreeCostView = function (node, ctypes, time, cd, isFree, policyFreeCount) {
        var _this = this;
        var _a;
        node.Child('cost').Items(ctypes || [], function (it, cost) { return _this.updateFreeCostViewOne(it, cost, !(isFree || policyFreeCount > 0)); });
        if ((_a = node.Child('time')) === null || _a === void 0 ? void 0 : _a.setActive(!!time)) {
            var up = node.Child('time/up');
            if (isFree || policyFreeCount > 0) {
                cd = 1;
                var node_1 = up.Swih('free')[0], bothFree = isFree && policyFreeCount > 0;
                node_1.Child('val', cc.Label).string = bothFree ? "x(" + policyFreeCount : policyFreeCount > 0 ? "x" + policyFreeCount : '';
                node_1.Child('add', cc.Label).string = bothFree ? '+1' : isFree ? 'x1' : '';
                node_1.Child('xx').active = bothFree;
            }
            else if (!!cd) {
                up.Swih('val')[0].Component(cc.Label).string = "(-" + Math.floor(cd * 100) + "%)";
            }
            else {
                up.Swih('');
            }
            if (cd) {
                time = Math.max(3, Math.floor(time * (1 - cd)));
            }
            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss');
            node.Child('cost').children.forEach(function (m) { var _a; return m.opacity = (isFree || policyFreeCount > 0) && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.type) !== Enums_1.CType.FIXATOR ? 100 : 255; });
            node.Child('time/up/val').opacity = node.Child('time/val').opacity = node.Child('time/icon').opacity = (isFree || policyFreeCount > 0) ? 100 : 255;
        }
    };
    // 刷新单个费用
    ViewHelper.prototype.updateCostViewOne = function (it, cost, isCheck) {
        if (it && cost) {
            it.Data = cost;
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost.type);
            if (!isCheck) {
                it.Child('val', cc.Label).string = cost.count + '';
            }
            else {
                it.Child('val', cc.Label).Color(GameHelper_1.gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + '';
            }
        }
    };
    // 刷新单个费用
    ViewHelper.prototype.updateFreeCostViewOne = function (it, cost, isCheck) {
        if (it && cost) {
            it.Data = cost;
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost.type);
            it.Child('val', cc.Label).Color(!isCheck || GameHelper_1.gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + '';
        }
    };
    // 更新费用
    ViewHelper.prototype.updateCostText = function (it, json) {
        if (json.gold > 0) {
            it.Child('gold/icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.GOLD);
            it.Child('gold/val', cc.Label).string = json.gold + '';
        }
        else if (json.ingot > 0) {
            it.Child('gold/icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.INGOT);
            it.Child('gold/val', cc.Label).string = json.ingot + '';
        }
        else {
            it.Child('gold').active = false;
        }
    };
    // 显示位置
    ViewHelper.prototype.updatePositionView = function (it, index, hasName) {
        if (hasName === void 0) { hasName = true; }
        it.Data = index;
        var posLbl = it.Component(cc.Label);
        if (hasName) {
            posLbl.setLocaleKey('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(index), MapHelper_1.mapHelper.indexToPoint(index).Join());
        }
        else {
            posLbl.string = '(' + MapHelper_1.mapHelper.indexToPoint(index).Join() + ')';
        }
        posLbl._forceUpdateRenderData();
        it.Child('line').width = posLbl.node.width;
    };
    // 刷新道具 根据类型
    ViewHelper.prototype.updateItemByCTypes = function (node, items, key) {
        var _this = this;
        node === null || node === void 0 ? void 0 : node.Items(items, function (it, data) { return _this.updateItemByCTypeOne(it, data, key); });
    };
    ViewHelper.prototype.updateItemByCTypeOne = function (it, item, key, adaptSize) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
        if (!item || !it) {
            return;
        }
        var iconSpr = it.Child('icon', cc.Sprite), countLbl = it.Child('count', cc.Label);
        if (iconSpr) {
            iconSpr.node.removeAllChildren();
        }
        if (item.type === Enums_1.CType.BUILD_LV) { //建筑等级
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadBuildIcon('build_' + item.id, iconSpr, key || mc.currWindName);
            (_a = it.Child('text')) === null || _a === void 0 ? void 0 : _a.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count);
        }
        else if (item.type === Enums_1.CType.HEAD_ICON) { //头像
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPlayerHead(iconSpr, ((_b = assetsMgr.getJsonData('headIcon', item.id)) === null || _b === void 0 ? void 0 : _b.icon) || '', key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_c = it.Child('text')) === null || _c === void 0 ? void 0 : _c.setLocaleKey('ui.headicon_title');
        }
        else if (item.type === Enums_1.CType.CHAT_EMOJI) { //聊天表情
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadEmojiIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_d = it.Child('text')) === null || _d === void 0 ? void 0 : _d.setLocaleKey('ui.chat_emoji_title');
        }
        else if (item.type === Enums_1.CType.TREASURE) { //宝箱
            var lv = ((_e = assetsMgr.getJsonData('treasure', item.id)) === null || _e === void 0 ? void 0 : _e.lv) || 1;
            (_f = it.Swih('text')[0]) === null || _f === void 0 ? void 0 : _f.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count);
        }
        else if (item.type === Enums_1.CType.TITLE) { //称号
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
            (_g = it.Child('text')) === null || _g === void 0 ? void 0 : _g.setLocaleKey('titleText.' + item.id);
        }
        else if (item.type === Enums_1.CType.WIN_POINT) { //胜点
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
            (_h = it.Child('text')) === null || _h === void 0 ? void 0 : _h.setLocaleKey('ui.rank_score_num_2', item.count);
        }
        else if (item.type === Enums_1.CType.PAWN) { //士兵
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_j = it.Child('text')) === null || _j === void 0 ? void 0 : _j.setLocaleKey('pawnText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.EQUIP) { //装备
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadEquipIcon(item.id, iconSpr, key || mc.currWindName);
            (_k = it.Child('text')) === null || _k === void 0 ? void 0 : _k.setLocaleKey('equipText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.POLICY) { //政策
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPolicyIcon(item.id, iconSpr, key || mc.currWindName);
            (_l = it.Child('text')) === null || _l === void 0 ? void 0 : _l.setLocaleKey('policyText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.PAWN_SKIN) { //皮肤
            var textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin');
            if (textNode) { //纯文本 '限定皮肤'
                it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc');
                textNode.off('click');
                textNode.on('click', function () { return _this.showPnl('common/ItemBox', item); });
            }
            else if (skinNode) {
                it.Swih('pawn_skin');
                ResHelper_1.resHelper.loadPawnHeadIcon(item.id, skinNode, key || mc.currWindName);
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                iconSpr && ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
                (_m = it.Child('text')) === null || _m === void 0 ? void 0 : _m.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000));
            }
        }
        else if (item.type === Enums_1.CType.HERO_DEBRIS) { //英雄残卷
            if (it.Child('text_click')) {
                (_o = it.Swih('text')[0]) === null || _o === void 0 ? void 0 : _o.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id));
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName);
                (_p = it.Child('text')) === null || _p === void 0 ? void 0 : _p.setLocaleKey('portrayalText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.HERO_OPT) { //自选英雄包
            if (it.Child('text_click')) {
                (_q = it.Swih('text')[0]) === null || _q === void 0 ? void 0 : _q.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id);
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type] + '_' + item.id, iconSpr, key || mc.currWindName);
                (_r = it.Child('text')) === null || _r === void 0 ? void 0 : _r.setLocaleKey('ui.hero_opt_gift_short_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.COMPLETE_GUIDE) { //完成新手引导
            (_s = it.Swih('text')[0]) === null || _s === void 0 ? void 0 : _s.setLocaleKey('guideText.guide_task_complete');
        }
        else if (item.type === Enums_1.CType.CITY_SKIN) { //城市皮肤
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadCityIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_t = it.Child('text')) === null || _t === void 0 ? void 0 : _t.setLocaleKey('ui.title_main_city_skin');
        }
        else {
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'count'; });
            if (iconSpr) {
                var sf = ResHelper_1.resHelper.getResIcon(item.type);
                if (sf) {
                    iconSpr.spriteFrame = sf;
                }
                else {
                    ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
                }
            }
            if (countLbl) {
                countLbl.string = '' + item.count;
            }
        }
    };
    ViewHelper.prototype.updateItemNameByCTypeOne = function (it, item, isFormat) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8;
        if (isFormat === void 0) { isFormat = false; }
        if (!item || !it) {
            return;
        }
        var countLbl = it.Child('count', cc.Label);
        if (item.type === Enums_1.CType.BUILD_LV) { //建筑等级
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_a = it.Child('text')) === null || _a === void 0 ? void 0 : _a.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count);
            }
            else {
                (_b = it.Child('text')) === null || _b === void 0 ? void 0 : _b.setLocaleKey('ui.build_lv', ut.nameFormator(assetsMgr.lang('buildText.name_' + item.id), 5), item.count);
            }
        }
        else if (item.type === Enums_1.CType.HEAD_ICON) { //头像
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_c = it.Child('text')) === null || _c === void 0 ? void 0 : _c.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.headicon_title'), 5));
            }
            else {
                (_d = it.Child('text')) === null || _d === void 0 ? void 0 : _d.setLocaleKey('ui.headicon_title');
            }
        }
        else if (item.type === Enums_1.CType.CHAT_EMOJI) { //聊天表情
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_e = it.Child('text')) === null || _e === void 0 ? void 0 : _e.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.chat_emoji_title'), 5));
            }
            else {
                (_f = it.Child('text')) === null || _f === void 0 ? void 0 : _f.setLocaleKey('ui.chat_emoji_title');
            }
        }
        else if (item.type === Enums_1.CType.TREASURE) { //宝箱
            var lv = ((_g = assetsMgr.getJsonData('treasure', item.id)) === null || _g === void 0 ? void 0 : _g.lv) || 1;
            if (isFormat) {
                (_h = it.Swih('text')[0]) === null || _h === void 0 ? void 0 : _h.setLocaleKey('ui.treasure_reward_desc', ut.nameFormator(assetsMgr.lang('ui.treasure_name_' + lv), 5), item.count);
            }
            else {
                (_j = it.Swih('text')[0]) === null || _j === void 0 ? void 0 : _j.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count);
            }
        }
        else if (item.type === Enums_1.CType.TITLE) { //称号
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_k = it.Child('text')) === null || _k === void 0 ? void 0 : _k.setLocaleKey(ut.nameFormator(assetsMgr.lang('titleText.' + item.id), 5));
            }
            else {
                (_l = it.Child('text')) === null || _l === void 0 ? void 0 : _l.setLocaleKey('titleText.' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.WIN_POINT) { //胜点
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_m = it.Child('text')) === null || _m === void 0 ? void 0 : _m.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.rank_score_num_2'), 5), item.count);
            }
            else {
                (_o = it.Child('text')) === null || _o === void 0 ? void 0 : _o.setLocaleKey('ui.rank_score_num_2', item.count);
            }
        }
        else if (item.type === Enums_1.CType.PAWN) { //士兵
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_p = it.Child('text')) === null || _p === void 0 ? void 0 : _p.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + item.id), 5));
            }
            else {
                (_q = it.Child('text')) === null || _q === void 0 ? void 0 : _q.setLocaleKey('pawnText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.EQUIP) { //装备
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_r = it.Child('text')) === null || _r === void 0 ? void 0 : _r.setLocaleKey(ut.nameFormator(assetsMgr.lang('equipText.name_' + item.id), 5));
            }
            else {
                (_s = it.Child('text')) === null || _s === void 0 ? void 0 : _s.setLocaleKey('equipText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.POLICY) { //政策
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_t = it.Child('text')) === null || _t === void 0 ? void 0 : _t.setLocaleKey(ut.nameFormator(assetsMgr.lang('policyText.name_' + item.id), 5));
            }
            else {
                (_u = it.Child('text')) === null || _u === void 0 ? void 0 : _u.setLocaleKey('policyText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.PAWN_SKIN) { //皮肤
            var textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin');
            if (textNode) { //纯文本 '限定皮肤'
                if (isFormat) {
                    it.Swih('text_click')[0].setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.limited_skin_reward_desc'), 5));
                }
                else {
                    it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc');
                }
                textNode.off('click');
                textNode.on('click', function () { return _this.showPnl('common/ItemBox', item); });
            }
            else if (skinNode) {
                it.Swih('pawn_skin');
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_v = it.Child('text')) === null || _v === void 0 ? void 0 : _v.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + Math.floor(item.id / 1000)), 5));
                }
                else {
                    (_w = it.Child('text')) === null || _w === void 0 ? void 0 : _w.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000));
                }
            }
        }
        else if (item.type === Enums_1.CType.HERO_DEBRIS) { //英雄残卷
            if (it.Child('text_click')) {
                if (isFormat) {
                    (_x = it.Swih('text')[0]) === null || _x === void 0 ? void 0 : _x.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id), 5));
                }
                else {
                    (_y = it.Swih('text')[0]) === null || _y === void 0 ? void 0 : _y.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id));
                }
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_z = it.Child('text')) === null || _z === void 0 ? void 0 : _z.setLocaleKey(ut.nameFormator(assetsMgr.lang('portrayalText.name_' + item.id), 5));
                }
                else {
                    (_0 = it.Child('text')) === null || _0 === void 0 ? void 0 : _0.setLocaleKey('portrayalText.name_' + item.id);
                }
            }
        }
        else if (item.type === Enums_1.CType.HERO_OPT) { //自选英雄包
            if (it.Child('text_click')) {
                if (isFormat) {
                    (_1 = it.Swih('text')[0]) === null || _1 === void 0 ? void 0 : _1.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_' + item.id), 5));
                }
                else {
                    (_2 = it.Swih('text')[0]) === null || _2 === void 0 ? void 0 : _2.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id);
                }
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_3 = it.Child('text')) === null || _3 === void 0 ? void 0 : _3.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_short_' + item.id), 5));
                }
                else {
                    (_4 = it.Child('text')) === null || _4 === void 0 ? void 0 : _4.setLocaleKey('ui.hero_opt_gift_short_' + item.id);
                }
            }
        }
        else if (item.type === Enums_1.CType.COMPLETE_GUIDE) { //完成新手引导
            if (isFormat) {
                (_5 = it.Swih('text')[0]) === null || _5 === void 0 ? void 0 : _5.setLocaleKey(ut.nameFormator(assetsMgr.lang('guideText.guide_task_complete'), 5));
            }
            else {
                (_6 = it.Swih('text')[0]) === null || _6 === void 0 ? void 0 : _6.setLocaleKey('guideText.guide_task_complete');
            }
        }
        else if (item.type === Enums_1.CType.CITY_SKIN) { //城市皮肤
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_7 = it.Child('text')) === null || _7 === void 0 ? void 0 : _7.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.title_main_city_skin'), 5));
            }
            else {
                (_8 = it.Child('text')) === null || _8 === void 0 ? void 0 : _8.setLocaleKey('ui.title_main_city_skin');
            }
        }
        else {
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'count'; });
            if (countLbl) {
                countLbl.string = '' + item.count;
            }
        }
    };
    // 刷新装备显示
    ViewHelper.prototype.updateEquipView = function (node, equip, key, lockEffect, smeltEffects, showRange) {
        var nameNode = node.Child('name') || node.Child('icon/name');
        nameNode.setLocaleKey(equip.name);
        var meltNode = node.Child('melt') || node.Child('icon/melt');
        if (meltNode === null || meltNode === void 0 ? void 0 : meltNode.setActive(equip.isSmelt())) {
            for (var i = 0; i < 2; i++) {
                var it = meltNode.Child(i), data = equip.smeltEffects[i];
                if (it.active = !!data) {
                    ResHelper_1.resHelper.loadEquipIcon(data.id, it, key);
                }
            }
        }
        var noForge = node.Child('no_forge') || node.Child('icon/no_forge');
        noForge === null || noForge === void 0 ? void 0 : noForge.setActive(false);
        var exclusive = node.Child('exclusive');
        if (exclusive === null || exclusive === void 0 ? void 0 : exclusive.setActive(!!equip.exclusive_pawn)) {
            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + equip.exclusive_pawn);
        }
        this.updateEquipAttrView(node.Child('attrs'), equip, lockEffect, smeltEffects, showRange);
    };
    ViewHelper.prototype.updateEquipAttrView = function (node, equip, lockEffect, smeltEffects, showRange) {
        var _a, _b;
        var attrNode = node.Child('attr'), effectNode = node.Child('effects'), skillIntensifyNode = node.Child('skill_intensify');
        (_a = node.Child('more_desc')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        (_b = node.Child('more_effects')) === null || _b === void 0 ? void 0 : _b.setActive(false);
        // 属性
        var serverRunDay = GameHelper_1.gameHpr.getServerRunDay();
        attrNode.Items(equip.mainAttrs, function (it, data) {
            var _a, _b;
            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
            var value = data.value, canShowRange = showRange && data.base.length > 0;
            if (!!data.todayAdd) {
                value += serverRunDay * data.todayAdd;
            }
            it.Child('val', cc.Label).string = '+' + value;
            // 额外添加的
            var add = value - data.initValue;
            if ((_a = it.Child('add')) === null || _a === void 0 ? void 0 : _a.setActive(!!add)) {
                it.Child('add/0', cc.Label).string = '(' + data.initValue;
                var addLbl = it.Child('add/1', cc.Label);
                if (addLbl.setActive(!canShowRange)) {
                    addLbl.string = '+' + add;
                }
            }
            // 随机范围
            if ((_b = it.Child('base')) === null || _b === void 0 ? void 0 : _b.setActive(canShowRange)) {
                data.base.forEach(function (v, i) { return it.Child('base/' + i, cc.Label).string = v + ''; });
            }
        });
        // 效果
        var effectCount = equip.effects.length;
        if (effectNode.active = effectCount > 0) {
            var mult_1 = effectCount > 1;
            var effects = equip.effects, smeltEffectMap_1 = null, effectIdMap_1 = null;
            // 如果有锁定和融炼 排个序
            if (lockEffect || smeltEffects) {
                smeltEffectMap_1 = {};
                smeltEffects.forEach(function (m) { return smeltEffectMap_1[m.type] = m.id; });
                effects = effects.slice().sort(function (a, b) {
                    var aw = smeltEffectMap_1[a.type] ? 1 : 0, bw = smeltEffectMap_1[b.type] ? 1 : 0;
                    aw = aw * 10 + Number(a.type === lockEffect);
                    bw = bw * 10 + Number(b.type === lockEffect);
                    return aw - bw;
                });
            }
            if (smeltEffects && equip.isExclusive()) {
                effectIdMap_1 = {};
                GameHelper_1.gameHpr.world.getExclusiveEquipEffects(equip.id).forEach(function (m) { return effectIdMap_1[m] = true; });
            }
            effectNode.Items(effects, function (it, data) {
                var descParams = data.getDescParams(showRange), smeltId = smeltEffectMap_1 === null || smeltEffectMap_1 === void 0 ? void 0 : smeltEffectMap_1[data.type];
                if (smeltId) { // 融炼词条
                    it.Color('#C2B3A1');
                    it.setLocaleKey('ui.yet_smelt_equip_' + Number(!!effectIdMap_1[data.type]), assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)).replace(/#000001/g, '#C2B3A1'), 'equipText.name_' + smeltId);
                }
                else if (data.type === lockEffect) { // 锁定词条
                    it.Color('#C2B3A1');
                    it.setLocaleKey('ui.yet_lock_equip', assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)).replace(/#000001/g, '#C2B3A1'));
                }
                else if (mult_1) { // 正常词条
                    it.Color('#756963');
                    it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)));
                }
                else {
                    it.Color('#756963');
                    it.setLocaleKey.apply(it, __spread([data.name], descParams));
                }
            });
        }
        // 技能强化效果
        if (skillIntensifyNode === null || skillIntensifyNode === void 0 ? void 0 : skillIntensifyNode.setActive(!!equip.skillIntensify && equip.skillIntensify.length > 0)) {
            skillIntensifyNode.setLocaleKey("pawnSkillText.intensify_desc_" + equip.skillIntensify[0] + "_" + equip.skillIntensify[1]);
        }
    };
    // 刷新装备基础信息显示
    ViewHelper.prototype.updateEquipBaseView = function (node, json) {
        var _this = this;
        var _a;
        var nameNode = node.Child('name') || node.Child('icon/name');
        nameNode.setLocaleKey('equipText.name_' + json.id);
        var exclusive = node.Child('exclusive') || node.Child('icon/exclusive');
        if (exclusive === null || exclusive === void 0 ? void 0 : exclusive.setActive(!!json.exclusive_pawn)) {
            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + json.exclusive_pawn);
        }
        var noForge = node.Child('no_forge') || node.Child('icon/no_forge');
        noForge === null || noForge === void 0 ? void 0 : noForge.setActive(true);
        // 属性
        var attrNode = node.Child('attrs/attr'), attrs = this.getEquipAttrs(json);
        attrNode.Items(attrs, function (it, data) {
            var _a, _b;
            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
            it.Child('val', cc.Label).string = _this.wrapEquipVal(data.val);
            (_a = it.Child('add')) === null || _a === void 0 ? void 0 : _a.setActive(false);
            (_b = it.Child('base')) === null || _b === void 0 ? void 0 : _b.setActive(false);
        });
        // 效果
        var effectIds = ut.stringToNumbers(json.effect, '|');
        if (!!json.exclusive_pawn) {
            effectIds = GameHelper_1.gameHpr.world.getExclusiveEquipEffects(json.id);
        }
        var effects = effectIds.map(function (m) { return assetsMgr.getJsonData('equipEffect', m); }).sort(function (a, b) { return a.sort - b.sort; });
        var effectsNode = node.Child('attrs/effects'), moreDescNode = node.Child('attrs/more_desc'), moreEffectsNode = node.Child('attrs/more_effects'), skillIntensifyNode = node.Child('attrs/skill_intensify');
        // 多个的处理
        moreDescNode.active = moreEffectsNode.active = effects.length > 1;
        if (moreEffectsNode.active) {
            moreDescNode.setLocaleKey('ui.equip_more_effect_desc', json.effect_count);
            moreEffectsNode.Items(effects, function (it, m) { return _this.updateEquipBaseEffect(it, m, true); });
        }
        // 单个的处理
        if (effectsNode.active = !!effects.length && !moreEffectsNode.active) {
            effectsNode.Items(effects, function (it, m) { return _this.updateEquipBaseEffect(it, m, false); });
        }
        // 技能强化效果
        if (skillIntensifyNode.active = !!json.skill_intensify) {
            var _b = __read(ut.stringToNumbers(json.skill_intensify, ','), 2), a = _b[0], b = _b[1];
            skillIntensifyNode.setLocaleKey("pawnSkillText.intensify_desc_" + a + "_" + b);
        }
        // 空属性
        (_a = node.Child('attrs/empty_effect')) === null || _a === void 0 ? void 0 : _a.setActive(!effects.length);
    };
    ViewHelper.prototype.wrapEquipVal = function (arr) {
        return arr[0] === arr[1] ? (arr[0] + '') : "[" + arr.join('-') + "]";
    };
    ViewHelper.prototype.getEquipAttrs = function (data) {
        var arr = [];
        if (data.hp) {
            arr.push({ type: 1, val: ut.stringToNumbers(data.hp, ',') });
        }
        if (data.attack) {
            arr.push({ type: 2, val: ut.stringToNumbers(data.attack, ',') });
        }
        return arr;
    };
    ViewHelper.prototype.updateEquipBaseEffect = function (it, json, isMore) {
        var params = [], _a = __read(json.value.split(','), 2), a = _a[0], b = _a[1];
        if (json.id === Enums_1.EquipEffectType.MINGGUANG_ARMOR || json.id === Enums_1.EquipEffectType.BAIBI_SWORD) {
            params.push('0');
        }
        else if (!json.value) {
            params.push('');
        }
        else if (a === b) {
            params.push("" + a + json.suffix);
        }
        else {
            params.push("[" + a + "-" + b + "]" + json.suffix);
        }
        if (json.odds) {
            params.push("[" + ut.stringToNumbers(json.odds, ',').join('-') + "]%");
        }
        it.Color('#756963');
        if (isMore) {
            it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang('equipText.effect_' + json.id, params));
        }
        else {
            it.setLocaleKey('equipText.effect_' + json.id, params);
        }
    };
    // 刷新宠物信息
    ViewHelper.prototype.updatePetView = function (it, id, lv) {
        it.Child('name').setLocaleKey('pawnText.name_' + id);
        var lvNode = it.Child('name/lv');
        if (lvNode.active = !!lv) {
            lvNode.setLocaleKey('ui.lv', lv);
        }
        it.Child('none').active = !lv;
        this.updatePetAttrView(it.Child('attrs'), id, lv);
    };
    ViewHelper.prototype.updatePetAttrView = function (node, id, lv) {
        if (node.active = !!lv) {
            var attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + lv);
            var mainAttrs = [{ type: 1, value: attrJson.hp }, { type: 2, value: attrJson.attack }];
            // 属性
            node.Child('attr').Items(mainAttrs, function (it, data) {
                it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
                it.Child('val', cc.Label).string = '' + data.value;
            });
            // 技能
            var skillNode = node.Child('skill');
            if (skillNode.active = !!attrJson.skill) {
                skillNode.Items(ut.stringToNumbers(attrJson.skill), function (node, data) {
                    var json = assetsMgr.getJsonData('pawnSkill', data);
                    var text = assetsMgr.lang(json.desc, json.desc_params.split('|'));
                    node.setLocaleKey('ui.res_transit_cap_desc', "<color=#333333>" + assetsMgr.lang(json.name) + "</c>", text);
                });
            }
        }
    };
    // 刷新玩家简介
    ViewHelper.prototype.updatePlayerPersonalDesc = function (node, uid, plr) {
        var personalDescLbl = node.Component(cc.Label);
        personalDescLbl.Color('#B6A591').string = '...';
        GameHelper_1.gameHpr.getUserPersonalDesc(uid, plr).then(function (val) {
            if (personalDescLbl.isValid) {
                personalDescLbl.Color(val ? '#756963' : '#B6A591').string = val || assetsMgr.lang('ui.empty_personal_desc');
            }
        });
    };
    // 刷新称号显示
    ViewHelper.prototype.updatePlayerTitleText = function (node, uid, plr) {
        if (!node) {
            return;
        }
        GameHelper_1.gameHpr.getUserTitle(uid, plr).then(function (title) {
            if (node.isValid) {
                var json = assetsMgr.getJsonData('title', title);
                node.Color((json === null || json === void 0 ? void 0 : json.color) || '#756963').setLocaleKey(json ? 'titleText.' + json.id : 'ui.nought');
            }
        });
    };
    // 刷新人气显示
    ViewHelper.prototype.updatePlayerPopularity = function (root, buttonNode, uid, key, plr) {
        var _a, _b;
        var button = (_b = (_a = buttonNode.Swih(uid === GameHelper_1.gameHpr.getUid() ? 'popularity_record_be' : 'add_popularity_be')) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.Component(cc.Button);
        if (button) {
            button.node.opacity = 120;
            button.interactable = false;
        }
        root.Items(1, function (it, _) {
            ResHelper_1.resHelper.loadGiftIcon(101, it.Child('icon'), key);
            it.Child('count').active = false;
            it.Child('loading').active = true;
        });
        GameHelper_1.gameHpr.getUserPopularity(uid, plr).then(function (info) {
            var _a;
            if (!root.isValid) {
                return;
            }
            else if (!((_a = info === null || info === void 0 ? void 0 : info.list) === null || _a === void 0 ? void 0 : _a.length)) {
                info = { list: [[101, 0]] };
            }
            if (button === null || button === void 0 ? void 0 : button.isValid) {
                button.node.opacity = 255;
                button.interactable = true;
            }
            root.Items(info.list, function (it, _a) {
                var _b = __read(_a, 2), id = _b[0], count = _b[1];
                it.Child('count').active = true;
                it.Child('loading').active = false;
                ResHelper_1.resHelper.loadGiftIcon(id, it.Child('icon'), key);
                it.Child('count', cc.Label).string = count + '';
            });
        });
    };
    // 刷新段位
    ViewHelper.prototype.updatePlayerRankInfo = function (node, uid, key, plr) {
        GameHelper_1.gameHpr.getUserRankScore(uid, plr).then(function (data) {
            if (node.isValid) {
                var _a = GameHelper_1.gameHpr.resolutionRankScore(data.score, data.count), id = _a.id, winPoint = _a.winPoint;
                var icon = node.Child('icon');
                icon.Swih(id >= 0 ? 'val' : 'none');
                id >= 0 && ResHelper_1.resHelper.loadRankScoreIcon(id, icon, key);
                node.Child('rank').setLocaleKey(id >= 0 ? 'ui.rank_name_' + id : 'ui.rank_name_none');
                node.Child('rank_val').setLocaleKey('ui.rank_score_num', winPoint);
                node.Child('ranked_val', cc.Label).string = data.count + '';
            }
        });
    };
    // 刷新总局数
    ViewHelper.prototype.updateTotalGameCount = function (node, uid, plr) {
        var valLbl = node.Child('val', cc.Label), winRate = node.Child('win_rate');
        valLbl.string = '-';
        winRate.active = false;
        GameHelper_1.gameHpr.getUserTotalGameCount(uid, plr).then(function (info) {
            if (node.isValid) {
                // winRate.active = true
                var _a = __read(info, 2), win = _a[0], total = _a[1];
                valLbl.string = '' + total;
                // winRate.setLocaleKey('ui.win_rate', total ? Math.floor(win / total * 100) : 0)
            }
        });
    };
    // 在主场景显示建筑信息
    ViewHelper.prototype.showBuildInfoByMain = function (id, params) {
        return __awaiter(this, void 0, void 0, function () {
            var ui, area, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ui = Constant_1.FIXATION_MENU_CONFIG[id];
                        if (!ui) {
                            return [2 /*return*/, false];
                        }
                        this.showNetWait(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.areaCenter.reqAreaByIndex(GameHelper_1.gameHpr.player.getMainCityIndex())];
                    case 1:
                        area = _a.sent();
                        this.showNetWait(false);
                        data = area === null || area === void 0 ? void 0 : area.getBuildsById(id)[0];
                        if (!data) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.showPnl(ui, data, params)];
                    case 2:
                        _a.sent();
                        return [2 /*return*/, true];
                    case 3: return [2 /*return*/, false];
                }
            });
        });
    };
    // 显示金币不足
    ViewHelper.prototype.showGoldNotEnough = function () {
        var _this = this;
        this.showMessageBox('ui.gold_not_enough_tip', {
            okText: 'ui.button_exchange',
            cancelText: 'ui.button_no',
            ok: function () { return _this.showPnl('common/ShopBuyGoldTip'); },
            cancel: function () { }
        });
    };
    // 初始化转盘界面
    ViewHelper.prototype.initWheelItem = function (it, data, descColor) {
        var items = [];
        if (data.factor > 0) {
            it.Data = data;
            var runDay = GameHelper_1.gameHpr.user.getWheelInRoomRunDay();
            if (runDay > 0) {
                var count = Math.floor(30 * Math.min(runDay, 10) * data.factor);
                items = count > 0 ? [new CTypeObj_1.default().init(Enums_1.CType.CEREAL, 0, count), new CTypeObj_1.default().init(Enums_1.CType.TIMBER, 0, count), new CTypeObj_1.default().init(Enums_1.CType.STONE, 0, count)] : [];
            }
        }
        else if (data.factor === -1) {
            it.Data = data;
            items = GameHelper_1.gameHpr.user.getWheelRandomAwards();
        }
        else {
            it.Data = null;
            items = GameHelper_1.gameHpr.stringToCTypes(data.award);
        }
        this.updateItemByCTypes(it.Child('award'), items);
        it.Child('loading').active = !!it.Data && !items.length;
        it.Child('desc').Color(descColor).setLocaleKey(items.length === 0 ? 'ui.empty' : '');
    };
    // 打开关闭弹出框
    ViewHelper.prototype.changePopupBoxList = function (node, val, isDown) {
        node.Child('select_mask_be').active = val;
        var mask = node.Child('mask'), root = mask.Child('root');
        if (val) {
            mask.active = true;
            root.y = isDown ? mask.height : -mask.height;
            var y = isDown ? -4 : 4;
            cc.tween(root).to(0.15, { y: y }, { easing: cc.easing.sineOut }).start();
        }
        else {
            root.y = isDown ? -4 : 4;
            var y = isDown ? mask.height : -mask.height;
            cc.tween(root).to(0.1, { y: y }).call(function () { return mask.active = false; }).start();
        }
        cc.tween(node.Child('icon')).to(0.15, { angle: val ? -180 : 0 }).start();
    };
    ViewHelper.prototype.closePopupBoxList = function (node) {
        node.Child('select_mask_be').active = false;
        node.Child('mask').active = false;
        node.Child('icon').angle = 0;
    };
    // 显示不再提示
    ViewHelper.prototype.showNoLongerTip = function (key, data) {
        if (!GameHelper_1.gameHpr.isNoLongerTip(key)) {
            data.noKey = key;
            data.okText = data.okText || 'ui.button_gotit';
            this.showPnl('common/NoLongerTip', data);
            return true;
        }
        return false;
    };
    // 显示不再提示
    ViewHelper.prototype.showResFullNoLongerTip = function (key, items, data) {
        if (!GameHelper_1.gameHpr.isNoLongerTipBySid(key)) {
            data.noKey = key;
            data.okText = data.okText || 'ui.button_gotit';
            this.showPnl('common/ResFullNoLongerTip', items, data);
            return true;
        }
        return false;
    };
    // 添加人气
    ViewHelper.prototype.addPlayerPopularity = function (data, cb) {
        var info = data.popularityInfo;
        if (info === null || info === void 0 ? void 0 : info.reqing) {
            return;
        }
        else if (info && info.records.length > 0) {
            var lastTime_1 = 0, d_1 = null, uid_1 = GameHelper_1.gameHpr.getUid();
            info.records.forEach(function (m) {
                if (m.time > lastTime_1) {
                    lastTime_1 = m.time;
                }
                if (m.uid === uid_1) {
                    d_1 = m;
                }
            });
            var now = Date.now();
            if (d_1 && now - d_1.time < Constant_1.ONE_USER_POPULARITY_CHANGE_INTERVAL) {
                var day = Math.max(1, Math.floor((Constant_1.ONE_USER_POPULARITY_CHANGE_INTERVAL - (now - d_1.time)) / ut.Time.Day));
                return this.showMessageBox('ui.month_add_popularity_tip', { params: [day], okText: 'ui.button_gotit' });
            }
            else if (lastTime_1 > ut.dateZeroTime(GameHelper_1.gameHpr.getServerNowTime())) {
                return this.showMessageBox('ui.today_add_popularity_tip', { okText: 'ui.button_gotit' });
            }
        }
        this.showPnl('common/AddPopularityTip', data, cb);
    };
    // 刷新图鉴心
    ViewHelper.prototype.updateBookStar = function (node, star) {
        var val = star * 0.5;
        node.children.forEach(function (m, i) {
            if (val >= 1) {
                val -= 1;
                m.Component(cc.MultiFrame).setFrame(2);
            }
            else if (val >= 0.5) {
                val -= 0.5;
                m.Component(cc.MultiFrame).setFrame(1);
            }
            else {
                m.Component(cc.MultiFrame).setFrame(0);
            }
        });
    };
    // 显示画像名字
    ViewHelper.prototype.showPortrayalName = function (node, name, vice) {
        node.Child('val').setLocaleKey(name);
        var viceNode = node.Child('vice');
        if (viceNode.active = !!vice) {
            viceNode.setLocaleKey('ui.bracket', vice);
        }
    };
    // 显示立绘
    ViewHelper.prototype.updatePicture = function (id, isUnlock, iconNode, offset, hasAnim, key) {
        var valNode = iconNode.Child('val');
        var anim = valNode.Component(FrameAnimationCmpt_1.default);
        anim === null || anim === void 0 ? void 0 : anim.clean();
        ResHelper_1.resHelper.loadPortrayalImage(id, valNode, key);
        iconNode.setPosition(offset);
        valNode.opacity = isUnlock ? 255 : 100;
        if (isUnlock) {
            iconNode.Component(cc.Sprite).spriteFrame = null;
        }
        else {
            ResHelper_1.resHelper.loadPortrayalImage(id, iconNode, key);
        }
        if (anim && isUnlock && hasAnim) {
            anim.init('portrayal_' + id, key).then(function () {
                if (valNode.isValid) {
                    anim.play('standby');
                }
            });
        }
    };
    // 刷新画像碎片数量
    ViewHelper.prototype.updatePortrayalDebrisCount = function (it, debris) {
        var isCanComp = debris >= Constant_1.PORTRAYAL_COMP_NEED_COUNT;
        it.Child('debris_count/val', cc.Label).Color(isCanComp ? cc.Color.GREEN : cc.Color.WHITE).string = debris + '';
        it.Child('debris_count').Color(isCanComp ? '#FFA647' : cc.Color.GRAY);
    };
    // 显示获得画像
    ViewHelper.prototype.showGainPortrayalDebris = function (id, count) {
        this.showPnl('common/GetPortrayal', id, count);
    };
    // 刷新英雄属性
    ViewHelper.prototype.updatePortrayalAttr = function (node, data, isHero) {
        var _this = this;
        var root = node.Child('attrs');
        //
        root.Child('avatar/val').setLocaleKey(data.avatarPawnName);
        // 属性
        root.Child('attr').Items(this.getPortrayalMainAttrs(data, isHero), function (it, d) {
            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1);
            it.Child('val', cc.Label).string = d.value;
        });
        // 技能
        var id = data.json.skill;
        root.Child('skill').setLocaleKey('ui.res_transit_cap_desc', "<color=#333333>" + assetsMgr.lang('portrayalSkillText.name_' + id) + "</c>", assetsMgr.lang('portrayalSkillText.desc_' + id, this.getPortrayalSkillDescParams(data, isHero)));
        // 韬略
        var strategysNode = root.Child('strategys');
        var showStrategy = root.Child('strategy').active = strategysNode.active = isHero && data.strategys.length > 0;
        if (showStrategy) {
            root.Child('strategy/name/count', cc.Label).string = "(" + data.strategys.length + ")";
            strategysNode.Items(data.strategys, function (it, strategy) { return _this.showStrategyText(it, strategy, data.avatarPawnName); });
        }
    };
    // 显示韬略文本
    ViewHelper.prototype.showStrategyText = function (it, strategy, avatarPawnName) {
        it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang(strategy.desc, strategy.getDescParamsRange(avatarPawnName, 1, '#' + it.color.toHEX('#rrggbb'))));
    };
    ViewHelper.prototype.getPortrayalMainAttrs = function (data, isHero) {
        if (isHero && data.mainAttrs.length > 0) {
            return data.mainAttrs.map(function (m) { return { type: m.type, value: '+' + m.value }; });
        }
        return ['hp', 'attack'].map(function (k, i) { return { type: i + 1, value: '[' + data.json[k].replace(',', '-') + ']' }; });
    };
    // 获取说明参数
    ViewHelper.prototype.getPortrayalSkillDescParams = function (data, isHero) {
        if (isHero && data.skill) {
            return data.skill.getDescParams();
        }
        var json = assetsMgr.getJsonData('portrayalSkill', data.json.skill);
        var arr = [];
        if (json.value) {
            arr.push('[' + json.value.replace(',', '-') + ']' + json.suffix);
        }
        if (json.target) {
            arr.push(json.target);
        }
        return arr;
    };
    // 刷新英雄简短属性
    ViewHelper.prototype.updatePortrayalShortAttr = function (node, attrs, avatarPawnName) {
        var mainAttrs = [], skill = null, strategys = [];
        attrs.forEach(function (m) {
            var _a = __read(m.attr, 3), fieldType = _a[0], type = _a[1], value = _a[2];
            if (fieldType === 0) { //属性
                mainAttrs.push({ type: type, value: value });
            }
            else if (fieldType === 1) { //技能
                skill = new PortrayalSkillObj_1.default().init(type, value);
            }
            else if (fieldType === 2) { //韬略
                strategys.push(new StrategyObj_1.default().init(type));
            }
        });
        // 属性
        node.Child('attr').Items(mainAttrs, function (it, d) {
            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1);
            it.Child('val', cc.Label).string = '+' + d.value;
        });
        // 技能
        var skillNode = node.Child('skill');
        if (skillNode.active = !!skill) {
            skillNode.setLocaleKey(skill.desc, skill.getDescParams());
        }
        // 韬略
        node.Child('strategy/name/count', cc.Label).string = "(" + strategys.length + ")";
        node.Child('strategys').Items(strategys, function (it, strategy) { return exports.viewHelper.showStrategyText(it, strategy, avatarPawnName); });
    };
    // 返回大厅
    ViewHelper.prototype.backLobby = function () {
        var _this = this;
        GameHelper_1.gameHpr.resetSelectServer(true).then(function (res) { return _this.gotoWind('lobby'); });
    };
    // 显示英雄自选
    ViewHelper.prototype.showHeroOptSelect = function (lv) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var arr = Constant_1.HERO_OPT_GIFT[lv];
                        var list = arr ? arr.map(function (m) { return new PortrayalInfo_1.default().init(m); }) : assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
                        _this.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.GIFT, list, function (arr) { var _a, _b; return resolve((_b = (_a = arr[0]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0); }, lv);
                    })];
            });
        });
    };
    // 获取状态背景颜色
    ViewHelper.prototype.getHeroStateBgColor = function (state) {
        if (state === 0) {
            return '#FFFFFF';
        }
        else if (state === 1) {
            return '#21DE29';
        }
        else if (state === 2) {
            return '#FFFFFF';
        }
        return '#F45757';
    };
    // 显示申请好友
    ViewHelper.prototype.showApplyFriend = function (data) {
        var _this = this;
        this.showMessageBox('ui.apply_friend_tip', {
            params: [ut.nameFormator(data.nickname, 8)],
            ok: function () { return GameHelper_1.gameHpr.friend.applyFriend(data.uid).then(function (err) {
                if (err) {
                    return _this.showAlert(err);
                }
                _this.showAlert('toast.apply_friend_succeed');
            }); },
            cancel: function () { }
        });
    };
    // 显示拉黑
    ViewHelper.prototype.showBlacklist = function (data, event, buttonsNode) {
        var _this = this;
        var state = GameHelper_1.gameHpr.friend.isInBlacklist(data.uid);
        this.showMessageBox(state ? 'ui.cancel_blacklist_desc' : 'ui.add_blacklist_desc', {
            params: [ut.nameFormator(data.nickname, 8)],
            ok: function () { return GameHelper_1.gameHpr.friend.doBlacklist(data.uid, state).then(function (err) {
                if (err) {
                    return _this.showAlert(err);
                }
                else if (buttonsNode.isValid) {
                    event.target.Child('val', cc.MultiFrame).setFrame(!state);
                    buttonsNode.Child('add_friend_be').active = state && !GameHelper_1.gameHpr.friend.isFriend(data.uid);
                }
                _this.showAlert(state ? 'toast.cancel_blacklist_succeed' : 'toast.add_blacklist_succeed');
            }); },
            cancel: function () { }
        });
    };
    ViewHelper.prototype.showFeedback = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isGLobal()) {
                    return [2 /*return*/, this.showPnl('login/HDFeedback')];
                }
                else {
                    return [2 /*return*/, this.showPnl('login/Feedback')];
                }
                return [2 /*return*/];
            });
        });
    };
    // 查看奖励详情
    ViewHelper.prototype.previewRewardDetail = function (reward) {
        if (reward.type === Enums_1.CType.PAWN_SKIN) {
            this.showPnl('common/ItemBox', reward);
        }
        else if (reward.type === Enums_1.CType.HERO_DEBRIS) {
            var json = assetsMgr.getJsonData('portrayalBase', reward.id);
            this.showPnl('common/PortrayalBaseInfo', json, 'shop');
        }
        else if (reward.type === Enums_1.CType.HERO_OPT) {
            var id = reward.id, arr = Constant_1.HERO_OPT_GIFT[id];
            var list = arr ? arr.map(function (m) { return new PortrayalInfo_1.default().init(m); }) : assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
            this.showPnl('common/SelectPortrayalPreview', id, list);
        }
    };
    // 通过deeplink打开相应游戏界面
    ViewHelper.prototype.openUIByDeepLink = function () {
        var data = GameHelper_1.gameHpr.getEnterQuery();
        if (data === null || data === void 0 ? void 0 : data.openUI) {
            var url = data.openUI.replace('_', '/'), params = data.params.split('_');
            this.showPnl.apply(this, __spread([url], params));
        }
    };
    // 刷新士兵属性
    ViewHelper.prototype.updatePawnAttrs = function (node, pawn) {
        node.Child('hp/val', cc.Label).string = pawn.getHpText();
        node.Child('anger/val', cc.Label).string = pawn.getAngerText();
        node.Child('attack/val', cc.Label).string = pawn.getAttackText();
        node.Child('attack_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getAttackRange());
        node.Child('move_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getMoveRange());
        if (node.Child('cereal_c').active = !!pawn.baseJson.cereal_cost) {
            node.Child('cereal_c/val', cc.Label).string = pawn.baseJson.cereal_cost;
        }
    };
    // 刷新士兵技能
    ViewHelper.prototype.updatePawnSkills = function (node, pawn, key) {
        var _a, _b;
        // 技能
        var skillNode = node.Child('skills'), portrayalSkill = node.Child('portrayal_skill_be');
        if (skillNode.active = pawn.skills.length > 0) {
            var skills_1 = [];
            pawn.skills.forEach(function (m) {
                if (m.type < Enums_1.PawnSkillType.RESTRAIN) {
                    var d = skills_1.find(function (s) { return s.type === Enums_1.PawnSkillType.RESTRAIN; });
                    if (!d) {
                        d = skills_1.add({ id: 101, useType: m.use_type, type: Enums_1.PawnSkillType.RESTRAIN, name: m.json.name, descs: [], desc_params: [] });
                    }
                    d.descs.push(m.json.desc);
                    d.desc_params.push(String(m.json.desc_params).split('|'));
                }
                else if (m.type === Enums_1.PawnSkillType.INSTABILITY_ATTACK) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getAttackText(), m.json.desc_params]] });
                }
                else if (m.type === Enums_1.PawnSkillType.PEOPLE_BROKEN) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [pawn.getAttackTextByIndex(2)] });
                }
                else if (m.type === Enums_1.PawnSkillType.SKILL_217) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[m.json.desc_params, pawn.getMoveRange()]] });
                }
                else if (m.type === Enums_1.PawnSkillType.CADET) {
                    skills_1.add({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getCadetLvText(), m.json.desc_params, 'pawnText.name_4205']] });
                }
                else {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [String(m.json.desc_params).split('|')] });
                }
            });
            skillNode.Items(skills_1, function (it, skill) {
                it.Data = skill;
                ResHelper_1.resHelper.loadSkillIcon(skill.id, it.Child('val'), key);
            });
        }
        // 英雄技能
        if (portrayalSkill === null || portrayalSkill === void 0 ? void 0 : portrayalSkill.setActive(!!((_b = (_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id))) {
            ResHelper_1.resHelper.loadHeroSkillIcon(pawn.portrayal.skill.id, portrayalSkill.Child('val'), key);
        }
        return skillNode.active || !!(portrayalSkill === null || portrayalSkill === void 0 ? void 0 : portrayalSkill.active);
    };
    // 刷新军队状态
    ViewHelper.prototype.updateArmyState = function (node, data, march, isHasLving, isMe) {
        var _this = this;
        var states = [], tonden = data instanceof ArmyObj_1.default ? GameHelper_1.gameHpr.world.getArmyTondenInfo(data.index, data.uid) : data.tonden;
        if (data.drillPawns.length > 0) {
            states.push(Enums_1.ArmyState.DRILL);
        }
        if (data.curingPawns.length > 0) {
            states.push(Enums_1.ArmyState.CURING);
        }
        if (isHasLving) {
            states.push(Enums_1.ArmyState.LVING);
        }
        if (tonden) {
            states.push(Enums_1.ArmyState.TONDEN);
        }
        if (data.state !== Enums_1.ArmyState.NONE || states.length === 0) {
            states.unshift(data.state);
        }
        var scroll = node.Child('stateScroll'), player = GameHelper_1.gameHpr.player;
        scroll.Child('state').Items(states, function (it, state) {
            var color = Constant_1.ARMY_STATE_COLOR[state];
            it.Child('val').Color(color).setLocaleKey('ui.army_state_' + state);
            var timeLbl = it.Child('time', cc.LabelTimer);
            timeLbl.Color(color);
            if (!isMe) {
                timeLbl.setActive(false);
            }
            else if (state === Enums_1.ArmyState.MARCH && march) {
                var targetIndex_1 = march.targetIndex;
                timeLbl.setActive(true);
                timeLbl.run(march.getSurplusTime() * 0.001, function () {
                    if (node.isValid) {
                        data.index = targetIndex_1;
                        data.state = Enums_1.ArmyState.NONE;
                        data.treasures.forEach(function (m) { return m.index = data.index; });
                        _this.updateArmyState(node, data, march, isHasLving, isMe);
                    }
                });
                timeLbl.node.opacity = 255;
            }
            else if (state === Enums_1.ArmyState.DRILL || state === Enums_1.ArmyState.LVING || state === Enums_1.ArmyState.CURING) { //训练
                timeLbl.setActive(true);
                var _a = state === Enums_1.ArmyState.DRILL ? player.getSumDrillTimeByArmy(data.uid) : state === Enums_1.ArmyState.CURING ? player.getSumCuringTimeByArmy(data.uid) : player.getSumLvingTimeByArmy(data.uid), time = _a.time, pause = _a.pause;
                if (pause) {
                    timeLbl.string = ut.millisecondFormat(time, 'h:mm:ss');
                }
                else {
                    timeLbl.run(time * 0.001);
                }
                timeLbl.node.opacity = pause ? 128 : 255;
            }
            else if (state === Enums_1.ArmyState.TONDEN && tonden) { //屯田
                timeLbl.setActive(true);
                timeLbl.run(tonden.getSurplusTime() * 0.001, function () {
                    if (node.isValid) {
                        if (data instanceof ArmyObj_1.default) {
                        }
                        else {
                            data.tonden = null;
                        }
                        data.state = Enums_1.ArmyState.NONE;
                        _this.updateArmyState(node, data, march, isHasLving, isMe);
                    }
                });
                timeLbl.node.opacity = 255;
            }
            else {
                timeLbl.setActive(false);
            }
        });
    };
    return ViewHelper;
}());
exports.viewHelper = new ViewHelper();
if (cc.sys.isBrowser) {
    window['viewHelper'] = exports.viewHelper;
}

cc._RF.pop();