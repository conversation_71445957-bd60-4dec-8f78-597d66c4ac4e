"use strict";
cc._RF.push(module, '1813aKPqRBJYIhvIDSfAGSQ', 'AreaCenterModel');
// app/script/model/area/AreaCenterModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var AreaObj_1 = require("./AreaObj");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ECode_1 = require("../../common/constant/ECode");
var NetHelper_1 = require("../../common/helper/NetHelper");
var PawnObj_1 = require("./PawnObj");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
/**
 * 战场管理中心
 */
var AreaCenterModel = /** @class */ (function (_super) {
    __extends(AreaCenterModel, _super);
    function AreaCenterModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.AREA_MAX_LIFE_TIME = 60 * 1000 * 1; //区域最大存在时间
        _this.areas = new Map();
        _this.watchPlayers = new Map(); //区域观战玩家
        _this.net = null;
        _this.player = null;
        _this.lookArea = null; //当前查看的区域
        return _this;
    }
    AreaCenterModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.player = this.getModel('player');
    };
    // 初始化监听
    AreaCenterModel.prototype.initNetEvent = function () {
        this.net.on('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this);
        this.net.on('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this);
    };
    AreaCenterModel.prototype.clean = function () {
        this.net.off('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this);
        this.net.off('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this);
        this.cleanAreas();
        this.watchPlayers.clear();
    };
    AreaCenterModel.prototype.getArea = function (index) {
        var _a;
        if (((_a = this.lookArea) === null || _a === void 0 ? void 0 : _a.index) === index) {
            return this.lookArea;
        }
        return this.areas.get(GameHelper_1.gameHpr.amendAreaIndex(index));
    };
    AreaCenterModel.prototype.addArea = function (area) {
        this.areas.set(area.index, area);
        return area;
    };
    AreaCenterModel.prototype.removeArea = function (index) {
        var _a;
        (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.clean();
        this.delArea(index);
    };
    AreaCenterModel.prototype.delArea = function (index) {
        this.areas.delete(index);
        NetHelper_1.netHelper.sendLeaveArea({ index: index });
    };
    AreaCenterModel.prototype.cleanAreas = function () {
        var _a;
        this.areas.forEach(function (m) { return m.clean(); });
        this.areas.clear();
        if (((_a = this.lookArea) === null || _a === void 0 ? void 0 : _a.index) >= 0) {
            this.lookArea = null;
        }
    };
    // 获取当前场景的区域信息
    AreaCenterModel.prototype.getLookArea = function () {
        var _a, _b;
        if (!this.lookArea) {
            this.lookArea = this.getArea((_b = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : -1);
        }
        return this.lookArea;
    };
    AreaCenterModel.prototype.setLookArea = function (area) {
        this.lookArea = area;
    };
    // 获取自己的主城市
    AreaCenterModel.prototype.getMeMainArea = function () {
        return this.areas.get(this.player.getMainCityIndex());
    };
    // 获取军队
    AreaCenterModel.prototype.getArmy = function (index, uid) {
        var _a;
        return (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.getArmyByUid(uid);
    };
    // 请求战场信息
    AreaCenterModel.prototype.reqAreaInfo = function (index, reinit) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, areaInfo, area;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqGetAreaInfo({ index: index })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        areaInfo = data === null || data === void 0 ? void 0 : data.data;
                        if (!areaInfo) {
                            return [2 /*return*/, null];
                        }
                        else if (areaInfo.battle) {
                            this.checkRemoveBattleArea();
                        }
                        area = new AreaObj_1.default().init(areaInfo);
                        this.areas.set(index, area);
                        // 是否有战斗
                        if (areaInfo.battle) {
                            area.battleBegin(areaInfo.battle, !reinit);
                        }
                        // 如果有不一样 表示没更新到 这里兼容同步一下地块信息
                        if (area.owner && ((_a = GameHelper_1.gameHpr.world.getMapCellByIndex(index)) === null || _a === void 0 ? void 0 : _a.owner) !== area.owner) {
                            GameHelper_1.gameHpr.world.syncServerCellInfo(index);
                        }
                        return [2 /*return*/, area];
                }
            });
        });
    };
    // 获取战场信息没有就请求
    AreaCenterModel.prototype.reqAreaByIndex = function (index, reinit) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var cell, owner, area;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (index === -1) {
                            return [2 /*return*/, null];
                        }
                        cell = GameHelper_1.gameHpr.world.getMapCellByIndex(index);
                        owner = cell.owner || '';
                        index = (_b = (_a = cell === null || cell === void 0 ? void 0 : cell.city) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : index; //修正一下index 有可能点到其他点
                        area = this.areas.get(index);
                        if (!(!area || area.owner !== owner)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.reqAreaInfo(index, reinit)];
                    case 1:
                        area = _c.sent();
                        _c.label = 2;
                    case 2: return [2 /*return*/, area];
                }
            });
        });
    };
    // 升级建筑
    AreaCenterModel.prototype.upBuildToServer = function (item) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!GameHelper_1.gameHpr.checkCTypes(item.upCost)) {
                            return [2 /*return*/, ECode_1.ecode.RES_NOT_ENOUGH];
                        }
                        else if (this.player.getBtQueues().has('id', item.id)) {
                            return [2 /*return*/, ECode_1.ecode.YET_IN_BTQUEUE];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqUpAreaBuild({ index: item.aIndex, uid: item.uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, err];
                        }
                        this.player.updateOutputByFlags(data.output);
                        this.player.updateBtQueue(data.queues);
                        // 买量数据上报
                        if (item.id === Enums_1.BUILD_NID.MAIN) {
                            if (item.lv === 4) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_5');
                            }
                            else if (item.lv === 9) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_10');
                            }
                            else if (item.lv === 14) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_15');
                            }
                            else if (item.lv === 19) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_20');
                            }
                        }
                        else if (item.id === Enums_1.BUILD_NID.SMITHY) {
                            if (item.lv === 9) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('smithy_lv_10');
                            }
                            else if (item.lv === 14) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('smithy_lv_15');
                            }
                            else if (item.lv === 19) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('smithy_lv_20');
                            }
                        }
                        else if (item.id === Enums_1.BUILD_NID.CAMP) {
                            if (item.lv === 0) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('frontier_garrison');
                            }
                            else if (item.lv === 4) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('frontier_lv_5');
                            }
                        }
                        return [2 /*return*/, ''];
                }
            });
        });
    };
    // 修建建筑
    AreaCenterModel.prototype.unlockBuildToServer = function (index, id) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqAddAreaBuild({ index: index, id: id })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            this.player.updateOutputByFlags(data.output);
                            this.player.updateBtQueue(data.queues);
                            (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.addBuild(data.build);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 训练士兵
    AreaCenterModel.prototype.drillPawnToServer = function (index, buildUid, id, armyUid, armyName) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqDrillPawn({ index: index, buildUid: buildUid, id: id, armyUid: armyUid, armyName: armyName })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyDrillPawns(data.army);
                            this.player.updateOutputByFlags(data.output);
                            this.player.updatePawnDrillQueue(data.queues);
                            this.player.setFreeRecruitPawnCount(data.freeRecruitPawnCount || 0);
                            this.player.setUpRecruitPawnCount(data.upRecruitPawnCount || 0);
                        }
                        return [2 /*return*/, { err: err, army: data === null || data === void 0 ? void 0 : data.army }];
                }
            });
        });
    };
    // 治疗士兵
    AreaCenterModel.prototype.curePawnToServer = function (index, armyUid, armyName, pawnUid) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqCurePawn({ index: index, armyUid: armyUid, armyName: armyName, pawnUid: pawnUid })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                            this.player.updateOutputByFlags(data.output);
                            this.player.updatePawnCuringQueue(data.queues);
                            this.player.setFreeCurePawnCount(data.freeCurePawnCount || 0);
                        }
                        return [2 /*return*/, { err: err, list: data === null || data === void 0 ? void 0 : data.queues, army: data === null || data === void 0 ? void 0 : data.army }];
                }
            });
        });
    };
    // 士兵练级
    AreaCenterModel.prototype.pawnLvingToServer = function (index, auid, puid) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqPawnLving({ index: index, auid: auid, puid: puid })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            this.player.updateRewardItemsByFlags(data.cost);
                            this.player.updatePawnLevelingQueue(data.queues);
                            this.player.setFreeLevingPawnCount(data.freeLevingPawnCount || 0);
                        }
                        return [2 /*return*/, { err: err, armyUid: (_a = data === null || data === void 0 ? void 0 : data.army) === null || _a === void 0 ? void 0 : _a.uid }];
                }
            });
        });
    };
    // 创建一个士兵 根据招募信息
    AreaCenterModel.prototype.createPawnByDrillInfo = function (data) {
        var _a, _b;
        var conf = this.player.getConfigPawnInfo(data.id);
        var pawn = new PawnObj_1.default().init(data.id, conf.equip, 1, conf.skinId).initAnger();
        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed;
        pawn.lv = data.lv;
        pawn.armyUid = data.auid;
        pawn.armyName = ((_b = (_a = this.getArea(data.index)) === null || _a === void 0 ? void 0 : _a.getArmyByUid(data.auid)) === null || _b === void 0 ? void 0 : _b.name) || '';
        return pawn;
    };
    // 创建一个士兵 根据治疗信息
    AreaCenterModel.prototype.createPawnByCureInfo = function (data) {
        var _a, _b;
        var conf = this.player.getConfigPawnInfo(data.id);
        var pawn = new PawnObj_1.default().init(data.id, conf.equip, 1, conf.skinId).initAnger();
        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed;
        pawn.lv = data.lv;
        pawn.armyUid = data.auid;
        pawn.armyName = ((_b = (_a = this.getArea(data.index)) === null || _a === void 0 ? void 0 : _a.getArmyByUid(data.auid)) === null || _b === void 0 ? void 0 : _b.name) || '';
        return pawn;
    };
    // 创建一个士兵 根据练级信息
    AreaCenterModel.prototype.createPawnByLvingInfo = function (pawn, data) {
        var _a;
        var newPawn = new PawnObj_1.default().init(pawn.id, pawn.equip, data.lv, pawn.skinId).initAnger();
        newPawn.aIndex = pawn.aIndex;
        newPawn.owner = pawn.owner;
        newPawn.uid = pawn.uid;
        newPawn.armyUid = pawn.armyUid;
        newPawn.armyName = pawn.armyName;
        newPawn.attackSpeed = pawn.attackSpeed;
        newPawn.treasures = pawn.treasures;
        newPawn.portrayal = (_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.clone();
        if (newPawn.portrayal) {
            newPawn.updateAttr();
        }
        return newPawn;
    };
    // 检测删除多的战斗区域
    AreaCenterModel.prototype.checkRemoveBattleArea = function () {
        var _a;
        var loolAreaIndex = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index;
        var leaveTime = 0, count = 0, area = null;
        this.areas.forEach(function (m) {
            if (!m.active && m.index >= 0 && m.isBattleing() && loolAreaIndex !== m.index && m.leaveTime > 0) {
                count += 1;
                if (leaveTime < m.leaveTime) {
                    leaveTime = m.leaveTime;
                    area = m;
                }
            }
        });
        if (count >= 5 && area) {
            area.clean();
            this.delArea(area.index);
            // cc.log('checkRemoveBattleArea index=' + area.index)
        }
    };
    // 获取观战玩家列表
    AreaCenterModel.prototype.getAreaWatchPlayersByIndex = function (index) {
        return this.watchPlayers.get(index) || [];
    };
    AreaCenterModel.prototype.update = function (dt) {
        var _this = this;
        var _a;
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode;
        var loolAreaIndex = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index;
        var now = Date.now(), life = this.AREA_MAX_LIFE_TIME;
        this.areas.forEach(function (m) {
            if (!isNoviceMode && !m.active && m.index >= 0 && loolAreaIndex !== m.index && m.leaveTime > 0 && now - m.leaveTime >= life) {
                m.clean();
                _this.delArea(m.index);
                // cc.log('delArea index=' + m.index)
            }
            else {
                m.update(dt);
            }
        });
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 更新战场信息
    AreaCenterModel.prototype.OnUpdateAreaInfo = function (res) {
        var _a;
        var type = res.type, index = res.index, data = res['data_' + type];
        cc.log('OnUpdateAreaInfo', Enums_1.NotifyType[type], index, data);
        var area = this.getArea(index);
        if (type === Enums_1.NotifyType.AREA_PLAYER_CHANGE) { //通知战场人员变动
            if (data === null || data === void 0 ? void 0 : data.length) {
                this.watchPlayers.set(index, data);
            }
            else {
                this.watchPlayers.delete(index);
            }
            this.emit(EventType_1.default.UPDATE_AREA_WATCH_PLAYER, index);
        }
        else if (type === Enums_1.NotifyType.AREA_CHAT) { //通知战场聊天
            this.emit(EventType_1.default.ADD_AREA_CHAT, index, data);
        }
        else if (!area) {
        }
        else if (type === Enums_1.NotifyType.BUILD_UP) { //建筑升级
            area.buildUp(data.uid, data.lv);
        }
        else if (type === Enums_1.NotifyType.MOVE_BUILD) { //移动建筑
            var build = area.getBuildByUid(data.uid);
            if (build) {
                build.point.set(data.point);
                this.emit(EventType_1.default.UPDATE_BUILD_POINT, build);
            }
        }
        else if (type === Enums_1.NotifyType.ADD_BUILD) { //添加建筑
            area.addBuild(data);
        }
        else if (type === Enums_1.NotifyType.REMOVE_BUILD) { //删除建筑
            area.removeBuild(data);
        }
        else if (type === Enums_1.NotifyType.ADD_ARMY) { //添加军队
            area.addArmy(data);
        }
        else if (type === Enums_1.NotifyType.REMOVE_ARMY) { //删除军队
            area.removeArmy(data);
        }
        else if (type === Enums_1.NotifyType.UPDATE_ARMY) { //更新军队
            area.updateArmy(data);
        }
        else if (type === Enums_1.NotifyType.UPDATE_ALL_PAWN_HP) { //更新所有士兵血量
            area.updateAllArmy(data);
        }
        else if (type === Enums_1.NotifyType.MOVE_PAWN) { //移动士兵
            var army = area.getArmyByUid(data.armyUid);
            if (army) {
                army.setPawnsPoint(data.pawns || []);
                if (!army.isOwner()) { //不是我自己就通知一下
                    eventCenter.emit(EventType_1.default.UPDATE_ARMY, army);
                }
            }
        }
        else if (type === Enums_1.NotifyType.CHANGE_PAWN_ATTR) { //改变士兵属性
            data === null || data === void 0 ? void 0 : data.forEach(function (m) {
                var pawn = area.getPawnByPrecise(m.armyUid, m.uid);
                if (pawn) {
                    pawn.setAttackSpeed(m.attackSpeed);
                    pawn.changeSkin(m.skinId);
                    pawn.changeEquip(m.equip);
                }
            });
            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, index);
        }
        else if (type === Enums_1.NotifyType.CHANGE_PAWN_PORTRAYAL) { //改变士兵化身
            var pawn = area.getPawnByPrecise(data.armyUid, data.uid);
            if (pawn) {
                pawn.setPortrayal(data.portrayal);
            }
        }
        else if (type === Enums_1.NotifyType.UPDATE_PAWN_TREASURE) { //刷新士兵宝箱
            var pawn = area.getPawnByPrecise(data.armyUid, data.uid);
            if (pawn) {
                pawn.updateTreasures(data.treasures);
                this.emit(EventType_1.default.UPDATE_PAWN_TREASURE, pawn);
                area.updateTreasureReddot();
            }
        }
        else if (type === Enums_1.NotifyType.UPDATE_ARMY_TREASURES) { //刷新军队宝箱
            var army_1 = area.getArmyByUid(data.armyUid);
            if (army_1) {
                data.pawnTreasures.forEach(function (m) {
                    var pawn = army_1.pawns.find(function (p) { return p.uid === m.uid; });
                    if (pawn) {
                        pawn.updateTreasures(m.treasures);
                    }
                });
                this.emit(EventType_1.default.UPDATE_ARMY_TREASURES, army_1);
                area.updateTreasureReddot();
            }
        }
        else if (type === Enums_1.NotifyType.AREA_BATTLE_BEGIN) { //发生战斗
            area.initBattleData(data);
            area.battleBegin(data.battle);
        }
        else if (type === Enums_1.NotifyType.AREA_BATTLE_END) { //战斗结束
            area.battleEndByServer(data);
        }
        else if (type === Enums_1.NotifyType.CELL_TONDEN_END) { //屯田结束
            if (area.active && ((_a = area.getArmyByUid(data.auid)) === null || _a === void 0 ? void 0 : _a.isOwner())) {
                ViewHelper_1.viewHelper.showPnl('area/TondenEnd', data.treasures);
            }
        }
    };
    // 战斗帧数据
    AreaCenterModel.prototype.OnFSPCheckFrame = function (res) {
        var _a, _b;
        var data = (res === null || res === void 0 ? void 0 : res.data) || {};
        var fsp = ((_a = this.getArea(data.index)) === null || _a === void 0 ? void 0 : _a.getFspModel()) || ((_b = this.lookArea) === null || _b === void 0 ? void 0 : _b.getFspModel());
        fsp === null || fsp === void 0 ? void 0 : fsp.onFSPCheckFrame(data);
    };
    AreaCenterModel = __decorate([
        mc.addmodel('areaCenter')
    ], AreaCenterModel);
    return AreaCenterModel;
}(mc.BaseModel));
exports.default = AreaCenterModel;

cc._RF.pop();