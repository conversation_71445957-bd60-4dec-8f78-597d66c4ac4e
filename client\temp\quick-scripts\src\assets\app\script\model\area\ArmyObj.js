"use strict";
cc._RF.push(module, '82a01xgsE1JY4cV4syXCG5d', 'ArmyObj');
// app/script/model/area/ArmyObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PawnObj_1 = require("./PawnObj");
// 战场里面的军队
var ArmyObj = /** @class */ (function () {
    function ArmyObj() {
        this.aIndex = 0; //所属哪个区域
        this.enterDir = -1; //进入方向
        this.uid = '';
        this.name = '';
        this.owner = ''; //拥有者
        this.pawns = []; //士兵列表
        this.drillPawns = []; //训练中的士兵列表
        this.marchSpeed = 0; //行军速度
        this.defaultMarchSpeed = 0; //默认行军速度
        this.state = Enums_1.ArmyState.NONE;
        this.curingPawns = []; // 治疗中的士兵
    }
    ArmyObj.prototype.init = function (index, owner, name) {
        this.aIndex = index;
        this.uid = 'temp_' + ut.UID();
        this.owner = owner;
        this.name = name;
        this.pawns.length = 0;
        this.drillPawns.length = 0;
        this.state = Enums_1.ArmyState.NONE;
        this.curingPawns.length = 0;
        return this;
    };
    ArmyObj.prototype.fromSvr = function (data) {
        var _a, _b, _c;
        this.aIndex = data.index;
        this.enterDir = (_a = data.enterDir) !== null && _a !== void 0 ? _a : -1;
        this.uid = data.uid || '';
        this.name = data.name || '';
        this.owner = data.owner || '';
        this.updatePawns(data.pawns || []);
        this.drillPawns = data.drillPawns || [];
        this.state = data.state || Enums_1.ArmyState.NONE;
        this.defaultMarchSpeed = (_c = (_b = this.getPawnByMinMarchSpeed()) === null || _b === void 0 ? void 0 : _b.marchSpeed) !== null && _c !== void 0 ? _c : 0;
        this.marchSpeed = data.marchSpeed || this.defaultMarchSpeed;
        this.curingPawns = data.curingPawns || [];
        return this;
    };
    ArmyObj.prototype.strip = function () {
        return {
            index: this.aIndex,
            uid: this.uid,
            name: this.name,
            owner: this.owner,
            pawns: this.pawns.map(function (p) { return p.strip(); }),
            drillPawns: this.drillPawns,
            state: this.state,
            enterDir: this.enterDir,
            marchSpeed: this.marchSpeed,
            curingPawns: this.curingPawns,
        };
    };
    ArmyObj.prototype.toPawnsByHP = function () {
        return {
            pawns: this.pawns.map(function (p) {
                return { uid: p.uid, curHp: Math.min(p.curHp, p.maxHp), maxHp: p.maxHp };
            })
        };
    };
    Object.defineProperty(ArmyObj.prototype, "index", {
        get: function () { return this.aIndex; },
        set: function (val) { this.aIndex = val; },
        enumerable: false,
        configurable: true
    });
    // 是否自己的军队
    ArmyObj.prototype.isOwner = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否战斗中
    ArmyObj.prototype.isBattleing = function () {
        return this.state === Enums_1.ArmyState.FIGHT;
    };
    // 是否在治疗中
    ArmyObj.prototype.isCuring = function () {
        return this.state === Enums_1.ArmyState.CURING;
    };
    // 是否有英雄
    ArmyObj.prototype.isHasHero = function () {
        return this.pawns.some(function (m) { return m.isHero(); });
    };
    ArmyObj.prototype.updatePawns = function (pawns) {
        var _this = this;
        // 先删除没有的
        var uidMap = {};
        pawns.forEach(function (m) { return uidMap[m.uid] = true; });
        for (var i = this.pawns.length - 1; i >= 0; i--) {
            if (!uidMap[this.pawns[i].uid]) {
                this.pawns.splice(i, 1);
            }
        }
        // 一个个添加
        pawns.forEach(function (m) { return _this.addPawn(m); });
    };
    // 删除士兵
    ArmyObj.prototype.removePawn = function (uid) {
        this.pawns.remove('uid', uid);
    };
    // 添加士兵
    ArmyObj.prototype.addPawn = function (data) {
        var _a;
        var pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
        if (pawn) {
            pawn.fromSvr(data, this.uid, this.owner, this.name);
        }
        else if (data.uid === ((_a = GameHelper_1.gameHpr.uiShowPawnData) === null || _a === void 0 ? void 0 : _a.uid)) {
            pawn = this.pawns.add(GameHelper_1.gameHpr.uiShowPawnData.fromSvr(data, this.uid, this.owner, this.name));
        }
        else {
            pawn = this.pawns.add(new PawnObj_1.default().fromSvr(data, this.uid, this.owner, this.name));
        }
        pawn.enterDir = this.enterDir;
        return pawn;
    };
    // 获取实际士兵个数
    ArmyObj.prototype.getActPawnCount = function () {
        return this.pawns.length + this.drillPawns.length + this.curingPawns.length;
    };
    // 获取士兵的实际数量
    ArmyObj.prototype.getPawnActCount = function () {
        return this.pawns.filter(function (m) { return !m.isDie(); }).length;
    };
    // 是否可以训练士兵
    ArmyObj.prototype.isCanDrillPawn = function () {
        return this.isOwner() && this.state !== Enums_1.ArmyState.MARCH;
    };
    // 是否可以治疗士兵
    ArmyObj.prototype.isCanCurePawn = function () {
        return this.isOwner() && this.state !== Enums_1.ArmyState.MARCH;
    };
    // 获取所有士兵宝箱数量
    ArmyObj.prototype.getAllPawnTreasureCount = function () {
        return this.pawns.reduce(function (val, cur) { return cur.treasures.length + val; }, 0);
    };
    // 获取所有士兵宝箱列表
    ArmyObj.prototype.getAllPawnTreasures = function () {
        var arr = [];
        this.pawns.forEach(function (m) { return arr.pushArr(m.treasures); });
        return arr;
    };
    Object.defineProperty(ArmyObj.prototype, "treasures", {
        get: function () { return this.getAllPawnTreasures(); },
        enumerable: false,
        configurable: true
    });
    // 设置士兵位置
    ArmyObj.prototype.setPawnsPoint = function (pawns) {
        if (!pawns || pawns.length === 0) {
            return;
        }
        var obj = {};
        pawns.forEach(function (m) { return obj[m.uid] = m.point; });
        this.pawns.forEach(function (m) {
            var point = obj[m.uid];
            if (point) {
                m.setPoint(point);
            }
        });
    };
    /////////////////////////////////////////////////////////// 新手村相关 ///////////////////////////////////////////////////////////////////
    // 获取行军速度最少士兵
    ArmyObj.prototype.getPawnByMinMarchSpeed = function () {
        var speed = -1, pawn = null;
        this.pawns.forEach(function (m) {
            if (speed === -1 || m.marchSpeed < speed || (m.marchSpeed == speed && m.skinId > 0)) {
                speed = m.marchSpeed;
                pawn = m;
            }
        });
        return pawn || this.pawns[0];
    };
    // 回复血量
    ArmyObj.prototype.recoverAllPawn = function () {
        this.pawns.forEach(function (m) { return m.curHp = m.maxHp; });
    };
    // 设置士兵位置
    ArmyObj.prototype.setPawnPoints = function (points) {
        var len = points === null || points === void 0 ? void 0 : points.length;
        if (!len) {
            return;
        }
        for (var i = 0, l = this.pawns.length; i < l; i++) {
            if (i < len) {
                this.pawns[i].setPoint(points[i]);
            }
            else {
                this.pawns[i].setPoint(points[0]);
            }
        }
    };
    // 是否可以战斗
    ArmyObj.prototype.isCanBattle = function () {
        return this.state === Enums_1.ArmyState.NONE && this.drillPawns.length === 0 && this.pawns.length > 0 && !this.pawns.some(function (m) { return GameHelper_1.gameHpr.noviceServer.checkPawnLving(m.uid); });
    };
    ArmyObj.prototype.updatePawnEquipAttr = function (equip) {
        this.pawns.forEach(function (m) { return m.updateEquipAttr(equip.id, equip.attrs); });
    };
    return ArmyObj;
}());
exports.default = ArmyObj;

cc._RF.pop();