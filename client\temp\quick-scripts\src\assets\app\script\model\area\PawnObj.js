"use strict";
cc._RF.push(module, 'e3304Wt8+VDip92tR5grFfv', 'PawnObj');
// app/script/model/area/PawnObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PortrayalInfo_1 = require("../common/PortrayalInfo");
var EquipInfo_1 = require("../main/EquipInfo");
var BuffObj_1 = require("./BuffObj");
var PawnSkillObj_1 = require("./PawnSkillObj");
var PawnStateObj_1 = require("./PawnStateObj");
// 一个士兵
var PawnObj = /** @class */ (function () {
    function PawnObj() {
        this.aIndex = 0; //所属哪个区域
        this.enterDir = -1;
        this.uid = '';
        this.cuid = ''; //客户端uid
        this.armyUid = ''; //所属队伍uid
        this.armyName = ''; //军队名字
        this.owner = '';
        this.recordDataMap = {}; //记录
        this.treasures = []; //当前宝箱个数
        this.id = 0;
        this.point = cc.v2();
        this.lv = 0; //等级
        this.skinId = 0; //皮肤id
        this.curHp = 0; //当前血量
        this.maxHp = 0;
        this.curAnger = 0; //怒气
        this.maxAnger = 0;
        this.attack = 0;
        this.attackRange = 0; //攻击范围
        this.moveRange = 0; //移动范围
        this.attackSpeed = 0; //当前出手速度
        this.equip = null; //当前携带的装备
        this.portrayal = null; //携带的画像
        this.rodeleroCadetLv = 0; //当前 见习勇者 层数
        this.petId = 0; //携带的宠物
        this.state = null; //当前状态
        this.skills = []; //技能列表
        this.actioning = false; //是否行动中
        this.buffs = []; //当前的buff列表
        this.strategyBuffMap = {}; //韬略map
        this.attrId = 0;
        this.baseJson = null;
        this.attrJson = null;
        this.upCost = []; //升级费用
        this.tempCurrHp = -1; //用于记录
        this.tempAttackAnimTimes = null; //攻击动画时间
        this.cuid = ut.UID();
    }
    PawnObj.prototype.toString = function () {
        return "uid:" + this.uid + ", point:" + this.point.Join(",") + ", hp:" + this.curHp + "/" + this.getMaxHp() + ", attack:" + this.attack + ", attackRange:" + this.attackRange + ", moveRange:" + this.moveRange;
    };
    PawnObj.prototype.init = function (id, equip, lv, skinId, rodeleroCadetLv) {
        var _a;
        this.id = id;
        this.lv = lv || 1;
        this.skinId = skinId || 0;
        this.equip = new EquipInfo_1.default().fromSvr(equip || { id: 0, attrs: [] });
        this.rodeleroCadetLv = rodeleroCadetLv !== null && rodeleroCadetLv !== void 0 ? rodeleroCadetLv : (this.id === 3205 ? (((_a = GameHelper_1.gameHpr.getPlayerInfo(this.owner || GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.rodeleroCadetLv) || 0) : 0);
        this.tempCurrHp = -1;
        this.initJson();
        this.curHp = this.maxHp;
        return this;
    };
    PawnObj.prototype.fromSvr = function (data, auid, owner, aname) {
        var _a, _b;
        // cc.log(data)
        this.aIndex = data.index;
        this.uid = data.uid;
        this.id = data.id;
        this.lv = data.lv || 1;
        this.skinId = data.skinId || 0;
        this.point.set(data.point);
        this.armyUid = auid;
        this.attackSpeed = data.attackSpeed || 0;
        this.equip = new EquipInfo_1.default().fromSvr(data.equip || { id: 0, attrs: [] });
        this.portrayal = data.portrayal ? new PortrayalInfo_1.default().fromSvr(data.portrayal) : null;
        this.rodeleroCadetLv = data.rodeleroCadetLv || 0;
        this.petId = data.petId || 0;
        this.armyName = aname || '';
        this.owner = owner;
        this.recordDataMap = data.recordDataMap || {};
        this.tempCurrHp = -1;
        this.updateTreasures(data.treasures);
        this.setBuffs(data.buffs || []);
        this.strategyBuffMap = {};
        this.initJson(data.isFight);
        this.curHp = (_b = (_a = data.hp) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : this.maxHp;
        this.curAnger = data.curAnger;
        if (!this.curAnger && !this.isBattleing()) {
            this.initAnger();
        }
        return this;
    };
    PawnObj.prototype.strip = function () {
        var _a;
        return {
            index: this.aIndex,
            armyUid: this.armyUid,
            uid: this.uid,
            skinId: this.skinId,
            point: this.point.toJson(),
            id: this.id,
            lv: this.lv,
            curAnger: this.curAnger,
            attackSpeed: this.attackSpeed,
            equip: this.equip.strip(),
            portrayal: (_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.strip(),
            rodeleroCadetLv: this.rodeleroCadetLv,
            petId: this.petId,
            treasures: ut.deepClone(this.treasures),
            hp: [this.curHp, this.maxHp],
            buffs: this.buffs.map(function (m) { return m.strip(); }),
            recordDataMap: ut.deepClone(this.recordDataMap)
        };
    };
    Object.defineProperty(PawnObj.prototype, "index", {
        get: function () { return this.aIndex; },
        enumerable: false,
        configurable: true
    });
    PawnObj.prototype.initJson = function (isFight) {
        var _a;
        this.baseJson = assetsMgr.getJsonData('pawnBase', this.id);
        this.attackSpeed = this.attackSpeed || (((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.attack_speed) || 9);
        this.changeState(isFight ? Enums_1.PawnState.STAND : Enums_1.PawnState.NONE, null, true);
        this.updateAttrJson();
    };
    // 刷新属性json
    PawnObj.prototype.updateAttrJson = function () {
        var _a, _b;
        if (this.isMachine()) {
            this.lv = 1;
        }
        this.attrId = this.id * 1000 + this.lv;
        this.attrJson = assetsMgr.getJsonData('pawnAttr', this.attrId);
        if (!this.attrJson) {
            return ErrorReportHelper_1.errorReportHelper.reportError('PawnObj.updateAttrJson', { id: this.id, lv: this.lv });
        }
        this.maxHp = this.attrJson.hp || 0;
        this.maxAnger = this.attrJson.anger || 0;
        this.attack = this.attrJson.attack || 0;
        this.attackRange = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.attack_range) || 0;
        this.moveRange = ((_b = this.attrJson) === null || _b === void 0 ? void 0 : _b.move_range) || 0;
        this.skills = ut.stringToNumbers(this.attrJson.skill).map(function (m) { return new PawnSkillObj_1.default().init(m); });
        this.upCost = GameHelper_1.gameHpr.getPawnCost(this.id, this.lv, this.attrJson);
        this.updateAttr(true);
    };
    // 获取攻击动画时间
    PawnObj.prototype.getAttackAnimTimes = function () {
        var _a, _b, _c;
        if (!this.tempAttackAnimTimes) {
            var attackAnimTimeStr = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.attack_anim_time) || '';
            if (this.isHero()) {
                attackAnimTimeStr = ((_b = assetsMgr.getJsonData('portrayalBase', this.portrayal.id)) === null || _b === void 0 ? void 0 : _b.attack_anim_time) || attackAnimTimeStr;
            }
            else if (this.skinId > 0) {
                attackAnimTimeStr = ((_c = assetsMgr.getJsonData('pawnSkin', this.skinId)) === null || _c === void 0 ? void 0 : _c.attack_anim_time) || attackAnimTimeStr;
            }
            this.tempAttackAnimTimes = attackAnimTimeStr.split('|').map(function (m) { return ut.stringToNumbers(m, ','); });
        }
        return this.tempAttackAnimTimes;
    };
    // 记录当前的血量 用于打开士兵面板时 切换装备的时候可以记录一开始的血量
    PawnObj.prototype.recordCurrHp = function (val) {
        this.tempCurrHp = val ? this.curHp : -1;
    };
    PawnObj.prototype.getViewId = function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) || this.skinId || this.id; };
    PawnObj.prototype.getPrefabUrl = function () { return 'pawn/PAWN_' + this.getViewId(); };
    PawnObj.prototype.getUid = function () { return this.uid; };
    PawnObj.prototype.getAbsUid = function () { return this.uid + this.getViewId(); };
    PawnObj.prototype.getPoint = function () { return this.point; };
    PawnObj.prototype.setPoint = function (point) { this.point.set(point); };
    PawnObj.prototype.getPawnType = function () { return this.type; };
    PawnObj.prototype.getPortrayalSkill = function () { var _a; return (_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.skill; };
    PawnObj.prototype.getPortrayalId = function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) || 0; };
    Object.defineProperty(PawnObj.prototype, "name", {
        get: function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.getChatName()) || 'pawnText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "type", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.type) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "typeName", {
        get: function () { return this.type ? 'ui.pawn_type_' + this.type : ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "marchSpeed", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.march_speed) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "cerealCost", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.cereal_cost) || 1; } //粮耗
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "behaviorId", {
        get: function () { var _a; return (_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.behavior_id; } //行为树配置id
        ,
        enumerable: false,
        configurable: true
    });
    PawnObj.prototype.getAttackRange = function () {
        return this.attackRange + this.getStrategyValue(40101) + this.getStrategyValue(40302) + this.getStrategyValue(50013);
    };
    PawnObj.prototype.getMoveRange = function () {
        return this.moveRange + this.getStrategyValue(31901);
    };
    // 获取移动速度 每秒移动距离
    PawnObj.prototype.getMoveVelocity = function () {
        var _a;
        if (this.portrayal) {
            return this.portrayal.moveVelocity;
        }
        return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.velocity) || 100;
    };
    PawnObj.prototype.getAttackText = function () {
        var attack = this.amendAttack(this.attack) + '';
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (skill) {
            var maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
            return attack + '-' + this.amendAttack(maxAttack);
        }
        return attack;
    };
    PawnObj.prototype.getAttackTextByIndex = function (index) {
        var minAttack = this.amendAttack(this.attack);
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (!skill) {
            return minAttack + '';
        }
        var maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
        maxAttack = this.amendAttack(maxAttack);
        minAttack += (Math.round((maxAttack - minAttack) / 3) * index + 1);
        return minAttack + '-' + maxAttack;
    };
    // 获取最大攻击力 目前只用于陌刀
    PawnObj.prototype.getInstabilityMaxAttack = function () {
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (!skill) {
            return this.attack;
        }
        var attack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
        return this.amendAttack(attack);
    };
    PawnObj.prototype.amendAttack = function (attack, ignoreBuffType) {
        var _this = this;
        var addAttackRatio = 0, lowAttackRatio = 0;
        this.buffs.forEach(function (m) {
            var _a, _b, _c, _d, _e;
            if (m.type === ignoreBuffType) {
                return;
            }
            else if (m.type === Enums_1.BuffType.ATTACK_HALO || m.type === Enums_1.BuffType.LV_1_POWER || m.type === Enums_1.BuffType.LONGYUAN_SWORD_ATTACK) {
                addAttackRatio += (m.value * 0.01); //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || m.type === Enums_1.BuffType.KUROU_ADD_ATTACK) {
                addAttackRatio += m.value; //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.ADD_ATTACK || m.type === Enums_1.BuffType.INSPIRE || m.type === Enums_1.BuffType.WORTHY_MONARCH || m.type === Enums_1.BuffType.ADD_EXECUTE_ATTACK || m.type === Enums_1.BuffType.KILL_ADD_ATTACK || m.type === Enums_1.BuffType.GOD_WAR || m.type === Enums_1.BuffType.DDIE_ADD_ATTACK) {
                attack += m.value; //增加攻击力
            }
            else if (m.type === Enums_1.BuffType.WISDOM_COURAGE) { //姜维 智勇
                if (((_b = (_a = _this.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.JIANG_WEI) {
                    attack += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.VALOR) { //李嗣业 勇猛
                if (((_d = (_c = _this.portrayal) === null || _c === void 0 ? void 0 : _c.skill) === null || _d === void 0 ? void 0 : _d.id) === Enums_1.HeroType.LI_SIYE) {
                    attack += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.MORALE) { //曹操 士气
                var v = ((_e = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_CAO)) === null || _e === void 0 ? void 0 : _e.target) || 0;
                attack += (m.value * v);
            }
            else if (m.type === Enums_1.BuffType.TIGER_MANIA) { //许褚 虎痴
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.DESTROY_WEAPONS) { //摧毁武器 降低攻击力
                lowAttackRatio += m.value;
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK_S) { //韬略 加攻击力
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.THOUSAND_UMBRELLA) { //千机伞
                var effect = _this.getEquipEffectByType(Enums_1.EquipEffectType.THOUSAND_UMBRELLA);
                if (effect) {
                    var val = m.value === 0 ? effect.value * 2 : effect.value;
                    addAttackRatio += (val * 0.01);
                }
            }
            else if (m.type === Enums_1.BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //养由基 投喂强化
                addAttackRatio += (m.value * 0.06);
            }
            else if (m.type === Enums_1.BuffType.COURAGEOUSLY) { //典韦 奋勇
                attack += m.value;
            }
            else if (m.type === Enums_1.BuffType.KERIAN) { //辛弃疾 金戈
                addAttackRatio += (m.value * 0.01);
            }
        });
        // 韬略
        for (var key in this.strategyBuffMap) {
            var s = this.strategyBuffMap[key];
            if (s.type === 20001 || s.type === 40301 || s.type === 50010) {
                attack += s.value;
            }
            else if (s.type === 20003 || s.type === 50029) {
                addAttackRatio += (s.value * 0.01);
            }
        }
        // 提升比列
        if (addAttackRatio > 0) {
            attack += Math.round(attack * addAttackRatio);
        }
        // 降低比列
        if (lowAttackRatio > 0) {
            attack = Math.max(0, attack - Math.round(attack * lowAttackRatio));
        }
        return attack;
    };
    PawnObj.prototype.getInitMaxHp = function () {
        return this.maxHp;
    };
    PawnObj.prototype.getMaxHp = function () {
        return this.amendMaxHp();
    };
    PawnObj.prototype.amendMaxHp = function (ignoreBuffType) {
        var _this = this;
        if (ignoreBuffType === void 0) { ignoreBuffType = Enums_1.BuffType.NONE; }
        var hp = this.maxHp;
        var addHpRatio = 0, lowHpRatio = 0;
        this.buffs.forEach(function (m) {
            var _a, _b, _c;
            if (m.type === ignoreBuffType) {
                return;
            }
            else if (m.type === Enums_1.BuffType.DEFEND_HALO || m.type === Enums_1.BuffType.LV_1_POWER) {
                addHpRatio += (m.value * 0.01); //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.ADD_MAX_HP) {
                hp += m.value;
            }
            else if (m.type === Enums_1.BuffType.MORALE) { //曹操 士气
                var v = ((_a = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_CAO)) === null || _a === void 0 ? void 0 : _a.params) || 0;
                hp += (m.value * v);
            }
            else if (m.type === Enums_1.BuffType.TOUGH) { //曹仁 坚韧
                if (((_c = (_b = _this.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.CAO_REN) {
                    hp += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵
                addHpRatio += 0.1;
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //养由基 投喂强化
                addHpRatio += (m.value * 0.04);
            }
            else if (m.type === Enums_1.BuffType.TYRANNICAL) { //董卓 暴虐
                lowHpRatio += (m.value * 0.04);
            }
        });
        // 韬略
        for (var key in this.strategyBuffMap) {
            var s = this.strategyBuffMap[key];
            if (s.type === 20002) {
                hp += s.value;
            }
            else if (s.type === 50010) {
                hp += s.params;
            }
            else if (s.type === 20004) {
                addHpRatio += (s.value * 0.01);
            }
        }
        // 提升比列
        if (addHpRatio > 0) {
            hp += Math.round(hp * addHpRatio);
        }
        // 降低比列
        if (lowHpRatio > 0) {
            hp = Math.max(1, hp - Math.round(hp * lowHpRatio));
        }
        return hp;
    };
    PawnObj.prototype.getCadetLvText = function () {
        var _a;
        if (this.rodeleroCadetLv < 0) {
            return '0';
        }
        var actLv = ((_a = GameHelper_1.gameHpr.getPlayerInfo(this.owner || GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.rodeleroCadetLv) || 0;
        if (!this.uid) {
            return actLv;
        }
        var curLv = this.rodeleroCadetLv;
        if (actLv > curLv) {
            return curLv + "(+" + (actLv - curLv) + ")";
        }
        return actLv + '';
    };
    PawnObj.prototype.getPetId = function () {
        return this.petId;
    };
    PawnObj.prototype.setPetId = function (id) {
        this.petId = id;
    };
    // 改变军队
    PawnObj.prototype.changeArmy = function (data) {
        this.armyUid = data.uid;
        this.armyName = data.name;
    };
    // 是否英雄
    PawnObj.prototype.isHero = function () { return !!this.portrayal; };
    // 是否boss
    PawnObj.prototype.isBoss = function () { return (this.type === Enums_1.PawnType.BEAST || this.type === Enums_1.PawnType.CATERAN) && this.attrJson.move_range === 0; };
    // 是否器械
    PawnObj.prototype.isMachine = function () { return this.type === Enums_1.PawnType.MACHINE; };
    // 是否建筑
    PawnObj.prototype.isBuilding = function () { return this.type === Enums_1.PawnType.BUILD; };
    // 是否非战斗单位
    PawnObj.prototype.isNoncombat = function () { return this.type === Enums_1.PawnType.NONCOMBAT; };
    // 获取血条预制体url
    PawnObj.prototype.getHPBarPrefabUrl = function () {
        if (this.isHero()) {
            return 'pawn/HERO_HP_BAR';
        }
        else if (this.isBoss()) {
            return 'pawn/BOSS_HP_BAR';
        }
        return 'pawn/HP_BAR';
    };
    // 是否可以穿装备
    PawnObj.prototype.isCanWearEquip = function () {
        var _a;
        return (this.type < Enums_1.PawnType.MACHINE || this.isBoss()) && (!!this.owner || !!((_a = this.equip) === null || _a === void 0 ? void 0 : _a.id));
    };
    // 是否满级
    PawnObj.prototype.isMaxLv = function () {
        var _a;
        return !((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.lv_cost);
    };
    // 是否自己的士兵
    PawnObj.prototype.isOwner = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否战斗中
    PawnObj.prototype.isBattleing = function () {
        var _a;
        return ((_a = this.state) === null || _a === void 0 ? void 0 : _a.type) >= Enums_1.PawnState.STAND || this.aIndex < 0;
    };
    // 获取战斗阵营
    PawnObj.prototype.getBattleCamp = function () {
        var _a, _b, _c;
        var ctrl = (_b = (_a = GameHelper_1.gameHpr.areaCenter.getArea(this.aIndex)) === null || _a === void 0 ? void 0 : _a.getFspModel()) === null || _b === void 0 ? void 0 : _b.getBattleController();
        return (_c = ctrl === null || ctrl === void 0 ? void 0 : ctrl.getFighterCampIndex(this.uid)) !== null && _c !== void 0 ? _c : -1;
    };
    PawnObj.prototype.getHpText = function () {
        var maxHp = this.getMaxHp();
        var curHp = this.uid ? this.curHp : maxHp;
        return curHp + '/' + maxHp;
    };
    PawnObj.prototype.getHpRatio = function () {
        var maxHp = this.getMaxHp();
        return maxHp > 0 ? this.curHp / maxHp : 0;
    };
    PawnObj.prototype.getMaxAnger = function () {
        var _a;
        if (this.maxAnger === 0) {
            return 0;
        }
        var maxAnger = this.maxAnger;
        // 吕蒙 -50%怒气
        if (((_a = this.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.LV_MENG) {
            maxAnger = Math.round(this.maxAnger * 0.5);
        }
        // 韬略 怒气-1
        if (this.isHasStrategys(40102, 40201, 40303, 40401)) {
            maxAnger = Math.max(1, maxAnger - 1);
        }
        return maxAnger;
    };
    PawnObj.prototype.getCurAnger = function () {
        return this.curAnger;
    };
    PawnObj.prototype.getAngerText = function () {
        return this.maxAnger > 0 ? this.curAnger + '/' + this.getMaxAnger() : '0/0';
    };
    PawnObj.prototype.getAngerRatio = function () {
        return this.maxAnger > 0 ? this.curAnger / this.getMaxAnger() : 0;
    };
    PawnObj.prototype.isDie = function () {
        return this.curHp <= 0 && this.maxHp > 0;
    };
    PawnObj.prototype.getState = function () {
        var _a;
        return ((_a = this.state) === null || _a === void 0 ? void 0 : _a.type) || Enums_1.PawnState.NONE;
    };
    // 改变状态
    PawnObj.prototype.changeState = function (state, data, init) {
        if (!this.state) {
            this.state = new PawnStateObj_1.default();
        }
        this.state.init(state, data);
        // cc.log('changeState', this.uid, this.point.ID(), PawnState[state], data)
        // 非战斗状态
        if (!init && !this.isBattleing()) {
            this.initAnger();
            this.cleanAllBuffs();
            this.tempAttackAnimTimes = null;
        }
    };
    // 设置画像
    PawnObj.prototype.setPortrayal = function (data) {
        var _a;
        if (((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) !== data.id) {
            this.skinId = 0; //有画像就一定没有皮肤
            this.portrayal = new PortrayalInfo_1.default().fromSvr(data);
            this.updateAttr();
            this.initAnger();
            eventCenter.emit(EventType_1.default.CHANGE_PAWN_PORTRAYAL, this);
        }
    };
    // 改变攻击速度
    PawnObj.prototype.changeAttackSpeed = function (val) {
        var num = this.attackSpeed + val;
        if (num < 1) {
            num = 9;
        }
        else if (num > 9) {
            num = 1;
        }
        this.attackSpeed = num;
    };
    PawnObj.prototype.setAttackSpeed = function (val) {
        this.attackSpeed = val;
    };
    PawnObj.prototype.changeSkin = function (skinId) {
        if (this.skinId !== skinId) {
            this.skinId = skinId;
            eventCenter.emit(EventType_1.default.CHANGE_PAWN_SKIN, this);
        }
    };
    // 切换装备
    PawnObj.prototype.changeEquip = function (data, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (this.equip.uid !== data.uid) {
            this.equip.setId(data.uid, data.id);
            this.updateEquipAttr(this.equip.uid, data.attrs);
            isEmit && eventCenter.emit(EventType_1.default.CHANGE_PAWN_EQUIP, this);
        }
    };
    // 刷新装备信息
    PawnObj.prototype.updateEquipAttr = function (uid, attrs) {
        if (!this.equip.uid || !attrs) {
            this.equip.setAttr([]);
        }
        else if (this.equip.uid === uid) {
            // 如果是专属 检测自己是否可以携带
            if (!this.equip.isExclusive() || this.equip.checkExclusivePawn(this.id)) {
                this.equip.setAttr(attrs);
            }
            else {
                this.equip.clean();
            }
        }
        this.updateAttr();
    };
    // 刷新英雄信息
    PawnObj.prototype.updateHeroAttr = function (id, attrs) {
        var _a;
        if (((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) !== id || !attrs) {
            return;
        }
        this.portrayal.setAttr(attrs);
        this.updateAttr();
    };
    // 刷新属性 这个只是前端模拟 还是需要以服务器数据为准
    // 一般如果在场景没有战斗并改变属性的时候可以前端自行计算
    PawnObj.prototype.updateAttr = function (init) {
        var _this = this;
        var _a, _b, _c, _d;
        if (!init && this.isBattleing()) {
            return;
        }
        var maxHp = this.attrJson.hp || 0, attack = this.attrJson.attack || 0, attackRange = this.attrJson.attack_range || 0, moveRange = this.attrJson.move_range || 0;
        this.maxHp = maxHp;
        this.attack = attack;
        this.attackRange = attackRange;
        this.moveRange = moveRange;
        var runDay = GameHelper_1.gameHpr.getServerRunDay();
        var addHpRatio = 0, addAttackRatio = 0;
        // 加上装备的
        this.getEquipEffects().forEach(function (m) {
            if (m.type === Enums_1.EquipEffectType.MINGGUANG_ARMOR) { //提高生命
                _this.maxHp += m.value * 10;
            }
            else if (m.type === Enums_1.EquipEffectType.BAIBI_SWORD) { //提高攻击
                _this.attack += m.value;
            }
            else if (m.type === Enums_1.EquipEffectType.TODAY_ADD_HP) { //根据服务器运行时间加生命
                _this.maxHp += m.value * runDay;
            }
            else if (m.type === Enums_1.EquipEffectType.TODAY_ADD_ATTACK) { //根据服务器运行时间加攻击
                _this.attack += m.value * runDay;
            }
            else if (m.type === Enums_1.EquipEffectType.CENTERING_HELMET) { //提高生命比例
                addHpRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.EquipEffectType.LONGYUAN_SWORD) { //提高攻击比例
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.EquipEffectType.NOT_DODGE) { //攻击范围 +1
                _this.attackRange += 1;
            }
            else if (m.type === Enums_1.EquipEffectType.ADD_MOVE_RANGE) { //移动范围 +1
                _this.moveRange += 1;
            }
        });
        this.maxHp += this.equip.hp;
        this.attack += this.equip.attack;
        // 画像属性
        if (this.portrayal) {
            this.maxHp += this.portrayal.hp;
            this.attack += this.portrayal.attack;
            // 裴行俨 自带攻击力
            if (((_a = this.portrayal.skill) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.PEI_XINGYAN) {
                addAttackRatio += this.portrayal.skill.value * 0.01;
            }
        }
        // 见习勇者
        if (this.rodeleroCadetLv > 0) {
            var max = ((_b = this.getSkillByType(Enums_1.PawnSkillType.CADET)) === null || _b === void 0 ? void 0 : _b.value) || 0;
            var cadetLv = Math.min(this.rodeleroCadetLv, max);
            this.maxHp += cadetLv * 5;
            this.attack += Math.round(cadetLv * 0.5);
        }
        // 生命 提升比列
        if (addHpRatio > 0) {
            this.maxHp += Math.round(this.maxHp * addHpRatio);
        }
        // 攻击 提升比例
        if (addAttackRatio > 0) {
            this.attack += Math.round(this.attack * addAttackRatio);
        }
        // 是否有技能强化
        if (this.equip.skillIntensify) {
            var _e = __read(this.equip.skillIntensify, 2), id_1 = _e[0], type = _e[1];
            (_c = this.skills.find(function (m) { return m.baseId === id_1; })) === null || _c === void 0 ? void 0 : _c.setIntensifyType(type);
        }
        else {
            this.skills.forEach(function (m) { return m.setIntensifyType(0); });
        }
        // 如果是主城或配置士兵直接满血
        if ((_d = GameHelper_1.gameHpr.world.getMapCellByIndex(this.aIndex)) === null || _d === void 0 ? void 0 : _d.isRecoverPawnHP()) {
            this.curHp = this.getMaxHp();
        }
        else if (this.tempCurrHp >= 0) {
            this.curHp = Math.min(this.tempCurrHp, this.getMaxHp());
        }
        else {
            this.curHp = Math.min(this.curHp, this.getMaxHp());
        }
    };
    // 获取技能信息
    PawnObj.prototype.getSkillByType = function (type) {
        return this.skills.find(function (m) { return m.type === type; });
    };
    // 获取主动技能
    PawnObj.prototype.getActiveSkill = function () {
        return this.skills.find(function (m) { return m.use_type === 1 || m.type === Enums_1.PawnSkillType.FULL_STRING || m.type === Enums_1.PawnSkillType.LONGITUDINAL_CLEFT; }); //目前满弦、顺劈也算主动
    };
    PawnObj.prototype.setBuffs = function (buffs) {
        this.buffs = buffs.map(function (m) { return new BuffObj_1.default().fromSvr(m); });
    };
    PawnObj.prototype.addBuffs = function (buffs) {
        var _this = this;
        if (buffs === null || buffs === void 0 ? void 0 : buffs.length) {
            var buffMap_1 = {};
            this.buffs.forEach(function (m) { return buffMap_1[m.type] = m; });
            buffs.forEach(function (m) {
                var buff = buffMap_1[m.type];
                if (buff) {
                    buff.fromSvr(m);
                }
                else {
                    _this.buffs.push(new BuffObj_1.default().fromSvr(m));
                }
            });
        }
    };
    PawnObj.prototype.isHasBuff = function (type) {
        return this.buffs.some(function (m) { return m.type === type; });
    };
    PawnObj.prototype.getBuffValue = function (type) {
        var _a;
        return ((_a = this.buffs.find(function (m) { return m.type === type; })) === null || _a === void 0 ? void 0 : _a.value) || 0;
    };
    PawnObj.prototype.cleanAllBuffs = function () {
        this.cleanBuffs();
        this.cleanStrategyBuffs();
    };
    PawnObj.prototype.cleanBuffs = function () {
        this.buffs.length = 0;
    };
    // 获取护盾值
    PawnObj.prototype.getShieldValue = function () {
        var val = 0;
        this.buffs.forEach(function (m) {
            if (m.isHasShield()) {
                val += m.value;
            }
        });
        return val;
    };
    PawnObj.prototype.cleanStrategyBuffs = function () {
        this.strategyBuffMap = {};
    };
    PawnObj.prototype.addStrategyBuff = function (strategy) {
        this.strategyBuffMap[strategy.type] = strategy;
    };
    PawnObj.prototype.getStrategyBuff = function (tp) {
        return this.strategyBuffMap[tp];
    };
    // 获取韬略数值
    PawnObj.prototype.getStrategyValue = function (tp) {
        var _a;
        return ((_a = this.getStrategyBuff(tp)) === null || _a === void 0 ? void 0 : _a.value) || 0;
    };
    // 是否有某个韬略
    PawnObj.prototype.isHasStrategys = function () {
        var _this = this;
        var tps = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            tps[_i] = arguments[_i];
        }
        return tps.some(function (m) { return !!_this.strategyBuffMap[m]; });
    };
    PawnObj.prototype.isHasStrategy = function () {
        return !ut.isEmptyObject(this.strategyBuffMap);
    };
    PawnObj.prototype.initAnger = function () {
        var _a, _b;
        this.curAnger = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.init_anger) || 0;
        if (this.curAnger > 0) {
            // 吕蒙
            if (((_b = this.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.LV_MENG) {
                this.curAnger = Math.round(this.curAnger * 0.5);
            }
        }
        return this;
    };
    PawnObj.prototype.isHasAnger = function () {
        return this.maxAnger > 0;
    };
    // 获取装备效果列表
    PawnObj.prototype.getEquipEffects = function () {
        var _a;
        return (_a = this.equip) === null || _a === void 0 ? void 0 : _a.effects;
    };
    // 获取某个装备效果
    PawnObj.prototype.getEquipEffectByType = function (type) {
        var _a;
        return (_a = this.equip) === null || _a === void 0 ? void 0 : _a.effects.find(function (m) { return m.type === type; });
    };
    // 刷新宝箱
    PawnObj.prototype.updateTreasures = function (treasures) {
        var _this = this;
        this.treasures = (treasures || []).map(function (m) { return GameHelper_1.gameHpr.fromSvrTreasureInfo(m, _this.aIndex, _this.armyUid, _this.uid); });
    };
    return PawnObj;
}());
exports.default = PawnObj;

cc._RF.pop();