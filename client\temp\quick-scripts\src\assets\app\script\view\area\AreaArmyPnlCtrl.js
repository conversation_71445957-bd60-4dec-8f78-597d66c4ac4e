"use strict";
cc._RF.push(module, 'ec3dakw2/tDeJ/37Uxmro8h', 'AreaArmyPnlCtrl');
// app/script/view/area/AreaArmyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var AreaArmyPnlCtrl = /** @class */ (function (_super) {
    __extends(AreaArmyPnlCtrl, _super);
    function AreaArmyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabLbl_ = null; // path://root/info/bg/tab_l
        _this.showPawnEquipTge_ = null; // path://root/info/show_pawn_equip_te_t
        _this.listSv_ = null; // path://root/list_sv
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.PKEY_TAB = 'AREA_ARMY_TAB';
        _this.tab = 0;
        _this.area = null;
        _this.player = null;
        _this.preShowPawnEquip = false;
        _this.hpBarList = [];
        return _this;
    }
    AreaArmyPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PAWN_TREASURE] = this.onUpdatePawnTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY_TREASURES] = this.onUpdateArmyTreasures, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_ARMY_NAME] = this.onUpdateArmyName, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_AREA_ARMY_LIST] = this.onUpdateAreaArmyList, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.REMOVE_ARMY] = this.onUpdateAreaArmyList, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdateAreaArmyList, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_PAWN_LVING_QUEUE] = this.onUpdateAreaArmyList, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdateAreaArmyList, _h.enter = true, _h),
        ];
    };
    AreaArmyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    AreaArmyPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        var area = this.area = GameHelper_1.gameHpr.areaCenter.getLookArea();
        if (!area) {
            return this.hide();
        }
        this.preShowPawnEquip = this.showPawnEquipTge_.isChecked = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED)) !== null && _a !== void 0 ? _a : false;
        var _b = __read(this.updateArmyCount(), 2), defCount = _b[0], atkCount = _b[1];
        var tabStr = GameHelper_1.gameHpr.user.getTempPreferenceMap(this.PKEY_TAB) || '';
        var _c = __read(ut.stringToNumbers(tabStr, '_'), 2), index = _c[0], type = _c[1];
        if (index !== area.index) {
            this.listSv_.node.Data = -1;
            if (GameHelper_1.gameHpr.checkIsOneAlliance(area.owner)) {
                type = defCount > 0 || atkCount === 0 ? 0 : 1;
            }
            else {
                type = atkCount > 0 || defCount === 0 ? 1 : 0;
            }
        }
        this.tabsTc_.Tabs(type !== null && type !== void 0 ? type : 0);
    };
    AreaArmyPnlCtrl.prototype.onRemove = function () {
        this.hpBarList = [];
        this.area = null;
        if (this.preShowPawnEquip !== this.showPawnEquipTge_.isChecked) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED, this.showPawnEquipTge_.isChecked);
        }
    };
    AreaArmyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/treasure_be
    AreaArmyPnlCtrl.prototype.onClickTreasure = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (data.owner !== GameHelper_1.gameHpr.getUid()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_OPEN_OTHER_TREASURE);
        }
        var treasures = data === null || data === void 0 ? void 0 : data.getAllPawnTreasures();
        if (treasures && treasures.length > 0) {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', treasures);
        }
    };
    // path://root/list_sv/view/content/item/name/edit/edit_name_be
    AreaArmyPnlCtrl.prototype.onClickEditName = function (event, _) {
        var data = event.target.parent.parent.parent.parent.Data;
        if (!data || !data.isOwner()) {
        }
        else if (this.area.isBattleing()) {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('area/EditArmyName', data);
        }
    };
    // path://root/list_sv/view/content/item/pawns/pawn_be
    AreaArmyPnlCtrl.prototype.onClickPawn = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (!data) {
        }
        else if (data.drillInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByDrillInfo(data.drillInfo), data.drillInfo, 'area_army');
        }
        else if (data.curingInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByCureInfo(data.curingInfo), data.curingInfo, 'area_army');
        }
        else if (data.lvingInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByLvingInfo(data.pawn, data.lvingInfo), data.lvingInfo, 'area_army');
        }
        else if (data.pawn) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', data.pawn, null, 'area_army');
        }
    };
    // path://root/info/show_pawn_equip_te_t
    AreaArmyPnlCtrl.prototype.onClickShowPawnEquip = function (event, data) {
        audioMgr.playSFX('click');
        this.updateArmyList();
        ViewHelper_1.viewHelper.showNoLongerTip('area_army_show_equip', { content: 'ui.area_army_show_equip_tip' });
    };
    // path://root/list_sv/view/content/item/march_speed_be
    AreaArmyPnlCtrl.prototype.onClickMarchSpeed = function (event, _) {
        var _this = this;
        var it = event.target.parent.parent;
        var data = it === null || it === void 0 ? void 0 : it.Data;
        if (!data) {
            return;
        }
        else if (this.area.isBattleing()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        ViewHelper_1.viewHelper.showPnl('main/ModifyMarchSpeed', data, function (speed) {
            if (speed) {
                data.marchSpeed = speed;
                if (_this.isValid) {
                    _this.updateArmyMarchSpeed(it.Child('top'), data);
                }
            }
        });
    };
    // path://root/list_sv/view/content/item/force_revoke_be
    AreaArmyPnlCtrl.prototype.onClickForceRevoke = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (data.owner === GameHelper_1.gameHpr.getUid() || data.aIndex !== GameHelper_1.gameHpr.player.getMainCityIndex() || data.isBattleing()) {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.force_revoke_tip', {
            params: [GameHelper_1.gameHpr.getPlayerName(data.owner), data.name],
            ok: function () {
                GameHelper_1.gameHpr.world.forceRevoke(data.uid).then(function (err) {
                    if (err) {
                        return ViewHelper_1.viewHelper.showAlert(err);
                    }
                    else if (_this.isValid) {
                        // this.hide()
                    }
                });
            },
            cancel: function () { }
        });
    };
    // path://root/tabs_tc_tce
    AreaArmyPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var tab = this.tab = Number(event.node.name);
        GameHelper_1.gameHpr.user.setTempPreferenceData(this.PKEY_TAB, this.area.index + '_' + tab);
        this.updateArmyList();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新士兵宝箱
    AreaArmyPnlCtrl.prototype.onUpdatePawnTreasure = function (pawn) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === pawn.armyUid; });
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data);
        }
    };
    // 刷新军队宝箱
    AreaArmyPnlCtrl.prototype.onUpdateArmyTreasures = function (army) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === army.uid; });
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data);
        }
    };
    // 刷新军队名字
    AreaArmyPnlCtrl.prototype.onUpdateArmyName = function (uid, name) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid; });
        if (it) {
            it.Child('top').Child('name/val', cc.Label).string = name;
        }
    };
    AreaArmyPnlCtrl.prototype.onUpdateAreaArmyList = function (index) {
        if (this.area.index === index) {
            this.updateArmyCount();
            this.updateArmyList();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新军队数量
    AreaArmyPnlCtrl.prototype.updateArmyCount = function () {
        var owner = this.area.owner, maxArmyCount = this.area.maxArmyCount;
        var defCount = 0, atkCount = 0;
        // 计算各个军队数量
        this.area.armys.forEach(function (m) {
            if (m.getPawnActCount() === 0) {
                return;
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defCount += 1;
            }
            else {
                atkCount += 1;
            }
        });
        // 从这个区域开始行军的军队数量
        var index = this.area.index;
        GameHelper_1.gameHpr.world.getMarchs().filter(function (m) { return m.armyIndex === index; }).forEach(function (m) {
            if (m.autoRevoke) {
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defCount += 1;
            }
            else {
                atkCount += 1;
            }
        });
        this.tabsTc_.Child('0/Background/lay/count', cc.Label).string = "(" + defCount + "/" + maxArmyCount + ")";
        this.tabsTc_.Child('0/checkmark/lay/count', cc.Label).string = "(" + defCount + "/" + maxArmyCount + ")";
        this.tabsTc_.Child('1/Background/lay/count', cc.Label).string = "(" + atkCount + "/" + maxArmyCount + ")";
        this.tabsTc_.Child('1/checkmark/lay/count', cc.Label).string = "(" + atkCount + "/" + maxArmyCount + ")";
        return [defCount, atkCount];
    };
    // 刷新军队列表
    AreaArmyPnlCtrl.prototype.updateArmyList = function () {
        var _this = this;
        this.tabLbl_.setLocaleKey('ui.area_army_' + this.tab);
        var marchs = {};
        GameHelper_1.gameHpr.world.getMarchs().forEach(function (m) { return marchs[m.armyUid] = true; });
        var owner = this.area.owner, uid = GameHelper_1.gameHpr.getUid();
        var arr = this.area.armys.filter(function (m) {
            if (m.pawns.length === 0 && m.owner !== uid) {
                return false;
            }
            return !_this.tab === GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner);
        });
        var pawnDrillMap = {}, lvingPawnLvMap = {}, curingPawnLvMap = {};
        this.player.getAllPawnDrillList().forEach(function (m) {
            var p = pawnDrillMap[m.auid];
            if (!p) {
                p = pawnDrillMap[m.auid] = [];
            }
            p.push(m);
        });
        this.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m; });
        this.player.getCuringPawnsQueue().forEach(function (m) { return curingPawnLvMap[m.uid] = m; });
        this.listSv_.Child('empty').setActive(arr.length === 0);
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode, showEquip = this.showPawnEquipTge_.isChecked;
        var mainCityIndex = GameHelper_1.gameHpr.player.getMainCityIndex(), isAncient = this.area.isAncient();
        this.hpBarList = [];
        if (this.listSv_.node.Data !== this.tab) {
            this.listSv_.node.Data = this.tab;
            this.listSv_.stopAutoScroll();
            this.listSv_.content.y = 0;
        }
        this.listSv_.Items(arr, function (it, data) {
            var _a;
            it.Data = data;
            var top = it.Child('top'), bottom = it.Child('bottom');
            var pawns = data.pawns, isHasLving = false, isOwner = data.isOwner(), isOneAlliance = GameHelper_1.gameHpr.checkIsOneAlliance(data.owner, uid);
            it.Color(isOwner ? '#E9DDC7' : isOneAlliance ? '#DAEBDD' : '#F6D6CD');
            var armyName = data.owner ? data.name : isAncient ? assetsMgr.lang('ui.ancient_army_name') : assetsMgr.lang(pawns[0] ? 'ui.pawn_type_' + (pawns[0].type || 6) : 'ui.neutral_pawn');
            top.Child('name/val', cc.Label).Color(isOwner ? '#564C49' : isOneAlliance ? '#4A85D5' : '#D54A4A').string = armyName;
            top.Child('name/edit').active = isOwner && !isNoviceMode;
            var other = top.Child('name/other'), alli = top.Child('alli');
            if (other.active = !isOwner && !!data.owner) {
                var plr = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
                if (plr) {
                    ResHelper_1.resHelper.loadPlayerHead(other.Child('head'), plr.headIcon, _this.key);
                    other.Child('name', cc.Label).string = ut.nameFormator(plr.nickname, 7);
                    // 联盟
                    if (alli.active = !!plr.allianceUid && !isOneAlliance) {
                        ResHelper_1.resHelper.loadAlliIcon(plr.allianceIcon, alli.Child('icon'), _this.key);
                        alli.Child('name', cc.Label).string = plr.allianceName;
                    }
                }
                else {
                    other.active = alli.active = false;
                }
            }
            else {
                alli.active = false;
            }
            var drills = ((_a = pawnDrillMap[data.uid]) === null || _a === void 0 ? void 0 : _a.slice()) || [];
            var list = isOwner ? pawns.concat(data.drillPawns).concat(data.curingPawns) : pawns;
            it.Child('pawns').Items(list, function (node, pawn) {
                var _a, _b;
                var icon = node.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                if (isId) {
                    node.Data = { id: pawn, drillInfo: drills.remove('id', pawn) };
                }
                else if (isCuring) {
                    node.Data = { id: pawn, curingInfo: curingPawnLvMap[pawn.uid] };
                }
                else {
                    node.Data = { pawn: pawn, id: pawn.id, lvingInfo: lvingPawnLvMap[pawn.uid] };
                }
                var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                var lv = isLving ? (_a = lvingPawnLvMap[pawn.uid]) === null || _a === void 0 ? void 0 : _a.lv : (isId ? 1 : pawn.lv);
                icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_b = pawn.portrayal) === null || _b === void 0 ? void 0 : _b.id) || pawn.id), icon, _this.key, false);
                node.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                if (node.Child('hp').active = (!isId && !isCuring)) {
                    var spr = node.Child('hp/bar', cc.Sprite);
                    spr.fillRange = pawn.getHpRatio();
                    _this.hpBarList.push({ bar: spr, pawn: pawn });
                }
                var showNode = node.Child('show');
                if (showNode.active = showEquip) {
                    // 出手速度
                    showNode.Child('speed', cc.Label).string = (isId || isCuring) ? '' : '' + pawn.attackSpeed;
                    // 装备
                    if (showNode.Child('equip').active = !isId && !isCuring && !!(pawn === null || pawn === void 0 ? void 0 : pawn.isCanWearEquip())) {
                        var spr = showNode.Child('equip/val', cc.Sprite), equip = pawn.equip;
                        if (equip === null || equip === void 0 ? void 0 : equip.id) {
                            ResHelper_1.resHelper.loadEquipIcon(equip.id, spr, _this.key, equip.getSmeltCount());
                        }
                        else {
                            spr.spriteFrame = null;
                        }
                    }
                }
                if (isLving) {
                    isHasLving = true;
                }
            });
            bottom.Child('force_revoke_be').active = data.aIndex === mainCityIndex && !isOwner && !data.isBattleing();
            ViewHelper_1.viewHelper.updateArmyState(bottom, data, marchs[data.uid], isHasLving, data.isOwner());
            _this.updateArmyMarchSpeed(top, data);
            _this.updateArmyTreasure(bottom, data);
        });
    };
    // 行军速度
    AreaArmyPnlCtrl.prototype.updateArmyMarchSpeed = function (it, data) {
        var node = it.Child('march_speed_be'), isOneAlliance = GameHelper_1.gameHpr.checkIsOneAlliance(data.owner);
        if (node.active = isOneAlliance && !GameHelper_1.gameHpr.isNoviceMode && data.pawns.length > 0 && data.defaultMarchSpeed > 0) {
            var marchSpeedLbl = node.Component(cc.Label), line = node.Child('line'), isOwner = data.isOwner();
            node.Color(/* isOwner &&  */ data.marchSpeed === data.defaultMarchSpeed ? '#936E5A' : '#B6A591').setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            node.Component(cc.Button).interactable = isOwner;
            if (line.active = isOwner) {
                marchSpeedLbl._forceUpdateRenderData();
                line.width = marchSpeedLbl.node.width;
            }
        }
    };
    // 刷新宝箱信息
    AreaArmyPnlCtrl.prototype.updateArmyTreasure = function (it, data) {
        var node = it.Child('treasure_be'), treasureCount = data.getAllPawnTreasureCount();
        if (node.active = treasureCount > 0 && data.isOwner()) {
            node.Child('treasure', TextButtonCmpt_1.default).setKey('ui.get_treasure_count', treasureCount);
        }
    };
    AreaArmyPnlCtrl.prototype.update = function (dt) {
        this.hpBarList.forEach(function (m) {
            if (m.pawn) {
                m.bar.fillRange = m.pawn.getHpRatio();
            }
        });
    };
    AreaArmyPnlCtrl = __decorate([
        ccclass
    ], AreaArmyPnlCtrl);
    return AreaArmyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AreaArmyPnlCtrl;

cc._RF.pop();