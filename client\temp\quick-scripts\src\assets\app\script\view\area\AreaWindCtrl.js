"use strict";
cc._RF.push(module, '9ec9aBNxbRL7r2yUkdcDY7W', 'AreaWindCtrl');
// app/script/view/area/AreaWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var PawnCmpt_1 = require("./PawnCmpt");
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var NetEvent_1 = require("../../common/event/NetEvent");
var HPBarCmpt_1 = require("./HPBarCmpt");
var Constant_1 = require("../../common/constant/Constant");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var Enums_1 = require("../../common/constant/Enums");
var ECode_1 = require("../../common/constant/ECode");
var SearchCircle_1 = require("../../common/astar/SearchCircle");
var GameHelper_1 = require("../../common/helper/GameHelper");
var SelectCellCmpt_1 = require("../cmpt/SelectCellCmpt");
var NetHelper_1 = require("../../common/helper/NetHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var ccclass = cc._decorator.ccclass;
var AreaWindCtrl = /** @class */ (function (_super) {
    __extends(AreaWindCtrl, _super);
    function AreaWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.gridNode_ = null; // path://root/map_n/grid_n
        _this.skillDiNode_ = null; // path://root/map_n/skill_di_n
        _this.buildNode_ = null; // path://root/map_n/build_n
        _this.selectPawnNode_ = null; // path://root/select_pawn_n
        _this.roleNode_ = null; // path://root/role_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        _this.editJiantouNode_ = null; // path://root/edit_jiantou_n
        _this.weakGuideNode_ = null; // path://root/weak_guide_n
        //@end
        _this.PAWN_SIZE = cc.v2(1, 1);
        _this.diNode = null;
        _this.maskNode = null;
        _this.touchCmpt = null;
        _this.model = null;
        _this.areaCenter = null;
        _this.centre = cc.v2();
        _this.preCameraZoomRatio = 0;
        _this.areaSize = cc.v2(); //战场大小
        _this.buildSize = cc.v2(); //建筑区域
        _this.areaActSize = cc.v2(); //战场的实际大小
        _this.borderSize = cc.v2(); //地图边框宽度
        _this.buildOrigin = cc.v2(); //建筑起点
        _this.walls = []; //城墙列表
        _this.flames = []; //火焰列表
        _this.alliFlags = []; //联盟旗帜
        _this.areaOutDecorate = []; //装饰
        _this.builds = []; //建筑列表
        _this.buildMap = {};
        _this.pawns = []; //士兵列表
        _this.pawnMap = {};
        _this.wallLvNode = null;
        _this.hpBar = null; //血条
        _this.currEditBuild = null; //当前编辑的建筑
        _this.currEditPawn = null; //当前编辑的士兵
        _this.searchCircle = null;
        _this.isPawnMoveing = false; //当前是否士兵移动中
        _this.editPawns = {}; //编辑过的士兵列表
        _this.tempSeasonType = 0;
        _this._temp_vec2_0 = cc.v2();
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        return _this;
    }
    AreaWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.REENTER_AREA_WIND] = this.onReenterAreaWind, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.SHOW_BUILD_JIANTOU] = this.onShowBuildJiantou, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.LONG_PRESS_BUILD] = this.onLongPressBuild, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.MOVE_BUILD] = this.onMoveBuild, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CLICK_EDIT_BUILD_MENU] = this.onClickEditBuildMenu, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.EDIT_PAWN_POS] = this.onEditPawnPos, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.CLICK_EDIT_PAWN_MENU] = this.onClickEditPawnMenu, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.ADD_BUILD] = this.onAddBuild, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.REMOVE_BUILD] = this.onRemoveBuild, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_BUILD_POINT] = this.onUpdateBuildPoint, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_BUILDS] = this.onUpdateBuilds, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_AREA_HP] = this.onUpdateAreaHp, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.UPDATE_ANCIENT_INFO] = this.onUpdateAncientInfo, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.ADD_ARMY] = this.onAddArmy, _r.enter = true, _r),
            (_s = {}, _s[EventType_1.default.ADD_PAWN] = this.onAddPawn, _s.enter = true, _s),
            (_t = {}, _t[EventType_1.default.REMOVE_ARMY] = this.onRemoveArmy, _t.enter = true, _t),
            (_u = {}, _u[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _u.enter = true, _u),
            (_v = {}, _v[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateAllArmy, _v.enter = true, _v),
            (_w = {}, _w[EventType_1.default.REMOVE_PAWN] = this.onRemovePawn, _w.enter = true, _w),
            (_x = {}, _x[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _x.enter = true, _x),
            (_y = {}, _y[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _y.enter = true, _y),
            (_z = {}, _z[EventType_1.default.AREA_MAIN_HIT] = this.onAreaMainHit, _z.enter = true, _z),
            (_0 = {}, _0[EventType_1.default.PLAY_FLUTTER_HP] = this.onPlayFlutterHp, _0.enter = true, _0),
            (_1 = {}, _1[EventType_1.default.PLAY_FLUTTER_ANGER] = this.onPlayFlutterAnger, _1.enter = true, _1),
            (_2 = {}, _2[EventType_1.default.PLAY_BULLET_FLY] = this.onPlayBulletFly, _2.enter = true, _2),
            (_3 = {}, _3[EventType_1.default.PLAY_BATTLE_EFFECT] = this.onPlayBattleEffect, _3.enter = true, _3),
            (_4 = {}, _4[EventType_1.default.PLAY_BATTLE_SFX] = this.onPlayBattleSfx, _4.enter = true, _4),
            (_5 = {}, _5[EventType_1.default.PLAY_BATTLE_SCENE_SHAKE] = this.onPlayBattleSceneShake, _5.enter = true, _5),
            (_6 = {}, _6[EventType_1.default.FOCUS_PAWN] = this.onFocusPawn, _6.enter = true, _6),
            (_7 = {}, _7[EventType_1.default.UPDATE_BT_QUEUE] = this.onUpdateBtQueue, _7.enter = true, _7),
            (_8 = {}, _8[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdatePawnDrillQueue, _8.enter = true, _8),
            (_9 = {}, _9[EventType_1.default.UPDATE_PAWN_LVING_QUEUE] = this.onUpdatePawnDrillQueue, _9.enter = true, _9),
            (_10 = {}, _10[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdatePawnDrillQueue, _10.enter = true, _10),
            (_11 = {}, _11[EventType_1.default.CHANGE_SHOW_PAWN_LV] = this.onChangeShowPawnLv, _11.enter = true, _11),
            (_12 = {}, _12[EventType_1.default.CHANGE_SHOW_PAWN_EQUIP] = this.onChangeShowPawnEquip, _12.enter = true, _12),
            (_13 = {}, _13[EventType_1.default.CHANGE_PAWN_EQUIP] = this.onChangePawnEquip, _13.enter = true, _13),
            (_14 = {}, _14[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _14.enter = true, _14),
            (_15 = {}, _15[EventType_1.default.CHANGE_PAWN_PORTRAYAL] = this.onChangePawnPortrayal, _15.enter = true, _15),
            (_16 = {}, _16[EventType_1.default.FORGE_EQUIP_BEGIN] = this.onForgeEquipBegin, _16.enter = true, _16),
            (_17 = {}, _17[EventType_1.default.FORGE_EQUIP_COMPLETE] = this.onForgeEquipComplete, _17.enter = true, _17),
            (_18 = {}, _18[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _18.enter = true, _18),
        ];
    };
    AreaWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.setParam({ isClean: false });
                        this.areaCenter = this.getModel('areaCenter');
                        this.diNode = this.mapNode_.FindChild('di');
                        this.maskNode = this.mapNode_.FindChild('mask');
                        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                        this.selectPawnNode_.active = false;
                        this.editJiantouNode_.active = false;
                        this.gridNode_.active = false;
                        // 加载UI
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadPnl('area/AreaUI')];
                    case 1:
                        // 加载UI
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.onReady = function () {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var _c, range, count;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        _c = this;
                        return [4 /*yield*/, this.areaCenter.reqAreaByIndex((_b = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : -1)];
                    case 1:
                        _c.model = _d.sent();
                        if (!this.model) {
                            return [2 /*return*/];
                        }
                        this.areaCenter.setLookArea(this.model);
                        // 区域大小
                        this.areaSize.set(this.model.areaSize);
                        this.buildSize.set(this.model.buildSize);
                        // 获取地图边框的宽度 至少都有2格
                        this.model.getBorderSize(this.borderSize);
                        // 重新计算地图的真实大小
                        this.borderSize.mul(2, this.areaActSize).addSelf(this.areaSize);
                        // 计算建筑的起点
                        this.model.buildOrigin.add(this.borderSize, this.buildOrigin);
                        range = GameHelper_1.gameHpr.world.getMaxTileRange(), count = (range.x * 2 + 1) * (range.y * 2 + 1);
                        this.diNode.Items(count);
                        // 初始化城墙
                        return [4 /*yield*/, this.initWall()];
                    case 2:
                        // 初始化城墙
                        _d.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.onEnter = function (reenter) {
        if (!this.model) {
            ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey());
            if (GameHelper_1.gameHpr.net.isConnected()) {
                ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.UNKNOWN);
            }
            return;
        }
        this.buildNode_.Data = true;
        this.topLayerNode_.Data = true;
        this.model.setActive(true);
        this.tempSeasonType = GameHelper_1.gameHpr.world.getSeasonType();
        // 刷新宝箱红点
        this.model.updateTreasureReddot();
        // 设置中心位置
        this.areaActSize.mul(0.5, this.centre).subSelf(cc.v2(0.5, 0.5));
        // 初始化相机位置
        var zr = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO);
        CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(this.centre), this.areaActSize, cc.Vec2.ZERO, zr);
        // 绘制士兵
        this.initPawns();
        // 绘制建筑
        this.initBuilds();
        // 刷新地图
        this.updateMap(this.centre.floor());
        // UI
        if (reenter) {
            this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('area/AreaUI', this.model);
        }
        //
        this.touchCmpt.init(this.onClickMap.bind(this));
        //
        GameHelper_1.gameHpr.playAreaBgm(this.model.isBattleing());
    };
    AreaWindCtrl.prototype.onLeave = function () {
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO, CameraCtrl_1.cameraCtrl.zoomRatio);
        ViewHelper_1.viewHelper.hidePnl('area/AreaUI');
        this.touchCmpt.clean();
        GameHelper_1.gameHpr.world.setLookCell(null);
        this.clean();
        this.cleanPawns();
        ResHelper_1.resHelper.cleanNodeChildren(this.diNode);
        ResHelper_1.resHelper.cleanNodeChildren(this.maskNode);
        this.buildNode_.removeAllChildren();
        this.buildNode_.Data = false;
        this.topLayerNode_.removeAllChildren();
        this.topLayerNode_.Data = false;
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        AnimHelper_1.animHelper.clean();
        assetsMgr.releaseTempResByTag(this.key);
        audioMgr.releaseByMod('build');
        audioMgr.releaseByMod('pawn');
    };
    AreaWindCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AreaWindCtrl.prototype.onNetReconnect = function () {
        this.reinit();
    };
    AreaWindCtrl.prototype.onReenterAreaWind = function () {
        return this.reenter();
    };
    // 显示编辑家具的箭头
    AreaWindCtrl.prototype.onShowBuildJiantou = function (item, index) {
        this.editJiantouNode_.active = !!item;
        if (item) {
            this.editJiantouNode_.setPosition(item.getBodyOffsetTopPosition(68));
            this.editJiantouNode_.Component(cc.MultiFrame).setFrame(index);
        }
    };
    // 长按选中一个家具
    AreaWindCtrl.prototype.onLongPressBuild = function (item) {
        // 设置可以点击选择了
        this.builds.forEach(function (m) { return item.uid !== m.uid && m.setCanClickSelect(true); });
        this.pawns.forEach(function (m) { return m.setCanClick(false); });
        // 显示编辑UI
        this.openEditBuild(item);
    };
    // 移动建筑
    AreaWindCtrl.prototype.onMoveBuild = function (item, pos) {
        var point = item.getActPointByPixel(pos);
        this.model.amendBuildPoint(point, item.data.size); //修正一下
        item.setOffsetPositionByPoint(point);
        item.updateEditState(this.model.getBuildGroundPointMap(), point);
    };
    // 点击编辑建筑菜单
    AreaWindCtrl.prototype.onClickEditBuildMenu = function (type) {
        if (!this.currEditBuild || !this.currEditBuild.data /* || this.model.isBattleing() */) {
            return;
        }
        var item = this.currEditBuild;
        if (type === 'cancel') { //取消
            item.cancel();
        }
        else if (type === 'ok') { //确定
            if (item.editState) {
                return ViewHelper_1.viewHelper.showAlert(item.editState);
            }
            item.confirm(item.getActPointByPixel(item.getTempPosition()));
            // audioMgr.playSFX('area/sound06')
        }
        ViewHelper_1.viewHelper.hidePnl('area/EditBuild'); //隐藏编辑UI
        this.builds.forEach(function (m) { return m.setCanClickSelect(false); }); //关闭点击选择
        this.pawns.forEach(function (m) { return m.setCanClick(true); });
        item.syncZindex(); //同步zindex
        this.closeEditBuild();
    };
    // 编辑士兵
    AreaWindCtrl.prototype.onEditPawnPos = function (index, uid) {
        if (this.model.index !== index || this.model.isBattleing()) {
            return;
        }
        var pawn = this.pawns.find(function (m) { return m.uid === uid; });
        if (pawn) {
            this.builds.forEach(function (m) { return m.setCanClick(false); });
            this.pawns.forEach(function (m) { return m.setCanClick(false); });
            this.maskNode.children.forEach(function (m) { return m.active = !!m.Data; });
            // 行动中
            pawn.data.actioning = true;
            // 显示pnl
            ViewHelper_1.viewHelper.showPnl('area/EditPawn', pawn);
            this.currEditPawn = pawn;
            this.selectPawnNode_.Component(SelectCellCmpt_1.default).open(this.currEditPawn.getTempPosition(), this.PAWN_SIZE);
        }
    };
    // 点击编辑士兵的菜单
    AreaWindCtrl.prototype.onClickEditPawnMenu = function (type) {
        if (!this.currEditPawn || !this.currEditPawn.data || this.model.isBattleing()) {
            ViewHelper_1.viewHelper.hidePnl('area/EditPawn');
            this.builds.forEach(function (m) { return m.setCanClick(true); });
            this.pawns.forEach(function (m) { return m.setCanClick(true); });
            this.maskNode.children.forEach(function (m) { return m.active = false; });
            this.closeEditPawn();
        }
        else if (type === 'gather') { //集合
            this.gatherPawn();
        }
        else if (type === 'ok') {
            this.editPawnEnd();
        }
    };
    // 添加建筑
    AreaWindCtrl.prototype.onAddBuild = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            var isOwner_1 = this.model.isOwner();
            this.createBuild(data).then(function (item) {
                if (item && _this.isActive() && isOwner_1) {
                    var body = item.getBody();
                    // 摄像机移动到这个位置来
                    CameraCtrl_1.cameraCtrl.setTargetOnce(body);
                    // 扫光
                    // animHelper.playFlashLight([body])
                }
            });
        }
    };
    // 删除建筑
    AreaWindCtrl.prototype.onRemoveBuild = function (data) {
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            var build = this.builds.remove('uid', data.uid);
            if (build) {
                this.cleanBuild(build);
                assetsMgr.releaseTempRes(data.getPrefabUrl(), this.key);
            }
        }
    };
    // 刷新建筑等级
    AreaWindCtrl.prototype.onUpdateBuildLv = function (data) {
        var _a, _b;
        if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
        }
        else if (data.uid === this.model.wall.uid) {
            this.updateWallLv(data.lv);
        }
        else {
            (_b = this.builds.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.updateLv(data.lv);
        }
    };
    // 刷新建筑位置
    AreaWindCtrl.prototype.onUpdateBuildPoint = function (data) {
        var _a, _b;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            (_b = this.builds.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.syncPoint();
        }
    };
    // 刷新血量
    AreaWindCtrl.prototype.onUpdateAreaHp = function (index) {
        var _a, _b;
        if (index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        }
    };
    // 刷新遗迹信息
    AreaWindCtrl.prototype.onUpdateAncientInfo = function (data) {
        var _a;
        if (data.index !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            return;
        }
        var build = this.builds.find(function (m) { return m.data.isAncient(); });
        if (build) {
            build.updateLv(data.lv);
            build.updateUpLvAnim();
        }
    };
    // 刷新城市
    AreaWindCtrl.prototype.onUpdateBuilds = function (index) {
        var _a;
        if (index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            this.initBuilds();
        }
    };
    // 添加军队
    AreaWindCtrl.prototype.onAddArmy = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            data.pawns.forEach(function (m) { return _this.createPawn(m); });
        }
    };
    // 删除军队
    AreaWindCtrl.prototype.onRemoveArmy = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            data.pawns.forEach(function (m) {
                var i = _this.pawns.findIndex(function (p) { var _a; return p.uid === m.uid && ((_a = p.data) === null || _a === void 0 ? void 0 : _a.armyUid) === data.uid; });
                if (i !== -1) {
                    _this.cleanPawn(_this.pawns.splice(i, 1)[0], true);
                }
            });
        }
    };
    // 更新军队信息
    AreaWindCtrl.prototype.onUpdateArmy = function (data) {
        var _this = this;
        var _a, _b;
        if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            return;
        }
        // 先删除没有的
        for (var i = this.pawns.length - 1; i >= 0; i--) {
            var m = this.pawns[i];
            if (((_b = m.data) === null || _b === void 0 ? void 0 : _b.armyUid) !== data.uid) {
                continue;
            }
            else if (!data.pawns.has('uid', m.uid)) {
                this.pawns.splice(i, 1);
                this.cleanPawn(m, true);
            }
        }
        data.pawns.forEach(function (m) { return _this.createPawn(m); });
    };
    // 更新所有军队
    AreaWindCtrl.prototype.onUpdateAllArmy = function (index) {
        if (this.model.index === index) {
            this.initPawns();
        }
    };
    // 添加士兵
    AreaWindCtrl.prototype.onAddPawn = function (index, data) {
        if (this.model.index === index) {
            this.createPawn(data);
        }
    };
    // 删除士兵
    AreaWindCtrl.prototype.onRemovePawn = function (index, uid) {
        if (this.model.index === index) {
            this.cleanPawn(this.pawns.remove('uid', uid), true);
        }
    };
    // 战斗开始
    AreaWindCtrl.prototype.onAreaBattleBegin = function (index) {
        var _a, _b;
        if (this.model.index !== index) {
            return;
        }
        // 关闭当前正在编辑的建筑
        this.checkConfirmEditBuild();
        this.closeEditBuild();
        // 关闭当前正在编辑的士兵
        (_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.cancel();
        this.closeEditPawn();
        // 初始化血量
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        // 初始化士兵
        this.initPawns();
        // 战斗时间
        this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        //
        GameHelper_1.gameHpr.playAreaBgm(true);
    };
    // 战斗结束
    AreaWindCtrl.prototype.onAreaBattleEnd = function (index) {
        var _a, _b;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) !== index) {
            return;
        }
        else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
            return this.reenter();
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        this.initBuilds(true);
        this.initPawns();
        // 战斗时间
        this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        //
        GameHelper_1.gameHpr.playAreaBgm(false);
    };
    // 受到伤害
    AreaWindCtrl.prototype.onAreaMainHit = function (data) {
        var _a, _b;
        if (data.index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            if (this.model.isBattleing()) {
                (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.play();
            }
            AnimHelper_1.animHelper.playFlutterHp({ type: 'isDamage', value: data.value }, this.topLayerNode_, this.getPixelByPoint(data.point), this.key);
        }
    };
    // 播放飘血
    AreaWindCtrl.prototype.onPlayFlutterHp = function (data) {
        var _a, _b;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            var pos = data.point ? this.getPixelByPoint(data.point).clone() : (_b = this.pawns.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.getPosition();
            if (pos) {
                AnimHelper_1.animHelper.readyPlayFlutterHp(data, pos, this.topLayerNode_, this.key);
            }
        }
    };
    // 播放增加怒气
    AreaWindCtrl.prototype.onPlayFlutterAnger = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            var pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
            if (pawn) {
                AnimHelper_1.animHelper.playFlutterAnger(data.value, this.topLayerNode_, pawn.getPosition(), this.key);
            }
        }
    };
    // 播放子弹飞行
    AreaWindCtrl.prototype.onPlayBulletFly = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            data.startPos = this.getPixelByPoint(data.startPoint).clone();
            data.targetPos = this.getPixelByPoint(data.targetPoint).clone();
            AnimHelper_1.animHelper.playBulletFly(data, this.topLayerNode_, this.key);
        }
    };
    // 播放战斗特效
    AreaWindCtrl.prototype.onPlayBattleEffect = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            data.pos = this.getPixelByPoint(data.point).clone();
            var root = this.skillDiNode_;
            if (data.root === 'top') {
                root = this.topLayerNode_;
            }
            else if (data.root === 'role') {
                root = this.roleNode_;
                data.zIndex = (Constant_1.AREA_MAX_ZINDEX - (data.pos.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10 + 3;
            }
            AnimHelper_1.animHelper.playBattleEffect(data, root, this.key);
        }
    };
    // 播放音效
    AreaWindCtrl.prototype.onPlayBattleSfx = function (index, url, data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            audioMgr.playSFX(url, data);
        }
    };
    // 播放屏幕抖动
    AreaWindCtrl.prototype.onPlayBattleSceneShake = function (index, time) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            CameraCtrl_1.cameraCtrl.shake(time);
        }
    };
    // 聚焦士兵
    AreaWindCtrl.prototype.onFocusPawn = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) !== (data === null || data === void 0 ? void 0 : data.index)) {
            return;
        } /*  else if (cameraCtrl.getNoDragTime() < 5000) {
            return //多久没拖动才可以聚焦
        } */
        var pos = this.getPixelByPoint(data.point);
        if (CameraCtrl_1.cameraCtrl.isInScreenRangeByWorld(pos)) {
            CameraCtrl_1.cameraCtrl.moveTo(0.5, pos, true);
        }
    };
    // 刷新修建队列
    AreaWindCtrl.prototype.onUpdateBtQueue = function () {
        this.builds.forEach(function (m) { return m.updateUpLvAnim(); });
    };
    // 刷新训练队列
    AreaWindCtrl.prototype.onUpdatePawnDrillQueue = function (index) {
        if (this.model.index === index) {
            this.builds.forEach(function (m) { return m.updateDrillPawn(); });
        }
    };
    // 切换显示士兵等级
    AreaWindCtrl.prototype.onChangeShowPawnLv = function (val) {
        this.pawns.forEach(function (m) { return m.showPawnLv(val); });
    };
    // 切换显示士兵装备
    AreaWindCtrl.prototype.onChangeShowPawnEquip = function (val) {
        this.pawns.forEach(function (m) { return m.showPawnEquip(val); });
    };
    // 切换士兵装备
    AreaWindCtrl.prototype.onChangePawnEquip = function (data) {
        var _a;
        (_a = this.pawns.find(function (m) { return m.uid === data.uid; })) === null || _a === void 0 ? void 0 : _a.updateShowPawnEquip();
    };
    // 切换士兵皮肤
    AreaWindCtrl.prototype.onChangePawnSkin = function (data) {
        var i = this.pawns.findIndex(function (m) { return m.uid === data.uid; });
        if (i !== -1) {
            var pawn = this.pawns[i];
            if (pawn.curSkinId !== data.skinId) {
                this.pawns.splice(i, 1);
                this.cleanPawn(pawn);
                this.createPawn(data);
            }
        }
        this.builds.forEach(function (m) { return m.updateDrillPawn(); });
    };
    // 化身英雄
    AreaWindCtrl.prototype.onChangePawnPortrayal = function (data) {
        var _this = this;
        if (!data.portrayal) {
            return;
        }
        var i = this.pawns.findIndex(function (m) { return m.uid === data.uid; });
        if (i !== -1) {
            var pawn_1 = this.pawns[i];
            if (pawn_1.curPortrayalId !== data.portrayal.id) {
                pawn_1.playAvatarHeroAnim(data.portrayal.id).then(function () {
                    if (_this.isActive()) {
                        _this.pawns.splice(i, 1);
                        _this.cleanPawn(pawn_1);
                        _this.createPawn(data);
                    }
                });
            }
        }
    };
    // 打造装备开始
    AreaWindCtrl.prototype.onForgeEquipBegin = function () {
        var _a;
        (_a = this.builds.find(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID; })) === null || _a === void 0 ? void 0 : _a.updateForgeEquip();
    };
    // 打造装备完成
    AreaWindCtrl.prototype.onForgeEquipComplete = function () {
        var _a;
        (_a = this.builds.find(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID; })) === null || _a === void 0 ? void 0 : _a.updateForgeEquip();
    };
    // 若引导
    AreaWindCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'area') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AreaWindCtrl.prototype.isActive = function () { var _a; return this.isValid && !!((_a = this.model) === null || _a === void 0 ? void 0 : _a.active); };
    AreaWindCtrl.prototype.getPixelByPoint = function (point) {
        return point && MapHelper_1.mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_3));
    };
    AreaWindCtrl.prototype.getPointByPixel = function (pixel) {
        return pixel && MapHelper_1.mapHelper.getPointByPixel(pixel).subSelf(this.borderSize);
    };
    AreaWindCtrl.prototype.getBuildPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.buildOrigin, this._temp_vec2_1), this._temp_vec2_1);
    };
    AreaWindCtrl.prototype.clean = function () {
        var _a;
        this.areaCenter.setLookArea(null);
        (_a = this.model) === null || _a === void 0 ? void 0 : _a.setActive(false);
        this.model = null;
        GameHelper_1.gameHpr.cleanPawnAstarMap();
        this.searchCircle = null;
        this.cleanWalls();
        this.cleanFlames();
        this.cleanAlliFlags();
        this.cleanAreaOutDecorate();
        this.cheanBuilds();
        // this.cleanPawns()
        this.closeEditBuild();
        this.closeEditPawn();
    };
    // 重连之后的初始化
    AreaWindCtrl.prototype.reinit = function () {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var world, _e;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        GameHelper_1.gameHpr.cleanPawnAstarMap();
                        // 关闭当前正在编辑的建筑
                        this.checkConfirmEditBuild();
                        this.closeEditBuild();
                        // 关闭当前正在编辑的士兵
                        (_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.cancel();
                        this.closeEditPawn();
                        world = GameHelper_1.gameHpr.world;
                        _e = this;
                        return [4 /*yield*/, this.areaCenter.reqAreaByIndex((_c = (_b = world.getLookCell()) === null || _b === void 0 ? void 0 : _b.index) !== null && _c !== void 0 ? _c : -1, true)];
                    case 1:
                        _e.model = _f.sent();
                        if (!this.model) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.gotoWind(world.getSceneKey())];
                        }
                        else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
                            return [2 /*return*/, this.reenter()];
                        }
                        this.model.setActive(true);
                        // 刷新地图
                        this.updateMap(this.centre.floor());
                        // 城墙等级
                        this.model.wall && this.updateWallLv(this.model.wall.lv);
                        // 血条
                        (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.init(this.model);
                        // 绘制建筑
                        this.initBuilds();
                        // 绘制士兵
                        this.initPawns();
                        // 战斗时间
                        this.emit(EventType_1.default.REINIT_AREA_UI, this.model);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 重新绘制
    AreaWindCtrl.prototype.reenter = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.emit(mc.Event.READY_BEGIN_WIND);
                        this.clean();
                        return [4 /*yield*/, this.onReady()];
                    case 1:
                        _a.sent();
                        this.emit(mc.Event.READY_END_WIND);
                        this.onEnter(true);
                        this.emit(EventType_1.default.UPDATE_REENTER_AREA);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化城墙
    AreaWindCtrl.prototype.initWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb, node, size;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanWalls();
                        if (!!this.model.isBoss()) return [3 /*break*/, 6];
                        return [4 /*yield*/, assetsMgr.loadTempRes('wall/WALL_HP_BAR', cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb || !this.isValid) {
                            return [2 /*return*/];
                        }
                        node = cc.instantiate2(pfb, this.roleNode_);
                        this.hpBar = node.addComponent(HPBarCmpt_1.default).init(this.model);
                        size = this.model.buildSize;
                        ViewHelper_1.viewHelper.drawGrid(this.gridNode_.Component(cc.Graphics), cc.v2(size.x - 2, size.y - 2), cc.v2(this.buildOrigin.x + 1, this.buildOrigin.y + 1));
                        this.gridNode_.active = false;
                        if (!this.model.wall) return [3 /*break*/, 5];
                        if (!!this.model.isAncient()) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.createWall()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (!(this.model.wall.lv > 0)) return [3 /*break*/, 5];
                        return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, this.key)];
                    case 4:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = this.wallLvNode = cc.instantiate2(pfb, this.roleNode_);
                            this.updateWallLv(this.model.wall.lv);
                        }
                        _a.label = 5;
                    case 5:
                        this.updateWallHpPosition();
                        _a.label = 6;
                    case 6: 
                    // 创建区域外的装饰
                    return [4 /*yield*/, this.createAreaOutDecorate()
                        // 创建联盟旗帜
                    ];
                    case 7:
                        // 创建区域外的装饰
                        _a.sent();
                        // 创建联盟旗帜
                        return [4 /*yield*/, this.createAlliFlags(GameHelper_1.gameHpr.getPlayerAlliIcon(this.model.owner))];
                    case 8:
                        // 创建联盟旗帜
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.createWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var skinId;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        while (this.walls.length > 0) {
                            this.walls.pop().destroy();
                        }
                        this.walls.length = 0;
                        skinId = 0;
                        return [4 /*yield*/, Promise.all(this.model.walls.map(function (m) { return __awaiter(_this, void 0, void 0, function () {
                                var url, pfb, node;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            url = "wall/" + skinId + "/WALL_" + skinId + "_" + m.type + "_" + m.dir;
                                            if (m.index) {
                                                url += '_' + m.index;
                                            }
                                            return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                                        case 1:
                                            pfb = _a.sent();
                                            if (pfb && this.isValid) {
                                                node = cc.instantiate2(pfb, this.buildNode_);
                                                node.Data = this.model.wall;
                                                node.setPosition(MapHelper_1.mapHelper.getPixelByPoint(m.point.add(this.buildOrigin, this._temp_vec2_1)));
                                                node.addComponent(ClickTouchCmpt_1.default).on(this.onClickWall, this);
                                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                                this.walls.push(node);
                                            }
                                            return [2 /*return*/];
                                    }
                                });
                            }); }))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanWalls = function () {
        var _a, _b;
        (_a = this.wallLvNode) === null || _a === void 0 ? void 0 : _a.destroy();
        this.wallLvNode = null;
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.clean();
        this.hpBar = null;
        while (this.walls.length > 0) {
            this.walls.pop().destroy();
        }
        this.walls.length = 0;
    };
    // 初始化火焰
    AreaWindCtrl.prototype.createFlames = function () {
        return __awaiter(this, void 0, void 0, function () {
            var flames, pfb;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        flames = this.model.flames;
                        if (this.flames.length === flames.length) {
                            return [2 /*return*/, flames.forEach(function (point, i) {
                                    var node = _this.flames[i], pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                    node.setPosition(pos.x - 2, pos.y + 26);
                                    node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                })];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes('build/FLAME', cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            flames.forEach(function (point) {
                                var node = cc.instantiate2(pfb, _this.roleNode_), pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                node.setPosition(pos.x - 2, pos.y + 26);
                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                _this.flames.push(node);
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanFlames = function () {
        while (this.flames.length > 0) {
            this.flames.pop().destroy();
        }
        this.flames.length = 0;
    };
    // 创建联盟旗帜
    AreaWindCtrl.prototype.createAlliFlags = function (icon) {
        return __awaiter(this, void 0, void 0, function () {
            var points, pfb;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!icon) {
                            return [2 /*return*/, this.cleanAlliFlags()];
                        }
                        points = this.model.alliFlags;
                        if (this.alliFlags.length === points.length && this.alliFlags[0].Data === icon) {
                            return [2 /*return*/, points.forEach(function (point, i) {
                                    var node = _this.alliFlags[i], pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                    node.setPosition(pos);
                                    node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                })];
                        }
                        this.cleanAlliFlags();
                        return [4 /*yield*/, assetsMgr.loadTempRes('alli_flag/ALLI_FLAG_' + icon, cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            points.forEach(function (point) {
                                var node = cc.instantiate2(pfb, _this.roleNode_), pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                node.Child('body').y = -28;
                                node.setPosition(pos);
                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                _this.alliFlags.push(node);
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanAlliFlags = function () {
        while (this.alliFlags.length > 0) {
            this.alliFlags.pop().destroy();
        }
        this.alliFlags.length = 0;
    };
    // 创建区域外的装饰
    AreaWindCtrl.prototype.createAreaOutDecorate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var seasonType, cell, drawType, url, pfb, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanAreaOutDecorate();
                        seasonType = 0;
                        cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.model.index), drawType = cell.getLandDrawType();
                        url = "area_decorate/" + seasonType + "/AREA_DECORATE_" + drawType;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = cc.instantiate2(pfb, this.mapNode_.Child('bg'));
                            node.setPosition(this.borderSize.x * Constant_1.TILE_SIZE, this.borderSize.y * Constant_1.TILE_SIZE);
                            this.areaOutDecorate.push(node);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanAreaOutDecorate = function () {
        while (this.areaOutDecorate.length > 0) {
            this.areaOutDecorate.pop().destroy();
        }
        this.areaOutDecorate.length = 0;
    };
    // 初始化建筑
    AreaWindCtrl.prototype.initBuilds = function (playOccupyEffect) {
        return __awaiter(this, void 0, void 0, function () {
            var builds, i;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.buildMap = {};
                        builds = this.model.builds;
                        builds.forEach(function (m) { return _this.buildMap[m.uid] = 1; });
                        for (i = this.builds.length - 1; i >= 0; i--) {
                            if (!this.buildMap[this.builds[i].uid]) {
                                this.cleanBuild(this.builds.splice(i, 1)[0]);
                            }
                        }
                        return [4 /*yield*/, Promise.all(builds.map(function (m) { return _this.createBuild(m); }))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.createBuild = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var build, pfb;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        build = this.builds.find(function (m) { return m.uid === data.uid; });
                        if (build) {
                            this.buildMap[data.uid] = 2;
                            return [2 /*return*/, build.resync(data, this.model.owner)];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        else if (this.buildMap[data.uid] === 2) {
                            return [2 /*return*/, null]; //防止重复创建或创建没有的
                        }
                        else if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
                            return [2 /*return*/, null];
                        }
                        this.buildMap[data.uid] = 2;
                        build = cc.instantiate2(pfb, this.roleNode_).getComponent(BaseBuildCmpt_1.default).init(data, this.buildOrigin, this.borderSize.y * Constant_1.TILE_SIZE, this.model.owner);
                        this.builds.push(build);
                        return [2 /*return*/, build];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cheanBuilds = function () {
        while (this.builds.length > 0) {
            this.builds.pop().clean();
        }
        this.buildMap = {};
    };
    AreaWindCtrl.prototype.cleanBuild = function (data) {
        if (data) {
            data.clean();
            delete this.buildMap[data.uid];
        }
    };
    // 刷新墙的等级
    AreaWindCtrl.prototype.updateWallLv = function (lv) {
        if (this.wallLvNode) {
            this.wallLvNode.Child('val', cc.Label).string = '' + lv;
        }
    };
    // 刷新血条位置
    AreaWindCtrl.prototype.updateWallHpPosition = function () {
        if (this.hpBar) {
            var node = this.hpBar.node, pos = cc.v2();
            if (this.model.cityId === Constant_1.CITY_MAIN_NID) {
                pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + 25);
            }
            else if (this.model.isAncient()) {
                pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + 25);
            }
            else {
                pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
                node.setPosition(pos.x, pos.y + 47);
            }
            node.zIndex = (Constant_1.AREA_MAX_ZINDEX - (pos.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10 + 1;
        }
        if (this.wallLvNode) {
            var pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
            this.wallLvNode.setPosition(pos.x, pos.y + 16);
            this.wallLvNode.zIndex = this.hpBar.node.zIndex + 1;
        }
    };
    // 打开编辑建筑
    AreaWindCtrl.prototype.openEditBuild = function (item) {
        // 显示pnl
        ViewHelper_1.viewHelper.showPnl('area/EditBuild');
        this.gridNode_.active = true;
        // 如果之前有就放下
        this.checkConfirmEditBuild();
        this.currEditBuild = item;
        // 刷新一下地面点
        this.model.updateBuildGroundPoints(item.data);
        // 刷新编辑状态
        item.updateEditState(this.model.getBuildGroundPointMap(), item.point);
    };
    // 如果有建筑在编辑状态 就放下
    AreaWindCtrl.prototype.checkConfirmEditBuild = function () {
        if (!this.isEditBuildState()) {
        }
        else if (this.currEditBuild.editState) {
            this.currEditBuild.cancel();
        }
        else {
            this.currEditBuild.confirm(this.currEditBuild.getActPointByPixel(this.currEditBuild.getTempPosition()));
        }
    };
    // 关闭编辑建筑
    AreaWindCtrl.prototype.closeEditBuild = function () {
        this.currEditBuild = null;
        this.editJiantouNode_.active = false;
        this.gridNode_.active = false;
    };
    // 关闭编辑士兵
    AreaWindCtrl.prototype.closeEditPawn = function () {
        var _a;
        for (var key in this.editPawns) {
            var pawn = this.editPawns[key], state = (_a = pawn.data) === null || _a === void 0 ? void 0 : _a.getState();
            if (state && state < Enums_1.PawnState.STAND) {
                pawn.data.changeState(Enums_1.PawnState.NONE);
            }
        }
        this.editPawns = {};
        this.currEditPawn = null;
        this.selectPawnNode_.Component(SelectCellCmpt_1.default).close();
        this.isPawnMoveing = false;
    };
    AreaWindCtrl.prototype.cleanPawns = function () {
        while (this.pawns.length > 0) {
            this.pawns.pop().clean();
        }
        this.pawnMap = {};
    };
    // 初始化士兵
    AreaWindCtrl.prototype.initPawns = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawns, uidMap, i, m;
            var _this = this;
            return __generator(this, function (_a) {
                pawns = this.model.getAllPawns(), uidMap = {};
                pawns.forEach(function (m) { return uidMap[m.getAbsUid()] = true; });
                for (i = this.pawns.length - 1; i >= 0; i--) {
                    m = this.pawns[i];
                    if (!uidMap[m.getAbsUid()] || !m.data || m.data.isDie()) {
                        this.cleanPawn(this.pawns.splice(i, 1)[0]);
                    }
                }
                return [2 /*return*/, Promise.all(pawns.map(function (m) { return _this.createPawn(m); }))];
            });
        });
    };
    AreaWindCtrl.prototype.createPawn = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var pawn, pfb, uid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isActive() || !data) {
                            return [2 /*return*/, null];
                        }
                        pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
                        if (!pawn) {
                        }
                        else if (pawn.curSkinId !== data.skinId || pawn.curPortrayalId !== data.getPortrayalId()) {
                            this.pawns.remove('uid', data.uid);
                            this.cleanPawn(pawn);
                        }
                        else if (data.isDie()) {
                            this.pawns.remove('uid', data.uid);
                            this.cleanPawn(pawn);
                            return [2 /*return*/, null];
                        }
                        else {
                            return [2 /*return*/, pawn.resync(data)];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        else if (this.pawnMap[data.uid]) {
                            return [2 /*return*/, null]; //防止多次创建
                        }
                        else if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
                            return [2 /*return*/, null];
                        }
                        uid = data.uid;
                        data = this.model.getPawn(uid) || this.model.getBattleTempPawn(uid);
                        if (!data || data.isDie()) {
                            return [2 /*return*/, null];
                        }
                        pawn = cc.instantiate2(pfb, this.roleNode_).getComponent(PawnCmpt_1.default).init(data, this.borderSize, this.key);
                        this.pawns.push(pawn);
                        this.pawnMap[data.uid] = pawn;
                        return [2 /*return*/, pawn];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanPawn = function (pawn, release) {
        if (pawn) {
            delete this.pawnMap[pawn.uid];
            pawn.clean(release);
        }
    };
    // 是否编辑建筑中
    AreaWindCtrl.prototype.isEditBuildState = function () {
        var _a;
        return !!((_a = this.currEditBuild) === null || _a === void 0 ? void 0 : _a.data);
    };
    // 是否编辑小兵中
    AreaWindCtrl.prototype.isEditPawnState = function () {
        var _a;
        return !!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data);
    };
    // 绘制地图
    AreaWindCtrl.prototype.updateMap = function (centre) {
        var _this = this;
        var seasonType = /* gameHpr.world.getSeasonType() */ 0;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.model.index), seasonColorConf = Constant_1.AREA_DI_COLOR_CONF[seasonType];
        var colorConf = seasonColorConf[cell.getLandDrawType()] || seasonColorConf[0];
        var areaSize = this.model.areaSize, oYindex = (areaSize.x + 1) % 2;
        // 设置整个背景颜色
        CameraCtrl_1.cameraCtrl.setBgColor(colorConf.bg);
        //
        // this.preCameraZoomRatio = cameraCtrl.zoomRatio
        // this.centre.set(centre)
        var buildOrigin = this.model.buildOrigin, buildSize = this.model.buildSize;
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, GameHelper_1.gameHpr.world.getMaxTileRange());
        var isBoss = this.model.isBoss();
        var mi = 0;
        this.diNode.Items(points, function (it, point, i) {
            var x = point.x - _this.borderSize.x, y = point.y - _this.borderSize.y;
            var bx = x - buildOrigin.x, by = y - buildOrigin.y;
            var index = it.Data = MapHelper_1.mapHelper.pointToIndexByNumer(x, y, areaSize);
            var id = x + '_' + y;
            it.setPosition(MapHelper_1.mapHelper.getPixelByPoint(point, _this._temp_vec2_0));
            if (MapHelper_1.mapHelper.isBorder(x, y, areaSize)) { //边界外
                it.Component(cc.Sprite).spriteFrame = null;
            }
            else if (MapHelper_1.mapHelper.isBorder(bx, by, buildSize)) { //战斗区域
                var idx = y % 2 === 0 ? index : index + oYindex;
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                it.Color(colorConf.battle[Number(idx % 2 !== 0)]);
                if (_this.model.banPlacePawnPointMap[id]) {
                    var mn = ResHelper_1.resHelper.getNodeByIndex(_this.maskNode, mi++, _this._temp_vec2_0);
                    mn.Data = true;
                    mn.active = _this.isEditPawnState();
                }
            }
            else if (!isBoss) { //建筑区域
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                if (bx === 0 || bx === buildSize.x - 1 || by === 0 || by === buildSize.y - 1) {
                    it.Color(colorConf.build);
                }
                else if (cell.isMainCity() || cell.isAncient()) {
                    it.Color('#D6DBAA');
                }
            }
            else if (bx === 1 && by === 1) { //boss位置
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('land_area_boss_' + cell.landType);
            }
            else {
                it.Component(cc.Sprite).spriteFrame = null;
            }
        });
        // 隐藏多余的
        ResHelper_1.resHelper.hideNodeByIndex(this.maskNode, mi);
    };
    AreaWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandItemIcon(icon, this.tempSeasonType);
    };
    // 点击地图
    AreaWindCtrl.prototype.onClickMap = function (worldLocation) {
        if (!this.model || !worldLocation) {
            return;
        }
        var point = this.getPointByPixel(worldLocation);
        if (!point) {
            return;
        }
        else if (MapHelper_1.mapHelper.isBorder(point.x, point.y, this.model.areaSize)) {
            return;
        }
        // 是否点击的建筑区域
        var bpoint = point.sub(this.model.buildOrigin, this._temp_vec2_1);
        if (MapHelper_1.mapHelper.isBorder(bpoint.x, bpoint.y, this.model.buildSize)) {
            this.onClickMapArea(point);
        }
        else {
            this.onClickBuildArea(bpoint);
        }
    };
    // 点击地图区域
    AreaWindCtrl.prototype.onClickMapArea = function (point) {
        if (!this.isEditPawnState() || this.isEditBuildState() || this.isPawnMoveing) {
            return;
        }
        var uid = this.currEditPawn.uid;
        if (!this.model.banPlacePawnPointMap[point.ID()] && !this.pawns.some(function (m) { return m.uid !== uid && m.getActPoint().equals(point); })) {
            this.selectPawnNode_.Component(SelectCellCmpt_1.default).open(this.getPixelByPoint(point), this.PAWN_SIZE);
            this.movePawn(point);
        }
    };
    // 点击建筑区域
    AreaWindCtrl.prototype.onClickBuildArea = function (point) {
        if (!this.isEditBuildState() || this.isEditPawnState()) {
            return;
        }
        else if (!this.model.isBuildBorder(point)) {
            this.currEditBuild.setOffsetPositionByPoint(point);
            this.currEditBuild.updateEditState(this.model.getBuildGroundPointMap(), point);
        }
    };
    // 点击墙
    AreaWindCtrl.prototype.onClickWall = function () {
        if (!this.model.wall || this.isEditBuildState() || this.isEditPawnState()) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.model.isAncient()) {
            var build = this.model.getBuildById(this.model.cityId);
            if (!build) {
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(this.model.owner)) {
                ViewHelper_1.viewHelper.showPnl(build.getUIUrl(), build);
            }
            else {
                ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', build);
            }
        }
        else if (this.model.isOwner()) {
            ViewHelper_1.viewHelper.showPnl(this.model.wall.getUIUrl(), this.model.wall);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildCity', this.model.wall);
        }
    };
    AreaWindCtrl.prototype.getSearchCircle = function () {
        if (!this.searchCircle) {
            var model_1 = this.model;
            this.searchCircle = new SearchCircle_1.default().init(function (x, y) { return model_1.checkIsBattleArea(x, y) && !model_1.banPlacePawnPointMap[x + '_' + y]; });
        }
        return this.searchCircle;
    };
    // 移动士兵
    AreaWindCtrl.prototype.movePawn = function (point) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.currEditPawn) return [3 /*break*/, 2];
                        this.isPawnMoveing = true;
                        this.emit(EventType_1.default.EDIT_PAWN_MOVEING, true);
                        return [4 /*yield*/, this.movePawnOne(this.currEditPawn, point)];
                    case 1:
                        _a.sent();
                        if (this.isActive()) {
                            this.isPawnMoveing = false;
                            this.emit(EventType_1.default.EDIT_PAWN_MOVEING, false);
                        }
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 移动单个士兵
    AreaWindCtrl.prototype.movePawnOne = function (pawn, point) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var data, sp, area, as, points, time;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!(pawn === null || pawn === void 0 ? void 0 : pawn.data) || ((_a = pawn.data) === null || _a === void 0 ? void 0 : _a.getState()) === Enums_1.PawnState.EDIT_MOVE) {
                            return [2 /*return*/];
                        }
                        data = pawn.data;
                        sp = pawn.getActPoint();
                        area = this.model, as = GameHelper_1.gameHpr.getPawnASatr(data.uid).init(function (x, y) { return area.checkIsBattleArea(x, y) && !area.banPlacePawnPointMap[x + '_' + y]; });
                        return [4 /*yield*/, as.search(sp, point)];
                    case 1:
                        points = _b.sent();
                        if (!this.isActive() || points.length === 0) {
                            return [2 /*return*/];
                        }
                        else if (!this.editPawns[pawn.uid]) {
                            this.editPawns[pawn.uid] = pawn;
                        }
                        time = MapHelper_1.mapHelper.getMoveNeedTime(points, 400);
                        data.changeState(Enums_1.PawnState.EDIT_MOVE, { paths: points, needMoveTime: time });
                        return [4 /*yield*/, ut.wait(time * 0.001)];
                    case 2:
                        _b.sent();
                        if (data.getState() < Enums_1.PawnState.STAND) {
                            data.changeState(Enums_1.PawnState.NONE);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 集合士兵
    AreaWindCtrl.prototype.gatherPawn = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var pawn, uid, armyUid, point, pawns, otherPawns, count, points;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data)) {
                            return [2 /*return*/];
                        }
                        pawn = this.currEditPawn;
                        uid = this.currEditPawn.uid;
                        armyUid = this.currEditPawn.data.armyUid;
                        point = pawn.getActPoint();
                        pawns = [], otherPawns = {};
                        this.pawns.forEach(function (m) {
                            var _a;
                            if (m.uid === uid) {
                            }
                            else if (((_a = m.data) === null || _a === void 0 ? void 0 : _a.armyUid) === armyUid) {
                                pawns.push(m);
                            }
                            else {
                                otherPawns[m.point.ID()] = true;
                            }
                        });
                        count = pawns.length;
                        if (count === 0) {
                            return [2 /*return*/];
                        }
                        points = this.getSearchCircle().search(point, count, otherPawns);
                        // 删除已经在这个位置的士兵
                        points.delete(function (m) {
                            var i = pawns.findIndex(function (p) { return p.getActPoint().equals(m); });
                            if (i !== -1) {
                                pawns.splice(i, 1);
                                return true;
                            }
                            return false;
                        });
                        if (points.length === 0) {
                            return [2 /*return*/];
                        }
                        this.isPawnMoveing = true;
                        this.emit(EventType_1.default.EDIT_PAWN_MOVEING, true);
                        return [4 /*yield*/, Promise.all(points.map(function (m, i) { return _this.movePawnOne(pawns[i], m); }))];
                    case 1:
                        _b.sent();
                        if (this.isActive()) {
                            this.isPawnMoveing = false;
                            this.emit(EventType_1.default.EDIT_PAWN_MOVEING, false);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 保存编辑士兵的信息
    AreaWindCtrl.prototype.editPawnEnd = function () {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var info, pawns, moveInfos, key, pawn, point, _c, err, data, key;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        if (!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data) || this.isPawnMoveing) {
                            return [2 /*return*/];
                        }
                        info = this.currEditPawn.data;
                        pawns = [];
                        moveInfos = [];
                        for (key in this.editPawns) {
                            pawn = this.editPawns[key];
                            point = pawn.getActPoint();
                            moveInfos.push({
                                uid: pawn.uid,
                                point: point.toJson()
                            });
                            if (!((_b = pawn.point) === null || _b === void 0 ? void 0 : _b.equals(point))) {
                                pawns.push({ uid: pawn.uid, point: point.toJson() });
                            }
                        }
                        if (!(pawns.length > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, NetHelper_1.netHelper.reqMoveAreaPawns({ index: info.aIndex, armyUid: info.armyUid, pawns: moveInfos })];
                    case 1:
                        _c = _d.sent(), err = _c.err, data = _c.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (!this.isActive()) {
                            return [2 /*return*/];
                        }
                        _d.label = 2;
                    case 2:
                        ViewHelper_1.viewHelper.hidePnl('area/EditPawn');
                        for (key in this.editPawns) {
                            this.editPawns[key].confirm();
                        }
                        this.currEditPawn.data.actioning = false;
                        this.builds.forEach(function (m) { return m.setCanClick(true); });
                        this.pawns.forEach(function (m) { return m.setCanClick(true); });
                        this.maskNode.children.forEach(function (m) { return m.active = false; });
                        this.closeEditPawn();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.update = function (dt) {
        var _a;
        if (!((_a = this.model) === null || _a === void 0 ? void 0 : _a.active)) {
            return;
        }
        // 检测是否需要填充地图
        // this.checkUpdateMap()
    };
    AreaWindCtrl.prototype.checkUpdateMap = function () {
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2_2);
        if (!this.centre.equals(point) || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateMap(point);
        }
    };
    AreaWindCtrl = __decorate([
        ccclass
    ], AreaWindCtrl);
    return AreaWindCtrl;
}(mc.BaseWindCtrl));
exports.default = AreaWindCtrl;

cc._RF.pop();