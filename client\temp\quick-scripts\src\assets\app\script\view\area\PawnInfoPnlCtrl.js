"use strict";
cc._RF.push(module, 'a0bf3bKAGhA8oj2JJzlsoGa', 'PawnInfoPnlCtrl');
// app/script/view/area/PawnInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PawnInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(PawnInfoPnlCtrl, _super);
    function PawnInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.headNode_ = null; // path://root/head_be_n
        _this.lvEditLbl_ = null; // path://root/head_be_n/lv/edit/num/lv_edit_l
        _this.attackSpeedEditLbl_ = null; // path://root/head_be_n/attack_speed/edit/num/attack_speed_edit_l
        _this.attrNode_ = null; // path://root/attr_n_be
        _this.skillNode_ = null; // path://root/skill_n
        _this.equipNode_ = null; // path://root/equip_n
        _this.fromNode_ = null; // path://root/from_n
        _this.buttonNode_ = null; // path://root/button_n
        _this.buffNode_ = null; // path://root/buff/buff_n
        _this.selectSkinBoxNode_ = null; // path://select_skin_box_be_n
        _this.syncSkinNode_ = null; // path://select_skin_box_be_n/sync_skin_be_n
        _this.syncSkinMaskNode_ = null; // path://select_skin_box_be_n/sync_skin_mask_be_n
        _this.settingSyncSkinNode_ = null; // path://select_skin_box_be_n/setting_sync_skin_n
        _this.selectEquipBoxNode_ = null; // path://select_equip_box_be_n
        _this.syncEquipNode_ = null; // path://select_equip_box_be_n/sync_equip_be_n
        _this.syncEquipMaskNode_ = null; // path://select_equip_box_be_n/sync_equip_mask_be_n
        _this.settingSyncEquipNode_ = null; // path://select_equip_box_be_n/setting_sync_equip_n
        _this.selectPetBoxNode_ = null; // path://select_pet_box_be_n
        _this.selectArmyBoxNode_ = null; // path://select_army_box_be_n
        //@end
        _this.root = null;
        _this.data = null;
        _this.drillInfo = null;
        _this.fromTo = '';
        _this.preAttackSpeed = 0;
        _this.preEquipUid = '';
        _this.preSkinId = 0;
        _this.prePetId = 0;
        _this.preRootHeight = -1;
        _this.isCanEditSkin = false;
        _this.isCanEdit = false;
        _this.isConfPawn = false;
        _this.isBattleing = false;
        return _this;
    }
    PawnInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PAWN_TREASURE] = this.onUpdatePawnTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_BUFF] = this.onUpdateBuff, _c.enter = true, _c),
        ];
    };
    PawnInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.root = this.FindChild('root');
                this.selectSkinBoxNode_.active = false;
                this.selectEquipBoxNode_.active = false;
                this.selectArmyBoxNode_.active = false;
                return [2 /*return*/];
            });
        });
    };
    PawnInfoPnlCtrl.prototype.onEnter = function (data, drillInfo, fromTo) {
        var _a, _b;
        GameHelper_1.gameHpr.uiShowPawnData = data;
        this.data = data;
        this.drillInfo = drillInfo;
        this.fromTo = fromTo;
        this.preAttackSpeed = data.attackSpeed;
        this.preEquipUid = data.equip.uid;
        this.prePetId = data.petId;
        var isOwner = data.isOwner();
        if (isOwner && !GameHelper_1.gameHpr.isSpectate() && !GameHelper_1.gameHpr.user.isHasPawnSkinById(data.skinId)) {
            data.skinId = 0;
        }
        this.preSkinId = data.skinId;
        this.data.recordCurrHp(true); //先记录一下
        var isBattleing = this.isBattleing = data.isBattleing() || GameHelper_1.gameHpr.isBattleingByIndex(data.aIndex), hasOwner = !!data.owner;
        var isCanEdit = this.isCanEdit = isOwner && !isBattleing && data.getState() === Enums_1.PawnState.NONE && !drillInfo && (!fromTo || fromTo === 'area_army');
        var isConfPawn = this.isConfPawn = !data.uid && !drillInfo && fromTo !== 'ceri' && fromTo !== 'book'; //配置士兵
        var isInArea = mc.currWindName === 'area'; //是否在战斗场景
        var isFromDrillground = fromTo === 'drillground';
        var isMachine = data.type >= Enums_1.PawnType.MACHINE; //是否器械
        var isHero = data.isHero(); //是否英雄
        var isCanEditEquip = isOwner && !isBattleing && data.getState() === Enums_1.PawnState.NONE && (!fromTo || fromTo === 'area_army');
        // 头像
        var editNode = this.headNode_.Child('edit'), headValNode = this.headNode_.Child('val');
        headValNode.active = true;
        if (data.isBoss()) { //boss加载mini头像
            headValNode.y = -32;
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(data.getViewId(), headValNode, this.key);
        }
        else {
            headValNode.y = -36;
            ResHelper_1.resHelper.loadPawnHeadIcon(data.getViewId(), headValNode, this.key);
        }
        editNode.active = this.isCanEditSkin = this.headNode_.Component(cc.Button).interactable = !isHero && (isCanEdit || isConfPawn || fromTo === 'book');
        this.headNode_.Child('name/val').setLocaleKey(data.name);
        this.headNode_.Child('name/type').setLocaleKey(data.type ? data.typeName : '');
        // 同步皮肤设置
        this.settingSyncSkinNode_.active = false;
        this.syncSkinMaskNode_.active = false;
        if (this.syncSkinNode_.active = !isConfPawn && isCanEditEquip) {
            var val = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0;
            this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
        }
        // 等级
        this.updateLv();
        // 出手速度
        var attackSpeed = this.headNode_.Child('attack_speed');
        if (attackSpeed.active = fromTo !== 'book') {
            var isCanEditAs = !drillInfo && fromTo !== 'ceri' && (isCanEdit || isConfPawn);
            attackSpeed.Child('edit').active = isCanEditAs;
            attackSpeed.Child('val').active = !isCanEditAs;
            if (isCanEditAs) {
                this.attackSpeedEditLbl_.string = data.attackSpeed + '';
            }
            else {
                attackSpeed.Child('val', cc.Label).string = data.attackSpeed + '';
            }
        }
        // 属性
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        // 技能
        this.updateSkills();
        // 装备 和 背包
        var isYyj = ((_b = (_a = this.data.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.YANG_YOUJI;
        var isEquip = !isMachine && (hasOwner || isConfPawn || !!drillInfo || isYyj);
        if (this.equipNode_.active = isEquip || !!this.preEquipUid) {
            var info = this.equipNode_.Child('info');
            // 装备
            info.Child('edit_equip_be').active = isCanEditEquip || isConfPawn;
            this.updateEquipInfo(info.Child('equip_show_be'));
            // 宠物
            info.Child('edit_pet_be').active = isCanEditEquip && isYyj;
            var petNode = info.Child('pet_show_be');
            if (petNode.active = isYyj) {
                this.updatePetInfo(petNode);
            }
            // 背包
            var bagNode = info.Child('bag');
            if (isEquip) {
                this.updateBag(bagNode, data.treasures);
            }
            else {
                bagNode.Swih('');
            }
        }
        // 同步装备设置
        this.settingSyncEquipNode_.active = false;
        this.syncEquipMaskNode_.active = false;
        if (this.syncEquipNode_.active = !isConfPawn && isCanEditEquip) {
            var val = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0;
            this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
        }
        // 归属
        var from = this.fromNode_;
        var showMarchSpeed = data.marchSpeed > 0 && !data.uid, showArmy = !!data.armyName, showPlayer = hasOwner && !isOwner;
        if (from.active = showMarchSpeed || showArmy || showPlayer) {
            // 行军速度
            if (from.Child('march_speed').active = showMarchSpeed) {
                from.Child('march_speed/val', cc.Label).setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            }
            // 所属军队
            var army = from.Child('army');
            if (army.active = showArmy) {
                army.Child('val/edit').active = isCanEdit && isInArea && !isFromDrillground;
                this.updateArmyInfo(army, data.armyName);
            }
            var playerInfo = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
            // 所属玩家
            if (from.Child('player').active = showPlayer) {
                from.Child('player/val', cc.Label).string = ut.nameFormator((playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.nickname) || '???', 8);
            }
            // 所属联盟
            var alliName = playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.allianceName;
            if (from.Child('alli').active = showPlayer && !!alliName) {
                from.Child('alli/val', cc.Label).string = alliName;
            }
        }
        // 按钮
        do {
            var buttonRoot = this.buttonNode_.Child('root');
            var isEditPos = isCanEdit && isInArea && !isFromDrillground;
            if (isEditPos) {
                buttonRoot.Swih('edit_pos_be');
            }
            else if (drillInfo) {
                var node = buttonRoot.Swih('cancel_' + drillInfo.type + '_be')[0];
                if (drillInfo.type === 'drill') {
                    node.Child('val').setLocaleKey(data.isMachine() ? 'ui.button_cancel_sc' : 'ui.button_cancel_drill');
                }
                else if (drillInfo.type === 'cure') {
                    node.Child('val').setLocaleKey('ui.button_cancel_cure');
                }
            }
            else {
                this.buttonNode_.active = false;
                break;
            }
            var player = GameHelper_1.gameHpr.player;
            var isLving = player.isInPawnLvingQueue(data.uid);
            this.buttonNode_.active = true;
            this.buttonNode_.Child('uplv_be').active = isEditPos && !isMachine && !data.isMaxLv() && !isLving;
            var avatarNode = this.buttonNode_.Child('avatar_be');
            var isCanAvatar = isOwner && !isBattleing && !isHero && !drillInfo;
            if (avatarNode.active = isCanAvatar) {
                var army = GameHelper_1.gameHpr.areaCenter.getArmy(data.aIndex, data.armyUid);
                var isNotAvatar = !army || army.pawns.some(function (m) { return m.isHero(); }) || player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL) <= 0 || player.getHeroSlots().every(function (m) { return !m.hero; }) || !player.checkCanAvatarPawn(data.id);
                avatarNode.opacity = isNotAvatar ? 100 : 255;
            }
        } while (false);
        // 刷新buff
        this.updateBuffs();
        this.buffNode_.stopAllActions();
        this.buffNode_.opacity = 0;
        cc.tween(this.buffNode_).delay(0.2).to(0.5, { opacity: 255 }).start();
    };
    PawnInfoPnlCtrl.prototype.onRemove = function () {
        GameHelper_1.gameHpr.uiShowPawnData = null;
        this.selectEquipBoxNode_.active = false;
        this.closeArmyList();
        this.syncInfoToServer();
        this.data.recordCurrHp(false);
        this.data = null;
    };
    PawnInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/head_be_n/attack_speed/edit/0/attack_speed_be
    PawnInfoPnlCtrl.prototype.onClickAttackSpeed = function (event, data) {
        var type = event.target.parent.name;
        this.data.changeAttackSpeed(type === '0' ? -1 : 1);
        this.attackSpeedEditLbl_.string = this.data.attackSpeed + '';
    };
    // path://root/button_n/root/edit_pos_be
    PawnInfoPnlCtrl.prototype.onClickEditPos = function (event, data) {
        this.emit(EventType_1.default.EDIT_PAWN_POS, this.data.aIndex, this.data.uid);
        ViewHelper_1.viewHelper.hidePnl('area/AreaArmy');
        this.hide();
    };
    // path://root/button_n/root/cancel_drill_be
    PawnInfoPnlCtrl.prototype.onClickCancelDrill = function (event, _) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox(this.data.isMachine() ? 'ui.cancel_sc_no_back_cost_tip' : 'ui.cancel_drill_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelDrill(_this.drillInfo); },
                cancel: function () { },
            });
        }
        this.cancelDrill(this.drillInfo);
    };
    // path://root/button_n/root/cancel_lving_be
    PawnInfoPnlCtrl.prototype.onClickCancelLving = function (event, _) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_lving_no_back_cost_tip', {
                ok: function () {
                    _this.cancelLving(_this.drillInfo);
                },
                cancel: function () { },
            });
        }
        this.cancelLving(this.drillInfo);
    };
    // path://root/equip_n/info/edit_equip_be
    PawnInfoPnlCtrl.prototype.onClickEditEquip = function (event, data) {
        this.showEquipList();
    };
    // path://select_equip_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectEquipBox = function (event, data) {
        this.closeEquipList();
    };
    // path://select_equip_box_be_n/root/list/view/content/equip_item_be
    PawnInfoPnlCtrl.prototype.onClickEquipItem = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        this.data.changeEquip(data);
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        if (this.data.id === 3104) { //陌刀兵还需要刷新技能信息
            this.skillNode_.Child('root/skills').children.forEach(function (m) {
                if (!m.Data) {
                }
                else if (m.Data.type === Enums_1.PawnSkillType.INSTABILITY_ATTACK) {
                    m.Data.desc_params = [_this.data.getAttackText()];
                }
                else if (m.Data.type === Enums_1.PawnSkillType.PEOPLE_BROKEN) {
                    m.Data.desc_params = [_this.data.getAttackTextByIndex(2)];
                }
            });
        }
        if (GameHelper_1.gameHpr.guide.isGuideById(3)) {
            this.closeEquipList();
        }
        else {
            this.updateEquipListSelect(data);
        }
    };
    // path://root/from_n/army/val/edit/edit_army_be
    PawnInfoPnlCtrl.prototype.onClickEditArmy = function (event, data) {
        this.showArmyList();
    };
    // path://select_army_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectArmyBox = function (event, data) {
        this.closeArmyList();
    };
    // path://select_army_box_be_n/root/list/army_item_be
    PawnInfoPnlCtrl.prototype.onClickArmyItem = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        if (data) {
            if (this.data.isHero() && data.isHasHero()) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_ONLY_AVATAR_ONE);
            }
            return this.changeArmy(data.uid, false);
        }
        else if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.changeArmy(name, true);
            }
        });
    };
    // path://root/skill_n/root/skills/skill_be
    PawnInfoPnlCtrl.prototype.onClickSkill = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/SkillInfoBox', event.target.Data);
    };
    // path://root/equip_n/info/equip_show_be
    PawnInfoPnlCtrl.prototype.onClickEquipShow = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data === null || data === void 0 ? void 0 : data.id) {
            ViewHelper_1.viewHelper.showPnl('common/EquipInfoBox', data);
        }
        else {
            this.showEquipList();
        }
    };
    // path://root/equip_n/info/bag/bag_be
    PawnInfoPnlCtrl.prototype.onClickBag = function (event, data) {
        if (this.data.owner !== GameHelper_1.gameHpr.getUid()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_OPEN_OTHER_TREASURE);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', [event.target.Data]);
        }
    };
    // path://root/attr_n_be
    PawnInfoPnlCtrl.prototype.onClickAttr = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/PawnAttrBox', this.data);
    };
    // path://select_skin_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectSkinBox = function (event, data) {
        this.closeSkinList(this.selectSkinBoxNode_.Child('skin_show').Data || 0);
    };
    // path://select_skin_box_be_n/root/list/view/content/skin_item_be
    PawnInfoPnlCtrl.prototype.onClickSkinItem = function (event, _) {
        var data = event.target.Data;
        data && this.updateSkinListSelect(data);
    };
    // path://root/head_be_n
    PawnInfoPnlCtrl.prototype.onClickHead = function (event, data) {
        audioMgr.playSFX('click');
        this.showSkinList();
    };
    // path://select_skin_box_be_n/root/button/buy_skin_be
    PawnInfoPnlCtrl.prototype.onClickBuySkin = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        if (data && data.gold > 0) {
            if (GameHelper_1.gameHpr.user.getGold() < data.gold) {
                return ViewHelper_1.viewHelper.showGoldNotEnough();
            }
            GameHelper_1.gameHpr.user.buyPawnSkin(data.id).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.closeSkinList(data.id);
                }
            });
        }
    };
    // path://select_equip_box_be_n/sync_equip_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncEquip = function (event, data) {
        this.settingSyncEquipNode_.active = true;
        this.syncEquipMaskNode_.active = true;
        var val = (GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0) + '';
        this.settingSyncEquipNode_.Child('lay').children.forEach(function (m) {
            m.Component(cc.Toggle).isChecked = m.name === val;
        });
    };
    // path://select_equip_box_be_n/sync_equip_mask_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncEquipMask = function (event, data) {
        var it = this.settingSyncEquipNode_.Child('lay').children.find(function (m) { return m.Component(cc.Toggle).isChecked; });
        var val = it ? Number(it.name) : 0;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF, val);
        this.settingSyncEquipNode_.active = false;
        this.syncEquipMaskNode_.active = false;
        this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
    };
    // path://root/button_n/uplv_be
    PawnInfoPnlCtrl.prototype.onClickUplv = function (event, data) {
        var _this = this;
        if (!this.data || this.data.isMaxLv() || this.data.isMachine()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('area/UpPawnLv', this.data, function (ok) { return __awaiter(_this, void 0, void 0, function () {
            var cond;
            var _a, _b;
            return __generator(this, function (_c) {
                if (!ok || !this.isValid) {
                    return [2 /*return*/, true];
                }
                else if (GameHelper_1.gameHpr.player.getUpScroll() < 1) {
                    ViewHelper_1.viewHelper.showAlert('toast.res_deficiency', { params: [Constant_1.CTYPE_NAME[Enums_1.CType.UP_SCROLL]] });
                    return [2 /*return*/, false];
                }
                cond = GameHelper_1.gameHpr.checkCondsByString((_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.attrJson) === null || _b === void 0 ? void 0 : _b.lv_cond);
                if ((cond === null || cond === void 0 ? void 0 : cond.type) === Enums_1.CType.BUILD_LV) {
                    ViewHelper_1.viewHelper.showAlert('toast.build_cond_unmet', { params: ['buildText.name_' + cond.id, cond.count] });
                    return [2 /*return*/, false];
                }
                return [2 /*return*/, this.upLvByUseScroll()];
            });
        }); });
    };
    // path://root/buff/buff_n/buff_icon_be
    PawnInfoPnlCtrl.prototype.onClickBuffIcon = function (event, _) {
        var data = event.target.Data;
        if (!data) {
        }
        else if (data.iconType === 3) { //韬略
            ViewHelper_1.viewHelper.showPnl('area/PawnStrategyInfo', this.data);
        }
        else if (data.iconType === 4) { //政策
            ViewHelper_1.viewHelper.showPnl('area/PolicyBuffInfo', data.buffs);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/BuffInfoBox', data);
        }
    };
    // path://root/head_be_n/lv/edit/0/edit_lv_be
    PawnInfoPnlCtrl.prototype.onClickEditLv = function (event, data) {
        var type = event.target.parent.name;
        var val = type === '0' ? -1 : 1;
        var lv = this.data.lv + val;
        if (lv > 6) {
            lv = 1;
        }
        else if (lv < 1) {
            lv = 6;
        }
        this.localUplv(this.data, lv);
    };
    // path://root/button_n/avatar_be
    PawnInfoPnlCtrl.prototype.onClickAvatar = function (event, _) {
        var _this = this;
        var pawn = this.data;
        var army = GameHelper_1.gameHpr.areaCenter.getArmy(pawn.aIndex, pawn.armyUid);
        if (!army) {
            return;
        }
        else if (army.pawns.some(function (m) { return m.isHero(); })) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_ONLY_AVATAR_ONE);
        }
        else if (GameHelper_1.gameHpr.player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL) <= 0) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_build_first', { params: ['buildText.name_' + Enums_1.BUILD_NID.HERO_HALL] });
        }
        else if (GameHelper_1.gameHpr.player.getHeroSlots().every(function (m) { return !m.hero; })) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_worship_hero'); //请先在英雄殿供奉一个英雄
        }
        ViewHelper_1.viewHelper.showPnl('area/SelectAvatarHero', pawn.id, function (portrayalId) {
            if (!_this.isValid || !portrayalId) {
                return;
            }
            GameHelper_1.gameHpr.player.changePawnPortrayal(pawn.aIndex, pawn.armyUid, pawn.uid, portrayalId).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.hide();
                    ViewHelper_1.viewHelper.hidePnl('area/AreaArmy');
                    // 聚焦士兵
                    _this.emit(EventType_1.default.FOCUS_PAWN, { index: pawn.aIndex, uid: pawn.uid, point: pawn.point });
                }
            });
        });
    };
    // path://root/skill_n/root/portrayal_skill_be
    PawnInfoPnlCtrl.prototype.onClickPortrayalSkill = function (event, data) {
        audioMgr.playSFX('click');
        if (this.data.portrayal) {
            ViewHelper_1.viewHelper.showPnl('common/PortrayalInfoBox', this.data.portrayal, 'pawn', this.data.owner);
        }
    };
    // path://root/equip_n/info/pet_show_be
    PawnInfoPnlCtrl.prototype.onClickPetShow = function (event, _) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        if (id) {
            ViewHelper_1.viewHelper.showPnl('common/PetInfoBox', id, Constant_1.SUMMON_LV[this.data.lv]);
        }
        else {
            this.showPetList();
        }
    };
    // path://root/equip_n/info/edit_pet_be
    PawnInfoPnlCtrl.prototype.onClickEditPet = function (event, data) {
        this.showPetList();
    };
    // path://select_pet_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectPetBox = function (event, data) {
        this.closePetList();
    };
    // path://select_pet_box_be_n/root/list/view/content/pet_item_be
    PawnInfoPnlCtrl.prototype.onClickPetItem = function (event, _) {
        var id = event.target.Data;
        if (id === Constant_1.DEFAULT_PET_ID || GameHelper_1.gameHpr.player.getKillRecordMap()[id]) {
            this.data.setPetId(id);
        }
        this.updatePetListSelect(id);
    };
    // path://select_skin_box_be_n/sync_skin_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncSkin = function (event, data) {
        this.settingSyncSkinNode_.active = true;
        this.syncSkinMaskNode_.active = true;
        var val = (GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0) + '';
        this.settingSyncSkinNode_.Child('lay').children.forEach(function (m) {
            m.Component(cc.Toggle).isChecked = m.name === val;
        });
    };
    // path://select_skin_box_be_n/sync_skin_mask_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncSkinMask = function (event, data) {
        var it = this.settingSyncSkinNode_.Child('lay').children.find(function (m) { return m.Component(cc.Toggle).isChecked; });
        var val = it ? Number(it.name) : 0;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF, val);
        this.settingSyncSkinNode_.active = false;
        this.syncSkinMaskNode_.active = false;
        this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
    };
    // path://root/button_n/root/cancel_cure_be
    PawnInfoPnlCtrl.prototype.onClickCancelCure = function (event, data) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelCure(_this.drillInfo); },
                cancel: function () { },
            });
        }
        this.cancelCure(this.drillInfo);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    PawnInfoPnlCtrl.prototype.onUpdatePawnTreasure = function (pawn) {
        if (pawn.uid === this.data.uid) {
            this.updateBag(this.equipNode_.Child('info/bag'), pawn.treasures);
        }
    };
    PawnInfoPnlCtrl.prototype.onUpdateArmy = function (army) {
        if (this.data.armyUid === army.uid) {
            var uid_1 = this.data.uid;
            this.data = army.pawns.find(function (m) { return m.uid === uid_1; }) || this.data;
        }
    };
    // 刷新buff
    PawnInfoPnlCtrl.prototype.onUpdateBuff = function () {
        this.updateBuffs();
    };
    // 刷新士兵信息
    PawnInfoPnlCtrl.prototype.onUpdatePawnInfo = function () {
        this.updateLv();
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        this.updateSkills();
        var uplvButton = this.buttonNode_.Child('uplv_be');
        uplvButton.active = uplvButton.active && !this.data.isMaxLv();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateLv = function () {
        var data = this.data;
        var lvNode = this.headNode_.Child('lv'), isNoLv = data.isMachine() || data.isBuilding();
        if (lvNode.Child('edit').active = !isNoLv && this.fromTo === 'book') {
            lvNode.Child('name').Color('#756963').setLocaleKey('ui.level');
            lvNode.Child('val').active = false;
            this.lvEditLbl_.string = data.lv + '';
        }
        else if (lvNode.Child('val').active = !isNoLv) {
            var hasOwner = !!data.owner, isOwner = data.isOwner(), isLving = GameHelper_1.gameHpr.player.isInPawnLvingQueue(data.uid);
            var lvColor = hasOwner && data.isMaxLv() ? '#B6A591' : '#756963';
            lvNode.Child('name').Color(lvColor).setLocaleKey('ui.level');
            lvNode.Child('val/0', cc.Label).Color(lvColor).string = data.lv + '';
            lvNode.Child('val/up').active = isOwner && isLving && !this.drillInfo;
        }
        else {
            lvNode.Child('name').Color('#B6A591').setLocaleKey('ui.not_lv');
        }
    };
    PawnInfoPnlCtrl.prototype.updateSkills = function () {
        this.skillNode_.active = ViewHelper_1.viewHelper.updatePawnSkills(this.skillNode_.Child('root'), this.data, this.key);
    };
    PawnInfoPnlCtrl.prototype.updateListPosition = function () {
        if (this.preRootHeight !== this.root.height) {
            this.preRootHeight = this.root.height;
            // 皮肤列表
            var node = this.selectSkinBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('skin_list'), this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            this.settingSyncSkinNode_.setPosition(node.getPosition());
            this.settingSyncSkinNode_.scale = this.root.scale;
            node = this.selectSkinBoxNode_.Child('skin_show');
            node.setPosition(ut.convertToNodeAR(this.headNode_, this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            node = this.syncSkinNode_;
            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('sync_pos'), this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            // 装备列表
            node = this.selectEquipBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_list'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            this.settingSyncEquipNode_.setPosition(node.getPosition());
            this.settingSyncEquipNode_.scale = this.root.scale;
            node = this.selectEquipBoxNode_.Child('equip_show');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_show_be'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            node = this.syncEquipNode_;
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/sync_pos'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            // 宠物列表
            node = this.selectPetBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_list'), this.selectPetBoxNode_));
            node.scale = this.root.scale;
            node = this.selectPetBoxNode_.Child('pet_show');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_show_be'), this.selectPetBoxNode_));
            node.scale = this.root.scale;
            // 军队列表
            node = this.selectArmyBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.fromNode_.Child('army/val/edit/list'), this.selectArmyBoxNode_));
            node.scale = this.root.scale;
        }
    };
    // 显示皮肤列表 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.showSkinList = function () {
        var _this = this;
        var _a, _b;
        this.headNode_.Child('edit').active = this.headNode_.Child('val').active = false;
        this.selectSkinBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectSkinBoxNode_.Child('root');
        var skins = GameHelper_1.gameHpr.user.getPawnSkins(this.data.id), len = skins.length;
        var sv = root.Child('list', cc.ScrollView);
        sv.Child('empty').active = len === 0;
        sv.Items(skins, function (it, data) {
            it.Data = data;
            var valNode = it.Child('val');
            valNode.opacity = data.unlock ? 255 : 120;
            ResHelper_1.resHelper.loadPawnHeadIcon((data === null || data === void 0 ? void 0 : data.id) || _this.data.id, valNode, _this.key);
        });
        var skinId = this.data.skinId;
        var index = skins.findIndex(function (m) { return m.id === skinId; });
        if (index === -1) {
            index = 0;
            skinId = this.data.skinId = (_b = (_a = skins[index]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0;
        }
        var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
        var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
        var minx = Math.max(w - pw, 0);
        sv.stopAutoScroll();
        sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        this.updateSkinListSelect(skins[index]);
    };
    PawnInfoPnlCtrl.prototype.closeSkinList = function (skinId) {
        this.headNode_.Child('val').active = true;
        this.headNode_.Child('edit').active = this.isCanEditSkin;
        this.selectSkinBoxNode_.active = false;
        if (this.data.skinId !== skinId) {
            this.data.skinId = skinId;
            ResHelper_1.resHelper.loadPawnHeadIcon(this.data.getViewId(), this.headNode_.Child('val'), this.key);
            if (this.fromTo !== 'book') {
                if (!this.data.uid && !this.drillInfo) {
                    GameHelper_1.gameHpr.player.changeConfigPawnInfoByData(this.data);
                }
                eventCenter.emit(EventType_1.default.CHANGE_PAWN_SKIN, this.data);
            }
        }
    };
    PawnInfoPnlCtrl.prototype.updateSkinListSelect = function (skin) {
        var _a;
        var root = this.selectSkinBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var _a;
            var id = ((_a = it.Data) === null || _a === void 0 ? void 0 : _a.id) || 0;
            it.Child('select1').active = it.Child('select2').active = skin.id === id;
            it.Component(cc.Button).interactable = skin.id !== id;
        });
        // 显示选择的
        if (skin.unlock || !skin.id) {
            var node = this.selectSkinBoxNode_.Child('skin_show');
            node.Data = skin.id;
            ResHelper_1.resHelper.loadPawnHeadIcon(skin.id || this.data.id, node.Child('val'), this.key);
        }
        // 显示信息
        var desc = skin.id ? ((_a = assetsMgr.getJsonData('pawnSkin', skin.id)) === null || _a === void 0 ? void 0 : _a.desc) || '' : 'ui.default_pawn_skin';
        root.Child('desc').setLocaleKey(desc);
        var stateNode = root.Child('state');
        if (stateNode.active = !!skin.id && !skin.gold) {
            stateNode.Color(skin.unlock ? '#4AB32E' : '#A18876').setLocaleKey(skin.unlock ? 'ui.yet_owned' : 'ui.not_owned');
        }
        var buttonNode = root.Child('button');
        if (buttonNode.active = !!skin.gold) {
            var buyNode = buttonNode.Child('buy_skin_be');
            buyNode.Data = skin;
            buyNode.Child('lay/gold/val', cc.Label).string = skin.gold + '';
        }
    };
    // 刷新装备信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateEquipInfo = function (node, equip) {
        equip = node.Data = equip || this.data.equip;
        var isOwner = this.data.isOwner() && this.fromTo !== 'drillground';
        var isCanEditEquip = isOwner && !this.isBattleing && this.data.getState() === Enums_1.PawnState.NONE && (!this.fromTo || this.fromTo === 'area_army');
        node.Component(cc.Button).interactable = !!(equip === null || equip === void 0 ? void 0 : equip.id) || this.isConfPawn || isCanEditEquip;
        if (equip === null || equip === void 0 ? void 0 : equip.id) {
            ResHelper_1.resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount());
        }
        else if (isOwner || this.isCanEdit || this.isConfPawn) {
            node.Swih('add')[0].Child('dot').active = GameHelper_1.gameHpr.player.getEquips().length > 0;
        }
        else {
            node.Swih('');
        }
    };
    // 显示装备列表
    PawnInfoPnlCtrl.prototype.showEquipList = function () {
        var _this = this;
        var _a, _b;
        if (this.data.isOwner() && this.isBattleing) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        this.selectEquipBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectEquipBoxNode_.Child('root');
        var equips = GameHelper_1.gameHpr.player.getPawnEquips(this.data.id), len = equips.length;
        var randomArr = ut.stringToNumbers((_b = (_a = assetsMgr.getJson('equipBase').get('exclusive_pawn', this.data.id)) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.random, ',');
        var isHp = randomArr[0] > randomArr[1];
        equips.sort(function (a, b) {
            var aw = a.isExclusive() ? 1 : 0, bw = b.isExclusive() ? 1 : 0;
            if (isHp) {
                aw = aw * 10 + (a.hp ? 1 : 0);
                bw = bw * 10 + (b.hp ? 1 : 0);
            }
            else {
                aw = aw * 10 + (a.attack ? 1 : 0);
                bw = bw * 10 + (b.attack ? 1 : 0);
            }
            return bw - aw;
        });
        var equip = this.equipNode_.Child('info/equip_show_be').Data;
        var sv = root.Child('list', cc.ScrollView);
        sv.Child('empty').active = len === 0;
        sv.Items(equips, function (it, data) {
            it.Data = data;
            ResHelper_1.resHelper.loadEquipIcon(data.id, it.Child('val'), _this.key, data.getSmeltCount());
            it.Child('recommend').active = false;
        });
        if (equip === null || equip === void 0 ? void 0 : equip.uid) {
            var index = equips.findIndex(function (m) { return m.uid === equip.uid; });
            var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
            var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
            var minx = Math.max(w - pw, 0);
            sv.stopAutoScroll();
            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        }
        else {
            sv.scrollToLeft();
        }
        this.updateEquipListSelect(equip);
    };
    // 刷新选择信息
    PawnInfoPnlCtrl.prototype.updateEquipListSelect = function (equip) {
        var _this = this;
        var root = this.selectEquipBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var data = it.Data;
            var select = it.Child('select').active = (equip === null || equip === void 0 ? void 0 : equip.uid) === (data === null || data === void 0 ? void 0 : data.uid);
            it.Component(cc.Button).interactable = !select;
        });
        // 显示选择的
        var node = this.selectEquipBoxNode_.Child('equip_show');
        if (equip === null || equip === void 0 ? void 0 : equip.id) {
            ResHelper_1.resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount());
        }
        else {
            node.Swih('add');
        }
        // 显示信息
        root.Child('empty').active = !(equip === null || equip === void 0 ? void 0 : equip.id);
        var sv = root.Child('info', cc.ScrollView), info = sv.content;
        sv.stopAutoScroll();
        info.y = 0;
        if (sv.setActive(!!(equip === null || equip === void 0 ? void 0 : equip.id))) {
            ViewHelper_1.viewHelper.updateEquipView(info, equip, this.key);
            ut.waitNextFrame(2).then(function () {
                if (_this.isValid) {
                    sv.node.height = Math.min(320, info.height + 4);
                    sv.node.Child('view', cc.Widget).updateAlignment();
                }
            });
        }
    };
    PawnInfoPnlCtrl.prototype.closeEquipList = function () {
        this.selectEquipBoxNode_.active = false;
        this.updateEquipInfo(this.equipNode_.Child('info/equip_show_be'));
    };
    // 刷新宠物信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updatePetInfo = function (node, id) {
        id = node.Data = id || this.data.petId || Constant_1.DEFAULT_PET_ID;
        ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, node.Child('val'), this.key);
    };
    // 显示宠物列表
    PawnInfoPnlCtrl.prototype.showPetList = function () {
        var _this = this;
        if (this.data.isOwner() && this.isBattleing) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        this.selectPetBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectPetBoxNode_.Child('root');
        var pets = assetsMgr.getJson('pawnBase').datas.filter(function (m) { return m.type === Enums_1.PawnType.BEAST && !!m.velocity; }).sort(function (a, b) { return a.drill_time - b.drill_time; }), len = pets.length;
        var id = this.equipNode_.Child('info/pet_show_be').Data;
        var sv = root.Child('list', cc.ScrollView);
        var killRecordMap = GameHelper_1.gameHpr.player.getKillRecordMap();
        sv.Items(pets, function (it, json) {
            it.Data = json.id;
            var icon = it.Child('val');
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(json.id, icon, _this.key);
            icon.opacity = json.id === Constant_1.DEFAULT_PET_ID || killRecordMap[json.id] ? 255 : 120;
        });
        if (id) {
            var index = pets.findIndex(function (m) { return m.id === id; });
            var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
            var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
            var minx = Math.max(w - pw, 0);
            sv.stopAutoScroll();
            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        }
        else {
            sv.scrollToLeft();
        }
        this.updatePetListSelect(id);
    };
    // 刷新选择信息
    PawnInfoPnlCtrl.prototype.updatePetListSelect = function (id) {
        var root = this.selectPetBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            it.Child('select').active = id === it.Data;
            it.Component(cc.Button).interactable = id !== it.Data;
        });
        // 显示选择的
        var node = this.selectPetBoxNode_.Child('pet_show');
        if (id) {
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, node.Swih('val')[0], this.key);
        }
        else {
            node.Swih('add');
        }
        // 显示信息
        var lv = id === Constant_1.DEFAULT_PET_ID || GameHelper_1.gameHpr.player.getKillRecordMap()[id] ? Constant_1.SUMMON_LV[this.data.lv] : 0;
        ViewHelper_1.viewHelper.updatePetView(root.Child('info'), id, lv);
    };
    PawnInfoPnlCtrl.prototype.closePetList = function () {
        this.selectPetBoxNode_.active = false;
        this.updatePetInfo(this.equipNode_.Child('info/pet_show_be'));
    };
    // 刷新军队信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateArmyInfo = function (node, armyName) {
        node = node || this.fromNode_.Child('army');
        armyName = armyName || this.data.armyName;
        node.Child('val/name/val', cc.Label).string = armyName;
    };
    // 显示军队列表
    PawnInfoPnlCtrl.prototype.showArmyList = function () {
        var _a;
        this.selectArmyBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectArmyBoxNode_.Child('root');
        var uid = this.data.armyUid;
        var arr = ((_a = GameHelper_1.gameHpr.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys) || [];
        var armys = [];
        arr.forEach(function (m) {
            if (m.uid == uid) {
                armys.unshift(m);
            }
            else if (m.isCanDrillPawn() && m.getActPawnCount() < Constant_1.ARMY_PAWN_MAX_COUNT) {
                armys.push(m);
            }
        });
        if (armys.length < GameHelper_1.gameHpr.player.getArmyMaxCount()) {
            armys.push(null);
        }
        var len = armys.length;
        root.Swih('list')[0].Items(armys, function (it, data, i) {
            it.Data = data;
            var select = uid === (data === null || data === void 0 ? void 0 : data.uid);
            it.Child('name', cc.Label).Color(select ? '#B6A591' : '#756963').string = (data === null || data === void 0 ? void 0 : data.name) || '';
            it.Child('line').active = i < (len - 1);
            it.Child('select').active = select;
            it.Child('add').active = !data;
            it.Component(cc.Button).interactable = !select;
        });
    };
    PawnInfoPnlCtrl.prototype.closeArmyList = function () {
        this.selectArmyBoxNode_.active = false;
    };
    // 改变军队
    PawnInfoPnlCtrl.prototype.changeArmy = function (newArmyUid, isNewCreate) {
        return __awaiter(this, void 0, void 0, function () {
            var pawn, index, armyUid, uid, attackSpeed, equipUid, skinId, petId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        pawn = this.data;
                        index = pawn.aIndex;
                        armyUid = pawn.armyUid;
                        uid = pawn.uid;
                        attackSpeed = pawn.attackSpeed;
                        equipUid = pawn.equip.uid;
                        skinId = pawn.skinId;
                        petId = pawn.petId;
                        if (!isNewCreate) {
                            pawn.armyUid = newArmyUid;
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnArmy({ index: index, armyUid: armyUid, uid: uid, newArmyUid: newArmyUid, isNewCreate: isNewCreate, attackSpeed: attackSpeed, equipUid: equipUid, skinId: skinId, petId: petId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            pawn.armyUid = armyUid;
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        pawn.changeArmy(data);
                        if (this.isValid) {
                            this.preAttackSpeed = attackSpeed;
                            this.preEquipUid = equipUid;
                            this.preSkinId = skinId;
                            this.updateArmyInfo();
                            this.closeArmyList();
                        }
                        if (this.fromTo === 'area_army') {
                            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, index);
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 背包
    PawnInfoPnlCtrl.prototype.updateBag = function (node, treasures) {
        var _this = this;
        var _a;
        if (this.equipNode_.active && this.data) {
            var data = this.data;
            var cap = Math.max((treasures === null || treasures === void 0 ? void 0 : treasures.length) || 0, ((_a = data.baseJson) === null || _a === void 0 ? void 0 : _a.bag_cap) || 0);
            node.Items(cap, function (it, _, i) {
                var _a;
                var treasure = it.Data = treasures[i];
                it.Swih(treasure ? 'val' : 'empty');
                if (it.Component(cc.Button).interactable = !!treasure) {
                    var state = treasure.rewards.length > 0 ? 1 : 0;
                    ResHelper_1.resHelper.loadIcon('icon/treasure_' + (((_a = treasure.json) === null || _a === void 0 ? void 0 : _a.lv) || 1) + '_' + state, it.Child('val', cc.Sprite), _this.key);
                }
            });
        }
        else {
            node.Swih('');
        }
    };
    // 刷新buff
    PawnInfoPnlCtrl.prototype.updateBuffs = function () {
        var _this = this;
        var _a;
        var buffs = [], policyBuffs = [];
        var heroSkill = (_a = this.data.portrayal) === null || _a === void 0 ? void 0 : _a.skill;
        this.data.buffs.forEach(function (m) {
            var _a, _b;
            if (m.iconType === 4) {
                return policyBuffs.push(m);
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || m.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD) {
                var buff = buffs.find(function (b) { return !!b && (b.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || b.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD); });
                if (buff) {
                    buff.tempParam = m.value;
                }
                else {
                    buffs.push(m);
                }
                return;
            }
            else if (m.type === Enums_1.BuffType.DELAY_DEDUCT_HP) {
                m.tempParam = (_b = (_a = _this.data.getEquipEffectByType(Enums_1.EquipEffectType.FIXED_DAMAGE)) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 0;
            }
            else if (m.type === Enums_1.BuffType.THOUSAND_UMBRELLA) { //千机伞
                var effect = _this.data.getEquipEffectByType(Enums_1.EquipEffectType.THOUSAND_UMBRELLA);
                m.tempParam = [(effect === null || effect === void 0 ? void 0 : effect.value) || 1, (effect === null || effect === void 0 ? void 0 : effect.odds) || 1];
                var v = m.tempParam[m.value];
                if (v) {
                    m.tempParam[m.value] = v * 2;
                }
            }
            else if (m.type === Enums_1.BuffType.TOUGH) { //曹仁 判断是否叠满
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.CAO_REN ? heroSkill.value : 50;
            }
            else if (m.type === Enums_1.BuffType.TIGER_MANIA) { //许褚 虎痴
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.XU_CHU ? heroSkill.value : 200;
            }
            else if (m.type === Enums_1.BuffType.CHECK_ABNEGATION) { //吕蒙 检测克己
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.LV_MENG ? heroSkill.value : 0;
            }
            else if (m.type === Enums_1.BuffType.CHECK_LITTLE_GIRL) { //孙尚香 枭姬
                m.tempParam = _this.data.isMaxLv() ? 2 : 1;
            }
            else if (m.type === Enums_1.BuffType.COURAGEOUSLY) { //典韦 奋勇是否满层
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.DIAN_WEI ? heroSkill.value : 50;
            }
            else if (m.type === Enums_1.BuffType.RECURRENCE) { //孟获 再起
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.MENG_HUO ? heroSkill.value : 15;
            }
            if (m.icon) {
                buffs.push(m);
            }
        });
        buffs.sort(function (a, b) {
            var aw = a ? a.effectType : 100, bw = b ? b.effectType : 100;
            return bw - aw;
        });
        // 是否有韬略
        if (this.data.isHasStrategy()) {
            buffs.unshift({ iconType: 3, icon: 0 });
        }
        // 是否有政策
        if (policyBuffs.length > 0) {
            buffs.unshift({ iconType: 4, icon: 1000, buffs: policyBuffs });
        }
        this.buffNode_.Items(buffs, function (it, data) {
            it.Data = data;
            it.Component(cc.MultiFrame).setFrame(data.iconType);
            ResHelper_1.resHelper.loadBuffIcon(data.icon, it.Child('val'), _this.key, false);
        });
    };
    // 使用卷轴升级士兵
    PawnInfoPnlCtrl.prototype.upLvByUseScroll = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawn, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.syncInfoToServer(true)]; //先同步一下属性
                    case 1:
                        _b.sent(); //先同步一下属性
                        pawn = this.data;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqUseUpScrollUpPawnLv({ index: pawn.aIndex, armyUid: pawn.armyUid, uid: pawn.uid })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                            return [2 /*return*/, false];
                        }
                        GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                        ViewHelper_1.viewHelper.showAlert('toast.up_pawn_lv_succeed');
                        this.localUplv(pawn, data.lv);
                        if (this.fromTo === 'area_army') {
                            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, pawn.index);
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    PawnInfoPnlCtrl.prototype.localUplv = function (pawn, lv) {
        pawn.lv = lv;
        pawn.updateAttrJson();
        pawn.curHp = pawn.getMaxHp();
        pawn.recordCurrHp(true);
        if (this.isValid) {
            this.onUpdatePawnInfo();
        }
    };
    // 同步信息到服务器
    PawnInfoPnlCtrl.prototype.syncInfoToServer = function (wait) {
        return __awaiter(this, void 0, void 0, function () {
            var data, isEquip, isBattleing, isChange, id, equipUid, skinId, attackSpeed, res, syncEquip, syncSkin, res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.fromTo === 'book') {
                            return [2 /*return*/];
                        }
                        data = this.data;
                        isEquip = this.preEquipUid !== data.equip.uid;
                        isBattleing = data.isBattleing() || GameHelper_1.gameHpr.isBattleingByIndex(data.aIndex);
                        isChange = this.preAttackSpeed !== data.attackSpeed || isEquip || this.preSkinId !== data.skinId || this.prePetId !== data.petId;
                        if (!(!data.uid && !this.drillInfo && isChange)) return [3 /*break*/, 2];
                        id = data.id, equipUid = data.equip.uid, skinId = data.skinId, attackSpeed = data.attackSpeed;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangeConfigPawnEquip({ id: id, equipUid: equipUid, skinId: skinId, attackSpeed: attackSpeed }, wait)];
                    case 1:
                        res = _a.sent();
                        if (!res.err) {
                            GameHelper_1.gameHpr.player.changeConfigPawnInfo(id, equipUid, skinId, attackSpeed);
                        }
                        this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, data.index);
                        return [3 /*break*/, 4];
                    case 2:
                        if (!(data.isOwner() && !isBattleing && isChange)) return [3 /*break*/, 4];
                        syncEquip = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0;
                        syncSkin = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnAttr({
                                index: data.aIndex,
                                armyUid: data.armyUid,
                                uid: data.uid,
                                attackSpeed: data.attackSpeed,
                                equipUid: data.equip.uid,
                                syncEquip: syncEquip,
                                skinId: data.skinId,
                                syncSkin: syncSkin,
                                petId: data.petId,
                            }, wait)];
                    case 3:
                        res = _a.sent();
                        if (!res.err && !!syncEquip && isEquip) {
                            ViewHelper_1.viewHelper.showAlert('toast.replace_pawn_equp_suc_' + syncEquip, { params: ['pawnText.name_' + data.id], showTime: 2 });
                        }
                        this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, data.index);
                        _a.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 取消招募
    PawnInfoPnlCtrl.prototype.cancelDrill = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        var isMachine = this.data.isMachine();
        NetHelper_1.netHelper.reqCancelDrillPawn({ index: index, buildUid: info.buid, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateOutputByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyDrillPawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnDrillQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: isMachine ? 'ui.cancel_sc_tip' : 'ui.cancel_drill_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // 取消训练
    PawnInfoPnlCtrl.prototype.cancelLving = function (info) {
        var _this = this;
        var index = info.index;
        var uid = info.uid;
        var id = info.id;
        var lv = info.lv;
        NetHelper_1.netHelper.reqCancelPawnLving({ index: index, uid: uid }).then(function (res) {
            var _a;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                GameHelper_1.gameHpr.player.updatePawnLevelingQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_a = data.needCost) === null || _a === void 0 ? void 0 : _a.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', { text: 'ui.cancel_lving_tip', id: id, cost: data.needCost });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // 取消治疗
    PawnInfoPnlCtrl.prototype.cancelCure = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        NetHelper_1.netHelper.reqCancelCurePawn({ index: index, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateOutputByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnCuringQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                _this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: 'ui.cancel_cure_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    PawnInfoPnlCtrl = __decorate([
        ccclass
    ], PawnInfoPnlCtrl);
    return PawnInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PawnInfoPnlCtrl;

cc._RF.pop();