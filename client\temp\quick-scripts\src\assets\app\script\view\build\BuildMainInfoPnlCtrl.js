"use strict";
cc._RF.push(module, '4ddc9BbGYpPmrbgT4WCZJuO', 'BuildMainInfoPnlCtrl');
// app/script/view/build/BuildMainInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BuildUnlockTipCmpt_1 = require("../cmpt/BuildUnlockTipCmpt");
var ccclass = cc._decorator.ccclass;
var BuildMainInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildMainInfoPnlCtrl, _super);
    function BuildMainInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.landNode_ = null; // path://root/pages_n/0/info/land_n
        _this.unlockTipNode_ = null; // path://root/pages_n/0/bottom/title/unlock_tip_n
        _this.policySv_ = null; // path://root/pages_n/1/info/policy_sv
        //@end
        _this.PKEY_TAB = 'MAIN_INFO_TAB';
        _this.tab = 0;
        _this.user = null;
        _this.player = null;
        _this.data = null;
        _this.unlockTipCmpt = null;
        return _this;
    }
    BuildMainInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_POLICY_SLOTS] = this.onUpdatePolicySlots, _b.enter = true, _b),
        ];
    };
    BuildMainInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt_1.default);
                return [2 /*return*/];
            });
        });
    };
    BuildMainInfoPnlCtrl.prototype.onEnter = function (data, tab) {
        this.data = data;
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    BuildMainInfoPnlCtrl.prototype.onRemove = function () {
    };
    BuildMainInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildMainInfoPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.tab = Number(event.node.name);
        var node = this.pagesNode_.Swih(type)[0];
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        if (type === 0) {
            this.landNode_.Child('val').setLocaleKey('ui.cur_land_count', GameHelper_1.gameHpr.getPlayerOweCellCount(GameHelper_1.gameHpr.getUid()));
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.POLICY_SLOT_CONF, this.key);
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            this.showPolicyInfo(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildMainInfoPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/policy/slot_nbe
    BuildMainInfoPnlCtrl.prototype.onClickSlot = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data, lv = Number(event.target.name);
        var isUnlock = this.data.lv >= lv;
        if (!isUnlock) {
            return ViewHelper_1.viewHelper.showAlert('ui.lv_unlock_new', { params: [assetsMgr.lang('ui.short_lv', lv), 'ui.ceri_type_name_1'] });
        }
        else if (!!(data === null || data === void 0 ? void 0 : data.isYetStudy())) {
            return ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', data.id, 'book');
        }
        else if (!data || data.selectIds.length === 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NEED_STUDY_PER_SLOT);
        }
        ViewHelper_1.viewHelper.showPnl('build/StudySelect', data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildMainInfoPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.POLICY_SLOT_CONF, this.key, data.lv);
        }
    };
    // 刷新政策
    BuildMainInfoPnlCtrl.prototype.onUpdatePolicySlots = function () {
        if (this.tab === 1) {
            this.showPolicyInfo(this.pagesNode_.Child(1));
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 内政
    BuildMainInfoPnlCtrl.prototype.showPolicyInfo = function (node) {
        var _this = this;
        var slot = node.Child('policy/slot_nbe'), buildLv = this.data.lv;
        var policys = this.player.getPolicySlots();
        slot.children.forEach(function (it) {
            var lv = Number(it.name);
            var data = it.Data = policys[lv];
            var isUnlock = buildLv >= lv, isSelect = !!(data === null || data === void 0 ? void 0 : data.isYetStudy());
            if (!isUnlock) { //还未解锁
                it.Swih('lock');
                // state.Color('#C34A32').setLocaleKey('ui.need_lv_unlock', lv)
            }
            else if (!isSelect) { //还未选择
                var canSelect = !!(data === null || data === void 0 ? void 0 : data.selectIds.length);
                it.Swih('add')[0].Child('dot').active = canSelect;
                // state.Color(canSelect ? '#49983C' : '#756963').setLocaleKey(canSelect ? 'ui.can_study' : 'ui.button_wait_study')
            }
            // 选择信息
            if (isSelect) {
                ResHelper_1.resHelper.loadPolicyIcon(data.id, it.Swih('icon')[0], _this.key);
            }
            // it.Component(cc.Button).interactable = isUnlock && !isSelect
        });
        this.updatePolicyEffectInfo();
    };
    // 内政效果
    BuildMainInfoPnlCtrl.prototype.updatePolicyEffectInfo = function () {
        var _this = this;
        var initItemHeight = 92;
        var datas = GameHelper_1.gameHpr.getPlayerPolicysBaseInfo();
        this.policySv_.stopAutoScroll();
        this.policySv_.Items(datas, function (it, data, i) {
            it.Child('name/val').setLocaleKey('policyText.name_' + data.id);
            var value = data.values[Math.min(data.up - 1, data.values.length - 1)];
            var params = "<color=#4AB32E>" + value + "</c>";
            var descRt = it.Child('desc', cc.RichText);
            descRt.setLocaleKey('policyText.desc_' + data.id, params);
            it.Child('policys').Items(data.styles, function (item, style) {
                ResHelper_1.resHelper.loadPolicyIcon(data.id, item.Child('icon'), _this.key);
                var styleSpr = item.Child('style', cc.Sprite);
                if (style >= 10) { // 季节政策
                    ResHelper_1.resHelper.loadIcon('icon/season_' + (style % 10), styleSpr, _this.key);
                }
                else if (style === 2) { // 联盟政策
                    ResHelper_1.resHelper.loadAlliIcon(GameHelper_1.gameHpr.alliance.getIcon(), styleSpr, _this.key);
                }
                else {
                    styleSpr.Component(cc.Sprite).spriteFrame = null;
                }
            });
            var h = Math.max(initItemHeight, (initItemHeight - descRt.lineHeight) + descRt.node.height);
            if (it.height !== h) {
                it.height = h;
                it.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
            }
        });
    };
    BuildMainInfoPnlCtrl = __decorate([
        ccclass
    ], BuildMainInfoPnlCtrl);
    return BuildMainInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildMainInfoPnlCtrl;

cc._RF.pop();