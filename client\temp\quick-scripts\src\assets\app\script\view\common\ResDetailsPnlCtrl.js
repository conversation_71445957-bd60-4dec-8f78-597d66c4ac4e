"use strict";
cc._RF.push(module, '0d9cfExgSVA1ot0V+P7Ia/i', 'ResDetailsPnlCtrl');
// app/script/view/common/ResDetailsPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ResDetailsPnlCtrl = /** @class */ (function (_super) {
    __extends(ResDetailsPnlCtrl, _super);
    function ResDetailsPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.landsSv_ = null; // path://root/pages/lands_sv
        _this.sumNode_ = null; // path://root/pages/sum_n
        _this.buyAddOpNode_ = null; // path://root/pages/sum_n/5/buy_add_op_be_n
        _this.curLbl_ = null; // path://root/pages/cur_l
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.type = 0;
        _this.resMap = {};
        return _this;
    }
    ResDetailsPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ADD_OUTPUT_TIME] = this.onUpdateAddOutputTime, _a.enter = true, _a),
        ];
    };
    ResDetailsPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.buyAddOpNode_.Child('val', cc.Label).string = Constant_1.ADD_OUTPUT_GOLD + '';
                return [2 /*return*/];
            });
        });
    };
    ResDetailsPnlCtrl.prototype.onEnter = function (type) {
        this.initResDist();
        this.tabsTc_.Tabs(type);
    };
    ResDetailsPnlCtrl.prototype.onRemove = function () {
    };
    ResDetailsPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    ResDetailsPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        this.showResDetails(Number(event.node.name));
    };
    // path://root/pages/sum_n/5/buy_add_op_be_n
    ResDetailsPnlCtrl.prototype.onClickBuyAddOp = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.world.isGameOver()) {
            return ViewHelper_1.viewHelper.showAlert('toast.gold_increase_output');
        }
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return;
        }
        var type = this.type;
        var hasAdd = !!GameHelper_1.gameHpr.player.getAddOutputSurplusTime()[type];
        ViewHelper_1.viewHelper.showMessageBox(hasAdd ? 'ui.add_output_desc_1' : 'ui.add_output_desc_0', {
            params: [Constant_1.ADD_OUTPUT_GOLD, Constant_1.ADD_OUTPUT_RATIO, Constant_1.CTYPE_NAME[type]],
            ok: function () { return GameHelper_1.gameHpr.player.buyAddOutputTime(type).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.showResDetails(type);
                }
            }); },
            cancel: function () { },
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    ResDetailsPnlCtrl.prototype.onUpdateAddOutputTime = function () {
        this.showResDetails(this.type);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ResDetailsPnlCtrl.prototype.initResDist = function () {
        var _a;
        var _this = this;
        var _b, _c;
        this.resMap = (_a = {}, _a[Enums_1.CType.CEREAL] = { arr: [], sum: 0 }, _a[Enums_1.CType.TIMBER] = { arr: [], sum: 0 }, _a[Enums_1.CType.STONE] = { arr: [], sum: 0 }, _a);
        (_c = (_b = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _b === void 0 ? void 0 : _b.cells) === null || _c === void 0 ? void 0 : _c.forEach(function (cell) {
            if (cell.isHasRes()) {
                var json_1 = cell.getResJson() || {};
                Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; }).forEach(function (m) {
                    var info = _this.resMap[Constant_1.RES_FIELDS_CTYPE[m]], val = json_1[m];
                    var it = info.arr.find(function (x) { return (x.cell.landId === cell.landId || x.cell.cityId === cell.cityId) && x.val === val; });
                    if (it) {
                        it.count += 1;
                    }
                    else {
                        info.arr.push({ cell: cell, val: val, count: 1 });
                    }
                    info.sum += val;
                });
            }
        });
        for (var key in this.resMap) {
            this.resMap[key].arr.sort(function (a, b) { return b.val - a.val; });
        }
    };
    ResDetailsPnlCtrl.prototype.showResDetails = function (type) {
        this.type = type;
        var player = GameHelper_1.gameHpr.player;
        var info = this.resMap[type], len = info.arr.length;
        this.landsSv_.Child('empty').active = len === 0;
        this.landsSv_.Items(info.arr, function (it, data, i) {
            it.Child('name').setLocaleKey(data.cell.getName());
            it.Child('val', cc.Label).string = data.val + '';
            it.Child('count', cc.Label).string = data.count + '';
            it.Child('line').active = i !== 4;
        });
        // 初始资源
        var output = player.isCapture() ? 0 : Constant_1.INIT_RES_OUTPUT, addNum = 0;
        this.sumNode_.Child('3/val', cc.Label).string = output + '';
        // 土地资源
        output += info.sum;
        this.sumNode_.Child('1/val', cc.Label).string = info.sum + '';
        // 内政加成 固定
        var policyAddRes = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.RES_OUTPUT);
        if (this.sumNode_.Child('2').active = policyAddRes > 0) {
            output += policyAddRes;
            this.sumNode_.Child('2/val', cc.Label).string = policyAddRes + '';
        }
        // 商城购买 百分比
        var time = player.getAddOutputSurplusTime()[type] || 0;
        addNum = time > 0 ? Math.floor(output * Constant_1.ADD_OUTPUT_RATIO * 0.01) : 0;
        output += addNum;
        this.sumNode_.Child('5/desc/val').setLocaleKey('ui.add_output_desc', '+' + Constant_1.ADD_OUTPUT_RATIO);
        this.sumNode_.Child('5/val', cc.Label).string = addNum + '';
        // 购买加成
        if (this.sumNode_.Child('5').active = !GameHelper_1.gameHpr.isNoviceMode) {
            this.updateAddOutputStateTime(this.sumNode_.Child('5/desc/state'), time);
        }
        // 粮耗
        var cost = type === Enums_1.CType.CEREAL ? player.getCerealConsume() : 0;
        if (this.sumNode_.Child('7').active = cost > 0) {
            output -= cost;
            this.sumNode_.Child('7/val', cc.Label).string = '-' + cost;
        }
        // 总
        this.sumNode_.Child('6/val', cc.Label).string = output + '';
        var cap = type === Enums_1.CType.CEREAL ? player.getGranaryCap() : player.getWarehouseCap();
        this.curLbl_.setLocaleKey('ui.cur_res_cap', GameHelper_1.gameHpr.getCountByCType(type) + '/' + cap);
    };
    ResDetailsPnlCtrl.prototype.updateAddOutputStateTime = function (node, time) {
        var hasAdd = time > 0, color = hasAdd ? '#59A733' : '#D7634D';
        this.buyAddOpNode_.Child('desc').setLocaleKey(hasAdd ? 'ui.button_lengthen' : 'ui.button_enable');
        node.children.forEach(function (m) { return m.Color(color); });
        node.Child('val').setLocaleKey(hasAdd ? 'ui.takeeffecting' : 'ui.not_takeeffect');
        node.Child('s').active = hasAdd;
        var lbl = node.Child('time', cc.LabelTimer);
        if (lbl.setActive(hasAdd)) {
            lbl.run(time * 0.001);
        }
    };
    ResDetailsPnlCtrl = __decorate([
        ccclass
    ], ResDetailsPnlCtrl);
    return ResDetailsPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ResDetailsPnlCtrl;

cc._RF.pop();