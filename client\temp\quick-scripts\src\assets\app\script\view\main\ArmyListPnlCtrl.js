"use strict";
cc._RF.push(module, '9c98fj2wCNEv4PGkOm+oFHe', 'ArmyListPnlCtrl');
// app/script/view/main/ArmyListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var ArmyListPnlCtrl = /** @class */ (function (_super) {
    __extends(ArmyListPnlCtrl, _super);
    function ArmyListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.player = null;
        return _this;
    }
    ArmyListPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ARMY_TREASURE] = this.onUpdateArmyTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY_AREA_INDEX] = this.onUpdateArmyAreaIndex, _b.enter = true, _b),
        ];
    };
    ArmyListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    ArmyListPnlCtrl.prototype.onEnter = function () {
        this.tabsTc_.Tabs(0);
    };
    ArmyListPnlCtrl.prototype.onRemove = function () {
    };
    ArmyListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/pages_n/0/list/view/content/item/pos_be
    ArmyListPnlCtrl.prototype.onClickPos = function (event, _) {
        var _a, _b;
        var data = event.target.parent.Data;
        if (data) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos((_b = (_a = data.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data.index);
        }
    };
    // path://root/tabs_tc_tce
    ArmyListPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = event.node.name;
        var node = this.pagesNode_.Swih(type)[0];
        if (type === '0') {
            this.showAllArmy(node);
        }
        else if (type === '1') {
            this.showArmyMarchRecord(node);
        }
        else if (type === '2') {
            this.showArmyBattleRecord(node);
        }
    };
    // path://root/pages_n/2/view/content/item/5/playback_be
    ArmyListPnlCtrl.prototype.onClickPlayback = function (event, _) {
        var data = event.target.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid;
        if (uid) {
            this.playbackBattle(uid);
        }
    };
    // path://root/pages_n/0/list/view/content/item/treasure_be
    ArmyListPnlCtrl.prototype.onClickTreasure = function (event, _) {
        var _a;
        var data = event.target.parent.Data;
        if (!data) {
            // } else if (gameHpr.isBattleingByIndex(data.index)) {
            //     return viewHelper.showAlert(ecode.BATTLEING)
        }
        else if (((_a = data.treasures) === null || _a === void 0 ? void 0 : _a.length) > 0) {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', data.treasures);
        }
    };
    // path://root/pages_n/2/view/content/item/5/buttons/send_to_chat_be
    ArmyListPnlCtrl.prototype.onClickSendToChat = function (event, _data) {
        var _this = this;
        var data = event.target.parent.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid, index = data.index;
        if (uid) {
            // mapHelper.indexToPoint(data.armyIndex).Join()
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_battle_record_to_chat_tip' }, function (type, childType, select) {
                if (GameHelper_1.gameHpr.chat.sendChat(type, childType, '', { select: select, battleInfo: { uid: uid, index: index } }) === 0) {
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type }).then(function () { return _this.isValid && _this.hide(); });
                }
            });
        }
    };
    // path://root/pages_n/2/view/content/item/5/buttons/battle_statistics_be
    ArmyListPnlCtrl.prototype.onClickBattleStatistics = function (event, _) {
        var data = event.target.parent.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid, uids = (data === null || data === void 0 ? void 0 : data.armyUidList) || [];
        if (uid && uids.length > 0) {
            this.showBattleStatistics(uid, uids);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新士兵宝箱
    ArmyListPnlCtrl.prototype.onUpdateArmyTreasure = function (auid) {
        var _a;
        var it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === auid; });
        if (it === null || it === void 0 ? void 0 : it.Data) {
            var army = this.player.getTempArmyList().find(function (m) { return m.uid === auid; });
            var treasures = it.Data.treasures = (_a = army === null || army === void 0 ? void 0 : army.treasures) !== null && _a !== void 0 ? _a : it.Data.treasures;
            this.updateArmyTreasure(it, treasures || []);
        }
    };
    // 刷新军队所在区域位置
    ArmyListPnlCtrl.prototype.onUpdateArmyAreaIndex = function (auid) {
        var _a, _b;
        var it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === auid; });
        if (it === null || it === void 0 ? void 0 : it.Data) {
            var data_1 = it.Data;
            data_1.march = GameHelper_1.gameHpr.world.getMarchs().find(function (m) { return m.armyUid === data_1.uid; });
            data_1.dis = GameHelper_1.gameHpr.getToMapCellDis((_b = (_a = data_1.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data_1.index, this.player.getMainCityIndex());
            this.updateArmyPos(it, data_1);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 显示所有军队
    ArmyListPnlCtrl.prototype.showAllArmy = function (node) {
        var _this = this;
        var sv = node.Child('list', cc.ScrollView);
        sv.content.Swih('');
        var emptyNode = sv.Child('empty'), countLbl = node.Child('title/bg/val', cc.Label);
        var armyMaxCount = this.player.getArmyMaxCount();
        emptyNode.active = false;
        countLbl.setLocaleKey('ui.own_army_count', '0/' + armyMaxCount);
        this.loadingNode_.active = true;
        this.player.getAllArmys(3, false).then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.loadingNode_.active = false;
            var index = _this.player.getMainCityIndex();
            var marchs = {};
            GameHelper_1.gameHpr.world.getMarchs().forEach(function (x) { return marchs[x.armyUid] = x; });
            var lvingPawnLvMap = {};
            _this.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m.lv; });
            list.forEach(function (m) {
                var _a, _b;
                m.march = marchs[m.uid];
                m.tonden = GameHelper_1.gameHpr.world.getArmyTondenInfo(m.index, m.uid);
                m.dis = GameHelper_1.gameHpr.getToMapCellDis((_b = (_a = m.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : m.index, index);
            });
            list.sort(function (a, b) {
                var aw = _this.getArmySortState(a, lvingPawnLvMap), bw = _this.getArmySortState(b, lvingPawnLvMap);
                return aw === bw ? a.dis - b.dis : bw - aw;
            });
            var len = list.length;
            emptyNode.active = len === 0;
            countLbl.setLocaleKey('ui.own_army_count', len + '/' + _this.player.getArmyMaxCount());
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(len, function (it, i) {
                var data = it.Data = list[i];
                it.Child('name', cc.Label).string = data.name;
                _this.updateArmyPos(it, data);
                var pawns = data.pawns, isHasLving = false;
                it.Child('pawns').Items(pawns.concat(data.drillPawns).concat(data.curingPawns), function (node2, pawn) {
                    var _a;
                    var icon = node2.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                    var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                    var lv = isLving ? lvingPawnLvMap[pawn.uid] : pawn.lv;
                    ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.id) || pawn.id), icon, _this.key);
                    icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                    node2.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                    if (node2.Child('hp').active = (!isId && !isCuring)) {
                        node2.Child('hp/bar', cc.Sprite).fillRange = pawn.hp[0] / pawn.hp[1];
                    }
                    if (isLving) {
                        isHasLving = true;
                    }
                });
                ViewHelper_1.viewHelper.updateArmyState(it, data, data.march, isHasLving, true);
                _this.updateArmyTreasure(it, data.treasures);
            });
        });
    };
    ArmyListPnlCtrl.prototype.getArmySortState = function (army, lvingPawnLvMap) {
        if (army.state !== Enums_1.ArmyState.NONE) {
            return army.state;
        }
        else if (army.drillPawns.length > 0) {
            return Enums_1.ArmyState.DRILL;
        }
        else if (army.curingPawns.length > 0) {
            return Enums_1.ArmyState.CURING;
        }
        else if (army.pawns.some(function (m) { return !!lvingPawnLvMap[m.uid]; })) {
            return Enums_1.ArmyState.LVING;
        }
        else if (army.tonden) {
            return Enums_1.ArmyState.TONDEN;
        }
        return army.state;
    };
    ArmyListPnlCtrl.prototype.updateArmyPos = function (node, data) {
        var _a, _b;
        var isMarching = !!data.march, isHasDis = data.dis > 0;
        var posNode = node.Child('pos_be'), disNode = node.Child('dis');
        if (posNode.active = isHasDis || isMarching) {
            ViewHelper_1.viewHelper.updatePositionView(posNode, (_b = (_a = data.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data.index);
        }
        if (disNode.active = !isHasDis && !posNode.active) {
            disNode.setLocaleKey('ui.in_main_city');
        }
    };
    // 刷新宝箱信息
    ArmyListPnlCtrl.prototype.updateArmyTreasure = function (it, treasures) {
        var node = it.Child('treasure_be'), treasureCount = treasures.length;
        if (node.active = treasureCount > 0) {
            node.Child('treasure', TextButtonCmpt_1.default).setKey('ui.get_treasure_count', treasureCount);
        }
    };
    // 显示军队行军记录列表
    ArmyListPnlCtrl.prototype.showArmyMarchRecord = function (node) {
        var _this = this;
        var sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty');
        sv.content.Swih('');
        this.loadingNode_.active = true;
        emptyNode.active = false;
        this.player.getArmyMarchRecords().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            var len = list.length;
            _this.loadingNode_.active = false;
            if (emptyNode.active = len === 0) {
                emptyNode.setLocaleKey(GameHelper_1.gameHpr.isNoviceMode ? 'ui.army_march_record_empty_1' : 'ui.army_march_record_empty');
            }
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.Items(list, function (it, data) {
                it.Data = data;
                it.Child('0/name', cc.Label).string = data.armyName;
                it.Child('0/time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
                _this.updateRecordInfo(it, data);
            });
        });
    };
    // 显示军队战斗记录列表
    ArmyListPnlCtrl.prototype.showArmyBattleRecord = function (node) {
        var _this = this;
        var sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty');
        sv.content.Swih('');
        this.loadingNode_.active = true;
        emptyNode.active = false;
        this.player.getArmyBattleRecords().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            var len = list.length;
            _this.loadingNode_.active = false;
            if (emptyNode.active = len === 0) {
                emptyNode.setLocaleKey('ui.army_battle_record_empty');
            }
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(list.length, function (it, i) {
                var data = list[i];
                it.Child('win/bg').Color(data.isWin ? '#EB9E4E' : '#96B2C8');
                it.Child('win/bg/val').setLocaleKey('ui.battle_result_' + Number(!!data.isWin));
                it.Child('win/time', cc.Label).string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.beginTime);
                _this.updateRecordInfo(it.Child('0'), { type: 0, armyIndex: data.index });
                it.Child('1/val').setLocaleKey('ui.end_battle_time', ut.millisecondFormat(data.endTime - data.beginTime, 'h:mm:ss'));
                it.Child('2/val').setLocaleKey('ui.battle_army_count', data.armyUidList.length);
                it.Child('3/val').setLocaleKey('ui.alli_battle_record_0_1', (data.invalidInfo[1] || 0) + (data.validInfo[1] || 0));
                it.Child('4/val').setLocaleKey('ui.alli_battle_record_1_1', data.deadInfo.length);
                it.Child('5').Data = data;
                it.Child('5/buttons/battle_statistics_be').active = !!data.armyUidList.length;
                it.Child('5/buttons/send_to_chat_be').active = !GameHelper_1.gameHpr.isNoviceMode;
                it.Child('5/playback_be', cc.Button).interactable = !!data.isCanPlay;
            });
        });
    };
    ArmyListPnlCtrl.prototype.updateRecordInfo = function (it, data) {
        var descNode = it.Child('desc'), texts = Constant_1.ARMY_RECORD_DESC_CONF[data.type];
        if (descNode.active = !!texts) {
            descNode.setLocaleKey('ui.army_record_desc_' + data.type, texts.map(function (m) {
                if (m === 'index') {
                    return " <color=#564C49>" + assetsMgr.lang('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(data.armyIndex), MapHelper_1.mapHelper.indexToPoint(data.armyIndex).Join()) + "</>";
                }
                else if (m === 'target') {
                    return " <color=#564C49>" + assetsMgr.lang('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(data.targetIndex), MapHelper_1.mapHelper.indexToPoint(data.targetIndex).Join()) + "</>";
                }
                return '';
            }));
        }
    };
    // 显示战斗统计
    ArmyListPnlCtrl.prototype.showBattleStatistics = function (battleUid, uids) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqGetArmyRecordsByUids({ battleUid: battleUid, uids: uids })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            ViewHelper_1.viewHelper.showPnl('main/BattleStatistics', data.list);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 回放战斗
    ArmyListPnlCtrl.prototype.playbackBattle = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showWindLoading(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordById(uid)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            ViewHelper_1.viewHelper.showWindLoading(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                    case 2:
                        _a.sent();
                        ViewHelper_1.viewHelper.showWindLoading(false);
                        ViewHelper_1.viewHelper.gotoWind('playback');
                        return [2 /*return*/];
                }
            });
        });
    };
    ArmyListPnlCtrl = __decorate([
        ccclass
    ], ArmyListPnlCtrl);
    return ArmyListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ArmyListPnlCtrl;

cc._RF.pop();