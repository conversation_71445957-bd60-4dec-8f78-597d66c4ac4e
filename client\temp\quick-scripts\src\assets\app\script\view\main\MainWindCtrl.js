"use strict";
cc._RF.push(module, '1e364aKCJxAnqBfRB6XE0Ca', 'MainWindCtrl');
// app/script/view/main/MainWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var PopupPnlHelper_1 = require("../../common/helper/PopupPnlHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BuildObj_1 = require("../../model/area/BuildObj");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var SelectCellCmpt_1 = require("../cmpt/SelectCellCmpt");
var CellInfoCmpt_1 = require("./CellInfoCmpt");
var MapAnimNodePool_1 = require("./MapAnimNodePool");
var MarchCmpt_1 = require("./MarchCmpt");
var SceneEffectCmpt_1 = require("./SceneEffectCmpt");
var ccclass = cc._decorator.ccclass;
var MainWindCtrl = /** @class */ (function (_super) {
    __extends(MainWindCtrl, _super);
    function MainWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.cellEffectNode_ = null; // path://root/map_n/cell_effect_n
        _this.selectCellNode_ = null; // path://root/select_cell_n
        _this.textNode_ = null; // path://root/text_n
        _this.ancientTextNode_ = null; // path://root/ancient_text_n
        _this.marchLineNode_ = null; // path://root/march/march_line_n
        _this.marchRoleNode_ = null; // path://root/march/march_role_n
        _this.cellEmojiNode_ = null; // path://root/cell_emoji_n
        _this.sceneEffectNode_ = null; // path://root/scene_effect_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        _this.weakGuideNode_ = null; // path://root/weak_guide_n
        //@end
        _this.INIT_KEY = '_init_main_';
        _this.diNode = null; //装饰
        _this.mountainNode = null; //山脉
        _this.protectLineNode = null; //保护线
        _this.lineNode = null;
        _this.seawaveNode = null; //海浪
        _this.landNode = null;
        _this.cityNode = null; //城市层
        _this.maskNode = null; //遮罩层
        _this.btinfoNode = null; //修建信息层
        _this.outputNode = null; //产出层
        _this.iconNode = null; //小图标层
        _this.tondenNode = null; //屯田中图标层
        _this.battleNode = null; //战斗中图标层
        _this.mapFlagNode = null; //地图标记
        _this.cellInfoCmpt = null;
        _this.touchCmpt = null;
        _this.sceneEffect = null;
        _this.cellEmojiItemMap = {};
        _this.seasonType = 0;
        _this.model = null;
        _this.user = null;
        _this.player = null;
        _this.centre = cc.v2(); //当前的中心位置
        _this.preCameraZoomRatio = 0;
        _this.preCameraPosition = cc.v2();
        _this.marchs = []; //当前所有行军
        _this.tempShowCellMap = {}; //当前屏幕显示的地块信息
        _this.reqSelectArmysing = false; //当前是否请求军队列表中
        _this.cellEmojiMap = {}; //当前的领地表情map
        _this.cityAnimNodePool = null; //城市节点管理
        _this.seawaveAnimNodePool = null; //海浪节点管理
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_vec2_4 = cc.v2();
        _this._temp_vec2_5 = cc.v2();
        return _this;
    }
    MainWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_CELL_INFO] = this.onUpdateCellInfo, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.ADD_MARCH] = this.onAddMarch, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.REMOVE_MARCH] = this.onRemoveMarch, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_ALL_MARCH] = this.onUpdateAllMarch, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.HIDE_WORLD_TEXT] = this.onHideWorldText, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.CLOSE_SELECT_CELL] = this.onCloseSelectCell, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_BATTLE_DIST_INFO] = this.onUpdateBattleDistInfo, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO] = this.onUpdateAvoidWarDistInfo, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_BT_CITY] = this.onUpdateBtCity, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_TONDEN] = this.onUpdateTonden, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmyDistInfo, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.MAP_MOVE_TO] = this.onMapMoveTo, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_PLAYER_NICKNAME] = this.onUpdatePlayerNickname, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.UPDATE_PLAYER_HEAD_ICON] = this.onUpdatePlayerHeadIcon, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.UPDATE_ALLI_MAP_FLAG] = this.onUpdateAlliMapFlag, _r.enter = true, _r),
            (_s = {}, _s[EventType_1.default.UPDATE_MARCH_OPACITY] = this.onUpdateMarchOpacity, _s.enter = true, _s),
            (_t = {}, _t[EventType_1.default.PLAY_NEW_CELL_EFFECT] = this.onPlayNewCellEffect, _t.enter = true, _t),
            (_u = {}, _u[EventType_1.default.PLAY_CELL_TONDEN_EFFECT] = this.onPlayCellTondenEffect, _u.enter = true, _u),
            (_v = {}, _v[EventType_1.default.PLAY_CELL_EMOJI] = this.onPlayCellEmoji, _v.enter = true, _v),
            (_w = {}, _w[EventType_1.default.UPDATE_CITY_OUTPUT] = this.onUpdateCityOutput, _w.enter = true, _w),
            (_x = {}, _x[EventType_1.default.CHANGE_SEASON_COMPLETE] = this.onChangeSeasonComplete, _x.enter = true, _x),
            (_y = {}, _y[EventType_1.default.UPDATE_ANCIENT_INFO] = this.onUpdateAncientInfo, _y.enter = true, _y),
            (_z = {}, _z[EventType_1.default.UPDATE_CITY_SKIN] = this.onUpdateCitySkin, _z.enter = true, _z),
            (_0 = {}, _0[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _0.enter = true, _0),
        ];
    };
    MainWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var i, item;
            return __generator(this, function (_a) {
                this.setParam({ isClean: false });
                this.diNode = this.mapNode_.FindChild('di');
                this.mountainNode = this.mapNode_.FindChild('mountain');
                this.protectLineNode = this.mapNode_.FindChild('protect_line');
                this.lineNode = this.mapNode_.FindChild('line');
                this.seawaveNode = this.mapNode_.FindChild('seawave');
                this.landNode = this.mapNode_.FindChild('land');
                this.cityNode = this.mapNode_.FindChild('city');
                this.maskNode = this.mapNode_.FindChild('mask');
                this.btinfoNode = this.mapNode_.FindChild('btinfo');
                this.outputNode = this.mapNode_.FindChild('output');
                this.iconNode = this.mapNode_.FindChild('icon');
                this.tondenNode = this.mapNode_.FindChild('tonden');
                this.battleNode = this.mapNode_.FindChild('battle');
                this.mapFlagNode = this.mapNode_.FindChild('map_flag');
                for (i = 2; i <= 3; i++) {
                    item = this.cellEmojiItemMap[i] = this.cellEmojiNode_.FindChild('item_' + i);
                    item.parent = null;
                }
                this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                this.cellInfoCmpt = this.FindChild('root/cell_info', CellInfoCmpt_1.default);
                this.cityAnimNodePool = new MapAnimNodePool_1.default().init(this.cityNode, ResHelper_1.resHelper.getCityPrefab.bind(ResHelper_1.resHelper));
                this.model = this.getModel('world');
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                // this.seawaveAnimNodePool = new MapAnimNodePool().init(this.seawaveNode, resHelper.getSeawavePrefab.bind(resHelper))
                // this.seawaveAnimNodePool.setAnimInfo('land_104', 1.76)
                this.selectCellNode_.active = false;
                this.cellInfoCmpt.close();
                this.sceneEffect = this.sceneEffectNode_.getComponent(SceneEffectCmpt_1.default);
                this.updateSeasonSeceneEffect();
                this.maskNode.children[0].color = cc.Color.WHITE.fromHEX(Constant_1.MAP_MASK_ITEM_COLOR[this.model.getSeasonType()]);
                return [2 /*return*/];
            });
        });
    };
    MainWindCtrl.prototype.onReady = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MainWindCtrl.prototype.onEnter = function (data) {
        this.model.initCameraInfo();
        this.cellEffectNode_.Data = true;
        this.cellEmojiNode_.Data = true;
        this.topLayerNode_.Data = true;
        this.checkSeason();
        this.initMarch(); //初始化行军
        this.playNewCellEffect();
        this.playCellTondenEffect();
        this.playCellEmoji();
        this.touchCmpt.init(this.onClickMap.bind(this));
        GameHelper_1.gameHpr.playMainBgm();
        CameraCtrl_1.cameraCtrl.setBgColor(Constant_1.CAMERA_BG_COLOR[this.model.getSeasonType()]);
    };
    MainWindCtrl.prototype.onLeave = function () {
        this.model.saveCameraInfo();
        this.touchCmpt.clean();
        this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
        this.cellInfoCmpt.close();
        this.reqSelectArmysing = false;
        this.cleanMarch();
        this.cellEffectNode_.removeAllChildren();
        this.cellEffectNode_.Data = false;
        this.cellEmojiNode_.removeAllChildren();
        this.cellEmojiNode_.Data = false;
        this.topLayerNode_.removeAllChildren();
        this.topLayerNode_.Data = false;
        // resHelper.cleanNodeChildren(this.diNode) 这里暂时不清理 因为进入其他场景太慢了
        this.cellEmojiMap = {};
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        assetsMgr.releaseTempResByTag(this.key);
        CameraCtrl_1.cameraCtrl.setBgColor('#D1F1F3');
    };
    MainWindCtrl.prototype.onClean = function () {
        var _a;
        for (var k in this.cellEmojiItemMap) {
            this.cellEmojiItemMap[k].destroy();
        }
        this.cellEmojiItemMap = {};
        this.cellEmojiMap = {};
        this.cellInfoCmpt.clean();
        (_a = this.sceneEffect) === null || _a === void 0 ? void 0 : _a.clean();
        assetsMgr.releaseTempResByTag(this.INIT_KEY);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/cell_info/buttons/enter_be
    MainWindCtrl.prototype.onClickEnter = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            this.model.setLookCell(cell);
            ViewHelper_1.viewHelper.gotoWind('area');
            this.hideSelectCell(false);
        }
    };
    // path://root/cell_info/buttons/occupy_be
    MainWindCtrl.prototype.onClickOccupy = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell) {
            return;
        }
        else if (!this.model.checkCanOccupyCell(cell)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ONLY_ATTACK_ADJOIN_CELL);
        }
        else if (cell.checkAttackByProtect()) { //是否有保护
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.CELL_PROTECT);
        }
        else if (cell.isAvoidWar()) { //是否金盾
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.AVOID_WAR_NOT_ATTACK);
        }
        this.occupyCell(cell.actIndex);
    };
    // path://root/cell_info/buttons/tonden_be
    MainWindCtrl.prototype.onClickTonden = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        else if (GameHelper_1.gameHpr.isBattleingByIndex(cell.index)) {
            return ViewHelper_1.viewHelper.showAlert('toast.battleing_not_tonden');
        }
        else if (cell.isBTCitying()) {
            return ViewHelper_1.viewHelper.showAlert('toast.bting_not_tonden');
        }
        this.cellTonden(cell.actIndex);
    };
    // path://root/cell_info/buttons/cancel_tonden_be
    MainWindCtrl.prototype.onClickCancelTonden = function (event, data) {
        var _this = this;
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn() || !cell.isTondening()) {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.cancel_tonden_tip', {
            ok: function () { return _this.isActive && _this.cancelTonden(cell.actIndex); },
            cancel: function () { },
        });
    };
    // path://root/cell_info/buttons/move_be
    MainWindCtrl.prototype.onClickMove = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOneAlliance()) {
            return;
        }
        this.moveToCell(cell.actIndex);
    };
    // path://root/cell_info/buttons/build_be
    MainWindCtrl.prototype.onClickBuild = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        else if (cell.isTondening()) {
            return ViewHelper_1.viewHelper.showAlert('toast.tondening_not_bt');
        }
        ViewHelper_1.viewHelper.showPnl('main/CityList', cell);
    };
    // path://root/cell_info/buttons/dismantle_be
    MainWindCtrl.prototype.onClickDismantle = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('main/DismantleCityTip', cell);
    };
    // path://root/cell_info/buttons/player_info_be
    MainWindCtrl.prototype.onClickPlayerInfo = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        var info = this.model.getPlayerInfo(cell === null || cell === void 0 ? void 0 : cell.owner);
        if (info) {
            ViewHelper_1.viewHelper.showPnl('common/PlayerInfo', info, 'cellinfo');
        }
    };
    // path://root/cell_info/title/share_pos_be
    MainWindCtrl.prototype.onClickSharePos = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            var point_1 = cell.actPoint.Join(',');
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_point_to_chat_tip', params: [point_1] }, function (type, select) {
                if (_this.isValid) {
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type, text: "[" + point_1 + "]" }).then(function () { return _this.isValid && _this.hideSelectCell(false); });
                }
            });
        }
    };
    // path://root/cell_info/buttons/flag_be
    MainWindCtrl.prototype.onClickFlag = function (event, data) {
        if (!GameHelper_1.gameHpr.alliance.isMeMilitary()) {
            return;
        }
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/SelectFlagIcon', cell.actIndex);
        }
    };
    // path://root/cell_info/info/score/score_desc_be
    MainWindCtrl.prototype.onClickScoreDesc = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/LandScoreDesc', cell.landLv);
        }
    };
    // path://root/cell_info/info/stamina/stamina_desc_be
    MainWindCtrl.prototype.onClickStaminaDesc = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType);
        }
    };
    // path://root/cell_info/title/cell_emoji_be
    MainWindCtrl.prototype.onClickCellEmoji = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/CellSelectEmoji', cell.isOwn(), function (id) {
                if (id) {
                    GameHelper_1.gameHpr.ground.sendCellEmoji(id, cell.actIndex);
                }
                if (_this.isValid) {
                    _this.hideSelectCell(false);
                }
            });
        }
    };
    // path://root/map_n/output/item/city_output_be
    MainWindCtrl.prototype.onClickCityOutput = function (event, data) {
        var _this = this;
        var _a;
        var cell = event.target.parent.Data;
        if (!cell || !cell.isOwn()) {
            return;
        }
        var rewards = ((_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.cityOutputMap[cell.index]) || [];
        this.model.claimCityOutput(cell.index).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            // gameHpr.addGainMassage(rewards)
            rewards.forEach(function (m, i) { return AnimHelper_1.animHelper.playFlutterCellOutput(i * 0.4, m, _this.topLayerNode_, cell.actPosition, _this.key); });
        });
    };
    // path://root/cell_info/buttons/ancient_info_be
    MainWindCtrl.prototype.onClickAncientInfo = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            var ancient = this.model.getAncientInfo(cell.index);
            if (ancient) {
                var build = new BuildObj_1.default().init(cell.index, ut.UID(), cc.v2(0, 0), ancient.id, ancient.lv);
                ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', build);
            }
        }
    };
    // path://root/cell_info/buttons/city_skin_be
    MainWindCtrl.prototype.onClickCitySkin = function (event, data) {
        var _this = this;
        var cell = this.cellInfoCmpt.getCell();
        if (cell && cell.cityId > 0) {
            ViewHelper_1.viewHelper.showPnl('main/SelectCitySkin', cell, function (ok) {
                if (_this.isValid && ok) {
                    _this.hideSelectCell(false);
                }
            });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    MainWindCtrl.prototype.onNetReconnect = function () {
        if (!GameHelper_1.gameHpr.guide.isOneGuideWorking()) { //这里如果在新手引导 就不要关闭了
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close();
            this.model.initCameraInfo();
        }
        this.updateMap(this.centre); //刷新地图显示
        this.initMarch(); //初始化行军
    };
    // 更新地块信息
    MainWindCtrl.prototype.onUpdateCellInfo = function () {
        this.updateMap(this.centre);
    };
    // 添加行军
    MainWindCtrl.prototype.onAddMarch = function (data) {
        var _this = this;
        if (!data.isCanShowMarch()) {
            return this.onRemoveMarch(data);
        }
        var march = this.marchs.find(function (m) { return m.uid === data.uid; });
        if (march) {
            march.init(data, this.marchRoleNode_, this.key);
            this.checkMarchLineOffset(data);
        }
        else {
            this.marchLineNode_.AddItem(function (it) {
                march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
                _this.checkMarchLineOffset(data);
            });
        }
    };
    // 删除行军
    MainWindCtrl.prototype.onRemoveMarch = function (data) {
        var march = this.marchs.remove('uid', data.uid);
        if (march) {
            march.clean();
            this.checkMarchLineOffset(data);
        }
    };
    // 刷新所有行军
    MainWindCtrl.prototype.onUpdateAllMarch = function () {
        this.initMarch();
    };
    // 隐藏文本
    MainWindCtrl.prototype.onHideWorldText = function (val) {
        val = !val;
        this.textNode_.active = val;
        this.ancientTextNode_.active = val;
        this.btinfoNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = val); });
        this.tondenNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = val); });
    };
    // 关闭选择地块
    MainWindCtrl.prototype.onCloseSelectCell = function (play) {
        this.hideSelectCell(!!play);
    };
    // 刷新战斗状态
    MainWindCtrl.prototype.onUpdateBattleDistInfo = function () {
        var cells = [], distMap = this.model.getBattleDistMap();
        for (var index in distMap) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push(cell);
        }
        this.battleNode.Items(cells, function (it, data) { return it.setPosition(data.actPosition); });
    };
    // 刷新免战状态
    MainWindCtrl.prototype.onUpdateAvoidWarDistInfo = function () {
        this.updateIconNode();
    };
    // 刷新修建信息
    MainWindCtrl.prototype.onUpdateBtCity = function (index) {
        var _a;
        this.updateMap(this.centre);
        if (((_a = this.cellInfoCmpt.getCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === index) {
            this.cellInfoCmpt.updateInfo();
        }
    };
    // 刷新屯田信息
    MainWindCtrl.prototype.onUpdateTonden = function (index) {
        var _a;
        this.updateMap(this.centre);
        if (((_a = this.cellInfoCmpt.getCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === index) {
            this.cellInfoCmpt.updateInfo();
        }
    };
    // 刷新地图上面的军队分布情况  这里主动绘制一次
    MainWindCtrl.prototype.onUpdateArmyDistInfo = function () {
        this.updateIconNode();
        this.cellInfoCmpt.updateArmyInfo();
    };
    // 移动地图
    MainWindCtrl.prototype.onMapMoveTo = function (point, showCellInfo) {
        var _this = this;
        if (this.centre.equals(point)) {
            return showCellInfo && this.showSelectCell(this.model.getMapCellByPoint(point.clone().floor()));
        }
        else if (!this.tempShowCellMap[MapHelper_1.mapHelper.pointToIndex(point)]) { //如果没有在当前绘制区域就移动到目标点
            var start = this.centre.sub(point, this._temp_vec2_4).normalizeSelf().mulSelf(2).addSelf(point);
            CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(start), MapHelper_1.mapHelper.MAP_SIZE, Constant_1.MAP_SHOW_OFFSET, CameraCtrl_1.cameraCtrl.zoomRatio);
            this.updateMap(start.floor());
            this.checkInCameraMarchLine();
        }
        // 移动
        CameraCtrl_1.cameraCtrl.moveTo(0.25, MapHelper_1.mapHelper.getPixelByPoint(point).subSelf(CameraCtrl_1.cameraCtrl.getWinSizeHalf())).then(function () {
            if (_this.isActive && showCellInfo) {
                _this.showSelectCell(_this.model.getMapCellByPoint(point.clone().floor()));
            }
        });
    };
    // 刷新玩家昵称
    MainWindCtrl.prototype.onUpdatePlayerNickname = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            this.updatePlayerNickname(it, data);
        }
    };
    // 刷新玩家头像
    MainWindCtrl.prototype.onUpdatePlayerHeadIcon = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, this.INIT_KEY);
        }
    };
    // 刷新联盟地图标记
    MainWindCtrl.prototype.onUpdateAlliMapFlag = function () {
        var cells = [], mapFalg = GameHelper_1.gameHpr.alliance.getMapFlag();
        for (var index in mapFalg) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push({ cell: cell, flag: mapFalg[index] });
        }
        this.mapFlagNode.Items(cells, function (it, data) {
            it.setPosition(data.cell.actPosition);
            it.Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(data.flag);
        });
    };
    // 刷新行军线透明度
    MainWindCtrl.prototype.onUpdateMarchOpacity = function () {
        this.marchs.forEach(function (m) { return m.updateOpacity(); });
    };
    // 播放新的地块效果
    MainWindCtrl.prototype.onPlayNewCellEffect = function () {
        this.playNewCellEffect();
    };
    // 播放屯田结束效果
    MainWindCtrl.prototype.onPlayCellTondenEffect = function () {
        this.playCellTondenEffect();
    };
    // 播放地图表情
    MainWindCtrl.prototype.onPlayCellEmoji = function () {
        this.playCellEmoji();
    };
    // 刷新城市产出
    MainWindCtrl.prototype.onUpdateCityOutput = function () {
        var cells = [];
        for (var index in this.tempShowCellMap) {
            var cell = this.tempShowCellMap[index];
            var output = cell.getOutputType();
            if (output) {
                cells.push({ cell: cell, output: output });
            }
        }
        this.outputNode.Items(cells, function (it, data) {
            it.setPosition(data.cell.actPosition);
            it.Child('city_output_be').active = data.cell.isOwn();
            it.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[data.output.type]);
            it.Child('root/val', cc.Label).string = data.output.count > 1 ? data.output.count + '' : '';
        });
    };
    // 改变季节完成
    MainWindCtrl.prototype.onChangeSeasonComplete = function () {
        this.playChangeSeason(this.model.getSeason().type);
    };
    // 刷新遗迹
    MainWindCtrl.prototype.onUpdateAncientInfo = function (data) {
        var index = data.index;
        var it = this.ancientTextNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.index) === index; });
        if (it) {
            this.updateAncientTextInfo(it, data);
        }
    };
    // 刷新城市皮肤
    MainWindCtrl.prototype.onUpdateCitySkin = function (index) {
        this.updateMap(this.centre);
    };
    // 若引导
    MainWindCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'main') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key);
        }
    };
    Object.defineProperty(MainWindCtrl.prototype, "isActive", {
        // ----------------------------------------- custom function ----------------------------------------------------
        get: function () { return this.isValid && this.isEnter(); },
        enumerable: false,
        configurable: true
    });
    // 点击地图
    MainWindCtrl.prototype.onClickMap = function (worldLocation) {
        var cell = this.model.getMapCellByPoint(MapHelper_1.mapHelper.getPointByPixel(worldLocation));
        if (cell && !this.selectCellNode_.Data) {
            audioMgr.playSFX('click');
            this.showSelectCell(cell);
            CameraCtrl_1.cameraCtrl.redressPositionByRange(cell.actPosition, Constant_1.SELECT_CELL_INFO_BOX);
        }
        else {
            this.hideSelectCell();
        }
    };
    // 刷新场景特效
    MainWindCtrl.prototype.updateSeasonSeceneEffect = function () {
        this.sceneEffect.clean();
        var sceneEffectUrl = this.model.getSeason().getCurrSceneEffectUrl();
        if (sceneEffectUrl) { //加载场景特效
            this.sceneEffect.init(sceneEffectUrl, this.INIT_KEY);
        }
    };
    // 绘制地图
    MainWindCtrl.prototype.updateMap = function (centre) {
        var _this = this;
        var _a, _b, _c, _d;
        this.model.initDecorationUseLand();
        this.preCameraZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.centre.set(centre);
        this.model.setCentre(centre);
        // 绘制地面
        var armyDistMap = this.player.getArmyDistMap(), battleDist = this.model.getBattleDistMap();
        var mapFlag = GameHelper_1.gameHpr.alliance.getMapFlag();
        var btCityMap = this.model.getBTCityQueueMap();
        var tondenMap = this.model.getTondenQueueMap();
        var di = 0, linei = 0, li = 0, mi = 0, ii = 0, ti = 0, bi = 0, mfi = 0, oi = 0, mti = 0, pli = 0;
        var texts = [], tondens = [], btCitys = [], ancientTexts = [];
        // this.seawaveAnimNodePool?.reset()
        this.cityAnimNodePool.reset();
        this.tempShowCellMap = {};
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, this.model.getMaxTileRange());
        var seasonType = this.seasonType;
        var tempDecorationLoadMap = {};
        for (var i = 0; i < points.length; i++) {
            var point = points[i], cell = this.model.getMapCellByPoint(point);
            var position = (cell === null || cell === void 0 ? void 0 : cell.position) || MapHelper_1.mapHelper.getPixelByPoint(point);
            if (cell) {
                var btInfo = btCityMap[cell.index], tondenInfo = tondenMap[cell.index];
                this.tempShowCellMap[cell.index] = cell;
                if (cell.cityId > 0) {
                    var animName = cell.cityId === Constant_1.CITY_FORT_NID ? 'city_2102_' + cell.getOwnType() : undefined;
                    var city = this.cityAnimNodePool.showNode(cell.getCityViewId(), cell.actPosition, true, animName);
                    if (cell.cityId === Constant_1.CITY_MAIN_NID) {
                        // 这里先获取后面用来显示文本
                        texts.push(cell);
                        // 是否有保护模式 绘制保护线
                        var state = GameHelper_1.gameHpr.checkPlayerProtectModeState(cell.owner);
                        if (state > 0) {
                            ResHelper_1.resHelper.getNodeByIndex(this.protectLineNode, pli++, cell.actPosition).opacity = state === 1 ? 255 : 100;
                        }
                    }
                    else if (cell.isAncient()) {
                        var info = this.model.getAncientInfo(cell.index);
                        if (info) {
                            ancientTexts.push(info); //遗迹
                            city.Child('val', cc.MultiFrame).setFrame(info.lv === 20);
                        }
                    }
                }
                else if (cell.cityId < 0) {
                }
                else if (tondenInfo) { //绘制屯田地
                    tondens.push({ tondenInfo: tondenInfo, cell: cell });
                    ResHelper_1.resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon('land_tonden', seasonType);
                }
                else if (cell.icon && (!btInfo || btInfo.id === 0)) {
                    ResHelper_1.resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(cell.icon, seasonType);
                }
                // 绘制修建信息
                if (btInfo) {
                    btCitys.push({ btInfo: btInfo, cell: cell });
                    // 只绘制修建 不绘制拆除
                    if (cell.cityId === 0 && btInfo.id > 0) {
                        this.cityAnimNodePool.showNode(btInfo.id, cell.actPosition, false);
                    }
                }
                // 绘制地图军队分布图标
                if (!!armyDistMap[cell.index]) {
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_a = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _a === void 0 ? void 0 : _a.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(-22, y).addSelf(cell.position); //显示到左下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('army_min_icon');
                }
                // 绘制免战图标
                if (cell.isCanShowAvoidWar()) {
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_b = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _b === void 0 ? void 0 : _b.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(22, y).addSelf(cell.getRightPosition(this._temp_vec2_5)); //显示到右下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('avoidwar_icon_1');
                }
                // 绘制战斗图标
                if (!!battleDist[cell.index]) {
                    ResHelper_1.resHelper.getNodeByIndex(this.battleNode, bi++, cell.actPosition);
                }
                var flag = mapFlag[cell.index];
                if (flag) { //地图标记
                    ResHelper_1.resHelper.getNodeByIndex(this.mapFlagNode, mfi++, cell.actPosition).Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(flag);
                }
                // 绘制产出
                var output = cell.getOutputType();
                if (output) {
                    var oNode = ResHelper_1.resHelper.getNodeByIndex(this.outputNode, oi++, cell.actPosition);
                    oNode.Data = cell;
                    oNode.Child('city_output_be').active = cell.isOwn();
                    oNode.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[output.type]);
                    oNode.Child('root/val', cc.Label).string = output.count > 1 ? output.count + '' : '';
                }
                // 记录边框线
                var borderLines = cell.owner ? cell.borderLines : [];
                if (borderLines.length > 0) {
                    var lineItemNode = ResHelper_1.resHelper.getNodeByIndex(this.lineNode, linei++, position);
                    ViewHelper_1.viewHelper.updateCellBorderLines(lineItemNode, borderLines, cell.getBorderLineColor());
                }
                // 绘制遮罩
                if (!cell.owner) {
                    var maskItemNode = ResHelper_1.resHelper.getNodeByIndex(this.maskNode, mi++, position);
                    maskItemNode.opacity = cell.getProtectOwner() ? 20 : 38;
                }
                //绘制地图装饰
                var decorationCell = cell;
                if (!decorationCell.decorationJson && this.model.getDecorationIndex(cell.index)) {
                    decorationCell = this.model.getMapCells()[this.model.getDecorationIndex(cell.index)];
                }
                if (decorationCell.decorationJson && !tempDecorationLoadMap[decorationCell.index]) {
                    tempDecorationLoadMap[decorationCell.index] = true;
                    var itemNode = null;
                    var iconName = this.model.getDecorationIcon(decorationCell);
                    var frame = ResHelper_1.resHelper.getLandItemIcon(iconName, seasonType);
                    switch (decorationCell.decorationJson.type) {
                        case Enums_1.DecorationType.MOUNTAIN:
                            itemNode = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, decorationCell.position);
                            if (!decorationCell.mountainAnchor) {
                                //偶数倍数时锚点偏移
                                var size = frame.getOriginalSize();
                                var anchorX = 0.5, anchorY = 0.5;
                                if (size.width / Constant_1.TILE_SIZE % 2 == 0) {
                                    anchorX = (size.width / 2 - Constant_1.TILE_SIZE / 2) / size.width;
                                }
                                if (size.height / Constant_1.TILE_SIZE % 2 == 0) {
                                    anchorY = (size.height / 2 - Constant_1.TILE_SIZE / 2) / size.height;
                                }
                                decorationCell.mountainAnchor = cc.v2(anchorX, anchorY);
                            }
                            itemNode.setAnchorPoint(decorationCell.mountainAnchor);
                            break;
                        default:
                            itemNode = ResHelper_1.resHelper.getNodeByIndex(this.diNode, di++, decorationCell.position);
                            break;
                    }
                    itemNode.Component(cc.Sprite).spriteFrame = frame;
                }
            }
            else {
                var landId = this.model.getRoundId(point.x, point.y);
                if (landId) {
                    var itemInfo = assetsMgr.getJsonData('land', landId);
                    ResHelper_1.resHelper.getNodeByIndex(this.diNode, di++, position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(itemInfo.icon, seasonType);
                    ResHelper_1.resHelper.getNodeByIndex(this.maskNode, mi++, position);
                }
            }
        }
        // 隐藏多余的
        ResHelper_1.resHelper.hideNodeByIndex(this.diNode, di);
        ResHelper_1.resHelper.hideNodeByIndex(this.protectLineNode, pli);
        ResHelper_1.resHelper.hideNodeByIndex(this.lineNode, linei);
        ResHelper_1.resHelper.hideNodeByIndex(this.landNode, li);
        ResHelper_1.resHelper.hideNodeByIndex(this.maskNode, mi);
        ResHelper_1.resHelper.hideNodeByIndex(this.iconNode, ii);
        ResHelper_1.resHelper.hideNodeByIndex(this.battleNode, bi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mapFlagNode, mfi);
        ResHelper_1.resHelper.hideNodeByIndex(this.outputNode, oi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mountainNode, mti);
        // this.seawaveAnimNodePool?.hideOtherNode()
        this.cityAnimNodePool.hideOtherNode();
        // 当前正在显示的
        var showIndex = (_d = (_c = this.cellInfoCmpt.getCell()) === null || _c === void 0 ? void 0 : _c.actIndex) !== null && _d !== void 0 ? _d : -1;
        var zIndexMaxY = MapHelper_1.mapHelper.MAP_SIZE.y;
        var isCanShowText = !this.touchCmpt.isDraging();
        // 绘制文本层
        this.textNode_.Items(texts, function (it, data) {
            var pos = data.actPosition, index = data.actIndex;
            var d = it.Data;
            var info = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
            it.setPosition(pos.x, pos.y + 76);
            if (!d || d.owner !== data.owner || d.nickname !== (info === null || info === void 0 ? void 0 : info.nickname) || d.title !== (info === null || info === void 0 ? void 0 : info.title) || d.headIcon !== (info === null || info === void 0 ? void 0 : info.headIcon)) {
                ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), info === null || info === void 0 ? void 0 : info.headIcon, _this.INIT_KEY);
                _this.updatePlayerNickname(it, info);
            }
            it.active = showIndex !== index;
            it.Data = { index: index, owner: data.owner, nickname: info === null || info === void 0 ? void 0 : info.nickname, title: info === null || info === void 0 ? void 0 : info.title, headIcon: info === null || info === void 0 ? void 0 : info.headIcon };
        });
        this.textNode_.active = isCanShowText;
        // 绘制遗迹文本层
        this.ancientTextNode_.Items(ancientTexts, function (it, data) {
            var pos = data.cell.actPosition, index = data.index;
            it.setPosition(pos.x, pos.y + 72);
            _this.updateAncientTextInfo(it, data);
            it.active = showIndex !== index;
            it.Data = { index: index };
        });
        this.ancientTextNode_.active = isCanShowText;
        // 绘制修建城市的信息
        this.btinfoNode.Items(btCitys, function (it, data) {
            var _a;
            var info = data.btInfo, index = info.index;
            it.setPosition(data.cell.actPosition);
            var timeNode = it.Child('time');
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.index) !== index) {
                var surplusTime = info.getSurplusTime();
                timeNode.Color(info.id ? '#21DC2D' : '#FF9162');
                timeNode.Component(cc.LabelTimer).run(surplusTime * 0.001);
                // 动画
                var anim_1 = it.Child('anim', cc.Animation);
                var elapsedTime = Math.max(0, info.needTime - surplusTime) * 0.001;
                var tween = cc.tween(it);
                tween.stop();
                if (elapsedTime < 0.62) {
                    anim_1.play('cting_begin', elapsedTime);
                    tween.delay(0.62 - elapsedTime).call(function () { return _this.isValid && anim_1.play('cting_loop'); }).start();
                }
                else {
                    anim_1.play('cting_loop');
                }
            }
            timeNode.active = isCanShowText && showIndex !== index;
            it.Data = { index: index };
            it.zIndex = zIndexMaxY - data.cell.actPoint.y;
        });
        // 绘制屯田信息
        this.tondenNode.Items(tondens, function (it, data) {
            var _a;
            var info = data.tondenInfo, index = info.index;
            it.setPosition(data.cell.actPosition);
            var timeNode = it.Child('time');
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.index) !== index) {
                timeNode.Component(cc.LabelTimer).run(info.getSurplusTime() * 0.001);
            }
            timeNode.active = isCanShowText;
            it.Data = { index: index };
            it.zIndex = zIndexMaxY - data.cell.actPoint.y;
        });
    };
    // 0.上 1.右 2.下 3.左 4.左上 5.右上 6.右下 7.左下
    MainWindCtrl.prototype.getSeaLandIcon = function (point, minx, miny, maxx, maxy) {
        if (point.x < minx) {
            return point.y < miny ? 7 : (point.y < maxy ? 3 : 4);
        }
        else if (point.x < maxx) {
            return point.y < miny ? 2 : 0;
        }
        return point.y < miny ? 6 : (point.y < maxy ? 1 : 5);
    };
    MainWindCtrl.prototype.setSeaLand = function (it, type, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy);
        it.Component(cc.Sprite).spriteFrame = this.getLandIcon(type + "_" + Math.min(dir, 4));
    };
    // 海浪
    MainWindCtrl.prototype.setSeawaveLand = function (position, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy), no = Math.floor(dir / 4) + 1, angle = dir % 4;
        var it = this.seawaveAnimNodePool.showNode(no, position, true);
        it.angle = angle * -90;
    };
    MainWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandIcon(icon);
    };
    MainWindCtrl.prototype.updatePlayerNickname = function (it, data) {
        var _a;
        var nameLbl = it.Child('name/val', cc.Label);
        nameLbl.string = ut.nameFormator((_a = data === null || data === void 0 ? void 0 : data.nickname) !== null && _a !== void 0 ? _a : '???', 7);
        var titleLbl = it.Child('name/title', cc.Label);
        if (titleLbl.setActive(!!(data === null || data === void 0 ? void 0 : data.title))) {
            var json = assetsMgr.getJsonData('title', data.title);
            titleLbl.Color((json === null || json === void 0 ? void 0 : json.color) || '#333333').setLocaleKey('titleText.' + (json === null || json === void 0 ? void 0 : json.id));
            nameLbl.node.y = -10;
        }
        else {
            nameLbl.node.y = 0;
        }
    };
    MainWindCtrl.prototype.updateAncientTextInfo = function (it, data) {
        it.Child('name').setLocaleKey('ui.ancient_name_text', data.name, assetsMgr.lang('ui.short_lv', data.lv || 1));
        if (it.Child('time').active = data.state === 1 && !data.pauseState) {
            it.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
        }
    };
    // 刷新显示文本节点
    MainWindCtrl.prototype.updateHideTextByIndex = function (index) {
        if (index === void 0) { index = -1; }
        this.textNode_.children.forEach(function (m) { return m.active = !!m.Data && m.Data.index !== index; });
        this.ancientTextNode_.children.forEach(function (m) { return m.active = !!m.Data && m.Data.index !== index; });
        this.btinfoNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = m.Data.index !== index); });
    };
    // 刷新图标层
    MainWindCtrl.prototype.updateIconNode = function () {
        var _this = this;
        var offset1 = cc.v2(-22, -22);
        var offset2 = cc.v2(22, -22);
        var cells = [], armyDistMap = this.player.getArmyDistMap();
        for (var key in this.tempShowCellMap) {
            var cell = this.tempShowCellMap[key];
            if (cell.isCanShowAvoidWar()) {
                cells.push({ position: cell.getRightPosition(), offset: offset2, icon: 'avoidwar_icon_1' });
            }
            if (armyDistMap[key]) {
                cells.push({ position: cell.position, offset: offset1, icon: 'army_min_icon' });
            }
        }
        this.iconNode.Items(cells, function (it, data) {
            it.setPosition(_this._temp_vec2_3.set(data.offset).addSelf(data.position));
            it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon(data.icon);
        });
    };
    // 显示选择地块
    MainWindCtrl.prototype.showSelectCell = function (cell) {
        if (!cell || cell.landType == Enums_1.LandType.SEA || cell.landType == Enums_1.LandType.BEACH) {
            return;
        }
        else if (cell.actIndex !== cell.index) {
            cell = this.model.getMapCellByIndex(cell.actIndex);
        }
        var pos = this.selectCellNode_.Data = cell.actPosition;
        this.selectCellNode_.Component(SelectCellCmpt_1.default).open(pos, cell.getSize());
        this.cellInfoCmpt.open(pos, cell);
        // 隐藏文本节点
        this.updateHideTextByIndex(cell.actIndex);
    };
    // 隐藏
    MainWindCtrl.prototype.hideSelectCell = function (play) {
        if (play === void 0) { play = true; }
        if (this.selectCellNode_.Data) {
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close(play);
            this.updateHideTextByIndex();
        }
    };
    // 初始化行军
    MainWindCtrl.prototype.initMarch = function () {
        var _this = this;
        this.cleanMarch();
        var list = this.model.getAllMarchs().filter(function (m) { return m.isCanShowMarch(); });
        this.marchLineNode_.Items(list, function (it, data) {
            var march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
            march.isCheckLineOffset = false;
        });
        this.marchs.forEach(function (m) { return !m.isCheckLineOffset && _this.checkMarchLineOffset(m.getData()); });
    };
    MainWindCtrl.prototype.cleanMarch = function () {
        while (this.marchs.length > 0) {
            this.marchs.pop().clean();
        }
        this.marchs = [];
        // resHelper.cleanNodeChildren(this.marchLineNode_) //这个注释了 不知道什么原因会出现行军线被消耗的情况
        this.marchRoleNode_.removeAllChildren();
    };
    // 检测行军线偏移
    MainWindCtrl.prototype.checkMarchLineOffset = function (data) {
        var others = [];
        this.marchs.forEach(function (m) {
            var d = m.getData();
            if (data.checkOtherMarchLine(d)) {
                m.angleOffset = data.startIndex === d.startIndex ? 0 : -180;
                m.isCheckLineOffset = true;
                others.push(m);
            }
        });
        var len = others.length;
        others.forEach(function (m, i) { return m.updateLineOffset(i, len); });
    };
    // 攻击地块
    MainWindCtrl.prototype.occupyCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 2, 0)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_TIME) {
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_time_tip')];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'occupy', index, list, canGotoCount, function (armys, isSameSpeed, autoBackType) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.occupyCell(armys, index, autoBackType, isSameSpeed).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 移动过去屯田
    MainWindCtrl.prototype.cellTonden = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 3, 0)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err === ECode_1.ecode.BATTLEING) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.battleing_not_tonden')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectTondenArmy', index, list, canGotoCount, function (army) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.cellTonden(army, index).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 取消屯田
    MainWindCtrl.prototype.cancelTonden = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.model.cancelTonden(index)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isActive) {
                            this.hideSelectCell(false);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 移动到地块
    MainWindCtrl.prototype.moveToCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 1)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'move', index, list, canGotoCount, function (armys, isSameSpeed) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.moveCellArmy(armys, index, isSameSpeed).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放新的地块效果
    MainWindCtrl.prototype.playNewCellEffect = function () {
        var _this = this;
        this.model.getNotPlayNewCells().forEach(function (index) {
            var cell = _this.tempShowCellMap[index];
            if (cell) {
                var json_1 = cell.getResJson() || {}, keys = Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; });
                var pos_1 = cell.actPosition, isMore_1 = keys.length > 1;
                keys.forEach(function (key, i) { return AnimHelper_1.animHelper.playFlutterCellRes(key, json_1[key], 0.3 + i * 0.2, isMore_1, _this.topLayerNode_, pos_1, _this.key); });
                AnimHelper_1.animHelper.playNewCellEffect(_this.cellEffectNode_, pos_1, _this.key);
                // 隐藏行军线
                _this.marchs.forEach(function (march) { return march.isHasIndex(index) && march.hide(1.2); });
            }
        });
    };
    // 播放屯田结束的地块效果
    MainWindCtrl.prototype.playCellTondenEffect = function () {
        var _this = this;
        this.model.getNotPlayCellTondens().forEach(function (data) {
            var _a;
            var cell = _this.tempShowCellMap[data.index];
            if (cell) {
                var pos = cell.actPosition;
                var obj_1 = {};
                (_a = data.treasureIds) === null || _a === void 0 ? void 0 : _a.forEach(function (id) {
                    var idObj = obj_1[id];
                    if (!idObj) {
                        var json = assetsMgr.getJsonData('treasure', id);
                        idObj = obj_1[id] = { count: 0, icon: 'treasure_' + ((json === null || json === void 0 ? void 0 : json.lv) || 1) + '_0' };
                    }
                    idObj.count += 1;
                });
                for (var key in obj_1) {
                    var data_1 = obj_1[key];
                    AnimHelper_1.animHelper.playFlutterTreasure(data_1.icon, data_1.count, _this.topLayerNode_, pos, _this.key);
                }
                AnimHelper_1.animHelper.playNewCellEffect(_this.cellEffectNode_, pos, _this.key);
            }
        });
    };
    // 播放地图表情
    MainWindCtrl.prototype.playCellEmoji = function () {
        var _this = this;
        var now = Date.now();
        GameHelper_1.gameHpr.ground.getCellEmojis().forEach(function (m) {
            var cell = _this.tempShowCellMap[m.index], item = _this.cellEmojiItemMap[Math.floor(m.emoji / 1000)];
            if (cell && item) {
                var node = _this.cellEmojiMap[m.index];
                if (node === null || node === void 0 ? void 0 : node.isValid) {
                    node.Child('root').children.forEach(function (it) { return nodePoolMgr.put(it); });
                    node.destroy();
                }
                var startTime = Math.max(0, (now - m.getTime) * 0.001);
                node = _this.cellEmojiMap[m.index] = cc.instantiate2(item, _this.cellEmojiNode_);
                node.Data = m.index;
                node.setPosition(cell.actPosition);
                node.zIndex = cell.point.y;
                AnimHelper_1.animHelper.playCellEmoji(node, m.emoji, m.uid, startTime, _this.key).then(function (n) {
                    if (_this.isValid && (n === null || n === void 0 ? void 0 : n.isValid)) {
                        delete _this.cellEmojiMap[n.Data];
                        n.Child('root').children.forEach(function (it) { return nodePoolMgr.put(it); });
                        n.destroy();
                    }
                });
            }
        });
    };
    MainWindCtrl.prototype.testPlayNewCell = function (x, y, keys, delay) {
        var _this = this;
        // for (let i = 0; i < 5; i++) {
        //     const position = mapHelper.getPixelByPoint(cc.v2(x + i, y)).clone()
        //     animHelper.playFlutterCellRes('stone', 30, this.topLayerNode_, position, this.key)
        //     animHelper.playNewCellEffect(this.cellEffectNode_, position, this.key)
        // }
        var pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(x, y)).clone(), isMore = keys.length > 1;
        keys.forEach(function (key, i) { return AnimHelper_1.animHelper.playFlutterCellRes(key, 1, 0.3 + i * delay, isMore, _this.topLayerNode_, pos, _this.key); });
        AnimHelper_1.animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key);
    };
    // 检测季节
    MainWindCtrl.prototype.checkSeason = function () {
        var _this = this;
        var _a;
        var oldType = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.LAST_PLAY_SEASON_TYPE)) !== null && _a !== void 0 ? _a : -1;
        this.seasonType = cc.misc.clampf(oldType, 0, 3);
        if (ResHelper_1.resHelper.checkLandSkin(this.seasonType)) {
            this.updateMap(this.model.getCentre()); //刷新地图显示
        }
        else {
            ResHelper_1.resHelper.initLandSkin(this.seasonType).then(function () { return _this.isValid && _this.updateMap(_this.model.getCentre()); });
        }
        // 弹界面
        var curType = this.model.getSeason().type;
        if (oldType !== curType) {
            PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/SeasonSwitch' });
        }
    };
    // 播放切换季节
    MainWindCtrl.prototype.playChangeSeason = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.seasonType = type;
                        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.LAST_PLAY_SEASON_TYPE, type);
                        this.hideSelectCell(false);
                        return [4 /*yield*/, ResHelper_1.resHelper.initLandSkin(type)];
                    case 1:
                        _a.sent();
                        this.updateSeasonSeceneEffect();
                        this.updateMap(this.centre); //刷新地图显示
                        ResHelper_1.resHelper.cleanLandSkin();
                        return [2 /*return*/];
                }
            });
        });
    };
    MainWindCtrl.prototype.update = function (dt) {
        var _a;
        //
        (_a = this.seawaveAnimNodePool) === null || _a === void 0 ? void 0 : _a.update(dt);
        // 检测是否需要填充地图
        this.checkUpdateMap();
        // 检测是否在相机范围
        this.checkInCameraRange();
    };
    MainWindCtrl.prototype.checkUpdateMap = function () {
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2_1);
        var size = Math.max(Math.abs(point.x - this.centre.x), Math.abs(point.y - this.centre.y));
        if (size >= Constant_1.MAP_EXTRA_SIZE / 2 || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateMap(point);
            this.checkInCameraMarchLine();
        }
    };
    // 检测只会在在相机范围内的行军线
    MainWindCtrl.prototype.checkInCameraMarchLine = function () {
        var _a;
        var uidMap = {};
        this.marchs.forEach(function (m) {
            m.checkUpdateInCamera();
            uidMap[m.uid] = true;
        });
        // 兼容检测是否有多余的行军角色
        for (var i = this.marchRoleNode_.childrenCount - 1; i >= 0; i--) {
            var node = this.marchRoleNode_.children[i];
            if (!uidMap[(_a = node.Data) === null || _a === void 0 ? void 0 : _a.uid]) {
                node.destroy();
            }
        }
    };
    MainWindCtrl.prototype.checkInCameraRange = function () {
        var _a;
        var position = CameraCtrl_1.cameraCtrl.getPosition();
        if (this.preCameraPosition.equals(position)) {
            return;
        }
        this.preCameraPosition.set(position);
        // 选择地块框
        if ((_a = this.cellInfoCmpt) === null || _a === void 0 ? void 0 : _a.checkNotInScreenRange()) {
            this.hideSelectCell(false);
        }
    };
    MainWindCtrl = __decorate([
        ccclass
    ], MainWindCtrl);
    return MainWindCtrl;
}(mc.BaseWindCtrl));
exports.default = MainWindCtrl;

cc._RF.pop();