"use strict";
cc._RF.push(module, '68e8b+3xppL9pvROLmEBpw4', 'MailListPnlCtrl');
// app/script/view/menu/MailListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MailListPnlCtrl = /** @class */ (function (_super) {
    __extends(MailListPnlCtrl, _super);
    function MailListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.ttileLbl_ = null; // path://root/title/ttile_l
        _this.listSv_ = null; // path://root/list_sv
        _this.loadingNode_ = null; // path://root/loading_n
        _this.delReadBtn_ = null; // path://root/buttons/del_read_be_b
        _this.writeNode_ = null; // path://root/buttons/write_be_n
        //@end
        _this.model = null;
        return _this;
    }
    MailListPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.REMOVE_MAIL] = this.onRemoveMail, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_MAIL_STATE] = this.onUpdateMailState, _b.enter = true, _b),
        ];
    };
    MailListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('user');
                this.ttileLbl_.setLocaleKey('ui.title_mail_list', 0);
                return [2 /*return*/];
            });
        });
    };
    MailListPnlCtrl.prototype.onEnter = function () {
        var _this = this;
        this.listSv_.content.Swih('');
        this.loadingNode_.active = true;
        this.delReadBtn_.interactable = false;
        this.listSv_.Child('empty').active = false;
        this.model.getMails().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            ReddotHelper_1.reddotHelper.set('new_mail', false);
            _this.loadingNode_.active = false;
            _this.updateList(list);
        });
        this.writeNode_.active = GameHelper_1.gameHpr.alliance.isCanSendMail();
    };
    MailListPnlCtrl.prototype.onRemove = function () {
    };
    MailListPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    MailListPnlCtrl.prototype.onClickItem = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('menu/MailInfo', data);
        }
    };
    // path://root/buttons/write_be_n
    MailListPnlCtrl.prototype.onClickWrite = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/WriteMail');
    };
    // path://root/buttons/del_read_be_b
    MailListPnlCtrl.prototype.onClickDelRead = function (event, data) {
        var _this = this;
        this.model.delAllReadMail().then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateList(_this.model.getTempMails());
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 删除邮件
    MailListPnlCtrl.prototype.onRemoveMail = function (uid) {
        this.updateList(this.model.getTempMails());
    };
    // 刷新邮件状态
    MailListPnlCtrl.prototype.onUpdateMailState = function (data) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === data.uid; });
        if (it) {
            this.updateState(it, data.state);
        }
        if (data.state === Enums_1.MailStateType.READ) {
            this.delReadBtn_.interactable = true;
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    MailListPnlCtrl.prototype.updateList = function (mails) {
        var _this = this;
        var list = mails.sort(function (a, b) { return a.state === b.state ? b.createTime - a.createTime : a.state - b.state; }), len = list.length;
        this.delReadBtn_.interactable = mails.some(function (m) { return m.state === Enums_1.MailStateType.READ; });
        this.ttileLbl_.setLocaleKey('ui.title_mail_list', len);
        this.listSv_.Child('empty').active = len === 0;
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.List(len, function (it, i) {
            var data = it.Data = list[i];
            _this.updateState(it, data.state);
            var isReaded = data.state === Enums_1.MailStateType.READ; // 已读状态全部换色 
            it.Child('title', cc.Label).Color(isReaded ? '#A18876' : '#3F332F').string = data.title;
            it.Child('sender/name').Color(isReaded ? '#A18876' : '#625450');
            var isSys = data.sender === '-1';
            it.Child('sender/val', cc.Label).Color(isReaded ? '#A18876' : isSys ? '#BE772B' : '#936E5A').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8);
            it.Child('time', cc.Label).Color(isReaded ? '#A18876' : '#625450').string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime);
        });
    };
    MailListPnlCtrl.prototype.updateState = function (it, state) {
        it.Child('state').Color(Constant_1.MAIL_STATE_COLOR[state]).setLocaleKey('ui.mail_state_' + state);
    };
    MailListPnlCtrl = __decorate([
        ccclass
    ], MailListPnlCtrl);
    return MailListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MailListPnlCtrl;

cc._RF.pop();