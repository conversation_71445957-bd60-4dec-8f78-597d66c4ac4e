package r

import (
	"context"
	"encoding/json"
	"math/rand"
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/bazaar"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bdtype"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/player"
	"slgsrv/server/game/record"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	mgo "slgsrv/utils/mgodb"
	rds "slgsrv/utils/redis"
	"sort"
	"strings"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"google.golang.org/protobuf/reflect/protoreflect"
)

type PlayerMap struct {
	deadlock.RWMutex
	Map map[string]*player.Model
}

func (this *PlayerMap) Get(uid string) *player.Model {
	this.RLock()
	defer this.RUnlock()
	return this.Map[uid]
}

func (this *PlayerMap) Set(uid string, plr *player.Model) {
	this.Lock()
	this.Map[uid] = plr
	this.Unlock()
}

func (this *PlayerMap) Del(uid string) {
	this.Lock()
	delete(this.Map, uid)
	this.Unlock()
}

// 被删除的玩家信息
type DeletePlayerInfo struct {
	UID           string `bson:"uid"`
	OfflineTime   int64  `bson:"offline_time"`    //离线时长
	Time          int64  `bson:"time"`            //删除时间
	MainCityIndex int32  `bson:"main_city_index"` //主城位置
	CellCount     int32  `bson:"cell_count"`      //删除时的地块数量
}

// 玩家通知信息
type NotifyAllPlayerInfo struct {
	topic string
	msg   []byte
	uids  []string
}

type PlayerNotifyInfo struct {
	uid string
	msg *pb.OnUpdatePlayerInfoNotify
}

// 一局游戏
type Model struct {
	initTime         int64   //初始化时间
	createTime       int64   //创建时间
	startupTime      int64   //启动时间
	dailyCheckTime   int64   //游戏结束每日检测时间
	checkLateWinTime int64   //游戏后期检测时间
	avgRankScore     float64 //平均段位分

	winCond   []int32    //胜利条件 [type,value,time] 1.领地争夺 2.修建遗迹 3.血战到底
	staminas  []int32    //体力配置 [初始体力，最大体力]
	mainCitys [][]int32  //玩家剩余可创建的位置
	applyUids [][]string //报名玩家uid列表

	clientVersion    string //要求客户端版本
	clientBigVersion string //客户端大版本
	curVersion       string //当前运行的版本

	deletePlayers        map[string]*DeletePlayerInfo //被删除的玩家信息
	playerHasTreasure    chan string                  //玩家是否有宝箱
	notifyAllPlayerQueue chan NotifyAllPlayerInfo     //通知队列
	playerNotifyQueue    chan PlayerNotifyInfo        //通知队列
	playerUpdateOpSec    chan string                  //通知玩家刷新产出

	mapSize      *ut.Vec2          //地图大小
	gameOverInfo *slg.GameOverInfo //游戏结束信息
	players      *PlayerMap        //当前在线玩家列表
	pMgr         *player.Manager   //玩家管理
	world        *world.Model      //世界
	bazaar       *bazaar.Model     //市场
	record       *record.Model     //记录

	createPlayerMutex *deadlock.RWMutex
	deletePlayerMutex *deadlock.RWMutex
	gameOverMutex     *deadlock.RWMutex

	Id         int32 //房间唯一id
	mapId      int32 //地图id
	currRunDay int32 //运行天数
	farmType   int32 //开荒方式

	Type      uint8 //对局类型 0.普通区 1.新手区 2.评分区
	SubType   uint8 //子类型
	state     int8  //服务器状态
	LateGame  bool  //是否游戏后期
	isRunning bool
}

func NewRoom(id int32, tp, subType uint8, mapSize *ut.Vec2, winCond []int32, staminas []int32, isAddIndex bool) *Model {
	if isAddIndex {
		sid := g.ServerIdToString(id)
		//添加玩家集合索引
		mgo.AddUniqueIndex("player_"+sid, "uid", 1)
		//添加地图集合索引
		mgo.AddIndex("world_"+sid, map[string]int{"index": 1})
		//添加战斗积分记录集合索引
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_BATTLE_SCORE+"_"+sid, map[string]int{"owner": 1, "date": -1, "alli_uid": 1})
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_BATTLE_SCORE+"_"+sid, map[string]int{"time": -1})
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_BATTLE_SCORE+"_"+sid, map[string]int{"version": 1})
		//添加玩家战斗积分记录集合索引
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_BATTLE_SCORE_PLAYER+"_"+sid, map[string]int{"owner": 1, "date": -1, "alli_uid": 1})
		//军队记录表添加索引
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_ARMY+"_"+sid, map[string]int{"owner": 1, "sid": 1, "type": 1, "time": -1})
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_ARMY+"_"+sid, map[string]int{"uid": 1, "battle_uid": 1})
		//战斗记录表添加索引
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_BATTLE+"_"+sid, map[string]int{"sid": 1, "uid": 1})
		//市场记录表添加索引
		mgo.AddIndex(slg.DB_COLLECTION_NAME_RECORD_BAZAAR+"_"+sid, map[string]int{"sid": 1, "owner": 1, "type": 1, "target": 1, "time": -1})
	}
	return &Model{
		Id:                   id,
		Type:                 tp,
		SubType:              subType,
		clientVersion:        slg.CLIENT_VERSION,
		clientBigVersion:     slg.CLIENT_BIG_VERSION,
		players:              &PlayerMap{Map: map[string]*player.Model{}},
		pMgr:                 player.NewManager(id),
		createPlayerMutex:    new(deadlock.RWMutex),
		deletePlayerMutex:    new(deadlock.RWMutex),
		gameOverMutex:        new(deadlock.RWMutex),
		playerHasTreasure:    make(chan string, 100),
		notifyAllPlayerQueue: make(chan NotifyAllPlayerInfo),
		playerNotifyQueue:    make(chan PlayerNotifyInfo),
		playerUpdateOpSec:    make(chan string),
		winCond:              winCond,
		staminas:             staminas,
		mapSize:              mapSize,
		isRunning:            false,
	}
}

// 创建一个空的游戏
func InitRoom(id int32, tp, subType uint8, now int64, mapSize *ut.Vec2, winCond []int32, staminas []int32, mainCitys [][]int32, maps []int32, mapId int32) *Model {
	model := NewRoom(id, tp, subType, mapSize, winCond, staminas, false)
	model.mapId = mapId
	model.initTime = now
	model.mainCitys = mainCitys
	model.deletePlayers = map[string]*DeletePlayerInfo{}
	model.world = world.NewModel(model).Init(maps, mapSize)
	model.bazaar = bazaar.NewModel(model).Init()
	model.InitModel()
	return model
}

func NewRoomByDB(data RoomTableData) *Model {
	isCreate := false
	if data.CreateTime == 0 {
		data.CreateTime = ut.Now()
		isCreate = true
		data.CurVersion = slg.CLIENT_VERSION
	}
	if data.MapSize == nil || data.MapSize.X == 0 {
		data.MapSize.Init(600, 600)
	}
	if data.WinCond == nil {
		data.WinCond = []int32{1, 30000, 20, ut.TIME_DAY * 1}
	}
	if data.Staminas == nil {
		data.Staminas = []int32{100, 50}
	}
	model := NewRoom(data.Id, data.Type, data.SubType, data.MapSize, data.WinCond, data.Staminas, true)
	model.createTime = data.CreateTime
	model.gameOverInfo = data.GameOverInfo
	model.mainCitys = data.MainCitys
	model.deletePlayers = ut.If(data.DeletePlayers == nil, map[string]*DeletePlayerInfo{}, data.DeletePlayers)
	model.clientVersion = slg.CLIENT_VERSION
	model.clientBigVersion = slg.CLIENT_BIG_VERSION
	model.state = data.State
	model.applyUids = data.ApplyUids
	model.initTime = data.InitTime
	model.mapId = data.MapId
	model.currRunDay = model.GetRunDay()
	model.farmType = data.FarmType
	model.curVersion = data.CurVersion
	model.avgRankScore = data.avgRankScore
	if model.farmType == constant.FARM_TYPE_NONE {
		// 兼容 未设置开荒方式的房间默认为普通模式
		model.farmType = constant.FARM_TYPE_NORMAL
	}
	// 如果没有关闭才初始化
	if !model.IsClose() {
		players := model.pMgr.Find() //获取所有的玩家
		model.world = world.NewModel(model)
		model.world.FromDB(players, isCreate)
		model.bazaar = bazaar.NewModel(model).FromDB()
		model.InitModel()
		model.GetWorld().AncientAvoidWarCheck()
	}
	model.curVersion = slg.CLIENT_VERSION
	if isCreate || model.curVersion != data.CurVersion {
		model.SaveDB()
	}
	return model
}

func (this *Model) InitModel() {
	this.record = record.NewModel(this)
	// this.state = slg.SERVER_STATUS_OPEN
}

func (this *Model) SaveDB() {
	if _, e := mgo.GetCollection("room").UpdateOne(context.TODO(), bson.M{"id": this.Id}, bson.M{"$set": RoomTableData{
		Id:            this.Id,
		InitTime:      this.initTime,
		CreateTime:    this.createTime,
		MainCitys:     this.mainCitys,
		MapSize:       this.mapSize,
		WinCond:       this.winCond,
		GameOverInfo:  this.gameOverInfo,
		DeletePlayers: this.ToDeletePlayersDB(),
		Type:          this.Type,
		SubType:       this.SubType,
		ApplyUids:     this.applyUids,
		Staminas:      this.staminas,
		MapId:         this.mapId,
		CurVersion:    this.curVersion,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error(e.Error())
	}
}

// 保存单条信息
func (this *Model) UpdateDataToDB(data bson.M) {
	go func() {
		if _, e := mgo.GetCollection("room").UpdateOne(context.TODO(), bson.M{"id": this.Id}, bson.M{"$set": data}); e != nil {
			log.Error("Room UpdateDataToDB Error: %v", e.Error())
		}
	}()
}

// 同步游戏服信息到 匹配服
func (this *Model) NotifyRoomInfoToMatch() {
	bytes, _ := json.Marshal(this.Strip())
	_, err := this.Invoke("match", slg.RPC_GAME_SERVER_STATE_CHANGE, bytes)
	if err != "" {
		log.Error("NotifyRoomInfoToMatch err: %v, sid: %v", err, this.Id)
	}
	log.Info("NotifyRoomInfoToMatch sid: %d", this.Id)
}

// 区服状态改变
func (this *Model) RoomStateChangeToOpen() {
	if this.state == slg.SERVER_STATUS_APPLYING {
		// 之前状态是报名中则开区
		this.OpenRoom()
	}
	this.state = slg.SERVER_STATUS_OPEN
	this.UpdateDataToDB(bson.M{"state": this.state, "avg_rank_score": this.avgRankScore})
}

// 匹配联盟的队伍
type TeamForMatch struct {
	Uid      string
	UserList []string
}

// 开启区服
func (this *Model) OpenRoom() {
	log.Info("RoomStateChangeToOpen createApplyPlayers")
	if slg.IsLocal() {
		time.Sleep(time.Second * 5)
	}
	// 从redis中获取队伍
	rdsKey := rds.RDS_CREATE_ROOM_TEAM_MAP_KEY + ut.String(this.Id)
	rdsData, err := rds.RdsHGetAll(rdsKey)
	userMap := map[string]interface{}{}
	teamForMatchList := []*TeamForMatch{} // 待分配队伍列表
	teamFinishList := []*TeamForMatch{}   // 已完成分配的队伍列表（满编队伍和独立队伍）
	if err == nil {
		// 获取每个队伍的玩家信息
		for teamUid := range rdsData {
			info, err := ut.RpcInterfaceMap(this.InvokeTeamFunc(teamUid, slg.RPC_GET_TEAM_USERS_BY_CREATEPLAYER, teamUid, this.Id))
			if err != "" {
				log.Error("RoomStateChangeToOpen createApplyPlayers get team users err: %v", err)
				continue
			}
			userInfoMap, ok := info["userMap"].(map[string]interface{})
			if !ok {
				log.Error("RoomStateChangeToOpen createApplyPlayers get team users data err: %v", userInfoMap)
				continue
			}
			if len(userInfoMap) == 0 {
				log.Error("RoomStateChangeToOpen createApplyPlayers get team users data nil teamUid: %v", teamUid)
				continue
			}
			teamforMatch := &TeamForMatch{Uid: teamUid, UserList: []string{}}
			for uid, userData := range userInfoMap {
				userMap[uid] = userData
				teamforMatch.UserList = append(teamforMatch.UserList, uid)
				log.Info("room start CreatePlayer uid: %v, CreatePlayer teamUid: %v", uid, teamUid)
			}
			if !ut.Bool(info["independent"]) || len(userInfoMap) < constant.ALLI_MAX_PERS {
				// 未满编且需要分配联盟的队伍添加到待分配列表
				teamForMatchList = append(teamForMatchList, teamforMatch)
			} else {
				// 否则添加到已完成分配列表
				teamFinishList = append(teamFinishList, teamforMatch)
			}
			// 删除缓存
			rds.RdsHDel(rdsKey, teamUid)
		}

		// 根据期望位置分配主城区域
		posUserMap := CreateRoomMallocPlayerPos(userMap)
		log.Info("RoomStateChangeToOpen createApplyPlayers userNum: %v", len(userMap))
		var totalRankScore int32
		// 初始化报名玩家
		for pos, uidList := range posUserMap {
			for _, uid := range uidList {
				userData := userMap[uid]
				user := ut.MapInterface(userData)
				uid, nickname, headIcon, personalDesc, title, pawnSkins, regTime, os, osVer, platform, fcmToken, lang, offlineNotifyOpt, distinctId, mainCitySkin, treasureLostCount, antiCheat, isOnline, farmType, rankScore :=
					ut.String(user["uid"]), ut.String(user["nickname"]), ut.String(user["headIcon"]), ut.String(user["personalDesc"]), ut.Int32(user["title"]), ut.Int32Array(user["pawnSkins"]), ut.Int64(user["registerTime"]),
					ut.String(user["deviceOS"]), ut.String(user["deviceOSVersion"]), ut.String(user["platform"]), ut.String(user["FCMToken"]), ut.String(user["lang"]), ut.Int32Array(user["offlineNotifyOpt"]),
					ut.String(user["distinctId"]), ut.Int32(user["mainCitySkin"]), ut.Int32(user["treasureLostCount"]), ut.Int32Array(user["antiCheat"]), ut.Bool(user["isOnline"]), ut.Int32(user["farmType"]), ut.Int32(user["rankScore"])
				this.CreatePlayer(uid, nickname, headIcon, personalDesc, os, osVer, platform, regTime, title, pawnSkins, int32(pos), lang, fcmToken, offlineNotifyOpt, distinctId, false, mainCitySkin, treasureLostCount, antiCheat, farmType, true)
				if !isOnline {
					this.world.OfflineNotify(uid, constant.OFFLINE_MSG_TYPE_SERVER_OPEN, ut.String(this.Id))
				}
				totalRankScore += rankScore
			}
		}

		// 设置平均段位分
		this.avgRankScore = float64(totalRankScore) / float64(this.pMgr.Pers)
		// 血战到底预创建联盟
		if this.IsConquer() {
			// 已完成分配的队伍直接创建联盟
			this.matchCreatAlli(teamFinishList)
			// 其他队伍分配到联盟
			this.AllocateTeamsToAlliances(teamForMatchList)
		}

		// 上报
		ta.TrackSystem("ta_open_new_server", map[string]interface{}{
			"sid_type": this.Type,
			"sid":      this.Id,
			"pers":     this.pMgr.Pers,
		})
	} else {
		log.Error("RoomStateChangeToOpen createApplyPlayers rds getTeam err: %v", err)
	}
	log.Info("RoomStateChangeToOpen createApplyPlayers end")

	// 初始化士兵资源消耗map
	this.world.InitPawnCostMap()
}

type AllianceForMatch struct {
	Teams []*TeamForMatch // 联盟包含的队伍
	Total int             // 联盟总人数
}

// 分配队伍到联盟
func AllocateTeamsToAlliances(teamForMatchList []*TeamForMatch) []*AllianceForMatch {
	// 队伍人数降序
	sort.Slice(teamForMatchList, func(i, j int) bool {
		return len(teamForMatchList[i].UserList) > len(teamForMatchList[j].UserList)
	})
	// 贪心算法分配队伍
	alliances := []*AllianceForMatch{}
	for _, team := range teamForMatchList {
		bestIdx := -1
		minRemaining := -1
		// 寻找最适合的联盟：能容纳且剩余空间最大 尽可能去填空位更多的联盟
		for i, alliance := range alliances {
			if alliance.Total+len(team.UserList) <= 40 {
				remaining := 40 - (alliance.Total + len(team.UserList))
				if remaining > minRemaining {
					minRemaining = remaining
					bestIdx = i
				}
			}
		}
		if bestIdx != -1 {
			// 加入现有联盟
			alliances[bestIdx].Teams = append(alliances[bestIdx].Teams, team)
			alliances[bestIdx].Total += len(team.UserList)
		} else {
			// 创建新联盟
			alliances = append(alliances, &AllianceForMatch{
				Teams: []*TeamForMatch{team},
				Total: len(team.UserList),
			})
		}
	}

	// 进一步调整队伍减小联盟人数差
	maxTryCount := len(alliances)
loop1:
	for maxTryCount > 0 && len(alliances) > 1 {
		// 联盟人数降序
		sort.Slice(alliances, func(i, j int) bool {
			return alliances[i].Total > alliances[j].Total
		})
		minAlli := alliances[len(alliances)-1]
		for left := 0; left < len(alliances)/2; left++ {
			maxAlli := alliances[left]
			// 获取人数最多联盟和最少联盟的人数差
			maxDiff := maxAlli.Total - minAlli.Total
			if maxDiff <= 1 {
				// 人数差已最小 跳出最外层循环
				break loop1
			}
			moveCnt := maxDiff / 2
			var moveTeam *TeamForMatch
			for i, team := range maxAlli.Teams {
				if len(team.UserList) <= moveCnt {
					// 降序排列的队伍 人数刚好满足移动人数
					moveTeam = team
					maxAlli.Teams = append(maxAlli.Teams[:i], maxAlli.Teams[i+1:]...)
					maxAlli.Total -= len(moveTeam.UserList)
					break
				}
			}
			if moveTeam == nil {
				// 人数最多联盟没有可移动的队伍
				continue
			}
			// 移动该队伍到末尾的联盟
			minAlli.Teams = append(minAlli.Teams, moveTeam)
			minAlli.Total += len(moveTeam.UserList)
			// 队伍按人数降序
			sort.Slice(minAlli.Teams, func(i, j int) bool {
				return len(minAlli.Teams[i].UserList) > len(minAlli.Teams[j].UserList)
			})
			break
		}

		maxTryCount--
	}

	return alliances
}

// 分配队伍到联盟
func (this *Model) AllocateTeamsToAlliances(teamForMatchList []*TeamForMatch) {
	alliances := AllocateTeamsToAlliances(teamForMatchList)
	// 创建联盟
	for _, v := range alliances {
		this.matchCreatAlli(v.Teams)
	}
}

// 匹配完成后创建联盟
func (this *Model) matchCreatAlli(teamList []*TeamForMatch) {
	if len(teamList) == 0 {
		return
	}
	plrList := []string{}
	for _, v := range teamList {
		plrList = append(plrList, v.UserList...)
	}
	this.world.CreateAllianceByMatch("", 0, 0, plrList)
}

func (this *Model) IsOpen() bool {
	return this.state == slg.SERVER_STATUS_OPEN
}

func (this *Model) Strip() map[string]interface{} {
	msg := map[string]interface{}{
		"id":               this.Id,
		"type":             this.Type,
		"createTime":       this.createTime,
		"pers":             this.pMgr.Pers,
		"persCap":          this.GetPersCap(),
		"surplusDay":       this.GetRunSurplusDay(),
		"state":            this.state,
		"clientVersion":    this.clientVersion,
		"clientBigVersion": this.clientBigVersion,
		"addr":             slg.SERVER_IP,
		"winCond":          this.winCond,
	}
	if this.gameOverInfo != nil {
		msg["winType"] = this.gameOverInfo.WinType
		msg["winName"] = this.gameOverInfo.WinName
		msg["winCellCount"] = this.gameOverInfo.WinCellCount
		msg["endTime"] = this.gameOverInfo.EndTime
	}
	return msg
}

func (this *Model) Run() {
	this.isRunning = true
	this.runTick()
	this.runUpdateCellInfoToDB()
	this.runUpdateGameToDB()
	this.runUpdateAllianceToDB()
	this.runUpdateChatToDB()
	this.runCheckWorldNotify()
	this.world.CheckAllTriggerAreaBattle()
	// this.runCheckPlayerOfflineForLong()
	this.bazaar.Run()
	this.runUpdatePlayerAlliScore()
	this.runUpdateCheckOfflineNotify()
	this.runUpdateTriggerTask()
	this.goRunTick(this.world.UpdatePawnUseStasticDb, time.Hour)

	go this.runNotifyAllPlayer()
	go this.runPlayerNotify()
	go this.runPlayerUpdateOpSec()

	this.startupTime = time.Now().UnixMilli()
	log.Info("run room[" + ut.Itoa(this.Id) + "] done.")
}

func (this *Model) Stop(close int) {
	if !this.isRunning {
		return
	}
	this.isRunning = false
	// 保存玩家信息
	for _, plr := range this.players.Map {
		this.PlayerLeave(plr, close)
	}
	// 保存世界信息
	this.world.Stop()
	// 保存市场
	this.bazaar.Stop()
	// 保存
	this.SaveDB()
	this.state = slg.SERVER_STATUS_STOP
	this.NotifyRoomInfoToMatch()
}

func (this *Model) update(_, now int64) {
	if this.gameOverInfo != nil {
		closeTime := int32(constant.CLOSE_GAME_TIME)
		if len(this.winCond) >= 4 {
			closeTime = this.winCond[3]
		}
		if now-this.gameOverInfo.EndTime >= int64(closeTime) {
			this.CloseGameOverRoom()
			return
		}
	}
	// 过天
	isToDay := false
	if this.dailyCheckTime == 0 || now > this.dailyCheckTime {
		isToDay = this.dailyCheckTime != 0
		this.dailyCheckTime = int64(ut.NowZeroTime()) + ut.TIME_DAY
	}
	// 运行天数
	if runDay := this.GetRunDay(); this.currRunDay != runDay {
		this.currRunDay = runDay
		this.world.UpdateRunDay(runDay)
	}
	// 刷新季节
	this.world.GetSeason().Update(now)
	// 刷新遗迹
	this.world.CheckUpdateAncient(now)
	// 检测盟主投票
	this.world.CheckAlliLeaderVote()
	// 检测盟主确认
	this.world.CheckAlliLeaderConfirm()
	// 发送是否有宝箱信息
	if len(this.playerHasTreasure) > 0 {
		uids := map[string]bool{}
		for len(this.playerHasTreasure) > 0 {
			uid := <-this.playerHasTreasure
			uids[uid] = true
		}
		for uid := range uids {
			if plr := this.GetPlayer(uid); plr != nil {
				plr.NotifyHasTreasure(this.world.CheckPlayerHasTreasure(uid))
			}
		}
	}
	// 检测游戏是否结束
	if isToDay {
		this.CheckGameOverByToDay(now)
		this.world.CheckAllMarchTargetCanAddArmy(false, -1) //检测行军
	}
	// 冬季 检测游戏结束 冬季结束强制结束游戏
	if this.world.GetSeason().GetType() >= 3 && !this.CheckGameOverByMaxDay() {
		if now-this.checkLateWinTime > 10000 {
			this.checkLateWinTime = now
			this.CheckLateGameAlliWin()
		}
	}
}

func (this *Model) GetSID() int32                { return this.Id }
func (this *Model) GetType() uint8               { return this.Type }
func (this *Model) GetSubType() uint8            { return this.SubType }
func (this *Model) GetCreateTime() int64         { return this.createTime }
func (this *Model) SetCreateTime(val int64)      { this.createTime = val }
func (this *Model) GetMapSize() *ut.Vec2         { return this.mapSize }
func (this *Model) GetMapId() int32              { return this.mapId }
func (this *Model) GetWinCond() []int32          { return this.winCond }
func (this *Model) SetWinCond(val []int32)       { this.winCond = val }
func (this *Model) GetWorld() g.World            { return this.world }
func (this *Model) GetBazaar() g.Bazaar          { return this.bazaar }
func (this *Model) GetRecord() g.Record          { return this.record }
func (this *Model) GetProcessRunTime() int64     { return time.Now().UnixMilli() - this.startupTime } //获取进程运行时间
func (this *Model) GetClientVersion() string     { return this.clientVersion }
func (this *Model) GetClientBigVersion() string  { return this.clientBigVersion }
func (this *Model) SetClientVersion(v string)    { this.clientVersion = v }
func (this *Model) SetClientBigVersion(v string) { this.clientBigVersion = v }
func (this *Model) GetStaminas() []int32         { return this.staminas }
func (this *Model) GetFarmType() int32           { return this.farmType }
func (this *Model) GetPlayerNum() int32          { return this.pMgr.Pers }
func (this *Model) GetAvgRankScore() float64     { return this.avgRankScore }

func (this *Model) IsGameOver() bool {
	this.gameOverMutex.RLock()
	defer this.gameOverMutex.RUnlock()
	return this.gameOverInfo != nil
}

// 是否自由区
func (this *Model) IsFreeServer() bool {
	return this.Type == slg.NORMAL_SERVER_TYPE && this.SubType == 1
}

func (this *Model) GetWinCondPb() []int64 {
	return array.Map(this.winCond, func(m int32, _ int) int64 { return pb.Int64(m) })
}

func (this *Model) GetMapSizePb() *pb.MapSize {
	return &pb.MapSize{
		X: pb.Int32(this.mapSize.X),
		Y: pb.Int32(this.mapSize.Y),
	}
}

// 是否已满
func (this *Model) IsFull() bool {
	return this.GetPersCap() == 0
}

// 获取剩余人数容量
func (this *Model) GetPersCap() int {
	cnt := 0
	for _, arr := range this.mainCitys {
		cnt += ut.Max(len(arr)-constant.MAIN_CITY_PERS_MIN_COUNT, 0)
	}
	return cnt
}

// 是否已经关闭
func (this *Model) IsClose() bool {
	return this.GetSurplusCloseTime() < 0
}

// 获取胜利者
func (this *Model) GetGameWiner() (winType int32, winUID string) {
	if this.gameOverInfo == nil {
		return -1, ""
	}
	return this.gameOverInfo.WinType, this.gameOverInfo.WinUID
}

// 获取剩余关闭时间 返回-1 表示已经关闭
func (this *Model) GetSurplusCloseTime() int {
	if this.gameOverInfo == nil {
		return 0
	}
	var time int32 = constant.CLOSE_GAME_TIME
	if len(this.winCond) >= 4 {
		time = this.winCond[3]
	}
	return ut.Max(-1, int(time)-int(ut.Now()-this.gameOverInfo.EndTime))
}

// 获取运行剩余天数
func (this *Model) GetRunSurplusDay() int32 {
	if this.gameOverInfo != nil {
		return 0
	} else if len(this.winCond) < 3 {
		return -1
	} else if maxDay := this.winCond[2]; maxDay > 0 {
		return ut.MaxInt32(0, maxDay*ut.TIME_DAY-this.GetRunTime())
	}
	return -1
}

// 运行时间
func (this *Model) GetRunTime() int32 {
	if this.createTime == 0 {
		return 0
	}
	return ut.MaxInt32(0, int32(ut.Now()-this.createTime))
}

// 获取运行天数
func (this *Model) GetRunDay() int32 {
	if this.createTime == 0 {
		return 0
	}
	return int32(ut.Ceil(float64(this.GetRunTime()) / float64(ut.TIME_DAY)))
}

// 游戏结束
func (this *Model) GameOver(winType int32, winUid string, winName string, winCellCount int32, winners [][]string, ancientInfo []int32) {
	winnerList, winAlliInfo := this.GetGameoverStatistics(winType, winUid, winners)
	this.gameOverMutex.Lock()
	defer this.gameOverMutex.Unlock()
	if this.gameOverInfo != nil {
		return
	} else if this.world.GetSeason().GetType() < 1 {
		return //胜利条件夏季才开始生效
	}

	endTime := time.Now().UnixMilli()
	this.gameOverInfo = &slg.GameOverInfo{
		WinType:      winType,
		WinUID:       winUid,
		WinName:      winName,
		Winners:      winners,
		WinCellCount: winCellCount,
		EndTime:      endTime,
		AncientInfo:  ancientInfo,
		WinnerList:   winnerList,
		WinAlliInfo:  winAlliInfo,
	}
	this.UpdateDataToDB(bson.M{"game_over_info": this.gameOverInfo})

	// 血战到底结束前已结算玩家数据 其他模式在结束时结算
	if !this.IsConquer() {
		// 获取交战时间
		var canOccupyTimeStartH, canOccupyTimeStartM int64
		canOccupyTime := slg.GetCanOccupyTime()
		if len(canOccupyTime) >= 4 {
			canOccupyTimeStartH = int64(canOccupyTime[0])
			canOccupyTimeStartM = int64(canOccupyTime[1])
		} else {
			canOccupyTimeStartH = 6
			canOccupyTimeStartM = 0
		}
		// 计算当天联盟分
		todayZero := ut.NowZeroTime()
		timeFour := ut.NowFourTime() //联盟分凌晨4点结算
		battleTimeStart := todayZero + canOccupyTimeStartH*3600*1000 + canOccupyTimeStartM*60*1000
		if endTime <= timeFour {
			// 结束时间小于凌晨4点 结算前一天的联盟分
			t := time.UnixMilli(int64(endTime - ut.TIME_DAY))
			this.world.UpdatePlayerAlliScore(t)
		} else if endTime >= battleTimeStart {
			// 结束时间大于当天交战时间 结算当天的联盟分
			t := time.UnixMilli(int64(endTime))
			this.world.UpdatePlayerAlliScore(t)
		}

		// 获取排行榜数据
		players := this.world.GetAllPlayerScoreRankInfo(winType, winUid)
		alliMap := this.world.GetSettleAlliInfo("", true)
		// 获取结算的大厅服
		lid := rds.GetRandomLid()
		if lid == "" {
			log.Error("GameOver not lid?")
			return
		}
		// 评分区结算玩家的段位分
		if this.Type == slg.RANK_SERVER_TYPE {
			this.InvokeLobbyRpcNR(lid, slg.RPC_GAMEOVER_SETTLE, this.Id, this.Type, []int64{this.createTime, endTime}, players, this.pMgr.Pers, alliMap)
		} else { //其他区 记录胜利次数
			this.InvokeLobbyRpcNR(lid, slg.RPC_GAMEOVER_SETTLE, this.Id, this.Type, []int64{this.createTime, endTime}, players, this.pMgr.Pers, alliMap)
		}
	}

	// 通知服务器状态
	this.NotifyRoomInfoToMatch()
	// 通知
	this.world.PutNotifyQueue(constant.NQ_GAME_OVER, &pb.OnUpdateWorldInfoNotify{Data_36: this.ToGameOverInfoPb()})
	// 上报
	ta.TrackSystem("ta_server_game_over", map[string]interface{}{
		"sid_type":      this.Type,
		"sid":           this.Id,
		"pers":          this.pMgr.Pers,
		"run_time":      endTime - this.createTime,
		"ancient_infos": this.world.GetAncientTrackData(),
		"season":        this.world.GetSeason().GetType(),
	})
	// 添加本局士兵使用统计
	this.world.AddGamePawnUseStastics()
	log.Info("GameOver winType: %v, winUid: %v, winName: %v", winType, winUid, winName)
}

// 获取对局结束统计数据
func (this *Model) GetGameoverStatistics(winType int32, winUid string, winners [][]string) ([]*slg.GameOverWinnerInfo, *slg.GameOverWinAlliInfo) {
	var winAlliInfo *slg.GameOverWinAlliInfo
	var winnerInfoList = []*slg.GameOverWinnerInfo{}
	// 获取获胜者信息
	for _, v := range winners {
		if len(v) == 0 {
			continue
		}
		plr := this.world.GetTempPlayer(v[0])
		if plr == nil {
			continue
		}
		winner := &slg.GameOverWinnerInfo{
			UID:         plr.Uid,
			Name:        plr.Nickname,
			Headicon:    plr.HeadIcon,
			Score:       plr.AlliScoreTop + plr.LandScoreTop,
			OccupyCount: plr.GetBattleRecordByType(bdtype.KILL_MAIN),
			KillPawn:    plr.GetBattleRecordByType(bdtype.KILL_PAWN),
			DeadPawn:    plr.GetBattleRecordByType(bdtype.PAWN_DEAD),
		}
		if len(v) >= 3 {
			winner.Job = ut.Int32(v[2])
		} else {
			winner.Job = 10
		}
		winnerInfoList = append(winnerInfoList, winner)
	}
	if winType == 2 {
		// 联盟获胜
		if alli := this.world.GetAlliance(winUid); alli != nil {
			winAlliInfo = &slg.GameOverWinAlliInfo{
				UID:      alli.Uid,
				Name:     alli.Name,
				Headicon: alli.Icon,
			}
		}

	}

	return winnerInfoList, winAlliInfo
}

// 检测游戏是否结束 根据是否超过最大天数
func (this *Model) CheckGameOverByMaxDay() bool {
	if this.IsGameOver() || len(this.winCond) < 3 || !this.world.GetSeason().IsEnd() {
		return false
	} else if this.winCond[2] == -1 { //永远不结束
		return false
	} else if this.winCond[0] == 2 { //修建遗迹条件
		// 判断等级最高的古城 如果没有就根据 领地最高
		var winnerUid, winnerAlliUid string
		var cityId, maxLv, maxScore int32
		this.world.AncientCityMap.ForEach(func(v *world.AncientInfo, index int32) bool {
			area := this.world.GetArea(index)
			if area == nil || area.Owner == "" {
				return true
			}
			build := area.GetAreaBuildById(area.CityId)
			if build == nil {
				return true
			}
			if build.Lv > maxLv {
				plr := this.world.GetTempPlayer(area.Owner)
				if plr == nil {
					return true
				}
				if alli := this.world.GetAlliance(plr.AllianceUid); alli != nil {
					winnerUid = ""
					winnerAlliUid = plr.AllianceUid
					_, _, _, maxScore = this.world.GetAlliRankInfo(alli)
				} else {
					winnerAlliUid = ""
					winnerUid = area.Owner
					maxScore = this.world.GetPlayerTotalScore(area.Owner)
				}
				maxLv = build.Lv
				cityId = area.CityId
			} else if build.Lv > 0 && build.Lv == maxLv {
				// 等级相等 比较积分
				if area.Owner == winnerUid {
					return true
				}
				plr := this.world.GetTempPlayer(area.Owner)
				if plr == nil {
					return true
				}
				if alli := this.world.GetAlliance(plr.AllianceUid); alli != nil {
					// 获取联盟总积分
					_, _, _, score := this.world.GetAlliRankInfo(alli)
					if score > maxScore {
						winnerAlliUid = plr.AllianceUid
						winnerUid = ""
						cityId = area.CityId
						maxScore = score
					}
				} else {
					// 没有联盟则计算个人积分
					score := this.world.GetPlayerTotalScore(area.Owner)
					if score > maxScore {
						winnerUid = plr.AllianceUid
						winnerAlliUid = ""
						cityId = area.CityId
						maxScore = score
					}
				}
			}
			return true
		})
		if winnerUid != "" {
			// 个人获胜
			plr := this.world.GetTempPlayer(winnerUid)
			if plr != nil {
				this.GameOver(1, plr.Uid, plr.Nickname, int32(len(plr.OwnCells)), [][]string{{plr.Uid, plr.Nickname}}, []int32{cityId, maxLv})
			}
		} else if winnerAlliUid != "" {
			// 联盟获胜
			if alli := this.world.GetAlliance(winnerAlliUid); alli != nil {
				count, _, _, _ := this.world.GetAlliRankInfo(alli)
				this.GameOver(2, alli.Uid, alli.Name, count, alli.GetMemberUidAndNames(), []int32{cityId, maxLv})
			}
		} else if winnerUid == "" && winnerAlliUid == "" {
			// 没人占领古城 则根据领地数获胜
			this.GameOverByCellCount()
		}
	} else if this.winCond[0] == 1 { // 领地获胜
		this.GameOverByCellCount()
	} else if this.winCond[0] == 3 { // 血战到底
		alliRkList := this.world.GetAlliRankList()
		// 从最后一名的联盟开始结算
		for i := len(alliRkList) - 1; i >= 0; i-- {
			alli := this.world.GetAlliance(alliRkList[i].Uid)
			this.world.ConquerAlliSettle(alli, false, i == 0)
		}
		winAlli := this.world.GetAlliance(alliRkList[0].Uid)
		this.GameOver(2, winAlli.Uid, winAlli.Name, alliRkList[0].LandCount, winAlli.GetMemberUidAndNames(), nil)
	}
	return true
}

// 根据领地数获胜
func (this *Model) GameOverByCellCount() {
	// 获取当前领地数最高的
	playerTop, alllTop := this.world.GetPlayerOrAlliTopWin()
	// 判断 联盟和玩家 谁最高
	if alllTop != nil && playerTop != nil && alllTop.LandCount > 0 && alllTop.LandCount > playerTop.LandCount {
		if alli := this.world.GetAlliance(alllTop.Uid); alli != nil {
			this.GameOver(2, alli.Uid, alli.Name, int32(alllTop.LandCount), alli.GetMemberUidAndNames(), nil)
		}
	} else if playerTop != nil {
		this.GameOver(1, playerTop.Uid, playerTop.Nickname, int32(playerTop.LandCount), [][]string{{playerTop.Uid, playerTop.Nickname}}, nil)
	} else {
		this.GameOver(0, "", "", 0, [][]string{}, nil)
	}
}

// 检测后期第一次进入冬季的时候
func (this *Model) CheckLateGameAlliWin() {
	if this.IsGameOver() || len(this.winCond) < 3 {
		return
	} else if this.winCond[2] == -1 {
		return //跳过检测
	} else if this.winCond[0] == 1 { //领地争夺：只剩一个联盟获胜
		alliCount := len(this.world.Alliances.Map)
		if alliCount == 1 {
			// 只剩一个联盟 则直接胜利
			_, alliTop := this.world.GetPlayerOrAlliTopWin()
			if alli := this.world.GetAlliance(alliTop.Uid); alli != nil {
				this.GameOver(2, alli.Uid, alli.Name, int32(alliTop.LandCount), alli.GetMemberUidAndNames(), nil)
			}
		} else if alliCount == 0 {
			// 没有联盟 则第一名玩家胜利
			playerTop, _ := this.world.GetPlayerOrAlliTopWin()
			if playerTop != nil {
				this.GameOver(1, playerTop.Uid, playerTop.Nickname, int32(playerTop.LandCount), [][]string{{playerTop.Uid, playerTop.Nickname}}, nil)
			}
		}
	} else if this.winCond[0] == 2 { //修建遗迹：只剩一个势力占领古城获胜
		this.world.AncientGameoverCheck()
	}
}

// 检测游戏是否结束 每日凌晨检测 仅领地争夺规则下
func (this *Model) CheckGameOverByToDay(now int64) {
	if this.IsGameOver() || len(this.winCond) < 3 || this.winCond[0] != 1 {
		return
	}
	landCountConf := int32(this.winCond[1])
	// 获取当前领地数最高的
	playerTop, alliTop := this.world.GetPlayerOrAlliTopWin()
	// 判断 联盟和玩家 谁先30000
	if alliTop != nil && alliTop.LandCount >= landCountConf {
		if alli := this.world.GetAlliance(alliTop.Uid); alli != nil {
			this.GameOver(2, alli.Uid, alli.Name, alliTop.LandCount, alli.GetMemberUidAndNames(), nil)
		}
	} else if playerTop != nil && playerTop.LandCount >= landCountConf {
		this.GameOver(1, playerTop.Uid, playerTop.Nickname, playerTop.LandCount, [][]string{{playerTop.Uid, playerTop.Nickname}}, nil)
	}
}

func (this *Model) ToGameOverInfoPb() *pb.GameOverInfo {
	if this.gameOverInfo == nil {
		return nil
	}
	return &pb.GameOverInfo{
		WinType:      pb.Int32(this.gameOverInfo.WinType),
		WinUid:       this.gameOverInfo.WinUID,
		WinName:      this.gameOverInfo.WinName,
		WinCellCount: pb.Int32(this.gameOverInfo.WinCellCount),
		CloseTime:    pb.Int64(this.GetSurplusCloseTime()),
		RunTime:      pb.Int64(this.gameOverInfo.EndTime - this.createTime),
		AncientInfo:  this.gameOverInfo.AncientInfo,
	}
}

// 检测前端版本
func (this *Model) CheckClientVersion(version string) string {
	arr := strings.Split(this.clientVersion, "|")
	minVersion := arr[0]
	maxVersion := minVersion
	if len(arr) == 2 {
		maxVersion = arr[1]
	}
	if !ut.CheckVersion(version, minVersion) {
		return ecode.VERSION_TOOLOW.String()
	} else if !ut.CheckVersion(maxVersion, version) {
		return ecode.VERSION_TOOTALL.String() //大于服务器版本 不能进入
	}
	return ""
}

// 获取可以创建主城的区域数量
func (this *Model) GetCanCreateAreaCount() []int32 {
	this.createPlayerMutex.Lock()
	defer this.createPlayerMutex.Unlock()
	arr := []int32{}
	for _, m := range this.mainCitys {
		arr = append(arr, int32(ut.Max(0, len(m)-constant.MAIN_CITY_PERS_MIN_COUNT)))
	}
	return arr
}

// 获取在线玩家数量
func (this *Model) GetOnlinePlayerCount() int {
	this.players.RLock()
	defer this.players.RUnlock()
	count := 0
	for _, player := range this.players.Map {
		if player.IsOnline() {
			count += 1
		}
	}
	return count
}

// 获取在线玩家
func (this *Model) GetPlayer(uid string) g.Player {
	if uid == "" {
		return nil
	} else if plr := this.players.Get(uid); plr != nil && plr.IsOnline() {
		return plr
	}
	return nil
}

func (this *Model) HasPlayer(uid string) bool {
	return this.players.Get(uid) != nil
}

// 获取玩家是否在线
func (this *Model) IsPlayerOnline(uid string) bool {
	if plr := this.players.Get(uid); plr != nil && plr.IsOnline() {
		return true
	}
	return false
}

// 从数据库获取玩家 会先从内存中获取
func (this *Model) LoginGetPlayerByDB(uid string) *player.Model {
	plr := this.players.Get(uid) //先从内存中取
	if plr != nil {
		plr.LoginMutex.Lock()
		defer plr.LoginMutex.Unlock()
		if plr.State == constant.PS_NONE {
			plr.Init()
			log.Info("x entry game uid=" + uid + ", init")
		} else if plr.IsOnline() { //如果在线需要踢他下线
			plr.Kick(0)
			this.CalSumOnlineTime(plr, time.Now().UnixMilli())
			log.Info("x entry game uid=" + uid + ", kick")
		} else if plr.State == constant.PS_OFFLINE {
			plr.State = constant.PS_ONLINE
			log.Info("x entry game uid=" + uid + ", offline")
		} else {
			// plr.Init()
			log.Info("x entry game uid=" + uid + ", ???")
		}
	} else if plr = this.pMgr.FindByUid(this, uid); plr != nil { //再从数据库取
		// this.players.Set(uid, plr.Init())
		plr.Init()
		log.Info("x entry game uid=" + uid + ", db")
	}
	if this.players.Get(uid) == nil && plr != nil {
		this.players.Set(uid, plr)
	}
	return plr
}

// 获取在线玩家 如果没有就从数据库获取
func (this *Model) GetOnlinePlayerOrDB(uid string) g.Player {
	if uid == "" {
		return nil
	}
	plr := this.players.Get(uid)
	if plr != nil {
		return plr
	} else if plr = this.pMgr.FindByUid(this, uid); plr != nil {
		now := time.Now().UnixMilli()
		// 刷新是否过天
		plr.CheckUpdateNextToDayTime(now, true)
		// 刷新体力
		plr.CheckUpdateStamina(now, true)
		// 刷新一下建筑信息
		plr.UpdateBuildEffect()
		this.players.Set(uid, plr)
	}
	return plr
}

// 创建玩家
func (this *Model) CreatePlayer(uid, nickname, headIcon, personalDesc, os, osVersion, platform string, registerTime int64, title int32, pawnSkins []int32, pos int32,
	lang, fcmToken string, offlineNotifyOtp []int32, distinctId string, online bool, mainCitySkin, treasureLostCount int32, antiCheatData []int32, farmType int32,
	isInit bool) (*player.Model, string) {
	this.createPlayerMutex.Lock()
	defer this.createPlayerMutex.Unlock()
	// 随机一个位置
	index := this.RandomMainCityIndex(pos, isInit)
	if index == -1 {
		log.Info("CreatePlayer index == -1, pos: %v", pos)
		return nil, ecode.NOT_CITY_INDEX.String() //这里没有位置的话 需要玩家重新选择
	}
	// 给未选择开荒方式的玩家设置开荒方式
	if farmType == constant.FARM_TYPE_NONE {
		farmType = this.farmType
	}
	plr := player.InitModel(this, uid, nickname, headIcon, personalDesc, antiCheatData)
	// 记录玩家的主城地块
	plr.MainCityIndex = index
	data := plr.ToDB(nickname, headIcon, personalDesc, title)
	data.Language = lang
	data.FCMToken = fcmToken
	data.ConfigPawnMap = wrapConfigPawnMap(pawnSkins)
	data.OfflineNotifyOtp = offlineNotifyOtp
	data.TreasureLostCount = treasureLostCount
	data.FarmType = farmType
	// 在数据库创建
	if err := this.pMgr.InsertOne(data); err != "" {
		log.Error("CreatePlayer InsertOne uid: %v, error: %v", uid, err)
		// 归还主城位置
		this.AddMainCityIndex(index)
		return nil, ecode.DB_ERROR.String()
	}
	// 创建临时玩家
	this.world.CreateTempPlayerLock(data)
	// 默认这4个地方都被占领
	this.world.CreateCellByMainCity(index, plr, lang, mainCitySkin)
	if online {
		this.players.Set(uid, plr.Init())
	}
	// 是否之前被回收 发送邮件
	recycleInfo := this.RemoveDeletePlayerInfo(uid)
	if recycleInfo != nil && recycleInfo.CellCount > 4 {
		resCount := int32(ut.If(recycleInfo.CellCount > 5, 1000, 500))
		this.SendMailItemOne(100007, "", "", "-1", uid, []*g.TypeObj{
			{Type: ctype.CEREAL, Count: resCount},
			{Type: ctype.TIMBER, Count: resCount},
			{Type: ctype.STONE, Count: resCount},
		})
	}
	this.world.RecordPlayerDistinctId(plr.Uid, distinctId)
	// 上报
	this.world.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_createPlayer", map[string]interface{}{
		"create_pos":        pos,
		"create_recycle":    ut.If(recycleInfo == nil, 0, 1),
		"os":                os,
		"platform":          platform,
		"language":          lang,
		"open_server_time":  this.GetCreateTime(),
		"create_player_num": 0,
	})
	//
	this.world.TaUserSet(plr.Uid, plr.ReCreateMainCityCount, map[string]interface{}{
		"uid":                plr.Uid,
		"sid":                this.Id,
		"uid_register_time":  registerTime,
		"create_player_time": plr.CreateTime,
		"create_player_num":  plr.ReCreateMainCityCount,
		"os":                 os,
		"os_version":         osVersion,
		"platform":           platform,
	})
	log.Info("create player uid: %v, name: %v, pos: %v, recycle: %v", plr.GetUID(), nickname, pos, ut.If(recycleInfo == nil, "false", "true"))
	return plr, ""
}
func wrapConfigPawnMap(pawnSkins []int32) map[int32]*g.PawnConfigInfo {
	ret := map[int32]*g.PawnConfigInfo{}
	for _, id := range pawnSkins {
		pawnId := id / 1000
		ret[pawnId] = &g.PawnConfigInfo{SkinId: id}
	}
	return ret
}

func (this *Model) RandomMainCityIndex(pos int32, isInit bool) int32 {
	if pos < 0 && slg.IsDebug() {
		return ut.AbsInt32(pos) //测试模式
	} else if pos < 0 {
		pos = 0
	} else if pos >= int32(len(this.mainCitys)) {
		pos = int32(len(this.mainCitys) - 1)
	}
	defer func() {
		this.UpdateDataToDB(bson.M{"main_citys": this.mainCitys})
		// this.SaveDB()
	}()
	indexs := this.mainCitys[pos]
	if !isInit {
		//非开服创建 需要检测每个点是否已经不存在了
		indexs = this.world.FilterMainCityIndexs(this.mainCitys[pos])
	}
	// 保存到数据库
	cnt := len(indexs)
	if cnt == 0 {
		this.mainCitys[pos] = indexs
		return -1
	}
	i := ut.Random(0, cnt-1)
	index := indexs[i]
	this.mainCitys[pos] = append(indexs[:i], indexs[i+1:]...)
	return index
}

// 添加主城位置
func (this *Model) AddMainCityIndex(index int32) {
	point := helper.IndexToPoint(index, this.mapSize)
	x := point.X / (this.mapSize.Y / 2)
	y := point.Y / (this.mapSize.X / 2)
	pos := y*2 + x
	if pos < 0 || pos >= int32(len(this.mainCitys)) {
		return
	} else if citys := this.mainCitys[pos]; !array.Has(citys, index) {
		this.mainCitys[pos] = append(citys, index)
		this.UpdateDataToDB(bson.M{"main_citys": this.mainCitys}) //保存到数据库
		log.Info("AddMainCityIndex sid=" + ut.Itoa(this.Id) + ", pos=" + ut.Itoa(pos) + ", index=" + ut.Itoa(index))
	}
}

// 重新随机一个主城位置
func (this *Model) ReRandomMainCityIndex() int32 {
	var count, pos int32
	for i, arr := range this.mainCitys {
		cnt := int32(len(arr))
		if cnt > count {
			count = cnt
			pos = int32(i)
		}
	}
	if count == 0 {
		return -1
	}
	return this.RandomMainCityIndex(pos, false)
}

// 记录被删除的玩家信息
func (this *Model) RecordDeletePlayer(uid string, index, cellCount int32, offlineTime int64) {
	this.deletePlayerMutex.Lock()
	this.deletePlayers[uid] = &DeletePlayerInfo{
		UID:           uid,
		MainCityIndex: index,
		CellCount:     cellCount,
		OfflineTime:   offlineTime,
		Time:          time.Now().UnixMilli(),
	}
	this.deletePlayerMutex.Unlock()
	// 保存到数据库
	this.UpdateDataToDB(bson.M{"delete_players": this.ToDeletePlayersDB()})
}

// 删除被记录的玩家
func (this *Model) RemoveDeletePlayerInfo(uid string) *DeletePlayerInfo {
	this.deletePlayerMutex.Lock()
	info := this.deletePlayers[uid]
	delete(this.deletePlayers, uid)
	this.deletePlayerMutex.Unlock()
	// 保存到数据库
	this.UpdateDataToDB(bson.M{"delete_players": this.ToDeletePlayersDB()})
	return info
}

func (this *Model) ToDeletePlayersDB() map[string]*DeletePlayerInfo {
	data := map[string]*DeletePlayerInfo{}
	this.deletePlayerMutex.RLock()
	for k, v := range this.deletePlayers {
		data[k] = v
	}
	this.deletePlayerMutex.RUnlock()
	return data
}

// 删除玩家
func (this *Model) RemovePlayer(uid string) {
	this.players.Lock()
	if plr := this.players.Map[uid]; plr != nil {
		plr.StopTick()
		delete(this.players.Map, uid)
		log.Info("RemovePlayer uid=" + uid + ", name=" + plr.Nickname)
	}
	this.players.Unlock()
}

// 删除玩家 从数据库
func (this *Model) DeletePlayer(uid string) {
	this.RemovePlayer(uid)
	if this.pMgr.DeleteOne(uid) == "" {
	}
}

// 玩家离开
func (this *Model) PlayerLeave(plr *player.Model, close int) {
	if plr.IsSpectator() {
		this.spectatePlayerLeave(plr, close)
		return
	}
	plr.LoginMutex.Lock()
	now := time.Now().UnixMilli()
	this.CalSumOnlineTime(plr, now)
	data := plr.ToDB(this.world.GetPlayerDataToDB(plr.Uid))
	// 关闭
	plr.StopTick()
	plr.Close(close)
	// 刷新玩家状态
	this.world.UpdatePlayerOnlineState(plr.Uid, now)
	// 历史最大地块数
	maxLandCount := this.world.GetPlayerMaxLandCount(plr.Uid)
	// 上报
	if now-data.LastTaTrackTime >= ta.USER_OFFLINE_TRACK_INTERVAL {
		data.LastTaTrackTime = now
		// 历史最大对局次数
		gameCount, rankScore, gold, accTotalGold := 0, 0, 0, 0
		// 更新用户的最大地块数
		if ret, _ := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_OFFLINE_REPORT_INFO, plr.Uid)); ret != nil {
			gameCount = ut.Int(ret["gameCount"])
			rankScore = ut.Int(ret["rankScore"])
			gold = ut.Int(ret["gold"])
			accTotalGold = ut.Int(ret["accTotalGold"])
		}
		roles := this.world.GetPlayerPawnTrackInfo(plr.Uid)
		equips := this.world.GetPlayerEquips(plr.Uid)
		_, _, landScoreTop, alliScoreTop := this.world.GetPlayerLandScoreAndAlliScore(plr.Uid)
		forgeEquips := []map[string]interface{}{}
		for _, equip := range equips {
			forgeEquips = append(forgeEquips, map[string]interface{}{
				"equip_id":         equip.ID,
				"forge_resetCount": equip.RecastCount,
			})
		}
		if len(forgeEquips) == 0 {
			forgeEquips = append(forgeEquips, map[string]interface{}{})
		}
		ta.UserSet(this.Id, plr.Uid, this.GetWorld().GetPlayerDistinctId(plr.Uid), plr.ReCreateMainCityCount, map[string]interface{}{
			"version":           slg.CLIENT_VERSION,
			"cereal":            data.Cereal,
			"timber":            data.Timber,
			"stone":             data.Stone,
			"gold":              gold,
			"accTotalGold":      accTotalGold,
			"expBook":           data.ExpBook,
			"iron":              data.Iron,
			"pawnCount":         len(roles),
			"landCount":         len(this.world.GetPlayerOwnCells(plr.Uid)),
			"cerealOp":          plr.Cereal.GetOpHour(),
			"timberOp":          plr.Timber.GetOpHour(),
			"stoneOp":           plr.Stone.GetOpHour(),
			"maxLandCount":      maxLandCount,
			"maincityLevel":     this.world.GetPlayerMainBuildLv(plr.Uid),
			"accTotalCereal":    data.AccTotalCereal,
			"accTotalTimber":    data.AccTotalTimber,
			"accTotalStone":     data.AccTotalStone,
			"accTotalExpBook":   data.AccTotalExpBook,
			"accTotalIron":      data.AccTotalIron,
			"accTotalPawnCount": this.world.GetPlayerAccTotalPawnCount(plr.Uid),
			"roles":             roles,
			"builds":            this.world.GetPlayerBuildTrackInfo(plr.Uid),
			"forgeEquips":       forgeEquips,
			"gameCount":         gameCount,
			"rank_score":        rankScore,
			"alli_score":        alliScoreTop,
			"land_score":        landScoreTop,
			"is_over":           this.IsGameOver(),
		})
	}
	// 保存数据库
	this.world.SetPlayerNeedUpdateDB(plr.Uid, false)
	if e := this.pMgr.UpdateOne(plr.Uid, data); e != "" {
		log.Error("leave game close=" + ut.Itoa(close) + ", uid=" + plr.Uid + ", err=" + e)
	} else {
		log.Info("leave game close=" + ut.Itoa(close) + ", uid=" + plr.Uid + ", name=" + plr.Nickname)
		this.RemovePlayer(plr.Uid) //删除内存中的玩家
	}
	// 从请求区域的map中删除
	this.world.CleanAreaRecordReqPlayer(plr.Uid)
	this.world.RemoveAreaWatchPlayer(plr.Uid)
	// 添加资源离线检测 3表示放弃 所以不添加通知了
	if close != 3 {
		plr.OutputOfflineCheck()
	}
	plr.LoginMutex.Unlock()
}

// 观战玩家离开
func (this *Model) spectatePlayerLeave(plr *player.Model, close int) {
	plr.LoginMutex.Lock()
	// 关闭
	plr.StopTick()
	plr.Close(close)
	uid := plr.Uid
	// 从请求区域的map中删除
	this.world.CleanAreaRecordReqPlayer(uid)
	this.world.RemoveAreaWatchPlayer(uid)
	plr.LoginMutex.Unlock()
	this.players.Del(uid)
	this.world.DelWorldPbTempPlayer(uid)

}

func (this *Model) GetPlayerGold(plr *player.Model) (int, int) {
	data, err := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_GOLD, plr.Uid))
	if err != "" {
		log.Error("login GetPlayerGold Error", err)
		return 0, 0
	}
	return ut.Int(data["gold"]), ut.Int(data["accTotalGold"])
}

// 保存玩家信息到数据
func (this *Model) UpdatePlayerDB(p g.Player) {
	plr, ok := p.(*player.Model)
	if !ok || plr == nil {
		return
	}
	this.world.SetPlayerNeedUpdateDB(plr.Uid, false)
	if e := this.pMgr.UpdateOne(plr.Uid, plr.ToDB(this.world.GetPlayerDataToDB(plr.Uid))); e != "" {
		log.Error("UpdatePlayerDB err: %v", e)
	} else if plr.State == constant.PS_NONE { //如果是还没有初始化的 就删除
		log.Info("UpdatePlayerDB RemovePlayer uid: %v", plr.Uid)
		this.RemovePlayer(plr.Uid)
	} else {
		log.Info("UpdatePlayerDB uid: %v, name: %v", plr.Uid, plr.Nickname)
	}
}

// 删除没在线的player 但是不需要保存数据库
func (this *Model) CheckRemoveOfflinePlayer(p g.Player) {
	plr, ok := p.(*player.Model)
	if !ok || plr == nil {
		return
	} else if !plr.IsOnline() {
		log.Info("CheckRemoveOfflinePlayer RemovePlayer uid: %v", plr.Uid)
		this.RemovePlayer(plr.Uid)
	}
}

func (this *Model) NotifyAll(topic string, msg *pb.GAME_ONNOTICE_NOTIFY, ignores ...string) {
	if !this.isRunning || this.IsClose() {
		return
	}
	body, _ := pb.ProtoMarshal(msg)
	this.NotifyAllByBytes(topic, body, ignores...)
}

func (this *Model) NotifyAllByBytes(topic string, body []byte, ignores ...string) {
	if !this.isRunning || this.IsClose() {
		return
	}
	this.players.RLock()
	defer this.players.RUnlock()
	for _, plr := range this.players.Map {
		if !array.Has(ignores, plr.GetUID()) {
			plr.SessionSendNR(topic, body)
		}
	}
}

func (this *Model) NotifyPlayers(topic string, msg []byte, uids []string) {
	if !this.isRunning || this.IsClose() {
		return
	}
	this.players.RLock()
	defer this.players.RUnlock()
	for _, uid := range uids {
		if plr := this.players.Map[uid]; plr != nil {
			plr.SessionSendNR(topic, msg)
		}
	}
}

func (this *Model) runNotifyAllPlayer() {
	defer close(this.notifyAllPlayerQueue)
	for this.isRunning {
		if data, ok := <-this.notifyAllPlayerQueue; ok {
			this.NotifyPlayers(data.topic, data.msg, data.uids)
		}
	}
}

func (this *Model) PutNotifyAllPlayersQueue(topic string, msg []byte, uids []string) {
	if msg != nil && this.isRunning && !this.IsClose() && len(uids) > 0 {
		this.notifyAllPlayerQueue <- NotifyAllPlayerInfo{
			topic: topic,
			msg:   msg,
			uids:  uids,
		}
	}
}

func (this *Model) PutNotifyAllPlayersQueuePb(topic string, msg protoreflect.ProtoMessage, uids []string) {
	if msg != nil && this.isRunning && !this.IsClose() && len(uids) > 0 {
		bytes, err := pb.ProtoMarshal(msg)
		if err != "" {
			return
		}
		this.notifyAllPlayerQueue <- NotifyAllPlayerInfo{
			topic: topic,
			msg:   bytes,
			uids:  uids,
		}
	}
}

// 添加到所有玩家的通知队列
func (this *Model) PutNotifyQueueAllPlayer(t int, data *pb.OnUpdatePlayerInfoNotify, ignores ...string) {
	if !this.isRunning || this.IsClose() {
		return
	}
	this.players.RLock()
	defer this.players.RUnlock()
	for _, plr := range this.players.Map {
		if !array.Has(ignores, plr.GetUID()) {
			plr.PutNotifyQueue(t, data)
		}
	}
}

func (this *Model) runPlayerNotify() {
	defer close(this.playerNotifyQueue)
	for this.isRunning {
		if data, ok := <-this.playerNotifyQueue; ok {
			if plr := this.GetPlayer(data.uid); plr != nil {
				plr.PutNotifyQueue(int(data.msg.Type), data.msg) //通知玩家
			}
		}
	}
}

func (this *Model) PutPlayerNotifyQueue(nType int, uid string, msg *pb.OnUpdatePlayerInfoNotify) {
	if this.isRunning && !this.IsClose() && uid != "" {
		msg.Type = int32(nType)
		this.playerNotifyQueue <- PlayerNotifyInfo{
			uid: uid,
			msg: msg,
		}
	}
}

func (this *Model) runPlayerUpdateOpSec() {
	defer close(this.playerUpdateOpSec)
	for this.isRunning {
		if uid, ok := <-this.playerUpdateOpSec; ok {
			if plr := this.GetPlayer(uid); plr != nil && plr.IsOnline() {
				plr.UpdateOpSec(true)
			}
		}
	}
}

// 通知玩家刷新产出
func (this *Model) PutPlayerUpdateOpSec(uid string) {
	if this.isRunning && !this.IsClose() && uid != "" {
		this.playerUpdateOpSec <- uid
	}
}

// 通知所有玩家刷新产出
func (this *Model) NotifyAllPlayerUpdateOpSec() {
	list := []*player.Model{}
	this.players.RLock()
	for _, m := range this.players.Map {
		if m.IsOnline() {
			list = append(list, m)
		}
	}
	this.players.RUnlock()
	for _, m := range list {
		m.UpdateOpSec(true)
	}
}

// 添加通知玩家是否有宝箱
func (this *Model) PutNotifyPlayerHasTreasure(uid string) {
	if this.isRunning && !this.IsClose() && uid != "" {
		this.playerHasTreasure <- uid
	}
}

// 通知聊天信息
func (this *Model) NotifyChat(chat *world.ChatInfo) {
	if !this.isRunning || this.IsClose() {
		return
	}
	topic := "game/OnChat"
	body, _ := pb.ProtoMarshal(&pb.GAME_ONCHAT_NOTIFY{
		Data: chat.ToPb(),
	})
	channelArr := strings.Split(chat.Channel, "_")
	// tp, AlliUID, PUID := channelArr[0], "", ""
	tp := channelArr[0]
	if (tp == "1" && len(channelArr) == 2) ||
		(tp == "4" && len(channelArr) == 3) { //联盟和联盟副频
		// AlliUID = channelArr[1]
		if alli := this.world.GetAlliance(channelArr[1]); alli != nil {
			var uids []string
			uids = alli.GetMemberUids()
			if tp == "4" {
				// 联盟副频
				alliChannelInfo := alli.GetChatChannel(channelArr[2])
				if alliChannelInfo == nil {
					return
				}
				if alliChannelInfo.MemberFilter {
					uids = alliChannelInfo.MemberUids
				}
			}
			for _, uid := range uids {
				if plr := this.players.Get(uid); plr != nil {
					plr.SessionSendNR(topic, body)
				}
			}
		}
	} else if tp == "2" && len(channelArr) == 4 { //私聊
		// PUID = channelArr[1] + channelArr[2]
		for i := 1; i <= 2; i++ {
			if plr := this.players.Get(channelArr[i]); plr != nil {
				plr.SessionSendNR(topic, body)
			}
		}
	} else {
		this.players.RLock()
		for _, plr := range this.players.Map {
			plr.SessionSendNR(topic, body)
		}
		this.players.RUnlock()
	}
	// for _, plr := range this.players.Map {
	// 	if tp == "0" || tp == "3" { //世界和系统
	// 		plr.SessionSendNR(topic, body)
	// 	} else if tp == "1" && AlliUID != "" && AlliUID == plr.AllianceUid { //联盟
	// 		plr.SessionSendNR(topic, body)
	// 	} else if tp == "2" && PUID != "" && strings.Contains(PUID, plr.Uid) { //私聊
	// 		plr.SessionSendNR(topic, body)
	// 	}
	// }
}

// rpc调用
func (this *Model) Invoke(moduleType string, _func string, params ...interface{}) (result interface{}, err string) {
	if serverModule != nil {
		return serverModule.InvokeWithCleanup(moduleType, _func, params...)
	}
	return
}

// rpc调用
func (this *Model) InvokeNR(moduleType string, _func string, params ...interface{}) {
	if serverModule != nil {
		serverModule.InvokeNR(moduleType, _func, params...)
	}
}

// 发送Rpc到lobby
func (this *Model) InvokeLobbyRpc(id, _func string, params ...interface{}) (result interface{}, err string) {
	if serverModule != nil && id != "" {
		serverType := slg.MACH_SERVER_TYPE_LOBBY
		paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
		return serverModule.InvokeWithCleanup(serverType+"@"+serverType+id, _func, paramsBytesArr...)
	}
	return
}

// 发送Rpc到lobby
func (this *Model) InvokeLobbyRpcNR(id, _func string, params ...interface{}) {
	if serverModule != nil && id != "" {
		serverType := slg.MACH_SERVER_TYPE_LOBBY
		paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
		serverModule.InvokeNR(serverType+"@"+serverType+id, _func, paramsBytesArr...)
	}
}

// 发送Rpc到指定队伍的大厅服
func (this *Model) InvokeTeamFunc(uid string, _func string, params ...interface{}) (result interface{}, err string) {
	lid := rds.GetUserLid(uid)
	if lid != "" {
		result, err = this.InvokeLobbyRpc(lid, _func, params...)
	} else {
		log.Warning("InvokeTeamFunc lobby full uid: %v, _func: %v, params: %v", uid, _func, params)
	}
	return
}

// 发送Rpc到指定队伍的大厅服 不需要回复
func (this *Model) InvokeTeamFuncNR(uid string, _func string, params ...interface{}) {
	lid := rds.GetUserLid(uid)
	if lid != "" {
		this.InvokeLobbyRpcNR(lid, _func, params...)
	} else {
		log.Warning("InvokeTeamFuncNR lobby full uid: %v, _func: %v, params: %v", uid, _func, params)
	}
}

// 发送Rpc到邮件服
func (this *Model) InvokeMailRpc(_func string, params ...interface{}) (result interface{}, err string) {
	paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
	return this.Invoke(slg.MACH_SERVER_TYPE_MAIL, _func, paramsBytesArr...)
}

// 发送Rpc到邮件服
func (this *Model) InvokeMailRpcNR(_func string, params ...interface{}) {
	paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
	this.InvokeNR(slg.MACH_SERVER_TYPE_MAIL, _func, paramsBytesArr...)
}

// GetPlayerManager 当前房间的玩家管理器
func (this *Model) GetPlayerManager() *player.Manager {
	return this.pMgr
}

// 获取随机主城区域
func (this *Model) GetRandMainCityPos() int32 {
	arr := []int32{}
	for pos, v := range this.mainCitys {
		if len(v) > 0 {
			arr = append(arr, int32(pos))
		}
	}
	if len(arr) == 0 {
		return -1
	}
	return int32(rand.Intn(len(arr)))
}

// 关闭已经结束的游戏服 废弃 目前通过登陆服执行脚本关闭
func (this *Model) CloseGameOverRoom() {
	// log.Info("CloseGameOverRoom")
	// if !this.IsClose() {
	// 	return
	// }
	// this.Stop(1)
	// log.Info("CloseGameOverRoom start cmd")
	// cmd := exec.Command("./close_game_server.sh", ut.String(this.Id))
	// // 执行命令
	// err := cmd.Run()
	// if err != nil {
	// 	log.Error("CloseGameOverRoom err: %v", err)
	// }
	// log.Info("CloseGameOverRoom end cmd")
}

// 计算累计在线时间
func (this *Model) CalSumOnlineTime(plr *player.Model, now int64) {
	onlineTime := ut.MaxInt64(now-plr.LastLoginTime, 0)
	if plr.State != constant.PS_OFFLINE {
		if onlineTime > plr.TopOnlineTime {
			plr.TopOnlineTime = onlineTime
		}
		plr.LastOnlineTime = onlineTime
		plr.OfflineTime = now
		plr.SumOnlineTime += onlineTime //总在线时间
		// 触发任务
		plr.TriggerTask(tctype.LIVER_EMPEROR, int32(len(this.GetWorld().GetPlayerOwnCells(plr.Uid))), ut.Int32(plr.SumOnlineTime/ut.TIME_HOUR))
	}
}

// 发送邮件
func (this *Model) SendMailItemOne(contentId int, title, content, sender, receiver string, items []*g.TypeObj) {
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_ITEM_ONE, this.Id, contentId, title, content, sender, receiver, items)
}

// 发送邮件
func (this *Model) SendMailOne(contentId int, title, content, sender, receiver string) {
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_ITEM_ONE, this.Id, contentId, title, content, sender, receiver, nil)
}

// 发送邮件
func (this *Model) SendMailMany(contentId int, title, content, sender string, uids []string) {
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_MANY, this.Id, contentId, title, content, sender, uids, nil)
}

// 从内存中获取观战玩家
func (this *Model) GetSpectatePlayer(uid string) *player.Model {
	plr := this.players.Get(uid) //先从内存中取
	if plr != nil {
		plr.LoginMutex.Lock()
		defer plr.LoginMutex.Unlock()
		if plr.State == constant.PS_NONE {
			plr.Init()
			log.Info("x spectate game uid=" + uid + ", init")
		} else if plr.IsOnline() { //如果在线需要踢他下线
			plr.Kick(0)
			log.Info("x spectate game uid=" + uid + ", kick")
		} else if plr.State == constant.PS_OFFLINE {
			plr.State = constant.PS_ONLINE
			log.Info("x spectate game uid=" + uid + ", offline")
		} else {
			log.Info("x spectate game uid=" + uid + ", ???")
		}
	}
	return plr
}

// 创建观战玩家
func (this *Model) CreateSpectatePlayer(uid, nickname, headIcon, personalDesc, lang string, title, mainCity int32) (*player.Model, string) {
	this.players.RLock()
	spectatorNum := 0
	for _, plr := range this.players.Map {
		if plr.IsSpectator() {
			spectatorNum++
		}
	}
	this.players.RUnlock()
	if spectatorNum >= slg.SPECTATE_USER_NUM_MAX {
		return nil, ecode.SPECTATE_USER_LIMIT.String()
	}
	tplr := this.world.GetTempPlayer(uid)
	if tplr != nil && tplr.IsGiveupGame {
		return nil, ecode.YET_GIVE_GAME.String()
	}
	this.createPlayerMutex.Lock()
	defer this.createPlayerMutex.Unlock()
	plr := player.InitSpectatorModel(this, uid, nickname, headIcon, personalDesc)
	this.players.Set(uid, plr.Init())
	// 创建临时玩家
	if tplr == nil {
		this.world.CreateSpectateTempPlayer(uid, nickname, headIcon, personalDesc, lang, title, mainCity)
	}
	// this.world.RecordPlayerDistinctId(plr.Uid, distinctId)
	log.Info("create spectate player uid=" + plr.GetUID() + ", name=" + nickname)
	return plr, ""
}

// 创建房间分配主城区域
func CreateRoomMallocPlayerPos(userInfoMap map[string]interface{}) map[int][]string {
	userPosMap := map[int][]string{}       // 最终分配的位置玩家UID map
	expectUserPosMap := map[int][]string{} // 玩家期望的位置玩家UID map
	backupArr := []string{}                // 待分配位置玩家UID
	posAvgCount := len(userInfoMap)/4 + 1  // 每个方位的人数平均分配
	posCount := 4

	// 将玩家按期望位置分类
	for _, data := range userInfoMap {
		user := ut.MapInterface(data)
		uid := user["uid"].(string)
		pos := ut.Int(user["pos"])
		if pos >= 0 && pos < 4 {
			expectUserPosMap[pos] = append(expectUserPosMap[pos], uid)
		} else {
			backupArr = append(backupArr, uid)
		}
	}

	// 处理每个位置的玩家
	for pos, uids := range expectUserPosMap {
		if len(uids) > posAvgCount {
			// 期望分配该区域的玩家数量大于该区域人数 则随机分配 剩余玩家放入待分配数组
			rand.Shuffle(len(uids), func(i, j int) { uids[i], uids[j] = uids[j], uids[i] })
			userPosMap[pos] = append(userPosMap[pos], uids[:posAvgCount]...)
			backupArr = append(backupArr, uids[posAvgCount:]...)
		} else {
			userPosMap[pos] = append(userPosMap[pos], uids...)
		}
	}

	// 将备份数组中的玩家随机分配到未满的其他位置
	for _, uid := range backupArr {
		for pos := 0; pos < posCount; pos++ {
			if len(userPosMap[pos]) < posAvgCount {
				userPosMap[pos] = append(userPosMap[pos], uid)
				break
			}
		}
	}
	return userPosMap
}

// 获取当前版本
func (this *Model) GetCurVersion() string {
	return this.curVersion
}

// 当前区服的版本是否指定版本
func (this *Model) CheckRoomVersionLower(version string) bool {
	if this.curVersion == "" {
		return true
	}
	if this.curVersion == version {
		return false
	}
	curVerArr := strings.Split(this.curVersion, ".")
	verArr := strings.Split(version, ".")
	if len(curVerArr) < 3 || len(verArr) < 3 {
		return false
	}
	for i := 0; i < 3; i++ {
		if curVerArr[i] < verArr[i] {
			return true
		} else if curVerArr[i] > verArr[i] {
			return false
		}
	}
	return false
}

// 是否血战到底
func (this *Model) IsConquer() bool {
	if this.winCond == nil || len(this.winCond) == 0 {
		return false
	}
	return this.winCond[0] == 3
}
