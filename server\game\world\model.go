package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"
	"strings"
	"sync/atomic"
	"time"

	"github.com/emirpasic/gods/trees/redblacktree"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

type CellMap struct {
	deadlock.RWMutex
	Map map[int32]*Cell
}

type AreaMap struct {
	deadlock.RWMutex
	Map map[int32]*Area
}

type AllTempPlayerMap struct {
	deadlock.RWMutex
	Map map[string]*TempPlayer
}

type ReqAreaPlayerMap struct {
	deadlock.RWMutex
	Map map[int32][]string
}

type BattleDistMap struct {
	deadlock.RWMutex
	Map map[int32][]string
}

type PawnLvingCompleteInfo struct {
	index int
	auid  string
	puid  string
	lv    int
}

type PlayerCellMap struct {
	deadlock.RWMutex
	Map map[string][]int32
}

// 玩家离线消息
type PlayerOfflineMsgInfo struct {
	uid     string   //玩家uid
	params  []string //参数
	endTime int64    //结束时间
	msgType int32    //消息类型
}

// 世界模块
type Model struct {
	room   g.Room
	db     *Mongodb
	rank   *RankManager //排行榜管理
	season *SeasonModel //季节

	Lands            []int32                   //基础地面信息
	Cells            *CellMap                  //单元格信息列表 只有被占领的才有
	Areas            *AreaMap                  //所有战场信息 没有发生战斗也会记录
	Marchs           *MarchList                //行军列表
	Transits         *TransitList              //运送列表
	Alliances        *AllianceMap              //所有联盟列表
	AvoidWarAreaData *AvoidWarData             //免战区域数据
	AvoidWarAreas2   *ut.MapLock[int32, int64] //免战区域 (战斗超过3小时的免战)
	CitySkinMap      *ut.MapLock[int32, int32] //城市皮肤分布
	CellTondenData   *CellTondenData           //屯田中的地块数据

	BTCityQueues        *BTCityQueueList                 //修建
	DrillPawnQueues     *DrillPawnQueueMap               //训练士兵队列
	PawnLvingQueues     *PawnLvingQueueList              //士兵练级队列
	PawnLvingPawnUIDMap *ut.MapLock[string, string]      //士兵练级映射UID
	BattleDist          *BattleDistMap                   //当前发生战斗的地块
	Chats               *ChatMap                         //当前的聊天信息
	PawnCuringQueues    *PawnCuringQueueList             //士兵治疗队列
	ExclusiveEffectMap  map[int32][]int32                //全局专武属性map
	MonsterEquipMap     map[int32]map[string]interface{} //野怪装备map
	PawnCostMap         map[int32]int32                  //士兵消耗资源map
	PawnUseStasticMap   *ut.MapLock[int32, int32]        //士兵使用统计map

	allTempPlayers      *AllTempPlayerMap                         //所有玩家临时信息
	reqAreaPlayers      *ReqAreaPlayerMap                         //记录请求过区域的玩家
	watchPlayers        *ut.MapLock[int32, []*pb.WatchPlayerInfo] //观战玩家列表
	notifyQueue         chan *pb.OnUpdateWorldInfoNotify          //世界通知队列
	updateIndexs        chan int32                                //记录需要更新的地图单元
	battleSettleWg      *deadlock.WaitGroup
	playerOfflineMsgMap deadlock.Map //玩家离线消息检测map key=>uid:type:params value=>PlayerOfflineMsgInfo

	playerCellMap      *PlayerCellMap                   //玩家拥有地块map
	AncientCityMap     *ut.MapLock[int32, *AncientInfo] //古城map key=>坐标 val=>古城信息
	AncientCityAreaMap deadlock.Map                     //古城区域信息map(未被占领) key=>坐标 val=>战场信息
	TriggerTaskChan    chan *TriggerTaskInfo            //任务触发chan
	FailPlrMap         *ut.MapLock[string, bool]        //已出局玩家map
	WorldEventMap      *ut.MapLock[int32, string]       //世界事件map

	worldPb            *pb.WorldInfo
	worldPbCellsLock   *deadlock.RWMutex
	worldPbPlayersLock *deadlock.RWMutex
	occupyLock         *deadlock.RWMutex //攻占主城锁
	playerSettleLock   *deadlock.RWMutex //玩家结算锁

	lastUpdateCellTime   int64 //记录更新地块时间
	captureNum           int32 //已沦陷人数
	settlePlayerNum      int32 //已结算人数
	HasCreateAnciet      bool  //是否已生成古城
	AlliLeaderVoteFinish bool  //是否已结束盟主投票
}

func NewModel(room g.Room) *Model {
	sidStr := g.ServerIdToString(room.GetSID())
	return &Model{
		room:                room,
		db:                  &Mongodb{world: "world_" + sidStr, SID: room.GetSID(), chat: "chat_" + sidStr},
		Lands:               []int32{},
		Cells:               &CellMap{Map: map[int32]*Cell{}},
		Areas:               &AreaMap{Map: map[int32]*Area{}},
		Marchs:              &MarchList{Map: map[string]*March{}, ArmyUIDMap: map[string]*March{}, RbTree: &ut.RbTreeContainer[*March]{Tree: redblacktree.NewWithIntComparator()}},
		Transits:            &TransitList{List: []*Transit{}, RbTree: &ut.RbTreeContainer[*Transit]{Tree: redblacktree.NewWithIntComparator()}},
		Alliances:           &AllianceMap{Map: map[string]*Alliance{}},
		AvoidWarAreaData:    &AvoidWarData{Map: map[int32]int64{}, RbTree: &ut.RbTreeContainer[int32]{Tree: redblacktree.NewWithIntComparator()}},
		AvoidWarAreas2:      ut.NewMapLock[int32, int64](),
		CitySkinMap:         ut.NewMapLock[int32, int32](),
		CellTondenData:      &CellTondenData{Map: map[int32]*TondenInfo{}, RbTree: &ut.RbTreeContainer[int32]{Tree: redblacktree.NewWithIntComparator()}},
		BTCityQueues:        &BTCityQueueList{List: []*BTCityInfo{}},
		DrillPawnQueues:     &DrillPawnQueueMap{Map: map[int32]*AreaDrillPawnQueue{}, RbTree: &ut.RbTreeContainer[*DrillPawnInfo]{Tree: redblacktree.NewWithIntComparator()}},
		PawnLvingQueues:     &PawnLvingQueueList{Map: map[int32]*AarePawnLvingQueue{}, RbTree: &ut.RbTreeContainer[*AarePawnLvingQueue]{Tree: redblacktree.NewWithIntComparator()}},
		PawnLvingPawnUIDMap: ut.NewMapLock[string, string](),
		BattleDist:          &BattleDistMap{Map: map[int32][]string{}},
		Chats:               &ChatMap{Map: map[string][]*ChatInfo{}, DbMap: map[string]int{}, AddTimeMap: map[string]int64{}},
		allTempPlayers:      &AllTempPlayerMap{Map: map[string]*TempPlayer{}},
		reqAreaPlayers:      &ReqAreaPlayerMap{Map: map[int32][]string{}},
		watchPlayers:        ut.NewMapLock[int32, []*pb.WatchPlayerInfo](),
		notifyQueue:         make(chan *pb.OnUpdateWorldInfoNotify, 300000),
		updateIndexs:        make(chan int32, 300000),
		rank:                &RankManager{},
		worldPb:             &pb.WorldInfo{},
		worldPbCellsLock:    new(deadlock.RWMutex),
		worldPbPlayersLock:  new(deadlock.RWMutex),
		battleSettleWg:      new(deadlock.WaitGroup),
		occupyLock:          new(deadlock.RWMutex),
		playerSettleLock:    new(deadlock.RWMutex),
		playerCellMap:       &PlayerCellMap{Map: map[string][]int32{}},
		AncientCityMap:      ut.NewMapLock[int32, *AncientInfo](),
		TriggerTaskChan:     make(chan *TriggerTaskInfo, 1000),
		PawnCuringQueues:    &PawnCuringQueueList{Map: map[int32]*AarePawnCuringQueue{}, RbTree: &ut.RbTreeContainer[*AarePawnCuringQueue]{Tree: redblacktree.NewWithIntComparator()}},
		ExclusiveEffectMap:  map[int32][]int32{},
		MonsterEquipMap:     map[int32]map[string]interface{}{},
		FailPlrMap:          ut.NewMapLock[string, bool](),
		PawnCostMap:         map[int32]int32{},
		PawnUseStasticMap:   ut.NewMapLock[int32, int32](),
		WorldEventMap:       ut.NewMapLock[int32, string](),
	}
}

// 返回基础信息
func (this *Model) ToPb(isReconnect bool) *pb.WorldInfo {
	alliNameMap := this.GetAlliNameMap()
	this.allTempPlayers.RLock()
	for uid, ply := range this.allTempPlayers.Map {
		if plyPb := this.GetWorldPbPlayersByUid(uid); plyPb != nil {
			this.UpdateTempPlayerPb(ply, plyPb, alliNameMap[ply.AllianceUid])
		}
	}
	this.allTempPlayers.RUnlock()
	ret := &pb.WorldInfo{
		Cells:   this.GetWorldPbCells(),
		Players: this.worldPb.GetPlayers(),
		MapId:   this.worldPb.GetMapId(),
	}
	return ret
}

func (this *Model) Init(maps []int32, mapSize *ut.Vec2) *Model {
	size := int(mapSize.X * mapSize.Y)
	if len(maps) != size {
		log.Error("world.init error. len(maps) != size")
		return this
	}
	datas := []interface{}{}
	for i := 0; i < size; i++ {
		landId := maps[i]
		index := int32(i)
		this.Lands = append(this.Lands, landId)
		if this.CheckLandCanOccupy(index) { //只要不是障碍 就存入
			this.Areas.Map[index] = NewArea(index, this)
		}
		datas = append(datas, this.WrapOneData(index))
	}
	this.db.CreateWorld(datas)
	return this
}

func (this *Model) FromDB(players map[string]player.TableData, isCreate bool) *Model {
	datas, err := this.db.FindWorld()
	if err != "" {
		log.Error("world FromDB error", err)
		return this
	}
	this.InitTempPlayers(players)
	citys := []int32{}
	armyUidMap, mapLen := map[string]bool{}, len(datas)
	this.worldPb.Cells = map[string]*pb.PlayerCellBytesInfo{}
	this.worldPb.MapId = this.room.GetMapId()
	this.Lands = make([]int32, mapLen)
	areaInfoMap := map[int32]map[string]interface{}{}
	for _, data := range datas {
		index := data.Index
		this.Lands[index] = data.LandId
		var owner, cityId, isAcient = "", int32(0), false
		if len(data.CellInfo) > 0 { //地块基础信息
			cell := NewCellByDB(index, data.CellInfo)
			owner, cityId = cell.Owner, cell.CityId
			isAcient = cell.IsAncient()
			this.Cells.Map[index] = cell
			this.WorldPbAddCell(cell, true)
		}
		// 这里兼容一下如果地还在但是玩家已经不再了
		if owner != "" && !this.AddPlayerOwnCell(owner, index) {
			this.WorldPbRemoveCell(owner, index, isAcient)
			owner = ""
			cityId = 0
			delete(this.Cells.Map, index)
			this.TagUpdateDBByIndex(index)
		}
		if this.CheckLandCanOccupy(index) { //地块战场信息
			areaInfoMap[index] = data.AreaInfo
			area := NewArea(index, this).FromDB(owner, cityId, data.AreaInfo, armyUidMap)
			// 4.0建筑兼容
			this.AreaBuildFix(area)
			this.Areas.Map[index] = area
			if cityId == constant.MAIN_CITY_ID && area.Owner != "" {
				this.UpdatePlayerTowerLv(area)
			}
			// 兼容士兵的装备
			for _, army := range area.Armys.List {
				if army.Owner != "" {
					for _, pawn := range army.Pawns.List {
						if equip := this.GetPlayerEquip(army.Owner, pawn.Equip.ID); equip != nil {
							pawn.ChangeEquip(equip.UID, equip.Attrs, false)
						} else {
							pawn.ChangeEquip("", nil, false)
						}
					}
				}
			}
		}
		if cityId > 0 {
			citys = append(citys, index)
		}
	}
	// 玩家地块矩形划分
	for uid := range this.playerCellMap.Map {
		this.PlayerCellSplit(uid)
	}
	// 初始化城市皮肤
	if data, e := this.db.FindCitySkin(); e == "" {
		for _, cs := range data.List {
			this.CitySkinMap.Set(cs["index"], cs["id"])
		}
	}
	// 初始化免战
	if data, e := this.db.FindAvoidWar(); e == "" {
		for _, aw := range data.List {
			index, endTime := int32(aw["index"]), int64(aw["time"])
			this.AvoidWarAreaData.Map[index] = endTime
			this.AvoidWarAreaData.RbTree.AddElement(int(endTime), index)
		}
	}
	if data, e := this.db.FindAvoidWar2(); e == "" {
		for _, aw := range data.List {
			this.AvoidWarAreas2.Set(int32(aw["index"]), int64(aw["time"]))
		}
	}
	// 初始化屯田
	if data, e := this.db.FindCellTonden(); e == "" {
		for index, info := range data.Data {
			this.CellTondenData.Map[index] = info
			this.CellTondenData.RbTree.AddElement(int(info.EndTime), index)
			area, army := this.GetAreaAndArmy(index, info.Auid)
			if area != nil && army != nil {
				army.StartCellTonden() // 设置军队屯田状态
			}
		}
	}
	// 初始化古城信息
	if data, e := this.db.FindAncientInfo(); e == "" {
		for _, v := range data.List {
			info := &AncientInfo{}
			info.FromDb(v)
			this.AncientCityMap.Set(info.Index, info)
		}
	}
	if this.AncientCityMap.Count() >= 4 {
		this.HasCreateAnciet = true
	}
	// 初始化修建城市队列
	if data, e := this.db.FindBTCity(); e == "" {
		this.BTCityQueues.List = data.List
	}
	// 初始化招募士兵列表
	if data, e := this.db.FindDrillPawn(); e == "" {
		for _, m := range data.List {
			obj := this.DrillPawnQueues.Map[m.Index]
			if obj == nil {
				obj = NewAareDrillPawnQueue(m.Index)
				this.DrillPawnQueues.Map[m.Index] = obj
			}
			queue := obj.Map[m.BUid]
			if queue == nil {
				queue = []*DrillPawnInfo{}
				obj.Map[m.BUid] = queue
			}
			queue = append(queue, m)
			this.DrillPawnQueues.Map[m.Index].Map[m.BUid] = queue
			if m.StartTime > 0 {
				this.DrillPawnQueues.RbTree.AddElement(int(m.StartTime+int64(m.NeedTime)), m)
			}
		}
	}
	// 初始化士兵训练列表
	if data, e := this.db.FindPawnLving(); e == "" {
		for _, m := range data.List {
			obj := this.PawnLvingQueues.Map[m.Index]
			if obj == nil {
				obj = NewAarePawnLvingQueue(m.Index)
				this.PawnLvingQueues.Map[m.Index] = obj
			}
			queue := obj.List
			if queue == nil {
				queue = []*PawnLvingInfo{}
			}
			this.PawnLvingQueues.Map[m.Index].List = append(queue, m)
			this.PawnLvingPawnUIDMap.Set(m.PUID, m.UID)
		}
		for _, v := range this.PawnLvingQueues.Map {
			if len(v.List) > 0 {
				endTime := v.List[0].StartTime + int64(v.List[0].NeedTime)
				this.PawnLvingQueues.RbTree.AddElement(int(endTime), v)
			}
		}
	}
	// 初始化士兵治疗列表
	if data, e := this.db.FindPawnCuring(); e == "" {
		for _, m := range data.List {
			area := this.GetArea(m.Index)
			if area == nil || area.Owner == "" {
				continue
			}
			obj := this.PawnCuringQueues.Map[m.Index]
			if obj == nil {
				obj = NewAarePawnCuringQueue(m.Index)
				this.PawnCuringQueues.Map[m.Index] = obj
			}
			queue := obj.List
			if queue == nil {
				queue = []*PawnCuringInfo{}
			}
			injuryPawn := this.GetInjuryPawn(m.UID, area.Owner)
			if injuryPawn != nil && injuryPawn.Curing {
				this.PawnCuringQueues.Map[m.Index].List = append(queue, m)
			}
		}
		for _, v := range this.PawnCuringQueues.Map {
			if len(v.List) > 0 {
				endTime := v.List[0].StartTime + int64(v.List[0].NeedTime)
				this.PawnCuringQueues.RbTree.AddElement(int(endTime), v)
			}
		}
	}
	// 初始化行军
	if data, e := this.db.FindMarch(); e == "" {
		for _, march := range data.List {
			area := this.GetArea(march.ArmyIndex)
			if area == nil {
			} else if army := area.GetArmyByUid(march.ArmyUid); army != nil {
				march.armyName = army.Name
				this.Marchs.Map[march.Uid] = march
				this.Marchs.ArmyUIDMap[march.ArmyUid] = march
				this.Marchs.RbTree.AddElement(int(march.StartTime+int64(march.NeedTime)), march)
			} else {
				log.Error("找不到军队的行军 index=" + ut.Itoa(march.ArmyIndex) + ", armyUid=" + march.ArmyUid)
			}
		}
	}
	// 初始化运送
	if data, e := this.db.FindTransit(); e == "" {
		for _, transit := range data.List {
			this.Transits.List = append(this.Transits.List, transit)
			this.Transits.RbTree.AddElement(int(transit.StartTime+int64(transit.NeedTime)), transit)
		}
	}
	// 初始化聊天
	if data, e := this.db.FindChat(); e == "" {
		this.FromChatDB(data)
	}
	// 初始化联盟
	this.AlliLeaderVoteFinish = true
	if alliances, e := this.db.FindAlliance(); e == "" {
		for _, data := range alliances {
			alli := this.InitAlliancePlayerInfo(NewAlliance().FromDB(data))
			if this.room.IsConquer() {
				alli.MemberPersLimit = int32(len(alli.Members.List))
			}
			this.Alliances.Map[data.Uid] = alli
			if data.Creater == "" {
				this.AlliLeaderVoteFinish = false
			}
		}
	}
	// 初始化季节
	this.season = NewSeasonModel(this.room).Init(this.db)
	// 初始化战场
	for _, area := range this.Areas.Map {
		if len(area.Builds.List) > 0 || len(area.Armys.List) > 0 {
			this.FromAreaDB(area)
		}
		// 战斗信息 这里如果有战斗就用战斗里面的maxhp
		if !area.FromBattle(areaInfoMap[area.index]) {
			area.UpdateMaxHP()
		} else if area.index == constant.MAIN_CITY_ID && area.BattleTime > 0 {
			this.PausePawnLving(area.index, area.BattleTime)
		}
	}
	// 设置地块关联
	for _, idx := range citys {
		this.UpdateCellDepend(idx)
	}

	// 初始化全局专武属性
	if data, e := this.db.FindExclusiveEffect(); e == "" && data.EffectMap != nil {
		this.ExclusiveEffectMap = data.EffectMap
	}
	// 检测所有专武是否都已随机属性
	needUpdate := false
	configs := config.GetJson("equipBase")
	if configs.Datas != nil {
		for _, conf := range configs.Datas {
			if ut.String(conf["exclusive_pawn"]) != "" {
				id := ut.Int32(conf["id"])
				if len(this.ExclusiveEffectMap[id]) == 0 {
					// 没有随机属性 开始随机
					effectList := RandomWorldExclusiveEffects(configs.Datas, conf)
					this.ExclusiveEffectMap[id] = effectList
					needUpdate = true
				}
			}
		}
	}
	if needUpdate {
		// 保存到数据库
		this.db.UpdateExclusiveEffect(this.ExclusiveEffectMap)
	}
	// 获取已沦陷玩家数量
	if data, e := this.db.FindCaptureRecords(); e == "" {
		this.captureNum = data.Count
	}
	// 初始化全局野怪装备
	equipIdMap := map[int32][]int32{}
	if data, e := this.db.FindMonsterEquip(); e == "" && data.EquipIdMap != nil {
		equipIdMap = data.EquipIdMap
	}
	equipMonsterIds := GetCanEquipMonsterIds()
	if len(equipMonsterIds) > len(equipIdMap) {
		for _, v := range equipMonsterIds {
			if _, ok := this.MonsterEquipMap[v]; !ok {
				equipIds := RandomMonsterEquipId(v, configs.Datas)
				log.Info("equipIds: %v", equipIds[0])
				equipIdMap[v] = equipIds
			}
		}
		// 保存到数据库
		this.db.UpdateMonsterEquip(equipIdMap)
	}
	// 生成野怪装备数据
	this.GenMonsterEquips(equipIdMap)
	// 清理随机装备缓存
	ClearRandomEquipCache()

	// 初始化士兵消耗数据
	if data, e := this.db.FindPawnCosts(); e == "" && data.CostMap != nil && len(data.CostMap) > 0 {
		this.PawnCostMap = data.CostMap
	}
	// 初始化士兵使用统计
	if data, e := this.db.FindPawnUseStastics(); e == "" && data.UseMap != nil {
		this.PawnUseStasticMap.FromMap(data.UseMap)
	}
	// 初始化世界事件记录
	if data, e := this.db.FindWorldEvents(); e == "" && data.EventMap != nil {
		this.WorldEventMap.FromMap(data.EventMap)
	}

	// 4.0版本兼容政策
	this.TempPlyInfoFix(players)
	// 4.0版本兼容遗迹建筑坐标
	this.AncientBuildFix()

	// 初始化Area内存管理器
	InitGlobalAreaMemoryManager(this)
	// 预热常用的点位模板
	this.preloadCommonTemplates()

	return this
}

// 初始化战场的拥有者信息
func (this *Model) FromAreaDB(area *Area) {
	area.Init(false)
	// 更新队伍正在训练的士兵
	if obj := this.DrillPawnQueues.Map[area.index]; obj != nil {
		for _, queue := range obj.Map {
			for _, m := range queue {
				if army := area.GetArmyByUid(m.AUid); army != nil {
					army.AddDrillPawn(m.Id)
				}
			}
		}
	}
	// 更新队伍正在治疗的士兵
	if obj := this.PawnCuringQueues.Map[area.index]; obj != nil {
		for _, m := range obj.List {
			if army := area.GetArmyByUid(m.AUID); army != nil {
				injuryPawn := this.GetInjuryPawn(m.UID, area.Owner)
				if injuryPawn != nil && injuryPawn.Curing {
					army.AddCuringPawn(injuryPawn)
				}
			}
		}
	}
	// 更新玩家的军队分布信息
	this.UpdatePlayerArmyDist(area)
}

// 检测是否触发区域战斗
func (this *Model) CheckAllTriggerAreaBattle() {
	for _, area := range this.Areas.Map {
		if area.IsBattle() {
			area.fspModel.Run()
			this.BattleDist.Lock()
			this.BattleDist.Map[area.index] = area.GetHasArmyPlayers()
			this.BattleDist.Unlock()
		} else {
			area.CheckUpdateTimeFlagHP("") //刷新时光旗
		}
	}
	// if len(this.BattleDist.Map) > 0 {
	// 	this.BattleDist.RLock()
	// 	defer this.BattleDist.RUnlock()
	// 	this.PutNotifyQueue(constant.NQ_BATTLE_DIST, &pb.OnUpdateWorldInfoNotify{Data_17: this.ToBattleDistPb()}) //通知
	// }
}

func (this *Model) Stop() {
	// close(this.notifyQueue)
	// 等待战斗结算完成
	this.battleSettleWg.Wait()
	// 保存有改动的玩家信息
	players := []string{}
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		if m.IsNeedUpdateDB {
			players = append(players, m.Uid)
		}
	}
	this.allTempPlayers.RUnlock()
	for _, m := range players {
		if plr := this.room.GetOnlinePlayerOrDB(m); plr != nil {
			this.room.UpdatePlayerDB(plr)
		}
	}
	log.Info("saveTempPlayer=%v", len(players))
	// 保存有战斗的区域
	for index := range this.BattleDist.Map {
		area := this.GetArea(index)
		if area.IsBattle() {
			area.fspModel.Pause(true)
		}
		this.TagUpdateDBByIndex(index)
	}
	this.UpdateWorldDB(true)
	this.UpdateGameDB()
	this.UpdateAllianceDB()
	this.UpdateChatDB()
	this.UpdateTriggerTask()
	this.UpdatePawnUseStasticDb()
}

// 刷新运行天数
func (this *Model) UpdateRunDay(runDay int32) {
	// 刷新时光旗生命
	this.CheckUpdateTimeFlagHP(runDay)
	// 上报
	ta.TrackSystem("ta_server_run_day", map[string]interface{}{
		"sid_type":      this.room.GetType(),
		"sid":           this.room.GetSID(),
		"pass_day":      runDay,
		"active_pers":   this.GetActivePlayerCount(),
		"ancient_infos": this.GetAncientTrackData(),
		"season":        this.GetSeason().GetType(),
	})
}

// 获取土地类型
func (this *Model) GetLandType(index int32) int32 {
	if index < 0 || index > int32(len(this.Lands)) {
		return 0
	} else if json := config.GetJsonData("land", this.Lands[index]); json != nil {
		return ut.Int32(json["type"])
	}
	return 0
}

// 获取地块等级
func (this *Model) GetLandLv(index int32) int32 {
	if index < 0 || index > int32(len(this.Lands)) {
		return 0
	} else if json := config.GetJsonData("land", this.Lands[index]); json != nil {
		return ut.Int32(json["lv"])
	}
	return 0
}

// 地块类型是否可占领
func (this *Model) CheckLandCanOccupy(index int32) bool {
	if index < 0 || index > int32(len(this.Lands)) {
		return false
	} else if json := config.GetJsonData("land", this.Lands[index]); json != nil {
		return ut.Int32(json["occupy"]) == 1
	}
	return false
}

// 获取指定地块配置
func (this *Model) GetLandAttrConf(index int32, fighter string) map[string]interface{} {
	landLv := this.GetLandLv(index)
	mainIndex := this.GetPlayerMainIndex(fighter)
	level := config.GetLandAttrLvByDis(this.GetToMapCellDis(index, mainIndex), landLv)
	return config.GetJsonData("landAttr", landLv*1000+level)
}

// 获取服务器运行天数
func (this *Model) GetServerRunDay() int32 { return this.room.GetRunDay() }
func (this *Model) GetSeason() g.Season    { return this.season }

// 检测游戏是否结束
func (this *Model) CheckGameOverByLandCount(uid string) {
	if this.room.IsGameOver() {
		return
	}
	cond := this.room.GetWinCond()
	if len(cond) < 2 || cond[0] != 1 {
		return
	}
	maxCount := cond[1]
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return
	} else if alli := this.GetAlliance(plr.AllianceUid); alli != nil { //先看有没有联盟
		if count, _, _, _ := this.GetAlliRankInfo(alli); count >= maxCount {
			this.room.GameOver(2, alli.Uid, alli.Name, count, alli.GetMemberUidAndNames(), nil)
		}
	} else if cellCount := int32(len(plr.OwnCells)); cellCount >= maxCount {
		this.room.GameOver(1, plr.Uid, plr.Nickname, cellCount, [][]string{{plr.Uid, plr.Nickname}}, nil)
	}
}

// 检测血战到底是否结束
func (this *Model) CheckGameOverByConquer() {
	if this.room.IsGameOver() {
		return
	}
	if !this.room.IsConquer() {
		return
	}

	// 仅剩一个联盟时游戏结束
	var alli *Alliance
	this.Alliances.RLock()
	alliNum := len(this.Alliances.Map)
	for _, v := range this.Alliances.Map {
		alli = v
		break
	}
	this.Alliances.RUnlock()
	if alliNum != 1 {
		return
	}
	// 胜利的联盟结算
	this.ConquerAlliSettle(alli, false, true)

	count, _, _, _ := this.GetAlliRankInfo(alli)
	this.room.GameOver(2, alli.Uid, alli.Name, count, alli.GetMemberUidAndNames(), nil)
}

// 古城升级获胜
func (this *Model) CheckGameOverByAncient(uid string, cityId, lv int32) {
	if this.room.IsGameOver() {
		return
	}
	cond := this.room.GetWinCond()
	if len(cond) < 2 || cond[0] != 2 {
		return
	}
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return
	} else if alli := this.GetAlliance(plr.AllianceUid); alli != nil { //先看有没有联盟
		this.room.GameOver(2, alli.Uid, alli.Name, 0, alli.GetMemberUidAndNames(), []int32{cityId, lv})
	} else {
		this.room.GameOver(1, plr.Uid, plr.Nickname, 0, [][]string{{plr.Uid, plr.Nickname}}, []int32{cityId, lv})
	}
}

// 获取当前最高的玩家或联盟
func (this *Model) GetPlayerOrAlliTopWin() (plyaer *pb.PlayerRankInfo, alli *pb.AlliRankInfo) {
	players := []*pb.PlayerRankInfo{}
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		landCount := len(m.OwnCells)
		if landCount == 0 {
			continue
		}
		players = append(players, &pb.PlayerRankInfo{
			Uid:       m.Uid,
			Nickname:  m.Nickname,
			LandCount: int32(landCount),
			Time:      m.CreateTime,
		})
	}
	this.allTempPlayers.RUnlock()
	// 排序 从大到小
	sort.Slice(players, func(i, j int) bool {
		a, b := players[i], players[j]
		if a.LandCount == b.LandCount {
			return a.Time < b.Time
		}
		return a.LandCount > b.LandCount
	})
	if len(players) > 0 {
		plyaer = players[0]
	}
	// 获取联盟的信息
	allis := []*pb.AlliRankInfo{}
	this.Alliances.RLock()
	for _, m := range this.Alliances.Map {
		landCount, _, _, score := this.GetAlliRankInfo(m)
		allis = append(allis, &pb.AlliRankInfo{
			Uid:       m.Uid,
			Name:      m.Name,
			LandCount: landCount,
			Time:      m.CreateTime,
			Score:     score,
		})
	}
	this.Alliances.RUnlock()
	// 排序 从大到小
	sort.Slice(allis, func(i, j int) bool {
		a, b := allis[i], allis[j]
		if a.LandCount == b.LandCount {
			return a.Time < b.Time
		}
		return a.LandCount > b.LandCount
	})
	if len(allis) > 0 {
		alli = allis[0]
	}
	return
}

// 世界pb数据读锁
func (this *Model) WorldPbRLock() {
	this.worldPbCellsLock.RLock()
	this.worldPbPlayersLock.RLock()
}

// 世界pb数据释放读锁
func (this *Model) WorldPbRUnLock() {
	this.worldPbPlayersLock.RUnlock()
	this.worldPbCellsLock.RUnlock()
}

// 获取世界pb数据的地块信息
func (this *Model) GetWorldPbCells() map[string]*pb.PlayerCellBytesInfo {
	this.worldPbCellsLock.RLock()
	defer this.worldPbCellsLock.RUnlock()
	return this.worldPb.GetCells()
}

// 获取世界pb数据中指定玩家的地块信息
func (this *Model) GetWorldPbCellsByUserId(uid string) *pb.PlayerCellBytesInfo {
	this.worldPbCellsLock.RLock()
	defer this.worldPbCellsLock.RUnlock()
	return this.worldPb.GetCells()[uid]
}

// 设置世界pb数据中指定玩家的地块信息
func (this *Model) SetWorldPbCell(uid string, data *pb.PlayerCellBytesInfo) {
	this.worldPbCellsLock.Lock()
	defer this.worldPbCellsLock.Unlock()
	this.worldPb.Cells[uid] = data
}

// 删除世界pb数据中指定玩家的地块信息
func (this *Model) DelWorldPbCellByUserId(uid string) {
	this.worldPbCellsLock.Lock()
	defer this.worldPbCellsLock.Unlock()
	delete(this.worldPb.Cells, uid)
}

// 获取世界pb数据中指定玩家信息
func (this *Model) GetWorldPbPlayersByUid(uid string) *pb.TempPlayerInfo {
	this.worldPbPlayersLock.RLock()
	defer this.worldPbPlayersLock.RUnlock()
	return this.worldPb.GetPlayers()[uid]
}

// 设置世界pb数据中指定玩家信息
func (this *Model) SetWorldPbPlayer(uid string, data *pb.TempPlayerInfo) {
	this.worldPbPlayersLock.Lock()
	defer this.worldPbPlayersLock.Unlock()
	this.worldPb.Players[uid] = data
}

// 删除世界pb数据中指定玩家信息
func (this *Model) DelWorldPbPlayer(uid string) {
	this.worldPbPlayersLock.Lock()
	defer this.worldPbPlayersLock.Unlock()
	delete(this.worldPb.Players, uid)
}

// 获取已沦陷人数
func (this *Model) GetCaptureNum() int32 {
	return atomic.LoadInt32(&this.captureNum)
}

// 添加沦陷人数
func (this *Model) AddCaptureNum(add int32) {
	atomic.AddInt32(&this.captureNum, add)
}

// 数数上报
func (this *Model) TaTrack(uid string, reCreateCount int32, eventName string, properties map[string]interface{}) {
	ta.Track(this.room.GetSID(), uid, this.GetPlayerDistinctId(uid), reCreateCount, eventName, properties)
}

// 数数上报
func (this *Model) TaUserSet(uid string, reCreateCount int32, properties map[string]interface{}) {
	ta.UserSet(this.room.GetSID(), uid, this.GetPlayerDistinctId(uid), reCreateCount, properties)
}

// 地图数据压缩
func LandsBytesCompress(lands []byte) []byte {
	ret := []byte{}
	mapCompressBitCount := 0
	mapCompressIndex := 0
	for _, v := range lands {
		if mapCompressBitCount > 0 {
			// 之前的字节仍有空位没用完 当前字节的高位填充其地位
			val := ret[mapCompressIndex]
			mv := constant.LAND_DATA_BIT_COUNT - mapCompressBitCount
			val |= v >> mv
			ret[mapCompressIndex] = val
			if mapCompressBitCount < constant.LAND_DATA_BIT_COUNT {
				v <<= (8 - constant.LAND_DATA_BIT_COUNT + mapCompressBitCount)
				ret = append(ret, v)
				mapCompressIndex++
				mapCompressBitCount += 8 - constant.LAND_DATA_BIT_COUNT
			} else {
				mapCompressBitCount -= constant.LAND_DATA_BIT_COUNT
			}
		} else {
			mv := 8 - constant.LAND_DATA_BIT_COUNT
			ret = append(ret, v<<mv)
			mapCompressBitCount = mv
			mapCompressIndex = len(ret) - 1
		}
	}
	return ret
}

// 公告
func (this *Model) GameNotice(id int32, parames []string) {
	json := config.GetJsonData("noticeText", id)
	if json == nil {
		return
	}
	text := map[string]string{}
	if textJson, ok := json["text"].(map[string]interface{}); ok {
		for k, v := range textJson {
			text[k] = ut.String(v)
		}
	}
	this.room.NotifyAll("game/OnNotice", &pb.GAME_ONNOTICE_NOTIFY{
		Text:    text,
		Parames: parames,
	})
}

// 检测指定玩家是否拥有古城
func (this *Model) CheckPlayerHasAncient(uid string) bool {
	return this.AncientCityMap.Some(func(v *AncientInfo, k int32) bool {
		area := this.GetArea(v.Index)
		return area != nil && area.Owner == uid
	})
}

// 添加防作弊检测积分
func (this *Model) AddPlayerAntiCheatSocre(uid string, score int32) {
	plr := this.room.GetOnlinePlayerOrDB(uid)
	if plr == nil {
		return
	}
	plr.AddAntiCheatScore(score)
	if !plr.IsOnline() {
		this.room.UpdatePlayerDB(plr) //如果没在线 就保存到数据库
	}
}

// 4.0建筑兼容
func (this *Model) AreaBuildFix(area *Area) {
	// 低于4.0.0版本的区服兼容数据
	if !this.room.CheckRoomVersionLower("4.0.0") {
		return
	}
	if len(area.Builds.List) == 0 {
		return
	}

	addPointsToMap := func(pointMap map[string]bool, points []*ut.Vec2) {
		for _, m := range points {
			pointMap[m.ID()] = true
		}
	}

	area.Init(false)
	// 将建筑设置为配置的坐标
	noSetBuilds := []*AreaBuild{}
	pointMap := map[string]bool{}
	for _, build := range area.Builds.List {
		conf := config.GetJsonData("buildBase", build.Id)
		if conf == nil {
			continue
		}
		posStr := ut.String(conf["pos"])
		if posStr == "" {
			// 未配置坐标
			noSetBuilds = append(noSetBuilds, build)
			continue
		}
		posArr := strings.Split(posStr, ",")
		if len(posArr) < 2 {
			noSetBuilds = append(noSetBuilds, build)
			continue
		}
		build.Point.X = ut.Int32(posArr[0])
		build.Point.Y = ut.Int32(posArr[1])
		if actPoints := build.GetActPoints(); actPoints != nil {
			addPointsToMap(pointMap, actPoints)
		}
	}

	var areaFullCnt int32
	// 未配置坐标的建筑分配位置
loopForPoint:
	for _, build := range noSetBuilds {
		// 先检测当前位置是否被占用
		actPoints := build.GetActPoints()
		if !array.Some(actPoints, func(p *ut.Vec2) bool { return pointMap[p.ID()] }) {
			// 未被占用则使用当前位置
			addPointsToMap(pointMap, actPoints)
			continue
		}
		// 被占用则寻找空闲位置 需要寻找位置的建筑只占1格
		for _, point := range area.buildMapPoints {
			key := point.ID()
			if pointMap[key] {
				continue
			} else {
				build.Point.Set(point)
				pointMap[key] = true
				continue loopForPoint
			}
		}
		// 无空闲位置 多余建筑强制放在主城(2,5)右边两格
		areaFullCnt++
		if areaFullCnt > 2 {
			log.Warning("AreaBuildFix index: %v, areaFullCnt: %v", area.index, areaFullCnt)
			continue
		}
		build.Point.X = 2 + areaFullCnt
		build.Point.Y = 5
	}
	this.TagUpdateDBByIndex(area.index)
}

// 玩家数据兼容
func (this *Model) TempPlyInfoFix(players map[string]player.TableData) {
	// 低于4.0.0版本的区服兼容数据
	if !this.room.CheckRoomVersionLower("4.0.0") {
		return
	}
	for _, plr := range players {
		tplr := this.GetTempPlayer(plr.Uid)
		// 获取主城等级
		curLv := this.GetAreaBuildLvById(tplr.MainCityIndex, constant.MAIN_BUILD_ID)
		// 政策数据兼容
		tplr.PolicyInfoFix(plr, curLv)
	}
}

// 4.0政策数据兼容
func (this *TempPlayer) PolicyInfoFix(plr player.TableData, mainLv int32) {
	for i := int32(1); i <= mainLv; i++ {
		if index, ok := constant.POLICY_SLOT_CONF_MAP[i]; ok {
			// 该等级可解锁槽位
			slotInfo := this.PolicySlots.AddCeriSlot(i)
			// 检测旧数据中该槽位是否选择政策
			oldPolicy := plr.Policys[index]
			if oldPolicy != nil {
				// 旧数据已选择则设置政策
				slotInfo.ID = oldPolicy.ID
			} else {
				// 未选择则随机政策选项
				this.PolicySlots.CeriRandomSelect(i, nil)
			}
		}
	}
}

// 4.0装备数据兼容
func (this *TempPlayer) EquipInfoFix() {
	delIdMap := map[int32]bool{}
	for _, equip := range this.Equips {
		if equip.ViceId > 0 {
			delIdMap[equip.ID] = true // 记录需要删除的融炼装备id
		}
		if equip.IsExclusive() {
			// 专属装备兼容
			if equip.ViceId > 0 && len(equip.Attrs) >= 4 {
				// 专属且已融练 移除融炼属性
				equip.Attrs = equip.Attrs[:5]
			}
			// 专属装备 新版本只保留2条效果 取出一条作为融炼 attrs前2条为基础属性不参与兼容
			var fixSmeltId int32
			var smeltAttrs []int32
			for i := len(equip.Attrs) - 1; i >= 2; i-- {
				attr := equip.Attrs[i]
				if len(attr) >= 2 {
					equipConf := config.GetEquipConfByEffct(ut.String(attr[1]))
					if equipConf != nil {
						// 根据效果类型从配置表找出匹配的装备添加 并删除这条属性
						fixSmeltId = ut.Int32(equipConf["id"])
						if len(attr) < 4 {
							attr = append(attr, 0)
						}
						attr = append(attr, fixSmeltId) // 属性添加融炼装备id
						smeltAttrs = attr
						equip.Attrs = append(equip.Attrs[:i], equip.Attrs[i+1:]...)
						break
					}
				}
			}
			if fixSmeltId == 0 {
				// 配置表没找到对应效果的装备 直接删掉最后一条效果
				len := len(equip.Attrs)
				equip.Attrs = append(equip.Attrs[:len-1], equip.Attrs[len:]...)
			} else {
				equip.Attrs = append(equip.Attrs, smeltAttrs)
			}
			if equip.ViceId != 0 {
				// 兼容之前熔炼的装备
				if smeltEquip := array.Find(this.Equips, func(m *g.EquipInfo) bool { return m.ID == equip.ViceId }); smeltEquip != nil {
					equip.Attrs = append(equip.Attrs, smeltEquip.ToViceAttrs()...)
				}
			}
			// 不保留上次的属性
			equip.LastAttrs = [][]int32{}
			equip.UpdateAttr()
		}
	}

	// 兼容删除融炼多余的装备
	for i := len(this.Equips) - 1; i >= 0; i-- {
		equip := this.Equips[i]
		if delIdMap[equip.ID] {
			if equip.IsExclusive() && equip.ViceId == 0 {
				// 专属需要保留融炼的装备
				this.Equips = append(this.Equips[:i], this.Equips[i+1:]...)
				continue
			} else if !equip.IsExclusive() && equip.ViceId != 0 {
				// 非专属则保留原装备
				this.Equips = append(this.Equips[:i], this.Equips[i+1:]...)
				continue
			}
		}
	}

	// 删掉多余装备后再兼容uid = id + 槽位lv
	curIndex := 0
	curExlusiveIndex := 0
	for _, equip := range this.Equips {
		var lv int32 // 槽位等级
		if !equip.IsExclusive() {
			// 普通装备
			if curIndex < len(constant.EQUIP_SLOT_CONF_NORMAL_LIST) {
				lv = constant.EQUIP_SLOT_CONF_NORMAL_LIST[curIndex]
				curIndex++
			}
		} else {
			// 专武
			if curExlusiveIndex < len(constant.EQUIP_SLOT_CONF_EXCLUSIVE_LIST) {
				lv = constant.EQUIP_SLOT_CONF_EXCLUSIVE_LIST[curExlusiveIndex]
				curExlusiveIndex++
			}
		}
		// 更新uid
		equip.SetUID(ut.String(equip.ID) + "_" + ut.String(lv))
	}
}

// 4.0遗迹建筑坐标兼容
func (this *Model) AncientBuildFix() {
	// 低于4.0.0版本的区服兼容数据
	if !this.room.CheckRoomVersionLower("4.0.0") {
		return
	}
	// 修复遗迹建筑坐标
	this.AncientCityMap.ForEach(func(v *AncientInfo, k int32) bool {
		area := this.GetArea(v.Index)
		if area == nil {
			return true
		}
		if build := area.GetAncientBuild(); build != nil {
			build.Point.X = 0
			build.Point.Y = 0
		}
		return true
	})
}

var (
	exclusiveDefendConfigs       []map[string]interface{} //专武可随机的防御属性配置
	exclusiveAttackConfigs       []map[string]interface{} //专武可随机的攻击属性配置
	bothAttackAndDefendEffectMap map[int32]bool           //同时包含攻击和防御的效果map
)

func InitRandomEquipCache(configs []map[string]interface{}) {
	// 初始化可随机属性
	if exclusiveDefendConfigs == nil || exclusiveAttackConfigs == nil || bothAttackAndDefendEffectMap == nil {
		exclusiveDefendConfigs = []map[string]interface{}{}
		exclusiveAttackConfigs = []map[string]interface{}{}
		bothAttackAndDefendEffectMap = map[int32]bool{}
		for _, conf := range configs {
			if ut.String(conf["random"]) != "1" || ut.String(conf["effect"]) == "" {
				continue
			}

			//防御装
			if ut.String(conf["hp"]) != "" {
				exclusiveDefendConfigs = append(exclusiveDefendConfigs, conf)
			}
			//攻击装
			if ut.String(conf["attack"]) != "" {
				exclusiveAttackConfigs = append(exclusiveAttackConfigs, conf)
			}
			// 既是攻击也是防御装
			if ut.String(conf["hp"]) != "" && ut.String(conf["attack"]) != "" {
				bothAttackAndDefendEffectMap[ut.Int32(conf["effect"])] = true
			}
		}
	}
}

// 随机全局专武属性
func RandomWorldExclusiveEffects(configs []map[string]interface{}, curConf map[string]interface{}) []int32 {
	InitRandomEquipCache(configs)
	ret := []int32{}
	// 获取当前专武的防御和攻击属性数量
	cntStr := ut.String(curConf["random"])
	if cntStr == "" {
		log.Error("RandomWorldExclusiveEffects conf err id: %v", curConf["id"])
		return ret
	}
	cntArr := strings.Split(cntStr, ",")
	if len(cntArr) < 2 {
		log.Error("RandomWorldExclusiveEffects conf err id: %v", curConf["id"])
		return ret
	}
	defendCnt := ut.Int32(cntArr[0])
	attackCnt := ut.Int32(cntArr[1])

	pawnId := ut.Int32(curConf["exclusive_pawn"])
	defendList := array.Clone(exclusiveDefendConfigs)
	attackList := array.Clone(exclusiveAttackConfigs)
	var bothAttAndDefCnt int32 // 同时包含攻击和防御的效果数量
	// 随机防御属性
	for defendCnt > 0 {
		len := len(defendList)
		if len == 0 {
			break
		}
		var conf map[string]interface{}
		conf, defendList, bothAttAndDefCnt = RandomEquipEffect(defendList, pawnId, "not_exclusive_effect", bothAttackAndDefendEffectMap, bothAttAndDefCnt, 2)
		if conf == nil {
			continue
		}
		effect := ut.Int32(conf["effect"])
		// 攻击属性列表中存在该属性也要删除
		array.Delete(attackList, func(m map[string]interface{}) bool { return ut.Int32(m["effect"]) == effect })
		ret = append(ret, effect)
		defendCnt--
	}
	// 随机攻击属性
	for attackCnt > 0 {
		len := len(attackList)
		if len == 0 {
			break
		}
		var conf map[string]interface{}
		conf, attackList, bothAttAndDefCnt = RandomEquipEffect(attackList, pawnId, "not_exclusive_effect", bothAttackAndDefendEffectMap, bothAttAndDefCnt, 2)
		if conf == nil {
			continue
		}
		effect := ut.Int32(conf["effect"])
		ret = append(ret, effect)
		attackCnt--
	}
	return ret
}

// 随机专属属性后清理缓存
func ClearRandomEquipCache() {
	exclusiveDefendConfigs = nil
	exclusiveAttackConfigs = nil
	bothAttackAndDefendEffectMap = nil
}

// 获取所有可以有装备的野怪id
func GetCanEquipMonsterIds() []int32 {
	arr := []int32{}
	datas := config.GetJson("pawnBase").Datas
	for _, conf := range datas {
		tp := ut.Int32(conf["type"])
		if (tp == constant.PAWN_TYPE_BEAST || tp == constant.PAWN_TYPE_CATERAN) && ut.Int32(conf["can_equip"]) > 0 {
			arr = append(arr, ut.Int32(conf["id"]))
		}
	}
	return arr
}

// 随机野怪装备id
func RandomMonsterEquipId(monsterId int32, configs []map[string]interface{}) []int32 {
	monsterCfg := config.GetJsonData("pawnBase", monsterId)
	if monsterCfg == nil {
		return nil
	}
	equipCnt := ut.Int32(monsterCfg["can_equip"])
	// 防御和攻击装备数量各位总数量的一般
	haflCnt := equipCnt / 2
	defendCnt := haflCnt
	attackCnt := haflCnt
	if equipCnt%2 > 0 {
		// 剩余的一条随机
		if ut.Random(0, 1) == 0 {
			defendCnt++
		} else {
			attackCnt++
		}
	}

	InitRandomEquipCache(configs)
	ret := []int32{}
	defendList := array.Clone(exclusiveDefendConfigs)
	attackList := array.Clone(exclusiveAttackConfigs)
	// 随机防御属性
	for defendCnt > 0 {
		len := len(defendList)
		if len == 0 {
			break
		}
		var conf map[string]interface{}
		conf, defendList, _ = RandomEquipEffect(defendList, monsterId, "not_monster", nil, 0, 0)
		if conf == nil {
			continue
		}
		id := ut.Int32(conf["id"])
		// 攻击装备列表中存在也要删除
		array.Delete(attackList, func(m map[string]interface{}) bool { return ut.Int32(m["id"]) == id })
		ret = append(ret, id)
		log.Info("def: %v", id)
		defendCnt--
	}
	// 随机攻击属性
	for attackCnt > 0 {
		len := len(attackList)
		if len == 0 {
			break
		}
		var conf map[string]interface{}
		conf, attackList, _ = RandomEquipEffect(attackList, monsterId, "not_monster", nil, 0, 0)
		if conf == nil {
			continue
		}
		ret = append(ret, ut.Int32(conf["id"]))
		log.Info("attack: %v", ut.Int32(conf["id"]))
		attackCnt--
	}

	// 随机装备id顺序
	sort.Slice(ret, func(i, j int) bool { return ut.Chance(50) })
	return ret
}

// 随机全局装备属性
func RandomEquipEffect(randomList []map[string]interface{}, filterId int32, filterStr string,
	filterMap map[int32]bool, bothCount, bothCond int32) (rst map[string]interface{}, newList []map[string]interface{}, bothCountRst int32) {
	len := len(randomList)
	if len == 0 {
		return
	}
	newList = randomList
	index := ut.Random(0, len-1)
	conf := randomList[index]
	newList = append(randomList[:index], randomList[index+1:]...)
	if exceptStr := ut.String(conf[filterStr]); exceptStr != "" {
		exceptArr := strings.Split(exceptStr, "|")
		if array.Find(exceptArr, func(m string) bool { return m == ut.String(filterId) }) != "" {
			// 该装备不适用
			return
		}
	}
	if filterMap != nil {
		// 限制同时包含攻击和防御的效果数量
		effect := ut.Int32(conf["effect"])
		bothCountRst = bothCount
		if bothAttackAndDefendEffectMap[effect] {
			if bothCount >= bothCond {
				return
			}
			bothCountRst++
		}
	}
	rst = conf
	return
}

// 生成野怪装备
func (this *Model) GenMonsterEquips(idMap map[int32][]int32) {
	for monsterId, equipIds := range idMap {
		if len(equipIds) == 0 {
			continue
		}
		// 主装备
		var mainEquip *g.EquipInfo
		for i := 0; i < len(equipIds); i++ {
			id := equipIds[i]
			equip := &g.EquipInfo{}
			equip.SetID(id)
			equip.Attrs = config.GetEquipTopAttrs(id)
			if i == 0 {
				mainEquip = equip
			} else {
				// 其他的装备作为属性融炼
				mainEquip.Attrs = append(mainEquip.Attrs, equip.ToViceAttrs()...)
			}
		}
		this.MonsterEquipMap[monsterId] = mainEquip.ToJson()
	}
}

// 预加载常用的点位模板
func (this *Model) preloadCommonTemplates() {
	manager := GetAreaPointTemplateManager()
	if manager == nil {
		return
	}

	// 预加载常用的Area配置
	commonConfigs := []struct {
		areaSize  *ut.Vec2
		buildSize *ut.Vec2
		hasOwner  bool
		isBoss    bool
	}{
		// 11x11 Area with 1x1 build (最常见)
		{ut.NewVec2(11, 11), ut.NewVec2(1, 1), false, false},
		{ut.NewVec2(11, 11), ut.NewVec2(1, 1), true, false},

		// 其他常见配置
		{ut.NewVec2(11, 11), ut.NewVec2(3, 3), false, false},
		{ut.NewVec2(11, 11), ut.NewVec2(3, 3), true, false},
		{ut.NewVec2(11, 11), ut.NewVec2(5, 5), false, false},
		{ut.NewVec2(11, 11), ut.NewVec2(5, 5), true, false},

		// Boss区域
		{ut.NewVec2(15, 15), ut.NewVec2(7, 7), false, true},
		{ut.NewVec2(15, 15), ut.NewVec2(7, 7), true, true},
	}

	preloadCount := 0
	for _, config := range commonConfigs {
		template := manager.GetTemplate(config.areaSize, config.buildSize, config.hasOwner, config.isBoss)
		if template != nil {
			preloadCount++
			// 立即释放引用，模板会保留在缓存中
			manager.ReleaseTemplate(config.areaSize, config.buildSize, config.hasOwner, config.isBoss)
		}
	}

	log.Info("Preloaded %d area point templates", preloadCount)
}

// 获取Area内存使用报告
func (this *Model) GetAreaMemoryReport() map[string]interface{} {
	totalAreas := int32(0)
	initializedAreas := int32(0)
	sharedTemplateAreas := int32(0)
	traditionalAreas := int32(0)

	mapSize := this.room.GetMapSize()
	for index := int32(0); index < mapSize.X*mapSize.Y; index++ {
		area := this.GetArea(index)
		if area == nil {
			continue
		}

		totalAreas++
		if area.IsInitialized() {
			initializedAreas++
			if area.usingSharedTemplate {
				sharedTemplateAreas++
			} else {
				traditionalAreas++
			}
		}
	}

	// 获取模板管理器统计
	templateStats := map[string]interface{}{}
	if manager := GetAreaPointTemplateManager(); manager != nil {
		templateStats = manager.GetStats()
	}

	// 获取内存管理器统计
	memoryStats := map[string]interface{}{}
	if memManager := GetGlobalAreaMemoryManager(); memManager != nil {
		memoryStats = memManager.GetStats()
		memoryUsage := memManager.EstimateMemoryUsage()
		memoryStats["memory_usage"] = memoryUsage
	}

	return map[string]interface{}{
		"total_areas":           totalAreas,
		"initialized_areas":     initializedAreas,
		"shared_template_areas": sharedTemplateAreas,
		"traditional_areas":     traditionalAreas,
		"template_stats":        templateStats,
		"memory_stats":          memoryStats,
		"timestamp":             time.Now().Unix(),
	}
}

// 强制清理所有未使用的Area点位数据
func (this *Model) ForceCleanAllUnusedAreaPoints() map[string]interface{} {
	cleanedCount := int32(0)
	totalCount := int32(0)

	mapSize := this.room.GetMapSize()
	for index := int32(0); index < mapSize.X*mapSize.Y; index++ {
		area := this.GetArea(index)
		if area == nil {
			continue
		}

		totalCount++
		if area.ClearUnusedPoints(0) { // 强制清理，不考虑时间
			cleanedCount++
		}
	}

	return map[string]interface{}{
		"total_areas":   totalCount,
		"cleaned_areas": cleanedCount,
		"timestamp":     time.Now().Unix(),
	}
}
