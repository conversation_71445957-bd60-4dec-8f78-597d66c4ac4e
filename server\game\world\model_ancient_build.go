package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 古城信息
type AncientInfo struct {
	deadlock.RWMutex
	MaxLvEffects []*g.EffectObj                    //初始占领满级效果
	LvUpRes      *AncientLvUpResList               //升级资源
	Logs         *AncientContributeLogList         //捐献记录
	MemLogs      *ut.MapLock[string, []*g.TypeObj] //成员累计捐献记录
	CurLogs      *ut.MapLock[string, int32]        //当前等级当天的捐献记录
	BuildInfo    *BTAncientInfo                    //升级建造信息

	UpdateTime      int64 //当天捐献记录更新时间
	FirstOccupyTime int64 //首次攻占时间
	LastCtbTime     int64 //上次捐献时间
	Index           int32 //位置
	CityId          int32 //城市id
	SpeedUpRes      int32 //加速资源
	CurCtbCount     int32 //当前捐献次数
}

// 古城资源捐献记录
type AncientContributeLogList struct {
	List []*AncientContributeLog
	deadlock.RWMutex
}

// 古城资源捐献记录
type AncientContributeLog struct {
	Uid     string `bson:"uid"`      //玩家uid
	Time    int64  `bson:"time"`     //时间
	ResType int32  `bson:"res_type"` //资源类型
	Count   int32  `bson:"count"`    //资源数量
	Type    int8   `bson:"type"`     //捐献类型 0升级 1加速
}

// 升级资源列表
type AncientLvUpResList struct {
	List []*g.TypeObj
	deadlock.RWMutex
}

func NewAncientInfo(index, cityId, lv int32) *AncientInfo {
	return &AncientInfo{
		Index:   index,
		CityId:  cityId,
		LvUpRes: &AncientLvUpResList{List: []*g.TypeObj{}},
		Logs:    &AncientContributeLogList{List: []*AncientContributeLog{}},
		MemLogs: ut.NewMapLock[string, []*g.TypeObj](),
		CurLogs: ut.NewMapLock[string, int32](),
	}
}

func (this *AncientInfo) ToDb() *AncientInfoTableData {
	ret := &AncientInfoTableData{
		Index:           this.Index,
		CityId:          this.CityId,
		LvUpRes:         []*g.TypeObj{},
		SpeedUpRes:      this.SpeedUpRes,
		Logs:            []*AncientContributeLog{},
		MemLogs:         map[string][]*g.TypeObj{},
		CurLogs:         map[string]int32{},
		UpdateTime:      this.UpdateTime,
		BuildInfo:       this.BuildInfo,
		FirstOccupyTime: this.FirstOccupyTime,
	}
	this.LvUpRes.RLock()
	for _, v := range this.LvUpRes.List {
		ret.LvUpRes = append(ret.LvUpRes, v)
	}
	this.LvUpRes.RUnlock()
	this.Logs.RLock()
	for _, v := range this.Logs.List {
		ret.Logs = append(ret.Logs, v)
	}
	this.Logs.RUnlock()
	this.MemLogs.ForEach(func(v []*g.TypeObj, k string) bool {
		ret.MemLogs[k] = v
		return true
	})
	this.CurLogs.ForEach(func(v int32, k string) bool {
		ret.CurLogs[k] = v
		return true
	})
	return ret
}

func (this *AncientInfo) ToPb(lv int32, userId string, logChange bool) *pb.AncientCityInfo {
	this.RLock()
	defer this.RUnlock()
	ret := &pb.AncientCityInfo{
		Index:      this.Index,
		Lv:         lv,
		SpeedUpRes: this.SpeedUpRes,
	}
	ret.LvUpRes = []*pb.TypeObj{}
	this.LvUpRes.RLock()
	for _, v := range this.LvUpRes.List {
		ret.LvUpRes = append(ret.LvUpRes, v.ToPb())
	}
	this.LvUpRes.RUnlock()
	now := time.Now().UnixMilli()
	if this.BuildInfo != nil {
		ret.State = int32(this.BuildInfo.State)
		ret.PauseState = int32(this.BuildInfo.PauseState)
		if this.BuildInfo.State != 0 {
			// 修建中
			if this.BuildInfo.PauseState != 0 && this.BuildInfo.PauseTime > 0 {
				// 暂停中
				ret.SurplusTime = int32(this.BuildInfo.EndTime - this.BuildInfo.PauseTime)
			} else {
				// 未暂停
				ret.SurplusTime = int32(this.BuildInfo.EndTime - now)
			}
		}
	}
	if userId != "" {
		ret.CurContribute = this.CurLogs.Get(userId)
	} else if !logChange {
		ret.CurContribute = -1
	}
	ret.CtbSurplusCount = this.GetCurContributeCount()
	if this.FirstOccupyTime > 0 {
		ret.FirstSurplusTime = int32(ut.MaxInt64(0, this.FirstOccupyTime+constant.ANCIENT_MAX_EFFECT_TIME-now))
	}
	if this.LastCtbTime > 0 {
		ret.CtbCdSurplusTime = int32(ut.MaxInt64(0, this.LastCtbTime+constant.ANCIENT_CONTRIBUTE_CD-now))
	}
	return ret
}

func (this *AncientInfo) FromDb(data *AncientInfoTableData) {
	this.Index = data.Index
	this.CityId = data.CityId
	this.LvUpRes = &AncientLvUpResList{List: data.LvUpRes}
	this.SpeedUpRes = data.SpeedUpRes
	this.Logs = &AncientContributeLogList{List: data.Logs}
	this.MemLogs = ut.NewMapLock[string, []*g.TypeObj]().FromMap(data.MemLogs)
	this.CurLogs = ut.NewMapLock[string, int32]().FromMap(data.CurLogs)
	this.UpdateTime = data.UpdateTime
	this.BuildInfo = data.BuildInfo
	this.CurCtbCount = 0
	if data.FirstOccupyTime > 0 && time.Now().UnixMilli()-data.FirstOccupyTime < constant.ANCIENT_MAX_EFFECT_TIME {
		this.FirstOccupyTime = data.FirstOccupyTime
		this.InitMaxLvEffect()
	}
	if this.BuildInfo != nil {
		this.CurLogs.ForEach(func(v int32, k string) bool {
			if checkCurContributeByType(pb.Int32(v), this.BuildInfo.State) {
				this.CurCtbCount++
			}
			return true
		})
	}

}

// 检查当天捐献记录
func (this *AncientInfo) CheckCurContribute(ctbType int8, uid string) bool {
	val := pb.Int32(this.CurLogs.Get(uid))
	// val为位运算 是否加速捐献占最低位 升级捐献根据捐献类型将具体位置1
	return checkCurContributeByType(val, ctbType)
}

// 更新当天捐献记录
func (this *AncientInfo) UpdateCurContribute(ctbType int8, resType int32, uid string) bool {
	val := pb.Int64(this.CurLogs.Get(uid))
	flag := int64(getCurLogFlag(ctbType, resType))
	if !pb.HasFlag(val, flag) {
		val |= pb.AddFlags(flag)
		this.CurLogs.Set(uid, int32(val))
		this.CurCtbCount++
	}
	// 通知
	return true
}

// 获取当前等级剩余捐献次数
func (this *AncientInfo) GetCurContributeCount() int32 {
	maxCount := constant.ANCIENT_CONTRIBUTE_COUNT_MAX
	return maxCount - this.CurCtbCount
}

// 初始化首次攻占的满级效果
func (this *AncientInfo) InitMaxLvEffect() {
	attrId := this.CityId*1000 + constant.ANCIENT_CITY_MAX_LV
	attrJson := config.GetJsonData("buildAttr", attrId)
	if attrJson != nil {
		this.MaxLvEffects = g.StringToEffectObjs(attrJson["effects"])
	}
}

// 检测当前等级指定类型是否捐献捐献
func checkCurContributeByType(val int32, ctbType int8) bool {
	switch ctbType {
	case 0:
		// 升级
		return val > 1
	case 1:
		// 加速
		return pb.HasFlag(int64(val), 0)
	}
	return false
}

// 捐赠古城资源
func (this *Model) AncientResContribute(area *Area, build *AreaBuild, ctbType int8, resType, count int32, plr g.Player, s2c *pb.GAME_HD_ANCIENTCONTRIBUTE_S2C) (output *pb.UpdateOutPut, err string) {
	if count <= 0 {
		return nil, ecode.ANCIETN_RES_TYPE_ERR.String()
	}
	if !constant.ANCIENT_CONTRIBUTE_RES_TYPE[resType] {
		// 古城捐献资源类型错误
		return nil, ecode.ANCIETN_RES_TYPE_ERR.String()
	}
	resInfo := this.AncientCityMap.Get(area.GetIndex())
	if resInfo.BuildInfo == nil {
		// 古城升级信息不存在
		return nil, ecode.AREA_NOT_ANCIETN.String()
	}
	resInfo.Lock()
	defer func() {
		resInfo.Unlock()
		if err == "" {
			this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{
				Data_66: resInfo.ToPb(build.Lv, "", false),
			})
		}
	}()

	// 已暂停升级无法捐献
	if resInfo.BuildInfo.PauseState != 0 {
		return nil, ecode.ANCIETN_PAUSE.String()
	}
	// 已达到最大等级
	if build.Lv >= constant.ANCIENT_CITY_MAX_LV {
		return nil, ecode.ANCIETN_PAUSE.String()
	}
	now := time.Now().UnixMilli()
	// 捐献CD中
	if now-resInfo.LastCtbTime < constant.ANCIENT_CONTRIBUTE_CD {
		s2c.CtbCdSurplusTime = int32(ut.MaxInt64(0, resInfo.LastCtbTime+constant.ANCIENT_CONTRIBUTE_CD-now))
		return nil, ecode.ANCIENT_CONTRIBUTE_CD.String()
	}
	// 检测当天是否已捐献
	if resInfo.CheckCurContribute(ctbType, plr.GetUID()) {
		return nil, ecode.ANCIETN_CONTRIBUTE_LIMIT.String()
	}
	// 检测捐献次数上限
	if resInfo.GetCurContributeCount() <= 0 {
		return nil, ecode.ANCIENT_CONTRIBUTE_COUNT_LIMIT.String()
	}
	// 检测资源捐赠数量上限
	switch ctbType {
	case 0:
		// 升级资源
		resCount := count
		resInfo.LvUpRes.RLock()
		for _, v := range resInfo.LvUpRes.List {
			if v.Type == resType {
				resCount += v.Count
			}
		}
		resInfo.LvUpRes.RUnlock()
		for _, v := range build.UpCosts {
			if v.Type == resType && resCount > v.Count {
				// 超过该资源上限
				return nil, ecode.ANCIETN_CONTRIBUTE_RES_LIMIT.String()
			}
		}

	case 1:
		// 加速资源
		var speedUpCost int32
		for _, v := range build.UpCosts {
			speedUpCost += v.Count
		}
		speedUpCost /= constant.ANCIENT_CITY_SPEED_UP_PARAM
		now := time.Now().UnixMilli()
		maxSpeedUpCount := int32((resInfo.BuildInfo.EndTime - now) / constant.ANCIENT_CITY_SPEED_UP_TIME)
		if (resInfo.BuildInfo.EndTime-now)%constant.ANCIENT_CITY_SPEED_UP_TIME > 0 {
			maxSpeedUpCount++
		}
		if (resInfo.SpeedUpRes+count)/speedUpCost > maxSpeedUpCount {
			// 超过加速次数上限
			return nil, ecode.ANCIETN_CONTRIBUTE_RES_LIMIT.String()
		}
	}
	costRes := g.NewTypeObj(resType, 0, count)
	if rst := plr.CheckAndDeductCostByTypeObjOne(costRes); !rst {
		// 检测并扣除资源
		return nil, ecode.RES_NOT_ENOUGH.String()
	}

	log.Info("AncientResContribute city: %v, userId: %v, ctbType: %v, resType: %v, count: %v", resInfo.CityId, plr.GetUID(), ctbType, resType, count)
	output = plr.ToItemByTypeObjsPb([]*g.TypeObj{costRes})
	switch ctbType {
	case 0:
		// 升级资源
		resInfo.LvUpRes.Lock()
		resInfo.LvUpRes.List = AddAncientRes(resInfo.LvUpRes.List, resType, count)
		resInfo.LvUpRes.Unlock()
	case 1:
		// 加速资源
		resInfo.SpeedUpRes += count
	}
	resInfo.LastCtbTime = now
	// 更新捐献记录
	resInfo.Logs.Lock()
	newLog := &AncientContributeLog{
		Uid:     plr.GetUID(),
		Type:    ctbType,
		ResType: resType,
		Count:   count,
		Time:    time.Now().UnixMilli(),
	}
	resInfo.Logs.List = append([]*AncientContributeLog{newLog}, resInfo.Logs.List...)
	if len(resInfo.Logs.List) > constant.ANCIENT_CONTRIBUTE_LOG_LIMIT {
		resInfo.Logs.List = resInfo.Logs.List[:constant.ANCIENT_CONTRIBUTE_LOG_LIMIT]
	}
	resInfo.Logs.Unlock()
	// 更新玩家捐献记录
	memLog := resInfo.MemLogs.Get(plr.GetUID())
	if memLog == nil {
		memLog = []*g.TypeObj{}
	}
	resInfo.MemLogs.Lock()
	memLog = AddAncientRes(memLog, resType, count)
	resInfo.MemLogs.Unlock()
	resInfo.MemLogs.Set(plr.GetUID(), memLog)
	// 更新当前等级玩家当天捐献记录
	if slg.GetAncientCtbLimit() {
		resInfo.UpdateCurContribute(ctbType, resType, plr.GetUID())
	}
	return
}

// 建造信息
type BTAncientInfo struct {
	// Index       int    `bson:"index"`        //位置
	// CityID      int    `bson:"city_id"`      //城市id
	// BUid       string `bson:"b_uid"`       //建筑uid
	// BLv        int `bson:"b_lv"`        //要建造的等级
	EndTime    int64 `bson:"end_time"`    //开始时间
	PauseTime  int64 `bson:"pause_time"`  //暂停时间
	State      int8  `bson:"state"`       //修建状态 0未修建 1修建中
	PauseState int8  `bson:"pause_state"` //暂停状态 0未暂停 1交战暂停
}

func NewBTAncientInfo(lv int32) *BTAncientInfo {
	return &BTAncientInfo{
		State: 0,
	}
}

// 检测更新古城信息
func (this *Model) CheckUpdateAncient(now int64) {
	checkAncientLvUpList := []*AncientInfo{}
	this.AncientCityMap.ForEach(func(m *AncientInfo, k int32) bool {
		if m.BuildInfo == nil {
			return true
		}
		if m.FirstOccupyTime > 0 && now-m.FirstOccupyTime >= constant.ANCIENT_MAX_EFFECT_TIME {
			m.FirstOccupyTime = 0
			m.MaxLvEffects = nil
		}
		// 当前等级捐献记录每日清理
		this.CheckCurLogClear(m, now)
		// 交战时间暂停处理
		if m.BuildInfo.PauseState == 0 && helper.IsCanOccupyTime() {
			// 未暂停且进入交战时间 开始暂停
			this.AncientBtPause(m, 1)
		} else if m.BuildInfo.PauseState == 1 && !helper.IsCanOccupyTime() {
			// 交战暂停中且未在交战时间 暂停结束
			if area := this.GetArea(m.Index); area != nil && !area.IsBattle() {
				this.AncientBtContinue(m)
			}
		}
		if m.BuildInfo.PauseState != 0 {
			// 暂停中
			return true
		}
		cell, area := this.GetCell(m.Index), this.GetArea(m.Index)
		if cell == nil || area == nil {
			return true
		}
		build := area.GetAncientBuild()
		if build == nil {
			return true
		}
		if build.Lv >= constant.ANCIENT_CITY_MAX_LV {
			return true
		}
		if this.CheckAncientLvUpRes(build, m) {
			// 通知
			this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{Data_66: m.ToPb(build.Lv, "", false)})
		}
		if m.BuildInfo.State == 0 {
			// 未修建
			return true
		}
		// 加速检测
		this.CheckSpeedUpRes(area, build, m)
		if now < m.BuildInfo.EndTime {
			return true
		}
		checkAncientLvUpList = append(checkAncientLvUpList, m)
		return true
	})
	for _, m := range checkAncientLvUpList {
		area := this.GetArea(m.Index)
		if area == nil {
			continue
		} else if build := area.GetAncientBuild(); build != nil {
			this.AncientLvUp(area, m, build, build.Lv+1)
		}
	}
}

// 古城被占领重置
func (this *Model) AncientOccupyReset(area *Area, oldOwner string) {
	resInfo := this.AncientCityMap.Get(area.GetIndex())
	if resInfo == nil {
		resInfo = NewAncientInfo(area.GetIndex(), area.CityId, 0)
		this.AncientCityMap.Set(area.GetIndex(), resInfo)
	}
	resInfo.Lock()
	build := area.GetAncientBuild()
	if oldOwner == "" {
		// 无主的设置为1级 设置初始占领加成
		build.Lv = 1
		resInfo.FirstOccupyTime = time.Now().UnixMilli()
		resInfo.InitMaxLvEffect()
	} else {
		// 有主的被占领降级 移除初始占领加成
		build.Lv = ut.MaxInt32(1, build.Lv-constant.ANCIENT_CITY_OCCUPY_LV_DOWN)
		resInfo.FirstOccupyTime = 0
		resInfo.MaxLvEffects = nil
	}
	build.UpdateAttrJson()
	// 重置古城数据
	resInfo.LvUpRes.Lock()
	resInfo.LvUpRes.List = []*g.TypeObj{}
	resInfo.LvUpRes.Unlock()
	resInfo.Logs.Lock()
	resInfo.Logs.List = []*AncientContributeLog{}
	resInfo.Logs.Unlock()
	resInfo.MemLogs.Clean()
	resInfo.CurLogs.Clean()
	resInfo.CurCtbCount = 0
	resInfo.SpeedUpRes = 0
	resInfo.BuildInfo = NewBTAncientInfo(build.Lv + 1)
	resInfo.UpdateTime = ut.NowZeroTime() + ut.TIME_DAY
	resInfo.Unlock()
	this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{
		Data_66: resInfo.ToPb(build.Lv, "", true),
	})
	// 公告
	plr := this.GetTempPlayer(area.Owner)
	this.PutNotifyQueue(constant.NQ_SYS_MSG, &pb.OnUpdateWorldInfoNotify{
		Data_54: &pb.SysMsgInfo{
			Id:      int32(slg.ANCIENT_OCCUPY_NOTICE_ID),
			Parames: []string{plr.Nickname, config.GetAncientNameById(area.CityId)},
		},
	})
	this.AncientAvoidWarCheck()
	this.AncientGameoverCheck()
}

// 古城免战检测
func (this *Model) AncientAvoidWarCheck() {
	alliAncientCountMap := map[string]int{} //联盟占领遗迹数量
	PlyAncientCountMap := map[string]int{}  //个人占领遗迹数量
	this.AncientCityMap.ForEach(func(v *AncientInfo, index int32) bool {
		area := this.GetArea(index)
		if area == nil || area.Owner == "" {
			return true
		}
		build := area.GetAreaBuildById(area.CityId)
		if build == nil {
			return true
		}
		tmpPly := this.GetTempPlayer(area.Owner)
		if tmpPly == nil {
			return true
		}
		alli := this.GetAlliance(tmpPly.AllianceUid)
		if alli != nil {
			alliAncientCountMap[alli.Uid]++
		} else {
			PlyAncientCountMap[area.Owner]++
		}
		return true
	})
	// 同时占领X个遗迹的势力取消免战
	for k, v := range alliAncientCountMap {
		if v >= constant.ANCIENT_OCCUPY_NO_AVOID_WAR_COUNT {
			this.SetAlliNoAvoidWar(k, true)
		}
	}
	for k, v := range PlyAncientCountMap {
		if v >= constant.ANCIENT_OCCUPY_NO_AVOID_WAR_COUNT {
			this.SetPlayerNoAvoidWar(k, true)
		}
	}
}

// 古城获胜检测
func (this *Model) AncientGameoverCheck() {
	winCond := this.room.GetWinCond()
	if len(winCond) == 0 || winCond[0] != 2 {
		return // 胜利条件不为遗迹修建
	}
	alliAncientCountMap := map[string]int{} //联盟占领遗迹数量
	PlyAncientCountMap := map[string]int{}  //个人占领遗迹数量
	var winAlli *Alliance
	var winPly *TempPlayer
	sameOccupy := true
	occupied := false
	var occupyPly *TempPlayer
	var occupyAlli *Alliance
	this.AncientCityMap.ForEach(func(v *AncientInfo, index int32) bool {
		area := this.GetArea(index)
		if area == nil || area.Owner == "" {
			return true
		}
		build := area.GetAreaBuildById(area.CityId)
		if build == nil {
			return true
		}
		tmpPly := this.GetTempPlayer(area.Owner)
		if tmpPly == nil {
			return true
		}
		occupied = true
		alli := this.GetAlliance(tmpPly.AllianceUid)
		// 达到最高等级
		if build.Lv >= constant.ANCIENT_CITY_MAX_LV {
			if alli != nil {
				winAlli = alli
			} else {
				winPly = tmpPly
			}
			return false
		}
		// 达到同时占领获胜条件的最低等级
		if build.Lv >= constant.ANCIENT_OCCUPY_WIN_SAME_TIME_LV {
			if alli != nil {
				alliAncientCountMap[alli.Uid]++
			} else {
				PlyAncientCountMap[area.Owner]++
			}
		}
		if sameOccupy {
			// 判断是否同一阵营占领
			if alli != nil {
				if occupyAlli == nil {
					occupyAlli = alli
				}
				if alli.Uid != occupyAlli.Uid || occupyPly != nil {
					sameOccupy = false
				}
			} else {
				if occupyPly == nil {
					occupyPly = tmpPly
				}
				if tmpPly.Uid != occupyPly.Uid || occupyAlli != nil {
					sameOccupy = false
				}
			}
		}
		return true
	})
	if winAlli != nil || winPly != nil {
		this.GameOver(winAlli, winPly)
		return
	}
	// 冬季同一阵营占领
	if this.GetSeason().GetType() >= 3 && occupied && sameOccupy {
		winAlli = occupyAlli
		winPly = occupyPly
		this.GameOver(winAlli, winPly)
		return
	}
	// 同一阵营占领X个达到指定等级的古城 则直接胜利
	for k, v := range alliAncientCountMap {
		if v >= constant.ANCIENT_OCCUPY_WIN_SAME_TIME_COUNT {
			winAlli = this.GetAlliance(k)
			break
		}
	}
	for k, v := range PlyAncientCountMap {
		if v >= constant.ANCIENT_OCCUPY_WIN_SAME_TIME_COUNT {
			winPly = this.GetTempPlayer(k)
			break
		}
	}
	this.GameOver(winAlli, winPly)
}

func (this *Model) GameOver(winAlli *Alliance, winPly *TempPlayer) {
	if winAlli != nil { //先看有没有联盟
		this.room.GameOver(2, winAlli.Uid, winAlli.Name, 0, winAlli.GetMemberUidAndNames(), []int32{})
	} else if winPly != nil {
		this.room.GameOver(1, winPly.Uid, winPly.Nickname, 0, [][]string{{winPly.Uid, winPly.Nickname}}, []int32{})
	}
}

// 古城升级
func (this *Model) AncientLvUp(area *Area, resInfo *AncientInfo, build *AreaBuild, lv int32) {
	build.Lv = ut.MinInt32(lv, constant.ANCIENT_CITY_MAX_LV)
	build.UpdateAttrJson()
	this.TagUpdateDBByIndex(area.index)
	// 公告
	this.PutNotifyQueue(constant.NQ_SYS_MSG, &pb.OnUpdateWorldInfoNotify{
		Data_54: &pb.SysMsgInfo{
			Id:      int32(slg.ANCIENT_LV_UP_NOTICE_ID),
			Parames: []string{config.GetAncientNameById(area.CityId), ut.String(build.Lv)},
		},
	})
	if build.Lv >= constant.ANCIENT_CITY_MAX_LV {
		// 达到最高等级 游戏结束
		this.CheckGameOverByAncient(area.Owner, area.CityId, build.Lv)
		resInfo.BuildInfo.State = 0
	} else {
		// 继续升级
		this.ContinueLvUp(build, resInfo)
		// 古城获胜检测
		this.AncientGameoverCheck()
	}
	// 通知
	this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{Data_66: resInfo.ToPb(build.Lv, "", true)})
}

// 古城暂停处理
func (this *Model) AncientBtPause(info *AncientInfo, pauseType int8) {
	area := this.GetArea(info.Index)
	if area == nil {
		return
	}
	build := area.GetAncientBuild()
	if build == nil {
		return
	}
	info.Lock()
	info.BuildInfo.PauseState = pauseType
	info.BuildInfo.PauseTime = time.Now().UnixMilli()
	info.Unlock()
	this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{Data_66: info.ToPb(build.Lv, "", false)})
}

// 古城升级恢复暂停
func (this *Model) AncientBtContinue(info *AncientInfo) {
	area := this.GetArea(info.Index)
	if area == nil {
		return
	}
	build := area.GetAncientBuild()
	if build == nil {
		return
	}
	info.Lock()
	info.BuildInfo.PauseState = 0
	// 结束时间需要加上暂停这段时间
	if info.BuildInfo.PauseTime > 0 {
		info.BuildInfo.EndTime += (time.Now().UnixMilli() - info.BuildInfo.PauseTime)
	}
	info.BuildInfo.PauseTime = 0
	info.Unlock()
	this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{Data_66: info.ToPb(build.Lv, "", false)})
}

// 检测升级资源
func (this *Model) CheckAncientLvUpRes(build *AreaBuild, resInfo *AncientInfo) bool {
	resInfo.LvUpRes.Lock()
	defer resInfo.LvUpRes.Unlock()
	// 根据配置获取升级消耗
	for _, v := range build.UpCosts {
		exist := false
		for _, m := range resInfo.LvUpRes.List {
			if m.Type != v.Type {
				continue
			} else if m.Count < v.Count {
				return false
			} else {
				exist = true
			}
		}
		if !exist {
			return false
		}
	}
	// 扣除升级资源
	for _, v := range build.UpCosts {
		for _, m := range resInfo.LvUpRes.List {
			if m.Type != v.Type {
				continue
			} else if m.Count < v.Count {
				return false
			} else {
				m.Count -= v.Count
			}
		}
	}
	resInfo.BuildInfo.EndTime = time.Now().UnixMilli() + config.GetAncientLvUpTime(build.Id, build.Lv)
	resInfo.BuildInfo.State = 1
	resInfo.CurCtbCount = 0
	return true
}

// 继续升级处理
func (this *Model) ContinueLvUp(build *AreaBuild, resInfo *AncientInfo) {
	resInfo.Lock()
	resInfo.CurLogs.Clean()
	resInfo.UpdateTime = ut.NowZeroTime() + ut.TIME_DAY
	resInfo.BuildInfo.EndTime = 0
	resInfo.BuildInfo.State = 0
	resInfo.CurCtbCount = 0
	resInfo.Unlock()
	this.CheckAncientLvUpRes(build, resInfo)
}

// 检测加速资源
func (this *Model) CheckSpeedUpRes(area *Area, build *AreaBuild, resInfo *AncientInfo) {
	if resInfo.BuildInfo.PauseState != 0 {
		// 暂停中不加速
		return
	}
	var speedUpCost int32
	for _, v := range build.UpCosts {
		speedUpCost += v.Count
	}
	speedUpCost /= constant.ANCIENT_CITY_SPEED_UP_PARAM
	if speedUpCost <= 0 {
		return
	}
	speedUpCount := resInfo.SpeedUpRes / speedUpCost
	if speedUpCount <= 0 {
		return
	}
	now := time.Now().UnixMilli()
	maxSpeedUpCount := (resInfo.BuildInfo.EndTime - now) / constant.ANCIENT_CITY_SPEED_UP_TIME
	if (resInfo.BuildInfo.EndTime-now)%constant.ANCIENT_CITY_SPEED_UP_TIME > 0 {
		maxSpeedUpCount++
	}
	speedUpCount = ut.MinInt32(speedUpCount, int32(maxSpeedUpCount))
	resInfo.Lock()
	// 扣除资源
	resInfo.SpeedUpRes -= speedUpCost * speedUpCount
	// 剩余时间加速
	resInfo.BuildInfo.EndTime -= constant.ANCIENT_CITY_SPEED_UP_TIME * int64(speedUpCount)
	log.Info("CheckSpeedUpRes city: %v, curLv: %v, speedUpCount: %v", resInfo.CityId, build.Lv, speedUpCount)
	resInfo.Unlock()
	// 通知
	this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{Data_66: resInfo.ToPb(build.Lv, "", false)})
}

// // 检测升级条件
// func (this *Model) CheckAncientBTCondition(index, cityId int, owner string, btInfo *BTAncientInfo, area *Area, build *AreaBuild) {
// 	rst := false
// 	defer func() {
// 		if btInfo.State == 1 && !rst {
// 			// 升级中条件不满足则暂停升级
// 			btInfo.State = 0
// 			btInfo.PauseTime = time.Now().UnixMilli()
// 		} else if btInfo.State == 0 && rst {
// 			// 暂停中满足条件则继续升级
// 			btInfo.State = 1
// 			if btInfo.PauseTime > 0 {
// 				// 恢复升级后需要的时间加上暂停的这段时间
// 				btInfo.EndTime += time.Now().UnixMilli() - btInfo.PauseTime
// 				btInfo.PauseTime = 0
// 			}
// 		}
// 	}()
// 	ownerPlr := this.room.GetOnlinePlayerOrDB(owner)
// 	if ownerPlr == nil {
// 		return
// 	}
// 	// 五级地数量
// 	var landCount int
// 	alliUid := this.GetPlayerAlliUid(owner)
// 	if alliUid != "" {
// 		alli := this.GetAlliance(alliUid)
// 		if alli != nil {
// 			alli.Members.RLock()
// 			for _, m := range alli.Members.List {
// 				plr := this.room.GetOnlinePlayerOrDB(m.Uid)
// 				if plr != nil {
// 					landCount += plr.GetOccupyLandCount(5)
// 				}
// 			}
// 			alli.Members.RUnlock()
// 		}
// 	} else {
// 		landCount = ownerPlr.GetOccupyLandCount(5)
// 	}
// 	// TODO 配置获取升级条件
// 	needLandCount := 50 + build.Lv
// 	rst = landCount >= needLandCount
// }

// 当前等级捐献记录每日清理
func (this *Model) CheckCurLogClear(info *AncientInfo, now int64) {
	if now >= info.UpdateTime {
		info.Lock()
		info.UpdateTime = ut.NowZeroTime() + ut.TIME_DAY
		info.CurLogs.Clean()
		info.CurCtbCount = 0
		info.Unlock()
		area := this.GetArea(info.Index)
		if area == nil {
			return
		}
		build := area.GetAncientBuild()
		if build == nil {
			return
		}
		this.PutNotifyQueue(constant.NQ_ANCIENT_INFO, &pb.OnUpdateWorldInfoNotify{Data_66: info.ToPb(build.Lv, "", true)})
	}
}

// 直接设置古城等级
func (this *Model) SetAncientLv(id, lv int32) {
	if _, info, ok := this.AncientCityMap.Find(func(v *AncientInfo, k int32) bool { return v.CityId == id }); ok && info != nil {
		if area := this.GetArea(info.Index); area != nil {
			if build := area.GetAncientBuild(); build != nil {
				this.AncientLvUp(area, info, build, lv)
			}
		}
	}
}

// 添加资源
func AddAncientRes(resArr []*g.TypeObj, resType, count int32) []*g.TypeObj {
	resExist := false
	for _, v := range resArr {
		if v.Type == resType {
			resExist = true
			v.Count += count
		}
	}
	if !resExist {
		resArr = append(resArr, &g.TypeObj{Type: resType, Count: count})
	}
	return resArr
}

func getCurLogFlag(ctbType int8, resType int32) int32 {
	if ctbType == 1 {
		return 0
	} else {
		return resType
	}
}

// 根据玩家获取遗迹
func (this *Model) GetAncientByPlayer(uid string) *AncientInfo {
	if _, info, ok := this.AncientCityMap.Find(func(v *AncientInfo, k int32) bool {
		if area := this.GetArea(v.Index); area != nil && area.Owner != "" {
			return this.CheckIsOneAlliance(uid, area.Owner)
		}
		return false
	}); ok && info != nil {
		return info
	}
	return nil
}

// 获取遗迹效果根据玩家
func (this *Model) GetAncientEffectFloatByPlayer(uid string, tp int32) float64 {
	if uid == "" {
		return 0
	}
	val, exist := 0.0, false
	this.AncientCityMap.ForEach(func(v *AncientInfo, k int32) bool {
		if area := this.GetArea(v.Index); area != nil && area.Owner != "" && this.CheckIsOneAlliance(uid, area.Owner) {
			if len(v.MaxLvEffects) > 0 {
				// 初始占领满级效果
				for _, m := range v.MaxLvEffects {
					if m.Type == tp {
						exist = true
						val = m.Value
						return false
					}
				}
			} else if build := area.GetAncientBuild(); build != nil {
				exist, val = build.GetEffect(tp)
				if exist {
					return false
				}
			}
		}
		return true
	})
	return val
}

// 获取是否已生成遗迹
func (this *Model) GetHasCreateAncient() bool {
	return this.HasCreateAnciet
}

// 获取遗迹最高等级
func (this *Model) GetAncientHighestLv() int32 {
	var lv int32
	this.AncientCityMap.ForEach(func(v *AncientInfo, k int32) bool {
		if area := this.GetArea(v.Index); area != nil {
			if build := area.GetAncientBuild(); build != nil {
				if build.Lv > lv {
					lv = build.Lv
				}
			}
		}
		return true
	})
	return lv
}

// 获取遗迹上报信息
func (this *Model) GetAncientTrackData() []map[string]interface{} {
	arr := []map[string]interface{}{}
	this.AncientCityMap.ForEach(func(v *AncientInfo, k int32) bool {
		data := map[string]interface{}{"ancient_id": v.CityId, "ancient_lv": 0}
		if area := this.GetArea(v.Index); area != nil {
			if build := area.GetAncientBuild(); build != nil {
				data["ancient_id"] = build.Id
				data["ancient_lv"] = build.Lv
			}
		}
		arr = append(arr, data)
		return true
	})
	return arr
}
