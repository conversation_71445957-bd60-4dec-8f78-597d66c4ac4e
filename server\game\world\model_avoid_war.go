package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"time"

	"github.com/sasha-s/go-deadlock"
)

type AvoidWarData struct {
	Map    map[int32]int64
	RbTree *ut.RbTreeContainer[int32]
	deadlock.RWMutex
}

func (this *Model) GetAvoidWarTimeByIndex(index int32) int64 {
	this.AvoidWarAreaData.RLock()
	time := this.AvoidWarAreaData.Map[index]
	this.AvoidWarAreaData.RUnlock()
	if time <= 0 {
		time = this.AvoidWarAreas2.Get(index)
	}
	return time
}

// 是否免战区域
func (this *Model) IsAvoidWarArea(area *Area, uid string) bool {
	return this.GetAvoidWarTimeByIndex(area.index) > 0
}

// 是否有保护
func (this *Model) IsHasProtect(uid string, index int32) bool {
	if this.GetSeason().GetType() > 0 {
		return false
	}
	if protectOwner := this.CheckAroundProtectMode(index); protectOwner == "" || protectOwner == uid {
		// 自己可进攻自己保护区域内的地块
		return false
	}
	return true
}

// 普通免战ToPb
func (this *Model) ToAvoidWarAreasPb() map[int32]int32 {
	data := map[int32]int32{}
	now := time.Now().UnixMilli()
	this.AvoidWarAreaData.RLock()
	for k, v := range this.AvoidWarAreaData.Map {
		data[k] = int32(v - now)
	}
	this.AvoidWarAreaData.RUnlock()
	return data
}

// 战斗超时免战ToPb
func (this *Model) ToAvoidWarAreas2Pb() map[int32]int32 {
	data := map[int32]int32{}
	now := time.Now().UnixMilli()
	this.AvoidWarAreas2.ForEach(func(v int64, k int32) bool {
		data[k] = int32(v - now)
		return true
	})
	return data
}

// 添加免战时间
func (this *Model) AddAvoidWarAreaEndTime(index int32, addTime int64, tp int) {
	now := time.Now().UnixMilli()
	var endTime int64
	switch tp {
	case constant.AVOID_WAR_TYPE_NORMAL:
		// 普通免战
		var lastEndTime int64
		this.AvoidWarAreaData.RLock()
		endTime = this.AvoidWarAreaData.Map[index]
		this.AvoidWarAreaData.RUnlock()
		if endTime == 0 {
			endTime = now + addTime
		} else {
			lastEndTime = endTime
			endTime += addTime
		}
		this.AvoidWarAreaData.Lock()
		if lastEndTime > 0 {
			// 免战已存在 需先从红黑数中删除
			this.AvoidWarAreaData.RbTree.RemoveElement(int(lastEndTime), index)
		}
		this.AvoidWarAreaData.Map[index] = endTime
		this.AvoidWarAreaData.RbTree.AddElement(int(endTime), index)
		this.AvoidWarAreaData.Unlock()

	case constant.AVOID_WAR_TYPE_BATTLE_TIMEOUT:
		// 战斗超时免战
		if endTime == 0 {
			endTime = now + addTime
		} else {
			endTime += addTime
		}
		this.AvoidWarAreas2.Set(index, endTime)
	}
	this.PutNotifyQueue(constant.NQ_AREA_AVOID_WAR, &pb.OnUpdateWorldInfoNotify{
		Data_26: &pb.AvoidWarInfo{
			Index: index,
			Time:  int32(endTime - now),
			Type:  int32(tp),
		},
	})
}

// 添加普通免战
func (this *Model) AddGeneralAvoidWarArea(index int32, time int64) {
	if this.GetSeason().GetType() < 2 { //只有春夏可以增加免战
		this.AddAvoidWarAreaEndTime(index, time, constant.AVOID_WAR_TYPE_NORMAL)
	}
}

// 清理普通免战
func (this *Model) CleanGeneralAvoidWarArea() {
	this.AvoidWarAreaData.Lock()
	this.AvoidWarAreaData.Map = map[int32]int64{}
	this.AvoidWarAreaData.RbTree.Clear()
	this.AvoidWarAreaData.Unlock()
	// 这里不通知 前端自己做处理
}

// 删除免战 这里暂时只删除普通免战
func (this *Model) RemoveAvoidWarArea(index int32) {
	this.AvoidWarAreaData.Lock()
	if endTime := this.AvoidWarAreaData.Map[index]; endTime > 0 {
		delete(this.AvoidWarAreaData.Map, index)
		this.AvoidWarAreaData.RbTree.RemoveElement(int(endTime), index)
		// 通知
		this.PutNotifyQueue(constant.NQ_AREA_AVOID_WAR, &pb.OnUpdateWorldInfoNotify{
			Data_26: &pb.AvoidWarInfo{Index: index},
		})
	}
	this.AvoidWarAreaData.Unlock()
}

// 刷新免战信息
func (this *Model) CheckUpdateAreaAvoidWar() {
	now := time.Now().UnixMilli()
	this.UpdateAvoidWar(now)
	this.AvoidWarAreas2.DeleteEach(func(v int64, k int32) bool { return this.CheckAvoidWarEnd(now, v, k) })
}

// 刷新普通免战
func (this *Model) UpdateAvoidWar(now int64) {
	// 遍历之前先检测最快的免战是否结束
	this.AvoidWarAreaData.RLock()
	if len(this.AvoidWarAreaData.Map) == 0 {
		this.AvoidWarAreaData.RUnlock()
		return
	}
	minTreeNode := this.AvoidWarAreaData.RbTree.FindMinElements()
	if minTreeNode == nil {
		this.AvoidWarAreaData.RUnlock()
		return
	} else {
		if ut.Int64(minTreeNode.Key) > now {
			// 结束时间最快的免战都没结束
			this.AvoidWarAreaData.RUnlock()
			return
		}
	}
	this.AvoidWarAreaData.RUnlock()
	this.AvoidWarAreaData.Lock()
	for m := this.AvoidWarAreaData.RbTree.FindMinElements(); m != nil; m = this.AvoidWarAreaData.RbTree.FindMinElements() {
		endTime := ut.Int64(m.Key)
		indexList := m.Value.([]int32)
		if endTime > now {
			// 结束时间最快的免战都没结束
			break
		}
		for i := len(indexList) - 1; i >= 0; i-- {
			index := indexList[i]
			delete(this.AvoidWarAreaData.Map, index)
			// 通知
			this.PutNotifyQueue(constant.NQ_AREA_AVOID_WAR, &pb.OnUpdateWorldInfoNotify{
				Data_26: &pb.AvoidWarInfo{Index: index},
			})
		}
		this.AvoidWarAreaData.RbTree.Remove(m.Key)
	}
	this.AvoidWarAreaData.Unlock()
}

func (this *Model) CheckAvoidWarEnd(now, time int64, index int32) bool {
	if now-time >= 0 {
		// 通知
		this.PutNotifyQueue(constant.NQ_AREA_AVOID_WAR, &pb.OnUpdateWorldInfoNotify{
			Data_26: &pb.AvoidWarInfo{Index: index},
		})
		return true
	}
	return false
}

// 刷新主城免战
func (this *Model) RefreshMainCityAvoidWar(ply *TempPlayer) {
	index := ply.MainCityIndex
	this.AvoidWarAreaData.RLock()
	avoidWarTime := this.AvoidWarAreaData.Map[index]
	this.AvoidWarAreaData.RUnlock()
	if avoidWarTime <= 0 {
		return
	}
	plyReduceKey := ply.AvoidWarReduce
	if plyReduceKey < 0 {
		return
	}
	cellCount := int32(len(ply.OwnCells))
	var reduceKey int32
	var reduceTime int64
	for k, v := range constant.REDUCE_AVOID_WAR {
		if plyReduceKey >= k {
			continue
		}
		if cellCount >= k && k > reduceKey {
			reduceKey = k
			reduceTime = v
		}
	}
	if reduceKey > 0 {
		ply.IsNeedUpdateDB = true
		ply.AvoidWarReduce = reduceKey
		lastTime := avoidWarTime
		avoidWarTime -= reduceTime
		now := time.Now().UnixMilli()
		// 先移除免战
		this.AvoidWarAreaData.Lock()
		this.AvoidWarAreaData.RbTree.RemoveElement(int(lastTime), index)
		if avoidWarTime > now {
			// 免战时间剩余 再添加免战
			this.AvoidWarAreaData.Map[index] = avoidWarTime
			this.AvoidWarAreaData.RbTree.AddElement(int(avoidWarTime), index)
		} else {
			delete(this.AvoidWarAreaData.Map, index)
		}
		this.AvoidWarAreaData.Unlock()
		// 通知
		this.PutNotifyQueue(constant.NQ_AREA_AVOID_WAR, &pb.OnUpdateWorldInfoNotify{
			Data_26: &pb.AvoidWarInfo{
				Index: index,
				Time:  int32(ut.MaxInt64(0, avoidWarTime-now)),
			},
		})
	}
}

func (this *AvoidWarData) GetAvoidWarClone() map[int32]int64 {
	rst := map[int32]int64{}
	this.RLock()
	for k, v := range this.Map {
		rst[k] = v
	}
	this.RUnlock()
	return rst
}
