package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

type PawnLvingQueueList struct {
	deadlock.RWMutex
	Map    map[int32]*AarePawnLvingQueue
	RbTree *ut.RbTreeContainer[*AarePawnLvingQueue]
}

type AarePawnLvingQueue struct {
	Index     int32
	PauseTime int64 //暂停时间
	List      []*PawnLvingInfo
}

// 士兵练级信息
type PawnLvingInfo struct {
	UID  string       `json:"uid" bson:"uid"`
	AUID string       `json:"auid" bson:"auid"` //军队uid
	PUID string       `json:"puid" bson:"puid"` //士兵uid
	Cost []*g.TypeObj `json:"cost" bson:"cost"` //费用

	StartTime int64 `json:"startTime" bson:"start_time"` //开始时间
	NeedTime  int32 `json:"needTime" bson:"need_time"`   //需要时间
	Index     int32 `json:"index" bson:"index"`          //区域位置
	ID        int32 `json:"id" bson:"id"`                //士兵id
	Lv        int32 `json:"lv" bson:"lv"`                //训练等级
}

func NewPawnLvingInfo(index int32, auid, puid string, id, lv, needTime int32) *PawnLvingInfo {
	return &PawnLvingInfo{
		UID:       ut.ID(),
		Index:     index,
		AUID:      auid,
		PUID:      puid,
		ID:        id,
		Lv:        lv,
		StartTime: 0,
		NeedTime:  needTime,
	}
}

func NewAarePawnLvingQueue(index int32) *AarePawnLvingQueue {
	return &AarePawnLvingQueue{List: []*PawnLvingInfo{}, Index: index}
}

func (this *PawnLvingInfo) toPb(now int64) *pb.PawnLvingInfo {
	if now == 0 {
		now = time.Now().UnixMilli()
	}
	return &pb.PawnLvingInfo{
		Uid:         this.UID,
		Index:       this.Index,
		Auid:        this.AUID,
		Puid:        this.PUID,
		Id:          this.ID,
		Lv:          this.Lv,
		NeedTime:    this.NeedTime,
		SurplusTime: int32(ut.Max(int(this.StartTime)+int(this.NeedTime)-int(now), 0)),
	}
}

func (this *PawnLvingQueueList) ToPb(index int32) []*pb.PawnLvingInfo {
	this.RLock()
	defer this.RUnlock()
	if obj := this.Map[index]; obj != nil {
		return array.Map(obj.List, func(m *PawnLvingInfo, _ int) *pb.PawnLvingInfo { return m.toPb(obj.PauseTime) })
	}
	return []*pb.PawnLvingInfo{}
}

func (this *Model) ToPawnLvingQueuePb(index int32) []*pb.PawnLvingInfo {
	return this.PawnLvingQueues.ToPb(index)
}

func (this *Model) ToPawnLvingQueueDB() map[int32]*AarePawnLvingQueue {
	this.PawnLvingQueues.RLock()
	defer this.PawnLvingQueues.RUnlock()
	obj := map[int32]*AarePawnLvingQueue{}
	for k, v := range this.PawnLvingQueues.Map {
		obj[k] = v
	}
	return obj
}

func (this *Model) GetPawnLvingQueue(index int32) []*PawnLvingInfo {
	this.PawnLvingQueues.RLock()
	defer this.PawnLvingQueues.RUnlock()
	if m := this.PawnLvingQueues.Map[index]; m != nil {
		return m.List
	}
	return nil
}

// 队列是否满了
func (this *Model) IsPawnLvingQueueFull(index, lvQueueMaxCnt int32) bool {
	queue := this.GetPawnLvingQueue(index)
	return queue != nil && len(queue) >= int(lvQueueMaxCnt)
}

// 是否在训练队列中
func (this *Model) IsInLvingQueue(index int32, uid string) bool {
	queue := this.GetPawnLvingQueue(index)
	return queue != nil && array.Some(queue, func(m *PawnLvingInfo) bool { return m.PUID == uid })
}

// 获取训练信息
func (this *Model) GetPawnLvingQueueInfo(index int32, uid string) *PawnLvingInfo {
	if uid == "" {
		return nil
	}
	queue := this.GetPawnLvingQueue(index)
	if queue == nil {
		return nil
	} else if m := array.Find(queue, func(m *PawnLvingInfo) bool { return m.UID == uid }); m != nil {
		return m
	}
	return nil
}

// 添加到队列
func (this *Model) PutPawnLvingQueue(index int32, auid, puid string, id, lv, needTime int32, cd float64, cost []*g.TypeObj) {
	needTime = ut.MaxInt32(1000, int32(float64(needTime)*1000*(1.0-cd)))
	info := NewPawnLvingInfo(index, auid, puid, id, lv, needTime)
	info.Cost = cost
	this.PawnLvingQueues.Lock()
	m := this.PawnLvingQueues.Map[index]
	if m == nil {
		m = NewAarePawnLvingQueue(index)
		this.PawnLvingQueues.Map[index] = m
	}
	queue := m.List
	if queue == nil {
		queue = []*PawnLvingInfo{}
	}
	queue = append(queue, info)
	this.PawnLvingQueues.Map[index].List = queue
	this.PawnLvingPawnUIDMap.Set(info.PUID, info.UID)
	// 设置第一个的开始时间
	if len(queue) == 1 {
		queue[0].StartTime = time.Now().UnixMilli()
		endTime := queue[0].StartTime + int64(queue[0].NeedTime)
		this.PawnLvingQueues.RbTree.AddElement(int(endTime), m)
	}
	this.PawnLvingQueues.Unlock()
}

// 取消训练
func (this *Model) _cancelPawnLvingQueue(index int32, uid string) *PawnLvingInfo {
	this.PawnLvingQueues.Lock()
	defer this.PawnLvingQueues.Unlock()
	// 从队列中删除
	obj := this.PawnLvingQueues.Map[index]
	if obj == nil {
		return nil
	}
	queue := obj.List
	for i, m := range queue {
		if m.UID == uid {
			queue = append(queue[:i], queue[i+1:]...)
			var newEndTime int64
			if len(queue) == 0 {
				delete(this.PawnLvingQueues.Map, index)
			} else if m.StartTime > 0 {
				queue[0].StartTime = time.Now().UnixMilli()
				newEndTime = queue[0].StartTime + int64(queue[0].NeedTime)
				this.PawnLvingQueues.Map[index].List = queue
			} else {
				this.PawnLvingQueues.Map[index].List = queue
			}
			if m.StartTime > 0 {
				// 取消的是正在训练中的 需要从红黑树删除
				endTime := m.StartTime + int64(m.NeedTime)
				this.PawnLvingQueues.RbTree.RemoveElement(int(endTime), obj)
				// 补位的训练添加到红黑树
				if newEndTime > 0 {
					this.PawnLvingQueues.RbTree.AddElement(int(newEndTime), obj)
				}
			}
			return m
		}
	}
	return nil
}

func (this *Model) CancelPawnLvingQueue(index int32, uid string) *PawnLvingInfo {
	m := this._cancelPawnLvingQueue(index, uid)
	if m != nil {
		this.PawnLvingPawnUIDMap.Del(m.PUID)
	}
	return m
}

// 检测更新士兵训练队列
func (this *Model) CheckUpdatePawnLvingQueue() {
	now := time.Now().UnixMilli()
	// 遍历之前先检测最快的训练是否结束
	notifyMap := map[int32]bool{}
	list := []*PawnLvingInfo{}
	this.PawnLvingQueues.RLock()
	minTreeNode := this.PawnLvingQueues.RbTree.FindMinElements()
	if minTreeNode == nil {
		this.PawnLvingQueues.RUnlock()
		return
	} else {
		if ut.Int64(minTreeNode.Key) > now {
			// 结束时间最快的招募都没结束
			this.PawnLvingQueues.RUnlock()
			return
		}
	}
	this.PawnLvingQueues.RUnlock()

	queueFinishMap := map[int32]bool{} //队列完成map 用于离线消息通知 k=>index
	this.PawnLvingQueues.Lock()
	for m := this.PawnLvingQueues.RbTree.FindMinElements(); m != nil; m = this.PawnLvingQueues.RbTree.FindMinElements() {
		if ut.Int64(m.Key) > now {
			// 最快的都未结束
			break
		}

		// 先删
		this.PawnLvingQueues.RbTree.Remove(m.Key)

		objList := m.Value.([]*AarePawnLvingQueue)
		if objList == nil {
			continue
		}

		for _, obj := range objList {
			queue := obj.List
			if len(queue) == 0 {
				continue
			}

			index := obj.Index
			if this.PawnLvingQueues.Map[index] == nil {
				continue
			}

			m := queue[0]                           //取出第一个开始
			queue = append(queue[:0], queue[1:]...) //删除第一个
			if len(queue) > 0 {
				queue[0].StartTime = m.StartTime + int64(m.NeedTime)
				this.PawnLvingQueues.Map[index].List = queue
				// 更新红黑树
				this.PawnLvingQueues.RbTree.AddElement(int(queue[0].StartTime+int64(queue[0].NeedTime)), obj)
			} else {
				delete(this.PawnLvingQueues.Map, index)
				queueFinishMap[index] = true
			}
			// 添加到通知队列
			notifyMap[index] = true
			list = append(list, m)
		}
	}
	this.PawnLvingQueues.Unlock()
	for _, m := range list {
		this.PawnLvingPawnUIDMap.Del(m.PUID)
		// 通知练级完成
		this.AreaPawnLvingComplete(m.Index, m.AUID, m.PUID, m.Lv)
	}
	for index := range notifyMap {
		// 通知队列信息
		this.NotifyPawnLvingQueue(index)
	}
	// 离线消息通知
	if len(queueFinishMap) > 0 {
		for index := range queueFinishMap {
			if area := this.GetArea(index); area != nil {
				if area.Owner == "" {
					continue
				}
				this.OfflineNotify(area.Owner, constant.OFFLINE_MSG_TYPE_LV_UP)
			}
		}
	}

}

// 通知
func (this *Model) NotifyPawnLvingQueue(index int32) {
	if area := this.GetArea(index); area != nil {
		this.room.PutPlayerNotifyQueue(constant.NQ_PAWN_LEVELING_QUEUE, area.Owner, &pb.OnUpdatePlayerInfoNotify{
			Data_40: this.PawnLvingQueues.ToPb(index),
		})
	}
}

// 暂停和恢复练级
func (this *Model) PausePawnLving(index int32, pauseTime int64) {
	this.PawnLvingQueues.Lock()
	obj := this.PawnLvingQueues.Map[index]
	if obj == nil {
		this.PawnLvingQueues.Unlock()
		return
	} else if pauseTime > 0 {
		// 暂停
		obj.PauseTime = pauseTime
		if len(obj.List) > 0 {
			endTime := obj.List[0].StartTime + int64(obj.List[0].NeedTime)
			this.PawnLvingQueues.RbTree.RemoveElement(int(endTime), obj)
		}
	} else if obj.PauseTime > 0 {
		// 恢复暂停
		now := time.Now().UnixMilli()
		for _, m := range obj.List {
			if m.StartTime > 0 {
				m.StartTime = now - (obj.PauseTime - m.StartTime)
				endTime := m.StartTime + int64(m.NeedTime)
				this.PawnLvingQueues.RbTree.AddElement(int(endTime), obj)
				break
			}
		}
		obj.PauseTime = 0
	}
	this.PawnLvingQueues.Unlock()
	// 通知队列信息
	this.NotifyPawnLvingQueue(index)
}

// 删除士兵练级
func (this *Model) RemovePawnLvingArea(index int32) {
	this.PawnLvingQueues.Lock()
	defer this.PawnLvingQueues.Unlock()
	m := this.PawnLvingQueues.Map[index]
	if m != nil {
		// 从红黑树删除
		minEndTime := m.FindMinEndTime()
		this.PawnLvingQueues.RbTree.RemoveElement(int(minEndTime), m)
	}
	delete(this.PawnLvingQueues.Map, index)
}

// 删除士兵练级
func (this *Model) _removePawnLvingPawn(index int32, puid string) string {
	this.PawnLvingQueues.Lock()
	defer this.PawnLvingQueues.Unlock()
	// 从队列中删除
	m := this.PawnLvingQueues.Map[index]
	if m == nil {
		return ""
	}
	queue := m.List
	for i := len(queue) - 1; i >= 0; i-- {
		d := queue[i]
		if d.PUID != puid {
			continue
		}
		var newEndTime int64
		queue = append(queue[:i], queue[i+1:]...)
		if len(queue) > 0 {
			if d.StartTime > 0 {
				queue[0].StartTime = time.Now().UnixMilli() //下一个开始
				newEndTime = queue[0].StartTime + int64(queue[0].NeedTime)
			}
			this.PawnLvingQueues.Map[index].List = queue
		} else {
			delete(this.PawnLvingQueues.Map, index)
		}
		if d.StartTime > 0 {
			// 取消的是正在招募中的 需要从红黑树删除
			endTime := d.StartTime + int64(d.NeedTime)
			this.PawnLvingQueues.RbTree.RemoveElement(int(endTime), m)
			if newEndTime > 0 && m.PauseTime == 0 { //没暂停的才加入
				// 补位的训练添加到红黑树
				this.PawnLvingQueues.RbTree.AddElement(int(newEndTime), m)
			}
		}
		return d.PUID
	}
	return ""
}

func (this *Model) RemovePawnLvingPawn(index int32, puid string) {
	if uid := this._removePawnLvingPawn(index, puid); uid != "" {
		this.PawnLvingPawnUIDMap.Del(uid)
		// 通知队列信息
		this.NotifyPawnLvingQueue(index)
	}
}

// 改变士兵的练级信息
func (this *Model) ChangePawnLvingInfo(pawn *AreaPawn) {
	uid := this.PawnLvingPawnUIDMap.Get(pawn.Uid)
	if info := this.GetPawnLvingQueueInfo(pawn.AIndex, uid); info != nil {
		info.AUID = pawn.ArmyUid
		info.PUID = pawn.Uid
		this.NotifyPawnLvingQueue(info.Index)
	}
}

// 检测军队是否在训练中
func (this *Model) CheckArmyLving(army *AreaArmy) bool {
	pawnList := army.GetPawnsClone()
	rst := false
	for _, pawn := range pawnList {
		if this.CheckPawnLving(pawn.Uid) {
			uid := this.PawnLvingPawnUIDMap.Get(pawn.Uid)
			if info := this.GetPawnLvingQueueInfo(pawn.AIndex, uid); info == nil {
				// 在练级映射表中有该士兵 但是在练级队列中没有 从映射表中删除 TODO 修复后可删除兼容
				this.PawnLvingPawnUIDMap.Del(pawn.Uid)
				log.Warning("CheckArmyLving PawnLvingPawnUIDMap err armyName: %v, pawn.Uid: %v, pawn.AIndex: %v, owner: %v", army.Name, pawn.Uid, pawn.AIndex, army.Owner)
			} else {
				rst = true
			}
		}
	}
	return rst
}

// 检测某个士兵是否在训练中
func (this *Model) CheckPawnLving(uid string) bool {
	return this.PawnLvingPawnUIDMap.Get(uid) != ""
}

// 查询队列最快的结束时间
func (this *AarePawnLvingQueue) FindMinEndTime() int64 {
	if len(this.List) == 0 {
		return 0
	}
	return this.List[0].StartTime + int64(this.List[0].NeedTime)
}
