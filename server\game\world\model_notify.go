package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
)

var (
	marchNotifyMap = map[string][]string{} // 行军通知map k=>行军uid v=>已通知的玩家uid列表
	marchNotifyAcc int32
)

// 定时通知
func (this *Model) UpdateNotify() {
	if len(this.notifyQueue) == 0 {
		return
	}
	list := []*pb.OnUpdateWorldInfoNotify{}
	for len(this.notifyQueue) > 0 {
		msg := <-this.notifyQueue
		if msg.Type == constant.NQ_ADD_MARCH || msg.Type == constant.NQ_REMOVE_MARCH {
			// 行军通知需筛选接收人
			this.NotifyMarch(msg)
			continue
		}
		list = append(list, msg)
		if len(list) >= 1000 {
			log.Info("UpdateNotify len(list) >= 1000, notifyQueue: %v", len(this.notifyQueue))
			break
		}
	}
	if body, err := pb.ProtoMarshal(&pb.GAME_ONUPDATEWORLDINFO_NOTIFY{List: list}); err == "" {
		this.room.NotifyAllByBytes("game/OnUpdateWorldInfo", body)
		// log.Info("world UpdateNotify ", list)
	} else {
		log.Error("world UpdateNotify error: %v", err)
	}
}

// 添加到通知队列
func (this *Model) PutNotifyQueue(t int32, msg *pb.OnUpdateWorldInfoNotify) {
	if msg == nil || this.notifyQueue == nil {
		return
	}
	msg.Type = t
	this.notifyQueue <- msg
}

// 通知战场更新信息
func (this *Model) NotifyAreaUpdateInfo(index, t int32, msg *pb.GAME_ONUPDATEAREAINFO_NOTIFY, ignores ...string) {
	if uids := this.GetReqAreaPlayers(index, ignores...); len(uids) > 0 {
		msg.Index = index
		msg.Type = t
		data, _ := pb.ProtoMarshal(msg)
		this.room.PutNotifyAllPlayersQueue("game/OnUpdateAreaInfo", data, uids)
	}
}

// 通知观战玩家
func (this *Model) NotifyWatchPlayer(index int32, t int, msg *pb.GAME_ONUPDATEAREAINFO_NOTIFY) {
	if players := this.watchPlayers.Get(index); len(players) > 0 {
		msg.Index = index
		msg.Type = int32(t)
		data, _ := pb.ProtoMarshal(msg)
		this.room.PutNotifyAllPlayersQueue("game/OnUpdateAreaInfo", data, array.Map(players, func(m *pb.WatchPlayerInfo, _ int) string { return m.Uid }))
	}
}

// 通知战场同步帧信息
func (this *Model) NotifyAreaCheckFrame(index int32, data *pb.GAME_ONFSPCHECKFRAME_NOTIFY) {
	if uids := this.GetReqAreaPlayers(index); len(uids) > 0 { //直接向玩家通知
		data.Data.Index = index
		msgBytes, _ := pb.ProtoMarshal(data)
		this.room.PutNotifyAllPlayersQueue("game/OnFSPCheckFrame", msgBytes, uids)
	}
}

// 通知行军信息
func (this *Model) NotifyMarch(notifyInfo *pb.OnUpdateWorldInfoNotify) {
	var march *pb.MarchInfo
	if notifyInfo.Data_13 != nil {
		// 添加行军
		march = notifyInfo.Data_13
	} else if notifyInfo.Data_14 != nil {
		// 删除行军
		march = notifyInfo.Data_14
	} else {
		return
	}
	var uids []string
	switch notifyInfo.Type {
	case constant.NQ_ADD_MARCH:
		// 添加行军
		march.NotifyIndex = marchNotifyAcc
		marchNotifyAcc++
		oldUids := marchNotifyMap[march.Uid]
		uids = this.GetMarchNotifyPlrList(march, oldUids)
		// 更新已通知行军map
		marchNotifyMap[march.Uid] = uids
	case constant.NQ_REMOVE_MARCH:
		// 删除行军 只通知uid
		uids = marchNotifyMap[march.Uid]
		if uids == nil {
			uids = this.GetMarchNotifyPlrList(march, nil)
		}
		uid := notifyInfo.Data_14.Uid
		notifyInfo.Data_14.Reset()
		notifyInfo.Data_14.Uid = uid
		// 从已通知行军中移除
		delete(marchNotifyMap, uid)
	}
	msg := &pb.GAME_ONUPDATEWORLDINFO_NOTIFY{List: []*pb.OnUpdateWorldInfoNotify{notifyInfo}}
	data, _ := pb.ProtoMarshal(msg)
	this.room.PutNotifyAllPlayersQueue("game/OnUpdateWorldInfo", data, uids)
}

// 获取行军通知玩家列表
func (this *Model) GetMarchNotifyPlrList(march *pb.MarchInfo, oldUids []string) []string {
	uids := []string{}
	notifyUserIdMap := map[string]bool{}
	notifyUserIdMap[march.Owner] = true
	// 通知军队同联盟的玩家
	if plr := this.GetTempPlayer(march.Owner); plr != nil {
		alli := this.GetAlliance(plr.AllianceUid)
		if alli != nil {
			uidList := alli.GetMemberUids()
			for _, uid := range uidList {
				notifyUserIdMap[uid] = true
			}
		} else {
			notifyUserIdMap[march.Owner] = true
		}
	}

	sArea := this.GetArea(march.StartIndex)
	if sArea != nil && sArea.Owner != "" {
		if plr := this.GetTempPlayer(sArea.Owner); plr != nil {
			// 通知起始位置同联盟的玩家
			alli := this.GetAlliance(plr.AllianceUid)
			if alli != nil {
				uidList := alli.GetMemberUids()
				for _, uid := range uidList {
					notifyUserIdMap[uid] = true
				}
			} else {
				notifyUserIdMap[sArea.Owner] = true
			}
		}
	}

	tArea := this.GetArea(march.TargetIndex)
	if tArea != nil && tArea.Owner != "" {
		if plr := this.GetTempPlayer(tArea.Owner); plr != nil {
			// 通知目标位置同联盟的玩家
			alli := this.GetAlliance(plr.AllianceUid)
			if alli != nil {
				uidList := alli.GetMemberUids()
				for _, uid := range uidList {
					notifyUserIdMap[uid] = true
				}
			} else {
				notifyUserIdMap[tArea.Owner] = true
			}
		}
	}
	// 该行军已通知过 可能为遣返 本次通知玩家列表为两次的并集去重
	for _, uid := range oldUids {
		notifyUserIdMap[uid] = true
	}
	for uid := range notifyUserIdMap {
		uids = append(uids, uid)
	}
	return uids
}
