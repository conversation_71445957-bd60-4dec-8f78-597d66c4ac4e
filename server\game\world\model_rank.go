package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"
	"sort"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

type ScoreInfo struct {
	UID        string
	Statistics map[int32]int32
	PawnUse    map[int32]bool //士兵使用
	EquipUse   map[int32]bool //装备使用
	PolicyUse  map[int32]bool //政策使用
	HeroUse    []int32        //使用的英雄
	Score      int32
	LandScore  int32
	Win        bool
}

type RankManager struct {
	deadlock.RWMutex
	Players        []*pb.PlayerRankInfo  //玩家排行列表
	Allis          []*pb.AlliRankInfo    //联盟排行列表
	Scores         []*pb.PlayerScoreInfo //玩家积分排行
	Scores2        []*pb.PlayerScoreInfo //玩家积分排行
	lastUpdateTime int64                 //最后一次刷新排行榜时间
}

func (this *Model) checkUpdateRankInfo(force bool) {
	rank := this.rank
	now := ut.Now()
	if !force && now-rank.lastUpdateTime < ut.TIME_MINUTE && rank.Players != nil {
		return
	}
	rank.Lock()
	defer rank.Unlock()
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	rank.lastUpdateTime = now
	// 获取联盟的信息
	rank.Allis = []*pb.AlliRankInfo{}
	for _, m := range this.Alliances.Map {
		if m.Name == "" || m.Creater == "" {
			continue
		}
		sumCount, resAcc, occupyCount, score := this.GetAlliRankInfo(m)
		rank.Allis = append(rank.Allis, &pb.AlliRankInfo{
			Uid:         m.Uid,
			Icon:        m.Icon,
			Name:        m.Name,
			Pers:        []int32{int32(len(m.Members.List)), m.MemberPersLimit},
			LandCount:   sumCount,
			Time:        m.CreateTime,
			ResAcc:      resAcc,
			OccupyCount: occupyCount,
			Score:       score,
		})
	}
	// 排序 从大到小
	sort.Slice(rank.Allis, func(i, j int) bool {
		a, b := rank.Allis[i], rank.Allis[j]
		ascore, bscore := a.Score, b.Score
		if ascore == bscore {
			return a.Time < b.Time
		}
		return ascore > bscore
	})
	// 获取玩家的信息
	rank.Players = []*pb.PlayerRankInfo{}
	rank.Scores = []*pb.PlayerScoreInfo{}
	rank.Scores2 = []*pb.PlayerScoreInfo{}
	winType, winUID := this.room.GetGameWiner()
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		alliName := ""
		var alliIcon int32
		if alli := this.Alliances.Map[m.AllianceUid]; alli != nil {
			alliName = alli.Name
			alliIcon = alli.Icon
		}
		if landCount := len(m.OwnCells); landCount > 0 {
			rank.Players = append(rank.Players, &pb.PlayerRankInfo{
				Uid:       m.Uid,
				HeadIcon:  m.HeadIcon,
				Nickname:  m.Nickname,
				AlliName:  alliName,
				LandCount: int32(landCount),
				Time:      m.CreateTime,
			})
		}
		if landScore, alliScore, extraScore := this.GetPlayerGameSumScore(m, winType, winUID); landScore > 0 || alliScore > 0 || extraScore > 0 {
			info := &pb.PlayerScoreInfo{
				Uid:        m.Uid,
				HeadIcon:   m.HeadIcon,
				Nickname:   m.Nickname,
				AlliName:   alliName,
				LandScore:  landScore,
				AlliScore:  alliScore,
				ExtraScore: extraScore,
				Time:       int64(m.CreateTime),
				AlliIcon:   alliIcon,
			}
			rank.Scores = append(rank.Scores, info)
			rank.Scores2 = append(rank.Scores2, info)
		}
	}
	this.allTempPlayers.RUnlock()
	// 排序 从大到小
	sort.Slice(rank.Players, func(i, j int) bool {
		a, b := rank.Players[i], rank.Players[j]
		if a.LandCount == b.LandCount {
			return a.Time < b.Time
		}
		return a.LandCount > b.LandCount
	})
	sort.Slice(rank.Scores, func(i, j int) bool {
		a, b := rank.Scores[i], rank.Scores[j]
		aScore := a.LandScore + a.AlliScore
		bScore := b.LandScore + b.AlliScore
		if aScore == bScore {
			return a.Time < b.Time
		}
		return aScore > bScore
	})
	sort.Slice(rank.Scores2, func(i, j int) bool {
		a, b := rank.Scores2[i], rank.Scores2[j]
		aScore := a.LandScore + a.AlliScore + a.ExtraScore
		bScore := b.LandScore + b.AlliScore + b.ExtraScore
		if aScore == bScore {
			return a.Time < b.Time
		}
		return aScore > bScore
	})
}

// 获取玩家排行信息
func (this *Model) GetPlayerRankList(uid string) ([]*pb.PlayerRankInfo, int) {
	this.checkUpdateRankInfo(false)
	return this.rank.Players[:ut.Min(len(this.rank.Players), 20)], array.FindIndex(this.rank.Players, func(m *pb.PlayerRankInfo) bool { return m.Uid == uid })
}

// 获取联盟排行信息
func (this *Model) GetAlliRankList() []*pb.AlliRankInfo {
	this.checkUpdateRankInfo(false)
	return this.rank.Allis[:ut.Min(len(this.rank.Allis), 30)]
}

// 获取联盟排行信息
func (this *Model) GetAlliRankListAll() []*pb.AlliRankInfo {
	this.checkUpdateRankInfo(false)
	return this.rank.Allis
}

// 获取玩家积分排行信息
func (this *Model) GetPlayerScoreList(uid string, tp int) (list []*pb.PlayerScoreInfo, no int, me *pb.MeScoreInfo) {
	this.checkUpdateRankInfo(false)
	scores := ut.If(tp == 0, this.rank.Scores, this.rank.Scores2)
	list = scores[:ut.Min(len(scores), 50)]
	if no = array.FindIndex(scores, func(m *pb.PlayerScoreInfo) bool { return m.Uid == uid }); no != -1 {
		landScore, alliScore, landScoreTop, alliScoreTop := this.GetPlayerLandScoreAndAlliScore(uid)
		me = &pb.MeScoreInfo{
			LandScore:    landScore,
			AlliScore:    alliScore,
			LandScoreTop: landScoreTop,
			AlliScoreTop: alliScoreTop,
			ExtraScore:   scores[no].ExtraScore,
		}
	}
	return
}

// 获取玩家当前最高领地和联盟最高领地
func (this *Model) GetPlayerAndAlliTop() (playerTop, alliTop, alliTopBeAddCount int) {
	this.checkUpdateRankInfo(false)
	if len(this.rank.Players) > 0 {
		playerTop = int(this.rank.Players[0].LandCount)
	}
	if len(this.rank.Allis) > 0 {
		alliTop = int(this.rank.Allis[0].LandCount)
		alliTopBeAddCount = int(this.rank.Allis[0].BeAddLandCount)
	}
	return
}

// 获取玩家总积分
func (this *Model) GetPlayerGameSumScore(plr *TempPlayer, winType int32, winUID string) (int32, int32, int32) {
	win, extraScore := false, int32(0)
	switch winType {
	case 1: //1.玩家 2.联盟
		win = winUID == plr.Uid
	case 2:
		win = winUID == plr.AllianceUid
	}
	if win && !this.room.IsConquer() {
		extraScore = constant.GAME_EXTRA_SCORE //获胜者额外获得积分
	}
	return plr.LandScoreTop, plr.AlliScoreTop, extraScore
}

// 获取所有玩家的积分排名
func (this *Model) GetAllPlayerScoreRankInfo(winType int32, winUid string) []map[string]interface{} {
	defer func() {
		go this.checkUpdateRankInfo(true) //结束的时候刷一下
	}()
	arr := []map[string]interface{}{}
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		if m.IsGiveupGame || m.IsSpectator() {
			continue //已经放弃或观战的 跳过
		}
		score := m.LandScoreTop + m.AlliScoreTop
		win := false
		if winUid == "" {
		} else if winType == 1 { //个人
			win = m.Uid == winUid
		} else if winType == 2 { //联盟
			win = m.AllianceUid == winUid
		}
		if win && !this.room.IsConquer() /* && score > 0 */ {
			score += constant.GAME_EXTRA_SCORE
		}
		arr = append(arr, m.getSettleInfo(0, win))
	}
	this.allTempPlayers.RUnlock()
	sort.Slice(arr, func(i, j int) bool { return ut.Int32(arr[i]["score"]) > ut.Int32(arr[j]["score"]) })
	return array.Map(arr, func(m map[string]interface{}, i int) map[string]interface{} {
		m["rank"] = i
		return m
	})
}

// 血战到底联盟结算
func (this *Model) ConquerAlliSettle(alli *Alliance, isFail, isWin bool) {
	if !this.room.IsConquer() || alli == nil {
		return
	}
	plrList := []*TempPlayer{}
	alli.Members.RLock()
	for _, v := range alli.Members.List {
		plr := this.GetTempPlayer(v.Uid)
		if plr == nil || plr.IsGiveupGame || plr.IsSettled || plr.IsSpectator() {
			continue
		}
		plrList = append(plrList, plr)
	}
	alli.Members.RUnlock()

	if isFail {
		// 玩家出局处理
		for _, v := range plrList {
			if v.IsCapture {
				continue // 已被攻占则不处理
			}
			plrModel, _ := this.room.GetOnlinePlayerOrDB(v.Uid).(*player.Model)
			plrModel.State = constant.PS_CAPTURE // 设置为已沦陷
			// 删除出局玩家的所有士兵和行军
			this.RemovePlayerAllArmy(v.Uid)
			this.RemovePlayerAllMarch(v.Uid)
			this.RemovePlayerAllTransit(v.Uid)
			this.RemoveDrillPawn(v.MainCityIndex)      //删除主城的训练士兵
			this.RemovePawnLvingArea(v.MainCityIndex)  //删除主城的士兵练级
			this.RemovePawnCuringArea(v.MainCityIndex) //删除主城治疗的士兵
			this.RepatriateTransitByTargetIndex(v.MainCityIndex, v.Uid)
			// 城墙 里属亭 边塞营全降为1级
			this.SetBuildLv(v.MainCityIndex, constant.WALL_BUILD_ID, 1)
			this.SetBuildLv(v.MainCityIndex, constant.FRONTIER_BUILD_ID, 1)
			this.SetBuildLv(v.MainCityIndex, constant.PAVILION_BUILD_ID, 1)
			// 移除玩家免战
			this.DelPlayerAvoidWar(v.Uid)
			this.FailPlrMap.Set(v.Uid, true)
		}
	}

	// 先结算当天的战斗积分
	for _, v := range plrList {
		date := time.Now().Format("2006-01-02")
		this.CalPlayerAlliScore(date, v)
	}
	// 该联盟未结算的玩家按积分升序排列
	sort.Slice(plrList, func(i, j int) bool {
		scoreA := plrList[i].AlliScore + plrList[i].LandScore
		scoreB := plrList[j].AlliScore + plrList[j].LandScore
		return scoreA < scoreB
	})
	playerNum := this.room.GetPlayerNum()
	// 获取结算的大厅服
	lid := rds.GetRandomLid()
	if lid == "" {
		log.Error("ConquerAlliSettle not lid?")
		return
	}

	notifyInfo := &pb.AlliSettleNotify{
		Uid:  alli.Uid,
		List: []*pb.PlayerSettleInfo{},
	}
	// 处理玩家结算数据
	settleList := []map[string]interface{}{}
	this.playerSettleLock.Lock()
	for _, plr := range plrList {
		if plr.IsGiveupGame || plr.IsSettled || plr.IsSpectator() {
			continue
		}
		// 传给大厅服的rank和其他模式统一 都为排名数组的下标
		rank := playerNum - this.settlePlayerNum - 1
		plr.IsSettled = true
		plr.IsNeedUpdateDB = true
		scoreInfo := plr.getSettleInfo(rank, isWin)
		settleList = append(settleList, scoreInfo)
		notifyInfo.List = append(notifyInfo.List, &pb.PlayerSettleInfo{Uid: plr.Uid, Rank: rank})
		this.settlePlayerNum++
	}
	this.playerSettleLock.Unlock()

	// 通知联盟结算
	this.PutNotifyQueue(constant.NO_ALLI_SETTLE, &pb.OnUpdateWorldInfoNotify{Data_88: notifyInfo})
	// 通知大厅服结算
	this.room.InvokeLobbyRpcNR(lid, slg.RPC_GAMEOVER_SETTLE, this.room.GetSID(), this.room.GetType(), []int64{this.room.GetCreateTime(), ut.Now()}, settleList, playerNum, this.GetSettleAlliInfo(alli.Uid, false))
	// 强制更新排行榜
	this.checkUpdateRankInfo(true)
}

// 血战到底个人出局结算
func (this *Model) ConquerFailSettle(tplr *TempPlayer) (rank int32, err string) {
	if !this.room.IsConquer() {
		return 0, ecode.NOT_CONQUER_TYPE.String()
	}
	if tplr == nil {
		return 0, ecode.PLAYER_NOT_EXIST.String()
	}
	if tplr.IsGiveupGame || tplr.IsSettled || tplr.IsSpectator() || !tplr.IsCapture {
		return 0, ecode.CANT_SETTLE.String() // 不满足结算条件
	}

	// 获取结算的大厅服
	lid := rds.GetRandomLid()
	if lid == "" {
		log.Error("ConquerAlliSettle not lid?")
		return 0, ecode.UNKNOWN.String()
	}

	date := time.Now().Format("2006-01-02")
	// 计算当天的战斗积分
	this.CalPlayerAlliScore(date, tplr)
	this.playerSettleLock.Lock()
	defer this.playerSettleLock.Unlock()
	if tplr.IsGiveupGame || tplr.IsSettled || tplr.IsSpectator() || !tplr.IsCapture {
		return 0, ecode.CANT_SETTLE.String() // 加锁后再检查一次结算条件
	}
	tplr.IsSettled = true
	tplr.IsNeedUpdateDB = true
	rank = this.room.GetPlayerNum() - this.settlePlayerNum
	this.settlePlayerNum++

	scoreInfo := tplr.getSettleInfo(rank, false)
	alliMap := this.GetSettleAlliInfo(tplr.AllianceUid, false)

	// 通知大厅服结算
	this.room.InvokeLobbyRpcNR(lid, slg.RPC_GAMEOVER_SETTLE, []int64{this.room.GetCreateTime(), ut.Now()}, []map[string]interface{}{scoreInfo}, this.room.GetPlayerNum(), alliMap)
	return
}

// 获取玩家结算信息
func (m *TempPlayer) getSettleInfo(rank int32, isWin bool) map[string]interface{} {
	score := m.LandScoreTop + m.AlliScoreTop
	return map[string]interface{}{
		"uid":        m.Uid,
		"win":        isWin,
		"score":      score,
		"land_score": m.LandScore,
		"rank":       rank,
		"statistics": m.ToStatistics(),         //统计
		"pawn_use":   m.ToPawnDrillMap(),       //士兵使用
		"equip_use":  m.ToEquipUseMap(),        //装备使用
		"policy_use": m.ToPolicyUseMap(),       //政策使用
		"hero_use":   m.GetWorshipHeroIdList(), //英雄使用
		"alli_uid":   m.AllianceUid,            //联盟uid
	}
}

// 获取玩家结算的联盟信息
func (this *Model) GetSettleAlliInfo(alliUid string, isAll bool) map[string]interface{} {
	rst := map[string]interface{}{}
	setAlliMapInfo := func(rst map[string]interface{}, alli *Alliance) {
		rst[alliUid] = map[string]interface{}{
			"uid":      alliUid,
			"name":     alli.Name,
			"headicon": alli.Icon,
		}
	}
	if isAll {
		this.Alliances.RLock()
		for _, v := range this.Alliances.Map {
			setAlliMapInfo(rst, v)
		}
		this.Alliances.RUnlock()
	} else if alli := this.GetAlliance(alliUid); alli != nil {
		setAlliMapInfo(rst, alli)
	}

	return rst
}
