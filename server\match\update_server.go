package match

import (
	"os/exec"
	slg "slgsrv/server/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 停机更新任务
type UpdateServerTask struct {
	Uid           string `bson:"uid"`
	StopTime      int64  `bson:"stop_time"`       // 停机时间
	OpenDelayTime int    `bson:"open_delay_time"` // 开服延迟时间 单位分钟
	OpenTime      int64  `bson:"open_time"`       // 开服时间
	UpdateCode    bool   `bson:"update_code"`     // 是否更新代码
	State         int    `bson:"state"`           // 状态
	NextCheckTime int64  `bson:"next_check_time"` // 下一次检测时间
	ExtraTime     int    `bson:"extra_time"`      // 重启冗余时间 单位分钟
	Version       string `bson:"version"`         // 版本
	BigVersion    string `bson:"big_version"`     // 大版本
	UpdateGate    bool   `bson:"update_gate"`     // 是否停机更新连接服
	NoticeUid     string `bson:"notice_uid"`      // 公告uid
}

// 停机更新任务队列
type UpdateServerTaskQueue struct {
	List []*UpdateServerTask
	deadlock.RWMutex
}

// 更新自动更新任务状态
func (this *UpdateServerTask) ChangeTaskState(state int) {
	this.State = state
	this.NextCheckTime = ut.Now() + slg.AUTO_UPDATE_HANDLE_EXTRA_TIME
}

var updateServerTaskQueue = &UpdateServerTaskQueue{List: []*UpdateServerTask{}}

// 停机更新任务tick
func (this *Match) UpdateServerTaskTick() {
	go func() {
		ticker := time.NewTicker(time.Second * 1)
		defer ticker.Stop()
		for isRunning {
			<-ticker.C
			if !isRunning {
				break
			}
			now := ut.Now()
			updateServerTaskQueue.RLock()
			for _, task := range updateServerTaskQueue.List {
				if task.State == slg.UPDATE_TASK_FINISH {
					continue
				}
				if task.NextCheckTime != 0 && now < task.NextCheckTime {
					continue
				}
				switch task.State {
				case slg.UPDATE_TASK_IDLE:
					// 任务未开始
					if !matchPause && int(task.StopTime-now) < slg.SERVER_UPDATE_MATCH_PAUSE_TIME {
						// 停机更新任务开始前 暂停匹配
						matchPause = true
					}
					if now >= task.StopTime {
						log.Info("auto update start!")
						// 更新维护时间
						allServerMaintainTime = now + int64(task.OpenDelayTime*ut.TIME_MINUTE) + int64(task.ExtraTime*ut.TIME_MINUTE)
						this.InvokeWithCleanup(slg.MACH_SERVER_TYPE_HTTP, slg.RPC_SERVER_UPDATE_TIME, ut.Bytes(allServerMaintainTime))
						// 设置版本号 rpc通知工具服改版本
						this.InvokeWithCleanup(slg.MACH_SERVER_TYPE_HTTP, slg.RPC_UPDATE_VERSION, ut.Bytes(task.Version), ut.Bytes(task.BigVersion))
						if task.UpdateCode {
							// 需要更新代码
							task.ChangeTaskState(slg.UPDATE_TASK_UPDATE_CODE)
							// 更新游戏服
							updateAllGameCode()
							// 更新大厅服
							lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
								v.HandleUpdateCode()
								return true
							})
							// 更新辅助服
							supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
								v.HandleUpdateCode()
								return true
							})
							// 更新匹配服（当前）
							cmd := exec.Command("bash", "-c", slg.GIT_UPDATE_SHELL)
							_, e := cmd.CombinedOutput()
							if e != nil {
								log.Error("UpdateServerTaskTick update match code err: %v", e)
								continue
							}
						} else {
							// 不需要更新代码 从游戏服开始停机
							task.ChangeTaskState(slg.UPDATE_TASK_GAME_STOP)
							this.stopAllGameServers()
						}
					}
				case slg.UPDATE_TASK_UPDATE_CODE:
					// 更新代码中 检测全部更新完成
					// 检测游戏服
					if !getUpdateAllGameCodeFinish() {
						continue
					}
					updateCodeFinish := true
					// 检测大厅服
					lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
						if v.UpdateStatus != slg.MACH_UPDATE_STATUS_UPDATED {
							updateCodeFinish = false
							return false
						}
						return true
					})
					if !updateCodeFinish {
						continue
					}
					// 检测辅助服
					supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
						if v.UpdateStatus != slg.MACH_UPDATE_STATUS_UPDATED {
							updateCodeFinish = false
							return false
						}
						return true
					})
					if !updateCodeFinish {
						continue
					}
					log.Info("auto update code finish!")
					// 全部更新完成则进入停机流程 先执行游戏服停机
					task.ChangeTaskState(slg.UPDATE_TASK_GAME_STOP)
					this.stopAllGameServers()
				case slg.UPDATE_TASK_GAME_STOP:
					// 游戏服停机中
					if getAllGameServersStopFinish() {
						log.Info("auto stop game server finish!")
						// 游戏服停机完成 开始停机大厅服
						task.ChangeTaskState(slg.UPDATE_TASK_LOBBY_STOP)
						lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
							this.InvokeLobbyRpcNR(ut.String(v.Id), slg.RPC_STOP_SERVER)
							v.UpdateMachStatus(slg.MACH_STATUS_STOPPING)
							return true
						})
					}
				case slg.UPDATE_TASK_LOBBY_STOP:
					// 大厅服停机中 检查是否所有大厅服停机完成
					stopFinish := true
					lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
						this.GetLobbyStopInfo(v)
						if v.Status != slg.MACH_STATUS_STOPPED {
							stopFinish = false
							return false
						}
						return true
					})
					if stopFinish {
						log.Info("auto stop lobby server finish!")
						// 大厅服停机完成 开始关闭游戏服
						task.ChangeTaskState(slg.UPDATE_TASK_GAME_CLOSE)
						closeAllGameServers()
					}
				case slg.UPDATE_TASK_GAME_CLOSE:
					// 游戏服关闭中 检测所有游戏服是否关闭完成
					if getCloseAllGameServersFinish() {
						log.Info("auto close game server finish!")
						// 游戏服关闭完成 开始关闭大厅服
						task.ChangeTaskState(slg.UPDATE_TASK_LOBBY_CLOSE)
						lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
							if v.Status != slg.MACH_STATUS_STOPPED {
								return true
							}
							_, err := ut.SshExcuteShell(v.Addr, slg.STOP_SERVER_BASH)
							if err != nil {
								log.Error("UpdateServerTaskTick close lobby addr: %v, err: %v", v.Addr, err)
								return true
							}
							v.UpdateMachStatus(slg.MACH_STATUS_CLOSING)
							return true
						})
					}
				case slg.UPDATE_TASK_LOBBY_CLOSE:
					// 大厅服关闭中 检测所有大厅服是否关闭完成
					lobbyCloseFinish := true
					lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
						this.GetLobbyProcessInfo(v)
						if v.Status != slg.MACH_STATUS_CLOSE {
							lobbyCloseFinish = false
							return false
						}
						return true
					})
					if lobbyCloseFinish {
						log.Info("auto close lobby server finish!")
						// 大厅服关闭完成 开始关闭辅助服
						task.ChangeTaskState(slg.UPDATE_TASK_SUP_CLOSE)
						supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
							if v.Type == slg.MACH_SERVER_TYPE_HTTP || (!task.UpdateGate && v.Type == slg.MACH_SERVER_TYPE_GATE) {
								// 工具服不自动停服 连接服根据配置是否自动重启
								return true
							}
							v.CloseProcess()
							return true
						})
					}
				case slg.UPDATE_TASK_SUP_CLOSE:
					// 辅助服关闭中 检测辅助服是否关闭完成
					CloseFinish := true
					supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
						if v.Type == slg.MACH_SERVER_TYPE_HTTP || (!task.UpdateGate && v.Type == slg.MACH_SERVER_TYPE_GATE) {
							// 工具服不自动停服 连接服根据配置是否自动重启
							return true
						}
						this.GetSupProcessInfo(v)
						if v.Status != slg.MACH_STATUS_CLOSE {
							CloseFinish = false
							return false
						}
						return true
					})
					if CloseFinish {
						log.Info("auto close sup server finish!")
						// 辅助服关闭完成 关服流程结束
						task.ChangeTaskState(slg.UPDATE_TASK_CLOSE_FINISH)
						// 设置开服时间
						task.OpenTime = ut.Now() + int64(ut.TIME_MINUTE*task.OpenDelayTime)
					}
				case slg.UPDATE_TASK_CLOSE_FINISH:
					// 关服流程结束
					if now < task.OpenTime {
						// 未到开服时间
						continue
					}
					log.Info("auto start open server!")
					lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
						// 开始启动
						if v.Status != slg.MACH_STATUS_CLOSE {
							log.Error("UpdateServerTaskTick lobby start not close Status: %v", v.Status)
							return true
						}
						// 启动大厅服
						_, err := ut.SshExcuteShell(v.Addr, slg.START_SERVER_BASH)
						if err != nil {
							log.Error("UpdateServerTaskTick lobby start err: %v", err)
							return true
						}
						v.UpdateMachStatus(slg.MACH_STATUS_OPENNING)
						return true
					})
					task.ChangeTaskState(slg.UPDATE_TASK_LOBBY_OPEN)
				case slg.UPDATE_TASK_LOBBY_OPEN:
					// 大厅服启动中 检测大厅服是否启动完成
					lobbyOpenFinish := true
					lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
						this.GetLobbyProcessInfo(v)
						if v.Status != slg.MACH_STATUS_OPEN {
							lobbyOpenFinish = false
							return false
						}
						return true
					})
					if lobbyOpenFinish {
						log.Info("auto open lobby server finish!")
						// 大厅服启动完成 开始启动游戏服
						task.ChangeTaskState(slg.UPDATE_TASK_GAME_OPEN)
						openAllGameServers()
					}
				case slg.UPDATE_TASK_GAME_OPEN:
					// 游戏服启动中 检测游戏服是否启动完成
					rst := getOpenAllGameServersFinish()
					if rst {
						log.Info("auto open game server finish!")
						// 游戏服启动完成 开始启动辅助服
						task.ChangeTaskState(slg.UPDATE_TASK_SUP_OPEN)
						supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
							if v.Type == slg.MACH_SERVER_TYPE_HTTP || (!task.UpdateGate && v.Type == slg.MACH_SERVER_TYPE_GATE) {
								// 工具服不自动重启 连接服根据配置是否自动重启
								return true
							}
							v.StartProcess()
							return true
						})
					}
				case slg.UPDATE_TASK_SUP_OPEN:
					// 辅助服启动中 检测辅助服是否启动完成
					openFinish := true
					supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
						if v.Type == slg.MACH_SERVER_TYPE_HTTP || (!task.UpdateGate && v.Type == slg.MACH_SERVER_TYPE_GATE) {
							// 工具服不自动重启 连接服根据配置是否自动重启
							return true
						}
						this.GetSupProcessInfo(v)
						if v.Status != slg.MACH_STATUS_OPEN {
							openFinish = false
							return false
						}
						return true
					})
					if openFinish {
						log.Info("auto open sup server finish!")
						// 辅助服启动完成 通知工具服定时重启 并重启匹配服(当前进程)
						this.InvokeNR(slg.MACH_SERVER_TYPE_HTTP, slg.RPC_HTTP_DELAY_RESTART)
						task.ChangeTaskState(slg.UPDATE_TASK_FINISH)
						cmd := exec.Command("pm2", "restart", "all")
						cmd.Run()
					}
				}
			}
			updateServerTaskQueue.RUnlock()
		}
	}()

}

// 获取停机更新任务列表
func (this *Match) httpGetStopUpdateTaskList() (response map[string]interface{}, err error) {
	updateServerTaskQueue.RLock()
	arr := array.Clone(updateServerTaskQueue.List)
	updateServerTaskQueue.RUnlock()
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": arr,
	}), nil
}

// 添加停机更新任务
func (this *Match) httpAddStopUpdateTask(stopTime, openTime, updateCode, extraTime, version, bigVersion, updateGate string) (response map[string]interface{}, err error) {
	stopTimeStamps := ut.Int64(stopTime)
	if stopTimeStamps == 0 || stopTimeStamps <= ut.Now() {
		return slg.HttpResponseErrorNoDataWithDesc("停机时间格式错误!"), nil
	}
	openDelayTime := ut.Int(openTime)
	if openDelayTime <= 0 {
		return slg.HttpResponseErrorNoDataWithDesc("开服时间格式错误!"), nil
	}
	noticeUid := ut.ID()
	updateServerTaskQueue.Lock()
	updateServerTaskQueue.List = append(updateServerTaskQueue.List, &UpdateServerTask{
		Uid:           ut.ID(),
		StopTime:      stopTimeStamps,
		OpenDelayTime: openDelayTime,
		UpdateCode:    updateCode == "1",
		ExtraTime:     ut.Int(extraTime),
		Version:       version,
		BigVersion:    bigVersion,
		UpdateGate:    updateGate == "1",
		NoticeUid:     noticeUid,
	})
	updateServerTaskQueue.Unlock()
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"noticeUid": noticeUid,
	}), nil
}

// 删除停机更新任务
func (this *Match) httpDelStopUpdateTask(uid string) (response map[string]interface{}, err error) {
	var noticeUid string
	updateServerTaskQueue.Lock()
	for i := len(updateServerTaskQueue.List) - 1; i >= 0; i-- {
		v := updateServerTaskQueue.List[i]
		if v.Uid == uid {
			noticeUid = v.NoticeUid
			updateServerTaskQueue.List = append(updateServerTaskQueue.List[:i], updateServerTaskQueue.List[i+1:]...)
			break
		}
	}
	updateServerTaskQueue.List = array.Delete(updateServerTaskQueue.List, func(v *UpdateServerTask) bool {
		return v.Uid == uid
	})
	updateServerTaskQueue.Unlock()
	if noticeUid != "" {
		// 通知工具服删除公告
		this.InvokeNR(slg.MACH_SERVER_TYPE_HTTP, slg.RPC_DEL_NOTICE_TICK, noticeUid)
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}
